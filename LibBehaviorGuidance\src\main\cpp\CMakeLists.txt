# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.22.1)

# Declares the project name.
project("BehaviorGuidance")

# 设置 OpenCV 路径
set(OpenCV_DIR ${CMAKE_CURRENT_SOURCE_DIR}/opencv-mobile-4.9.0-android/sdk/native/jni)
find_package(OpenCV REQUIRED)

# 设置 ncnn 路径
set(ncnn_DIR ${CMAKE_CURRENT_SOURCE_DIR}/ncnn-20240410-android-vulkan/${ANDROID_ABI}/lib/cmake/ncnn)
find_package(ncnn REQUIRED)

# 创建和命名库
add_library(
        ${CMAKE_PROJECT_NAME}
        SHARED
        native-lib.cpp
        scrfd.cpp
        MonitorReport.cpp
        MonitorAlert.cpp
        PostProcess.cpp
        SimplePose.cpp)

# 根据目标架构设置编译选项
if (${ANDROID_ABI} STREQUAL "armeabi-v7a")
    set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES COMPILE_OPTIONS "-march=armv7-a;-mfloat-abi=softfp;-mfpu=vfpv3-d16")
elseif (${ANDROID_ABI} STREQUAL "arm64-v8a")
    set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES COMPILE_OPTIONS "-march=armv8-a")
endif()

# 添加 OpenCV 和 ncnn 的包含目录
target_include_directories(${CMAKE_PROJECT_NAME}
    PRIVATE
    ${OpenCV_INCLUDE_DIRS}
    ${ncnn_INCLUDE_DIRS})

# 指定链接库
target_link_libraries(
        ${CMAKE_PROJECT_NAME}
        PRIVATE
        ncnn
        ${OpenCV_LIBS}
        android
        log
        atomic
        c++_shared
        m  # 添加数学库
        dl # 添加动态链接库
)
