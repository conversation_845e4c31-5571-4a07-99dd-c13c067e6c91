//
// Created by 姚轩 on 2024/3/21.
//
#include "MonitorAlert.h"

MonitorAlert::MonitorAlert(int frames, float alert_threshold, std::string name):
        max_frames_to_save(frames),
        alert_thred(alert_threshold),
        alert_name(name)
{
    monitor_results.resize(max_frames_to_save);
    iter = monitor_results.begin();
}

bool MonitorAlert::getTempAlert(const MonitorResult& monitor_result) {
    if (alert_name == "focus_res"){
      return monitor_result.head_horizontal_res.label || monitor_result.head_vertical_res.label;
    }
    else if (alert_name == "body_res"){
      return monitor_result.body_res.label;
    }
    else if (alert_name == "head_horizontal_res"){
      return monitor_result.head_horizontal_res.label;
    }
    else if (alert_name == "head_vertical_res"){
      return monitor_result.head_vertical_res.label;
    }
    else if (alert_name == "find_res"){
      return monitor_result.find_res.label;
    }
    else if (alert_name == "long_distance_res"){
      return monitor_result.long_distance_res.label;
    }
    else if (alert_name == "short_distance_res"){
      return monitor_result.short_distance_res.label;
    }
    else if (alert_name == "is_in_screen_center_res"){
      return monitor_result.is_in_screen_center_res.label;
    }
    else if (alert_name == "wearing_color_glasses_red_blue_res"){
      return monitor_result.wearing_color_glasses_red_blue_res.label;
    }
    else if (alert_name == "wearing_color_glasses_blue_red_res"){
      return monitor_result.wearing_color_glasses_blue_red_res.label;
    }
    else {
      return false;
    }
}
void MonitorAlert::setFrameRes(const MonitorResult& monitor_result) {
    /*
    if(monitor_result.find_res.label){
        *iter = true;
    }else */if(getTempAlert(monitor_result)){
        *iter = true;
    }else{
        *iter = false;
    }
}

MonitorScore MonitorAlert::calcScore() {
    MonitorScore monitor_score;
    float frames = 0;
    for (vector<bool>::iterator _iter = monitor_results.begin(); _iter != monitor_results.end(); ++_iter){
        if (*_iter){
            frames += 1;
        }
    }
    monitor_score.score = frames / max_frames_to_save;
    if (monitor_score.score > alert_thred){
        monitor_score.label = true;
    }else{
        monitor_score.label = false;
    }
    return monitor_score;
}

void MonitorAlert::resetRes() {
    monitor_results.clear();
    monitor_results.resize(max_frames_to_save);
}

MonitorScore MonitorAlert::alert(const MonitorResult &monitor_result) {
    MonitorScore monitor_score;
    if(monitor_results.empty()){
        return monitor_score;
    }
    if(iter != monitor_results.end()){
        setFrameRes(monitor_result);
        ++iter;
    }else{
        iter = monitor_results.begin();
        setFrameRes(monitor_result);
    }
    monitor_score = calcScore();
    /**
     if (monitor_score.label){
        resetRes();
    }
     */
    return monitor_score;
}
