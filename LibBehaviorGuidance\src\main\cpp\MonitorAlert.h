//
// Created by 姚轩 on 2024/3/21.
//

#ifndef MONITORALGO_MONITORALERT_H
#define MONITORALGO_MONITORALERT_H

#include <vector>
#include "common.h"
#include <string>
using std::vector;

class MonitorAlert{
public:
    MonitorAlert(int frames = 30, float alert_threshold = 0.6, std::string name = "focus_res");
    MonitorScore alert(const MonitorResult& monitor_result);
    void resetRes();
private:
    void setFrameRes(const MonitorResult& monitor_result);
    MonitorScore calcScore();
    bool getTempAlert(const MonitorResult& monitor_result);

    int max_frames_to_save;
    float alert_thred;
    std::string alert_name;
    vector<bool> monitor_results;
    vector<bool>::iterator iter;
};

#endif //MONITORALGO_MONITORALERT_H
