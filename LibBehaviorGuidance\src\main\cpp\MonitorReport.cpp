//
// Created by skm on 2024/5/8.
//
#include "MonitorReport.h"

MonitorReport::MonitorReport():
      total_frames_num(0)
{
    // Define monitoring object values 第一个为报警帧数，第二个为帧数占比
    totalReport = new TotalReport;
    totalReport->body_res = ReportDetials(0,0.0);
    totalReport->head_horizontal_res = ReportDetials(0,0.0);
    totalReport->head_vertical_res = ReportDetials(0,0.0);
    totalReport->find_res = ReportDetials(0,0.0);
    totalReport->long_distance_res = ReportDetials(0,0.0);
    totalReport->short_distance_res = ReportDetials(0,0.0);
    totalReport->is_in_screen_center_res = ReportDetials(0,0.0);
    totalReport->wearing_color_glasses_red_blue_res = ReportDetials(0,0.0);
    totalReport->wearing_color_glasses_blue_red_res = ReportDetials(0,0.0);
    totalReport->focus_res = ReportDetials(0,0.0);
    totalReport->normal_res = ReportDetials(0,0.0);
}


void MonitorReport::resetRes() {
    // Define monitoring object values 第一个为报警帧数，第二个为帧数占比
    totalReport = new TotalReport;
    totalReport->body_res = ReportDetials(0,0.0);
    totalReport->head_horizontal_res = ReportDetials(0,0.0);
    totalReport->head_vertical_res = ReportDetials(0,0.0);
    totalReport->find_res = ReportDetials(0,0.0);
    totalReport->long_distance_res = ReportDetials(0,0.0);
    totalReport->short_distance_res = ReportDetials(0,0.0);
    totalReport->is_in_screen_center_res = ReportDetials(0,0.0);
    totalReport->wearing_color_glasses_red_blue_res = ReportDetials(0,0.0);
    totalReport->wearing_color_glasses_blue_red_res = ReportDetials(0,0.0);
    totalReport->focus_res = ReportDetials(0,0.0);
    totalReport->normal_res = ReportDetials(0,0.0);
  
    total_frames_num = 0;
}

void MonitorReport::add_to_report(const MonitorResult &monitor_result) {
    total_frames_num += 1;
    bool normal_flag = true;
    if (monitor_result.body_res.label) {
        totalReport->body_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.head_horizontal_res.label) {
        totalReport->head_horizontal_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.head_vertical_res.label) {
        totalReport->head_vertical_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.find_res.label) {
        totalReport->find_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.long_distance_res.label) {
        totalReport->long_distance_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.short_distance_res.label) {
        totalReport->short_distance_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.is_in_screen_center_res.label) {
        totalReport->is_in_screen_center_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.wearing_color_glasses_red_blue_res.label) {
        totalReport->wearing_color_glasses_red_blue_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.wearing_color_glasses_blue_red_res.label) {
        totalReport->wearing_color_glasses_blue_red_res.cnt += 1;
        normal_flag = false;
    }
    if (monitor_result.focus_res.label) {
        totalReport->focus_res.cnt += 1;
        normal_flag = false;
    }
    if (normal_flag) {
        totalReport->normal_res.cnt += 1;
    }

    totalReport->body_res.percent = float(totalReport->body_res.cnt) / total_frames_num;
    totalReport->head_horizontal_res.percent = float(totalReport->head_horizontal_res.cnt) / total_frames_num;
    totalReport->head_vertical_res.percent = float(totalReport->head_vertical_res.cnt) / total_frames_num;
    totalReport->find_res.percent = float(totalReport->find_res.cnt) / total_frames_num;
    totalReport->long_distance_res.percent = float(totalReport->long_distance_res.cnt) / total_frames_num;
    totalReport->short_distance_res.percent = float(totalReport->short_distance_res.cnt) / total_frames_num;
    totalReport->is_in_screen_center_res.percent = float(totalReport->is_in_screen_center_res.cnt) / total_frames_num;
    totalReport->wearing_color_glasses_red_blue_res.percent = float(totalReport->wearing_color_glasses_red_blue_res.cnt) / total_frames_num;
    totalReport->wearing_color_glasses_blue_red_res.percent = float(totalReport->wearing_color_glasses_blue_red_res.cnt) / total_frames_num;
    totalReport->focus_res.percent = float(totalReport->focus_res.cnt) / total_frames_num;
    totalReport->normal_res.percent = float(totalReport->normal_res.cnt) / total_frames_num;
}

int MonitorReport::getTotalFrames(){
    return total_frames_num;
};
