//
// Created by skm on 2024/5/8.
//

#ifndef MONITORALGO_MONITORREPORT_H
#define MONITORALGO_MONITORREPORT_H

#include <vector>
#include "common.h"
#include <map>

using std::vector;

class MonitorReport{
public:
    MonitorReport();
    void resetRes();
    void add_to_report(const MonitorResult& monitor_result);
    TotalReport *totalReport;
    int getTotalFrames();
private:
    int total_frames_num;
};

#endif //MONITORALGO_MONITORREPORT_H
