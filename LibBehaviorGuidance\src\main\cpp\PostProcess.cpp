//
//  yolo.cpp
//  DemoOne
//
//  Created by 宋凯敏 on 2024/3/5.
//

#include "PostProcess.h"

bool right_glass_flag = false;
bool left_glass_flag = false;

PostProcess::PostProcess() {
}

PostProcess::~PostProcess() {
}

/*
cv::Mat PostProcess::UIImageToMat(UIImage* image) {
    CGSize imageSize = image.size;
    // 创建一个大小和类型与UIImage匹配的cv::Mat对象
    cv::Mat cvImage(imageSize.height, imageSize.width, CV_8UC4);
      
    // 创建一个颜色空间转换上下文
    CGColorSpaceRef colorSpace = CGImageGetColorSpace(image.CGImage);
    CGContextRef context = CGBitmapContextCreate(cvImage.data, cvImage.cols, cvImage.rows, 8, cvImage.step[0], colorSpace, kCGImageAlphaPremultipliedLast | kCGBitmapByteOrderDefault);
      
    // 将UIImage绘制到上下文中
    CGContextDrawImage(context, CGRectMake(0, 0, imageSize.width, imageSize.height), image.CGImage);
      
    // 释放上下文和颜色空间
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
  
    cv::Mat rgbImage;
      
    // 将BGRA图像转换为RGB图像
    cv::cvtColor(cvImage, rgbImage, cv::COLOR_BGRA2RGB);
      
    // 返回RGB图像
    return rgbImage;
}
*/

std::vector<cv::Point> get_max_score_keypoints(const std::vector<PoseResult> pose_preds) {
    if (pose_preds.empty()) {
            return std::vector<cv::Point>();
        }
    std::vector<cv::Point> max_score_keypoints;
    
    // 找到 score 最大的 PoseResult 中的关键点数组
    float max_score = -1.0;
    for (const auto& pose : pose_preds) {
        if (pose.boxInfos.score > max_score) {
            max_score_keypoints.clear(); // 清空之前保存的关键点数组
            max_score = pose.boxInfos.score;
            for (const auto& keypoint : pose.keyPoints) {
                max_score_keypoints.push_back(keypoint.p);
            }
        }
    }

    return max_score_keypoints;
}

int get_max_face(const std::vector<FaceObject>& faceobjects){
    float max_area = -1;
    int idx = 0;
    for (size_t i = 0; i < faceobjects.size(); i++){
        float cur_area = faceobjects[i].rect.width * faceobjects[i].rect.height;
        if(cur_area > max_area){
            idx = i;
            max_area = cur_area;
        }
    }
    return idx;
}

MonitorScore PostProcess::get_body(cv::Point left_shoulder, cv::Point right_shoulder) {
    double angle = round(atan((double)(right_shoulder.y - left_shoulder.y) / (right_shoulder.x - left_shoulder.x)) / M_PI * 180.0 * 100.0) / 100.0;
    
    MonitorScore body_res;
    body_res.score = std::abs(angle);
    body_res.label = std::abs(angle) > body_thres;
    return body_res;
}

MonitorScore PostProcess::get_head_horizontal(const cv::Point2f& left_eye, const cv::Point2f& right_eye) {
    double angle = round(atan((right_eye.y - left_eye.y) / (right_eye.x - left_eye.x)) / M_PI * 180.0 * 100.0) / 100.0;
    
    MonitorScore head_horizontal_res;
    head_horizontal_res.score = std::abs(angle);
    head_horizontal_res.label = std::abs(angle) > head_horizontal_thres;
    return head_horizontal_res;
}

MonitorScore PostProcess::get_head_vertical(const cv::Point2f& left_eye,
                                            const cv::Point2f& right_eye,
                                            const cv::Point2f& mouth_left,
                                            const cv::Point2f& mouth_right,
                                            const cv::Point2f& nose) {
    cv::Point2f eye_middle((left_eye.x+right_eye.x)/2, (left_eye.y+right_eye.y)/2);
    cv::Point2f mouth_middle((mouth_left.x+mouth_right.x)/2, (mouth_left.y+mouth_right.y)/2);

    float eye_middle_to_nose = std::sqrt(std::pow((eye_middle.x-nose.x),2) + std::pow((eye_middle.y-nose.y),2));
    float mouth_middle_to_nose = std::sqrt(std::pow((mouth_middle.x-nose.x),2) + std::pow((mouth_middle.y-nose.y),2));
    
    MonitorScore head_vertical_res;
    head_vertical_res.score = mouth_middle_to_nose / eye_middle_to_nose;
    head_vertical_res.label = head_vertical_res.score < head_vertical_thres;
    return head_vertical_res;
}

std::pair<MonitorScore,MonitorScore> PostProcess::calculate_distance(const FaceObject& faceObject) {
    float face_width = faceObject.rect.width;
    float face_height = faceObject.rect.height;
    // 计算人脸的对角线长度（近似为人脸的对角线长度）
    float face_diagonal = std::sqrt(face_height * face_height + face_width * face_width);
    // 计算人与屏幕的距离（近似为屏幕的对角线长度除以比例）
    float distance = screen_diagonal_inch / face_diagonal;

    MonitorScore long_distance_res;
    long_distance_res.score = distance;
    long_distance_res.label = distance > distance_thres_high * fov_ratio;
    
    MonitorScore short_distance_res;
    short_distance_res.score = distance;
    short_distance_res.label = distance < distance_thres_low * fov_ratio;
    //std::cout << "print distance res: " << short_distance_res.score << ", " << std::endl<< std::endl;
    //std::cout << "print face_height res: " << face_height << ", " << std::endl<< std::endl;
    //std::cout << "print face_width res: " << face_width << ", " << std::endl<< std::endl;
    return std::make_pair(long_distance_res,short_distance_res);
}


MonitorScore PostProcess::is_in_screen_center(cv::Point left_eye, cv::Point right_eye,cv::Point nose,cv::Point left_ear,cv::Point right_ear) {
    // 计算人脸的中心位置
    double face_center_x = (left_eye.x + right_eye.x + nose.x + left_ear.x + right_ear.x) / 5.0;
    double face_center_y = (left_eye.y + right_eye.y + nose.y + left_ear.y + right_ear.y) / 5.0;
    
    // 获取屏幕大小
    //double screen_width = image.size.width;
    //double screen_height = image.size.height;
    // 计算屏幕中心位置
    double screen_center_x = screen_width / 2.0;
    double screen_center_y = screen_height / 2.0;

    // 计算人脸与屏幕中心的水平和垂直距离
    double horizontal_distance = std::abs(face_center_x - screen_center_x);
    double vertical_distance = std::abs(face_center_y - screen_center_y);
    double total_distance = std::sqrt(horizontal_distance * horizontal_distance + vertical_distance * vertical_distance);

    // 计算距离阈值
    double distance_threshold = std::sqrt(screen_width * screen_width + screen_height * screen_height) * screen_center_thres;

    // 判断人脸是否在屏幕中心附近
    MonitorScore is_in_screen_center_res;
    is_in_screen_center_res.score = total_distance;
    is_in_screen_center_res.label = total_distance >= distance_threshold;
    return is_in_screen_center_res;
}

std::pair<MonitorScore,MonitorScore> PostProcess::wearing_color_glasses(const FaceObject& faceObject, const cv::Mat& image) {

    MonitorScore color_glass_res_red_blue;
    MonitorScore color_glass_res_blue_red;
    // true == not wear glass
    color_glass_res_red_blue.label = true;
    color_glass_res_blue_red.label = true;

    cv::Point2f left_eye = faceObject.landmark[0];
    cv::Point2f right_eye = faceObject.landmark[1];

    double eye_dis = std::abs(left_eye.x - right_eye.x);
    int roi_dis = eye_dis/2;
    if(roi_dis < 1){
        return std::make_pair(color_glass_res_red_blue,color_glass_res_blue_red);
    }

    cv::Mat img;
    image.copyTo(img);

    // 选择脸的左下半部分，验证下图像BGR通道顺序
    int roi_x1 = std::min(std::max(0, static_cast<int>(faceObject.rect.x)), img.cols);
    int roi_y1 = std::min(std::max(0, static_cast<int>(faceObject.rect.y + faceObject.rect.height / 2)), img.rows);
    int roi_x2 = std::min(std::max(0, static_cast<int>(faceObject.rect.x + faceObject.rect.width / 2)), img.cols);
    int roi_y2 = std::min(std::max(0, static_cast<int>(faceObject.rect.y + faceObject.rect.height)), img.rows);
    cv::Mat check_roi = img(cv::Range(roi_y1, roi_y2), cv::Range(roi_x1, roi_x2));
    //int cur_time = clock();
    //cv::imwrite("/storage/emulated/0/Pictures/" + std::to_string(cur_time) + ".png", check_roi);
    cv::Mat check_roi_hsv;
    cv::cvtColor(check_roi, check_roi_hsv, cv::COLOR_BGR2HSV);
    cv::Mat check_roi_mask;
    cv::inRange(check_roi_hsv, cv::Scalar(100, 0, 0), cv::Scalar(150, 255, 255), check_roi_mask);
    float check_roi_area = (roi_x2 - roi_x1) * (roi_y2 - roi_y1);
    float check_roi_ratio = cv::countNonZero(check_roi_mask) / check_roi_area;
    // a trick to handle image format problem like lenovo
    if (check_roi_ratio > 0.6){
        cv::cvtColor(image, img, cv::COLOR_BGR2RGB);
        // 定义眼睛区域
        int left_x1 = std::max(0, static_cast<int>(left_eye.x - roi_dis));
        int left_x2 = std::min(static_cast<int>(left_eye.x + roi_dis), img.cols);
        int left_y1 = std::max(0, static_cast<int>(left_eye.y - roi_dis));
        int left_y2 = std::min(static_cast<int>(left_eye.y + roi_dis), img.rows);
        cv::Mat left_eye_img = img(cv::Range(left_y1, left_y2), cv::Range(left_x1, left_x2));

        // 提取眼睛区域
        int right_x1 = std::max(0, static_cast<int>(right_eye.x - roi_dis));
        int right_x2 = std::min(static_cast<int>(right_eye.x + roi_dis), img.cols);
        int right_y1 = std::max(0, static_cast<int>(right_eye.y - roi_dis));
        int right_y2 = std::min(static_cast<int>(right_eye.y + roi_dis), img.rows);
        cv::Mat right_eye_img = img(cv::Range(right_y1, right_y2), cv::Range(right_x1, right_x2));

        // 检查红色眼睛特征
        cv::Mat hsv_left_eye, hsv_right_eye;
        cv::cvtColor(left_eye_img, hsv_left_eye, cv::COLOR_BGR2HSV);
        cv::cvtColor(right_eye_img, hsv_right_eye, cv::COLOR_BGR2HSV);

        float eye_area = hsv_left_eye.rows * hsv_left_eye.cols;
        cv::Mat left_mask, right_mask;
        cv::inRange(hsv_left_eye, cv::Scalar(0, 0, 0), cv::Scalar(255, 255, 50), left_mask);
        cv::inRange(hsv_right_eye, cv::Scalar(0, 0, 0), cv::Scalar(255, 255, 50), right_mask);
        double left_ratio = cv::countNonZero(left_mask) / eye_area;
        double right_ratio = cv::countNonZero(right_mask) / eye_area;
        if ((left_ratio + right_ratio) / 2 > 0.4){
            color_glass_res_red_blue.label = false;
            color_glass_res_red_blue.score = (left_ratio + right_ratio) / 2;
            color_glass_res_blue_red.label = false;
            color_glass_res_blue_red.score = (left_ratio + right_ratio) / 2;
        }
    }else{
        // 定义眼睛区域
        int left_x1 = std::max(0, static_cast<int>(left_eye.x - roi_dis));
        int left_x2 = std::min(static_cast<int>(left_eye.x + roi_dis), img.cols);
        int left_y1 = std::max(0, static_cast<int>(left_eye.y - roi_dis));
        int left_y2 = std::min(static_cast<int>(left_eye.y + roi_dis), img.rows);
        cv::Mat left_eye_img = img(cv::Range(left_y1, left_y2), cv::Range(left_x1, left_x2));

        // 提取眼睛区域
        int right_x1 = std::max(0, static_cast<int>(right_eye.x - roi_dis));
        int right_x2 = std::min(static_cast<int>(right_eye.x + roi_dis), img.cols);
        int right_y1 = std::max(0, static_cast<int>(right_eye.y - roi_dis));
        int right_y2 = std::min(static_cast<int>(right_eye.y + roi_dis), img.rows);
        cv::Mat right_eye_img = img(cv::Range(right_y1, right_y2), cv::Range(right_x1, right_x2));

#if defined(DRAW_DEBUG)
        int cur_time = clock();
        //cv::imwrite("/storage/emulated/0/Pictures/" + std::to_string(cur_time) + "-left.png", left_eye_img);
        //cv::imwrite("/storage/emulated/0/Pictures/" + std::to_string(cur_time) + "-right.png", right_eye_img);
#endif

        // 检查红色眼睛特征
        cv::Mat hsv_left_eye, hsv_right_eye;
        cv::cvtColor(left_eye_img, hsv_left_eye, cv::COLOR_BGR2HSV);
        cv::cvtColor(right_eye_img, hsv_right_eye, cv::COLOR_BGR2HSV);

        float eye_area = hsv_left_eye.rows * hsv_left_eye.cols;
        cv::Mat left_mask, right_mask;
        cv::inRange(hsv_left_eye, cv::Scalar(0, 0, 0), cv::Scalar(50, 255, 255), left_mask);
        cv::inRange(hsv_right_eye, cv::Scalar(0, 0, 0), cv::Scalar(50, 255, 255), right_mask);
        double left_ratio = cv::countNonZero(left_mask) / eye_area;
        double right_ratio = cv::countNonZero(right_mask) / eye_area;

        // first step, if glasses detected
        if ((left_ratio + right_ratio) / 2 < 0.8){
            cv::Mat left_red_mask, left_blue_mask, right_red_mask, right_blue_mask;
            // check left eye red & blue info
            cv::inRange(hsv_left_eye, cv::Scalar(150, 0, 0), cv::Scalar(200, 255, 255), left_red_mask);
            cv::inRange(hsv_left_eye, cv::Scalar(100, 0, 0), cv::Scalar(150, 255, 255), left_blue_mask);
            double left_red_ratio = cv::countNonZero(left_red_mask) / eye_area;
            double left_blue_ratio = cv::countNonZero(left_blue_mask) / eye_area;
            // check right eye red & blue info
            cv::inRange(hsv_right_eye, cv::Scalar(150, 0, 0), cv::Scalar(200, 255, 255), right_red_mask);
            cv::inRange(hsv_right_eye, cv::Scalar(100, 0, 0), cv::Scalar(150, 255, 255), right_blue_mask);
            double right_red_ratio = cv::countNonZero(right_red_mask) / eye_area;
            double right_blue_ratio = cv::countNonZero(right_blue_mask) / eye_area;

            // assume left is red
            if ((left_red_ratio > left_blue_ratio) && (left_red_ratio > 0.3 || right_blue_ratio > 0.3)){
                color_glass_res_red_blue.label = false;
                // consider average of both eye as a score
                color_glass_res_red_blue.score = (left_red_ratio + right_blue_ratio) / 2;
            }
                // assume right is red
            else if ((right_red_ratio > right_blue_ratio) && (right_red_ratio > 0.3 || left_blue_ratio > 0.3)){
                color_glass_res_blue_red.label = false;
                color_glass_res_blue_red.score = (right_red_ratio + left_blue_ratio) / 2;
            }
            // if not falls to any condition, then we still think glass is not detected
        }
    }

    return std::make_pair(color_glass_res_red_blue,color_glass_res_blue_red);
}

/*
MonitorScore PostProcess::color_glass(cv::Point left_eye, cv::Point right_eye, cv::Mat image_rgb) {
    std::map<int, std::string> color_dict = {{0, "red"}, {1, "green"}, {2, "blue"}};
    double eye_dis = std::abs(eye.x - eye_other.x);
    eye_dis /= 6.0;

    int x1 = std::max(0, static_cast<int>(eye.x - eye_dis / 2));
    int x2 = std::min(static_cast<int>(eye.x + eye_dis / 2), image_rgb.cols);
    int y1 = std::max(0, static_cast<int>(eye.y - eye_dis / 2));
    int y2 = std::min(static_cast<int>(eye.y + eye_dis / 2), image_rgb.rows);

    cv::Mat image_region_left = image_rgb(cv::Range(y1, y2), cv::Range(x1, x2));
    std::vector<double> other_list;
    double res_color = 0;
    for (int i = 0; i < 3; ++i) {
        double res_i = cv::sum(image_region)[i] / static_cast<double>((y2 - y1) * (x2 - x1));
        if (i == 0) {
            res_color = res_i;
        } else {
            other_list.push_back(res_i);
        }
    }

    MonitorScore color_glass_res;
    color_glass_res.score = std::round(std::accumulate(other_list.begin(), other_list.end(), 0.0) / 2.0 * 100.0) / 100.0;
    color_glass_res.label = res_color > color_glass_thres && other_list[0] <= color_glass_thres && other_list[1] < color_glass_thres;
    return color_glass_res;
}
**/

void PostProcess::adapt_params(float fieldOfView){
    // 78.73 is the default fieldOfView of many devices based on which we use the default distance_thres_high and distance_thres_low
    if (fieldOfView > 0){
        fov_ratio = fieldOfView / 78.73 * 1.1;
    }else{
        fov_ratio = 1.0;
    }
}

MonitorResult PostProcess::get_pose_dict(const std::vector<FaceObject>& faceobjects, const cv::Mat& image) {
    
    //std::vector<cv::Point> pose_pred = get_max_score_keypoints(pose_preds);
    //std::map<std::string, std::pair<double, bool>> pose_dict;
    MonitorResult result;

    if (faceobjects.empty()) {
        // 将参数写入 MonitorResult 结构体中
        result.body_res.label = false;
        result.head_horizontal_res.label = false;
        result.head_vertical_res.label = false;
        result.find_res.label = true;
        result.short_distance_res.label = false;
        result.long_distance_res.label = false;
        //result.distance_res = {0, false};
        //result.is_in_screen_center_res = {0, false};
#if defined(DRAW_DEBUG)
        int cur_time = clock();
        cv::imwrite("/storage/emulated/0/Pictures/" + std::to_string(cur_time) + ".png", image);
#endif
    } else {
        int idx = get_max_face(faceobjects);

#if defined(DRAW_DEBUG)
        int cur_time = clock();
        cv::Mat draw_image;
        image.copyTo(draw_image);
        cv::Rect_<float> cur_rect = faceobjects[idx].rect;
        cv::rectangle(draw_image, cur_rect, cv::Scalar(0, 255, 0));
        for(int pt = 0; pt < 5; pt++){
            cv::circle(draw_image, faceobjects[idx].landmark[pt], 3, cv::Scalar( 0, 0, 255 / (pt+1) ), cv::FILLED, cv::LINE_8);
        }
        cv::imwrite("/storage/emulated/0/Pictures/" + std::to_string(cur_time) + ".png", draw_image);
#endif

        // landmark[5]: 0: left_eye, 1: right_eye, 2: nose, 3: mouth_left, 4: mouth_right
        cv::Point2f nose = faceobjects[idx].landmark[2];
        cv::Point2f left_eye = faceobjects[idx].landmark[0];
        cv::Point2f right_eye = faceobjects[idx].landmark[1];
        cv::Point2f mouth_left = faceobjects[idx].landmark[3];
        cv::Point2f mouth_right = faceobjects[idx].landmark[4];
        screen_width = image.cols;
        screen_height = image.rows;
        screen_diagonal_inch = std::sqrt(screen_width * screen_width + screen_height * screen_height);

        //result.body_res = get_body(left_shoulder, right_shoulder);
        result.head_horizontal_res = get_head_horizontal(left_eye, right_eye);
        result.head_vertical_res = get_head_vertical(left_eye, right_eye, mouth_left, mouth_right, nose);
        //result.is_in_screen_center_res = is_in_screen_center(left_eye, right_eye, nose, left_ear, right_ear);

        std::pair<MonitorScore,MonitorScore> tmp_distance_pair  = calculate_distance(faceobjects[idx]);
        result.long_distance_res = tmp_distance_pair.first;
        result.short_distance_res = tmp_distance_pair.second;

        std::pair<MonitorScore,MonitorScore> tmp_glass_pair = wearing_color_glasses(faceobjects[idx], image);
        result.wearing_color_glasses_red_blue_res = tmp_glass_pair.first;
        result.wearing_color_glasses_blue_red_res = tmp_glass_pair.second;
    };

    return result;
}


