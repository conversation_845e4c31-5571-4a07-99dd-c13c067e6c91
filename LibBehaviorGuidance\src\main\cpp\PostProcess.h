//
//  yolo.hpp
//  DemoOne
//
//  Created by 宋凯敏 on 2024/3/5.
//

#ifndef PostProcess_H
#define PostProcess_H

#include <map>
#include <iostream>
#include <cmath>
#include <stdio.h>
#include <vector>
#include <opencv2/opencv.hpp>
#include <numeric>

#include "MonitorAlert.h"
#include "scrfd.h"

//#define DRAW_DEBUG


struct KeyPoint {
    cv::Point2f p;
    float prob;
};

typedef struct BoxInfo {
    float x1;
    float y1;
    float x2;
    float y2;
    float score;
    int label;
}BoxInfo;

struct PoseResult {
    std::vector<KeyPoint> keyPoints;
    BoxInfo boxInfos;
};

class PostProcess {
public:
    PostProcess();
    
    ~PostProcess();
    
    MonitorResult get_pose_dict(const std::vector<FaceObject>& faceobjects, const cv::Mat& image);

    void adapt_params(float fieldOfView);
    
private:
    //cv::Mat UIImageToMat(UIImage* image);
    MonitorScore get_body(cv::Point left_shoulder, cv::Point right_shoulder);
    MonitorScore get_head_horizontal(const cv::Point2f& left_eye, const cv::Point2f& right_eye);
    MonitorScore get_head_vertical(const cv::Point2f& left_eye,
                                   const cv::Point2f& right_eye,
                                   const cv::Point2f& mouth_left,
                                   const cv::Point2f& mouth_right,
                                   const cv::Point2f& nose);
    std::pair<MonitorScore,MonitorScore> calculate_distance(const FaceObject& faceObject);
    MonitorScore is_in_screen_center(cv::Point left_eye, cv::Point right_eye,cv::Point nose , cv::Point left_ear, cv::Point right_ear);
    std::pair<MonitorScore,MonitorScore> wearing_color_glasses(const FaceObject& faceObject, const cv::Mat& image);
    int body_thres = 80; //高低肩报警角度阈值
    int head_horizontal_thres = 10; //歪头报警角度阈值
    float head_vertical_thres = 0.4; //低头报警角度阈值
    float distance_thres_low = 2.5; //距离过近报警阈值下限
    float distance_thres_high = 5; //距离过远报警阈值上限
    float screen_center_thres = 0.25; //距离屏幕中央与对角线像素距离的比值阈值
    float color_glass_thres = 0.6; //佩戴红蓝眼镜的像素成功占比阈值。   #预测红蓝眼镜像素值总和 / 眼睛周围像素值总和
    
    int screen_width = 256;
    int screen_height = 196;
    // 假设屏幕对角线长度为 24 英寸（单位为像素）
    float screen_diagonal_inch = 24.0;

    float fov_ratio = 1.0;
    
public:
    
};


#endif //PostProcess_H
