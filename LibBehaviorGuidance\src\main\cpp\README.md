# MonitorAlgo

The cpp module of vision training monitor

## 接口

参考头文件SimplePose.h, 接口方法如下

模块构造函数，包括两个默认参数，AAssetManager类型指针用于获取模型以及选择是否使用GPU的参数to_use_GPU（目前首先支持使用CPU，所以传参数false）
```
SimplePose(AAssetManager *amgr, bool to_use_GPU);
```

模块析构函数
```
~SimplePose();
```

模型推理：
目前接受一个ncnn::Mat类型的常量引用，以及图像宽和高
```
MonitorResult detect(const ncnn::Mat& ncnn_img, int img_w, int img_h);
```

## 使用用例 （in native-lib.cpp）
```
#include <jni.h>
#include <string>

#include <android/asset_manager_jni.h>
#include "monitoralgo/SimplePose.h"

extern "C" JNIEXPORT jstring

JNICALL
Java_com_example_monitoralgo_MainActivity_stringFromJNI(
        JNIEnv *env,
        jobject /* this */,
        jobject assetManager,
        jobject image) {
    std::string hello = "Hello from C++";

    AAssetManager* mgr = AAssetManager_fromJava(env, assetManager);

    // initialize
    //SimplePose simplePose(mgr, false);
    SimplePose* sp = new SimplePose(mgr, false);

    AndroidBitmapInfo info;
    AndroidBitmap_getInfo(env, image, &info);

    // convert from android Bitmap to ncnn::Mat
    ncnn::Mat ncnn_img = ncnn::Mat::from_android_bitmap_resize(env, image, ncnn::Mat::PIXEL_RGBA2RGB,
                                                              info.width, info.height);
    // infer an image
    //simplePose.detect(ncnn_img, info.width, info.height);
    sp->detect(ncnn_img, info.width, info.height);

    return env->NewStringUTF(hello.c_str());
}
```

## 算法部分集成到Android

### 算法模型：
将[模型文件](https://github.com/cmdbug/YOLOv5_NCNN/tree/master/android_YOLOV5_NCNN/app/src/main/assets)person_detector.bin、person_detector.param、Ultralight-Nano-SimplePose.bin以及Ultralight-Nano-SimplePose.param下载到app/src/main/assets中

### 算法代码
```
git clone http://**********/visiontraining/monitoralgo
```
切换分支到dev-android，软链接（或者拷贝）算法项目到android项目cpp路径下
```
ln -s monitoralgo app/src/main/cpp
```

### 依赖库
下载[ncnn库](https://github.com/Tencent/ncnn/releases)ncnn-20240410-android-vulkan.zip并解压缩到app/src/mian/cpp/monitoralgo

下载[opencv库](https://github.com/nihui/opencv-mobile/releases/tag/v26)opencv-mobile-4.9.0-android.zip并解压缩到app/src/mian/cpp/monitoralgo

### cmake编译文件修改
修改android项目中app/src/mian/cpp目录下的根CMakeLists.txt文件，增加
```
add_subdirectory(monitoralgo)
```
链接库命令中修改如下：
```
target_link_libraries(${CMAKE_PROJECT_NAME}
        monitor
        # List libraries link to the target library
        android
        log)
```
CMakeLists文件示例
```
cmake_minimum_required(VERSION 3.22.1)

project("FrameProcess")

set(OpenCV_DIR ${CMAKE_CURRENT_SOURCE_DIR}/opencv-mobile-4.9.0-android/sdk/native/jni)
find_package(OpenCV REQUIRED core imgproc)

set(ncnn_DIR ${CMAKE_CURRENT_SOURCE_DIR}/ncnn-20240410-android-vulkan/${ANDROID_ABI}/lib/cmake/ncnn)
find_package(ncnn REQUIRED)

add_library(
        ${CMAKE_PROJECT_NAME}
        SHARED
        # List C/C++ source files with relative paths to this CMakeLists.txt.
        native-lib.cpp
        MonitorAlert.cpp
        PostProcess.cpp
        SimplePose.cpp)

target_link_libraries(${CMAKE_PROJECT_NAME}
    # List libraries link to the target library
    PUBLIC ncnn ${OpenCV_LIBS}
    android
    log)
```

### Future to do
后续将不提供源码，只提供android算法库，方便app集成。

## 算法部分集成到ios

### 依赖库

* ios simulator

https://github.com/Tencent/ncnn/releases 

下载ncnn-20240102-ios-simulator.zip

* ios

https://github.com/Tencent/ncnn/releases 

下载ncnn-20240102-ios.zip

### 相关模型
将模型文件person_detector.bin, person_detector.param, Ultralight-Nano-SimplePose.bin以及Ultralight-Nano-SimplePose.param放到项目下相关路径中，程序会根据名字自动找到相关模型