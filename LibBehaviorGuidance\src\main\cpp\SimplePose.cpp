//
// Created by 姚轩 on 2024/04/17 017.
//

#include "SimplePose.h"

SimplePose::SimplePose(AAssetManager *amgr, bool to_use_GPU)
: mgr(amgr) , use_GPU(to_use_GPU)
{
    int gpu_count = ncnn::get_gpu_count();
    use_GPU = (gpu_count > 0) && use_GPU;
    /*
    PersonNet = new ncnn::Net();
    PersonNet->opt.use_vulkan_compute = use_GPU;
    PersonNet->opt.use_fp16_arithmetic = true;
    int rp = PersonNet->load_param(mgr, "person_detector.param");
    int rm = PersonNet->load_model(mgr, "person_detector.bin");
    if (rp == 0 && rm == 0) {
        printf("net load param and model success!\n");
    } else {
        fprintf(stderr, "net load fail,param:%d model:%d\n", rp, rm);
    }
    */
    g_scrfd = new SCRFD;
    g_scrfd->load(mgr, "500m_kps", use_GPU);

    /*
    PoseNet = new ncnn::Net();
    PoseNet->opt.use_vulkan_compute = use_GPU;
    PoseNet->opt.use_fp16_arithmetic = true;
    int rp = PoseNet->load_param(mgr, "Ultralight-Nano-SimplePose.param");
    int rm = PoseNet->load_model(mgr, "Ultralight-Nano-SimplePose.bin");
    if (rp == 0 && rm == 0) {
        printf("net load param and model success!\n");
    } else {
        fprintf(stderr, "net load fail,param:%d model:%d\n", rp, rm);
    }
     */
    
    // initialize the postprocess and monitoralert
    postProcess = new PostProcess();

    bodyAlert = new MonitorAlert(30,0.8,"body_res");
    headHorizontalAlert = new MonitorAlert(5,0.8,"head_horizontal_res");
    headVerticalAlert = new MonitorAlert(5,0.6,"head_vertical_res");
    findAlert = new MonitorAlert(5,0.8,"find_res");
    longDistanceAlert = new MonitorAlert(5,0.8,"long_distance_res");
    shortDistanceAlert = new MonitorAlert(5,0.8,"short_distance_res");
    isInScreenCenterAlert = new MonitorAlert(30,0.8,"is_in_screen_center_res");
    redBlueAlert = new MonitorAlert(5,0.6,"wearing_color_glasses_red_blue_res");
    blueRedAlert = new MonitorAlert(5,0.6,"wearing_color_glasses_blue_red_res");
    focusAlert = new MonitorAlert(20,0.5,"focus_res");

    // generate report module
    monitorReport = new MonitorReport();
}

SimplePose::~SimplePose() {
    delete g_scrfd;
    //PersonNet->clear();
    //PoseNet->clear();
    //delete PersonNet;
    //delete PoseNet;
    delete postProcess;
    delete bodyAlert;
    delete headHorizontalAlert;
    delete headVerticalAlert;
    delete findAlert;
    delete longDistanceAlert;
    delete shortDistanceAlert;
    delete isInScreenCenterAlert;
    delete redBlueAlert;
    delete blueRedAlert;
    delete focusAlert;
    delete monitorReport;
    //删除后，专注模块的3秒内监控会被清空
}

ncnn::Mat SimplePose::preprocess(void *raw_img, int img_w, int img_h,
                            int degree) {
    cv::Mat cvSrcMat(img_h * 3 / 2, img_w, CV_8UC1, raw_img);
    cv::Mat cvDstMat(img_h, img_w, CV_8UC3);
    cv::cvtColor(cvSrcMat, cvDstMat, cv::COLOR_YUV2BGR_I420);

    // rotate according to camera
    if (90 == degree){
        cv::rotate(cvDstMat, cvDstMat, cv::ROTATE_90_CLOCKWISE);
    }else if(180 == degree){
        cv::rotate(cvDstMat, cvDstMat, cv::ROTATE_180);
    }else if(270 == degree){
        cv::rotate(cvDstMat, cvDstMat, cv::ROTATE_90_COUNTERCLOCKWISE);
    }

    ncnn::Mat ncnnMat = ncnn::Mat::from_pixels(cvDstMat.data, ncnn::Mat::PIXEL_BGR2RGB, img_w, img_h);
    return ncnnMat;
}

/*
int SimplePose::runpose(cv::Mat &roi, int pose_size_w, int pose_size_h, std::vector<KeyPoint> &keypoints,
                        float x1, float y1) {
    int w = roi.cols;
    int h = roi.rows;
    ncnn::Mat in = ncnn::Mat::from_pixels_resize(roi.data, ncnn::Mat::PIXEL_BGR2RGB, \
                                                 roi.cols, roi.rows, pose_size_w, pose_size_h);
//    LOGD("in w:%d h:%d", roi.cols, roi.rows);
    //数据预处理
    const float mean_vals[3] = {0.485f * 255.f, 0.456f * 255.f, 0.406f * 255.f};
    const float norm_vals[3] = {1 / 0.229f / 255.f, 1 / 0.224f / 255.f, 1 / 0.225f / 255.f};
    in.substract_mean_normalize(mean_vals, norm_vals);

    auto ex = PoseNet->create_extractor();
    ex.set_light_mode(true);
    //ex.set_num_threads(4); 用于指定线程如果使用可能会报很多log
    if (use_GPU) {  // 消除提示
        ex.set_vulkan_compute(use_GPU);
    }
    ex.input("data", in);
    ncnn::Mat out;
    ex.extract("hybridsequential0_conv7_fwd", out);
    keypoints.clear();
    for (int p = 0; p < out.c; p++) {
        const ncnn::Mat m = out.channel(p);

        float max_prob = 0.f;
        int max_x = 0;
        int max_y = 0;
        for (int y = 0; y < out.h; y++) {
            const float *ptr = m.row(y);
            for (int x = 0; x < out.w; x++) {
                float prob = ptr[x];
                if (prob > max_prob) {
                    max_prob = prob;
                    max_x = x;
                    max_y = y;
                }
            }
        }

        KeyPoint keypoint;
        keypoint.p = cv::Point2f(max_x * w / (float) out.w + x1, max_y * h / (float) out.h + y1);
        keypoint.prob = max_prob;
        keypoints.push_back(keypoint);
    }
    return 0;
}
 */

PostProcess* SimplePose::getPostProcess(){
    return postProcess;
}

MonitorResult SimplePose::detect(const ncnn::Mat& ncnn_img, int img_w, int img_h) {
    //ncnn::Mat in_net = ncnn::Mat::from_pixels_resize((unsigned char *)ncnn_img.data, ncnn::Mat::PIXEL_RGB, img_w, img_h, detector_size_width, detector_size_height);

    // construct an cv::Mat from original ncnn::Mat
    cv::Mat cv_img(ncnn_img.h, ncnn_img.w, CV_8UC3);
    ncnn_img.to_pixels(cv_img.data, ncnn::Mat::PIXEL_RGB2BGR);

    cv::Mat rgb;
    cv::cvtColor(cv_img, rgb, cv::COLOR_BGR2RGB);

    std::vector<FaceObject> faceobjects;
    g_scrfd->detect(rgb, faceobjects);

    //ways to postprocess the posedict and give out the final result
    MonitorResult final_res_per_frame = postProcess->get_pose_dict(faceobjects, cv_img);

    MonitorScore body_res = bodyAlert->alert(final_res_per_frame);
    MonitorScore head_horizontal_res = headHorizontalAlert->alert(final_res_per_frame);
    MonitorScore head_vertical_res = headVerticalAlert->alert(final_res_per_frame);
    MonitorScore find_res = findAlert->alert(final_res_per_frame);
    MonitorScore long_distance_res = longDistanceAlert->alert(final_res_per_frame);
    MonitorScore short_distance_res = shortDistanceAlert->alert(final_res_per_frame);
    MonitorScore is_in_screen_center_res = isInScreenCenterAlert->alert(final_res_per_frame);
    MonitorScore wearing_color_glasses_red_blue_res = redBlueAlert->alert(final_res_per_frame);
    MonitorScore wearing_color_glasses_blue_red_res = blueRedAlert->alert(final_res_per_frame);
    MonitorScore focus_res = focusAlert->alert(final_res_per_frame);

    MonitorResult final_res;
    final_res.body_res = body_res;
    final_res.head_horizontal_res = head_horizontal_res;
    final_res.head_vertical_res = head_vertical_res;
    final_res.find_res = find_res;
    final_res.long_distance_res = long_distance_res;
    final_res.short_distance_res = short_distance_res;
    final_res.is_in_screen_center_res = is_in_screen_center_res;
    final_res.wearing_color_glasses_red_blue_res = wearing_color_glasses_red_blue_res;
    final_res.wearing_color_glasses_blue_red_res = wearing_color_glasses_blue_red_res;
    final_res.focus_res = focus_res;

    // if focus_res.label is true, then report focus
    if (true == final_res.focus_res.label)
    {
        final_res.head_horizontal_res = {0, false};
        final_res.head_vertical_res = {0, false};
        headHorizontalAlert -> resetRes();
        headVerticalAlert -> resetRes();
    }

    // add to the final report
    monitorReport->add_to_report(final_res_per_frame);
  
    return final_res;
}

MonitorResult SimplePose::detect(void *raw_img, int img_w, int img_h, int degree) {
    //ncnn::Mat src_img = ncnn::Mat::from_pixels_resize(rgba, ncnn::Mat::PIXEL_RGBA2RGB, img_w, img_h, img_w, img_h);

    ncnn::Mat ncnn_img = preprocess(raw_img, img_w, img_h, degree);
    MonitorResult final_res;
    final_res = detect(ncnn_img, img_w, img_h);

    return final_res;
}

/*
void SimplePose::drawRect(UIImage *image, const std::vector<PoseResult>& poseResults){
    int img_w = image.size.width;
    int img_h = image.size.height;
    unsigned char* rgba = new unsigned char[img_w * img_h * 4];
    CGColorSpaceRef colorSpace = CGImageGetColorSpace(image.CGImage);
    CGContextRef contextRef = CGBitmapContextCreate(rgba, img_w, img_h, 8, img_w * 4, colorSpace, kCGImageAlphaNoneSkipLast | kCGBitmapByteOrderDefault);
    CGContextDrawImage(contextRef, CGRectMake(0, 0, img_w, img_h), image.CGImage);
    CGContextRelease(contextRef);
    
    ncnn::Mat src_img = ncnn::Mat::from_pixels_resize(rgba, ncnn::Mat::PIXEL_RGBA2RGB, img_w, img_h, img_w, img_h);
    cv::Mat bgr(src_img.h, src_img.w, CV_8UC3);
    src_img.to_pixels(bgr.data, ncnn::Mat::PIXEL_RGB2BGR);
    for (size_t i = 0; i < poseResults.size(); i++){
        cv::rectangle(bgr, cv::Point(poseResults[i].boxInfos.x1, poseResults[i].boxInfos.y1), cv::Point(poseResults[i].boxInfos.x2, poseResults[i].boxInfos.y2), cv::Scalar(0,255,0), 3);
    }
    cv::imwrite("/Users/<USER>/Desktop/samples/walkman-det-res.png", bgr);
}
*/