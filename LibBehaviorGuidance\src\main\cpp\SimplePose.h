//
// Created by 姚轩 on 2024/04/17 017.
//

#ifndef YOLOV5_SIMPLEPOSE_H
#define YOLOV5_SIMPLEPOSE_H

#include <vector>
#include <algorithm>
#include <string>

#include <opencv2/core/core.hpp>
#include <opencv2/highgui.hpp>
#include <opencv2/imgproc.hpp>
#include "net.h"

//#include "scrfd.h"
#include "PostProcess.h"
#include "MonitorReport.h"
//#include <opencv2/core/types.hpp>

class SimplePose {
public:
    SimplePose(AAssetManager *amgr, bool to_use_GPU);
    MonitorResult detect(const ncnn::Mat& ncnn_img, int img_w, int img_h);
    MonitorResult detect(void *raw_img, int img_w, int img_h, int degree);
    ~SimplePose();
    MonitorReport *monitorReport;
    PostProcess* getPostProcess();
    //void drawRect(UIImage *image, const std::vector<PoseResult>& poseResults);

private:
    ncnn::Mat preprocess(void *raw_img, int img_w, int img_h, int degree);
    //int runpose(cv::Mat &roi, int pose_size_width, int pose_size_height, std::vector<KeyPoint> &keypoints, float x1, float y1);

private:
    //ncnn::Net *PersonNet;
    SCRFD* g_scrfd;
    //ncnn::Net *PoseNet;
    PostProcess *postProcess;
    MonitorAlert *bodyAlert;
    MonitorAlert *headHorizontalAlert;
    MonitorAlert *headVerticalAlert;
    MonitorAlert *findAlert;
    MonitorAlert *longDistanceAlert;
    MonitorAlert *shortDistanceAlert;
    MonitorAlert *isInScreenCenterAlert;
    MonitorAlert *redBlueAlert;
    MonitorAlert *blueRedAlert;
    MonitorAlert *focusAlert;
    int detector_size_width = 320;
    int detector_size_height = 320;
    int pose_size_width = 192;
    int pose_size_height = 256;
    bool use_GPU;
    AAssetManager *mgr;
};
#endif //YOLOV5_SIMPLEPOSE_H
