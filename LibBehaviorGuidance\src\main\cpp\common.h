//
// Created by 姚轩 on 2024/3/21.
//
#ifndef COMMON_H
#define COMMON_H

struct MonitorScore {
    MonitorScore(float s = 0.0, bool l = false): score(s), label(l) {}
    float score;
    bool label;
};

struct MonitorResult {
    MonitorScore body_res; //是否高低肩(label=true表示高低肩)
    MonitorScore head_horizontal_res; //是否歪头(label=true表示歪头)
    MonitorScore head_vertical_res; //是否低头(label=true表示低头)
    MonitorScore find_res; //是否监测到人脸(label=true表示未监测到人脸)
    MonitorScore long_distance_res; //是否距离过远(label=true表示距离过远)
    MonitorScore short_distance_res; //是否距离过近(label=true表示距离过近)
    MonitorScore is_in_screen_center_res; //是否在屏幕中心(label=true表示不在屏幕中心)
    MonitorScore wearing_color_glasses_red_blue_res; //是否佩戴红蓝眼镜 左红右蓝(label=true表示未佩戴红蓝眼镜)
    MonitorScore wearing_color_glasses_blue_red_res; //是否佩戴蓝红眼镜 左蓝右红(label=true表示未佩戴蓝红眼镜)
    MonitorScore focus_res; //是否专注(label=true表示不专注)
};

struct ReportDetials {
    ReportDetials(int c = 0, float p = 0.0): cnt(c), percent(p) {}
    int cnt;
    float percent;
};

struct TotalReport {
    ReportDetials normal_res;
    ReportDetials body_res; //是否高低肩
    ReportDetials head_horizontal_res; //是否歪头
    ReportDetials head_vertical_res; //是否低头
    ReportDetials find_res; //是否监测到人脸
    ReportDetials long_distance_res; //是否距离过远
    ReportDetials short_distance_res; //是否距离过近
    ReportDetials is_in_screen_center_res; //是否在屏幕中心
    ReportDetials wearing_color_glasses_red_blue_res; //是否佩戴红蓝眼镜 左红右蓝
    ReportDetials wearing_color_glasses_blue_red_res; //是否佩戴蓝红眼镜 左蓝右红
    ReportDetials focus_res; //是否专注
};
#endif // COMMON_H
