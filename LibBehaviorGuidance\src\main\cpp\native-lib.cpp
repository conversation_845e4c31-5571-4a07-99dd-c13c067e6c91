#include <jni.h>
#include <string>
#include <unistd.h>
#include <android/log.h>
#include <android/asset_manager_jni.h>
#include <android/bitmap.h>
#include "SimplePose.h"
//#include <time.h>

#define  LOG_TAG    "DEVICE SERIAL MODULE"
#define  LOGI(...)  __android_log_print(ANDROID_LOG_INFO,LOG_TAG,__VA_ARGS__)

extern "C"
JNIEXPORT jlong JNICALL
Java_com_mitdd_behaviorguidance_BehaviorGuidance_nativeCreateObject(JNIEnv *env, jobject clazz, jobject assetManager) {
    AAssetManager* mgr = AAssetManager_fromJava(env, assetManager);
    SimplePose* simplePose = new SimplePose(mgr, true);
    // 返回simplePose地址给Java层
    return (jlong) simplePose;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_mitdd_behaviorguidance_BehaviorGuidance_nativeFrameProcess(JNIEnv *env, jobject clazz, jlong thiz, jbyteArray inputImage_, jint width, jint height,
                                                              jint rotationDegrees, jfloat fieldOfView) {

    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jmethodID hashMapPut = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");

    jobject hashMap = env->NewObject(hashMapClass, hashMapConstructor);

    if (thiz == 0) {
        return hashMap;
    }

    auto *simplePose = reinterpret_cast<SimplePose *>(thiz);

    // 将图片数据转化为jbyte
    jbyte *inputImage = env->GetByteArrayElements(inputImage_, 0);

    // 将jfloat转换为float
    float fieldOfViewFloat = static_cast<float>(fieldOfView);
    // adapt the params of postProcess to the fieldOfView of the device
    simplePose->getPostProcess()->adapt_params(fieldOfViewFloat);

    // infer an image
    MonitorResult monitorResult = simplePose->detect(inputImage,width,height,rotationDegrees);

    // 将 jboolean 转换为 Boolean 对象
    jclass booleanClass = env->FindClass("java/lang/Boolean");
    jmethodID booleanConstructor = env->GetMethodID(booleanClass, "<init>", "(Z)V");

    // 将 jfloat 转换为 Float 对象
    jclass floatClass = env->FindClass("java/lang/Float");
    jmethodID floatConstructor = env->GetMethodID(floatClass, "<init>", "(F)V");

    // 将 jint 转换为 Integer 对象
    jclass intClass = env->FindClass("java/lang/Integer");
    jmethodID intConstructor = env->GetMethodID(intClass, "<init>", "(I)V");

    //是否高低肩
    jobject bodyResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject bodyResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.body_res.score));
    jboolean body_res_label = monitorResult.body_res.label;
    jobject bodyResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(body_res_label));
    env->CallObjectMethod(bodyResHashMap, hashMapPut, env->NewStringUTF("score"), bodyResScore);
    env->CallObjectMethod(bodyResHashMap, hashMapPut, env->NewStringUTF("label"), bodyResLabel);

    //是否歪头
    jobject headHorizontalResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject headHorizontalResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.head_horizontal_res.score));
    jboolean head_horizontal_res_label = monitorResult.head_horizontal_res.label;
    jobject headHorizontalResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(head_horizontal_res_label));
    env->CallObjectMethod(headHorizontalResHashMap, hashMapPut, env->NewStringUTF("score"), headHorizontalResScore);
    env->CallObjectMethod(headHorizontalResHashMap, hashMapPut, env->NewStringUTF("label"), headHorizontalResLabel);

    //是否低头
    jobject headVerticalResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject headVerticalResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.head_vertical_res.score));
    jboolean head_vertical_res_label = monitorResult.head_vertical_res.label;
    jobject headVerticalResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(head_vertical_res_label));
    env->CallObjectMethod(headVerticalResHashMap, hashMapPut, env->NewStringUTF("score"), headVerticalResScore);
    env->CallObjectMethod(headVerticalResHashMap, hashMapPut, env->NewStringUTF("label"), headVerticalResLabel);

    //是否监测到人脸
    jobject findResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject findResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.find_res.score));
    jboolean find_res_label = monitorResult.find_res.label;
    jobject findResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(find_res_label));
    env->CallObjectMethod(findResHashMap, hashMapPut, env->NewStringUTF("score"), findResScore);
    env->CallObjectMethod(findResHashMap, hashMapPut, env->NewStringUTF("label"), findResLabel);

    //是否距离过远
    jobject longDistanceResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject longDistanceResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.long_distance_res.score));
    jboolean long_distance_res_label = monitorResult.long_distance_res.label;
    jobject longDistanceResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(long_distance_res_label));
    env->CallObjectMethod(longDistanceResHashMap, hashMapPut, env->NewStringUTF("score"), longDistanceResScore);
    env->CallObjectMethod(longDistanceResHashMap, hashMapPut, env->NewStringUTF("label"), longDistanceResLabel);

    //是否距离过近
    jobject shortDistanceResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject shortDistanceResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.short_distance_res.score));
    jboolean short_distance_res_label = monitorResult.short_distance_res.label;
    jobject shortDistanceResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(short_distance_res_label));
    env->CallObjectMethod(shortDistanceResHashMap, hashMapPut, env->NewStringUTF("score"), shortDistanceResScore);
    env->CallObjectMethod(shortDistanceResHashMap, hashMapPut, env->NewStringUTF("label"), shortDistanceResLabel);

    //是否在屏幕中心
    jobject isInScreenCenterResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject isInScreenCenterResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.is_in_screen_center_res.score));
    jboolean is_in_screen_center_res_label = monitorResult.is_in_screen_center_res.label;
    jobject isInScreenCenterResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(is_in_screen_center_res_label));
    env->CallObjectMethod(isInScreenCenterResHashMap, hashMapPut, env->NewStringUTF("score"), isInScreenCenterResScore);
    env->CallObjectMethod(isInScreenCenterResHashMap, hashMapPut, env->NewStringUTF("label"), isInScreenCenterResLabel);

    //是否佩戴红蓝眼镜 左红右蓝
    jobject wearingRBColorGlassesResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject wearingRBColorGlassesResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.wearing_color_glasses_red_blue_res.score));
    jboolean wearing_rb_color_glasses_res_label = monitorResult.wearing_color_glasses_red_blue_res.label;
    jobject wearingRBColorGlassesResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(wearing_rb_color_glasses_res_label));
    env->CallObjectMethod(wearingRBColorGlassesResHashMap, hashMapPut, env->NewStringUTF("score"), wearingRBColorGlassesResScore);
    env->CallObjectMethod(wearingRBColorGlassesResHashMap, hashMapPut, env->NewStringUTF("label"), wearingRBColorGlassesResLabel);

    //是否佩戴红蓝眼镜 左蓝右红
    jobject wearingBRColorGlassesResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject wearingBRColorGlassesResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.wearing_color_glasses_blue_red_res.score));
    jboolean wearing_br_color_glasses_res_label = monitorResult.wearing_color_glasses_blue_red_res.label;
    jobject wearingBRColorGlassesResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(wearing_br_color_glasses_res_label));
    env->CallObjectMethod(wearingBRColorGlassesResHashMap, hashMapPut, env->NewStringUTF("score"), wearingBRColorGlassesResScore);
    env->CallObjectMethod(wearingBRColorGlassesResHashMap, hashMapPut, env->NewStringUTF("label"), wearingBRColorGlassesResLabel);

    //是否专注
    jobject focusResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject focusResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(monitorResult.focus_res.score));
    jboolean focus_res_label = monitorResult.focus_res.label;
    jobject focusResLabel = env->NewObject(booleanClass, booleanConstructor, static_cast<jboolean>(focus_res_label));
    env->CallObjectMethod(focusResHashMap, hashMapPut, env->NewStringUTF("score"), focusResScore);
    env->CallObjectMethod(focusResHashMap, hashMapPut, env->NewStringUTF("label"), focusResLabel);

    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("body_res"), bodyResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("head_horizontal_res"), headHorizontalResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("head_vertical_res"), headVerticalResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("find_res"), findResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("long_distance_res"), longDistanceResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("short_distance_res"), shortDistanceResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("is_in_screen_center_res"), isInScreenCenterResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("wearing_color_glasses_red_blue_res"), wearingRBColorGlassesResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("wearing_color_glasses_blue_red_res"), wearingBRColorGlassesResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("focus_res"), focusResHashMap);

    env->ReleaseByteArrayElements(inputImage_, inputImage, 0);

    return hashMap;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_babyeye_pad6_FrameProcessAgent_nativeMonitorReport(JNIEnv *env, jobject clazz, jlong thiz) {

    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jmethodID hashMapPut = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");

    jobject hashMap = env->NewObject(hashMapClass, hashMapConstructor);

    if (thiz == 0) {
        return hashMap;
    }

    auto *simplePose = reinterpret_cast<SimplePose *>(thiz);

    TotalReport totalReport = *(simplePose->monitorReport->totalReport);

    // 将 jfloat 转换为 Float 对象
    jclass floatClass = env->FindClass("java/lang/Float");
    jmethodID floatConstructor = env->GetMethodID(floatClass, "<init>", "(F)V");

    // 将 jint 转换为 Integer 对象
    jclass intClass = env->FindClass("java/lang/Integer");
    jmethodID intConstructor = env->GetMethodID(intClass, "<init>", "(I)V");

    //是否高低肩
    jobject bodyResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject bodyResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.body_res.percent));
    jobject bodyResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.body_res.cnt));
    env->CallObjectMethod(bodyResHashMap, hashMapPut, env->NewStringUTF("percent"), bodyResScore);
    env->CallObjectMethod(bodyResHashMap, hashMapPut, env->NewStringUTF("cnt"), bodyResLabel);

    //是否歪头
    jobject headHorizontalResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject headHorizontalResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.head_horizontal_res.percent));
    jobject headHorizontalResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.head_horizontal_res.cnt));
    env->CallObjectMethod(headHorizontalResHashMap, hashMapPut, env->NewStringUTF("percent"), headHorizontalResScore);
    env->CallObjectMethod(headHorizontalResHashMap, hashMapPut, env->NewStringUTF("cnt"), headHorizontalResLabel);

    //是否低头
    jobject headVerticalResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject headVerticalResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.head_vertical_res.percent));
    jobject headVerticalResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.head_vertical_res.cnt));
    env->CallObjectMethod(headVerticalResHashMap, hashMapPut, env->NewStringUTF("percent"), headVerticalResScore);
    env->CallObjectMethod(headVerticalResHashMap, hashMapPut, env->NewStringUTF("cnt"), headVerticalResLabel);

    //是否监测到人脸
    jobject findResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject findResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.find_res.percent));
    jobject findResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.find_res.cnt));
    env->CallObjectMethod(findResHashMap, hashMapPut, env->NewStringUTF("percent"), findResScore);
    env->CallObjectMethod(findResHashMap, hashMapPut, env->NewStringUTF("cnt"), findResLabel);

    //是否距离过远
    jobject longDistanceResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject longDistanceResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.long_distance_res.percent));
    jobject longDistanceResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.long_distance_res.cnt));
    env->CallObjectMethod(longDistanceResHashMap, hashMapPut, env->NewStringUTF("percent"), longDistanceResScore);
    env->CallObjectMethod(longDistanceResHashMap, hashMapPut, env->NewStringUTF("cnt"), longDistanceResLabel);

    //是否距离过近
    jobject shortDistanceResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject shortDistanceResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.short_distance_res.percent));
    jobject shortDistanceResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.short_distance_res.cnt));
    env->CallObjectMethod(shortDistanceResHashMap, hashMapPut, env->NewStringUTF("percent"), shortDistanceResScore);
    env->CallObjectMethod(shortDistanceResHashMap, hashMapPut, env->NewStringUTF("cnt"), shortDistanceResLabel);

    //是否在屏幕中心
    jobject isInScreenCenterResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject isInScreenCenterResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.is_in_screen_center_res.percent));
    jobject isInScreenCenterResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.is_in_screen_center_res.cnt));
    env->CallObjectMethod(isInScreenCenterResHashMap, hashMapPut, env->NewStringUTF("percent"), isInScreenCenterResScore);
    env->CallObjectMethod(isInScreenCenterResHashMap, hashMapPut, env->NewStringUTF("cnt"), isInScreenCenterResLabel);

    //是否佩戴红蓝眼镜 左红右蓝
    jobject wearingRBColorGlassesResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject wearingRBColorGlassesResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.wearing_color_glasses_red_blue_res.percent));
    jobject wearingRBColorGlassesResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.wearing_color_glasses_blue_red_res.cnt));
    env->CallObjectMethod(wearingRBColorGlassesResHashMap, hashMapPut, env->NewStringUTF("percent"), wearingRBColorGlassesResScore);
    env->CallObjectMethod(wearingRBColorGlassesResHashMap, hashMapPut, env->NewStringUTF("cnt"), wearingRBColorGlassesResLabel);

    //是否佩戴红蓝眼镜 左蓝右红
    jobject wearingBRColorGlassesResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject wearingBRColorGlassesResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.wearing_color_glasses_blue_red_res.percent));
    jobject wearingBRColorGlassesResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.wearing_color_glasses_blue_red_res.cnt));
    env->CallObjectMethod(wearingBRColorGlassesResHashMap, hashMapPut, env->NewStringUTF("percent"), wearingBRColorGlassesResScore);
    env->CallObjectMethod(wearingBRColorGlassesResHashMap, hashMapPut, env->NewStringUTF("cnt"), wearingBRColorGlassesResLabel);

    //是否专注
    jobject focusResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject focusResScore = env->NewObject(floatClass, floatConstructor, static_cast<jfloat>(totalReport.focus_res.percent));
    jobject focusResLabel = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.focus_res.cnt));
    env->CallObjectMethod(focusResHashMap, hashMapPut, env->NewStringUTF("percent"), focusResScore);
    env->CallObjectMethod(focusResHashMap, hashMapPut, env->NewStringUTF("cnt"), focusResLabel);

    //overrall message
    jobject normalResHashMap = env->NewObject(hashMapClass, hashMapConstructor);
    jobject totalFramesCnt = env->NewObject(intClass, intConstructor, static_cast<jint>(simplePose->monitorReport->getTotalFrames()));
    jobject normalCnt = env->NewObject(intClass, intConstructor, static_cast<jint>(totalReport.normal_res.cnt));
    jobject abnormalCnt = env->NewObject(intClass, intConstructor, static_cast<jint>(simplePose->monitorReport->getTotalFrames() - totalReport.normal_res.cnt));
    env->CallObjectMethod(normalResHashMap, hashMapPut, env->NewStringUTF("total_cnt"), totalFramesCnt);
    env->CallObjectMethod(normalResHashMap, hashMapPut, env->NewStringUTF("normal_cnt"), normalCnt);
    env->CallObjectMethod(normalResHashMap, hashMapPut, env->NewStringUTF("abnormal_cnt"), abnormalCnt);

    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("body_res"), bodyResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("head_horizontal_res"), headHorizontalResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("head_vertical_res"), headVerticalResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("find_res"), findResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("long_distance_res"), longDistanceResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("short_distance_res"), shortDistanceResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("is_in_screen_center_res"), isInScreenCenterResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("wearing_color_glass_red_blue_res"), wearingRBColorGlassesResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("wearing_color_glass_blue_red_res"), wearingBRColorGlassesResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("focus_res"), focusResHashMap);
    env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("total"), normalResHashMap);

    // reset report when this method called
    simplePose->monitorReport->resetRes();

    return hashMap;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_mitdd_behaviorguidance_BehaviorGuidance_nativeDestroyObject(JNIEnv *env, jobject clazz, jlong thiz) {
    if (thiz != 0) {
        SimplePose *simplePose = reinterpret_cast<SimplePose *>(thiz);
        delete simplePose;
    }
}
