// Copyright (c) 2014-2020 The Khronos Group Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and/or associated documentation files (the "Materials"),
// to deal in the Materials without restriction, including without limitation
// the rights to use, copy, modify, merge, publish, distribute, sublicense,
// and/or sell copies of the Materials, and to permit persons to whom the
// Materials are furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Materials.
//
// MODIFICATIONS TO THIS FILE MAY MEAN IT NO LONGER ACCURATELY REFLECTS KHRONOS
// STANDARDS. THE UNMODIFIED, NORMATIVE VERSIONS OF KHRONOS SPECIFICATIONS AND
// HEADER INFORMATION ARE LOCATED AT https://www.khronos.org/registry/
//
// THE MATERIALS ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM,OUT OF OR IN CONNECTION WITH THE MATERIALS OR THE USE OR OTHER DEALINGS
// IN THE MATERIALS.

// This header is automatically generated by the same tool that creates
// the Binary Section of the SPIR-V specification.

// Enumeration tokens for SPIR-V, in various styles:
//   C, C++, C++11, JSON, Lua, Python, C#, D, Beef
//
// - C will have tokens with a "Spv" prefix, e.g.: SpvSourceLanguageGLSL
// - C++ will have tokens in the "spv" name space, e.g.: spv::SourceLanguageGLSL
// - C++11 will use enum classes in the spv namespace, e.g.: spv::SourceLanguage::GLSL
// - Lua will use tables, e.g.: spv.SourceLanguage.GLSL
// - Python will use dictionaries, e.g.: spv['SourceLanguage']['GLSL']
// - C# will use enum classes in the Specification class located in the "Spv" namespace,
//     e.g.: Spv.Specification.SourceLanguage.GLSL
// - D will have tokens under the "spv" module, e.g: spv.SourceLanguage.GLSL
// - Beef will use enum classes in the Specification class located in the "Spv" namespace,
//     e.g.: Spv.Specification.SourceLanguage.GLSL
//
// Some tokens act like mask values, which can be OR'd together,
// while others are mutually exclusive.  The mask-like ones have
// "Mask" in their name, and a parallel enum that has the shift
// amount (1 << x) for each corresponding enumerant.

#ifndef spirv_HPP
#define spirv_HPP

namespace spv {

typedef unsigned int Id;

#define SPV_VERSION 0x10600
#define SPV_REVISION 1

static const unsigned int MagicNumber = 0x07230203;
static const unsigned int Version = 0x00010600;
static const unsigned int Revision = 1;
static const unsigned int OpCodeMask = 0xffff;
static const unsigned int WordCountShift = 16;

enum SourceLanguage {
    SourceLanguageUnknown = 0,
    SourceLanguageESSL = 1,
    SourceLanguageGLSL = 2,
    SourceLanguageOpenCL_C = 3,
    SourceLanguageOpenCL_CPP = 4,
    SourceLanguageHLSL = 5,
    SourceLanguageCPP_for_OpenCL = 6,
    SourceLanguageSYCL = 7,
    SourceLanguageMax = 0x7fffffff,
};

enum ExecutionModel {
    ExecutionModelVertex = 0,
    ExecutionModelTessellationControl = 1,
    ExecutionModelTessellationEvaluation = 2,
    ExecutionModelGeometry = 3,
    ExecutionModelFragment = 4,
    ExecutionModelGLCompute = 5,
    ExecutionModelKernel = 6,
    ExecutionModelTaskNV = 5267,
    ExecutionModelMeshNV = 5268,
    ExecutionModelRayGenerationKHR = 5313,
    ExecutionModelRayGenerationNV = 5313,
    ExecutionModelIntersectionKHR = 5314,
    ExecutionModelIntersectionNV = 5314,
    ExecutionModelAnyHitKHR = 5315,
    ExecutionModelAnyHitNV = 5315,
    ExecutionModelClosestHitKHR = 5316,
    ExecutionModelClosestHitNV = 5316,
    ExecutionModelMissKHR = 5317,
    ExecutionModelMissNV = 5317,
    ExecutionModelCallableKHR = 5318,
    ExecutionModelCallableNV = 5318,
    ExecutionModelTaskEXT = 5364,
    ExecutionModelMeshEXT = 5365,
    ExecutionModelMax = 0x7fffffff,
};

enum AddressingModel {
    AddressingModelLogical = 0,
    AddressingModelPhysical32 = 1,
    AddressingModelPhysical64 = 2,
    AddressingModelPhysicalStorageBuffer64 = 5348,
    AddressingModelPhysicalStorageBuffer64EXT = 5348,
    AddressingModelMax = 0x7fffffff,
};

enum MemoryModel {
    MemoryModelSimple = 0,
    MemoryModelGLSL450 = 1,
    MemoryModelOpenCL = 2,
    MemoryModelVulkan = 3,
    MemoryModelVulkanKHR = 3,
    MemoryModelMax = 0x7fffffff,
};

enum ExecutionMode {
    ExecutionModeInvocations = 0,
    ExecutionModeSpacingEqual = 1,
    ExecutionModeSpacingFractionalEven = 2,
    ExecutionModeSpacingFractionalOdd = 3,
    ExecutionModeVertexOrderCw = 4,
    ExecutionModeVertexOrderCcw = 5,
    ExecutionModePixelCenterInteger = 6,
    ExecutionModeOriginUpperLeft = 7,
    ExecutionModeOriginLowerLeft = 8,
    ExecutionModeEarlyFragmentTests = 9,
    ExecutionModePointMode = 10,
    ExecutionModeXfb = 11,
    ExecutionModeDepthReplacing = 12,
    ExecutionModeDepthGreater = 14,
    ExecutionModeDepthLess = 15,
    ExecutionModeDepthUnchanged = 16,
    ExecutionModeLocalSize = 17,
    ExecutionModeLocalSizeHint = 18,
    ExecutionModeInputPoints = 19,
    ExecutionModeInputLines = 20,
    ExecutionModeInputLinesAdjacency = 21,
    ExecutionModeTriangles = 22,
    ExecutionModeInputTrianglesAdjacency = 23,
    ExecutionModeQuads = 24,
    ExecutionModeIsolines = 25,
    ExecutionModeOutputVertices = 26,
    ExecutionModeOutputPoints = 27,
    ExecutionModeOutputLineStrip = 28,
    ExecutionModeOutputTriangleStrip = 29,
    ExecutionModeVecTypeHint = 30,
    ExecutionModeContractionOff = 31,
    ExecutionModeInitializer = 33,
    ExecutionModeFinalizer = 34,
    ExecutionModeSubgroupSize = 35,
    ExecutionModeSubgroupsPerWorkgroup = 36,
    ExecutionModeSubgroupsPerWorkgroupId = 37,
    ExecutionModeLocalSizeId = 38,
    ExecutionModeLocalSizeHintId = 39,
    ExecutionModeNonCoherentColorAttachmentReadEXT = 4169,
    ExecutionModeNonCoherentDepthAttachmentReadEXT = 4170,
    ExecutionModeNonCoherentStencilAttachmentReadEXT = 4171,
    ExecutionModeSubgroupUniformControlFlowKHR = 4421,
    ExecutionModePostDepthCoverage = 4446,
    ExecutionModeDenormPreserve = 4459,
    ExecutionModeDenormFlushToZero = 4460,
    ExecutionModeSignedZeroInfNanPreserve = 4461,
    ExecutionModeRoundingModeRTE = 4462,
    ExecutionModeRoundingModeRTZ = 4463,
    ExecutionModeEarlyAndLateFragmentTestsAMD = 5017,
    ExecutionModeStencilRefReplacingEXT = 5027,
    ExecutionModeStencilRefUnchangedFrontAMD = 5079,
    ExecutionModeStencilRefGreaterFrontAMD = 5080,
    ExecutionModeStencilRefLessFrontAMD = 5081,
    ExecutionModeStencilRefUnchangedBackAMD = 5082,
    ExecutionModeStencilRefGreaterBackAMD = 5083,
    ExecutionModeStencilRefLessBackAMD = 5084,
    ExecutionModeOutputLinesEXT = 5269,
    ExecutionModeOutputLinesNV = 5269,
    ExecutionModeOutputPrimitivesEXT = 5270,
    ExecutionModeOutputPrimitivesNV = 5270,
    ExecutionModeDerivativeGroupQuadsNV = 5289,
    ExecutionModeDerivativeGroupLinearNV = 5290,
    ExecutionModeOutputTrianglesEXT = 5298,
    ExecutionModeOutputTrianglesNV = 5298,
    ExecutionModePixelInterlockOrderedEXT = 5366,
    ExecutionModePixelInterlockUnorderedEXT = 5367,
    ExecutionModeSampleInterlockOrderedEXT = 5368,
    ExecutionModeSampleInterlockUnorderedEXT = 5369,
    ExecutionModeShadingRateInterlockOrderedEXT = 5370,
    ExecutionModeShadingRateInterlockUnorderedEXT = 5371,
    ExecutionModeSharedLocalMemorySizeINTEL = 5618,
    ExecutionModeRoundingModeRTPINTEL = 5620,
    ExecutionModeRoundingModeRTNINTEL = 5621,
    ExecutionModeFloatingPointModeALTINTEL = 5622,
    ExecutionModeFloatingPointModeIEEEINTEL = 5623,
    ExecutionModeMaxWorkgroupSizeINTEL = 5893,
    ExecutionModeMaxWorkDimINTEL = 5894,
    ExecutionModeNoGlobalOffsetINTEL = 5895,
    ExecutionModeNumSIMDWorkitemsINTEL = 5896,
    ExecutionModeSchedulerTargetFmaxMhzINTEL = 5903,
    ExecutionModeStreamingInterfaceINTEL = 6154,
    ExecutionModeNamedBarrierCountINTEL = 6417,
    ExecutionModeMax = 0x7fffffff,
};

enum StorageClass {
    StorageClassUniformConstant = 0,
    StorageClassInput = 1,
    StorageClassUniform = 2,
    StorageClassOutput = 3,
    StorageClassWorkgroup = 4,
    StorageClassCrossWorkgroup = 5,
    StorageClassPrivate = 6,
    StorageClassFunction = 7,
    StorageClassGeneric = 8,
    StorageClassPushConstant = 9,
    StorageClassAtomicCounter = 10,
    StorageClassImage = 11,
    StorageClassStorageBuffer = 12,
    StorageClassTileImageEXT = 4172,
    StorageClassCallableDataKHR = 5328,
    StorageClassCallableDataNV = 5328,
    StorageClassIncomingCallableDataKHR = 5329,
    StorageClassIncomingCallableDataNV = 5329,
    StorageClassRayPayloadKHR = 5338,
    StorageClassRayPayloadNV = 5338,
    StorageClassHitAttributeKHR = 5339,
    StorageClassHitAttributeNV = 5339,
    StorageClassIncomingRayPayloadKHR = 5342,
    StorageClassIncomingRayPayloadNV = 5342,
    StorageClassShaderRecordBufferKHR = 5343,
    StorageClassShaderRecordBufferNV = 5343,
    StorageClassPhysicalStorageBuffer = 5349,
    StorageClassPhysicalStorageBufferEXT = 5349,
    StorageClassHitObjectAttributeNV = 5385,
    StorageClassTaskPayloadWorkgroupEXT = 5402,
    StorageClassCodeSectionINTEL = 5605,
    StorageClassDeviceOnlyINTEL = 5936,
    StorageClassHostOnlyINTEL = 5937,
    StorageClassMax = 0x7fffffff,
};

enum Dim {
    Dim1D = 0,
    Dim2D = 1,
    Dim3D = 2,
    DimCube = 3,
    DimRect = 4,
    DimBuffer = 5,
    DimSubpassData = 6,
    DimTileImageDataEXT = 4173,
    DimMax = 0x7fffffff,
};

enum SamplerAddressingMode {
    SamplerAddressingModeNone = 0,
    SamplerAddressingModeClampToEdge = 1,
    SamplerAddressingModeClamp = 2,
    SamplerAddressingModeRepeat = 3,
    SamplerAddressingModeRepeatMirrored = 4,
    SamplerAddressingModeMax = 0x7fffffff,
};

enum SamplerFilterMode {
    SamplerFilterModeNearest = 0,
    SamplerFilterModeLinear = 1,
    SamplerFilterModeMax = 0x7fffffff,
};

enum ImageFormat {
    ImageFormatUnknown = 0,
    ImageFormatRgba32f = 1,
    ImageFormatRgba16f = 2,
    ImageFormatR32f = 3,
    ImageFormatRgba8 = 4,
    ImageFormatRgba8Snorm = 5,
    ImageFormatRg32f = 6,
    ImageFormatRg16f = 7,
    ImageFormatR11fG11fB10f = 8,
    ImageFormatR16f = 9,
    ImageFormatRgba16 = 10,
    ImageFormatRgb10A2 = 11,
    ImageFormatRg16 = 12,
    ImageFormatRg8 = 13,
    ImageFormatR16 = 14,
    ImageFormatR8 = 15,
    ImageFormatRgba16Snorm = 16,
    ImageFormatRg16Snorm = 17,
    ImageFormatRg8Snorm = 18,
    ImageFormatR16Snorm = 19,
    ImageFormatR8Snorm = 20,
    ImageFormatRgba32i = 21,
    ImageFormatRgba16i = 22,
    ImageFormatRgba8i = 23,
    ImageFormatR32i = 24,
    ImageFormatRg32i = 25,
    ImageFormatRg16i = 26,
    ImageFormatRg8i = 27,
    ImageFormatR16i = 28,
    ImageFormatR8i = 29,
    ImageFormatRgba32ui = 30,
    ImageFormatRgba16ui = 31,
    ImageFormatRgba8ui = 32,
    ImageFormatR32ui = 33,
    ImageFormatRgb10a2ui = 34,
    ImageFormatRg32ui = 35,
    ImageFormatRg16ui = 36,
    ImageFormatRg8ui = 37,
    ImageFormatR16ui = 38,
    ImageFormatR8ui = 39,
    ImageFormatR64ui = 40,
    ImageFormatR64i = 41,
    ImageFormatMax = 0x7fffffff,
};

enum ImageChannelOrder {
    ImageChannelOrderR = 0,
    ImageChannelOrderA = 1,
    ImageChannelOrderRG = 2,
    ImageChannelOrderRA = 3,
    ImageChannelOrderRGB = 4,
    ImageChannelOrderRGBA = 5,
    ImageChannelOrderBGRA = 6,
    ImageChannelOrderARGB = 7,
    ImageChannelOrderIntensity = 8,
    ImageChannelOrderLuminance = 9,
    ImageChannelOrderRx = 10,
    ImageChannelOrderRGx = 11,
    ImageChannelOrderRGBx = 12,
    ImageChannelOrderDepth = 13,
    ImageChannelOrderDepthStencil = 14,
    ImageChannelOrdersRGB = 15,
    ImageChannelOrdersRGBx = 16,
    ImageChannelOrdersRGBA = 17,
    ImageChannelOrdersBGRA = 18,
    ImageChannelOrderABGR = 19,
    ImageChannelOrderMax = 0x7fffffff,
};

enum ImageChannelDataType {
    ImageChannelDataTypeSnormInt8 = 0,
    ImageChannelDataTypeSnormInt16 = 1,
    ImageChannelDataTypeUnormInt8 = 2,
    ImageChannelDataTypeUnormInt16 = 3,
    ImageChannelDataTypeUnormShort565 = 4,
    ImageChannelDataTypeUnormShort555 = 5,
    ImageChannelDataTypeUnormInt101010 = 6,
    ImageChannelDataTypeSignedInt8 = 7,
    ImageChannelDataTypeSignedInt16 = 8,
    ImageChannelDataTypeSignedInt32 = 9,
    ImageChannelDataTypeUnsignedInt8 = 10,
    ImageChannelDataTypeUnsignedInt16 = 11,
    ImageChannelDataTypeUnsignedInt32 = 12,
    ImageChannelDataTypeHalfFloat = 13,
    ImageChannelDataTypeFloat = 14,
    ImageChannelDataTypeUnormInt24 = 15,
    ImageChannelDataTypeUnormInt101010_2 = 16,
    ImageChannelDataTypeMax = 0x7fffffff,
};

enum ImageOperandsShift {
    ImageOperandsBiasShift = 0,
    ImageOperandsLodShift = 1,
    ImageOperandsGradShift = 2,
    ImageOperandsConstOffsetShift = 3,
    ImageOperandsOffsetShift = 4,
    ImageOperandsConstOffsetsShift = 5,
    ImageOperandsSampleShift = 6,
    ImageOperandsMinLodShift = 7,
    ImageOperandsMakeTexelAvailableShift = 8,
    ImageOperandsMakeTexelAvailableKHRShift = 8,
    ImageOperandsMakeTexelVisibleShift = 9,
    ImageOperandsMakeTexelVisibleKHRShift = 9,
    ImageOperandsNonPrivateTexelShift = 10,
    ImageOperandsNonPrivateTexelKHRShift = 10,
    ImageOperandsVolatileTexelShift = 11,
    ImageOperandsVolatileTexelKHRShift = 11,
    ImageOperandsSignExtendShift = 12,
    ImageOperandsZeroExtendShift = 13,
    ImageOperandsNontemporalShift = 14,
    ImageOperandsOffsetsShift = 16,
    ImageOperandsMax = 0x7fffffff,
};

enum ImageOperandsMask {
    ImageOperandsMaskNone = 0,
    ImageOperandsBiasMask = 0x00000001,
    ImageOperandsLodMask = 0x00000002,
    ImageOperandsGradMask = 0x00000004,
    ImageOperandsConstOffsetMask = 0x00000008,
    ImageOperandsOffsetMask = 0x00000010,
    ImageOperandsConstOffsetsMask = 0x00000020,
    ImageOperandsSampleMask = 0x00000040,
    ImageOperandsMinLodMask = 0x00000080,
    ImageOperandsMakeTexelAvailableMask = 0x00000100,
    ImageOperandsMakeTexelAvailableKHRMask = 0x00000100,
    ImageOperandsMakeTexelVisibleMask = 0x00000200,
    ImageOperandsMakeTexelVisibleKHRMask = 0x00000200,
    ImageOperandsNonPrivateTexelMask = 0x00000400,
    ImageOperandsNonPrivateTexelKHRMask = 0x00000400,
    ImageOperandsVolatileTexelMask = 0x00000800,
    ImageOperandsVolatileTexelKHRMask = 0x00000800,
    ImageOperandsSignExtendMask = 0x00001000,
    ImageOperandsZeroExtendMask = 0x00002000,
    ImageOperandsNontemporalMask = 0x00004000,
    ImageOperandsOffsetsMask = 0x00010000,
};

enum FPFastMathModeShift {
    FPFastMathModeNotNaNShift = 0,
    FPFastMathModeNotInfShift = 1,
    FPFastMathModeNSZShift = 2,
    FPFastMathModeAllowRecipShift = 3,
    FPFastMathModeFastShift = 4,
    FPFastMathModeAllowContractFastINTELShift = 16,
    FPFastMathModeAllowReassocINTELShift = 17,
    FPFastMathModeMax = 0x7fffffff,
};

enum FPFastMathModeMask {
    FPFastMathModeMaskNone = 0,
    FPFastMathModeNotNaNMask = 0x00000001,
    FPFastMathModeNotInfMask = 0x00000002,
    FPFastMathModeNSZMask = 0x00000004,
    FPFastMathModeAllowRecipMask = 0x00000008,
    FPFastMathModeFastMask = 0x00000010,
    FPFastMathModeAllowContractFastINTELMask = 0x00010000,
    FPFastMathModeAllowReassocINTELMask = 0x00020000,
};

enum FPRoundingMode {
    FPRoundingModeRTE = 0,
    FPRoundingModeRTZ = 1,
    FPRoundingModeRTP = 2,
    FPRoundingModeRTN = 3,
    FPRoundingModeMax = 0x7fffffff,
};

enum LinkageType {
    LinkageTypeExport = 0,
    LinkageTypeImport = 1,
    LinkageTypeLinkOnceODR = 2,
    LinkageTypeMax = 0x7fffffff,
};

enum AccessQualifier {
    AccessQualifierReadOnly = 0,
    AccessQualifierWriteOnly = 1,
    AccessQualifierReadWrite = 2,
    AccessQualifierMax = 0x7fffffff,
};

enum FunctionParameterAttribute {
    FunctionParameterAttributeZext = 0,
    FunctionParameterAttributeSext = 1,
    FunctionParameterAttributeByVal = 2,
    FunctionParameterAttributeSret = 3,
    FunctionParameterAttributeNoAlias = 4,
    FunctionParameterAttributeNoCapture = 5,
    FunctionParameterAttributeNoWrite = 6,
    FunctionParameterAttributeNoReadWrite = 7,
    FunctionParameterAttributeRuntimeAlignedINTEL = 5940,
    FunctionParameterAttributeMax = 0x7fffffff,
};

enum Decoration {
    DecorationRelaxedPrecision = 0,
    DecorationSpecId = 1,
    DecorationBlock = 2,
    DecorationBufferBlock = 3,
    DecorationRowMajor = 4,
    DecorationColMajor = 5,
    DecorationArrayStride = 6,
    DecorationMatrixStride = 7,
    DecorationGLSLShared = 8,
    DecorationGLSLPacked = 9,
    DecorationCPacked = 10,
    DecorationBuiltIn = 11,
    DecorationNoPerspective = 13,
    DecorationFlat = 14,
    DecorationPatch = 15,
    DecorationCentroid = 16,
    DecorationSample = 17,
    DecorationInvariant = 18,
    DecorationRestrict = 19,
    DecorationAliased = 20,
    DecorationVolatile = 21,
    DecorationConstant = 22,
    DecorationCoherent = 23,
    DecorationNonWritable = 24,
    DecorationNonReadable = 25,
    DecorationUniform = 26,
    DecorationUniformId = 27,
    DecorationSaturatedConversion = 28,
    DecorationStream = 29,
    DecorationLocation = 30,
    DecorationComponent = 31,
    DecorationIndex = 32,
    DecorationBinding = 33,
    DecorationDescriptorSet = 34,
    DecorationOffset = 35,
    DecorationXfbBuffer = 36,
    DecorationXfbStride = 37,
    DecorationFuncParamAttr = 38,
    DecorationFPRoundingMode = 39,
    DecorationFPFastMathMode = 40,
    DecorationLinkageAttributes = 41,
    DecorationNoContraction = 42,
    DecorationInputAttachmentIndex = 43,
    DecorationAlignment = 44,
    DecorationMaxByteOffset = 45,
    DecorationAlignmentId = 46,
    DecorationMaxByteOffsetId = 47,
    DecorationNoSignedWrap = 4469,
    DecorationNoUnsignedWrap = 4470,
    DecorationExplicitInterpAMD = 4999,
    DecorationOverrideCoverageNV = 5248,
    DecorationPassthroughNV = 5250,
    DecorationViewportRelativeNV = 5252,
    DecorationSecondaryViewportRelativeNV = 5256,
    DecorationPerPrimitiveEXT = 5271,
    DecorationPerPrimitiveNV = 5271,
    DecorationPerViewNV = 5272,
    DecorationPerTaskNV = 5273,
    DecorationPerVertexKHR = 5285,
    DecorationPerVertexNV = 5285,
    DecorationNonUniform = 5300,
    DecorationNonUniformEXT = 5300,
    DecorationRestrictPointer = 5355,
    DecorationRestrictPointerEXT = 5355,
    DecorationAliasedPointer = 5356,
    DecorationAliasedPointerEXT = 5356,
    DecorationHitObjectShaderRecordBufferNV = 5386,
    DecorationBindlessSamplerNV = 5398,
    DecorationBindlessImageNV = 5399,
    DecorationBoundSamplerNV = 5400,
    DecorationBoundImageNV = 5401,
    DecorationSIMTCallINTEL = 5599,
    DecorationReferencedIndirectlyINTEL = 5602,
    DecorationClobberINTEL = 5607,
    DecorationSideEffectsINTEL = 5608,
    DecorationVectorComputeVariableINTEL = 5624,
    DecorationFuncParamIOKindINTEL = 5625,
    DecorationVectorComputeFunctionINTEL = 5626,
    DecorationStackCallINTEL = 5627,
    DecorationGlobalVariableOffsetINTEL = 5628,
    DecorationCounterBuffer = 5634,
    DecorationHlslCounterBufferGOOGLE = 5634,
    DecorationHlslSemanticGOOGLE = 5635,
    DecorationUserSemantic = 5635,
    DecorationUserTypeGOOGLE = 5636,
    DecorationFunctionRoundingModeINTEL = 5822,
    DecorationFunctionDenormModeINTEL = 5823,
    DecorationRegisterINTEL = 5825,
    DecorationMemoryINTEL = 5826,
    DecorationNumbanksINTEL = 5827,
    DecorationBankwidthINTEL = 5828,
    DecorationMaxPrivateCopiesINTEL = 5829,
    DecorationSinglepumpINTEL = 5830,
    DecorationDoublepumpINTEL = 5831,
    DecorationMaxReplicatesINTEL = 5832,
    DecorationSimpleDualPortINTEL = 5833,
    DecorationMergeINTEL = 5834,
    DecorationBankBitsINTEL = 5835,
    DecorationForcePow2DepthINTEL = 5836,
    DecorationBurstCoalesceINTEL = 5899,
    DecorationCacheSizeINTEL = 5900,
    DecorationDontStaticallyCoalesceINTEL = 5901,
    DecorationPrefetchINTEL = 5902,
    DecorationStallEnableINTEL = 5905,
    DecorationFuseLoopsInFunctionINTEL = 5907,
    DecorationMathOpDSPModeINTEL = 5909,
    DecorationAliasScopeINTEL = 5914,
    DecorationNoAliasINTEL = 5915,
    DecorationInitiationIntervalINTEL = 5917,
    DecorationMaxConcurrencyINTEL = 5918,
    DecorationPipelineEnableINTEL = 5919,
    DecorationBufferLocationINTEL = 5921,
    DecorationIOPipeStorageINTEL = 5944,
    DecorationFunctionFloatingPointModeINTEL = 6080,
    DecorationSingleElementVectorINTEL = 6085,
    DecorationVectorComputeCallableFunctionINTEL = 6087,
    DecorationMediaBlockIOINTEL = 6140,
    DecorationConduitKernelArgumentINTEL = 6175,
    DecorationRegisterMapKernelArgumentINTEL = 6176,
    DecorationMMHostInterfaceAddressWidthINTEL = 6177,
    DecorationMMHostInterfaceDataWidthINTEL = 6178,
    DecorationMMHostInterfaceLatencyINTEL = 6179,
    DecorationMMHostInterfaceReadWriteModeINTEL = 6180,
    DecorationMMHostInterfaceMaxBurstINTEL = 6181,
    DecorationMMHostInterfaceWaitRequestINTEL = 6182,
    DecorationStableKernelArgumentINTEL = 6183,
    DecorationMax = 0x7fffffff,
};

enum BuiltIn {
    BuiltInPosition = 0,
    BuiltInPointSize = 1,
    BuiltInClipDistance = 3,
    BuiltInCullDistance = 4,
    BuiltInVertexId = 5,
    BuiltInInstanceId = 6,
    BuiltInPrimitiveId = 7,
    BuiltInInvocationId = 8,
    BuiltInLayer = 9,
    BuiltInViewportIndex = 10,
    BuiltInTessLevelOuter = 11,
    BuiltInTessLevelInner = 12,
    BuiltInTessCoord = 13,
    BuiltInPatchVertices = 14,
    BuiltInFragCoord = 15,
    BuiltInPointCoord = 16,
    BuiltInFrontFacing = 17,
    BuiltInSampleId = 18,
    BuiltInSamplePosition = 19,
    BuiltInSampleMask = 20,
    BuiltInFragDepth = 22,
    BuiltInHelperInvocation = 23,
    BuiltInNumWorkgroups = 24,
    BuiltInWorkgroupSize = 25,
    BuiltInWorkgroupId = 26,
    BuiltInLocalInvocationId = 27,
    BuiltInGlobalInvocationId = 28,
    BuiltInLocalInvocationIndex = 29,
    BuiltInWorkDim = 30,
    BuiltInGlobalSize = 31,
    BuiltInEnqueuedWorkgroupSize = 32,
    BuiltInGlobalOffset = 33,
    BuiltInGlobalLinearId = 34,
    BuiltInSubgroupSize = 36,
    BuiltInSubgroupMaxSize = 37,
    BuiltInNumSubgroups = 38,
    BuiltInNumEnqueuedSubgroups = 39,
    BuiltInSubgroupId = 40,
    BuiltInSubgroupLocalInvocationId = 41,
    BuiltInVertexIndex = 42,
    BuiltInInstanceIndex = 43,
    BuiltInCoreIDARM = 4160,
    BuiltInCoreCountARM = 4161,
    BuiltInCoreMaxIDARM = 4162,
    BuiltInWarpIDARM = 4163,
    BuiltInWarpMaxIDARM = 4164,
    BuiltInSubgroupEqMask = 4416,
    BuiltInSubgroupEqMaskKHR = 4416,
    BuiltInSubgroupGeMask = 4417,
    BuiltInSubgroupGeMaskKHR = 4417,
    BuiltInSubgroupGtMask = 4418,
    BuiltInSubgroupGtMaskKHR = 4418,
    BuiltInSubgroupLeMask = 4419,
    BuiltInSubgroupLeMaskKHR = 4419,
    BuiltInSubgroupLtMask = 4420,
    BuiltInSubgroupLtMaskKHR = 4420,
    BuiltInBaseVertex = 4424,
    BuiltInBaseInstance = 4425,
    BuiltInDrawIndex = 4426,
    BuiltInPrimitiveShadingRateKHR = 4432,
    BuiltInDeviceIndex = 4438,
    BuiltInViewIndex = 4440,
    BuiltInShadingRateKHR = 4444,
    BuiltInBaryCoordNoPerspAMD = 4992,
    BuiltInBaryCoordNoPerspCentroidAMD = 4993,
    BuiltInBaryCoordNoPerspSampleAMD = 4994,
    BuiltInBaryCoordSmoothAMD = 4995,
    BuiltInBaryCoordSmoothCentroidAMD = 4996,
    BuiltInBaryCoordSmoothSampleAMD = 4997,
    BuiltInBaryCoordPullModelAMD = 4998,
    BuiltInFragStencilRefEXT = 5014,
    BuiltInViewportMaskNV = 5253,
    BuiltInSecondaryPositionNV = 5257,
    BuiltInSecondaryViewportMaskNV = 5258,
    BuiltInPositionPerViewNV = 5261,
    BuiltInViewportMaskPerViewNV = 5262,
    BuiltInFullyCoveredEXT = 5264,
    BuiltInTaskCountNV = 5274,
    BuiltInPrimitiveCountNV = 5275,
    BuiltInPrimitiveIndicesNV = 5276,
    BuiltInClipDistancePerViewNV = 5277,
    BuiltInCullDistancePerViewNV = 5278,
    BuiltInLayerPerViewNV = 5279,
    BuiltInMeshViewCountNV = 5280,
    BuiltInMeshViewIndicesNV = 5281,
    BuiltInBaryCoordKHR = 5286,
    BuiltInBaryCoordNV = 5286,
    BuiltInBaryCoordNoPerspKHR = 5287,
    BuiltInBaryCoordNoPerspNV = 5287,
    BuiltInFragSizeEXT = 5292,
    BuiltInFragmentSizeNV = 5292,
    BuiltInFragInvocationCountEXT = 5293,
    BuiltInInvocationsPerPixelNV = 5293,
    BuiltInPrimitivePointIndicesEXT = 5294,
    BuiltInPrimitiveLineIndicesEXT = 5295,
    BuiltInPrimitiveTriangleIndicesEXT = 5296,
    BuiltInCullPrimitiveEXT = 5299,
    BuiltInLaunchIdKHR = 5319,
    BuiltInLaunchIdNV = 5319,
    BuiltInLaunchSizeKHR = 5320,
    BuiltInLaunchSizeNV = 5320,
    BuiltInWorldRayOriginKHR = 5321,
    BuiltInWorldRayOriginNV = 5321,
    BuiltInWorldRayDirectionKHR = 5322,
    BuiltInWorldRayDirectionNV = 5322,
    BuiltInObjectRayOriginKHR = 5323,
    BuiltInObjectRayOriginNV = 5323,
    BuiltInObjectRayDirectionKHR = 5324,
    BuiltInObjectRayDirectionNV = 5324,
    BuiltInRayTminKHR = 5325,
    BuiltInRayTminNV = 5325,
    BuiltInRayTmaxKHR = 5326,
    BuiltInRayTmaxNV = 5326,
    BuiltInInstanceCustomIndexKHR = 5327,
    BuiltInInstanceCustomIndexNV = 5327,
    BuiltInObjectToWorldKHR = 5330,
    BuiltInObjectToWorldNV = 5330,
    BuiltInWorldToObjectKHR = 5331,
    BuiltInWorldToObjectNV = 5331,
    BuiltInHitTNV = 5332,
    BuiltInHitKindKHR = 5333,
    BuiltInHitKindNV = 5333,
    BuiltInCurrentRayTimeNV = 5334,
    BuiltInHitTriangleVertexPositionsKHR = 5335,
    BuiltInIncomingRayFlagsKHR = 5351,
    BuiltInIncomingRayFlagsNV = 5351,
    BuiltInRayGeometryIndexKHR = 5352,
    BuiltInWarpsPerSMNV = 5374,
    BuiltInSMCountNV = 5375,
    BuiltInWarpIDNV = 5376,
    BuiltInSMIDNV = 5377,
    BuiltInCullMaskKHR = 6021,
    BuiltInMax = 0x7fffffff,
};

enum SelectionControlShift {
    SelectionControlFlattenShift = 0,
    SelectionControlDontFlattenShift = 1,
    SelectionControlMax = 0x7fffffff,
};

enum SelectionControlMask {
    SelectionControlMaskNone = 0,
    SelectionControlFlattenMask = 0x00000001,
    SelectionControlDontFlattenMask = 0x00000002,
};

enum LoopControlShift {
    LoopControlUnrollShift = 0,
    LoopControlDontUnrollShift = 1,
    LoopControlDependencyInfiniteShift = 2,
    LoopControlDependencyLengthShift = 3,
    LoopControlMinIterationsShift = 4,
    LoopControlMaxIterationsShift = 5,
    LoopControlIterationMultipleShift = 6,
    LoopControlPeelCountShift = 7,
    LoopControlPartialCountShift = 8,
    LoopControlInitiationIntervalINTELShift = 16,
    LoopControlMaxConcurrencyINTELShift = 17,
    LoopControlDependencyArrayINTELShift = 18,
    LoopControlPipelineEnableINTELShift = 19,
    LoopControlLoopCoalesceINTELShift = 20,
    LoopControlMaxInterleavingINTELShift = 21,
    LoopControlSpeculatedIterationsINTELShift = 22,
    LoopControlNoFusionINTELShift = 23,
    LoopControlLoopCountINTELShift = 24,
    LoopControlMaxReinvocationDelayINTELShift = 25,
    LoopControlMax = 0x7fffffff,
};

enum LoopControlMask {
    LoopControlMaskNone = 0,
    LoopControlUnrollMask = 0x00000001,
    LoopControlDontUnrollMask = 0x00000002,
    LoopControlDependencyInfiniteMask = 0x00000004,
    LoopControlDependencyLengthMask = 0x00000008,
    LoopControlMinIterationsMask = 0x00000010,
    LoopControlMaxIterationsMask = 0x00000020,
    LoopControlIterationMultipleMask = 0x00000040,
    LoopControlPeelCountMask = 0x00000080,
    LoopControlPartialCountMask = 0x00000100,
    LoopControlInitiationIntervalINTELMask = 0x00010000,
    LoopControlMaxConcurrencyINTELMask = 0x00020000,
    LoopControlDependencyArrayINTELMask = 0x00040000,
    LoopControlPipelineEnableINTELMask = 0x00080000,
    LoopControlLoopCoalesceINTELMask = 0x00100000,
    LoopControlMaxInterleavingINTELMask = 0x00200000,
    LoopControlSpeculatedIterationsINTELMask = 0x00400000,
    LoopControlNoFusionINTELMask = 0x00800000,
    LoopControlLoopCountINTELMask = 0x01000000,
    LoopControlMaxReinvocationDelayINTELMask = 0x02000000,
};

enum FunctionControlShift {
    FunctionControlInlineShift = 0,
    FunctionControlDontInlineShift = 1,
    FunctionControlPureShift = 2,
    FunctionControlConstShift = 3,
    FunctionControlOptNoneINTELShift = 16,
    FunctionControlMax = 0x7fffffff,
};

enum FunctionControlMask {
    FunctionControlMaskNone = 0,
    FunctionControlInlineMask = 0x00000001,
    FunctionControlDontInlineMask = 0x00000002,
    FunctionControlPureMask = 0x00000004,
    FunctionControlConstMask = 0x00000008,
    FunctionControlOptNoneINTELMask = 0x00010000,
};

enum MemorySemanticsShift {
    MemorySemanticsAcquireShift = 1,
    MemorySemanticsReleaseShift = 2,
    MemorySemanticsAcquireReleaseShift = 3,
    MemorySemanticsSequentiallyConsistentShift = 4,
    MemorySemanticsUniformMemoryShift = 6,
    MemorySemanticsSubgroupMemoryShift = 7,
    MemorySemanticsWorkgroupMemoryShift = 8,
    MemorySemanticsCrossWorkgroupMemoryShift = 9,
    MemorySemanticsAtomicCounterMemoryShift = 10,
    MemorySemanticsImageMemoryShift = 11,
    MemorySemanticsOutputMemoryShift = 12,
    MemorySemanticsOutputMemoryKHRShift = 12,
    MemorySemanticsMakeAvailableShift = 13,
    MemorySemanticsMakeAvailableKHRShift = 13,
    MemorySemanticsMakeVisibleShift = 14,
    MemorySemanticsMakeVisibleKHRShift = 14,
    MemorySemanticsVolatileShift = 15,
    MemorySemanticsMax = 0x7fffffff,
};

enum MemorySemanticsMask {
    MemorySemanticsMaskNone = 0,
    MemorySemanticsAcquireMask = 0x00000002,
    MemorySemanticsReleaseMask = 0x00000004,
    MemorySemanticsAcquireReleaseMask = 0x00000008,
    MemorySemanticsSequentiallyConsistentMask = 0x00000010,
    MemorySemanticsUniformMemoryMask = 0x00000040,
    MemorySemanticsSubgroupMemoryMask = 0x00000080,
    MemorySemanticsWorkgroupMemoryMask = 0x00000100,
    MemorySemanticsCrossWorkgroupMemoryMask = 0x00000200,
    MemorySemanticsAtomicCounterMemoryMask = 0x00000400,
    MemorySemanticsImageMemoryMask = 0x00000800,
    MemorySemanticsOutputMemoryMask = 0x00001000,
    MemorySemanticsOutputMemoryKHRMask = 0x00001000,
    MemorySemanticsMakeAvailableMask = 0x00002000,
    MemorySemanticsMakeAvailableKHRMask = 0x00002000,
    MemorySemanticsMakeVisibleMask = 0x00004000,
    MemorySemanticsMakeVisibleKHRMask = 0x00004000,
    MemorySemanticsVolatileMask = 0x00008000,
};

enum MemoryAccessShift {
    MemoryAccessVolatileShift = 0,
    MemoryAccessAlignedShift = 1,
    MemoryAccessNontemporalShift = 2,
    MemoryAccessMakePointerAvailableShift = 3,
    MemoryAccessMakePointerAvailableKHRShift = 3,
    MemoryAccessMakePointerVisibleShift = 4,
    MemoryAccessMakePointerVisibleKHRShift = 4,
    MemoryAccessNonPrivatePointerShift = 5,
    MemoryAccessNonPrivatePointerKHRShift = 5,
    MemoryAccessAliasScopeINTELMaskShift = 16,
    MemoryAccessNoAliasINTELMaskShift = 17,
    MemoryAccessMax = 0x7fffffff,
};

enum MemoryAccessMask {
    MemoryAccessMaskNone = 0,
    MemoryAccessVolatileMask = 0x00000001,
    MemoryAccessAlignedMask = 0x00000002,
    MemoryAccessNontemporalMask = 0x00000004,
    MemoryAccessMakePointerAvailableMask = 0x00000008,
    MemoryAccessMakePointerAvailableKHRMask = 0x00000008,
    MemoryAccessMakePointerVisibleMask = 0x00000010,
    MemoryAccessMakePointerVisibleKHRMask = 0x00000010,
    MemoryAccessNonPrivatePointerMask = 0x00000020,
    MemoryAccessNonPrivatePointerKHRMask = 0x00000020,
    MemoryAccessAliasScopeINTELMaskMask = 0x00010000,
    MemoryAccessNoAliasINTELMaskMask = 0x00020000,
};

enum Scope {
    ScopeCrossDevice = 0,
    ScopeDevice = 1,
    ScopeWorkgroup = 2,
    ScopeSubgroup = 3,
    ScopeInvocation = 4,
    ScopeQueueFamily = 5,
    ScopeQueueFamilyKHR = 5,
    ScopeShaderCallKHR = 6,
    ScopeMax = 0x7fffffff,
};

enum GroupOperation {
    GroupOperationReduce = 0,
    GroupOperationInclusiveScan = 1,
    GroupOperationExclusiveScan = 2,
    GroupOperationClusteredReduce = 3,
    GroupOperationPartitionedReduceNV = 6,
    GroupOperationPartitionedInclusiveScanNV = 7,
    GroupOperationPartitionedExclusiveScanNV = 8,
    GroupOperationMax = 0x7fffffff,
};

enum KernelEnqueueFlags {
    KernelEnqueueFlagsNoWait = 0,
    KernelEnqueueFlagsWaitKernel = 1,
    KernelEnqueueFlagsWaitWorkGroup = 2,
    KernelEnqueueFlagsMax = 0x7fffffff,
};

enum KernelProfilingInfoShift {
    KernelProfilingInfoCmdExecTimeShift = 0,
    KernelProfilingInfoMax = 0x7fffffff,
};

enum KernelProfilingInfoMask {
    KernelProfilingInfoMaskNone = 0,
    KernelProfilingInfoCmdExecTimeMask = 0x00000001,
};

enum Capability {
    CapabilityMatrix = 0,
    CapabilityShader = 1,
    CapabilityGeometry = 2,
    CapabilityTessellation = 3,
    CapabilityAddresses = 4,
    CapabilityLinkage = 5,
    CapabilityKernel = 6,
    CapabilityVector16 = 7,
    CapabilityFloat16Buffer = 8,
    CapabilityFloat16 = 9,
    CapabilityFloat64 = 10,
    CapabilityInt64 = 11,
    CapabilityInt64Atomics = 12,
    CapabilityImageBasic = 13,
    CapabilityImageReadWrite = 14,
    CapabilityImageMipmap = 15,
    CapabilityPipes = 17,
    CapabilityGroups = 18,
    CapabilityDeviceEnqueue = 19,
    CapabilityLiteralSampler = 20,
    CapabilityAtomicStorage = 21,
    CapabilityInt16 = 22,
    CapabilityTessellationPointSize = 23,
    CapabilityGeometryPointSize = 24,
    CapabilityImageGatherExtended = 25,
    CapabilityStorageImageMultisample = 27,
    CapabilityUniformBufferArrayDynamicIndexing = 28,
    CapabilitySampledImageArrayDynamicIndexing = 29,
    CapabilityStorageBufferArrayDynamicIndexing = 30,
    CapabilityStorageImageArrayDynamicIndexing = 31,
    CapabilityClipDistance = 32,
    CapabilityCullDistance = 33,
    CapabilityImageCubeArray = 34,
    CapabilitySampleRateShading = 35,
    CapabilityImageRect = 36,
    CapabilitySampledRect = 37,
    CapabilityGenericPointer = 38,
    CapabilityInt8 = 39,
    CapabilityInputAttachment = 40,
    CapabilitySparseResidency = 41,
    CapabilityMinLod = 42,
    CapabilitySampled1D = 43,
    CapabilityImage1D = 44,
    CapabilitySampledCubeArray = 45,
    CapabilitySampledBuffer = 46,
    CapabilityImageBuffer = 47,
    CapabilityImageMSArray = 48,
    CapabilityStorageImageExtendedFormats = 49,
    CapabilityImageQuery = 50,
    CapabilityDerivativeControl = 51,
    CapabilityInterpolationFunction = 52,
    CapabilityTransformFeedback = 53,
    CapabilityGeometryStreams = 54,
    CapabilityStorageImageReadWithoutFormat = 55,
    CapabilityStorageImageWriteWithoutFormat = 56,
    CapabilityMultiViewport = 57,
    CapabilitySubgroupDispatch = 58,
    CapabilityNamedBarrier = 59,
    CapabilityPipeStorage = 60,
    CapabilityGroupNonUniform = 61,
    CapabilityGroupNonUniformVote = 62,
    CapabilityGroupNonUniformArithmetic = 63,
    CapabilityGroupNonUniformBallot = 64,
    CapabilityGroupNonUniformShuffle = 65,
    CapabilityGroupNonUniformShuffleRelative = 66,
    CapabilityGroupNonUniformClustered = 67,
    CapabilityGroupNonUniformQuad = 68,
    CapabilityShaderLayer = 69,
    CapabilityShaderViewportIndex = 70,
    CapabilityUniformDecoration = 71,
    CapabilityCoreBuiltinsARM = 4165,
    CapabilityTileImageColorReadAccessEXT = 4166,
    CapabilityTileImageDepthReadAccessEXT = 4167,
    CapabilityTileImageStencilReadAccessEXT = 4168,
    CapabilityFragmentShadingRateKHR = 4422,
    CapabilitySubgroupBallotKHR = 4423,
    CapabilityDrawParameters = 4427,
    CapabilityWorkgroupMemoryExplicitLayoutKHR = 4428,
    CapabilityWorkgroupMemoryExplicitLayout8BitAccessKHR = 4429,
    CapabilityWorkgroupMemoryExplicitLayout16BitAccessKHR = 4430,
    CapabilitySubgroupVoteKHR = 4431,
    CapabilityStorageBuffer16BitAccess = 4433,
    CapabilityStorageUniformBufferBlock16 = 4433,
    CapabilityStorageUniform16 = 4434,
    CapabilityUniformAndStorageBuffer16BitAccess = 4434,
    CapabilityStoragePushConstant16 = 4435,
    CapabilityStorageInputOutput16 = 4436,
    CapabilityDeviceGroup = 4437,
    CapabilityMultiView = 4439,
    CapabilityVariablePointersStorageBuffer = 4441,
    CapabilityVariablePointers = 4442,
    CapabilityAtomicStorageOps = 4445,
    CapabilitySampleMaskPostDepthCoverage = 4447,
    CapabilityStorageBuffer8BitAccess = 4448,
    CapabilityUniformAndStorageBuffer8BitAccess = 4449,
    CapabilityStoragePushConstant8 = 4450,
    CapabilityDenormPreserve = 4464,
    CapabilityDenormFlushToZero = 4465,
    CapabilitySignedZeroInfNanPreserve = 4466,
    CapabilityRoundingModeRTE = 4467,
    CapabilityRoundingModeRTZ = 4468,
    CapabilityRayQueryProvisionalKHR = 4471,
    CapabilityRayQueryKHR = 4472,
    CapabilityRayTraversalPrimitiveCullingKHR = 4478,
    CapabilityRayTracingKHR = 4479,
    CapabilityFloat16ImageAMD = 5008,
    CapabilityImageGatherBiasLodAMD = 5009,
    CapabilityFragmentMaskAMD = 5010,
    CapabilityStencilExportEXT = 5013,
    CapabilityImageReadWriteLodAMD = 5015,
    CapabilityInt64ImageEXT = 5016,
    CapabilityShaderClockKHR = 5055,
    CapabilitySampleMaskOverrideCoverageNV = 5249,
    CapabilityGeometryShaderPassthroughNV = 5251,
    CapabilityShaderViewportIndexLayerEXT = 5254,
    CapabilityShaderViewportIndexLayerNV = 5254,
    CapabilityShaderViewportMaskNV = 5255,
    CapabilityShaderStereoViewNV = 5259,
    CapabilityPerViewAttributesNV = 5260,
    CapabilityFragmentFullyCoveredEXT = 5265,
    CapabilityMeshShadingNV = 5266,
    CapabilityImageFootprintNV = 5282,
    CapabilityMeshShadingEXT = 5283,
    CapabilityFragmentBarycentricKHR = 5284,
    CapabilityFragmentBarycentricNV = 5284,
    CapabilityComputeDerivativeGroupQuadsNV = 5288,
    CapabilityFragmentDensityEXT = 5291,
    CapabilityShadingRateNV = 5291,
    CapabilityGroupNonUniformPartitionedNV = 5297,
    CapabilityShaderNonUniform = 5301,
    CapabilityShaderNonUniformEXT = 5301,
    CapabilityRuntimeDescriptorArray = 5302,
    CapabilityRuntimeDescriptorArrayEXT = 5302,
    CapabilityInputAttachmentArrayDynamicIndexing = 5303,
    CapabilityInputAttachmentArrayDynamicIndexingEXT = 5303,
    CapabilityUniformTexelBufferArrayDynamicIndexing = 5304,
    CapabilityUniformTexelBufferArrayDynamicIndexingEXT = 5304,
    CapabilityStorageTexelBufferArrayDynamicIndexing = 5305,
    CapabilityStorageTexelBufferArrayDynamicIndexingEXT = 5305,
    CapabilityUniformBufferArrayNonUniformIndexing = 5306,
    CapabilityUniformBufferArrayNonUniformIndexingEXT = 5306,
    CapabilitySampledImageArrayNonUniformIndexing = 5307,
    CapabilitySampledImageArrayNonUniformIndexingEXT = 5307,
    CapabilityStorageBufferArrayNonUniformIndexing = 5308,
    CapabilityStorageBufferArrayNonUniformIndexingEXT = 5308,
    CapabilityStorageImageArrayNonUniformIndexing = 5309,
    CapabilityStorageImageArrayNonUniformIndexingEXT = 5309,
    CapabilityInputAttachmentArrayNonUniformIndexing = 5310,
    CapabilityInputAttachmentArrayNonUniformIndexingEXT = 5310,
    CapabilityUniformTexelBufferArrayNonUniformIndexing = 5311,
    CapabilityUniformTexelBufferArrayNonUniformIndexingEXT = 5311,
    CapabilityStorageTexelBufferArrayNonUniformIndexing = 5312,
    CapabilityStorageTexelBufferArrayNonUniformIndexingEXT = 5312,
    CapabilityRayTracingPositionFetchKHR = 5336,
    CapabilityRayTracingNV = 5340,
    CapabilityRayTracingMotionBlurNV = 5341,
    CapabilityVulkanMemoryModel = 5345,
    CapabilityVulkanMemoryModelKHR = 5345,
    CapabilityVulkanMemoryModelDeviceScope = 5346,
    CapabilityVulkanMemoryModelDeviceScopeKHR = 5346,
    CapabilityPhysicalStorageBufferAddresses = 5347,
    CapabilityPhysicalStorageBufferAddressesEXT = 5347,
    CapabilityComputeDerivativeGroupLinearNV = 5350,
    CapabilityRayTracingProvisionalKHR = 5353,
    CapabilityCooperativeMatrixNV = 5357,
    CapabilityFragmentShaderSampleInterlockEXT = 5363,
    CapabilityFragmentShaderShadingRateInterlockEXT = 5372,
    CapabilityShaderSMBuiltinsNV = 5373,
    CapabilityFragmentShaderPixelInterlockEXT = 5378,
    CapabilityDemoteToHelperInvocation = 5379,
    CapabilityDemoteToHelperInvocationEXT = 5379,
    CapabilityRayTracingOpacityMicromapEXT = 5381,
    CapabilityShaderInvocationReorderNV = 5383,
    CapabilityBindlessTextureNV = 5390,
    CapabilityRayQueryPositionFetchKHR = 5391,
    CapabilitySubgroupShuffleINTEL = 5568,
    CapabilitySubgroupBufferBlockIOINTEL = 5569,
    CapabilitySubgroupImageBlockIOINTEL = 5570,
    CapabilitySubgroupImageMediaBlockIOINTEL = 5579,
    CapabilityRoundToInfinityINTEL = 5582,
    CapabilityFloatingPointModeINTEL = 5583,
    CapabilityIntegerFunctions2INTEL = 5584,
    CapabilityFunctionPointersINTEL = 5603,
    CapabilityIndirectReferencesINTEL = 5604,
    CapabilityAsmINTEL = 5606,
    CapabilityAtomicFloat32MinMaxEXT = 5612,
    CapabilityAtomicFloat64MinMaxEXT = 5613,
    CapabilityAtomicFloat16MinMaxEXT = 5616,
    CapabilityVectorComputeINTEL = 5617,
    CapabilityVectorAnyINTEL = 5619,
    CapabilityExpectAssumeKHR = 5629,
    CapabilitySubgroupAvcMotionEstimationINTEL = 5696,
    CapabilitySubgroupAvcMotionEstimationIntraINTEL = 5697,
    CapabilitySubgroupAvcMotionEstimationChromaINTEL = 5698,
    CapabilityVariableLengthArrayINTEL = 5817,
    CapabilityFunctionFloatControlINTEL = 5821,
    CapabilityFPGAMemoryAttributesINTEL = 5824,
    CapabilityFPFastMathModeINTEL = 5837,
    CapabilityArbitraryPrecisionIntegersINTEL = 5844,
    CapabilityArbitraryPrecisionFloatingPointINTEL = 5845,
    CapabilityUnstructuredLoopControlsINTEL = 5886,
    CapabilityFPGALoopControlsINTEL = 5888,
    CapabilityKernelAttributesINTEL = 5892,
    CapabilityFPGAKernelAttributesINTEL = 5897,
    CapabilityFPGAMemoryAccessesINTEL = 5898,
    CapabilityFPGAClusterAttributesINTEL = 5904,
    CapabilityLoopFuseINTEL = 5906,
    CapabilityFPGADSPControlINTEL = 5908,
    CapabilityMemoryAccessAliasingINTEL = 5910,
    CapabilityFPGAInvocationPipeliningAttributesINTEL = 5916,
    CapabilityFPGABufferLocationINTEL = 5920,
    CapabilityArbitraryPrecisionFixedPointINTEL = 5922,
    CapabilityUSMStorageClassesINTEL = 5935,
    CapabilityRuntimeAlignedAttributeINTEL = 5939,
    CapabilityIOPipesINTEL = 5943,
    CapabilityBlockingPipesINTEL = 5945,
    CapabilityFPGARegINTEL = 5948,
    CapabilityDotProductInputAll = 6016,
    CapabilityDotProductInputAllKHR = 6016,
    CapabilityDotProductInput4x8Bit = 6017,
    CapabilityDotProductInput4x8BitKHR = 6017,
    CapabilityDotProductInput4x8BitPacked = 6018,
    CapabilityDotProductInput4x8BitPackedKHR = 6018,
    CapabilityDotProduct = 6019,
    CapabilityDotProductKHR = 6019,
    CapabilityRayCullMaskKHR = 6020,
    CapabilityCooperativeMatrixKHR = 6022,
    CapabilityBitInstructions = 6025,
    CapabilityGroupNonUniformRotateKHR = 6026,
    CapabilityAtomicFloat32AddEXT = 6033,
    CapabilityAtomicFloat64AddEXT = 6034,
    CapabilityLongConstantCompositeINTEL = 6089,
    CapabilityOptNoneINTEL = 6094,
    CapabilityAtomicFloat16AddEXT = 6095,
    CapabilityDebugInfoModuleINTEL = 6114,
    CapabilitySplitBarrierINTEL = 6141,
    CapabilityFPGAArgumentInterfacesINTEL = 6174,
    CapabilityGroupUniformArithmeticKHR = 6400,
    CapabilityMax = 0x7fffffff,
};

enum RayFlagsShift {
    RayFlagsOpaqueKHRShift = 0,
    RayFlagsNoOpaqueKHRShift = 1,
    RayFlagsTerminateOnFirstHitKHRShift = 2,
    RayFlagsSkipClosestHitShaderKHRShift = 3,
    RayFlagsCullBackFacingTrianglesKHRShift = 4,
    RayFlagsCullFrontFacingTrianglesKHRShift = 5,
    RayFlagsCullOpaqueKHRShift = 6,
    RayFlagsCullNoOpaqueKHRShift = 7,
    RayFlagsSkipTrianglesKHRShift = 8,
    RayFlagsSkipAABBsKHRShift = 9,
    RayFlagsForceOpacityMicromap2StateEXTShift = 10,
    RayFlagsMax = 0x7fffffff,
};

enum RayFlagsMask {
    RayFlagsMaskNone = 0,
    RayFlagsOpaqueKHRMask = 0x00000001,
    RayFlagsNoOpaqueKHRMask = 0x00000002,
    RayFlagsTerminateOnFirstHitKHRMask = 0x00000004,
    RayFlagsSkipClosestHitShaderKHRMask = 0x00000008,
    RayFlagsCullBackFacingTrianglesKHRMask = 0x00000010,
    RayFlagsCullFrontFacingTrianglesKHRMask = 0x00000020,
    RayFlagsCullOpaqueKHRMask = 0x00000040,
    RayFlagsCullNoOpaqueKHRMask = 0x00000080,
    RayFlagsSkipTrianglesKHRMask = 0x00000100,
    RayFlagsSkipAABBsKHRMask = 0x00000200,
    RayFlagsForceOpacityMicromap2StateEXTMask = 0x00000400,
};

enum RayQueryIntersection {
    RayQueryIntersectionRayQueryCandidateIntersectionKHR = 0,
    RayQueryIntersectionRayQueryCommittedIntersectionKHR = 1,
    RayQueryIntersectionMax = 0x7fffffff,
};

enum RayQueryCommittedIntersectionType {
    RayQueryCommittedIntersectionTypeRayQueryCommittedIntersectionNoneKHR = 0,
    RayQueryCommittedIntersectionTypeRayQueryCommittedIntersectionTriangleKHR = 1,
    RayQueryCommittedIntersectionTypeRayQueryCommittedIntersectionGeneratedKHR = 2,
    RayQueryCommittedIntersectionTypeMax = 0x7fffffff,
};

enum RayQueryCandidateIntersectionType {
    RayQueryCandidateIntersectionTypeRayQueryCandidateIntersectionTriangleKHR = 0,
    RayQueryCandidateIntersectionTypeRayQueryCandidateIntersectionAABBKHR = 1,
    RayQueryCandidateIntersectionTypeMax = 0x7fffffff,
};

enum FragmentShadingRateShift {
    FragmentShadingRateVertical2PixelsShift = 0,
    FragmentShadingRateVertical4PixelsShift = 1,
    FragmentShadingRateHorizontal2PixelsShift = 2,
    FragmentShadingRateHorizontal4PixelsShift = 3,
    FragmentShadingRateMax = 0x7fffffff,
};

enum FragmentShadingRateMask {
    FragmentShadingRateMaskNone = 0,
    FragmentShadingRateVertical2PixelsMask = 0x00000001,
    FragmentShadingRateVertical4PixelsMask = 0x00000002,
    FragmentShadingRateHorizontal2PixelsMask = 0x00000004,
    FragmentShadingRateHorizontal4PixelsMask = 0x00000008,
};

enum FPDenormMode {
    FPDenormModePreserve = 0,
    FPDenormModeFlushToZero = 1,
    FPDenormModeMax = 0x7fffffff,
};

enum FPOperationMode {
    FPOperationModeIEEE = 0,
    FPOperationModeALT = 1,
    FPOperationModeMax = 0x7fffffff,
};

enum QuantizationModes {
    QuantizationModesTRN = 0,
    QuantizationModesTRN_ZERO = 1,
    QuantizationModesRND = 2,
    QuantizationModesRND_ZERO = 3,
    QuantizationModesRND_INF = 4,
    QuantizationModesRND_MIN_INF = 5,
    QuantizationModesRND_CONV = 6,
    QuantizationModesRND_CONV_ODD = 7,
    QuantizationModesMax = 0x7fffffff,
};

enum OverflowModes {
    OverflowModesWRAP = 0,
    OverflowModesSAT = 1,
    OverflowModesSAT_ZERO = 2,
    OverflowModesSAT_SYM = 3,
    OverflowModesMax = 0x7fffffff,
};

enum PackedVectorFormat {
    PackedVectorFormatPackedVectorFormat4x8Bit = 0,
    PackedVectorFormatPackedVectorFormat4x8BitKHR = 0,
    PackedVectorFormatMax = 0x7fffffff,
};

enum CooperativeMatrixOperandsShift {
    CooperativeMatrixOperandsMatrixASignedComponentsShift = 0,
    CooperativeMatrixOperandsMatrixBSignedComponentsShift = 1,
    CooperativeMatrixOperandsMatrixCSignedComponentsShift = 2,
    CooperativeMatrixOperandsMatrixResultSignedComponentsShift = 3,
    CooperativeMatrixOperandsSaturatingAccumulationShift = 4,
    CooperativeMatrixOperandsMax = 0x7fffffff,
};

enum CooperativeMatrixOperandsMask {
    CooperativeMatrixOperandsMaskNone = 0,
    CooperativeMatrixOperandsMatrixASignedComponentsMask = 0x00000001,
    CooperativeMatrixOperandsMatrixBSignedComponentsMask = 0x00000002,
    CooperativeMatrixOperandsMatrixCSignedComponentsMask = 0x00000004,
    CooperativeMatrixOperandsMatrixResultSignedComponentsMask = 0x00000008,
    CooperativeMatrixOperandsSaturatingAccumulationMask = 0x00000010,
};

enum CooperativeMatrixLayout {
    CooperativeMatrixLayoutCooperativeMatrixRowMajorKHR = 0,
    CooperativeMatrixLayoutCooperativeMatrixColumnMajorKHR = 1,
    CooperativeMatrixLayoutMax = 0x7fffffff,
};

enum CooperativeMatrixUse {
    CooperativeMatrixUseMatrixAKHR = 0,
    CooperativeMatrixUseMatrixBKHR = 1,
    CooperativeMatrixUseMatrixAccumulatorKHR = 2,
    CooperativeMatrixUseMax = 0x7fffffff,
};

enum Op {
    OpNop = 0,
    OpUndef = 1,
    OpSourceContinued = 2,
    OpSource = 3,
    OpSourceExtension = 4,
    OpName = 5,
    OpMemberName = 6,
    OpString = 7,
    OpLine = 8,
    OpExtension = 10,
    OpExtInstImport = 11,
    OpExtInst = 12,
    OpMemoryModel = 14,
    OpEntryPoint = 15,
    OpExecutionMode = 16,
    OpCapability = 17,
    OpTypeVoid = 19,
    OpTypeBool = 20,
    OpTypeInt = 21,
    OpTypeFloat = 22,
    OpTypeVector = 23,
    OpTypeMatrix = 24,
    OpTypeImage = 25,
    OpTypeSampler = 26,
    OpTypeSampledImage = 27,
    OpTypeArray = 28,
    OpTypeRuntimeArray = 29,
    OpTypeStruct = 30,
    OpTypeOpaque = 31,
    OpTypePointer = 32,
    OpTypeFunction = 33,
    OpTypeEvent = 34,
    OpTypeDeviceEvent = 35,
    OpTypeReserveId = 36,
    OpTypeQueue = 37,
    OpTypePipe = 38,
    OpTypeForwardPointer = 39,
    OpConstantTrue = 41,
    OpConstantFalse = 42,
    OpConstant = 43,
    OpConstantComposite = 44,
    OpConstantSampler = 45,
    OpConstantNull = 46,
    OpSpecConstantTrue = 48,
    OpSpecConstantFalse = 49,
    OpSpecConstant = 50,
    OpSpecConstantComposite = 51,
    OpSpecConstantOp = 52,
    OpFunction = 54,
    OpFunctionParameter = 55,
    OpFunctionEnd = 56,
    OpFunctionCall = 57,
    OpVariable = 59,
    OpImageTexelPointer = 60,
    OpLoad = 61,
    OpStore = 62,
    OpCopyMemory = 63,
    OpCopyMemorySized = 64,
    OpAccessChain = 65,
    OpInBoundsAccessChain = 66,
    OpPtrAccessChain = 67,
    OpArrayLength = 68,
    OpGenericPtrMemSemantics = 69,
    OpInBoundsPtrAccessChain = 70,
    OpDecorate = 71,
    OpMemberDecorate = 72,
    OpDecorationGroup = 73,
    OpGroupDecorate = 74,
    OpGroupMemberDecorate = 75,
    OpVectorExtractDynamic = 77,
    OpVectorInsertDynamic = 78,
    OpVectorShuffle = 79,
    OpCompositeConstruct = 80,
    OpCompositeExtract = 81,
    OpCompositeInsert = 82,
    OpCopyObject = 83,
    OpTranspose = 84,
    OpSampledImage = 86,
    OpImageSampleImplicitLod = 87,
    OpImageSampleExplicitLod = 88,
    OpImageSampleDrefImplicitLod = 89,
    OpImageSampleDrefExplicitLod = 90,
    OpImageSampleProjImplicitLod = 91,
    OpImageSampleProjExplicitLod = 92,
    OpImageSampleProjDrefImplicitLod = 93,
    OpImageSampleProjDrefExplicitLod = 94,
    OpImageFetch = 95,
    OpImageGather = 96,
    OpImageDrefGather = 97,
    OpImageRead = 98,
    OpImageWrite = 99,
    OpImage = 100,
    OpImageQueryFormat = 101,
    OpImageQueryOrder = 102,
    OpImageQuerySizeLod = 103,
    OpImageQuerySize = 104,
    OpImageQueryLod = 105,
    OpImageQueryLevels = 106,
    OpImageQuerySamples = 107,
    OpConvertFToU = 109,
    OpConvertFToS = 110,
    OpConvertSToF = 111,
    OpConvertUToF = 112,
    OpUConvert = 113,
    OpSConvert = 114,
    OpFConvert = 115,
    OpQuantizeToF16 = 116,
    OpConvertPtrToU = 117,
    OpSatConvertSToU = 118,
    OpSatConvertUToS = 119,
    OpConvertUToPtr = 120,
    OpPtrCastToGeneric = 121,
    OpGenericCastToPtr = 122,
    OpGenericCastToPtrExplicit = 123,
    OpBitcast = 124,
    OpSNegate = 126,
    OpFNegate = 127,
    OpIAdd = 128,
    OpFAdd = 129,
    OpISub = 130,
    OpFSub = 131,
    OpIMul = 132,
    OpFMul = 133,
    OpUDiv = 134,
    OpSDiv = 135,
    OpFDiv = 136,
    OpUMod = 137,
    OpSRem = 138,
    OpSMod = 139,
    OpFRem = 140,
    OpFMod = 141,
    OpVectorTimesScalar = 142,
    OpMatrixTimesScalar = 143,
    OpVectorTimesMatrix = 144,
    OpMatrixTimesVector = 145,
    OpMatrixTimesMatrix = 146,
    OpOuterProduct = 147,
    OpDot = 148,
    OpIAddCarry = 149,
    OpISubBorrow = 150,
    OpUMulExtended = 151,
    OpSMulExtended = 152,
    OpAny = 154,
    OpAll = 155,
    OpIsNan = 156,
    OpIsInf = 157,
    OpIsFinite = 158,
    OpIsNormal = 159,
    OpSignBitSet = 160,
    OpLessOrGreater = 161,
    OpOrdered = 162,
    OpUnordered = 163,
    OpLogicalEqual = 164,
    OpLogicalNotEqual = 165,
    OpLogicalOr = 166,
    OpLogicalAnd = 167,
    OpLogicalNot = 168,
    OpSelect = 169,
    OpIEqual = 170,
    OpINotEqual = 171,
    OpUGreaterThan = 172,
    OpSGreaterThan = 173,
    OpUGreaterThanEqual = 174,
    OpSGreaterThanEqual = 175,
    OpULessThan = 176,
    OpSLessThan = 177,
    OpULessThanEqual = 178,
    OpSLessThanEqual = 179,
    OpFOrdEqual = 180,
    OpFUnordEqual = 181,
    OpFOrdNotEqual = 182,
    OpFUnordNotEqual = 183,
    OpFOrdLessThan = 184,
    OpFUnordLessThan = 185,
    OpFOrdGreaterThan = 186,
    OpFUnordGreaterThan = 187,
    OpFOrdLessThanEqual = 188,
    OpFUnordLessThanEqual = 189,
    OpFOrdGreaterThanEqual = 190,
    OpFUnordGreaterThanEqual = 191,
    OpShiftRightLogical = 194,
    OpShiftRightArithmetic = 195,
    OpShiftLeftLogical = 196,
    OpBitwiseOr = 197,
    OpBitwiseXor = 198,
    OpBitwiseAnd = 199,
    OpNot = 200,
    OpBitFieldInsert = 201,
    OpBitFieldSExtract = 202,
    OpBitFieldUExtract = 203,
    OpBitReverse = 204,
    OpBitCount = 205,
    OpDPdx = 207,
    OpDPdy = 208,
    OpFwidth = 209,
    OpDPdxFine = 210,
    OpDPdyFine = 211,
    OpFwidthFine = 212,
    OpDPdxCoarse = 213,
    OpDPdyCoarse = 214,
    OpFwidthCoarse = 215,
    OpEmitVertex = 218,
    OpEndPrimitive = 219,
    OpEmitStreamVertex = 220,
    OpEndStreamPrimitive = 221,
    OpControlBarrier = 224,
    OpMemoryBarrier = 225,
    OpAtomicLoad = 227,
    OpAtomicStore = 228,
    OpAtomicExchange = 229,
    OpAtomicCompareExchange = 230,
    OpAtomicCompareExchangeWeak = 231,
    OpAtomicIIncrement = 232,
    OpAtomicIDecrement = 233,
    OpAtomicIAdd = 234,
    OpAtomicISub = 235,
    OpAtomicSMin = 236,
    OpAtomicUMin = 237,
    OpAtomicSMax = 238,
    OpAtomicUMax = 239,
    OpAtomicAnd = 240,
    OpAtomicOr = 241,
    OpAtomicXor = 242,
    OpPhi = 245,
    OpLoopMerge = 246,
    OpSelectionMerge = 247,
    OpLabel = 248,
    OpBranch = 249,
    OpBranchConditional = 250,
    OpSwitch = 251,
    OpKill = 252,
    OpReturn = 253,
    OpReturnValue = 254,
    OpUnreachable = 255,
    OpLifetimeStart = 256,
    OpLifetimeStop = 257,
    OpGroupAsyncCopy = 259,
    OpGroupWaitEvents = 260,
    OpGroupAll = 261,
    OpGroupAny = 262,
    OpGroupBroadcast = 263,
    OpGroupIAdd = 264,
    OpGroupFAdd = 265,
    OpGroupFMin = 266,
    OpGroupUMin = 267,
    OpGroupSMin = 268,
    OpGroupFMax = 269,
    OpGroupUMax = 270,
    OpGroupSMax = 271,
    OpReadPipe = 274,
    OpWritePipe = 275,
    OpReservedReadPipe = 276,
    OpReservedWritePipe = 277,
    OpReserveReadPipePackets = 278,
    OpReserveWritePipePackets = 279,
    OpCommitReadPipe = 280,
    OpCommitWritePipe = 281,
    OpIsValidReserveId = 282,
    OpGetNumPipePackets = 283,
    OpGetMaxPipePackets = 284,
    OpGroupReserveReadPipePackets = 285,
    OpGroupReserveWritePipePackets = 286,
    OpGroupCommitReadPipe = 287,
    OpGroupCommitWritePipe = 288,
    OpEnqueueMarker = 291,
    OpEnqueueKernel = 292,
    OpGetKernelNDrangeSubGroupCount = 293,
    OpGetKernelNDrangeMaxSubGroupSize = 294,
    OpGetKernelWorkGroupSize = 295,
    OpGetKernelPreferredWorkGroupSizeMultiple = 296,
    OpRetainEvent = 297,
    OpReleaseEvent = 298,
    OpCreateUserEvent = 299,
    OpIsValidEvent = 300,
    OpSetUserEventStatus = 301,
    OpCaptureEventProfilingInfo = 302,
    OpGetDefaultQueue = 303,
    OpBuildNDRange = 304,
    OpImageSparseSampleImplicitLod = 305,
    OpImageSparseSampleExplicitLod = 306,
    OpImageSparseSampleDrefImplicitLod = 307,
    OpImageSparseSampleDrefExplicitLod = 308,
    OpImageSparseSampleProjImplicitLod = 309,
    OpImageSparseSampleProjExplicitLod = 310,
    OpImageSparseSampleProjDrefImplicitLod = 311,
    OpImageSparseSampleProjDrefExplicitLod = 312,
    OpImageSparseFetch = 313,
    OpImageSparseGather = 314,
    OpImageSparseDrefGather = 315,
    OpImageSparseTexelsResident = 316,
    OpNoLine = 317,
    OpAtomicFlagTestAndSet = 318,
    OpAtomicFlagClear = 319,
    OpImageSparseRead = 320,
    OpSizeOf = 321,
    OpTypePipeStorage = 322,
    OpConstantPipeStorage = 323,
    OpCreatePipeFromPipeStorage = 324,
    OpGetKernelLocalSizeForSubgroupCount = 325,
    OpGetKernelMaxNumSubgroups = 326,
    OpTypeNamedBarrier = 327,
    OpNamedBarrierInitialize = 328,
    OpMemoryNamedBarrier = 329,
    OpModuleProcessed = 330,
    OpExecutionModeId = 331,
    OpDecorateId = 332,
    OpGroupNonUniformElect = 333,
    OpGroupNonUniformAll = 334,
    OpGroupNonUniformAny = 335,
    OpGroupNonUniformAllEqual = 336,
    OpGroupNonUniformBroadcast = 337,
    OpGroupNonUniformBroadcastFirst = 338,
    OpGroupNonUniformBallot = 339,
    OpGroupNonUniformInverseBallot = 340,
    OpGroupNonUniformBallotBitExtract = 341,
    OpGroupNonUniformBallotBitCount = 342,
    OpGroupNonUniformBallotFindLSB = 343,
    OpGroupNonUniformBallotFindMSB = 344,
    OpGroupNonUniformShuffle = 345,
    OpGroupNonUniformShuffleXor = 346,
    OpGroupNonUniformShuffleUp = 347,
    OpGroupNonUniformShuffleDown = 348,
    OpGroupNonUniformIAdd = 349,
    OpGroupNonUniformFAdd = 350,
    OpGroupNonUniformIMul = 351,
    OpGroupNonUniformFMul = 352,
    OpGroupNonUniformSMin = 353,
    OpGroupNonUniformUMin = 354,
    OpGroupNonUniformFMin = 355,
    OpGroupNonUniformSMax = 356,
    OpGroupNonUniformUMax = 357,
    OpGroupNonUniformFMax = 358,
    OpGroupNonUniformBitwiseAnd = 359,
    OpGroupNonUniformBitwiseOr = 360,
    OpGroupNonUniformBitwiseXor = 361,
    OpGroupNonUniformLogicalAnd = 362,
    OpGroupNonUniformLogicalOr = 363,
    OpGroupNonUniformLogicalXor = 364,
    OpGroupNonUniformQuadBroadcast = 365,
    OpGroupNonUniformQuadSwap = 366,
    OpCopyLogical = 400,
    OpPtrEqual = 401,
    OpPtrNotEqual = 402,
    OpPtrDiff = 403,
    OpColorAttachmentReadEXT = 4160,
    OpDepthAttachmentReadEXT = 4161,
    OpStencilAttachmentReadEXT = 4162,
    OpTerminateInvocation = 4416,
    OpSubgroupBallotKHR = 4421,
    OpSubgroupFirstInvocationKHR = 4422,
    OpSubgroupAllKHR = 4428,
    OpSubgroupAnyKHR = 4429,
    OpSubgroupAllEqualKHR = 4430,
    OpGroupNonUniformRotateKHR = 4431,
    OpSubgroupReadInvocationKHR = 4432,
    OpTraceRayKHR = 4445,
    OpExecuteCallableKHR = 4446,
    OpConvertUToAccelerationStructureKHR = 4447,
    OpIgnoreIntersectionKHR = 4448,
    OpTerminateRayKHR = 4449,
    OpSDot = 4450,
    OpSDotKHR = 4450,
    OpUDot = 4451,
    OpUDotKHR = 4451,
    OpSUDot = 4452,
    OpSUDotKHR = 4452,
    OpSDotAccSat = 4453,
    OpSDotAccSatKHR = 4453,
    OpUDotAccSat = 4454,
    OpUDotAccSatKHR = 4454,
    OpSUDotAccSat = 4455,
    OpSUDotAccSatKHR = 4455,
    OpTypeCooperativeMatrixKHR = 4456,
    OpCooperativeMatrixLoadKHR = 4457,
    OpCooperativeMatrixStoreKHR = 4458,
    OpCooperativeMatrixMulAddKHR = 4459,
    OpCooperativeMatrixLengthKHR = 4460,
    OpTypeRayQueryKHR = 4472,
    OpRayQueryInitializeKHR = 4473,
    OpRayQueryTerminateKHR = 4474,
    OpRayQueryGenerateIntersectionKHR = 4475,
    OpRayQueryConfirmIntersectionKHR = 4476,
    OpRayQueryProceedKHR = 4477,
    OpRayQueryGetIntersectionTypeKHR = 4479,
    OpGroupIAddNonUniformAMD = 5000,
    OpGroupFAddNonUniformAMD = 5001,
    OpGroupFMinNonUniformAMD = 5002,
    OpGroupUMinNonUniformAMD = 5003,
    OpGroupSMinNonUniformAMD = 5004,
    OpGroupFMaxNonUniformAMD = 5005,
    OpGroupUMaxNonUniformAMD = 5006,
    OpGroupSMaxNonUniformAMD = 5007,
    OpFragmentMaskFetchAMD = 5011,
    OpFragmentFetchAMD = 5012,
    OpReadClockKHR = 5056,
    OpHitObjectRecordHitMotionNV = 5249,
    OpHitObjectRecordHitWithIndexMotionNV = 5250,
    OpHitObjectRecordMissMotionNV = 5251,
    OpHitObjectGetWorldToObjectNV = 5252,
    OpHitObjectGetObjectToWorldNV = 5253,
    OpHitObjectGetObjectRayDirectionNV = 5254,
    OpHitObjectGetObjectRayOriginNV = 5255,
    OpHitObjectTraceRayMotionNV = 5256,
    OpHitObjectGetShaderRecordBufferHandleNV = 5257,
    OpHitObjectGetShaderBindingTableRecordIndexNV = 5258,
    OpHitObjectRecordEmptyNV = 5259,
    OpHitObjectTraceRayNV = 5260,
    OpHitObjectRecordHitNV = 5261,
    OpHitObjectRecordHitWithIndexNV = 5262,
    OpHitObjectRecordMissNV = 5263,
    OpHitObjectExecuteShaderNV = 5264,
    OpHitObjectGetCurrentTimeNV = 5265,
    OpHitObjectGetAttributesNV = 5266,
    OpHitObjectGetHitKindNV = 5267,
    OpHitObjectGetPrimitiveIndexNV = 5268,
    OpHitObjectGetGeometryIndexNV = 5269,
    OpHitObjectGetInstanceIdNV = 5270,
    OpHitObjectGetInstanceCustomIndexNV = 5271,
    OpHitObjectGetWorldRayDirectionNV = 5272,
    OpHitObjectGetWorldRayOriginNV = 5273,
    OpHitObjectGetRayTMaxNV = 5274,
    OpHitObjectGetRayTMinNV = 5275,
    OpHitObjectIsEmptyNV = 5276,
    OpHitObjectIsHitNV = 5277,
    OpHitObjectIsMissNV = 5278,
    OpReorderThreadWithHitObjectNV = 5279,
    OpReorderThreadWithHintNV = 5280,
    OpTypeHitObjectNV = 5281,
    OpImageSampleFootprintNV = 5283,
    OpEmitMeshTasksEXT = 5294,
    OpSetMeshOutputsEXT = 5295,
    OpGroupNonUniformPartitionNV = 5296,
    OpWritePackedPrimitiveIndices4x8NV = 5299,
    OpReportIntersectionKHR = 5334,
    OpReportIntersectionNV = 5334,
    OpIgnoreIntersectionNV = 5335,
    OpTerminateRayNV = 5336,
    OpTraceNV = 5337,
    OpTraceMotionNV = 5338,
    OpTraceRayMotionNV = 5339,
    OpRayQueryGetIntersectionTriangleVertexPositionsKHR = 5340,
    OpTypeAccelerationStructureKHR = 5341,
    OpTypeAccelerationStructureNV = 5341,
    OpExecuteCallableNV = 5344,
    OpTypeCooperativeMatrixNV = 5358,
    OpCooperativeMatrixLoadNV = 5359,
    OpCooperativeMatrixStoreNV = 5360,
    OpCooperativeMatrixMulAddNV = 5361,
    OpCooperativeMatrixLengthNV = 5362,
    OpBeginInvocationInterlockEXT = 5364,
    OpEndInvocationInterlockEXT = 5365,
    OpDemoteToHelperInvocation = 5380,
    OpDemoteToHelperInvocationEXT = 5380,
    OpIsHelperInvocationEXT = 5381,
    OpConvertUToImageNV = 5391,
    OpConvertUToSamplerNV = 5392,
    OpConvertImageToUNV = 5393,
    OpConvertSamplerToUNV = 5394,
    OpConvertUToSampledImageNV = 5395,
    OpConvertSampledImageToUNV = 5396,
    OpSamplerImageAddressingModeNV = 5397,
    OpSubgroupShuffleINTEL = 5571,
    OpSubgroupShuffleDownINTEL = 5572,
    OpSubgroupShuffleUpINTEL = 5573,
    OpSubgroupShuffleXorINTEL = 5574,
    OpSubgroupBlockReadINTEL = 5575,
    OpSubgroupBlockWriteINTEL = 5576,
    OpSubgroupImageBlockReadINTEL = 5577,
    OpSubgroupImageBlockWriteINTEL = 5578,
    OpSubgroupImageMediaBlockReadINTEL = 5580,
    OpSubgroupImageMediaBlockWriteINTEL = 5581,
    OpUCountLeadingZerosINTEL = 5585,
    OpUCountTrailingZerosINTEL = 5586,
    OpAbsISubINTEL = 5587,
    OpAbsUSubINTEL = 5588,
    OpIAddSatINTEL = 5589,
    OpUAddSatINTEL = 5590,
    OpIAverageINTEL = 5591,
    OpUAverageINTEL = 5592,
    OpIAverageRoundedINTEL = 5593,
    OpUAverageRoundedINTEL = 5594,
    OpISubSatINTEL = 5595,
    OpUSubSatINTEL = 5596,
    OpIMul32x16INTEL = 5597,
    OpUMul32x16INTEL = 5598,
    OpConstantFunctionPointerINTEL = 5600,
    OpFunctionPointerCallINTEL = 5601,
    OpAsmTargetINTEL = 5609,
    OpAsmINTEL = 5610,
    OpAsmCallINTEL = 5611,
    OpAtomicFMinEXT = 5614,
    OpAtomicFMaxEXT = 5615,
    OpAssumeTrueKHR = 5630,
    OpExpectKHR = 5631,
    OpDecorateString = 5632,
    OpDecorateStringGOOGLE = 5632,
    OpMemberDecorateString = 5633,
    OpMemberDecorateStringGOOGLE = 5633,
    OpVmeImageINTEL = 5699,
    OpTypeVmeImageINTEL = 5700,
    OpTypeAvcImePayloadINTEL = 5701,
    OpTypeAvcRefPayloadINTEL = 5702,
    OpTypeAvcSicPayloadINTEL = 5703,
    OpTypeAvcMcePayloadINTEL = 5704,
    OpTypeAvcMceResultINTEL = 5705,
    OpTypeAvcImeResultINTEL = 5706,
    OpTypeAvcImeResultSingleReferenceStreamoutINTEL = 5707,
    OpTypeAvcImeResultDualReferenceStreamoutINTEL = 5708,
    OpTypeAvcImeSingleReferenceStreaminINTEL = 5709,
    OpTypeAvcImeDualReferenceStreaminINTEL = 5710,
    OpTypeAvcRefResultINTEL = 5711,
    OpTypeAvcSicResultINTEL = 5712,
    OpSubgroupAvcMceGetDefaultInterBaseMultiReferencePenaltyINTEL = 5713,
    OpSubgroupAvcMceSetInterBaseMultiReferencePenaltyINTEL = 5714,
    OpSubgroupAvcMceGetDefaultInterShapePenaltyINTEL = 5715,
    OpSubgroupAvcMceSetInterShapePenaltyINTEL = 5716,
    OpSubgroupAvcMceGetDefaultInterDirectionPenaltyINTEL = 5717,
    OpSubgroupAvcMceSetInterDirectionPenaltyINTEL = 5718,
    OpSubgroupAvcMceGetDefaultIntraLumaShapePenaltyINTEL = 5719,
    OpSubgroupAvcMceGetDefaultInterMotionVectorCostTableINTEL = 5720,
    OpSubgroupAvcMceGetDefaultHighPenaltyCostTableINTEL = 5721,
    OpSubgroupAvcMceGetDefaultMediumPenaltyCostTableINTEL = 5722,
    OpSubgroupAvcMceGetDefaultLowPenaltyCostTableINTEL = 5723,
    OpSubgroupAvcMceSetMotionVectorCostFunctionINTEL = 5724,
    OpSubgroupAvcMceGetDefaultIntraLumaModePenaltyINTEL = 5725,
    OpSubgroupAvcMceGetDefaultNonDcLumaIntraPenaltyINTEL = 5726,
    OpSubgroupAvcMceGetDefaultIntraChromaModeBasePenaltyINTEL = 5727,
    OpSubgroupAvcMceSetAcOnlyHaarINTEL = 5728,
    OpSubgroupAvcMceSetSourceInterlacedFieldPolarityINTEL = 5729,
    OpSubgroupAvcMceSetSingleReferenceInterlacedFieldPolarityINTEL = 5730,
    OpSubgroupAvcMceSetDualReferenceInterlacedFieldPolaritiesINTEL = 5731,
    OpSubgroupAvcMceConvertToImePayloadINTEL = 5732,
    OpSubgroupAvcMceConvertToImeResultINTEL = 5733,
    OpSubgroupAvcMceConvertToRefPayloadINTEL = 5734,
    OpSubgroupAvcMceConvertToRefResultINTEL = 5735,
    OpSubgroupAvcMceConvertToSicPayloadINTEL = 5736,
    OpSubgroupAvcMceConvertToSicResultINTEL = 5737,
    OpSubgroupAvcMceGetMotionVectorsINTEL = 5738,
    OpSubgroupAvcMceGetInterDistortionsINTEL = 5739,
    OpSubgroupAvcMceGetBestInterDistortionsINTEL = 5740,
    OpSubgroupAvcMceGetInterMajorShapeINTEL = 5741,
    OpSubgroupAvcMceGetInterMinorShapeINTEL = 5742,
    OpSubgroupAvcMceGetInterDirectionsINTEL = 5743,
    OpSubgroupAvcMceGetInterMotionVectorCountINTEL = 5744,
    OpSubgroupAvcMceGetInterReferenceIdsINTEL = 5745,
    OpSubgroupAvcMceGetInterReferenceInterlacedFieldPolaritiesINTEL = 5746,
    OpSubgroupAvcImeInitializeINTEL = 5747,
    OpSubgroupAvcImeSetSingleReferenceINTEL = 5748,
    OpSubgroupAvcImeSetDualReferenceINTEL = 5749,
    OpSubgroupAvcImeRefWindowSizeINTEL = 5750,
    OpSubgroupAvcImeAdjustRefOffsetINTEL = 5751,
    OpSubgroupAvcImeConvertToMcePayloadINTEL = 5752,
    OpSubgroupAvcImeSetMaxMotionVectorCountINTEL = 5753,
    OpSubgroupAvcImeSetUnidirectionalMixDisableINTEL = 5754,
    OpSubgroupAvcImeSetEarlySearchTerminationThresholdINTEL = 5755,
    OpSubgroupAvcImeSetWeightedSadINTEL = 5756,
    OpSubgroupAvcImeEvaluateWithSingleReferenceINTEL = 5757,
    OpSubgroupAvcImeEvaluateWithDualReferenceINTEL = 5758,
    OpSubgroupAvcImeEvaluateWithSingleReferenceStreaminINTEL = 5759,
    OpSubgroupAvcImeEvaluateWithDualReferenceStreaminINTEL = 5760,
    OpSubgroupAvcImeEvaluateWithSingleReferenceStreamoutINTEL = 5761,
    OpSubgroupAvcImeEvaluateWithDualReferenceStreamoutINTEL = 5762,
    OpSubgroupAvcImeEvaluateWithSingleReferenceStreaminoutINTEL = 5763,
    OpSubgroupAvcImeEvaluateWithDualReferenceStreaminoutINTEL = 5764,
    OpSubgroupAvcImeConvertToMceResultINTEL = 5765,
    OpSubgroupAvcImeGetSingleReferenceStreaminINTEL = 5766,
    OpSubgroupAvcImeGetDualReferenceStreaminINTEL = 5767,
    OpSubgroupAvcImeStripSingleReferenceStreamoutINTEL = 5768,
    OpSubgroupAvcImeStripDualReferenceStreamoutINTEL = 5769,
    OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeMotionVectorsINTEL = 5770,
    OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeDistortionsINTEL = 5771,
    OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeReferenceIdsINTEL = 5772,
    OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeMotionVectorsINTEL = 5773,
    OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeDistortionsINTEL = 5774,
    OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeReferenceIdsINTEL = 5775,
    OpSubgroupAvcImeGetBorderReachedINTEL = 5776,
    OpSubgroupAvcImeGetTruncatedSearchIndicationINTEL = 5777,
    OpSubgroupAvcImeGetUnidirectionalEarlySearchTerminationINTEL = 5778,
    OpSubgroupAvcImeGetWeightingPatternMinimumMotionVectorINTEL = 5779,
    OpSubgroupAvcImeGetWeightingPatternMinimumDistortionINTEL = 5780,
    OpSubgroupAvcFmeInitializeINTEL = 5781,
    OpSubgroupAvcBmeInitializeINTEL = 5782,
    OpSubgroupAvcRefConvertToMcePayloadINTEL = 5783,
    OpSubgroupAvcRefSetBidirectionalMixDisableINTEL = 5784,
    OpSubgroupAvcRefSetBilinearFilterEnableINTEL = 5785,
    OpSubgroupAvcRefEvaluateWithSingleReferenceINTEL = 5786,
    OpSubgroupAvcRefEvaluateWithDualReferenceINTEL = 5787,
    OpSubgroupAvcRefEvaluateWithMultiReferenceINTEL = 5788,
    OpSubgroupAvcRefEvaluateWithMultiReferenceInterlacedINTEL = 5789,
    OpSubgroupAvcRefConvertToMceResultINTEL = 5790,
    OpSubgroupAvcSicInitializeINTEL = 5791,
    OpSubgroupAvcSicConfigureSkcINTEL = 5792,
    OpSubgroupAvcSicConfigureIpeLumaINTEL = 5793,
    OpSubgroupAvcSicConfigureIpeLumaChromaINTEL = 5794,
    OpSubgroupAvcSicGetMotionVectorMaskINTEL = 5795,
    OpSubgroupAvcSicConvertToMcePayloadINTEL = 5796,
    OpSubgroupAvcSicSetIntraLumaShapePenaltyINTEL = 5797,
    OpSubgroupAvcSicSetIntraLumaModeCostFunctionINTEL = 5798,
    OpSubgroupAvcSicSetIntraChromaModeCostFunctionINTEL = 5799,
    OpSubgroupAvcSicSetBilinearFilterEnableINTEL = 5800,
    OpSubgroupAvcSicSetSkcForwardTransformEnableINTEL = 5801,
    OpSubgroupAvcSicSetBlockBasedRawSkipSadINTEL = 5802,
    OpSubgroupAvcSicEvaluateIpeINTEL = 5803,
    OpSubgroupAvcSicEvaluateWithSingleReferenceINTEL = 5804,
    OpSubgroupAvcSicEvaluateWithDualReferenceINTEL = 5805,
    OpSubgroupAvcSicEvaluateWithMultiReferenceINTEL = 5806,
    OpSubgroupAvcSicEvaluateWithMultiReferenceInterlacedINTEL = 5807,
    OpSubgroupAvcSicConvertToMceResultINTEL = 5808,
    OpSubgroupAvcSicGetIpeLumaShapeINTEL = 5809,
    OpSubgroupAvcSicGetBestIpeLumaDistortionINTEL = 5810,
    OpSubgroupAvcSicGetBestIpeChromaDistortionINTEL = 5811,
    OpSubgroupAvcSicGetPackedIpeLumaModesINTEL = 5812,
    OpSubgroupAvcSicGetIpeChromaModeINTEL = 5813,
    OpSubgroupAvcSicGetPackedSkcLumaCountThresholdINTEL = 5814,
    OpSubgroupAvcSicGetPackedSkcLumaSumThresholdINTEL = 5815,
    OpSubgroupAvcSicGetInterRawSadsINTEL = 5816,
    OpVariableLengthArrayINTEL = 5818,
    OpSaveMemoryINTEL = 5819,
    OpRestoreMemoryINTEL = 5820,
    OpArbitraryFloatSinCosPiINTEL = 5840,
    OpArbitraryFloatCastINTEL = 5841,
    OpArbitraryFloatCastFromIntINTEL = 5842,
    OpArbitraryFloatCastToIntINTEL = 5843,
    OpArbitraryFloatAddINTEL = 5846,
    OpArbitraryFloatSubINTEL = 5847,
    OpArbitraryFloatMulINTEL = 5848,
    OpArbitraryFloatDivINTEL = 5849,
    OpArbitraryFloatGTINTEL = 5850,
    OpArbitraryFloatGEINTEL = 5851,
    OpArbitraryFloatLTINTEL = 5852,
    OpArbitraryFloatLEINTEL = 5853,
    OpArbitraryFloatEQINTEL = 5854,
    OpArbitraryFloatRecipINTEL = 5855,
    OpArbitraryFloatRSqrtINTEL = 5856,
    OpArbitraryFloatCbrtINTEL = 5857,
    OpArbitraryFloatHypotINTEL = 5858,
    OpArbitraryFloatSqrtINTEL = 5859,
    OpArbitraryFloatLogINTEL = 5860,
    OpArbitraryFloatLog2INTEL = 5861,
    OpArbitraryFloatLog10INTEL = 5862,
    OpArbitraryFloatLog1pINTEL = 5863,
    OpArbitraryFloatExpINTEL = 5864,
    OpArbitraryFloatExp2INTEL = 5865,
    OpArbitraryFloatExp10INTEL = 5866,
    OpArbitraryFloatExpm1INTEL = 5867,
    OpArbitraryFloatSinINTEL = 5868,
    OpArbitraryFloatCosINTEL = 5869,
    OpArbitraryFloatSinCosINTEL = 5870,
    OpArbitraryFloatSinPiINTEL = 5871,
    OpArbitraryFloatCosPiINTEL = 5872,
    OpArbitraryFloatASinINTEL = 5873,
    OpArbitraryFloatASinPiINTEL = 5874,
    OpArbitraryFloatACosINTEL = 5875,
    OpArbitraryFloatACosPiINTEL = 5876,
    OpArbitraryFloatATanINTEL = 5877,
    OpArbitraryFloatATanPiINTEL = 5878,
    OpArbitraryFloatATan2INTEL = 5879,
    OpArbitraryFloatPowINTEL = 5880,
    OpArbitraryFloatPowRINTEL = 5881,
    OpArbitraryFloatPowNINTEL = 5882,
    OpLoopControlINTEL = 5887,
    OpAliasDomainDeclINTEL = 5911,
    OpAliasScopeDeclINTEL = 5912,
    OpAliasScopeListDeclINTEL = 5913,
    OpFixedSqrtINTEL = 5923,
    OpFixedRecipINTEL = 5924,
    OpFixedRsqrtINTEL = 5925,
    OpFixedSinINTEL = 5926,
    OpFixedCosINTEL = 5927,
    OpFixedSinCosINTEL = 5928,
    OpFixedSinPiINTEL = 5929,
    OpFixedCosPiINTEL = 5930,
    OpFixedSinCosPiINTEL = 5931,
    OpFixedLogINTEL = 5932,
    OpFixedExpINTEL = 5933,
    OpPtrCastToCrossWorkgroupINTEL = 5934,
    OpCrossWorkgroupCastToPtrINTEL = 5938,
    OpReadPipeBlockingINTEL = 5946,
    OpWritePipeBlockingINTEL = 5947,
    OpFPGARegINTEL = 5949,
    OpRayQueryGetRayTMinKHR = 6016,
    OpRayQueryGetRayFlagsKHR = 6017,
    OpRayQueryGetIntersectionTKHR = 6018,
    OpRayQueryGetIntersectionInstanceCustomIndexKHR = 6019,
    OpRayQueryGetIntersectionInstanceIdKHR = 6020,
    OpRayQueryGetIntersectionInstanceShaderBindingTableRecordOffsetKHR = 6021,
    OpRayQueryGetIntersectionGeometryIndexKHR = 6022,
    OpRayQueryGetIntersectionPrimitiveIndexKHR = 6023,
    OpRayQueryGetIntersectionBarycentricsKHR = 6024,
    OpRayQueryGetIntersectionFrontFaceKHR = 6025,
    OpRayQueryGetIntersectionCandidateAABBOpaqueKHR = 6026,
    OpRayQueryGetIntersectionObjectRayDirectionKHR = 6027,
    OpRayQueryGetIntersectionObjectRayOriginKHR = 6028,
    OpRayQueryGetWorldRayDirectionKHR = 6029,
    OpRayQueryGetWorldRayOriginKHR = 6030,
    OpRayQueryGetIntersectionObjectToWorldKHR = 6031,
    OpRayQueryGetIntersectionWorldToObjectKHR = 6032,
    OpAtomicFAddEXT = 6035,
    OpTypeBufferSurfaceINTEL = 6086,
    OpTypeStructContinuedINTEL = 6090,
    OpConstantCompositeContinuedINTEL = 6091,
    OpSpecConstantCompositeContinuedINTEL = 6092,
    OpControlBarrierArriveINTEL = 6142,
    OpControlBarrierWaitINTEL = 6143,
    OpGroupIMulKHR = 6401,
    OpGroupFMulKHR = 6402,
    OpGroupBitwiseAndKHR = 6403,
    OpGroupBitwiseOrKHR = 6404,
    OpGroupBitwiseXorKHR = 6405,
    OpGroupLogicalAndKHR = 6406,
    OpGroupLogicalOrKHR = 6407,
    OpGroupLogicalXorKHR = 6408,
    OpMax = 0x7fffffff,
};

#ifdef SPV_ENABLE_UTILITY_CODE
#ifndef __cplusplus
#include <stdbool.h>
#endif
inline void HasResultAndType(Op opcode, bool *hasResult, bool *hasResultType) {
    *hasResult = *hasResultType = false;
    switch (opcode) {
    default: /* unknown opcode */ break;
    case OpNop: *hasResult = false; *hasResultType = false; break;
    case OpUndef: *hasResult = true; *hasResultType = true; break;
    case OpSourceContinued: *hasResult = false; *hasResultType = false; break;
    case OpSource: *hasResult = false; *hasResultType = false; break;
    case OpSourceExtension: *hasResult = false; *hasResultType = false; break;
    case OpName: *hasResult = false; *hasResultType = false; break;
    case OpMemberName: *hasResult = false; *hasResultType = false; break;
    case OpString: *hasResult = true; *hasResultType = false; break;
    case OpLine: *hasResult = false; *hasResultType = false; break;
    case OpExtension: *hasResult = false; *hasResultType = false; break;
    case OpExtInstImport: *hasResult = true; *hasResultType = false; break;
    case OpExtInst: *hasResult = true; *hasResultType = true; break;
    case OpMemoryModel: *hasResult = false; *hasResultType = false; break;
    case OpEntryPoint: *hasResult = false; *hasResultType = false; break;
    case OpExecutionMode: *hasResult = false; *hasResultType = false; break;
    case OpCapability: *hasResult = false; *hasResultType = false; break;
    case OpTypeVoid: *hasResult = true; *hasResultType = false; break;
    case OpTypeBool: *hasResult = true; *hasResultType = false; break;
    case OpTypeInt: *hasResult = true; *hasResultType = false; break;
    case OpTypeFloat: *hasResult = true; *hasResultType = false; break;
    case OpTypeVector: *hasResult = true; *hasResultType = false; break;
    case OpTypeMatrix: *hasResult = true; *hasResultType = false; break;
    case OpTypeImage: *hasResult = true; *hasResultType = false; break;
    case OpTypeSampler: *hasResult = true; *hasResultType = false; break;
    case OpTypeSampledImage: *hasResult = true; *hasResultType = false; break;
    case OpTypeArray: *hasResult = true; *hasResultType = false; break;
    case OpTypeRuntimeArray: *hasResult = true; *hasResultType = false; break;
    case OpTypeStruct: *hasResult = true; *hasResultType = false; break;
    case OpTypeOpaque: *hasResult = true; *hasResultType = false; break;
    case OpTypePointer: *hasResult = true; *hasResultType = false; break;
    case OpTypeFunction: *hasResult = true; *hasResultType = false; break;
    case OpTypeEvent: *hasResult = true; *hasResultType = false; break;
    case OpTypeDeviceEvent: *hasResult = true; *hasResultType = false; break;
    case OpTypeReserveId: *hasResult = true; *hasResultType = false; break;
    case OpTypeQueue: *hasResult = true; *hasResultType = false; break;
    case OpTypePipe: *hasResult = true; *hasResultType = false; break;
    case OpTypeForwardPointer: *hasResult = false; *hasResultType = false; break;
    case OpConstantTrue: *hasResult = true; *hasResultType = true; break;
    case OpConstantFalse: *hasResult = true; *hasResultType = true; break;
    case OpConstant: *hasResult = true; *hasResultType = true; break;
    case OpConstantComposite: *hasResult = true; *hasResultType = true; break;
    case OpConstantSampler: *hasResult = true; *hasResultType = true; break;
    case OpConstantNull: *hasResult = true; *hasResultType = true; break;
    case OpSpecConstantTrue: *hasResult = true; *hasResultType = true; break;
    case OpSpecConstantFalse: *hasResult = true; *hasResultType = true; break;
    case OpSpecConstant: *hasResult = true; *hasResultType = true; break;
    case OpSpecConstantComposite: *hasResult = true; *hasResultType = true; break;
    case OpSpecConstantOp: *hasResult = true; *hasResultType = true; break;
    case OpFunction: *hasResult = true; *hasResultType = true; break;
    case OpFunctionParameter: *hasResult = true; *hasResultType = true; break;
    case OpFunctionEnd: *hasResult = false; *hasResultType = false; break;
    case OpFunctionCall: *hasResult = true; *hasResultType = true; break;
    case OpVariable: *hasResult = true; *hasResultType = true; break;
    case OpImageTexelPointer: *hasResult = true; *hasResultType = true; break;
    case OpLoad: *hasResult = true; *hasResultType = true; break;
    case OpStore: *hasResult = false; *hasResultType = false; break;
    case OpCopyMemory: *hasResult = false; *hasResultType = false; break;
    case OpCopyMemorySized: *hasResult = false; *hasResultType = false; break;
    case OpAccessChain: *hasResult = true; *hasResultType = true; break;
    case OpInBoundsAccessChain: *hasResult = true; *hasResultType = true; break;
    case OpPtrAccessChain: *hasResult = true; *hasResultType = true; break;
    case OpArrayLength: *hasResult = true; *hasResultType = true; break;
    case OpGenericPtrMemSemantics: *hasResult = true; *hasResultType = true; break;
    case OpInBoundsPtrAccessChain: *hasResult = true; *hasResultType = true; break;
    case OpDecorate: *hasResult = false; *hasResultType = false; break;
    case OpMemberDecorate: *hasResult = false; *hasResultType = false; break;
    case OpDecorationGroup: *hasResult = true; *hasResultType = false; break;
    case OpGroupDecorate: *hasResult = false; *hasResultType = false; break;
    case OpGroupMemberDecorate: *hasResult = false; *hasResultType = false; break;
    case OpVectorExtractDynamic: *hasResult = true; *hasResultType = true; break;
    case OpVectorInsertDynamic: *hasResult = true; *hasResultType = true; break;
    case OpVectorShuffle: *hasResult = true; *hasResultType = true; break;
    case OpCompositeConstruct: *hasResult = true; *hasResultType = true; break;
    case OpCompositeExtract: *hasResult = true; *hasResultType = true; break;
    case OpCompositeInsert: *hasResult = true; *hasResultType = true; break;
    case OpCopyObject: *hasResult = true; *hasResultType = true; break;
    case OpTranspose: *hasResult = true; *hasResultType = true; break;
    case OpSampledImage: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleDrefImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleDrefExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleProjImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleProjExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleProjDrefImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSampleProjDrefExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageFetch: *hasResult = true; *hasResultType = true; break;
    case OpImageGather: *hasResult = true; *hasResultType = true; break;
    case OpImageDrefGather: *hasResult = true; *hasResultType = true; break;
    case OpImageRead: *hasResult = true; *hasResultType = true; break;
    case OpImageWrite: *hasResult = false; *hasResultType = false; break;
    case OpImage: *hasResult = true; *hasResultType = true; break;
    case OpImageQueryFormat: *hasResult = true; *hasResultType = true; break;
    case OpImageQueryOrder: *hasResult = true; *hasResultType = true; break;
    case OpImageQuerySizeLod: *hasResult = true; *hasResultType = true; break;
    case OpImageQuerySize: *hasResult = true; *hasResultType = true; break;
    case OpImageQueryLod: *hasResult = true; *hasResultType = true; break;
    case OpImageQueryLevels: *hasResult = true; *hasResultType = true; break;
    case OpImageQuerySamples: *hasResult = true; *hasResultType = true; break;
    case OpConvertFToU: *hasResult = true; *hasResultType = true; break;
    case OpConvertFToS: *hasResult = true; *hasResultType = true; break;
    case OpConvertSToF: *hasResult = true; *hasResultType = true; break;
    case OpConvertUToF: *hasResult = true; *hasResultType = true; break;
    case OpUConvert: *hasResult = true; *hasResultType = true; break;
    case OpSConvert: *hasResult = true; *hasResultType = true; break;
    case OpFConvert: *hasResult = true; *hasResultType = true; break;
    case OpQuantizeToF16: *hasResult = true; *hasResultType = true; break;
    case OpConvertPtrToU: *hasResult = true; *hasResultType = true; break;
    case OpSatConvertSToU: *hasResult = true; *hasResultType = true; break;
    case OpSatConvertUToS: *hasResult = true; *hasResultType = true; break;
    case OpConvertUToPtr: *hasResult = true; *hasResultType = true; break;
    case OpPtrCastToGeneric: *hasResult = true; *hasResultType = true; break;
    case OpGenericCastToPtr: *hasResult = true; *hasResultType = true; break;
    case OpGenericCastToPtrExplicit: *hasResult = true; *hasResultType = true; break;
    case OpBitcast: *hasResult = true; *hasResultType = true; break;
    case OpSNegate: *hasResult = true; *hasResultType = true; break;
    case OpFNegate: *hasResult = true; *hasResultType = true; break;
    case OpIAdd: *hasResult = true; *hasResultType = true; break;
    case OpFAdd: *hasResult = true; *hasResultType = true; break;
    case OpISub: *hasResult = true; *hasResultType = true; break;
    case OpFSub: *hasResult = true; *hasResultType = true; break;
    case OpIMul: *hasResult = true; *hasResultType = true; break;
    case OpFMul: *hasResult = true; *hasResultType = true; break;
    case OpUDiv: *hasResult = true; *hasResultType = true; break;
    case OpSDiv: *hasResult = true; *hasResultType = true; break;
    case OpFDiv: *hasResult = true; *hasResultType = true; break;
    case OpUMod: *hasResult = true; *hasResultType = true; break;
    case OpSRem: *hasResult = true; *hasResultType = true; break;
    case OpSMod: *hasResult = true; *hasResultType = true; break;
    case OpFRem: *hasResult = true; *hasResultType = true; break;
    case OpFMod: *hasResult = true; *hasResultType = true; break;
    case OpVectorTimesScalar: *hasResult = true; *hasResultType = true; break;
    case OpMatrixTimesScalar: *hasResult = true; *hasResultType = true; break;
    case OpVectorTimesMatrix: *hasResult = true; *hasResultType = true; break;
    case OpMatrixTimesVector: *hasResult = true; *hasResultType = true; break;
    case OpMatrixTimesMatrix: *hasResult = true; *hasResultType = true; break;
    case OpOuterProduct: *hasResult = true; *hasResultType = true; break;
    case OpDot: *hasResult = true; *hasResultType = true; break;
    case OpIAddCarry: *hasResult = true; *hasResultType = true; break;
    case OpISubBorrow: *hasResult = true; *hasResultType = true; break;
    case OpUMulExtended: *hasResult = true; *hasResultType = true; break;
    case OpSMulExtended: *hasResult = true; *hasResultType = true; break;
    case OpAny: *hasResult = true; *hasResultType = true; break;
    case OpAll: *hasResult = true; *hasResultType = true; break;
    case OpIsNan: *hasResult = true; *hasResultType = true; break;
    case OpIsInf: *hasResult = true; *hasResultType = true; break;
    case OpIsFinite: *hasResult = true; *hasResultType = true; break;
    case OpIsNormal: *hasResult = true; *hasResultType = true; break;
    case OpSignBitSet: *hasResult = true; *hasResultType = true; break;
    case OpLessOrGreater: *hasResult = true; *hasResultType = true; break;
    case OpOrdered: *hasResult = true; *hasResultType = true; break;
    case OpUnordered: *hasResult = true; *hasResultType = true; break;
    case OpLogicalEqual: *hasResult = true; *hasResultType = true; break;
    case OpLogicalNotEqual: *hasResult = true; *hasResultType = true; break;
    case OpLogicalOr: *hasResult = true; *hasResultType = true; break;
    case OpLogicalAnd: *hasResult = true; *hasResultType = true; break;
    case OpLogicalNot: *hasResult = true; *hasResultType = true; break;
    case OpSelect: *hasResult = true; *hasResultType = true; break;
    case OpIEqual: *hasResult = true; *hasResultType = true; break;
    case OpINotEqual: *hasResult = true; *hasResultType = true; break;
    case OpUGreaterThan: *hasResult = true; *hasResultType = true; break;
    case OpSGreaterThan: *hasResult = true; *hasResultType = true; break;
    case OpUGreaterThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpSGreaterThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpULessThan: *hasResult = true; *hasResultType = true; break;
    case OpSLessThan: *hasResult = true; *hasResultType = true; break;
    case OpULessThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpSLessThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpFOrdEqual: *hasResult = true; *hasResultType = true; break;
    case OpFUnordEqual: *hasResult = true; *hasResultType = true; break;
    case OpFOrdNotEqual: *hasResult = true; *hasResultType = true; break;
    case OpFUnordNotEqual: *hasResult = true; *hasResultType = true; break;
    case OpFOrdLessThan: *hasResult = true; *hasResultType = true; break;
    case OpFUnordLessThan: *hasResult = true; *hasResultType = true; break;
    case OpFOrdGreaterThan: *hasResult = true; *hasResultType = true; break;
    case OpFUnordGreaterThan: *hasResult = true; *hasResultType = true; break;
    case OpFOrdLessThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpFUnordLessThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpFOrdGreaterThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpFUnordGreaterThanEqual: *hasResult = true; *hasResultType = true; break;
    case OpShiftRightLogical: *hasResult = true; *hasResultType = true; break;
    case OpShiftRightArithmetic: *hasResult = true; *hasResultType = true; break;
    case OpShiftLeftLogical: *hasResult = true; *hasResultType = true; break;
    case OpBitwiseOr: *hasResult = true; *hasResultType = true; break;
    case OpBitwiseXor: *hasResult = true; *hasResultType = true; break;
    case OpBitwiseAnd: *hasResult = true; *hasResultType = true; break;
    case OpNot: *hasResult = true; *hasResultType = true; break;
    case OpBitFieldInsert: *hasResult = true; *hasResultType = true; break;
    case OpBitFieldSExtract: *hasResult = true; *hasResultType = true; break;
    case OpBitFieldUExtract: *hasResult = true; *hasResultType = true; break;
    case OpBitReverse: *hasResult = true; *hasResultType = true; break;
    case OpBitCount: *hasResult = true; *hasResultType = true; break;
    case OpDPdx: *hasResult = true; *hasResultType = true; break;
    case OpDPdy: *hasResult = true; *hasResultType = true; break;
    case OpFwidth: *hasResult = true; *hasResultType = true; break;
    case OpDPdxFine: *hasResult = true; *hasResultType = true; break;
    case OpDPdyFine: *hasResult = true; *hasResultType = true; break;
    case OpFwidthFine: *hasResult = true; *hasResultType = true; break;
    case OpDPdxCoarse: *hasResult = true; *hasResultType = true; break;
    case OpDPdyCoarse: *hasResult = true; *hasResultType = true; break;
    case OpFwidthCoarse: *hasResult = true; *hasResultType = true; break;
    case OpEmitVertex: *hasResult = false; *hasResultType = false; break;
    case OpEndPrimitive: *hasResult = false; *hasResultType = false; break;
    case OpEmitStreamVertex: *hasResult = false; *hasResultType = false; break;
    case OpEndStreamPrimitive: *hasResult = false; *hasResultType = false; break;
    case OpControlBarrier: *hasResult = false; *hasResultType = false; break;
    case OpMemoryBarrier: *hasResult = false; *hasResultType = false; break;
    case OpAtomicLoad: *hasResult = true; *hasResultType = true; break;
    case OpAtomicStore: *hasResult = false; *hasResultType = false; break;
    case OpAtomicExchange: *hasResult = true; *hasResultType = true; break;
    case OpAtomicCompareExchange: *hasResult = true; *hasResultType = true; break;
    case OpAtomicCompareExchangeWeak: *hasResult = true; *hasResultType = true; break;
    case OpAtomicIIncrement: *hasResult = true; *hasResultType = true; break;
    case OpAtomicIDecrement: *hasResult = true; *hasResultType = true; break;
    case OpAtomicIAdd: *hasResult = true; *hasResultType = true; break;
    case OpAtomicISub: *hasResult = true; *hasResultType = true; break;
    case OpAtomicSMin: *hasResult = true; *hasResultType = true; break;
    case OpAtomicUMin: *hasResult = true; *hasResultType = true; break;
    case OpAtomicSMax: *hasResult = true; *hasResultType = true; break;
    case OpAtomicUMax: *hasResult = true; *hasResultType = true; break;
    case OpAtomicAnd: *hasResult = true; *hasResultType = true; break;
    case OpAtomicOr: *hasResult = true; *hasResultType = true; break;
    case OpAtomicXor: *hasResult = true; *hasResultType = true; break;
    case OpPhi: *hasResult = true; *hasResultType = true; break;
    case OpLoopMerge: *hasResult = false; *hasResultType = false; break;
    case OpSelectionMerge: *hasResult = false; *hasResultType = false; break;
    case OpLabel: *hasResult = true; *hasResultType = false; break;
    case OpBranch: *hasResult = false; *hasResultType = false; break;
    case OpBranchConditional: *hasResult = false; *hasResultType = false; break;
    case OpSwitch: *hasResult = false; *hasResultType = false; break;
    case OpKill: *hasResult = false; *hasResultType = false; break;
    case OpReturn: *hasResult = false; *hasResultType = false; break;
    case OpReturnValue: *hasResult = false; *hasResultType = false; break;
    case OpUnreachable: *hasResult = false; *hasResultType = false; break;
    case OpLifetimeStart: *hasResult = false; *hasResultType = false; break;
    case OpLifetimeStop: *hasResult = false; *hasResultType = false; break;
    case OpGroupAsyncCopy: *hasResult = true; *hasResultType = true; break;
    case OpGroupWaitEvents: *hasResult = false; *hasResultType = false; break;
    case OpGroupAll: *hasResult = true; *hasResultType = true; break;
    case OpGroupAny: *hasResult = true; *hasResultType = true; break;
    case OpGroupBroadcast: *hasResult = true; *hasResultType = true; break;
    case OpGroupIAdd: *hasResult = true; *hasResultType = true; break;
    case OpGroupFAdd: *hasResult = true; *hasResultType = true; break;
    case OpGroupFMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupUMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupSMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupFMax: *hasResult = true; *hasResultType = true; break;
    case OpGroupUMax: *hasResult = true; *hasResultType = true; break;
    case OpGroupSMax: *hasResult = true; *hasResultType = true; break;
    case OpReadPipe: *hasResult = true; *hasResultType = true; break;
    case OpWritePipe: *hasResult = true; *hasResultType = true; break;
    case OpReservedReadPipe: *hasResult = true; *hasResultType = true; break;
    case OpReservedWritePipe: *hasResult = true; *hasResultType = true; break;
    case OpReserveReadPipePackets: *hasResult = true; *hasResultType = true; break;
    case OpReserveWritePipePackets: *hasResult = true; *hasResultType = true; break;
    case OpCommitReadPipe: *hasResult = false; *hasResultType = false; break;
    case OpCommitWritePipe: *hasResult = false; *hasResultType = false; break;
    case OpIsValidReserveId: *hasResult = true; *hasResultType = true; break;
    case OpGetNumPipePackets: *hasResult = true; *hasResultType = true; break;
    case OpGetMaxPipePackets: *hasResult = true; *hasResultType = true; break;
    case OpGroupReserveReadPipePackets: *hasResult = true; *hasResultType = true; break;
    case OpGroupReserveWritePipePackets: *hasResult = true; *hasResultType = true; break;
    case OpGroupCommitReadPipe: *hasResult = false; *hasResultType = false; break;
    case OpGroupCommitWritePipe: *hasResult = false; *hasResultType = false; break;
    case OpEnqueueMarker: *hasResult = true; *hasResultType = true; break;
    case OpEnqueueKernel: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelNDrangeSubGroupCount: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelNDrangeMaxSubGroupSize: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelWorkGroupSize: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelPreferredWorkGroupSizeMultiple: *hasResult = true; *hasResultType = true; break;
    case OpRetainEvent: *hasResult = false; *hasResultType = false; break;
    case OpReleaseEvent: *hasResult = false; *hasResultType = false; break;
    case OpCreateUserEvent: *hasResult = true; *hasResultType = true; break;
    case OpIsValidEvent: *hasResult = true; *hasResultType = true; break;
    case OpSetUserEventStatus: *hasResult = false; *hasResultType = false; break;
    case OpCaptureEventProfilingInfo: *hasResult = false; *hasResultType = false; break;
    case OpGetDefaultQueue: *hasResult = true; *hasResultType = true; break;
    case OpBuildNDRange: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleDrefImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleDrefExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleProjImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleProjExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleProjDrefImplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseSampleProjDrefExplicitLod: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseFetch: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseGather: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseDrefGather: *hasResult = true; *hasResultType = true; break;
    case OpImageSparseTexelsResident: *hasResult = true; *hasResultType = true; break;
    case OpNoLine: *hasResult = false; *hasResultType = false; break;
    case OpAtomicFlagTestAndSet: *hasResult = true; *hasResultType = true; break;
    case OpAtomicFlagClear: *hasResult = false; *hasResultType = false; break;
    case OpImageSparseRead: *hasResult = true; *hasResultType = true; break;
    case OpSizeOf: *hasResult = true; *hasResultType = true; break;
    case OpTypePipeStorage: *hasResult = true; *hasResultType = false; break;
    case OpConstantPipeStorage: *hasResult = true; *hasResultType = true; break;
    case OpCreatePipeFromPipeStorage: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelLocalSizeForSubgroupCount: *hasResult = true; *hasResultType = true; break;
    case OpGetKernelMaxNumSubgroups: *hasResult = true; *hasResultType = true; break;
    case OpTypeNamedBarrier: *hasResult = true; *hasResultType = false; break;
    case OpNamedBarrierInitialize: *hasResult = true; *hasResultType = true; break;
    case OpMemoryNamedBarrier: *hasResult = false; *hasResultType = false; break;
    case OpModuleProcessed: *hasResult = false; *hasResultType = false; break;
    case OpExecutionModeId: *hasResult = false; *hasResultType = false; break;
    case OpDecorateId: *hasResult = false; *hasResultType = false; break;
    case OpGroupNonUniformElect: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformAll: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformAny: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformAllEqual: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBroadcast: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBroadcastFirst: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBallot: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformInverseBallot: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBallotBitExtract: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBallotBitCount: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBallotFindLSB: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBallotFindMSB: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformShuffle: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformShuffleXor: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformShuffleUp: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformShuffleDown: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformIAdd: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformFAdd: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformIMul: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformFMul: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformSMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformUMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformFMin: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformSMax: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformUMax: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformFMax: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBitwiseAnd: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBitwiseOr: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformBitwiseXor: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformLogicalAnd: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformLogicalOr: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformLogicalXor: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformQuadBroadcast: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformQuadSwap: *hasResult = true; *hasResultType = true; break;
    case OpCopyLogical: *hasResult = true; *hasResultType = true; break;
    case OpPtrEqual: *hasResult = true; *hasResultType = true; break;
    case OpPtrNotEqual: *hasResult = true; *hasResultType = true; break;
    case OpPtrDiff: *hasResult = true; *hasResultType = true; break;
    case OpColorAttachmentReadEXT: *hasResult = true; *hasResultType = true; break;
    case OpDepthAttachmentReadEXT: *hasResult = true; *hasResultType = true; break;
    case OpStencilAttachmentReadEXT: *hasResult = true; *hasResultType = true; break;
    case OpTerminateInvocation: *hasResult = false; *hasResultType = false; break;
    case OpSubgroupBallotKHR: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupFirstInvocationKHR: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAllKHR: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAnyKHR: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAllEqualKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupNonUniformRotateKHR: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupReadInvocationKHR: *hasResult = true; *hasResultType = true; break;
    case OpTraceRayKHR: *hasResult = false; *hasResultType = false; break;
    case OpExecuteCallableKHR: *hasResult = false; *hasResultType = false; break;
    case OpConvertUToAccelerationStructureKHR: *hasResult = true; *hasResultType = true; break;
    case OpIgnoreIntersectionKHR: *hasResult = false; *hasResultType = false; break;
    case OpTerminateRayKHR: *hasResult = false; *hasResultType = false; break;
    case OpSDot: *hasResult = true; *hasResultType = true; break;
    case OpUDot: *hasResult = true; *hasResultType = true; break;
    case OpSUDot: *hasResult = true; *hasResultType = true; break;
    case OpSDotAccSat: *hasResult = true; *hasResultType = true; break;
    case OpUDotAccSat: *hasResult = true; *hasResultType = true; break;
    case OpSUDotAccSat: *hasResult = true; *hasResultType = true; break;
    case OpTypeCooperativeMatrixKHR: *hasResult = true; *hasResultType = false; break;
    case OpCooperativeMatrixLoadKHR: *hasResult = true; *hasResultType = true; break;
    case OpCooperativeMatrixStoreKHR: *hasResult = false; *hasResultType = false; break;
    case OpCooperativeMatrixMulAddKHR: *hasResult = true; *hasResultType = true; break;
    case OpCooperativeMatrixLengthKHR: *hasResult = true; *hasResultType = true; break;
    case OpTypeRayQueryKHR: *hasResult = true; *hasResultType = false; break;
    case OpRayQueryInitializeKHR: *hasResult = false; *hasResultType = false; break;
    case OpRayQueryTerminateKHR: *hasResult = false; *hasResultType = false; break;
    case OpRayQueryGenerateIntersectionKHR: *hasResult = false; *hasResultType = false; break;
    case OpRayQueryConfirmIntersectionKHR: *hasResult = false; *hasResultType = false; break;
    case OpRayQueryProceedKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionTypeKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupIAddNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupFAddNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupFMinNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupUMinNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupSMinNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupFMaxNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupUMaxNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpGroupSMaxNonUniformAMD: *hasResult = true; *hasResultType = true; break;
    case OpFragmentMaskFetchAMD: *hasResult = true; *hasResultType = true; break;
    case OpFragmentFetchAMD: *hasResult = true; *hasResultType = true; break;
    case OpReadClockKHR: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectRecordHitMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectRecordHitWithIndexMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectRecordMissMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectGetWorldToObjectNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetObjectToWorldNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetObjectRayDirectionNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetObjectRayOriginNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectTraceRayMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectGetShaderRecordBufferHandleNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetShaderBindingTableRecordIndexNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectRecordEmptyNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectTraceRayNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectRecordHitNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectRecordHitWithIndexNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectRecordMissNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectExecuteShaderNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectGetCurrentTimeNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetAttributesNV: *hasResult = false; *hasResultType = false; break;
    case OpHitObjectGetHitKindNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetPrimitiveIndexNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetGeometryIndexNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetInstanceIdNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetInstanceCustomIndexNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetWorldRayDirectionNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetWorldRayOriginNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetRayTMaxNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectGetRayTMinNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectIsEmptyNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectIsHitNV: *hasResult = true; *hasResultType = true; break;
    case OpHitObjectIsMissNV: *hasResult = true; *hasResultType = true; break;
    case OpReorderThreadWithHitObjectNV: *hasResult = false; *hasResultType = false; break;
    case OpReorderThreadWithHintNV: *hasResult = false; *hasResultType = false; break;
    case OpTypeHitObjectNV: *hasResult = true; *hasResultType = false; break;
    case OpImageSampleFootprintNV: *hasResult = true; *hasResultType = true; break;
    case OpEmitMeshTasksEXT: *hasResult = false; *hasResultType = false; break;
    case OpSetMeshOutputsEXT: *hasResult = false; *hasResultType = false; break;
    case OpGroupNonUniformPartitionNV: *hasResult = true; *hasResultType = true; break;
    case OpWritePackedPrimitiveIndices4x8NV: *hasResult = false; *hasResultType = false; break;
    case OpReportIntersectionNV: *hasResult = true; *hasResultType = true; break;
    case OpIgnoreIntersectionNV: *hasResult = false; *hasResultType = false; break;
    case OpTerminateRayNV: *hasResult = false; *hasResultType = false; break;
    case OpTraceNV: *hasResult = false; *hasResultType = false; break;
    case OpTraceMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpTraceRayMotionNV: *hasResult = false; *hasResultType = false; break;
    case OpRayQueryGetIntersectionTriangleVertexPositionsKHR: *hasResult = true; *hasResultType = true; break;
    case OpTypeAccelerationStructureNV: *hasResult = true; *hasResultType = false; break;
    case OpExecuteCallableNV: *hasResult = false; *hasResultType = false; break;
    case OpTypeCooperativeMatrixNV: *hasResult = true; *hasResultType = false; break;
    case OpCooperativeMatrixLoadNV: *hasResult = true; *hasResultType = true; break;
    case OpCooperativeMatrixStoreNV: *hasResult = false; *hasResultType = false; break;
    case OpCooperativeMatrixMulAddNV: *hasResult = true; *hasResultType = true; break;
    case OpCooperativeMatrixLengthNV: *hasResult = true; *hasResultType = true; break;
    case OpBeginInvocationInterlockEXT: *hasResult = false; *hasResultType = false; break;
    case OpEndInvocationInterlockEXT: *hasResult = false; *hasResultType = false; break;
    case OpDemoteToHelperInvocation: *hasResult = false; *hasResultType = false; break;
    case OpIsHelperInvocationEXT: *hasResult = true; *hasResultType = true; break;
    case OpConvertUToImageNV: *hasResult = true; *hasResultType = true; break;
    case OpConvertUToSamplerNV: *hasResult = true; *hasResultType = true; break;
    case OpConvertImageToUNV: *hasResult = true; *hasResultType = true; break;
    case OpConvertSamplerToUNV: *hasResult = true; *hasResultType = true; break;
    case OpConvertUToSampledImageNV: *hasResult = true; *hasResultType = true; break;
    case OpConvertSampledImageToUNV: *hasResult = true; *hasResultType = true; break;
    case OpSamplerImageAddressingModeNV: *hasResult = false; *hasResultType = false; break;
    case OpSubgroupShuffleINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupShuffleDownINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupShuffleUpINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupShuffleXorINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupBlockReadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupBlockWriteINTEL: *hasResult = false; *hasResultType = false; break;
    case OpSubgroupImageBlockReadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupImageBlockWriteINTEL: *hasResult = false; *hasResultType = false; break;
    case OpSubgroupImageMediaBlockReadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupImageMediaBlockWriteINTEL: *hasResult = false; *hasResultType = false; break;
    case OpUCountLeadingZerosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpUCountTrailingZerosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAbsISubINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAbsUSubINTEL: *hasResult = true; *hasResultType = true; break;
    case OpIAddSatINTEL: *hasResult = true; *hasResultType = true; break;
    case OpUAddSatINTEL: *hasResult = true; *hasResultType = true; break;
    case OpIAverageINTEL: *hasResult = true; *hasResultType = true; break;
    case OpUAverageINTEL: *hasResult = true; *hasResultType = true; break;
    case OpIAverageRoundedINTEL: *hasResult = true; *hasResultType = true; break;
    case OpUAverageRoundedINTEL: *hasResult = true; *hasResultType = true; break;
    case OpISubSatINTEL: *hasResult = true; *hasResultType = true; break;
    case OpUSubSatINTEL: *hasResult = true; *hasResultType = true; break;
    case OpIMul32x16INTEL: *hasResult = true; *hasResultType = true; break;
    case OpUMul32x16INTEL: *hasResult = true; *hasResultType = true; break;
    case OpConstantFunctionPointerINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFunctionPointerCallINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAsmTargetINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAsmINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAsmCallINTEL: *hasResult = true; *hasResultType = true; break;
    case OpAtomicFMinEXT: *hasResult = true; *hasResultType = true; break;
    case OpAtomicFMaxEXT: *hasResult = true; *hasResultType = true; break;
    case OpAssumeTrueKHR: *hasResult = false; *hasResultType = false; break;
    case OpExpectKHR: *hasResult = true; *hasResultType = true; break;
    case OpDecorateString: *hasResult = false; *hasResultType = false; break;
    case OpMemberDecorateString: *hasResult = false; *hasResultType = false; break;
    case OpVmeImageINTEL: *hasResult = true; *hasResultType = true; break;
    case OpTypeVmeImageINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImePayloadINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcRefPayloadINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcSicPayloadINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcMcePayloadINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcMceResultINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImeResultINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImeResultSingleReferenceStreamoutINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImeResultDualReferenceStreamoutINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImeSingleReferenceStreaminINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcImeDualReferenceStreaminINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcRefResultINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeAvcSicResultINTEL: *hasResult = true; *hasResultType = false; break;
    case OpSubgroupAvcMceGetDefaultInterBaseMultiReferencePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetInterBaseMultiReferencePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultInterShapePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetInterShapePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultInterDirectionPenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetInterDirectionPenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultIntraLumaShapePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultInterMotionVectorCostTableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultHighPenaltyCostTableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultMediumPenaltyCostTableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultLowPenaltyCostTableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetMotionVectorCostFunctionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultIntraLumaModePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultNonDcLumaIntraPenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetDefaultIntraChromaModeBasePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetAcOnlyHaarINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetSourceInterlacedFieldPolarityINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetSingleReferenceInterlacedFieldPolarityINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceSetDualReferenceInterlacedFieldPolaritiesINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToImePayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToImeResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToRefPayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToRefResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToSicPayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceConvertToSicResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetMotionVectorsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterDistortionsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetBestInterDistortionsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterMajorShapeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterMinorShapeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterDirectionsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterMotionVectorCountINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterReferenceIdsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcMceGetInterReferenceInterlacedFieldPolaritiesINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeInitializeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetSingleReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetDualReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeRefWindowSizeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeAdjustRefOffsetINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeConvertToMcePayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetMaxMotionVectorCountINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetUnidirectionalMixDisableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetEarlySearchTerminationThresholdINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeSetWeightedSadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithSingleReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithDualReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithSingleReferenceStreaminINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithDualReferenceStreaminINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithSingleReferenceStreamoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithDualReferenceStreamoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithSingleReferenceStreaminoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeEvaluateWithDualReferenceStreaminoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeConvertToMceResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetSingleReferenceStreaminINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetDualReferenceStreaminINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeStripSingleReferenceStreamoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeStripDualReferenceStreamoutINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeMotionVectorsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeDistortionsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutSingleReferenceMajorShapeReferenceIdsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeMotionVectorsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeDistortionsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetStreamoutDualReferenceMajorShapeReferenceIdsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetBorderReachedINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetTruncatedSearchIndicationINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetUnidirectionalEarlySearchTerminationINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetWeightingPatternMinimumMotionVectorINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcImeGetWeightingPatternMinimumDistortionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcFmeInitializeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcBmeInitializeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefConvertToMcePayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefSetBidirectionalMixDisableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefSetBilinearFilterEnableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefEvaluateWithSingleReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefEvaluateWithDualReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefEvaluateWithMultiReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefEvaluateWithMultiReferenceInterlacedINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcRefConvertToMceResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicInitializeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicConfigureSkcINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicConfigureIpeLumaINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicConfigureIpeLumaChromaINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetMotionVectorMaskINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicConvertToMcePayloadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetIntraLumaShapePenaltyINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetIntraLumaModeCostFunctionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetIntraChromaModeCostFunctionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetBilinearFilterEnableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetSkcForwardTransformEnableINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicSetBlockBasedRawSkipSadINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicEvaluateIpeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicEvaluateWithSingleReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicEvaluateWithDualReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicEvaluateWithMultiReferenceINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicEvaluateWithMultiReferenceInterlacedINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicConvertToMceResultINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetIpeLumaShapeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetBestIpeLumaDistortionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetBestIpeChromaDistortionINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetPackedIpeLumaModesINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetIpeChromaModeINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetPackedSkcLumaCountThresholdINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetPackedSkcLumaSumThresholdINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSubgroupAvcSicGetInterRawSadsINTEL: *hasResult = true; *hasResultType = true; break;
    case OpVariableLengthArrayINTEL: *hasResult = true; *hasResultType = true; break;
    case OpSaveMemoryINTEL: *hasResult = true; *hasResultType = true; break;
    case OpRestoreMemoryINTEL: *hasResult = false; *hasResultType = false; break;
    case OpArbitraryFloatSinCosPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCastINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCastFromIntINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCastToIntINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatAddINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatSubINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatMulINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatDivINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatGTINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatGEINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLTINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLEINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatEQINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatRecipINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatRSqrtINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCbrtINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatHypotINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatSqrtINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLogINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLog2INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLog10INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatLog1pINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatExpINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatExp2INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatExp10INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatExpm1INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatSinINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatSinCosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatSinPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatCosPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatASinINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatASinPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatACosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatACosPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatATanINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatATanPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatATan2INTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatPowINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatPowRINTEL: *hasResult = true; *hasResultType = true; break;
    case OpArbitraryFloatPowNINTEL: *hasResult = true; *hasResultType = true; break;
    case OpLoopControlINTEL: *hasResult = false; *hasResultType = false; break;
    case OpAliasDomainDeclINTEL: *hasResult = true; *hasResultType = false; break;
    case OpAliasScopeDeclINTEL: *hasResult = true; *hasResultType = false; break;
    case OpAliasScopeListDeclINTEL: *hasResult = true; *hasResultType = false; break;
    case OpFixedSqrtINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedRecipINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedRsqrtINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedSinINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedCosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedSinCosINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedSinPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedCosPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedSinCosPiINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedLogINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFixedExpINTEL: *hasResult = true; *hasResultType = true; break;
    case OpPtrCastToCrossWorkgroupINTEL: *hasResult = true; *hasResultType = true; break;
    case OpCrossWorkgroupCastToPtrINTEL: *hasResult = true; *hasResultType = true; break;
    case OpReadPipeBlockingINTEL: *hasResult = true; *hasResultType = true; break;
    case OpWritePipeBlockingINTEL: *hasResult = true; *hasResultType = true; break;
    case OpFPGARegINTEL: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetRayTMinKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetRayFlagsKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionTKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionInstanceCustomIndexKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionInstanceIdKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionInstanceShaderBindingTableRecordOffsetKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionGeometryIndexKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionPrimitiveIndexKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionBarycentricsKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionFrontFaceKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionCandidateAABBOpaqueKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionObjectRayDirectionKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionObjectRayOriginKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetWorldRayDirectionKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetWorldRayOriginKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionObjectToWorldKHR: *hasResult = true; *hasResultType = true; break;
    case OpRayQueryGetIntersectionWorldToObjectKHR: *hasResult = true; *hasResultType = true; break;
    case OpAtomicFAddEXT: *hasResult = true; *hasResultType = true; break;
    case OpTypeBufferSurfaceINTEL: *hasResult = true; *hasResultType = false; break;
    case OpTypeStructContinuedINTEL: *hasResult = false; *hasResultType = false; break;
    case OpConstantCompositeContinuedINTEL: *hasResult = false; *hasResultType = false; break;
    case OpSpecConstantCompositeContinuedINTEL: *hasResult = false; *hasResultType = false; break;
    case OpControlBarrierArriveINTEL: *hasResult = false; *hasResultType = false; break;
    case OpControlBarrierWaitINTEL: *hasResult = false; *hasResultType = false; break;
    case OpGroupIMulKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupFMulKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupBitwiseAndKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupBitwiseOrKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupBitwiseXorKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupLogicalAndKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupLogicalOrKHR: *hasResult = true; *hasResultType = true; break;
    case OpGroupLogicalXorKHR: *hasResult = true; *hasResultType = true; break;
    }
}
#endif /* SPV_ENABLE_UTILITY_CODE */

// Overload bitwise operators for mask bit combining

inline ImageOperandsMask operator|(ImageOperandsMask a, ImageOperandsMask b) { return ImageOperandsMask(unsigned(a) | unsigned(b)); }
inline ImageOperandsMask operator&(ImageOperandsMask a, ImageOperandsMask b) { return ImageOperandsMask(unsigned(a) & unsigned(b)); }
inline ImageOperandsMask operator^(ImageOperandsMask a, ImageOperandsMask b) { return ImageOperandsMask(unsigned(a) ^ unsigned(b)); }
inline ImageOperandsMask operator~(ImageOperandsMask a) { return ImageOperandsMask(~unsigned(a)); }
inline FPFastMathModeMask operator|(FPFastMathModeMask a, FPFastMathModeMask b) { return FPFastMathModeMask(unsigned(a) | unsigned(b)); }
inline FPFastMathModeMask operator&(FPFastMathModeMask a, FPFastMathModeMask b) { return FPFastMathModeMask(unsigned(a) & unsigned(b)); }
inline FPFastMathModeMask operator^(FPFastMathModeMask a, FPFastMathModeMask b) { return FPFastMathModeMask(unsigned(a) ^ unsigned(b)); }
inline FPFastMathModeMask operator~(FPFastMathModeMask a) { return FPFastMathModeMask(~unsigned(a)); }
inline SelectionControlMask operator|(SelectionControlMask a, SelectionControlMask b) { return SelectionControlMask(unsigned(a) | unsigned(b)); }
inline SelectionControlMask operator&(SelectionControlMask a, SelectionControlMask b) { return SelectionControlMask(unsigned(a) & unsigned(b)); }
inline SelectionControlMask operator^(SelectionControlMask a, SelectionControlMask b) { return SelectionControlMask(unsigned(a) ^ unsigned(b)); }
inline SelectionControlMask operator~(SelectionControlMask a) { return SelectionControlMask(~unsigned(a)); }
inline LoopControlMask operator|(LoopControlMask a, LoopControlMask b) { return LoopControlMask(unsigned(a) | unsigned(b)); }
inline LoopControlMask operator&(LoopControlMask a, LoopControlMask b) { return LoopControlMask(unsigned(a) & unsigned(b)); }
inline LoopControlMask operator^(LoopControlMask a, LoopControlMask b) { return LoopControlMask(unsigned(a) ^ unsigned(b)); }
inline LoopControlMask operator~(LoopControlMask a) { return LoopControlMask(~unsigned(a)); }
inline FunctionControlMask operator|(FunctionControlMask a, FunctionControlMask b) { return FunctionControlMask(unsigned(a) | unsigned(b)); }
inline FunctionControlMask operator&(FunctionControlMask a, FunctionControlMask b) { return FunctionControlMask(unsigned(a) & unsigned(b)); }
inline FunctionControlMask operator^(FunctionControlMask a, FunctionControlMask b) { return FunctionControlMask(unsigned(a) ^ unsigned(b)); }
inline FunctionControlMask operator~(FunctionControlMask a) { return FunctionControlMask(~unsigned(a)); }
inline MemorySemanticsMask operator|(MemorySemanticsMask a, MemorySemanticsMask b) { return MemorySemanticsMask(unsigned(a) | unsigned(b)); }
inline MemorySemanticsMask operator&(MemorySemanticsMask a, MemorySemanticsMask b) { return MemorySemanticsMask(unsigned(a) & unsigned(b)); }
inline MemorySemanticsMask operator^(MemorySemanticsMask a, MemorySemanticsMask b) { return MemorySemanticsMask(unsigned(a) ^ unsigned(b)); }
inline MemorySemanticsMask operator~(MemorySemanticsMask a) { return MemorySemanticsMask(~unsigned(a)); }
inline MemoryAccessMask operator|(MemoryAccessMask a, MemoryAccessMask b) { return MemoryAccessMask(unsigned(a) | unsigned(b)); }
inline MemoryAccessMask operator&(MemoryAccessMask a, MemoryAccessMask b) { return MemoryAccessMask(unsigned(a) & unsigned(b)); }
inline MemoryAccessMask operator^(MemoryAccessMask a, MemoryAccessMask b) { return MemoryAccessMask(unsigned(a) ^ unsigned(b)); }
inline MemoryAccessMask operator~(MemoryAccessMask a) { return MemoryAccessMask(~unsigned(a)); }
inline KernelProfilingInfoMask operator|(KernelProfilingInfoMask a, KernelProfilingInfoMask b) { return KernelProfilingInfoMask(unsigned(a) | unsigned(b)); }
inline KernelProfilingInfoMask operator&(KernelProfilingInfoMask a, KernelProfilingInfoMask b) { return KernelProfilingInfoMask(unsigned(a) & unsigned(b)); }
inline KernelProfilingInfoMask operator^(KernelProfilingInfoMask a, KernelProfilingInfoMask b) { return KernelProfilingInfoMask(unsigned(a) ^ unsigned(b)); }
inline KernelProfilingInfoMask operator~(KernelProfilingInfoMask a) { return KernelProfilingInfoMask(~unsigned(a)); }
inline RayFlagsMask operator|(RayFlagsMask a, RayFlagsMask b) { return RayFlagsMask(unsigned(a) | unsigned(b)); }
inline RayFlagsMask operator&(RayFlagsMask a, RayFlagsMask b) { return RayFlagsMask(unsigned(a) & unsigned(b)); }
inline RayFlagsMask operator^(RayFlagsMask a, RayFlagsMask b) { return RayFlagsMask(unsigned(a) ^ unsigned(b)); }
inline RayFlagsMask operator~(RayFlagsMask a) { return RayFlagsMask(~unsigned(a)); }
inline FragmentShadingRateMask operator|(FragmentShadingRateMask a, FragmentShadingRateMask b) { return FragmentShadingRateMask(unsigned(a) | unsigned(b)); }
inline FragmentShadingRateMask operator&(FragmentShadingRateMask a, FragmentShadingRateMask b) { return FragmentShadingRateMask(unsigned(a) & unsigned(b)); }
inline FragmentShadingRateMask operator^(FragmentShadingRateMask a, FragmentShadingRateMask b) { return FragmentShadingRateMask(unsigned(a) ^ unsigned(b)); }
inline FragmentShadingRateMask operator~(FragmentShadingRateMask a) { return FragmentShadingRateMask(~unsigned(a)); }
inline CooperativeMatrixOperandsMask operator|(CooperativeMatrixOperandsMask a, CooperativeMatrixOperandsMask b) { return CooperativeMatrixOperandsMask(unsigned(a) | unsigned(b)); }
inline CooperativeMatrixOperandsMask operator&(CooperativeMatrixOperandsMask a, CooperativeMatrixOperandsMask b) { return CooperativeMatrixOperandsMask(unsigned(a) & unsigned(b)); }
inline CooperativeMatrixOperandsMask operator^(CooperativeMatrixOperandsMask a, CooperativeMatrixOperandsMask b) { return CooperativeMatrixOperandsMask(unsigned(a) ^ unsigned(b)); }
inline CooperativeMatrixOperandsMask operator~(CooperativeMatrixOperandsMask a) { return CooperativeMatrixOperandsMask(~unsigned(a)); }

}  // end namespace spv

#endif  // #ifndef spirv_HPP

