package com.mitdd.behaviorguidance

import android.content.Context
import android.content.res.AssetManager

/**
 * FileName: BehaviorGuidance
 * Author by lilin,Date on 2024/10/21 14:35
 * PS: Not easy to write code, please indicate.
 */
class BehaviorGuidance {

    companion object {

        private val TAG = BehaviorGuidance::class.java.simpleName

        private const val NO_NATIVE_OBJ = 0L

        init {
            System.loadLibrary("BehaviorGuidance")
        }
    }

    @Volatile
    private var mNativeObj: Long = NO_NATIVE_OBJ

    /**
     * 实例初始化
     */
    fun initialize(context: Context){
        mNativeObj = nativeCreateObject(context.assets)
    }

    /**
     * 图像数据处理
     * @param inputImage 图像数据
     * @param width 宽
     * @param height 高
     * @param rotationDegrees 选择角度
     * @param fieldOfView 相机广角
     */
    fun frameProcess(inputImage: ByteArray,
                     width: Int,
                     height: Int,
                     rotationDegrees:Int,
                     fieldOfView:Float): HashMap<String, Any>?{
        return if (checkIsInit()){
            nativeFrameProcess(mNativeObj, inputImage, width, height, rotationDegrees, fieldOfView)
        }else{
            null
        }
    }

    /**
     * 释放
     */
    fun release() {
        nativeDestroyObject(mNativeObj)
        mNativeObj = NO_NATIVE_OBJ
    }

    /**
     * 检查是否初始化
     */
    fun checkIsInit():Boolean{
        return mNativeObj != NO_NATIVE_OBJ
    }

    private external fun nativeCreateObject(assetManager:AssetManager): Long

    private external fun nativeFrameProcess(thiz: Long,
                                            inputImage: ByteArray,
                                            width: Int,
                                            height: Int,
                                            rotationDegrees:Int,
                                            fieldOfView:Float): HashMap<String, Any>

    private external fun nativeDestroyObject(thiz: Long)
}