package com.mitdd.behaviorguidance

import android.content.Context

/**
 * FileName: BehaviorGuidanceManager
 * Author by lilin,Date on 2024/10/21 15:03
 * PS: Not easy to write code, please indicate.
 */
object BehaviorGuidanceManager {

    private val behaviorGuidance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        BehaviorGuidance()
    }

    /**
     * 实例初始化
     */
    fun initialize(context: Context){
        behaviorGuidance.initialize(context)
    }

    /**
     * 图像数据处理
     * @param inputImage 图像数据
     * @param width 宽
     * @param height 高
     * @param rotationDegrees 选择角度
     * @param fieldOfView 相机广角
     */
    fun frameProcess(inputImage: ByteArray,
                     width: Int,
                     height: Int,
                     rotationDegrees:Int,
                     fieldOfView:Float): HashMap<String, Any>?{
        return behaviorGuidance.frameProcess(inputImage, width, height, rotationDegrees, fieldOfView)
    }

    /**
     * 释放
     */
    fun release() {
        behaviorGuidance.release()
    }

    /**
     * 检查是否初始化
     */
    fun checkIsInit():Boolean{
        return behaviorGuidance.checkIsInit()
    }

}