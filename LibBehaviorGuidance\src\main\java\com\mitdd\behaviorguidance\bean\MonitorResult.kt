package com.mitdd.behaviorguidance.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * FileName: MonitorResult
 * Author by lilin,Date on 2024/10/22 10:11
 * PS: Not easy to write code, please indicate.
 */

/**
 * 监测结果
 */
@Parcelize
data class MonitorResult(
    //是否高低肩(label=true表示高低肩)
    @SerializedName("body_res") var bodyRes: MonitorScore? = null,
    //是否歪头(label=true表示歪头)
    @SerializedName("head_horizontal_res") var headHorizontalRes: MonitorScore? = null,
    //是否低头(label=true表示低头)
    @SerializedName("head_vertical_res") var headVerticalRes: MonitorScore? = null,
    //是否监测到人脸(label=true表示未监测到人脸)
    @SerializedName("find_res") var findRes: MonitorScore? = null,
    //是否距离过远(label=true表示距离过远)
    @SerializedName("long_distance_res") var longDistanceRes: MonitorScore? = null,
    //是否距离过近(label=true表示距离过近)
    @SerializedName("short_distance_res") var shortDistanceRes: MonitorScore? = null,
    //是否在屏幕中心(label=true表示不在屏幕中心)
    @SerializedName("is_in_screen_center_res") var isInScreenCenterRes: MonitorScore? = null,
    //是否佩戴红蓝眼镜 左红右蓝(label=true表示未佩戴红蓝眼镜)
    @SerializedName("wearing_color_glasses_red_blue_res") var wearingColorGlassesRedBlueRes: MonitorScore? = null,
    //是否佩戴蓝红眼镜 左蓝右红(label=true表示未佩戴蓝红眼镜)
    @SerializedName("wearing_color_glasses_blue_red_res") var wearingColorGlassesBlueRedRes: MonitorScore? = null,
    //是否专注(label=true表示不专注)
    @SerializedName("focus_res") var focusRes: MonitorScore? = null,
) : Parcelable

/**
 * 监控分
 */
@Parcelize
data class MonitorScore(
    val score:Float? = null,
    val label:Boolean? = null
) : Parcelable