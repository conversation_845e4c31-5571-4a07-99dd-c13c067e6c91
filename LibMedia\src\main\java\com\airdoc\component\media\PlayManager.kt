package com.airdoc.component.media

import android.content.Context
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.media.bean.AssetsMedia
import com.airdoc.component.media.bean.RawMedia
import com.airdoc.component.media.bean.StreamMedia
import com.airdoc.component.media.bean.UrlMedia
import com.airdoc.component.media.player.ExoMediaPlayer
import com.airdoc.component.media.player.IPlayEventListener
import com.airdoc.component.media.player.PlaybackState

/**
 * FileName: PlayManager
 * Author by lilin,Date on 2024/6/3 11:23
 * PS: Not easy to write code, please indicate.
 */
object PlayManager {

    private val mediaPlayer: ExoMediaPlayer by lazy {
        ExoMediaPlayer(BaseCommonApplication.instance).apply {
            setPlayerListener(ExternalPlayerListener())
        }
    }

    private val playEventListeners = mutableListOf<IPlayEventListener>()

    fun prepare(){
        mediaPlayer.prepare()
    }

    fun play() {
        mediaPlayer.play()
    }

    fun pause() {
        mediaPlayer.pause()
    }

    fun stop() {
        mediaPlayer.stop()
    }

    @OptIn(UnstableApi::class)
    fun playUrlMedia(urlMedia: UrlMedia){
        val mediaItem = urlMedia.createMediaItem()
        if (mediaItem != null){
            playMediaItem(mediaItem)
        }
    }

    @OptIn(UnstableApi::class)
    fun playAssetsMedia(context: Context,assetsMedia: AssetsMedia){
        val mediaSource = assetsMedia.createMediaSource(context)
        if (mediaSource != null){
            playMediaSource(mediaSource)
        }
    }

    @OptIn(UnstableApi::class)
    fun playRawMedia(context: Context,rawMedia: RawMedia){
        val mediaSource = rawMedia.createMediaSource(context)
        if (mediaSource != null){
            playMediaSource(mediaSource)
        }
    }

    @OptIn(UnstableApi::class)
    fun playStreamMedia(context: Context,streamMedia: StreamMedia){
        val mediaSource = streamMedia.createMediaSource(context)
        playMediaSource(mediaSource)
    }

    fun playMediaItem(mediaItem: MediaItem){
        mediaPlayer.playMediaItem(mediaItem)
    }

    fun playMediaItem(mediaItems: List<MediaItem>){
        mediaPlayer.playMediaItem(mediaItems)
    }

    @OptIn(UnstableApi::class)
    fun playMediaSource(mediaSource: MediaSource){
        mediaPlayer.playMediaSource(mediaSource)
    }

    @OptIn(UnstableApi::class)
    fun playMediaSource(mediaSources: List<MediaSource>){
        mediaPlayer.playMediaSource(mediaSources)
    }

    fun release() {
        mediaPlayer.release()
    }

    fun isPlaying(): Boolean {
        return mediaPlayer.isPlaying()
    }

    fun addPlayerEvenListener(listener: IPlayEventListener) {
        removePlayerEvenListener(listener)
        playEventListeners.add(listener)
    }

    fun removePlayerEvenListener(listener: IPlayEventListener) {
        if (playEventListeners.contains(listener)){
            playEventListeners.remove(listener)
        }
    }

    class ExternalPlayerListener : Player.Listener{

        override fun onMediaMetadataChanged(mediaMetadata: MediaMetadata) {
            super.onMediaMetadataChanged(mediaMetadata)
        }

        override fun onIsLoadingChanged(isLoading: Boolean) {
            super.onIsLoadingChanged(isLoading)
            if (isLoading){
                playEventListeners.forEach {
                    it.onPlaybackStateChanged(PlaybackState.STATE_LOADING)
                }
            }
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            when(playbackState){
                Player.STATE_BUFFERING -> {
                    playEventListeners.forEach {
                        it.onPlaybackStateChanged(PlaybackState.STATE_BUFFERING)
                    }
                }
                Player.STATE_ENDED -> {
                    playEventListeners.forEach {
                        it.onPlaybackStateChanged(PlaybackState.STATE_ENDED)
                    }
                }
                Player.STATE_IDLE -> {
                    playEventListeners.forEach {
                        it.onPlaybackStateChanged(PlaybackState.STATE_IDLE)
                    }
                }
                Player.STATE_READY -> {
                    playEventListeners.forEach {
                        it.onPlaybackStateChanged(PlaybackState.STATE_READY)
                    }
                }
            }
        }

        override fun onIsPlayingChanged(isPlaying: Boolean) {
            super.onIsPlayingChanged(isPlaying)
            if (isPlaying){
                playEventListeners.forEach {
                    it.onPlaybackStateChanged(PlaybackState.STATE_PLAYING)
                }
            }else{
                playEventListeners.forEach {
                    it.onPlaybackStateChanged(PlaybackState.STATE_PAUSED)
                }
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            super.onPlayerError(error)
            playEventListeners.forEach {
                it.onPlayerError()
            }
        }

    }

}