package com.airdoc.component.media

import android.content.Context
import android.media.AudioAttributes
import android.media.SoundPool

/**
 * FileName: SoundPoolManager
 * Author by lilin,Date on 2024/6/5 19:32
 * PS: Not easy to write code, please indicate.
 * 提示音播放管理
 */
object SoundPoolManager {

    private var mSoundPool: SoundPool? = null

    private fun initSoundPool(){
        if (mSoundPool == null){
            val attributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()
            mSoundPool = SoundPool.Builder()
                .setMaxStreams(1)//同时播放流的最大数量，当播放的流的数目大于此值，则会选择性停止优先级较低的流
                .setAudioAttributes(attributes)//设置audio 属性
                .build()
        }
    }

    /**
     *@param path File路径
     *@param priority 优先级
     */
    fun playSoundByPath(path:String,priority:Int = 1):Int{
        initSoundPool()
        var soundId = -1
        mSoundPool?.let { soundPool ->
            soundId = soundPool.load(path, priority)
            soundPool.setOnLoadCompleteListener { _, _, status ->
                if (status == 0) {
                    soundPool.play(soundId,1.0f,1.0f,1,0,1.0f)
                }
            }
        }
        return soundId
    }

    /**
     * @param context 上下文
     * @param resId 资源id
     * @param priority 优先级
     */
    fun playSoundByRes(context: Context, resId:Int, priority:Int = 1):Int{
        initSoundPool()
        var soundId = -1
        mSoundPool?.let { soundPool ->
            soundId = soundPool.load(context, resId, priority)
            soundPool.setOnLoadCompleteListener { _, _, status ->
                if (status == 0) {
                    soundPool
                    soundPool.play(soundId,1.0f,1.0f,1,0,1.0f)
                }
            }
        }
        return soundId
    }

    /**
     * @param afd asset文件描叙符
     * @param priority 优先级
     * @param rate 播放速率（1.0=正常播放，范围为0.5~2.0）
     */
    fun playSoundByAsset(context: Context,assetsPath: String,priority:Int = 1,rate:Float = 1.0f):Int{
        initSoundPool()
        val assetManager = context.assets
        val descriptor = assetManager.openFd(assetsPath)
        var soundId = -1
        mSoundPool?.let { soundPool ->
            soundId = soundPool.load(descriptor, priority)
            soundPool.setOnLoadCompleteListener { _, _, status ->
                if (status == 0) {
                    soundPool.play(soundId,1.0f,1.0f,1,0,rate)
                }
                descriptor.close()
            }
        }
        return soundId
    }

    fun pause(streamID: Int){
        mSoundPool?.pause(streamID)
    }

    fun stop(streamID: Int){
        mSoundPool?.stop(streamID)
    }

    fun autoPause(){
        mSoundPool?.autoPause()
    }

    fun autoResume(){
        mSoundPool?.autoResume()
    }

    fun release(){
        mSoundPool?.release()
    }
}