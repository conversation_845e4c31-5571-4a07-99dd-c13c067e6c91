package com.airdoc.component.media.bean

import android.content.Context
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.AssetDataSource
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.airdoc.component.media.BuildConfig
import kotlinx.parcelize.Parcelize

/**
 * FileName: AssetsMedia
 * Author by lilin,Date on 2024/6/28 20:34
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class AssetsMedia(
    val assetsPath:String
) : Media(), Parcelable{
    override fun createMediaItem(): MediaItem? {
        return try {
            MediaItem.fromUri(Uri.parse("asset:///${assetsPath}"))
        }catch (e:Exception){
            if (BuildConfig.DEBUG){
                e.printStackTrace()
            }
            null
        }
    }

    @OptIn(UnstableApi::class)
    override fun createMediaSource(context: Context): MediaSource? {
        val mediaItem = createMediaItem()
        return if (mediaItem != null) {
            ProgressiveMediaSource.Factory {
                AssetDataSource(context)
            }.createMediaSource(mediaItem)
        }else{
            null
        }
    }

}
