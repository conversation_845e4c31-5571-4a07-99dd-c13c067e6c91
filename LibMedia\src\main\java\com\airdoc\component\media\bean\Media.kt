package com.airdoc.component.media.bean

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.source.MediaSource

/**
 * FileName: MediaInfo
 * Author by lilin,Date on 2024/6/3 11:03
 * PS: Not easy to write code, please indicate.
 */
abstract class Media {

    abstract fun createMediaItem(): MediaItem?

    abstract fun createMediaSource(context: Context): MediaSource?
}
