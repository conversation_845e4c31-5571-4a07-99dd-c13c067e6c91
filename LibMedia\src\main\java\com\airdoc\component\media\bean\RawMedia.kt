package com.airdoc.component.media.bean

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.RawResourceDataSource
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.airdoc.component.media.BuildConfig
import kotlinx.parcelize.Parcelize

/**
 * FileName: RawMedia
 * Author by lilin,Date on 2024/12/6 15:53
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class RawMedia(
    val resId:Int
): Media(), Parcelable{
    override fun createMediaItem(): MediaItem? {
        return try {
            val uri = Uri.Builder()
                .scheme(ContentResolver.SCHEME_ANDROID_RESOURCE)
                .path(resId.toString())
                .build()
            MediaItem.fromUri(uri)
        }catch (e:Exception){
            if (BuildConfig.DEBUG){
                e.printStackTrace()
            }
            null
        }
    }

    @OptIn(UnstableApi::class)
    override fun createMediaSource(context: Context): MediaSource? {
        val mediaItem = createMediaItem()
        return if (mediaItem != null) {
            ProgressiveMediaSource.Factory {
                RawResourceDataSource(context)
            }.createMediaSource(mediaItem)
        }else{
            null
        }
    }

}
