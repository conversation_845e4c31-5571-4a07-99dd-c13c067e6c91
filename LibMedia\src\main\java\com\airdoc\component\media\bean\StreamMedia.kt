package com.airdoc.component.media.bean

import android.content.Context
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.TransferListener
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.airdoc.component.media.factory.StreamDataSourceFactory
import kotlinx.parcelize.Parcelize

/**
 * FileName: StreamMedia
 * Author by lilin,Date on 2024/6/29 14:03
 * PS: Not easy to write code, please indicate.
 * 流式语音合成
 */
@Parcelize
class StreamMedia : Media(), Parcelable {

    private val dataSourceFactory = StreamDataSourceFactory()

    @UnstableApi
    fun setTransferListener(listener: TransferListener){
        dataSourceFactory.setTransferListener(listener)
    }

    override fun createMediaItem(): MediaItem {
        return MediaItem.fromUri(Uri.EMPTY)
    }

    @OptIn(UnstableApi::class)
    override fun createMediaSource(context: Context): MediaSource {
        return ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(createMediaItem())
    }
}