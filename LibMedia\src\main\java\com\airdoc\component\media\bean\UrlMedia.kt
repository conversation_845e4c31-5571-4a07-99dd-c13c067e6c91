package com.airdoc.component.media.bean

import android.content.Context
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.airdoc.component.media.BuildConfig
import kotlinx.parcelize.Parcelize

/**
 * FileName: UrlMedia
 * Author by lilin,Date on 2024/6/3 16:29
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class UrlMedia(
    val url: String
) : Media(), Parcelable {
    override fun createMediaItem(): MediaItem? {
        return try {
            MediaItem.fromUri(Uri.parse(url))
        }catch (e:Exception){
            if (BuildConfig.DEBUG){
                e.printStackTrace()
            }
            null
        }
    }

    @OptIn(UnstableApi::class)
    override fun createMediaSource(context: Context): MediaSource? {
        val mediaItem = createMediaItem()
        return if (mediaItem != null){
            ProgressiveMediaSource.Factory(DefaultDataSource.Factory(context))
                .createMediaSource(mediaItem)
        }else{
            null
        }
    }
}