package com.airdoc.component.media.factory

import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.TransferListener
import com.airdoc.component.media.source.StreamDataSource

/**
 * FileName: StreamDataSourceFactory
 * Author by lilin,Date on 2024/6/5 16:30
 * PS: Not easy to write code, please indicate.
 */
class StreamDataSourceFactory : DataSource.Factory {

    @UnstableApi
    private var transferListener: TransferListener? = null

    @UnstableApi
    override fun createDataSource(): DataSource {
        return StreamDataSource().apply {
            transferListener?.let {
                addTransferListener(it)
            }
        }
    }

    @UnstableApi
    fun setTransferListener(listener: TransferListener?){
        transferListener = listener
    }
}