package com.airdoc.component.media.player

import android.content.Context
import androidx.media3.common.AudioAttributes
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource

/**
 * FileName: ExoMediaPlayer
 * Author by lilin,Date on 2024/6/3 11:20
 * PS: Not easy to write code, please indicate.
 */
class ExoMediaPlayer(context: Context){

    private var exoPlayer: ExoPlayer = ExoPlayer.Builder(context).build()

    private var internalPlayerListener:Player.Listener? = null

    init {
        exoPlayer.setAudioAttributes(AudioAttributes.DEFAULT, true)
        exoPlayer.addListener(PlayerListener())
    }

    fun setPlayerListener(listener: Player.Listener){
        internalPlayerListener = listener
    }

    fun prepare() {
        exoPlayer.prepare()
    }

    fun play() {
        exoPlayer.play()
    }

    fun pause() {
        exoPlayer.pause()
    }

    fun stop() {
        exoPlayer.stop()
    }

    fun playMediaItem(mediaItem: MediaItem) {
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.prepare()
        exoPlayer.play()
    }

    fun playMediaItem(mediaItems: List<MediaItem>) {
        exoPlayer.setMediaItems(mediaItems)
        exoPlayer.prepare()
        exoPlayer.play()
    }

    @UnstableApi
    fun playMediaSource(mediaSource: MediaSource) {
        exoPlayer.setMediaSource(mediaSource)
        exoPlayer.prepare()
        exoPlayer.play()
    }

    @UnstableApi
    fun playMediaSource(mediaSources: List<MediaSource>) {
        exoPlayer.setMediaSources(mediaSources)
        exoPlayer.prepare()
        exoPlayer.play()
    }

    fun release() {
        exoPlayer.release()
    }

    fun isPlaying(): Boolean {
        return exoPlayer.isPlaying
    }

    private inner class PlayerListener : Player.Listener{

        override fun onEvents(player: Player, events: Player.Events) {
            super.onEvents(player, events)
        }

        override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
            super.onMediaItemTransition(mediaItem, reason)
        }

        override fun onMediaMetadataChanged(mediaMetadata: MediaMetadata) {
            super.onMediaMetadataChanged(mediaMetadata)
            internalPlayerListener?.onMediaMetadataChanged(mediaMetadata)
        }

        override fun onPlaylistMetadataChanged(mediaMetadata: MediaMetadata) {
            super.onPlaylistMetadataChanged(mediaMetadata)
        }

        override fun onIsLoadingChanged(isLoading: Boolean) {
            super.onIsLoadingChanged(isLoading)
            internalPlayerListener?.onIsLoadingChanged(isLoading)
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            internalPlayerListener?.onPlaybackStateChanged(playbackState)
        }

        override fun onIsPlayingChanged(isPlaying: Boolean) {
            super.onIsPlayingChanged(isPlaying)
            internalPlayerListener?.onIsPlayingChanged(isPlaying)
        }

        override fun onPlayerError(error: PlaybackException) {
            super.onPlayerError(error)
            internalPlayerListener?.onPlayerError(error)
        }

        override fun onRenderedFirstFrame() {
            super.onRenderedFirstFrame()
        }
    }
}