package com.airdoc.component.media.player

import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.media.bean.Media

/**
 * FileName: IPlayer
 * Author by lilin,Date on 2024/6/3 11:05
 * PS: Not easy to write code, please indicate.
 */
interface IPlayer {

    fun prepare()

    fun play()

    fun pause()

    fun stop()

    fun playMedia(medias: List<Media>)

    fun playMediaItem(mediaItems: List<MediaItem>)

    fun playMediaSource(mediaSources: List<MediaSource>)

    fun addMedia(media: Media)

    fun addMedias(medias: List<Media>)

    fun addMediaItem(mediaItem: MediaItem)

    fun addMediaItems(mediaItems: List<MediaItem>)

    fun addMediaSource(mediaSource: MediaSource)

    fun addMediaSources(mediaSources: List<MediaSource>)

    fun release()

    fun isPlaying():Boolean

    fun addPlayerEvenListener(listener: IPlayEventListener)

    fun removePlayerEvenListener(listener: IPlayEventListener)
}