package com.airdoc.component.media.player.track

import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.LinkedBlockingQueue

/**
 * FileName: AudioTrackPlayer
 * Author by lilin,Date on 2024/6/19 15:38
 * PS: Not easy to write code, please indicate.
 */
class AudioTrackPlayer {

    companion object{
        private val TAG = AudioTrackPlayer::class.java.simpleName
    }

    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    private var SAMPLE_RATE = 16000

    private var audioPlayerCallback: AudioTrackPlayerCallback? = null
    private val audioQueue: LinkedBlockingQueue<ByteArray> = LinkedBlockingQueue<ByteArray>()

    @Volatile
    private var playState: PlayState = PlayState.IDLE
    private var tempData: ByteArray? = null

    // 初始化播放器
    // 此处仅使用Android系统自带的AudioTrack进行音频播放Demo演示, 客户可根据自己需要替换播放器
    // 默认采样率为16000、单通道、16bit pcm格式
    private var iMinBufSize = AudioTrack.getMinBufferSize(
        SAMPLE_RATE,
        AudioFormat.CHANNEL_OUT_MONO,
        AudioFormat.ENCODING_PCM_16BIT
    ) * 2
    private var audioTrack:AudioTrack? = AudioTrack(
        AudioManager.STREAM_MUSIC, SAMPLE_RATE,
        AudioFormat.CHANNEL_OUT_MONO,
        AudioFormat.ENCODING_PCM_16BIT,
        iMinBufSize, AudioTrack.MODE_STREAM
    )

    private var playJob:Job? = null

    fun setAudioTrackPlayerCallback(callback: AudioTrackPlayerCallback){
        audioPlayerCallback = callback
    }

    fun setAudioData(data: ByteArray) {
        audioQueue.offer(data)
    }

    fun setSampleRate(sampleRate: Int) {
        if (SAMPLE_RATE != sampleRate) {
            release()
            initAudioTrack(sampleRate)
            SAMPLE_RATE = sampleRate
        }
    }

    fun play() {
        if (playState == PlayState.PLAYING) return
        playState = PlayState.PLAYING
        if (audioTrack == null){
            initAudioTrack(SAMPLE_RATE)
        }
        audioTrack?.play()
        audioPlayerCallback?.playStart()
        if (playJob == null || !playJob!!.isActive){
            playJob = coroutineScope.launch(Dispatchers.IO) {
                while (playState != PlayState.RELEASE){
                    if (playState == PlayState.PLAYING){
                        if (audioQueue.size == 0) {
                            withContext(NonCancellable){
                                delay(10)
                            }
                        }else{
                            try {
                                tempData = audioQueue.take()
                            } catch (e: InterruptedException) {
                                e.printStackTrace()
                            }
                            tempData?.let {
                                audioTrack?.write(it, 0, it.size)
                            }
                        }
                    }else{
                        withContext(NonCancellable){
                            delay(10)
                        }
                    }
                }
            }.also { job ->
                job.invokeOnCompletion{
                    audioPlayerCallback?.playOver()
                }
            }
        }
    }

    fun stop() {
        playState = PlayState.IDLE
        audioQueue.clear()
        audioTrack?.flush()
        audioTrack?.pause()
        audioTrack?.stop()
    }

    fun pause() {
        playState = PlayState.PAUSE
        audioTrack?.pause()
    }

    fun resume() {
        audioTrack?.play()
        playState = PlayState.PLAYING
    }

    fun release() {
        audioTrack?.stop()
        playState = PlayState.RELEASE
        audioTrack?.release()
        audioTrack = null
    }

    private fun initAudioTrack(samplerate: Int) {
        // 初始化播放器
        // 此处仅使用Android系统自带的AudioTrack进行音频播放Demo演示, 客户可根据自己需要替换播放器
        // 默认采样率为16000、单通道、16bit pcm格式
        iMinBufSize = AudioTrack.getMinBufferSize(
            samplerate,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        ) * 2
        audioTrack = AudioTrack(
            AudioManager.STREAM_MUSIC, samplerate,
            AudioFormat.CHANNEL_OUT_MONO, AudioFormat.ENCODING_PCM_16BIT,
            iMinBufSize, AudioTrack.MODE_STREAM
        )
    }

    enum class PlayState {
        IDLE,
        PLAYING,
        PAUSE,
        RELEASE
    }

}