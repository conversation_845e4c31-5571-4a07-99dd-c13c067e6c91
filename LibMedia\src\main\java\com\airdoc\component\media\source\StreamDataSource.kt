package com.airdoc.component.media.source

import android.net.Uri
import androidx.media3.common.C
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DataSpec
import androidx.media3.datasource.TransferListener
import java.io.IOException
import java.io.PipedInputStream
import java.io.PipedOutputStream

/**
 * FileName: StreamDataSource
 * Author by lilin,Date on 2024/6/5 16:24
 * PS: Not easy to write code, please indicate.
 * 流式数据源
 */
class StreamDataSource : DataSource {

    private val pipedOutputStream = PipedOutputStream()
    private val pipedInputStream = PipedInputStream(pipedOutputStream)
    @Volatile
    private var opened = false
    @Volatile
    private var endOfStream = false
    private val transferListeners = mutableListOf<TransferListener>()
    @UnstableApi
    private lateinit var dataSpec: DataSpec

    /**
     * 写数据，要在子线程执行
     */
    fun write(data:ByteArray){
        try {
            pipedOutputStream.write(data)
        }catch (_: IOException){
        }
    }

    /**
     * 结束写入，要在子线程执行
     */
    fun endWrite(){
        try {
            pipedOutputStream.close()
        }catch (_: IOException){
        }
        endOfStream = true
    }

    @UnstableApi
    override fun read(buffer: ByteArray, offset: Int, length: Int): Int {
        val bytesRead = pipedInputStream.read(buffer, offset, length)
        if (bytesRead == -1) {
            return if (endOfStream){
                C.RESULT_END_OF_INPUT
            }else{
                C.RESULT_NOTHING_READ
            }
        }
        notifyBytesTransferred(bytesRead)
        return bytesRead
    }

    @UnstableApi
    override fun addTransferListener(transferListener: TransferListener) {
        if (transferListeners.contains(transferListener)){
            transferListeners.remove(transferListener)
        }
        transferListeners.add(transferListener)
    }

    @UnstableApi
    override fun open(dataSpec: DataSpec): Long {
        this.dataSpec = dataSpec
        notifyTransferInitializing()
        opened = true
        notifyTransferStart()
        // 流式数据长度未知
        return C.LENGTH_UNSET.toLong()
    }

    @UnstableApi
    override fun getUri(): Uri? {
        if (::dataSpec.isInitialized){
            return dataSpec.uri
        }
        return null
    }

    @UnstableApi
    override fun close() {
        try {
            pipedOutputStream.close()
        }catch (_: IOException){
        }
        try {
            pipedInputStream.close()
        }catch (_: IOException){
        }
        opened = false
        notifyTransferEnd()
    }

    @UnstableApi
    private fun notifyTransferInitializing() {
        if (::dataSpec.isInitialized){
            for (listener in transferListeners) {
                listener.onTransferInitializing(this, dataSpec, false)
            }
        }
    }

    @UnstableApi
    private fun notifyTransferStart() {
        if (::dataSpec.isInitialized){
            for (listener in transferListeners) {
                listener.onTransferStart(this, dataSpec, false)
            }
        }
    }

    @UnstableApi
    private fun notifyBytesTransferred(bytesRead: Int) {
        if (::dataSpec.isInitialized){
            for (listener in transferListeners){
                listener.onBytesTransferred(this, dataSpec, false,bytesRead)
            }
        }
    }

    @UnstableApi
    private fun notifyTransferEnd() {
        for (listener in transferListeners) {
            if (::dataSpec.isInitialized){
                listener.onTransferEnd(this, dataSpec, false)
            }else{
                listener.onTransferEnd(this, DataSpec(Uri.EMPTY), false)
            }
        }
    }
}