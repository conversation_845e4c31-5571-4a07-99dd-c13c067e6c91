<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .activity-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .fragment-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .intent-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .lifecycle-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .navigation-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .container-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 2; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
      .lifecycle-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#purplearrowhead); }
      .fragment-arrow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#orangearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
    
    <marker id="purplearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
    
    <marker id="orangearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">Activity切换与Fragment使用机制详解</text>
  
  <!-- Activity切换流程 -->
  <rect x="50" y="70" width="1500" height="300" class="navigation-box" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">Activity切换流程</text>
  
  <!-- ReadInitActivity -->
  <rect x="80" y="120" width="200" height="120" class="activity-box" rx="5"/>
  <text x="180" y="140" text-anchor="middle" class="step-title">ReadInitActivity</text>
  <text x="90" y="160" class="step-text">阅读初始化页面</text>
  <text x="90" y="175" class="code-text">• 设置身份年级</text>
  <text x="90" y="190" class="code-text">• 眼动校准</text>
  <text x="90" y="205" class="code-text">• 开始评估</text>
  <text x="90" y="220" class="code-text">goToRead()</text>
  
  <!-- Intent传递 -->
  <rect x="320" y="150" width="120" height="60" class="intent-box" rx="5"/>
  <text x="380" y="170" text-anchor="middle" class="step-title">Intent</text>
  <text x="330" y="185" class="code-text">身份参数</text>
  <text x="330" y="200" class="code-text">年级参数</text>
  
  <!-- ReadActivity -->
  <rect x="480" y="120" width="200" height="120" class="activity-box" rx="5"/>
  <text x="580" y="140" text-anchor="middle" class="step-title">ReadActivity</text>
  <text x="490" y="160" class="step-text">阅读测试页面</text>
  <text x="490" y="175" class="code-text">• 3秒倒计时</text>
  <text x="490" y="190" class="code-text">• 视线追踪</text>
  <text x="490" y="205" class="code-text">• 数据收集</text>
  <text x="490" y="220" class="code-text">跳转结果页面</text>
  
  <!-- Intent传递2 -->
  <rect x="720" y="150" width="120" height="60" class="intent-box" rx="5"/>
  <text x="780" y="170" text-anchor="middle" class="step-title">Intent</text>
  <text x="730" y="185" class="code-text">测试结果</text>
  <text x="730" y="200" class="code-text">轨迹数据</text>
  
  <!-- ReadResultAnalysisActivity -->
  <rect x="880" y="120" width="200" height="120" class="activity-box" rx="5"/>
  <text x="980" y="140" text-anchor="middle" class="step-title">ReadResultAnalysisActivity</text>
  <text x="890" y="160" class="step-text">结果分析页面</text>
  <text x="890" y="175" class="code-text">• 计算速度</text>
  <text x="890" y="190" class="code-text">• 判断达标</text>
  <text x="890" y="205" class="code-text">• 显示统计</text>
  <text x="890" y="220" class="code-text">查看轨迹</text>
  
  <!-- Intent传递3 -->
  <rect x="1120" y="150" width="120" height="60" class="intent-box" rx="5"/>
  <text x="1180" y="170" text-anchor="middle" class="step-title">Intent</text>
  <text x="1130" y="185" class="code-text">轨迹数据</text>
  <text x="1130" y="200" class="code-text">LiveEventBus</text>
  
  <!-- ReadTrackActivity -->
  <rect x="1280" y="120" width="200" height="120" class="activity-box" rx="5"/>
  <text x="1380" y="140" text-anchor="middle" class="step-title">ReadTrackActivity</text>
  <text x="1290" y="160" class="step-text">轨迹查看页面</text>
  <text x="1290" y="175" class="code-text">• 静态轨迹图</text>
  <text x="1290" y="190" class="code-text">• 动态轨迹图</text>
  <text x="1290" y="205" class="code-text">• 示例视频</text>
  
  <!-- 切换箭头 -->
  <line x1="280" y1="180" x2="320" y2="180" class="arrow"/>
  <line x1="440" y1="180" x2="480" y2="180" class="arrow"/>
  <line x1="680" y1="180" x2="720" y2="180" class="arrow"/>
  <line x1="840" y1="180" x2="880" y2="180" class="arrow"/>
  <line x1="1080" y1="180" x2="1120" y2="180" class="arrow"/>
  <line x1="1240" y1="180" x2="1280" y2="180" class="arrow"/>
  
  <!-- Fragment使用详解 -->
  <rect x="50" y="400" width="1500" height="750" class="container-box" rx="8"/>
  <text x="800" y="425" text-anchor="middle" class="subtitle">Fragment使用机制详解</text>
  
  <!-- ReadInitActivity Fragment管理 -->
  <rect x="80" y="450" width="700" height="320" class="activity-box" rx="5"/>
  <text x="430" y="475" text-anchor="middle" class="step-title">ReadInitActivity - Fragment容器管理</text>
  
  <!-- Fragment容器 -->
  <rect x="100" y="490" width="660" height="60" class="container-box" rx="3"/>
  <text x="430" y="510" text-anchor="middle" class="step-text">Fragment容器 (FrameLayout)</text>
  <text x="110" y="530" class="code-text">android:id="@+id/fragment_container" - 用于动态加载Fragment</text>
  <text x="110" y="545" class="code-text">supportFragmentManager.beginTransaction().replace(R.id.fragment_container, fragment).commit()</text>
  
  <!-- 三个Fragment -->
  <rect x="120" y="570" width="180" height="80" class="fragment-box" rx="3"/>
  <text x="210" y="590" text-anchor="middle" class="step-title">ReadInitBasicInfoFragment</text>
  <text x="130" y="610" class="step-text">基础信息设置</text>
  <text x="130" y="625" class="code-text">• 选择身份</text>
  <text x="130" y="640" class="code-text">• 选择年级</text>
  
  <rect x="320" y="570" width="180" height="80" class="fragment-box" rx="3"/>
  <text x="410" y="590" text-anchor="middle" class="step-title">ReadInitCalibrationFragment</text>
  <text x="330" y="610" class="step-text">眼动校准</text>
  <text x="330" y="625" class="code-text">• 校准流程</text>
  <text x="330" y="640" class="code-text">• 校准验证</text>
  
  <rect x="520" y="570" width="180" height="80" class="fragment-box" rx="3"/>
  <text x="610" y="590" text-anchor="middle" class="step-title">ReadInitStartEvaluateFragment</text>
  <text x="530" y="610" class="step-text">开始评估</text>
  <text x="530" y="625" class="code-text">• 准备测试</text>
  <text x="530" y="640" class="code-text">• 跳转测试</text>
  
  <!-- Fragment切换方法 -->
  <rect x="100" y="670" width="660" height="80" class="lifecycle-box" rx="3"/>
  <text x="430" y="690" text-anchor="middle" class="step-title">Fragment切换方法</text>
  <text x="110" y="710" class="code-text">goToBasicInfoTab() -> 切换到基础信息Fragment</text>
  <text x="110" y="725" class="code-text">goToCalibrationTab() -> 切换到校准Fragment</text>
  <text x="110" y="740" class="code-text">goToStartEvaluate() -> 切换到开始评估Fragment</text>
  
  <!-- Fragment切换箭头 -->
  <line x1="210" y1="570" x2="210" y2="550" class="fragment-arrow"/>
  <line x1="410" y1="570" x2="410" y2="550" class="fragment-arrow"/>
  <line x1="610" y1="570" x2="610" y2="550" class="fragment-arrow"/>
  
  <!-- Activity生命周期与Fragment关系 -->
  <rect x="820" y="450" width="700" height="320" class="lifecycle-box" rx="5"/>
  <text x="1170" y="475" text-anchor="middle" class="step-title">Activity生命周期与Fragment关系</text>
  
  <!-- 生命周期对比 -->
  <rect x="840" y="490" width="320" height="260" class="activity-box" rx="3"/>
  <text x="1000" y="510" text-anchor="middle" class="step-title">Activity生命周期</text>
  <text x="850" y="530" class="step-text">onCreate() - 创建Activity</text>
  <text x="860" y="545" class="code-text">• 初始化布局</text>
  <text x="860" y="560" class="code-text">• 设置Fragment容器</text>
  <text x="850" y="580" class="step-text">onStart() - Activity可见</text>
  <text x="860" y="595" class="code-text">• 绑定Service</text>
  <text x="850" y="615" class="step-text">onResume() - Activity活跃</text>
  <text x="860" y="630" class="code-text">• 用户可交互</text>
  <text x="850" y="650" class="step-text">onPause() - Activity暂停</text>
  <text x="850" y="670" class="step-text">onStop() - Activity不可见</text>
  <text x="860" y="685" class="code-text">• 解绑Service</text>
  <text x="850" y="705" class="step-text">onDestroy() - Activity销毁</text>
  <text x="860" y="720" class="code-text">• 清理资源</text>
  
  <rect x="1180" y="490" width="320" height="260" class="fragment-box" rx="3"/>
  <text x="1340" y="510" text-anchor="middle" class="step-title">Fragment生命周期</text>
  <text x="1190" y="530" class="step-text">onAttach() - 附加到Activity</text>
  <text x="1190" y="550" class="step-text">onCreate() - Fragment创建</text>
  <text x="1190" y="570" class="step-text">onCreateView() - 创建视图</text>
  <text x="1200" y="585" class="code-text">• 加载布局</text>
  <text x="1190" y="605" class="step-text">onViewCreated() - 视图创建完成</text>
  <text x="1200" y="620" class="code-text">• 初始化控件</text>
  <text x="1190" y="640" class="step-text">onStart() - Fragment可见</text>
  <text x="1190" y="660" class="step-text">onResume() - Fragment活跃</text>
  <text x="1190" y="680" class="step-text">onPause() - Fragment暂停</text>
  <text x="1190" y="700" class="step-text">onDestroyView() - 销毁视图</text>
  <text x="1190" y="720" class="step-text">onDestroy() - Fragment销毁</text>
  <text x="1190" y="740" class="step-text">onDetach() - 从Activity分离</text>
  
  <!-- Fragment管理机制 -->
  <rect x="80" y="800" width="1440" height="320" class="navigation-box" rx="5"/>
  <text x="800" y="825" text-anchor="middle" class="step-title">Fragment管理机制详解</text>
  
  <!-- FragmentManager -->
  <rect x="100" y="850" width="300" height="120" class="container-box" rx="3"/>
  <text x="250" y="870" text-anchor="middle" class="step-title">FragmentManager</text>
  <text x="110" y="890" class="step-text">Fragment事务管理器</text>
  <text x="110" y="905" class="code-text">supportFragmentManager</text>
  <text x="110" y="920" class="code-text">• add() - 添加Fragment</text>
  <text x="110" y="935" class="code-text">• replace() - 替换Fragment</text>
  <text x="110" y="950" class="code-text">• remove() - 移除Fragment</text>
  <text x="110" y="965" class="code-text">• commit() - 提交事务</text>
  
  <!-- FragmentTransaction -->
  <rect x="420" y="850" width="300" height="120" class="intent-box" rx="3"/>
  <text x="570" y="870" text-anchor="middle" class="step-title">FragmentTransaction</text>
  <text x="430" y="890" class="step-text">Fragment事务操作</text>
  <text x="430" y="905" class="code-text">beginTransaction()</text>
  <text x="430" y="920" class="code-text">• 设置动画效果</text>
  <text x="430" y="935" class="code-text">• 添加到回退栈</text>
  <text x="430" y="950" class="code-text">• 设置标签</text>
  <text x="430" y="965" class="code-text">• commitAllowingStateLoss()</text>
  
  <!-- Fragment通信 -->
  <rect x="740" y="850" width="300" height="120" class="fragment-box" rx="3"/>
  <text x="890" y="870" text-anchor="middle" class="step-title">Fragment通信</text>
  <text x="750" y="890" class="step-text">Fragment与Activity通信</text>
  <text x="750" y="905" class="code-text">• 接口回调</text>
  <text x="750" y="920" class="code-text">• ViewModel共享</text>
  <text x="750" y="935" class="code-text">• LiveData观察</text>
  <text x="750" y="950" class="code-text">• Bundle参数传递</text>
  <text x="750" y="965" class="code-text">• EventBus事件</text>
  
  <!-- Fragment优势 -->
  <rect x="1060" y="850" width="300" height="120" class="lifecycle-box" rx="3"/>
  <text x="1210" y="870" text-anchor="middle" class="step-title">Fragment优势</text>
  <text x="1070" y="890" class="step-text">模块化UI组件</text>
  <text x="1070" y="905" class="code-text">• 可复用性强</text>
  <text x="1070" y="920" class="code-text">• 独立生命周期</text>
  <text x="1070" y="935" class="code-text">• 动态切换</text>
  <text x="1070" y="950" class="code-text">• 内存效率高</text>
  <text x="1070" y="965" class="code-text">• 适配不同屏幕</text>
  
  <!-- 实际应用示例 -->
  <rect x="100" y="990" width="1360" height="120" class="activity-box" rx="3"/>
  <text x="780" y="1010" text-anchor="middle" class="step-title">实际应用示例 - ReadInitActivity</text>
  <text x="120" y="1030" class="code-text">fun goToBasicInfoTab() {</text>
  <text x="140" y="1045" class="code-text">    supportFragmentManager.beginTransaction()</text>
  <text x="160" y="1060" class="code-text">        .replace(R.id.fragment_container, ReadInitBasicInfoFragment())</text>
  <text x="160" y="1075" class="code-text">        .commit()</text>
  <text x="120" y="1090" class="code-text">}</text>
  
  <text x="500" y="1030" class="code-text">fun goToRead() {</text>
  <text x="520" y="1045" class="code-text">    val intent = ReadActivity.createIntent(this, identity, grade)</text>
  <text x="520" y="1060" class="code-text">    startActivity(intent)</text>
  <text x="520" y="1075" class="code-text">    finish()  // 结束当前Activity</text>
  <text x="500" y="1090" class="code-text">}</text>
  
  <text x="900" y="1030" class="code-text">// Fragment间数据传递</text>
  <text x="900" y="1045" class="code-text">val bundle = Bundle().apply {</text>
  <text x="920" y="1060" class="code-text">    putSerializable("identity", identity)</text>
  <text x="920" y="1075" class="code-text">    putSerializable("grade", grade)</text>
  <text x="900" y="1090" class="code-text">}</text>
</svg>
