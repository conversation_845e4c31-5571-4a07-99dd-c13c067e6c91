<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .xml-code { font-family: 'Courier New', monospace; font-size: 8px; fill: #e74c3c; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .fragment-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .xml-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .custom-view-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .main-entry-box { fill: #ffebee; stroke: #f44336; stroke-width: 3; }
      .container-arrow { stroke: #2196f3; stroke-width: 3; fill: none; marker-end: url(#containerarrow); }
      .load-arrow { stroke: #9c27b0; stroke-width: 2; fill: none; marker-end: url(#loadarrow); }
      .custom-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#customarrow); }
    </style>
    <marker id="containerarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2196f3" />
    </marker>
    <marker id="loadarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0" />
    </marker>
    <marker id="customarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">眼球运动评估模块 Activity/Fragment/XML/自定义View 完整结构</text>
  <text x="800" y="50" text-anchor="middle" class="subtitle">从主入口到具体实现的层级关系</text>

  <!-- 主入口Activity -->
  <rect x="50" y="80" width="1500" height="120" class="main-entry-box" rx="10"/>
  <text x="800" y="105" text-anchor="middle" class="subtitle">主入口 - EyeMovementEvaluateActivity</text>
  <text x="70" y="130" class="text">📍 <tspan class="code">添加位置</tspan>: app/src/main/java/com/mitdd/gazetracker/movement/EyeMovementEvaluateActivity.kt</text>
  <text x="70" y="150" class="text">🎯 <tspan class="code">功能</tspan>: 眼球运动评估系统主页，包含4个评估模块入口</text>
  <text x="70" y="170" class="text">📱 <tspan class="code">XML布局</tspan>: activity_eye_movement_evaluate.xml (ConstraintLayout + 4个TextView按钮)</text>
  <text x="70" y="190" class="text">🔗 <tspan class="code">导航</tspan>: 通过Intent跳转到各个具体评估Activity</text>

  <!-- 主入口XML结构 -->
  <rect x="1100" y="220" width="450" height="160" class="xml-box" rx="8"/>
  <text x="1325" y="245" text-anchor="middle" class="subtitle">activity_eye_movement_evaluate.xml</text>
  <text x="1110" y="265" class="xml-code">&lt;ConstraintLayout android:background="#EFF3F6"&gt;</text>
  <text x="1120" y="280" class="xml-code">  &lt;ImageView src="@drawable/icon_read_main_logo"/&gt;</text>
  <text x="1120" y="295" class="xml-code">  &lt;TextView text="眼球运动评估系统"/&gt;</text>
  <text x="1120" y="310" class="xml-code">  &lt;TextView id="tv_gaze_stability" text="注视稳定性"/&gt;</text>
  <text x="1120" y="325" class="xml-code">  &lt;TextView id="tv_follow_ability" text="追随能力"/&gt;</text>
  <text x="1120" y="340" class="xml-code">  &lt;TextView id="tv_saccade_ability" text="扫视能力"/&gt;</text>
  <text x="1120" y="355" class="xml-code">  &lt;TextView id="tv_roi_detection" text="ROI检测"/&gt;</text>
  <text x="1120" y="370" class="xml-code">&lt;/ConstraintLayout&gt;</text>

  <!-- 扫视能力评估Activity详细结构 -->
  <rect x="50" y="420" width="1500" height="140" class="activity-box" rx="10"/>
  <text x="800" y="445" text-anchor="middle" class="subtitle">扫视能力评估 - SaccadeAbilityEvaluateActivity</text>
  <text x="70" y="470" class="text">📍 <tspan class="code">添加位置</tspan>: app/src/main/java/com/mitdd/gazetracker/movement/saccade/SaccadeAbilityEvaluateActivity.kt</text>
  <text x="70" y="490" class="text">🏗️ <tspan class="code">架构</tspan>: Fragment容器Activity，使用FrameLayout作为Fragment容器</text>
  <text x="70" y="510" class="text">📱 <tspan class="code">XML布局</tspan>: activity_saccade_ability_evaluate.xml (简单FrameLayout容器)</text>
  <text x="70" y="530" class="text">🔄 <tspan class="code">Fragment管理</tspan>: supportFragmentManager.beginTransaction().replace() 切换Fragment</text>
  <text x="70" y="550" class="text">📦 <tspan class="code">包含部分</tspan>: 1.说明Fragment → 2.评估Fragment → 3.结果Activity</text>

  <!-- Fragment结构详解 -->
  <rect x="50" y="580" width="700" height="200" class="fragment-box" rx="10"/>
  <text x="400" y="605" text-anchor="middle" class="subtitle">Fragment 结构 (2个主要Fragment)</text>

  <!-- 说明Fragment -->
  <rect x="70" y="620" width="300" height="140" class="fragment-box" rx="5"/>
  <text x="220" y="640" text-anchor="middle" class="subtitle">1. SaccadeAbilityExplainFragment</text>
  <text x="80" y="660" class="text">📍 <tspan class="code">位置</tspan>: .../saccade/SaccadeAbilityExplainFragment.kt</text>
  <text x="80" y="675" class="text">🎯 <tspan class="code">功能</tspan>: 显示评估说明和演示GIF</text>
  <text x="80" y="690" class="text">📱 <tspan class="code">XML</tspan>: fragment_saccade_ability_explain.xml</text>
  <text x="80" y="705" class="text">🔗 <tspan class="code">包含</tspan>: TextView说明文字 + ImageView演示图</text>
  <text x="80" y="720" class="text">⚡ <tspan class="code">交互</tspan>: 开始按钮 → 切换到评估Fragment</text>
  <text x="80" y="735" class="code">activity.showSaccadeAbilityEvaluating()</text>

  <!-- 评估Fragment -->
  <rect x="390" y="620" width="340" height="140" class="fragment-box" rx="5"/>
  <text x="560" y="640" text-anchor="middle" class="subtitle">2. SaccadeAbilityEvaluatingFragment</text>
  <text x="400" y="660" class="text">📍 <tspan class="code">位置</tspan>: .../saccade/SaccadeAbilityEvaluatingFragment.kt</text>
  <text x="400" y="675" class="text">🎯 <tspan class="code">功能</tspan>: 执行实际评估，收集眼动数据</text>
  <text x="400" y="690" class="text">📱 <tspan class="code">XML</tspan>: fragment_saccade_ability_evaluating.xml</text>
  <text x="400" y="705" class="text">🔗 <tspan class="code">包含</tspan>: 自定义View + 倒计时布局</text>
  <text x="400" y="720" class="text">📡 <tspan class="code">通信</tspan>: 与GazeTrackService通信</text>
  <text x="400" y="735" class="code">sendMessageToService(MSG_START_TRACK)</text>

  <!-- Fragment XML详解 -->
  <rect x="770" y="580" width="780" height="200" class="xml-box" rx="10"/>
  <text x="1160" y="605" text-anchor="middle" class="subtitle">Fragment XML 嵌套结构</text>

  <!-- 评估Fragment XML -->
  <rect x="790" y="620" width="360" height="140" class="xml-box" rx="5"/>
  <text x="970" y="640" text-anchor="middle" class="subtitle">fragment_saccade_ability_evaluating.xml</text>
  <text x="800" y="660" class="xml-code">&lt;ConstraintLayout&gt;</text>
  <text x="810" y="675" class="xml-code">  &lt;TextView id="tv_time" 计时显示/&gt;</text>
  <text x="810" y="690" class="xml-code">  &lt;SaccadeAbilityEvaluatingView</text>
  <text x="820" y="705" class="xml-code">    自定义View 全屏覆盖/&gt;</text>
  <text x="810" y="720" class="xml-code">  &lt;ConstraintLayout id="cl_count_down"</text>
  <text x="820" y="735" class="xml-code">    倒计时遮罩层/&gt;</text>
  <text x="800" y="750" class="xml-code">&lt;/ConstraintLayout&gt;</text>

  <!-- 结果Activity XML -->
  <rect x="1170" y="620" width="360" height="140" class="xml-box" rx="5"/>
  <text x="1350" y="640" text-anchor="middle" class="subtitle">activity_saccade_ability_evaluate_result.xml</text>
  <text x="1180" y="660" class="xml-code">&lt;ConstraintLayout&gt;</text>
  <text x="1190" y="675" class="xml-code">  &lt;SaccadeAbilityEvaluateResultView</text>
  <text x="1200" y="690" class="xml-code">    android:id="evaluate_result_view"</text>
  <text x="1200" y="705" class="xml-code">    android:layout_width="match_parent"</text>
  <text x="1200" y="720" class="xml-code">    android:layout_height="match_parent"</text>
  <text x="1200" y="735" class="xml-code">    全屏自定义Canvas绘制View/&gt;</text>
  <text x="1180" y="750" class="xml-code">&lt;/ConstraintLayout&gt;</text>

  <!-- 自定义View详解 -->
  <rect x="50" y="800" width="1500" height="200" class="custom-view-box" rx="10"/>
  <text x="800" y="825" text-anchor="middle" class="subtitle">自定义View实现 (Canvas绘制核心)</text>

  <!-- 评估View -->
  <rect x="70" y="840" width="350" height="140" class="custom-view-box" rx="5"/>
  <text x="245" y="860" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingView</text>
  <text x="80" y="880" class="text">📍 <tspan class="code">位置</tspan>: .../saccade/SaccadeAbilityEvaluatingView.kt</text>
  <text x="80" y="895" class="text">🏗️ <tspan class="code">继承</tspan>: FrameLayout</text>
  <text x="80" y="910" class="text">🎯 <tspan class="code">功能</tspan>: 显示目标点，响应用户注视</text>
  <text x="80" y="925" class="text">🔗 <tspan class="code">组件</tspan>: ImageView目标点 + 动态位置更新</text>
  <text x="80" y="940" class="code">fun startEvaluating(pointF: PointF)</text>
  <text x="80" y="955" class="code">targetImageView.x = pointF.x</text>
  <text x="80" y="970" class="code">targetImageView.y = pointF.y</text>

  <!-- 结果View -->
  <rect x="440" y="840" width="350" height="140" class="custom-view-box" rx="5"/>
  <text x="615" y="860" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultView</text>
  <text x="450" y="880" class="text">📍 <tspan class="code">位置</tspan>: .../saccade/SaccadeAbilityEvaluateResultView.kt</text>
  <text x="450" y="895" class="text">🏗️ <tspan class="code">继承</tspan>: FrameLayout</text>
  <text x="450" y="910" class="text">🎯 <tspan class="code">功能</tspan>: Canvas绘制评估结果轨迹</text>
  <text x="450" y="925" class="text">🎨 <tspan class="code">绘制</tspan>: 眼动轨迹 + 目标点 + 分析数据</text>
  <text x="450" y="940" class="code">override fun onDraw(canvas: Canvas)</text>
  <text x="450" y="955" class="code">canvas.drawPath(gazePath, paint)</text>
  <text x="450" y="970" class="code">canvas.drawCircle(x, y, radius, paint)</text>

  <!-- 其他评估模块View -->
  <rect x="810" y="840" width="350" height="140" class="custom-view-box" rx="5"/>
  <text x="985" y="860" text-anchor="middle" class="subtitle">其他评估模块自定义View</text>
  <text x="820" y="880" class="text">🔍 <tspan class="code">GazeStabilityEvaluateResultView</tspan>: 注视稳定性结果</text>
  <text x="820" y="895" class="text">👁️ <tspan class="code">FollowAbilityEvaluateResultView</tspan>: 追随能力结果</text>
  <text x="820" y="910" class="text">📊 <tspan class="code">ROIDetectionResultView</tspan>: ROI检测结果</text>
  <text x="820" y="925" class="text">🎨 <tspan class="code">共同特点</tspan>: 继承FrameLayout，重写onDraw</text>
  <text x="820" y="940" class="text">📐 <tspan class="code">坐标转换</tspan>: 相对坐标 → 屏幕坐标</text>
  <text x="820" y="955" class="text">💾 <tspan class="code">图片生成</tspan>: drawToBitmap()生成结果图</text>

  <!-- Canvas绘制详解 -->
  <rect x="1180" y="840" width="350" height="140" class="custom-view-box" rx="5"/>
  <text x="1355" y="860" text-anchor="middle" class="subtitle">Canvas绘制核心代码</text>
  <text x="1190" y="880" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="1200" y="895" class="code">  super.onDraw(canvas)</text>
  <text x="1200" y="910" class="code">  // 绘制眼动轨迹路径</text>
  <text x="1200" y="925" class="code">  canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="1200" y="940" class="code">  // 绘制注视点圆圈</text>
  <text x="1200" y="955" class="code">  gazePoints.forEach { point -></text>
  <text x="1210" y="970" class="code">    canvas.drawCircle(x, y, radius, paint)</text>
  <text x="1200" y="985" class="code">  }</text>
  <text x="1190" y="1000" class="code">}</text>

  <!-- 其他评估模块Activity结构 -->
  <rect x="50" y="1020" width="1500" height="160" class="activity-box" rx="10"/>
  <text x="800" y="1045" text-anchor="middle" class="subtitle">其他评估模块Activity结构 (相同架构模式)</text>

  <!-- 注视稳定性 -->
  <rect x="70" y="1060" width="340" height="100" class="activity-box" rx="5"/>
  <text x="240" y="1080" text-anchor="middle" class="subtitle">GazeStabilityEvaluateActivity</text>
  <text x="80" y="1100" class="text">📍 .../gaze/GazeStabilityEvaluateActivity.kt</text>
  <text x="80" y="115" class="text">🏗️ Fragment容器 (说明→评估→结果)</text>
  <text x="80" y="1130" class="text">📱 GazeStabilityEvaluatingView (自定义)</text>
  <text x="80" y="1145" class="text">🎨 GazeStabilityEvaluateResultView (Canvas)</text>

  <!-- 追随能力 -->
  <rect x="430" y="1060" width="340" height="100" class="activity-box" rx="5"/>
  <text x="600" y="1080" text-anchor="middle" class="subtitle">FollowAbilityEvaluateActivity</text>
  <text x="440" y="1100" class="text">📍 .../follow/FollowAbilityEvaluateActivity.kt</text>
  <text x="440" y="1115" class="text">🏗️ Fragment容器 (说明→评估→结果)</text>
  <text x="440" y="1130" class="text">📱 FollowAbilityEvaluatingView (自定义)</text>
  <text x="440" y="1145" class="text">🎨 FollowAbilityEvaluateResultView (Canvas)</text>

  <!-- ROI检测 -->
  <rect x="790" y="1060" width="340" height="100" class="activity-box" rx="5"/>
  <text x="960" y="1080" text-anchor="middle" class="subtitle">ROIDetectionEvaluateActivity</text>
  <text x="800" y="1100" class="text">📍 .../roi/ROIDetectionEvaluateActivity.kt</text>
  <text x="800" y="1115" class="text">🏗️ Fragment容器 (说明→评估→结果)</text>
  <text x="800" y="1130" class="text">📱 ROIDetectionEvaluatingView (自定义)</text>
  <text x="800" y="1145" class="text">🎨 ROIDetectionResultView (Canvas)</text>

  <!-- 患者信息管理 -->
  <rect x="1150" y="1060" width="340" height="100" class="activity-box" rx="5"/>
  <text x="1320" y="1080" text-anchor="middle" class="subtitle">EMPatientActivity</text>
  <text x="1160" y="1100" class="text">📍 .../patient/EMPatientActivity.kt</text>
  <text x="1160" y="1115" class="text">🏗️ 患者信息管理 (列表+添加)</text>
  <text x="1160" y="1130" class="text">📱 RecyclerView + Dialog</text>
  <text x="1160" y="1145" class="text">🔗 EMPatientViewModel + Repository</text>

  <!-- XML嵌套层级说明 -->
  <rect x="50" y="1200" width="1500" height="140" class="xml-box" rx="10"/>
  <text x="800" y="1225" text-anchor="middle" class="subtitle">XML布局嵌套层级结构</text>

  <!-- 层级1: Activity容器 -->
  <rect x="70" y="1240" width="280" height="80" class="xml-box" rx="5"/>
  <text x="210" y="1260" text-anchor="middle" class="subtitle">层级1: Activity容器</text>
  <text x="80" y="1280" class="xml-code">activity_xxx_evaluate.xml</text>
  <text x="80" y="1295" class="xml-code">&lt;FrameLayout id="container"&gt;</text>
  <text x="90" y="1310" class="xml-code">  Fragment动态加载区域</text>

  <!-- 层级2: Fragment布局 -->
  <rect x="370" y="1240" width="280" height="80" class="xml-box" rx="5"/>
  <text x="510" y="1260" text-anchor="middle" class="subtitle">层级2: Fragment布局</text>
  <text x="380" y="1280" class="xml-code">fragment_xxx_evaluating.xml</text>
  <text x="380" y="1295" class="xml-code">&lt;ConstraintLayout&gt;</text>
  <text x="390" y="1310" class="xml-code">  自定义View + 控制组件</text>

  <!-- 层级3: 自定义View -->
  <rect x="670" y="1240" width="280" height="80" class="xml-box" rx="5"/>
  <text x="810" y="1260" text-anchor="middle" class="subtitle">层级3: 自定义View</text>
  <text x="680" y="1280" class="xml-code">XxxEvaluatingView.kt</text>
  <text x="680" y="1295" class="xml-code">继承FrameLayout</text>
  <text x="690" y="1310" class="xml-code">动态添加ImageView等组件</text>

  <!-- 层级4: Canvas绘制 -->
  <rect x="970" y="1240" width="280" height="80" class="xml-box" rx="5"/>
  <text x="1110" y="1260" text-anchor="middle" class="subtitle">层级4: Canvas绘制</text>
  <text x="980" y="1280" class="xml-code">XxxResultView.kt</text>
  <text x="980" y="1295" class="xml-code">override onDraw(canvas)</text>
  <text x="990" y="1310" class="xml-code">直接绘制到Canvas</text>

  <!-- 结果Activity -->
  <rect x="1270" y="1240" width="280" height="80" class="xml-box" rx="5"/>
  <text x="1410" y="1260" text-anchor="middle" class="subtitle">结果Activity</text>
  <text x="1280" y="1280" class="xml-code">activity_xxx_result.xml</text>
  <text x="1280" y="1295" class="xml-code">&lt;ConstraintLayout&gt;</text>
  <text x="1290" y="1310" class="xml-code">  &lt;XxxResultView/&gt;</text>

  <!-- 连接箭头 -->
  <!-- 主入口到具体Activity -->
  <line x1="800" y1="200" x2="800" y2="420" class="container-arrow"/>
  <text x="810" y="310" class="text" fill="#2196f3">Intent跳转</text>

  <!-- Activity到Fragment -->
  <line x1="400" y1="560" x2="400" y2="580" class="load-arrow"/>
  <text x="410" y="575" class="text" fill="#9c27b0">Fragment加载</text>

  <!-- Fragment到自定义View -->
  <line x1="560" y1="780" x2="245" y2="840" class="custom-arrow"/>
  <line x1="970" y1="780" x2="615" y2="840" class="custom-arrow"/>
  <text x="400" y="815" class="text" fill="#ff9800">自定义View嵌入</text>

  <!-- 架构特点总结 -->
  <rect x="50" y="1360" width="1500" height="80" class="main-entry-box" rx="10"/>
  <text x="800" y="1385" text-anchor="middle" class="subtitle">架构设计特点总结</text>
  <text x="70" y="1410" class="text">🏗️ <tspan class="subtitle">统一架构模式</tspan>: 所有评估模块都采用 Activity容器 → Fragment切换 → 自定义View绘制 的三层架构</text>
  <text x="70" y="1430" class="text">🎨 <tspan class="subtitle">自定义View核心</tspan>: 继承FrameLayout，重写onDraw()，实现复杂的Canvas绘制和用户交互</text>

</svg>
