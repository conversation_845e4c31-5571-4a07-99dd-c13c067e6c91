<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .message-text { font-family: Arial, sans-serif; font-size: 11px; fill: #e74c3c; font-weight: bold; }
      .note-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .lifecycle-phase { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2980b9; }
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 1; stroke-dasharray: 5,5; fill: none; marker-end: url(#greenarrowhead); }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 3,3; fill: none; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="greenarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">ReadActivity 与 GazeTrackService 交互时序图</text>
  
  <!-- 参与者 -->
  <rect x="50" y="60" width="120" height="40" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="110" y="85" text-anchor="middle" class="actor-title">用户</text>
  
  <rect x="250" y="60" width="120" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="310" y="85" text-anchor="middle" class="actor-title">ReadActivity</text>
  
  <rect x="450" y="60" width="120" height="40" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="510" y="85" text-anchor="middle" class="actor-title">GazeTrackService</text>
  
  <rect x="650" y="60" width="120" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
  <text x="710" y="85" text-anchor="middle" class="actor-title">摄像头</text>
  
  <rect x="850" y="60" width="120" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="910" y="85" text-anchor="middle" class="actor-title">眼动追踪</text>
  
  <!-- 生命线 -->
  <line x1="110" y1="100" x2="110" y2="1150" class="lifeline"/>
  <line x1="310" y1="100" x2="310" y2="1150" class="lifeline"/>
  <line x1="510" y1="100" x2="510" y2="1150" class="lifeline"/>
  <line x1="710" y1="100" x2="710" y2="1150" class="lifeline"/>
  <line x1="910" y1="100" x2="910" y2="1150" class="lifeline"/>
  
  <!-- 生命周期阶段标记 -->
  <rect x="1050" y="60" width="300" height="40" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="1200" y="85" text-anchor="middle" class="lifecycle-phase">📱 Activity 生命周期开始</text>
  
  <!-- 1. 启动Activity -->
  <line x1="110" y1="130" x2="310" y2="130" class="arrow"/>
  <text x="210" y="125" text-anchor="middle" class="message-text">启动阅读Activity</text>
  
  <rect x="320" y="140" width="80" height="20" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="360" y="155" text-anchor="middle" style="font-size: 10px; fill: white;">Activity激活</text>
  
  <!-- 2. onCreate -->
  <rect x="1050" y="170" width="300" height="30" fill="#e3f2fd" stroke="#2196f3" stroke-width="1" rx="3"/>
  <text x="1200" y="190" text-anchor="middle" class="lifecycle-phase">🔧 onCreate() 初始化操作</text>
  
  <rect x="250" y="180" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="3"/>
  <text x="310" y="195" text-anchor="middle" class="step-text">setContentView()</text>
  <text x="310" y="210" text-anchor="middle" class="step-text">initParam()</text>
  <text x="310" y="225" text-anchor="middle" class="step-text">initView()</text>
  <text x="310" y="240" text-anchor="middle" class="step-text">initListener()</text>
  
  <!-- 3. onStart -->
  <rect x="1050" y="260" width="300" height="30" fill="#e8f5e8" stroke="#4caf50" stroke-width="1" rx="3"/>
  <text x="1200" y="280" text-anchor="middle" class="lifecycle-phase">🔗 onStart() 服务绑定</text>
  
  <line x1="310" y1="300" x2="510" y2="300" class="arrow"/>
  <text x="410" y="295" text-anchor="middle" class="message-text">bindService(GazeTrackService)</text>
  
  <rect x="520" y="310" width="80" height="20" fill="#27ae60" stroke="#229954" stroke-width="1" rx="3"/>
  <text x="560" y="325" text-anchor="middle" style="font-size: 10px; fill: white;">Service激活</text>
  
  <line x1="510" y1="340" x2="310" y2="340" class="return-arrow"/>
  <text x="410" y="335" text-anchor="middle" class="message-text">ServiceConnection建立</text>
  
  <!-- 4. onResume -->
  <rect x="1050" y="360" width="300" height="30" fill="#fff3e0" stroke="#ff9800" stroke-width="1" rx="3"/>
  <text x="1200" y="380" text-anchor="middle" class="lifecycle-phase">▶️ onResume() 开始用户交互</text>
  
  <!-- 5. 倒计时完成后 -->
  <rect x="1050" y="410" width="300" height="30" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1" rx="3"/>
  <text x="1200" y="430" text-anchor="middle" class="lifecycle-phase">⏰ 倒计时完成后</text>
  
  <rect x="250" y="450" width="120" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
  <text x="310" y="465" text-anchor="middle" style="font-size: 10px; fill: white;">startRead()</text>
  
  <line x1="310" y1="480" x2="510" y2="480" class="arrow"/>
  <text x="410" y="475" text-anchor="middle" class="message-text">MSG_TURN_ON_CAMERA</text>
  
  <line x1="510" y1="500" x2="710" y2="500" class="arrow"/>
  <text x="610" y="495" text-anchor="middle" class="message-text">开启摄像头</text>
  
  <rect x="720" y="510" width="80" height="20" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
  <text x="760" y="525" text-anchor="middle" style="font-size: 10px; fill: white;">摄像头激活</text>
  
  <line x1="310" y1="540" x2="510" y2="540" class="arrow"/>
  <text x="410" y="535" text-anchor="middle" class="message-text">MSG_START_TRACK</text>
  
  <line x1="510" y1="560" x2="910" y2="560" class="arrow"/>
  <text x="710" y="555" text-anchor="middle" class="message-text">开始眼动追踪</text>
  
  <rect x="920" y="570" width="80" height="20" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="3"/>
  <text x="960" y="585" text-anchor="middle" style="font-size: 10px; fill: white;">追踪激活</text>
  
  <line x1="310" y1="600" x2="510" y2="600" class="arrow"/>
  <text x="410" y="595" text-anchor="middle" class="message-text">MSG_START_APPLIED_READING</text>
  
  <line x1="510" y1="620" x2="910" y2="620" class="arrow"/>
  <text x="710" y="615" text-anchor="middle" class="message-text">开始应用阅读模式</text>
  
  <!-- 6. 用户进行阅读测试 -->
  <rect x="1050" y="650" width="300" height="30" fill="#e8f5e8" stroke="#4caf50" stroke-width="1" rx="3"/>
  <text x="1200" y="670" text-anchor="middle" class="lifecycle-phase">📖 用户进行阅读测试</text>
  
  <!-- 7. 用户点击完成 -->
  <line x1="110" y1="700" x2="310" y2="700" class="arrow"/>
  <text x="210" y="695" text-anchor="middle" class="message-text">点击完成阅读</text>
  
  <rect x="250" y="710" width="120" height="40" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="3"/>
  <text x="310" y="725" text-anchor="middle" class="step-text">isFinishRead = true</text>
  <text x="310" y="740" text-anchor="middle" class="step-text">endTime = currentTimeMillis()</text>
  
  <line x1="310" y1="760" x2="510" y2="760" class="arrow"/>
  <text x="410" y="755" text-anchor="middle" class="message-text">MSG_GET_GAZE_TRAJECTORY</text>
  
  <line x1="510" y1="780" x2="310" y2="780" class="return-arrow"/>
  <text x="410" y="775" text-anchor="middle" class="message-text">返回眼动轨迹数据</text>
  
  <!-- 8. 处理数据并跳转 -->
  <rect x="250" y="790" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="3"/>
  <text x="310" y="805" text-anchor="middle" class="step-text">parseMessage()</text>
  <text x="310" y="820" text-anchor="middle" class="step-text">创建ReadResult</text>
  <text x="310" y="835" text-anchor="middle" class="step-text">LiveEventBus.post()</text>
  <text x="310" y="850" text-anchor="middle" class="step-text">startActivity() & finish()</text>

  <!-- 9. onPause 阶段 -->
  <rect x="1050" y="870" width="300" height="30" fill="#fce4ec" stroke="#e91e63" stroke-width="1" rx="3"/>
  <text x="1200" y="890" text-anchor="middle" class="lifecycle-phase">⏸️ onPause() 暂停交互</text>

  <!-- 10. onStop 阶段 -->
  <rect x="1050" y="920" width="300" height="30" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1" rx="3"/>
  <text x="1200" y="940" text-anchor="middle" class="lifecycle-phase">⏹️ onStop() 停止操作</text>

  <line x1="310" y1="960" x2="510" y2="960" class="arrow"/>
  <text x="410" y="955" text-anchor="middle" class="message-text">MSG_STOP_APPLIED_READING</text>

  <line x1="510" y1="980" x2="910" y2="980" class="arrow"/>
  <text x="710" y="975" text-anchor="middle" class="message-text">停止应用阅读模式</text>

  <line x1="310" y1="1000" x2="510" y2="1000" class="arrow"/>
  <text x="410" y="995" text-anchor="middle" class="message-text">MSG_STOP_TRACK</text>

  <line x1="510" y1="1020" x2="910" y2="1020" class="arrow"/>
  <text x="710" y="1015" text-anchor="middle" class="message-text">停止眼动追踪</text>

  <rect x="920" y="1030" width="80" height="20" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="3"/>
  <text x="960" y="1045" text-anchor="middle" style="font-size: 10px; fill: white;">追踪停止</text>

  <line x1="310" y1="1060" x2="510" y2="1060" class="arrow"/>
  <text x="410" y="1055" text-anchor="middle" class="message-text">MSG_TURN_OFF_CAMERA</text>

  <line x1="510" y1="1080" x2="710" y2="1080" class="arrow"/>
  <text x="610" y="1075" text-anchor="middle" class="message-text">关闭摄像头</text>

  <rect x="720" y="1090" width="80" height="20" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="3"/>
  <text x="760" y="1105" text-anchor="middle" style="font-size: 10px; fill: white;">摄像头关闭</text>

  <line x1="310" y1="1120" x2="510" y2="1120" class="arrow"/>
  <text x="410" y="1115" text-anchor="middle" class="message-text">unbindService()</text>

  <rect x="520" y="1130" width="80" height="20" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="3"/>
  <text x="560" y="1145" text-anchor="middle" style="font-size: 10px; fill: white;">服务解绑</text>

  <!-- 11. onDestroy 阶段 -->
  <rect x="1050" y="1160" width="300" height="30" fill="#ffebee" stroke="#f44336" stroke-width="1" rx="3"/>
  <text x="1200" y="1180" text-anchor="middle" class="lifecycle-phase">🗑️ onDestroy() 资源清理</text>

  <rect x="250" y="1190" width="120" height="20" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="310" y="1205" text-anchor="middle" style="font-size: 10px; fill: white;">Activity销毁</text>

</svg>
