<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .enum-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .native-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .jni-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .java-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .service-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .new-mode-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 3; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .step-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">新增AppliedMode模式完整扩展指南</text>
  <text x="800" y="55" text-anchor="middle" class="description-text">以添加SACCADE(扫视能力增强)模式为例</text>
  
  <!-- 步骤1: 枚举定义 -->
  <rect x="50" y="80" width="1500" height="120" class="enum-box" rx="8"/>
  <text x="800" y="105" text-anchor="middle" class="subtitle">步骤1: 修改AppliedMode枚举 (Java层)</text>
  
  <rect x="80" y="120" width="700" height="70" class="new-mode-box" rx="5"/>
  <text x="430" y="140" text-anchor="middle" class="step-title">AppliedMode.kt 修改</text>
  <text x="90" y="160" class="code-text">enum class AppliedMode(val code:Int) {</text>
  <text x="100" y="175" class="code-text">    // ... 现有模式 ...</text>
  <text x="100" y="190" class="code-text">    GLANCE(5),</text>
  <text x="100" y="205" class="code-text">    SACCADE(6)  // 新增：扫视能力增强模式</text>
  
  <rect x="800" y="120" width="700" height="70" class="new-mode-box" rx="5"/>
  <text x="1150" y="140" text-anchor="middle" class="step-title">utils.h 修改 (Native层枚举)</text>
  <text x="810" y="160" class="code-text">typedef enum {</text>
  <text x="820" y="175" class="code-text">    // ... 现有模式 ...</text>
  <text x="820" y="190" class="code-text">    APP_GLANCE = 5,</text>
  <text x="820" y="205" class="code-text">    APP_SACCADE = 6  // 新增</text>
  
  <!-- 步骤2: Native层实现 -->
  <rect x="50" y="220" width="1500" height="200" class="native-box" rx="8"/>
  <text x="800" y="245" text-anchor="middle" class="subtitle">步骤2: 创建Native层模式实现</text>
  
  <!-- 创建头文件 -->
  <rect x="80" y="270" width="450" height="130" class="new-mode-box" rx="5"/>
  <text x="305" y="290" text-anchor="middle" class="step-title">创建 app_saccade.h</text>
  <text x="90" y="310" class="code-text">#ifndef MIT_DD_DEMO_APP_SACCADE_H</text>
  <text x="90" y="325" class="code-text">#define MIT_DD_DEMO_APP_SACCADE_H</text>
  <text x="90" y="340" class="code-text">#include "utils.h"</text>
  <text x="90" y="355" class="code-text">class GazeSaccade {</text>
  <text x="100" y="370" class="code-text">    std::vector&lt;cv::Point3f&gt; gaze_data_list;</text>
  <text x="100" y="385" class="code-text">    void collect_data(float x, float y, float duration);</text>
  <text x="100" y="400" class="code-text">    string get_record_jsondata();</text>
  
  <!-- 创建实现文件 -->
  <rect x="550" y="270" width="450" height="130" class="new-mode-box" rx="5"/>
  <text x="775" y="290" text-anchor="middle" class="step-title">创建 app_saccade.cpp</text>
  <text x="560" y="310" class="code-text">void GazeSaccade::collect_data(float x, float y, float duration) {</text>
  <text x="570" y="325" class="code-text">    // 扫视能力增强的特殊逻辑</text>
  <text x="570" y="340" class="code-text">    gaze_data_list.emplace_back(Point3f(x, y, duration));</text>
  <text x="560" y="355" class="code-text">}</text>
  <text x="560" y="370" class="code-text">string GazeSaccade::get_record_jsondata() {</text>
  <text x="570" y="385" class="code-text">    // 返回扫视能力增强的JSON数据</text>
  <text x="560" y="400" class="code-text">}</text>
  
  <!-- 修改GazeApplication -->
  <rect x="1020" y="270" width="450" height="130" class="new-mode-box" rx="5"/>
  <text x="1245" y="290" text-anchor="middle" class="step-title">修改 GazeApplication.h</text>
  <text x="1030" y="310" class="code-text">#include "app_saccade.h"</text>
  <text x="1030" y="325" class="code-text">class GazeApplication {</text>
  <text x="1040" y="340" class="code-text">private:</text>
  <text x="1050" y="355" class="code-text">    // ... 现有模型 ...</text>
  <text x="1050" y="370" class="code-text">    GazeSaccade saccade_model;  // 新增</text>
  <text x="1030" y="385" class="code-text">};</text>
  
  <!-- 步骤3: 修改GazeApplication.cpp -->
  <rect x="50" y="440" width="1500" height="280" class="native-box" rx="8"/>
  <text x="800" y="465" text-anchor="middle" class="subtitle">步骤3: 修改GazeApplication.cpp核心方法</text>
  
  <!-- start_application -->
  <rect x="80" y="490" width="450" height="210" class="new-mode-box" rx="5"/>
  <text x="305" y="510" text-anchor="middle" class="step-title">修改 start_application()</text>
  <text x="90" y="530" class="code-text">bool GazeApplication::start_application(int mode) {</text>
  <text x="100" y="545" class="code-text">    if (mode >= APP_PQBLUR && mode <= APP_SACCADE) {  // 更新范围</text>
  <text x="110" y="560" class="code-text">        switch (mode) {</text>
  <text x="120" y="575" class="code-text">            // ... 现有case ...</text>
  <text x="120" y="590" class="code-text">            case app_typename::APP_SACCADE:</text>
  <text x="130" y="605" class="code-text">                saccade_model.reset_params_func();</text>
  <text x="130" y="620" class="code-text">                break;</text>
  <text x="110" y="635" class="code-text">        }</text>
  <text x="100" y="650" class="code-text">    }</text>
  <text x="90" y="665" class="code-text">}</text>
  
  <!-- collect_gaze -->
  <rect x="550" y="490" width="450" height="210" class="new-mode-box" rx="5"/>
  <text x="775" y="510" text-anchor="middle" class="step-title">修改 collect_gaze()</text>
  <text x="560" y="530" class="code-text">bool GazeApplication::collect_gaze(...) {</text>
  <text x="570" y="545" class="code-text">    switch (app_mode) {</text>
  <text x="580" y="560" class="code-text">        // ... 现有case ...</text>
  <text x="580" y="575" class="code-text">        case app_typename::APP_SACCADE:</text>
  <text x="590" y="590" class="code-text">            saccade_model.collect_data(x, y, duration);</text>
  <text x="590" y="605" class="code-text">            break;</text>
  <text x="570" y="620" class="code-text">    }</text>
  <text x="560" y="635" class="code-text">}</text>
  
  <!-- get_record_data_func -->
  <rect x="1020" y="490" width="450" height="210" class="new-mode-box" rx="5"/>
  <text x="1245" y="510" text-anchor="middle" class="step-title">修改 get_record_data_func()</text>
  <text x="1030" y="530" class="code-text">string GazeApplication::get_record_data_func(int mode) {</text>
  <text x="1040" y="545" class="code-text">    switch (mode) {</text>
  <text x="1050" y="560" class="code-text">        // ... 现有case ...</text>
  <text x="1050" y="575" class="code-text">        case app_typename::APP_SACCADE:</text>
  <text x="1060" y="590" class="code-text">            result_dict = saccade_model.get_record_jsondata();</text>
  <text x="1060" y="605" class="code-text">            break;</text>
  <text x="1040" y="620" class="code-text">    }</text>
  <text x="1030" y="635" class="code-text">}</text>
  
  <!-- 步骤4: JNI层扩展 -->
  <rect x="50" y="740" width="1500" height="180" class="jni-box" rx="8"/>
  <text x="800" y="765" text-anchor="middle" class="subtitle">步骤4: JNI层扩展 (可选参数设置)</text>
  
  <!-- GazeApplied.kt -->
  <rect x="80" y="790" width="700" height="110" class="new-mode-box" rx="5"/>
  <text x="430" y="810" text-anchor="middle" class="step-title">GazeApplied.kt 添加参数设置方法</text>
  <text x="90" y="830" class="code-text">/**</text>
  <text x="90" y="845" class="code-text"> * 设置扫视能力增强参数，[AppliedMode.SACCADE]模式下使用</text>
  <text x="90" y="860" class="code-text"> */</text>
  <text x="90" y="875" class="code-text">fun setSaccadeParams(difficulty: Int, speed: Float): Boolean {</text>
  <text x="100" y="890" class="code-text">    return nativeSetSaccadeParams(nativeObj.get(), difficulty, speed)</text>
  
  <!-- native-lib.cpp -->
  <rect x="800" y="790" width="700" height="110" class="new-mode-box" rx="5"/>
  <text x="1150" y="810" text-anchor="middle" class="step-title">native-lib.cpp 添加JNI方法</text>
  <text x="810" y="830" class="code-text">extern "C" JNIEXPORT jboolean JNICALL</text>
  <text x="810" y="845" class="code-text">Java_..._nativeSetSaccadeParams(JNIEnv *env, jclass clazz,</text>
  <text x="810" y="860" class="code-text">                                jlong thiz, jint difficulty, jfloat speed) {</text>
  <text x="820" y="875" class="code-text">    auto *app = reinterpret_cast&lt;GazeApplication *&gt;(thiz);</text>
  <text x="820" y="890" class="code-text">    return app-&gt;set_saccade_params_func(difficulty, speed);</text>
  
  <!-- 步骤5: Java层管理器扩展 -->
  <rect x="50" y="940" width="1500" height="180" class="java-box" rx="8"/>
  <text x="800" y="965" text-anchor="middle" class="subtitle">步骤5: Java层管理器扩展</text>
  
  <!-- AppliedManager.kt -->
  <rect x="80" y="990" width="700" height="110" class="new-mode-box" rx="5"/>
  <text x="430" y="1010" text-anchor="middle" class="step-title">AppliedManager.kt 添加启动方法</text>
  <text x="90" y="1030" class="code-text">/**</text>
  <text x="90" y="1045" class="code-text"> * 启动扫视能力增强模式</text>
  <text x="90" y="1060" class="code-text"> */</text>
  <text x="90" y="1075" class="code-text">fun startAppliedSaccade(): Int {</text>
  <text x="100" y="1090" class="code-text">    return startApplied(SACCADE)</text>
  
  <!-- 回调处理 -->
  <rect x="800" y="990" width="700" height="110" class="new-mode-box" rx="5"/>
  <text x="1150" y="1010" text-anchor="middle" class="step-title">NativeGazeAppliedCallback 添加处理</text>
  <text x="810" y="1030" class="code-text">fun onGazeAppliedModeChange(mode: Int) {</text>
  <text x="820" y="1045" class="code-text">    when (mode) {</text>
  <text x="830" y="1060" class="code-text">        // ... 现有case ...</text>
  <text x="830" y="1075" class="code-text">        AppliedMode.SACCADE.code -> {</text>
  <text x="840" y="1090" class="code-text">            internalListener?.onGazeAppliedModeChange(AppliedMode.SACCADE)</text>
  
  <!-- 步骤6: Service层集成 -->
  <rect x="50" y="1140" width="1500" height="180" class="service-box" rx="8"/>
  <text x="800" y="1165" text-anchor="middle" class="subtitle">步骤6: Service层集成</text>
  
  <!-- GazeConstants -->
  <rect x="80" y="1190" width="450" height="110" class="new-mode-box" rx="5"/>
  <text x="305" y="1210" text-anchor="middle" class="step-title">GazeConstants.kt 添加消息常量</text>
  <text x="90" y="1230" class="code-text">object GazeConstants {</text>
  <text x="100" y="1245" class="code-text">    // ... 现有常量 ...</text>
  <text x="100" y="1260" class="code-text">    const val MSG_START_APPLIED_SACCADE = 1016</text>
  <text x="100" y="1275" class="code-text">    const val MSG_STOP_APPLIED_SACCADE = 1017</text>
  <text x="90" y="1290" class="code-text">}</text>
  
  <!-- GazeTrackService -->
  <rect x="550" y="1190" width="450" height="110" class="new-mode-box" rx="5"/>
  <text x="775" y="1210" text-anchor="middle" class="step-title">GazeTrackService.kt 添加消息处理</text>
  <text x="560" y="1230" class="code-text">override fun parseMessage(msg: Message) {</text>
  <text x="570" y="1245" class="code-text">    when (msg.what) {</text>
  <text x="580" y="1260" class="code-text">        MSG_START_APPLIED_SACCADE -> {</text>
  <text x="590" y="1275" class="code-text">            AppliedManager.startAppliedSaccade()</text>
  <text x="580" y="1290" class="code-text">        }</text>
  
  <!-- 应用层使用 -->
  <rect x="1020" y="1190" width="450" height="110" class="new-mode-box" rx="5"/>
  <text x="1245" y="1210" text-anchor="middle" class="step-title">Activity/Fragment 使用</text>
  <text x="1030" y="1230" class="code-text">// 启动扫视能力增强模式</text>
  <text x="1030" y="1245" class="code-text">sendMessageToService(Message.obtain().apply {</text>
  <text x="1040" y="1260" class="code-text">    what = GazeConstants.MSG_START_APPLIED_SACCADE</text>
  <text x="1030" y="1275" class="code-text">})</text>
  
  <!-- 步骤流程箭头 -->
  <line x1="800" y1="200" x2="800" y2="220" class="step-arrow"/>
  <line x1="800" y1="420" x2="800" y2="440" class="step-arrow"/>
  <line x1="800" y1="720" x2="800" y2="740" class="step-arrow"/>
  <line x1="800" y1="920" x2="800" y2="940" class="step-arrow"/>
  <line x1="800" y1="1120" x2="800" y2="1140" class="step-arrow"/>
  
  <!-- 总结 -->
  <rect x="50" y="1340" width="1500" height="120" class="new-mode-box" rx="8"/>
  <text x="800" y="1365" text-anchor="middle" class="subtitle">扩展完成检查清单</text>
  
  <text x="80" y="1390" class="step-text" font-weight="bold">✓ 必须修改的文件：</text>
  <text x="100" y="1405" class="step-text">1. AppliedMode.kt (枚举定义) 2. utils.h (Native枚举) 3. app_saccade.h/.cpp (新模式实现)</text>
  <text x="100" y="1420" class="step-text">4. GazeApplication.h/.cpp (集成新模式) 5. GazeApplied.kt (JNI接口) 6. AppliedManager.kt (管理器)</text>
  <text x="100" y="1435" class="step-text">7. GazeConstants.kt (消息常量) 8. GazeTrackService.kt (服务处理) 9. native-lib.cpp (JNI实现)</text>
  
  <text x="800" y="1390" class="step-text" font-weight="bold">✓ 可选扩展：</text>
  <text x="820" y="1405" class="step-text">• 特殊参数设置方法 • 专用UI界面 • 数据分析算法</text>
  <text x="820" y="1420" class="step-text">• 结果展示页面 • API接口集成 • 医疗报告生成</text>
  
  <text x="1200" y="1390" class="step-text" font-weight="bold">✓ 测试验证：</text>
  <text x="1220" y="1405" class="step-text">• 模式启动/停止 • 数据收集</text>
  <text x="1220" y="1420" class="step-text">• JSON数据格式 • 回调机制</text>
  <text x="1220" y="1435" class="step-text">• 内存泄漏检查 • 性能测试</text>
</svg>
