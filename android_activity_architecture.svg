<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://schemas.android.com/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 12px; fill: #2c3e50; }
      .manifest-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; }
      .activity-box { fill: #3498db; stroke: #2980b9; stroke-width: 3; }
      .xml-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 3; }
      .fragment-box { fill: #f39c12; stroke: #e67e22; stroke-width: 3; }
      .lifecycle-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; }
      .view-box { fill: #1abc9c; stroke: #16a085; stroke-width: 3; }
      .system-box { fill: #34495e; stroke: #2c3e50; stroke-width: 3; }
      .arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #7f8c8d; stroke-width: 2; stroke-dasharray: 8,4; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #e67e22; stroke-width: 4; fill: none; marker-end: url(#flow-arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#34495e"/>
    </marker>
    <marker id="flow-arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e67e22"/>
    </marker>
    <linearGradient id="manifestGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="activityGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景渐变 -->
  <defs>
    <radialGradient id="bgGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bgGrad)"/>

  <!-- 标题区域 -->
  <rect x="50" y="20" width="1500" height="80" fill="#2c3e50" stroke="#34495e" stroke-width="2" rx="15"/>
  <text x="800" y="50" text-anchor="middle" class="title" fill="white">Android Activity 架构组成详解</text>
  <text x="800" y="75" text-anchor="middle" class="subtitle" fill="#ecf0f1">从配置文件到UI组件的完整结构和加载流程</text>

  <!-- 系统层 -->
  <rect x="50" y="120" width="1500" height="60" class="system-box" rx="10"/>
  <text x="800" y="140" text-anchor="middle" class="subtitle" fill="white">Android 系统层</text>
  <text x="80" y="160" class="text" fill="white">• ActivityManagerService • PackageManagerService • WindowManagerService • 应用进程管理</text>

  <!-- AndroidManifest.xml 配置层 -->
  <rect x="50" y="200" width="1500" height="140" fill="url(#manifestGrad)" stroke="#c0392b" stroke-width="3" rx="15"/>
  <text x="800" y="230" text-anchor="middle" class="subtitle" fill="white">📋 AndroidManifest.xml 配置层</text>

  <!-- Manifest 代码示例 -->
  <rect x="80" y="250" width="680" height="70" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="90" y="270" class="code-text" fill="white">&lt;activity android:name=".MainActivity"</text>
  <text x="90" y="285" class="code-text" fill="white">    android:launchMode="singleTop"</text>
  <text x="90" y="300" class="code-text" fill="white">    android:exported="true"&gt;</text>
  <text x="90" y="315" class="code-text" fill="white">    &lt;intent-filter&gt;...&lt;/intent-filter&gt;</text>

  <rect x="780" y="250" width="680" height="70" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="790" y="270" class="text" fill="white">• 主题配置: android:theme="@style/AppTheme"</text>
  <text x="790" y="285" class="text" fill="white">• 配置变更: android:configChanges="orientation"</text>
  <text x="790" y="300" class="text" fill="white">• 权限声明: &lt;uses-permission&gt;</text>
  <text x="790" y="315" class="text" fill="white">• 应用组件注册和配置</text>

  <!-- Activity 类层 -->
  <rect x="50" y="360" width="720" height="200" fill="url(#activityGrad)" stroke="#2980b9" stroke-width="3" rx="15"/>
  <text x="410" y="385" text-anchor="middle" class="subtitle" fill="white">🏗️ Activity 类结构</text>

  <!-- Activity 代码示例 -->
  <rect x="80" y="400" width="660" height="90" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="90" y="420" class="code-text" fill="white">class MainActivity : AppCompatActivity() {</text>
  <text x="90" y="435" class="code-text" fill="white">    override fun onCreate(savedInstanceState: Bundle?) {</text>
  <text x="90" y="450" class="code-text" fill="white">        super.onCreate(savedInstanceState)</text>
  <text x="90" y="465" class="code-text" fill="white">        setContentView(R.layout.activity_main)</text>
  <text x="90" y="480" class="code-text" fill="white">    }</text>

  <text x="80" y="510" class="text" fill="white">• 继承关系: AppCompatActivity → FragmentActivity → Activity</text>
  <text x="80" y="530" class="text" fill="white">• 生命周期管理 • 布局加载 • Fragment管理 • 事件处理</text>
  <text x="80" y="545" class="text" fill="white">• Intent处理 • 权限管理 • 状态保存与恢复</text>

  <!-- Activity 生命周期 -->
  <rect x="800" y="360" width="750" height="200" class="lifecycle-box" rx="15"/>
  <text x="1175" y="385" text-anchor="middle" class="subtitle" fill="white">🔄 Activity 生命周期</text>

  <!-- 生命周期流程图 -->
  <circle cx="900" cy="420" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="900" y="427" text-anchor="middle" class="small-text" fill="white">onCreate</text>

  <circle cx="1050" cy="420" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1050" y="427" text-anchor="middle" class="small-text" fill="white">onStart</text>

  <circle cx="1200" cy="420" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1200" y="427" text-anchor="middle" class="small-text" fill="white">onResume</text>

  <circle cx="1350" cy="420" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1350" y="427" text-anchor="middle" class="small-text" fill="white">onPause</text>

  <circle cx="1200" cy="480" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1200" y="487" text-anchor="middle" class="small-text" fill="white">onStop</text>

  <circle cx="1050" cy="480" r="25" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1050" y="487" text-anchor="middle" class="small-text" fill="white">onDestroy</text>

  <!-- 生命周期箭头 -->
  <line x1="925" y1="420" x2="1025" y2="420" stroke="white" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1075" y1="420" x2="1175" y2="420" stroke="white" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1225" y1="420" x2="1325" y2="420" stroke="white" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1350" y1="445" x2="1200" y2="455" stroke="white" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1175" y1="480" x2="1075" y2="480" stroke="white" stroke-width="2" marker-end="url(#arrowhead)"/>

  <text x="830" y="520" class="text" fill="white">创建 → 启动 → 恢复 → 暂停 → 停止 → 销毁</text>
  <text x="830" y="540" class="text" fill="white">• onSaveInstanceState() • onRestoreInstanceState() • onRestart()</text>

  <!-- XML 布局层 -->
  <rect x="50" y="580" width="720" height="220" class="xml-box" rx="15"/>
  <text x="410" y="605" text-anchor="middle" class="subtitle" fill="white">📄 XML 布局文件结构</text>

  <!-- XML 代码示例 -->
  <rect x="80" y="620" width="660" height="110" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="90" y="640" class="code-text" fill="white">&lt;ConstraintLayout xmlns:android="..."</text>
  <text x="90" y="655" class="code-text" fill="white">    android:layout_width="match_parent"</text>
  <text x="90" y="670" class="code-text" fill="white">    android:layout_height="match_parent"&gt;</text>
  <text x="90" y="685" class="code-text" fill="white">    &lt;TextView android:id="@+id/textView" .../&gt;</text>
  <text x="90" y="700" class="code-text" fill="white">    &lt;Button android:id="@+id/button" .../&gt;</text>
  <text x="90" y="715" class="code-text" fill="white">    &lt;FrameLayout android:id="@+id/fragment_container"/&gt;</text>
  <text x="90" y="730" class="code-text" fill="white">&lt;/ConstraintLayout&gt;</text>

  <text x="80" y="755" class="text" fill="white">• 根布局容器: LinearLayout, RelativeLayout, ConstraintLayout</text>
  <text x="80" y="770" class="text" fill="white">• ViewGroup容器 • View基础组件 • 自定义View • Fragment容器</text>
  <text x="80" y="785" class="text" fill="white">• 属性配置: id, layout_width, layout_height, 约束关系</text>

  <!-- Fragment 层 -->
  <rect x="800" y="580" width="750" height="220" class="fragment-box" rx="15"/>
  <text x="1175" y="605" text-anchor="middle" class="subtitle" fill="white">🧩 Fragment 组件结构</text>

  <!-- Fragment 代码示例 -->
  <rect x="830" y="620" width="690" height="110" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="840" y="640" class="code-text" fill="white">class MainFragment : Fragment() {</text>
  <text x="840" y="655" class="code-text" fill="white">    override fun onCreateView(inflater: LayoutInflater,</text>
  <text x="840" y="670" class="code-text" fill="white">        container: ViewGroup?, savedInstanceState: Bundle?)</text>
  <text x="840" y="685" class="code-text" fill="white">        : View? {</text>
  <text x="840" y="700" class="code-text" fill="white">        return inflater.inflate(R.layout.fragment_main,</text>
  <text x="840" y="715" class="code-text" fill="white">            container, false)</text>
  <text x="840" y="730" class="code-text" fill="white">    }</text>

  <text x="830" y="755" class="text" fill="white">• Fragment类: 继承Fragment/BaseFragment</text>
  <text x="830" y="770" class="text" fill="white">• 生命周期: onAttach → onCreate → onCreateView → onStart</text>
  <text x="830" y="785" class="text" fill="white">• FragmentManager管理 • 参数传递Bundle • 与Activity通信</text>

  <!-- View 组件层 -->
  <rect x="50" y="820" width="1500" height="160" class="view-box" rx="15"/>
  <text x="800" y="845" text-anchor="middle" class="subtitle" fill="white">🎨 View 组件层级结构</text>

  <!-- View 层级示意图 -->
  <rect x="80" y="860" width="200" height="100" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="5"/>
  <text x="180" y="880" text-anchor="middle" class="text" fill="white">ViewGroup</text>
  <rect x="100" y="890" width="70" height="30" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="3"/>
  <text x="135" y="908" text-anchor="middle" class="small-text" fill="white">TextView</text>
  <rect x="190" y="890" width="70" height="30" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="3"/>
  <text x="225" y="908" text-anchor="middle" class="small-text" fill="white">Button</text>

  <text x="320" y="875" class="text" fill="white">• 基础组件: TextView, Button, ImageView, EditText, CheckBox</text>
  <text x="320" y="895" class="text" fill="white">• 容器组件: LinearLayout, RelativeLayout, ConstraintLayout, FrameLayout</text>
  <text x="320" y="915" class="text" fill="white">• 列表组件: RecyclerView, ListView, GridView, ViewPager2</text>
  <text x="320" y="935" class="text" fill="white">• 高级组件: TabLayout, NavigationView, CoordinatorLayout, AppBarLayout</text>
  <text x="320" y="955" class="text" fill="white">• 自定义View: 继承View或ViewGroup，重写onDraw(), onMeasure(), onLayout()</text>

  <text x="1000" y="875" class="text" fill="white">📱 Material Design 组件:</text>
  <text x="1000" y="895" class="text" fill="white">• FloatingActionButton • Snackbar • BottomSheet</text>
  <text x="1000" y="915" class="text" fill="white">• CardView • Toolbar • DrawerLayout</text>
  <text x="1000" y="935" class="text" fill="white">• 第三方库: Glide, Retrofit, OkHttp</text>
  <text x="1000" y="955" class="text" fill="white">• 数据绑定: ViewBinding, DataBinding</text>

  <!-- 数据流和关系箭头 -->
  <line x1="800" y1="180" x2="410" y2="360" class="flow-arrow"/>
  <line x1="800" y1="180" x2="1175" y2="360" class="flow-arrow"/>
  <line x1="410" y1="560" x2="410" y2="580" class="flow-arrow"/>
  <line x1="1175" y1="560" x2="1175" y2="580" class="flow-arrow"/>
  <line x1="410" y1="800" x2="800" y2="820" class="flow-arrow"/>
  <line x1="1175" y1="800" x2="800" y2="820" class="flow-arrow"/>
  <line x1="770" y1="680" x2="800" y2="680" class="dashed-arrow"/>

  <!-- 加载流程说明 -->
  <rect x="50" y="1000" width="1500" height="180" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="800" y="1025" text-anchor="middle" class="subtitle">🔄 组件关系和加载流程详解</text>

  <!-- 流程步骤 -->
  <circle cx="100" cy="1050" r="15" fill="#e74c3c"/>
  <text x="100" y="1057" text-anchor="middle" class="small-text" fill="white">1</text>
  <text x="130" y="1055" class="text">AndroidManifest.xml 声明Activity，系统根据配置创建Activity实例</text>

  <circle cx="100" cy="1075" r="15" fill="#3498db"/>
  <text x="100" y="1082" text-anchor="middle" class="small-text" fill="white">2</text>
  <text x="130" y="1080" class="text">Activity.onCreate() 调用 setContentView() 加载XML布局文件</text>

  <circle cx="100" cy="1100" r="15" fill="#2ecc71"/>
  <text x="100" y="1107" text-anchor="middle" class="small-text" fill="white">3</text>
  <text x="130" y="1105" class="text">XML布局文件被解析为View树结构，包含各种View组件和Fragment容器</text>

  <circle cx="800" cy="1050" r="15" fill="#f39c12"/>
  <text x="800" y="1057" text-anchor="middle" class="small-text" fill="white">4</text>
  <text x="830" y="1055" class="text">Fragment通过FragmentManager动态加载到Activity中，拥有独立的生命周期</text>

  <circle cx="800" cy="1075" r="15" fill="#1abc9c"/>
  <text x="800" y="1082" text-anchor="middle" class="small-text" fill="white">5</text>
  <text x="830" y="1080" class="text">View组件通过findViewById()或ViewBinding绑定到Activity/Fragment中进行交互</text>

  <circle cx="800" cy="1100" r="15" fill="#9b59b6"/>
  <text x="800" y="1107" text-anchor="middle" class="small-text" fill="white">6</text>
  <text x="830" y="1105" class="text">整个架构支持模块化开发，Fragment可复用，View可自定义扩展</text>

  <!-- 技术要点 -->
  <text x="100" y="1140" class="subtitle">🔧 技术要点:</text>
  <text x="100" y="1160" class="text">• 生命周期管理: Activity和Fragment都有完整的生命周期，需要正确处理状态保存和恢复</text>
  <text x="100" y="1175" class="text">• 内存管理: 避免内存泄漏，正确使用WeakReference，及时释放资源</text>

  <!-- 底部说明 -->
  <text x="800" y="1200" text-anchor="middle" class="small-text">💡 这个架构图展示了Android Activity从配置到UI组件的完整层级结构和加载流程</text>

</svg>
