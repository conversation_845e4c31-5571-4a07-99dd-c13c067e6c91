<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .role-text { font-family: Arial, sans-serif; font-size: 13px; fill: #e74c3c; font-weight: bold; }
      .manifest-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .fragment-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .xml-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .view-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .lifecycle-box { fill: #f0f8ff; stroke: #4169e1; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #7f8c8d; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">Android Activity 详细结构与作用解析</text>

  <!-- AndroidManifest.xml 配置区域 -->
  <rect x="50" y="60" width="700" height="280" class="manifest-box" rx="10"/>
  <text x="400" y="85" text-anchor="middle" class="subtitle">📋 AndroidManifest.xml 配置</text>
  <text x="400" y="105" text-anchor="middle" class="role-text">作用：声明Activity组件，定义启动方式和权限</text>

  <text x="70" y="135" class="text">&lt;application&gt;</text>
  <text x="90" y="155" class="text">&lt;activity</text>
  <text x="110" y="175" class="small-text">android:name=".LauncherActivity"</text>
  <text x="450" y="175" class="role-text">→ 指定Activity类名</text>
  <text x="110" y="195" class="small-text">android:launchMode="singleTop"</text>
  <text x="450" y="195" class="role-text">→ 控制Activity启动模式</text>
  <text x="110" y="215" class="small-text">android:exported="true"</text>
  <text x="450" y="215" class="role-text">→ 允许外部应用启动</text>
  <text x="110" y="235" class="small-text">android:theme="@style/LauncherActivityTheme"&gt;</text>
  <text x="450" y="235" class="role-text">→ 设置Activity主题样式</text>
  <text x="130" y="255" class="small-text">&lt;intent-filter&gt;</text>
  <text x="450" y="255" class="role-text">→ 定义启动条件和过滤器</text>
  <text x="150" y="275" class="small-text">&lt;action android:name="android.intent.action.MAIN" /&gt;</text>
  <text x="150" y="295" class="small-text">&lt;category android:name="android.intent.category.LAUNCHER" /&gt;</text>
  <text x="130" y="315" class="small-text">&lt;/intent-filter&gt;</text>
  <text x="90" y="330" class="text">&lt;/activity&gt;</text>

  <!-- Activity 类结构 -->
  <rect x="800" y="60" width="750" height="380" class="activity-box" rx="10"/>
  <text x="1175" y="85" text-anchor="middle" class="subtitle">🏗️ Activity 类结构</text>
  <text x="1175" y="105" text-anchor="middle" class="role-text">作用：应用界面的主要容器，管理UI和业务逻辑</text>

  <text x="820" y="135" class="text">class ReadActivity : GTBaseActivity() {</text>

  <text x="840" y="160" class="text">📍 onCreate(savedInstanceState: Bundle?)</text>
  <text x="860" y="180" class="small-text">- super.onCreate(savedInstanceState)</text>
  <text x="1200" y="180" class="role-text">→ 调用父类初始化</text>
  <text x="860" y="200" class="small-text">- setContentView(R.layout.activity_read)</text>
  <text x="1200" y="200" class="role-text">→ 加载XML布局文件</text>
  <text x="860" y="220" class="small-text">- initParam()</text>
  <text x="1200" y="220" class="role-text">→ 初始化参数</text>
  <text x="860" y="240" class="small-text">- initView()</text>
  <text x="1200" y="240" class="role-text">→ 初始化UI控件</text>

  <text x="840" y="270" class="text">📍 onStart() / onStop()</text>
  <text x="860" y="290" class="small-text">- bindService() / unbindService()</text>
  <text x="1200" y="290" class="role-text">→ 服务绑定/解绑</text>

  <text x="840" y="320" class="text">📍 业务逻辑方法</text>
  <text x="860" y="340" class="small-text">- initListener() 事件监听器设置</text>
  <text x="860" y="360" class="small-text">- startRead() 开始阅读功能</text>
  <text x="860" y="380" class="small-text">- parseMessage() 消息处理</text>

  <text x="840" y="410" class="text">📍 UI控件绑定</text>
  <text x="860" y="430" class="small-text">- private val tvFinishRead by id&lt;TextView&gt;</text>
  <text x="820" y="450" class="text">}</text>

  <!-- Activity 生命周期详解 -->
  <rect x="50" y="480" width="700" height="280" class="lifecycle-box" rx="10"/>
  <text x="400" y="505" text-anchor="middle" class="subtitle">🔄 Activity 生命周期详解</text>
  <text x="400" y="525" text-anchor="middle" class="role-text">作用：管理Activity从创建到销毁的完整生命周期</text>

  <text x="70" y="555" class="text">📍 onCreate() - 创建阶段</text>
  <text x="90" y="575" class="small-text">• 设置布局 setContentView()</text>
  <text x="90" y="595" class="small-text">• 初始化变量和UI控件</text>
  <text x="90" y="615" class="small-text">• 恢复保存的状态</text>

  <text x="400" y="555" class="text">📍 onStart() - 启动阶段</text>
  <text x="420" y="575" class="small-text">• Activity变为可见</text>
  <text x="420" y="595" class="small-text">• 绑定服务 bindService()</text>
  <text x="420" y="615" class="small-text">• 注册广播接收器</text>

  <text x="70" y="645" class="text">📍 onResume() - 恢复阶段</text>
  <text x="90" y="665" class="small-text">• Activity获得焦点</text>
  <text x="90" y="685" class="small-text">• 开始用户交互</text>
  <text x="90" y="705" class="small-text">• 启动动画和传感器</text>

  <text x="400" y="645" class="text">📍 onPause() - 暂停阶段</text>
  <text x="420" y="665" class="small-text">• 失去焦点但仍可见</text>
  <text x="420" y="685" class="small-text">• 暂停动画和传感器</text>
  <text x="420" y="705" class="small-text">• 保存用户数据</text>

  <text x="70" y="735" class="text">📍 onStop() - 停止阶段</text>
  <text x="90" y="755" class="small-text">• Activity不可见</text>
  <text x="420" y="735" class="text">📍 onDestroy() - 销毁阶段</text>
  <text x="440" y="755" class="small-text">• 释放资源和内存</text>

  <!-- XML 布局文件 -->
  <rect x="800" y="480" width="450" height="280" class="xml-box" rx="10"/>
  <text x="1025" y="505" text-anchor="middle" class="subtitle">📄 XML 布局文件</text>
  <text x="1025" y="525" text-anchor="middle" class="role-text">作用：定义UI界面的结构和样式</text>

  <text x="820" y="555" class="text">activity_read.xml</text>
  <text x="820" y="580" class="text">&lt;ConstraintLayout&gt;</text>
  <text x="840" y="600" class="small-text">&lt;TextView android:id="@+id/tvTitle"/&gt;</text>
  <text x="1100" y="600" class="role-text">→ 文本显示</text>
  <text x="840" y="620" class="small-text">&lt;Button android:id="@+id/btnAction"/&gt;</text>
  <text x="1100" y="620" class="role-text">→ 按钮交互</text>
  <text x="840" y="640" class="small-text">&lt;FrameLayout android:id="@+id/container"/&gt;</text>
  <text x="1100" y="640" class="role-text">→ Fragment容器</text>
  <text x="840" y="660" class="small-text">&lt;ImageView android:id="@+id/ivDemo"/&gt;</text>
  <text x="1100" y="660" class="role-text">→ 图片显示</text>
  <text x="820" y="680" class="text">&lt;/ConstraintLayout&gt;</text>

  <text x="820" y="710" class="text">🎯 主要功能：</text>
  <text x="820" y="730" class="small-text">• 定义UI组件位置和大小</text>
  <text x="820" y="750" class="small-text">• 设置样式和主题</text>

  <!-- Fragment 结构 -->
  <rect x="50" y="800" width="600" height="350" class="fragment-box" rx="10"/>
  <text x="350" y="825" text-anchor="middle" class="subtitle">🧩 Fragment 组成结构</text>
  <text x="350" y="845" text-anchor="middle" class="role-text">作用：可重用的UI模块，管理自己的生命周期和布局</text>

  <text x="70" y="875" class="text">class FollowAbilityExplainFragment :</text>
  <text x="90" y="895" class="text">BaseCommonFragment() {</text>

  <text x="110" y="920" class="text">📍 getLayoutResId(): Int</text>
  <text x="130" y="940" class="small-text">return R.layout.fragment_follow_ability</text>
  <text x="400" y="940" class="role-text">→ 返回布局资源ID</text>

  <text x="110" y="965" class="text">📍 companion object</text>
  <text x="130" y="985" class="small-text">fun newInstance(): Fragment</text>
  <text x="400" y="985" class="role-text">→ 创建Fragment实例</text>

  <text x="110" y="1010" class="text">📍 生命周期方法</text>
  <text x="130" y="1030" class="small-text">• onCreateView() - 创建视图</text>
  <text x="130" y="1050" class="small-text">• onViewCreated() - 视图创建完成</text>
  <text x="130" y="1070" class="small-text">• onAttach() - 附加到Activity</text>
  <text x="130" y="1090" class="small-text">• onDetach() - 从Activity分离</text>

  <text x="110" y="1115" class="text">📍 initView() - UI初始化</text>
  <text x="130" y="1135" class="small-text">• 控件绑定和事件设置</text>
  <text x="70" y="1155" class="text">}</text>

  <!-- View 组件详解 -->
  <rect x="700" y="800" width="450" height="350" class="view-box" rx="10"/>
  <text x="925" y="825" text-anchor="middle" class="subtitle">🎨 View 组件详解</text>
  <text x="925" y="845" text-anchor="middle" class="role-text">作用：构成用户界面的基本元素</text>

  <text x="720" y="875" class="text">📍 基础View组件：</text>
  <text x="740" y="895" class="text">• TextView</text>
  <text x="850" y="895" class="role-text">→ 显示文本内容</text>
  <text x="740" y="915" class="text">• Button</text>
  <text x="850" y="915" class="role-text">→ 用户点击交互</text>
  <text x="740" y="935" class="text">• ImageView</text>
  <text x="850" y="935" class="role-text">→ 显示图片资源</text>
  <text x="740" y="955" class="text">• EditText</text>
  <text x="850" y="955" class="role-text">→ 文本输入框</text>

  <text x="720" y="985" class="text">📍 布局容器：</text>
  <text x="740" y="1005" class="text">• ConstraintLayout</text>
  <text x="880" y="1005" class="role-text">→ 约束布局</text>
  <text x="740" y="1025" class="text">• FrameLayout</text>
  <text x="880" y="1025" class="role-text">→ 帧布局容器</text>
  <text x="740" y="1045" class="text">• LinearLayout</text>
  <text x="880" y="1045" class="role-text">→ 线性布局</text>

  <text x="720" y="1075" class="text">📍 高级组件：</text>
  <text x="740" y="1095" class="text">• RecyclerView</text>
  <text x="880" y="1095" class="role-text">→ 列表展示</text>
  <text x="740" y="1115" class="text">• WebView</text>
  <text x="880" y="1115" class="role-text">→ 网页显示</text>
  <text x="740" y="1135" class="text">• 自定义View</text>
  <text x="880" y="1135" class="role-text">→ 继承View/ViewGroup</text>

  <!-- 加载流程与数据流 -->
  <rect x="50" y="1200" width="1500" height="150" class="manifest-box" rx="10"/>
  <text x="800" y="1225" text-anchor="middle" class="subtitle">🔄 完整加载流程与数据流</text>

  <text x="70" y="1255" class="text">1️⃣ AndroidManifest.xml 声明Activity → 系统识别组件</text>
  <text x="70" y="1275" class="text">2️⃣ Activity.onCreate() 调用 setContentView() → LayoutInflater解析XML</text>
  <text x="70" y="1295" class="text">3️⃣ 创建View树 → Fragment通过getLayoutResId()加载布局</text>
  <text x="70" y="1315" class="text">4️⃣ findViewById()或ViewBinding绑定控件 → 设置事件监听器</text>
  <text x="70" y="1335" class="text">5️⃣ 生命周期驱动 → 业务逻辑执行 → 用户交互响应</text>

  <!-- 箭头连接 -->
  <line x1="750" y1="340" x2="800" y2="480" class="arrow"/>
  <text x="775" y="410" text-anchor="middle" class="small-text">声明</text>

  <line x1="1175" y1="440" x2="1025" y2="480" class="arrow"/>
  <text x="1100" y="465" text-anchor="middle" class="small-text">setContentView()</text>

  <line x1="350" y1="800" x2="350" y2="760" class="dashed-arrow"/>
  <text x="280" y="780" text-anchor="middle" class="small-text">Fragment容器</text>

  <line x1="650" y1="975" x2="700" y2="975" class="dashed-arrow"/>
  <text x="675" y="965" text-anchor="middle" class="small-text">包含</text>

  <!-- 技术要点和最佳实践 -->
  <rect x="1200" y="800" width="350" height="350" class="lifecycle-box" rx="10"/>
  <text x="1375" y="825" text-anchor="middle" class="subtitle">💡 技术要点</text>

  <text x="1220" y="855" class="text">🎯 Activity职责：</text>
  <text x="1220" y="875" class="small-text">• 界面容器和生命周期管理</text>
  <text x="1220" y="895" class="small-text">• Fragment事务管理</text>
  <text x="1220" y="915" class="small-text">• 系统回调处理</text>

  <text x="1220" y="945" class="text">🎯 Fragment优势：</text>
  <text x="1220" y="965" class="small-text">• 模块化UI组件</text>
  <text x="1220" y="985" class="small-text">• 可重用和组合</text>
  <text x="1220" y="1005" class="small-text">• 独立生命周期</text>

  <text x="1220" y="1035" class="text">🎯 View绑定方式：</text>
  <text x="1220" y="1055" class="small-text">• findViewById()</text>
  <text x="1220" y="1075" class="small-text">• ViewBinding (推荐)</text>
  <text x="1220" y="1095" class="small-text">• DataBinding</text>

  <text x="1220" y="1125" class="text">⚠️ 注意事项：</text>
  <text x="1220" y="1145" class="small-text">• 避免内存泄漏</text>

  <!-- 层级关系说明 -->
  <text x="800" y="1380" text-anchor="middle" class="subtitle">📊 层级关系：AndroidManifest.xml → Activity → XML Layout → Fragment → View Components</text>
</svg>
