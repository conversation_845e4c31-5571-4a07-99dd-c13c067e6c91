<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>

  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    BroadcastReceiver使用情况详解
  </text>
  
  <!-- 四大组件概览区域 -->
  <rect x="50" y="70" width="500" height="320" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="300" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#34495e">
    Android四大组件使用情况
  </text>
  
  <!-- Activity组件 -->
  <rect x="80" y="110" width="200" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="180" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    Activity (活动)
  </text>
  <text x="180" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    ✓ 已使用
  </text>
  <text x="180" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
    多个Activity实现
  </text>
  
  <!-- Service组件 -->
  <rect x="320" y="110" width="200" height="80" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="420" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    Service (服务)
  </text>
  <text x="420" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    ✓ 已使用
  </text>
  <text x="420" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
    2个Service
  </text>
  
  <!-- BroadcastReceiver组件 -->
  <rect x="80" y="210" width="200" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
  <text x="180" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    BroadcastReceiver
  </text>
  <text x="180" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    ✓ 已使用
  </text>
  <text x="180" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
    2个Receiver
  </text>
  
  <!-- ContentProvider组件 -->
  <rect x="320" y="210" width="200" height="80" fill="#95a5a6" stroke="#7f8c8d" stroke-width="2" rx="5"/>
  <text x="420" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    ContentProvider
  </text>
  <text x="420" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    ✗ 未使用
  </text>
  <text x="420" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
    无相关实现
  </text>
  
  <!-- 统计信息 -->
  <text x="300" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#e74c3c">
    使用了4个组件中的3个
  </text>
  <text x="300" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#7f8c8d">
    Activity + Service + BroadcastReceiver
  </text>
  
  <!-- 首页指定方式区域 -->
  <rect x="600" y="70" width="550" height="320" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="875" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#34495e">
    首页指定方式
  </text>
  
  <!-- LauncherActivity -->
  <rect x="630" y="110" width="180" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="720" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    LauncherActivity
  </text>
  <text x="720" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
    启动页面 (MAIN+LAUNCHER)
  </text>
  
  <!-- 箭头和判断逻辑 -->
  <path d="M 720 170 L 720 200" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <rect x="640" y="200" width="160" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="720" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
    DeviceManager.getPlacementType()
  </text>
  
  <!-- 四个目标Activity -->
  <rect x="850" y="110" width="120" height="50" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="910" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">
    HomeMainActivity
  </text>
  <text x="910" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
    M_HOME
  </text>
  
  <rect x="980" y="110" width="120" height="50" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="1040" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">
    HospitalMainActivity
  </text>
  <text x="1040" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
    M_HOSPITAL
  </text>
  
  <rect x="850" y="170" width="120" height="50" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="910" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">
    ReadHomeMainActivity
  </text>
  <text x="910" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
    R_HOME
  </text>
  
  <rect x="980" y="170" width="120" height="50" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="1040" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">
    TscMainActivity
  </text>
  <text x="1040" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
    TSC
  </text>
  
  <!-- 连接线 -->
  <path d="M 800 220 L 850 135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 800 220 L 980 135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 800 220 L 850 195" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 800 220 L 980 195" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 详细说明区域 -->
  <rect x="50" y="420" width="1100" height="350" fill="#ffffff" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="600" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2c3e50">
    详细说明
  </text>
  
  <!-- Activity详细说明 -->
  <text x="70" y="475" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3498db">
    1. Activity (活动) - 已使用
  </text>
  <text x="90" y="495" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • LauncherActivity: 应用启动页，配置了MAIN+LAUNCHER意图过滤器
  </text>
  <text x="90" y="515" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • HomeMainActivity, HospitalMainActivity, ReadHomeMainActivity, TscMainActivity: 不同场景的主页面
  </text>
  <text x="90" y="535" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • 其他功能Activity: 如眼动检测、校准、配置等相关Activity
  </text>
  
  <!-- Service详细说明 -->
  <text x="70" y="565" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#27ae60">
    2. Service (服务) - 已使用
  </text>
  <text x="90" y="585" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • GazeTrackService: 眼动追踪服务，运行在独立进程":GazeTracker"中，前台服务类型为camera
  </text>
  <text x="90" y="605" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • DesktopService: 桌面服务，运行在独立进程":Desktop"中
  </text>
  
  <!-- BroadcastReceiver详细说明 -->
  <text x="70" y="635" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f39c12">
    3. BroadcastReceiver (广播接收器) - 已使用
  </text>
  <text x="90" y="655" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • BootReceiver: 监听系统开机广播(BOOT_COMPLETED)
  </text>
  <text x="90" y="675" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • GlobalBootReceiver: 监听自定义广播，如眼动追踪启停、遮盖疗法控制、校准启动等
  </text>
  
  <!-- ContentProvider详细说明 -->
  <text x="70" y="705" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#95a5a6">
    4. ContentProvider (内容提供器) - 未使用
  </text>
  <text x="90" y="725" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    • 项目中没有实现ContentProvider组件，数据共享通过其他方式实现
  </text>
  
  <!-- 首页指定说明 -->
  <text x="70" y="755" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#e74c3c">
    首页指定机制:
  </text>
  <text x="90" y="775" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    通过LauncherActivity作为统一入口，根据DeviceManager.getPlacementType()动态跳转到对应的主页面，支持医疗家用、医疗医院、阅读家用、TSC等不同场景
  </text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>
</svg>
