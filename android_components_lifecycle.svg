<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .lifecycle-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2980b9; }
      .operation-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .component-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .connection { stroke: #95a5a6; stroke-width: 1; stroke-dasharray: 5,5; fill: none; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">Android四大组件生命周期操作图</text>
  
  <!-- Activity 生命周期区域 -->
  <rect x="50" y="60" width="320" height="400" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
  <text x="210" y="85" text-anchor="middle" class="subtitle">Activity 生命周期</text>
  
  <!-- Activity 生命周期方法 -->
  <rect x="70" y="100" width="80" height="40" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="5"/>
  <text x="110" y="125" text-anchor="middle" class="component-title">onCreate</text>
  
  <rect x="70" y="150" width="80" height="40" fill="#27ae60" stroke="#229954" stroke-width="1" rx="5"/>
  <text x="110" y="175" text-anchor="middle" class="component-title">onStart</text>
  
  <rect x="70" y="200" width="80" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="5"/>
  <text x="110" y="225" text-anchor="middle" class="component-title">onResume</text>
  
  <rect x="70" y="250" width="80" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="5"/>
  <text x="110" y="275" text-anchor="middle" class="component-title">onPause</text>
  
  <rect x="70" y="300" width="80" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="5"/>
  <text x="110" y="325" text-anchor="middle" class="component-title">onStop</text>
  
  <rect x="70" y="350" width="80" height="40" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="5"/>
  <text x="110" y="375" text-anchor="middle" class="component-title">onDestroy</text>
  
  <!-- Activity 操作说明 -->
  <text x="170" y="115" class="operation-text">• setContentView(布局)</text>
  <text x="170" y="130" class="operation-text">• initParam() - 获取Intent参数</text>
  <text x="170" y="145" class="operation-text">• initView() - 初始化界面</text>
  
  <text x="170" y="165" class="operation-text">• bindService(GazeTrackService)</text>
  <text x="170" y="180" class="operation-text">• 建立服务连接</text>
  
  <text x="170" y="215" class="operation-text">• 开始用户交互</text>
  <text x="170" y="230" class="operation-text">• 启动倒计时</text>
  
  <text x="170" y="265" class="operation-text">• 暂停用户交互</text>
  <text x="170" y="280" class="operation-text">• 保存状态</text>
  
  <text x="170" y="315" class="operation-text">• 停止眼动追踪</text>
  <text x="170" y="330" class="operation-text">• unbindService()</text>
  
  <text x="170" y="365" class="operation-text">• 释放资源</text>
  <text x="170" y="380" class="operation-text">• WebView.destroy()</text>
  
  <!-- Activity 生命周期箭头 -->
  <line x1="110" y1="140" x2="110" y2="150" class="arrow"/>
  <line x1="110" y1="190" x2="110" y2="200" class="arrow"/>
  <line x1="110" y1="240" x2="110" y2="250" class="arrow"/>
  <line x1="110" y1="290" x2="110" y2="300" class="arrow"/>
  <line x1="110" y1="340" x2="110" y2="350" class="arrow"/>
  
  <!-- Service 生命周期区域 -->
  <rect x="400" y="60" width="320" height="400" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
  <text x="560" y="85" text-anchor="middle" class="subtitle">Service 生命周期</text>
  
  <!-- Service 生命周期方法 -->
  <rect x="420" y="100" width="80" height="40" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="5"/>
  <text x="460" y="125" text-anchor="middle" class="component-title">onCreate</text>
  
  <rect x="420" y="150" width="100" height="40" fill="#27ae60" stroke="#229954" stroke-width="1" rx="5"/>
  <text x="470" y="175" text-anchor="middle" class="component-title">onStartCommand</text>
  
  <rect x="420" y="200" width="80" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="5"/>
  <text x="460" y="225" text-anchor="middle" class="component-title">onBind</text>
  
  <rect x="420" y="250" width="80" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="5"/>
  <text x="460" y="275" text-anchor="middle" class="component-title">onUnbind</text>
  
  <rect x="420" y="300" width="80" height="40" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="5"/>
  <text x="460" y="325" text-anchor="middle" class="component-title">onDestroy</text>
  
  <!-- Service 操作说明 -->
  <text x="530" y="115" class="operation-text">• 创建Handler和Messenger</text>
  <text x="530" y="130" class="operation-text">• 启动前台服务</text>
  <text x="530" y="145" class="operation-text">• 启动WebSocket服务</text>
  
  <text x="530" y="165" class="operation-text">• 设置STARTED状态</text>
  <text x="530" y="180" class="operation-text">• 返回START_REDELIVER_INTENT</text>
  
  <text x="530" y="215" class="operation-text">• 返回Messenger.binder</text>
  <text x="530" y="230" class="operation-text">• 建立通信连接</text>
  
  <text x="530" y="265" class="operation-text">• 断开连接</text>
  <text x="530" y="280" class="operation-text">• 记录日志</text>
  
  <text x="530" y="315" class="operation-text">• 关闭眼动服务</text>
  <text x="530" y="330" class="operation-text">• 关闭相机和WebSocket</text>
  
  <!-- Service 生命周期箭头 -->
  <line x1="460" y1="140" x2="460" y2="150" class="arrow"/>
  <line x1="470" y1="190" x2="470" y2="200" class="arrow"/>
  <line x1="460" y1="240" x2="460" y2="250" class="arrow"/>
  <line x1="460" y1="290" x2="460" y2="300" class="arrow"/>
  
  <!-- BroadcastReceiver 区域 -->
  <rect x="750" y="60" width="300" height="200" fill="#fff8e1" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="900" y="85" text-anchor="middle" class="subtitle">BroadcastReceiver</text>
  
  <rect x="770" y="100" width="80" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="5"/>
  <text x="810" y="125" text-anchor="middle" class="component-title">onReceive</text>
  
  <text x="870" y="115" class="operation-text">• BootReceiver: 监听开机广播</text>
  <text x="870" y="130" class="operation-text">• GlobalBootReceiver: 眼动追踪控制</text>
  <text x="870" y="145" class="operation-text">• RefreshBindUserReceiver: 定时刷新</text>
  <text x="870" y="160" class="operation-text">• FlipBeatManager: 蓝牙状态监听</text>
  <text x="870" y="175" class="operation-text">• 通过LiveEventBus转发事件</text>
  
  <!-- Fragment 区域 -->
  <rect x="750" y="280" width="300" height="180" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" rx="10"/>
  <text x="900" y="305" text-anchor="middle" class="subtitle">Fragment 生命周期</text>
  
  <rect x="770" y="320" width="100" height="30" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="3"/>
  <text x="820" y="340" text-anchor="middle" style="font-size: 11px; fill: white;">onCreateView</text>
  
  <rect x="770" y="360" width="80" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="810" y="380" text-anchor="middle" style="font-size: 11px; fill: white;">onResume</text>
  
  <rect x="770" y="400" width="80" height="30" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="3"/>
  <text x="810" y="420" text-anchor="middle" style="font-size: 11px; fill: white;">onStop</text>
  
  <text x="880" y="335" class="operation-text">• 返回布局资源</text>
  <text x="880" y="350" class="operation-text">• getLayoutResId()</text>
  
  <text x="860" y="375" class="operation-text">• 启动评估流程</text>
  <text x="860" y="415" class="operation-text">• 停止评估，清理资源</text>
  
  <!-- ContentProvider 区域 -->
  <rect x="1080" y="60" width="250" height="120" fill="#fafafa" stroke="#95a5a6" stroke-width="2" rx="10"/>
  <text x="1205" y="85" text-anchor="middle" class="subtitle">ContentProvider</text>
  
  <rect x="1100" y="100" width="80" height="40" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="5"/>
  <text x="1140" y="125" text-anchor="middle" class="component-title">未使用</text>
  
  <text x="1200" y="115" class="operation-text">• 项目中未实现</text>
  <text x="1200" y="130" class="operation-text">• 数据共享通过其他方式</text>
  
  <!-- Application 生命周期监听 -->
  <rect x="50" y="500" width="600" height="120" fill="#e0f2f1" stroke="#26a69a" stroke-width="2" rx="10"/>
  <text x="350" y="525" text-anchor="middle" class="subtitle">Application 生命周期监听</text>
  
  <rect x="70" y="540" width="150" height="40" fill="#26a69a" stroke="#00695c" stroke-width="1" rx="5"/>
  <text x="145" y="565" text-anchor="middle" class="component-title">ActivityLifecycleCallbacks</text>
  
  <text x="240" y="550" class="operation-text">• onActivityStarted: 计数+1，第一个Activity启动时设置前台状态</text>
  <text x="240" y="565" class="operation-text">• onActivityStopped: 计数-1，最后一个Activity停止时设置后台状态</text>
  <text x="240" y="580" class="operation-text">• 通过LiveEventBus发送前后台状态变化事件</text>
  <text x="240" y="595" class="operation-text">• 全局监控应用前后台切换状态</text>
  
  <!-- 连接线 -->
  <line x1="150" y1="170" x2="420" y2="120" class="connection"/>
  <line x1="150" y1="320" x2="420" y2="270" class="connection"/>

  <!-- 具体实现示例 -->
  <rect x="700" y="500" width="650" height="450" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="1025" y="525" text-anchor="middle" class="subtitle">具体实现示例 - ReadActivity</text>

  <!-- ReadActivity 时序图 -->
  <text x="720" y="550" class="lifecycle-text">1. onCreate() 阶段:</text>
  <text x="740" y="570" class="operation-text">• setContentView(R.layout.activity_read)</text>
  <text x="740" y="585" class="operation-text">• initParam() - 获取身份(ReadIdentity)和年级(ReadGrade)</text>
  <text x="740" y="600" class="operation-text">• initView() - 启动3秒倒计时 countdown(3000,1000)</text>
  <text x="740" y="615" class="operation-text">• initListener() - 设置完成阅读按钮监听器</text>

  <text x="720" y="640" class="lifecycle-text">2. onStart() 阶段:</text>
  <text x="740" y="660" class="operation-text">• bindService(Intent(this, GazeTrackService::class.java), serviceConnection, BIND_AUTO_CREATE)</text>
  <text x="740" y="675" class="operation-text">• 建立与GazeTrackService的连接</text>

  <text x="720" y="700" class="lifecycle-text">3. 用户交互阶段:</text>
  <text x="740" y="720" class="operation-text">• 倒计时完成 → startRead() → 发送消息到Service</text>
  <text x="740" y="735" class="operation-text">• MSG_TURN_ON_CAMERA, MSG_START_TRACK, MSG_START_APPLIED_READING</text>
  <text x="740" y="750" class="operation-text">• 用户点击完成 → endTime = System.currentTimeMillis() → 获取轨迹数据</text>
  <text x="740" y="765" class="operation-text">• parseMessage() → 处理Service返回的数据 → 跳转结果页面</text>

  <text x="720" y="790" class="lifecycle-text">4. onStop() 阶段:</text>
  <text x="740" y="810" class="operation-text">• 发送停止消息: MSG_STOP_APPLIED_READING, MSG_STOP_TRACK, MSG_TURN_OFF_CAMERA</text>
  <text x="740" y="825" class="operation-text">• unbindService(serviceConnection) - 解绑服务</text>

  <text x="720" y="850" class="lifecycle-text">5. 关键特性:</text>
  <text x="740" y="870" class="operation-text">• 使用LifecycleHandler处理消息，自动管理生命周期</text>
  <text x="740" y="885" class="operation-text">• @Volatile isFinishRead 保证线程安全</text>
  <text x="740" y="900" class="operation-text">• enableBack() = false 禁用返回键</text>
  <text x="740" y="915" class="operation-text">• 通过LiveEventBus进行组件间通信</text>

</svg>
