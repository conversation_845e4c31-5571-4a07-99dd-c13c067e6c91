<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #27ae60; }
      .activity-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .compose-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .navhost-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .screen-box { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">Android新需求开发模式：Activity + Compose + NavHost导航</text>
  
  <!-- Activity层 -->
  <rect x="50" y="80" width="300" height="180" class="activity-box" rx="10"/>
  <text x="200" y="100" text-anchor="middle" class="subtitle">1. Activity层</text>
  
  <text x="70" y="125" class="text">MainActivity.kt</text>
  <text x="70" y="145" class="code">class MainActivity : ComponentActivity() {</text>
  <text x="70" y="160" class="code">  override fun onCreate(...) {</text>
  <text x="70" y="175" class="code">    setContent {</text>
  <text x="70" y="190" class="code">      MyAppTheme {</text>
  <text x="70" y="205" class="code">        MainNavigation()</text>
  <text x="70" y="220" class="code">      }</text>
  <text x="70" y="235" class="code">    }</text>
  <text x="70" y="250" class="code">  }</text>
  
  <!-- Compose导航层 -->
  <rect x="400" y="80" width="350" height="220" class="navhost-box" rx="10"/>
  <text x="575" y="100" text-anchor="middle" class="subtitle">2. Compose导航层</text>
  
  <text x="420" y="125" class="text">MainNavigation.kt</text>
  <text x="420" y="145" class="code">@Composable</text>
  <text x="420" y="160" class="code">fun MainNavigation() {</text>
  <text x="420" y="175" class="code">  val navController = rememberNavController()</text>
  <text x="420" y="190" class="code">  NavHost(</text>
  <text x="420" y="205" class="code">    navController = navController,</text>
  <text x="420" y="220" class="code">    startDestination = "home"</text>
  <text x="420" y="235" class="code">  ) {</text>
  <text x="420" y="250" class="code">    composable("home") { HomeScreen(navController) }</text>
  <text x="420" y="265" class="code">    composable("detail/{id}") { DetailScreen(...) }</text>
  <text x="420" y="280" class="code">    composable("settings") { SettingsScreen(...) }</text>
  <text x="420" y="295" class="code">  }</text>
  
  <!-- 页面层 -->
  <rect x="800" y="80" width="350" height="220" class="screen-box" rx="10"/>
  <text x="975" y="100" text-anchor="middle" class="subtitle">3. Composable页面层</text>
  
  <text x="820" y="125" class="text">各个Screen.kt文件</text>
  <text x="820" y="145" class="code">@Composable</text>
  <text x="820" y="160" class="code">fun HomeScreen(navController: NavController) {</text>
  <text x="820" y="175" class="code">  Column {</text>
  <text x="820" y="190" class="code">    Button(</text>
  <text x="820" y="205" class="code">      onClick = {</text>
  <text x="820" y="220" class="code">        navController.navigate("detail/123")</text>
  <text x="820" y="235" class="code">      }</text>
  <text x="820" y="250" class="code">    ) { Text("Go to Detail") }</text>
  <text x="820" y="265" class="code">  }</text>
  <text x="820" y="280" class="code">}</text>
  
  <!-- 导航操作说明 -->
  <rect x="50" y="350" width="1100" height="120" class="compose-box" rx="10"/>
  <text x="600" y="375" text-anchor="middle" class="subtitle">4. 导航操作方法</text>
  
  <text x="70" y="400" class="text">• navigate("route") - 导航到新页面</text>
  <text x="70" y="420" class="text">• navigate("detail/{id}") - 带参数导航</text>
  <text x="70" y="440" class="text">• popBackStack() - 返回上一页</text>
  <text x="70" y="460" class="text">• popUpTo("route") - 返回到指定页面</text>
  
  <text x="600" y="400" class="text">• launchSingleTop = true - 避免重复创建</text>
  <text x="600" y="420" class="text">• restoreState = true - 恢复页面状态</text>
  <text x="600" y="440" class="text">• saveState = true - 保存页面状态</text>
  <text x="600" y="460" class="text">• deepLinks - 支持深度链接</text>
  
  <!-- 优势说明 -->
  <rect x="50" y="500" width="1100" height="150" class="activity-box" rx="10"/>
  <text x="600" y="525" text-anchor="middle" class="subtitle">5. 新模式优势</text>
  
  <text x="70" y="550" class="text">✓ 声明式UI：使用Compose构建现代化界面</text>
  <text x="70" y="570" class="text">✓ 类型安全：编译时检查导航路由</text>
  <text x="70" y="590" class="text">✓ 状态管理：自动处理页面状态保存和恢复</text>
  <text x="70" y="610" class="text">✓ 动画支持：内置页面切换动画</text>
  <text x="70" y="630" class="text">✓ 深度链接：支持外部应用跳转</text>
  
  <text x="600" y="550" class="text">✓ 单Activity架构：减少Activity管理复杂度</text>
  <text x="600" y="570" class="text">✓ 组件化：页面组件可复用</text>
  <text x="600" y="590" class="text">✓ 测试友好：Composable函数易于单元测试</text>
  <text x="600" y="610" class="text">✓ 性能优化：Compose重组机制提升性能</text>
  <text x="600" y="630" class="text">✓ 开发效率：减少样板代码，提升开发速度</text>
  
  <!-- 实施步骤 -->
  <rect x="50" y="680" width="1100" height="100" class="navhost-box" rx="10"/>
  <text x="600" y="705" text-anchor="middle" class="subtitle">6. 实施步骤</text>
  
  <text x="70" y="730" class="text">1. 添加Compose依赖 → 2. 创建Activity并设置setContent → 3. 定义NavHost和路由</text>
  <text x="70" y="750" class="text">4. 创建各个Screen Composable → 5. 实现导航逻辑 → 6. 测试导航流程</text>
  <text x="70" y="770" class="text">7. 添加动画和深度链接 → 8. 优化性能和用户体验</text>
  
  <!-- 箭头连接 -->
  <line x1="350" y1="170" x2="400" y2="170" class="arrow"/>
  <line x1="750" y1="170" x2="800" y2="170" class="arrow"/>
  <line x1="200" y1="260" x2="200" y2="350" class="arrow"/>
  <line x1="575" y1="300" x2="575" y2="350" class="arrow"/>
  <line x1="975" y1="300" x2="975" y2="350" class="arrow"/>
  
</svg>
