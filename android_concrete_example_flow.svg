<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 13px; fill: #e74c3c; font-weight: bold; }
      .manifest-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .fragment-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .service-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .lifecycle-box { fill: #f0f8ff; stroke: #4169e1; stroke-width: 2; }
      .flow-box { fill: #fffacd; stroke: #daa520; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #7f8c8d; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .thick-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#redarrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">Android 具体场景实例：阅读追踪功能完整流程</text>
  <text x="900" y="55" text-anchor="middle" class="subtitle">基于 ReadActivity 和 FollowAbilityEvaluateActivity 的真实代码场景</text>

  <!-- 场景1: ReadActivity 阅读追踪流程 -->
  <rect x="50" y="80" width="800" height="450" class="activity-box" rx="15"/>
  <text x="450" y="105" text-anchor="middle" class="subtitle">🔍 场景1: ReadActivity 阅读追踪完整流程</text>

  <!-- AndroidManifest.xml 配置 -->
  <rect x="70" y="120" width="350" height="80" class="manifest-box" rx="10"/>
  <text x="245" y="140" text-anchor="middle" class="text">📋 AndroidManifest.xml</text>
  <text x="80" y="160" class="code-text">&lt;activity android:name=".read.ReadActivity"</text>
  <text x="80" y="175" class="code-text">    android:configChanges="orientation|..."&gt;</text>
  <text x="80" y="190" class="code-text">&lt;/activity&gt;</text>

  <!-- Activity 创建和初始化 -->
  <rect x="450" y="120" width="380" height="160" class="lifecycle-box" rx="10"/>
  <text x="640" y="140" text-anchor="middle" class="text">🏗️ Activity 创建过程</text>
  <text x="460" y="165" class="step-text">Step 1: onCreate()</text>
  <text x="470" y="180" class="code-text">setContentView(R.layout.activity_read)</text>
  <text x="470" y="195" class="code-text">initParam() // 解析Intent参数</text>
  <text x="470" y="210" class="code-text">initView() // 初始化UI</text>
  <text x="460" y="235" class="step-text">Step 2: onStart()</text>
  <text x="470" y="250" class="code-text">bindService(GazeTrackService)</text>
  <text x="470" y="265" class="code-text">serviceConnection.onServiceConnected</text>

  <!-- UI 初始化详细过程 -->
  <rect x="70" y="220" width="350" height="140" class="fragment-box" rx="10"/>
  <text x="245" y="240" text-anchor="middle" class="text">🎨 initView() 详细过程</text>
  <text x="80" y="260" class="code-text">initListener() {</text>
  <text x="90" y="275" class="code-text">  tvFinishRead.setOnSingleClickListener {</text>
  <text x="100" y="290" class="code-text">    endTime = System.currentTimeMillis()</text>
  <text x="100" y="305" class="code-text">    sendMessage(MSG_GET_GAZE_TRAJECTORY)</text>
  <text x="90" y="320" class="code-text">  }</text>
  <text x="80" y="335" class="code-text">}</text>
  <text x="80" y="350" class="code-text">countdown(3000) { startRead() }</text>

  <!-- 服务交互过程 -->
  <rect x="70" y="380" width="760" height="140" class="service-box" rx="10"/>
  <text x="450" y="400" text-anchor="middle" class="text">🔄 与 GazeTrackService 交互过程</text>
  <text x="80" y="425" class="step-text">Step 3: startRead() 发送消息序列</text>
  <text x="90" y="440" class="code-text">sendMessageToService(MSG_TURN_ON_CAMERA)    // 开启摄像头</text>
  <text x="90" y="455" class="code-text">sendMessageToService(MSG_START_TRACK)       // 开始视线追踪</text>
  <text x="90" y="470" class="code-text">sendMessageToService(MSG_START_APPLIED_READING) // 开始应用阅读模式</text>
  <text x="80" y="495" class="step-text">Step 4: 用户点击完成 → 获取轨迹数据 → 跳转结果页面</text>
  <text x="90" y="510" class="code-text">parseMessage(MSG_GAZE_TRAJECTORY_RESULT) → startActivity(ReadResultAnalysisActivity)</text>

  <!-- 场景2: Fragment 管理流程 -->
  <rect x="900" y="80" width="850" height="450" class="fragment-box" rx="15"/>
  <text x="1325" y="105" text-anchor="middle" class="subtitle">🧩 场景2: FollowAbilityEvaluateActivity Fragment管理流程</text>

  <!-- Activity Fragment 容器 -->
  <rect x="920" y="120" width="400" height="100" class="activity-box" rx="10"/>
  <text x="1120" y="140" text-anchor="middle" class="text">🏗️ FollowAbilityEvaluateActivity</text>
  <text x="930" y="165" class="code-text">onCreate() {</text>
  <text x="940" y="180" class="code-text">  setContentView(R.layout.activity_follow_ability)</text>
  <text x="940" y="195" class="code-text">  showFollowAbilityEvaluateExplain() // 显示说明页面</text>
  <text x="930" y="210" class="code-text">}</text>

  <!-- Fragment 切换过程 -->
  <rect x="1350" y="120" width="380" height="180" class="lifecycle-box" rx="10"/>
  <text x="1540" y="140" text-anchor="middle" class="text">🔄 Fragment 切换机制</text>
  <text x="1360" y="165" class="step-text">Step 1: 显示说明Fragment</text>
  <text x="1370" y="180" class="code-text">supportFragmentManager.beginTransaction()</text>
  <text x="1370" y="195" class="code-text">.replace(R.id.root_follow_ability,</text>
  <text x="1370" y="210" class="code-text">  FollowAbilityExplainFragment.newInstance())</text>
  <text x="1370" y="225" class="code-text">.commitAllowingStateLoss()</text>
  <text x="1360" y="250" class="step-text">Step 2: 用户点击开始评估</text>
  <text x="1370" y="265" class="code-text">tvStartEvaluating.setOnSingleClickListener {</text>
  <text x="1370" y="280" class="code-text">  activity.showFollowAbilityEvaluating()</text>
  <text x="1370" y="295" class="code-text">}</text>

  <!-- Fragment 生命周期 -->
  <rect x="920" y="240" width="400" height="180" class="fragment-box" rx="10"/>
  <text x="1120" y="260" text-anchor="middle" class="text">📱 FollowAbilityExplainFragment</text>
  <text x="930" y="285" class="step-text">Fragment 生命周期:</text>
  <text x="940" y="300" class="code-text">getLayoutResId() → R.layout.fragment_follow_ability_explain</text>
  <text x="940" y="315" class="code-text">onCreateView() → LayoutInflater.inflate(layoutResId)</text>
  <text x="940" y="330" class="code-text">onViewCreated() → initView()</text>
  <text x="940" y="350" class="step-text">initView() 具体实现:</text>
  <text x="940" y="365" class="code-text">ivBack.setOnSingleClickListener { activity.finish() }</text>
  <text x="940" y="380" class="code-text">Glide.load(R.drawable.follow_ability_explain).into(ivDemo)</text>
  <text x="940" y="395" class="code-text">tvStartEvaluating.setOnSingleClickListener { 切换Fragment }</text>

  <!-- Fragment 切换详细过程 -->
  <rect x="920" y="440" width="810" height="80" class="flow-box" rx="10"/>
  <text x="1325" y="460" text-anchor="middle" class="text">🔄 Fragment 切换详细过程</text>
  <text x="930" y="480" class="code-text">FollowAbilityExplainFragment → 用户点击"开始评估" → Activity.showFollowAbilityEvaluating() →</text>
  <text x="930" y="495" class="code-text">FragmentTransaction.replace() → FollowAbilityEvaluatingFragment2 → 开始视线追踪评估</text>
  <text x="930" y="510" class="code-text">评估完成 → 获取轨迹数据 → 跳转到 FollowAbilityEvaluateResultActivity2</text>

  <!-- 数据流和消息传递 -->
  <rect x="50" y="560" width="1700" height="200" class="service-box" rx="15"/>
  <text x="900" y="585" text-anchor="middle" class="subtitle">📡 数据流和消息传递机制</text>

  <!-- Service 消息处理 -->
  <rect x="70" y="600" width="500" height="140" class="lifecycle-box" rx="10"/>
  <text x="320" y="620" text-anchor="middle" class="text">🔧 GazeTrackService 消息处理</text>
  <text x="80" y="645" class="code-text">parseMessage(msg) {</text>
  <text x="90" y="660" class="code-text">  when(msg.what) {</text>
  <text x="100" y="675" class="code-text">    MSG_TURN_ON_CAMERA → GTCameraManager.startCamera()</text>
  <text x="100" y="690" class="code-text">    MSG_START_TRACK → TrackingManager.startTracking()</text>
  <text x="100" y="705" class="code-text">    MSG_GET_GAZE_TRAJECTORY → 返回轨迹数据</text>
  <text x="90" y="720" class="code-text">  }</text>
  <text x="80" y="735" class="code-text">}</text>

  <!-- LiveEventBus 数据传递 -->
  <rect x="600" y="600" width="550" height="140" class="fragment-box" rx="10"/>
  <text x="875" y="620" text-anchor="middle" class="text">📨 LiveEventBus 数据传递</text>
  <text x="610" y="645" class="step-text">Activity → Activity 数据传递:</text>
  <text x="620" y="660" class="code-text">// ReadActivity 发送结果</text>
  <text x="620" y="675" class="code-text">LiveEventBus.get&lt;ReadResult&gt;(INPUT_PARAM_READ_RESULT)</text>
  <text x="620" y="690" class="code-text">  .post(readResult)</text>
  <text x="620" y="710" class="code-text">// ReadResultAnalysisActivity 接收</text>
  <text x="620" y="725" class="code-text">LiveEventBus.get&lt;ReadResult&gt;().observeSticky(this) { }</text>

  <!-- Intent 参数传递 -->
  <rect x="1180" y="600" width="550" height="140" class="manifest-box" rx="10"/>
  <text x="1455" y="620" text-anchor="middle" class="text">📤 Intent 参数传递</text>
  <text x="1190" y="645" class="step-text">Activity 启动参数传递:</text>
  <text x="1200" y="660" class="code-text">// ReadActivity 创建Intent</text>
  <text x="1200" y="675" class="code-text">fun createIntent(context: Context, identity: ReadIdentity,</text>
  <text x="1200" y="690" class="code-text">  grade: ReadGrade): Intent {</text>
  <text x="1210" y="705" class="code-text">intent.putExtra("identity", identity)</text>
  <text x="1210" y="720" class="code-text">intent.putExtra("grade", grade)</text>
  <text x="1200" y="735" class="code-text">}</text>

  <!-- 完整流程时序图 -->
  <rect x="50" y="780" width="1700" height="300" class="flow-box" rx="15"/>
  <text x="900" y="805" text-anchor="middle" class="subtitle">⏱️ 完整时序流程图</text>

  <!-- 时序步骤 -->
  <text x="70" y="835" class="step-text">1️⃣ 应用启动</text>
  <text x="70" y="850" class="text">AndroidManifest.xml → 系统创建Activity → onCreate() → setContentView()</text>

  <text x="70" y="875" class="step-text">2️⃣ 服务绑定</text>
  <text x="70" y="890" class="text">onStart() → bindService(GazeTrackService) → ServiceConnection.onServiceConnected()</text>

  <text x="70" y="915" class="step-text">3️⃣ UI初始化</text>
  <text x="70" y="930" class="text">initView() → initListener() → countdown() → 用户界面准备就绪</text>

  <text x="70" y="955" class="step-text">4️⃣ 功能执行</text>
  <text x="70" y="970" class="text">startRead() → 发送消息到Service → 开启摄像头 → 开始追踪 → 用户操作</text>

  <text x="70" y="995" class="step-text">5️⃣ 数据处理</text>
  <text x="70" y="1010" class="text">用户完成 → 获取轨迹数据 → 创建结果对象 → LiveEventBus传递 → 跳转结果页面</text>

  <text x="70" y="1035" class="step-text">6️⃣ 资源清理</text>
  <text x="70" y="1050" class="text">onStop() → 停止追踪 → 关闭摄像头 → unbindService() → onDestroy()</text>

  <!-- 箭头连接 -->
  <line x1="420" y1="160" x2="450" y2="160" class="thick-arrow"/>
  <line x1="640" y1="280" x2="640" y2="380" class="thick-arrow"/>
  <line x1="1320" y1="220" x2="1320" y2="240" class="thick-arrow"/>
  <line x1="850" y1="480" x2="900" y2="560" class="thick-arrow"/>

  <!-- 关键技术点说明 -->
  <rect x="50" y="1100" width="1700" height="180" class="lifecycle-box" rx="15"/>
  <text x="900" y="1125" text-anchor="middle" class="subtitle">💡 关键技术点和最佳实践</text>

  <text x="70" y="1155" class="step-text">🎯 Activity 职责分离:</text>
  <text x="90" y="1170" class="text">• Activity 负责生命周期管理和Fragment事务</text>
  <text x="90" y="1185" class="text">• Fragment 负责具体UI逻辑和用户交互</text>
  <text x="90" y="1200" class="text">• Service 负责后台数据处理和硬件控制</text>

  <text x="900" y="1155" class="step-text">🎯 数据传递方式:</text>
  <text x="920" y="1170" class="text">• Intent: Activity间参数传递</text>
  <text x="920" y="1185" class="text">• LiveEventBus: 跨组件数据通信</text>
  <text x="920" y="1200" class="text">• Messenger: Activity与Service通信</text>

  <text x="70" y="1230" class="step-text">⚠️ 注意事项:</text>
  <text x="90" y="1245" class="text">• 正确处理生命周期，避免内存泄漏</text>
  <text x="90" y="1260" class="text">• 使用 commitAllowingStateLoss() 避免状态丢失异常</text>

  <text x="900" y="1230" class="step-text">🔧 性能优化:</text>
  <text x="920" y="1245" class="text">• 使用 ViewBinding 替代 findViewById</text>
  <text x="920" y="1260" class="text">• 合理使用 Fragment 缓存和复用</text>

  <!-- 底部总结 -->
  <text x="900" y="1320" text-anchor="middle" class="subtitle">📊 这个流程图展示了真实Android应用中Activity、Fragment、Service协同工作的完整场景</text>
  <text x="900" y="1350" text-anchor="middle" class="text">从配置声明到用户交互，再到数据处理和页面跳转的全链路实现</text>

</svg>
