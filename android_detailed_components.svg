<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; }
      .text { font-family: Arial, sans-serif; font-size: 11px; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; }
      .activity-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .fragment-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .xml-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .lifecycle-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .module-box { fill: #ffebee; stroke: #d32f2f; stroke-width: 2; }
      .flow-box { fill: #e0f2f1; stroke: #00796b; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">Android Activity、Fragment、XML详解与新建模块流程</text>
  
  <!-- Activity详解 -->
  <g id="activity-detail">
    <rect x="20" y="50" width="420" height="280" class="activity-box" rx="10"/>
    <text x="230" y="75" text-anchor="middle" class="subtitle">Activity - 应用界面容器</text>
    
    <!-- Activity作用 -->
    <text x="30" y="95" class="text" font-weight="bold">🎯 主要作用：</text>
    <text x="30" y="110" class="small-text">• 代表一个完整的用户界面屏幕</text>
    <text x="30" y="125" class="small-text">• 管理应用的生命周期和状态</text>
    <text x="30" y="140" class="small-text">• 处理用户交互和系统事件</text>
    <text x="30" y="155" class="small-text">• 协调Fragment和View的显示</text>
    
    <!-- Activity生命周期 -->
    <rect x="30" y="170" width="180" height="150" class="lifecycle-box" rx="5"/>
    <text x="120" y="185" text-anchor="middle" class="text" font-weight="bold">生命周期方法</text>
    <text x="35" y="200" class="code">onCreate() - 创建初始化</text>
    <text x="35" y="215" class="code">onStart() - 开始可见</text>
    <text x="35" y="230" class="code">onResume() - 获得焦点</text>
    <text x="35" y="245" class="code">onPause() - 失去焦点</text>
    <text x="35" y="260" class="code">onStop() - 不可见</text>
    <text x="35" y="275" class="code">onDestroy() - 销毁</text>
    <text x="35" y="290" class="code">onRestart() - 重新启动</text>
    <text x="35" y="305" class="code">onSaveInstanceState()</text>
    
    <!-- Activity示例代码 -->
    <rect x="220" y="170" width="210" height="150" class="activity-box" rx="5"/>
    <text x="325" y="185" text-anchor="middle" class="text" font-weight="bold">实际代码示例</text>
    <text x="225" y="200" class="code">class ROIDetectionActivity :</text>
    <text x="225" y="215" class="code">  GTBaseActivity() {</text>
    <text x="225" y="230" class="code">  </text>
    <text x="225" y="245" class="code">  override fun onCreate(</text>
    <text x="225" y="260" class="code">    savedInstanceState: Bundle?) {</text>
    <text x="225" y="275" class="code">    super.onCreate(savedInstanceState)</text>
    <text x="225" y="290" class="code">    setContentView(R.layout.activity_roi)</text>
    <text x="225" y="305" class="code">    initView() // 初始化界面</text>
    <text x="225" y="320" class="code">  }</text>
  </g>
  
  <!-- Fragment详解 -->
  <g id="fragment-detail">
    <rect x="460" y="50" width="420" height="280" class="fragment-box" rx="10"/>
    <text x="670" y="75" text-anchor="middle" class="subtitle">Fragment - 可重用UI组件</text>
    
    <!-- Fragment作用 -->
    <text x="470" y="95" class="text" font-weight="bold">🎯 主要作用：</text>
    <text x="470" y="110" class="small-text">• 模块化UI组件，可在多个Activity中重用</text>
    <text x="470" y="125" class="small-text">• 支持动态添加、移除、替换</text>
    <text x="470" y="140" class="small-text">• 拥有独立的生命周期</text>
    <text x="470" y="155" class="small-text">• 便于平板和手机适配</text>
    
    <!-- Fragment生命周期 -->
    <rect x="470" y="170" width="180" height="150" class="lifecycle-box" rx="5"/>
    <text x="560" y="185" text-anchor="middle" class="text" font-weight="bold">生命周期方法</text>
    <text x="475" y="200" class="code">onAttach() - 附加到Activity</text>
    <text x="475" y="215" class="code">onCreate() - 创建</text>
    <text x="475" y="230" class="code">onCreateView() - 创建视图</text>
    <text x="475" y="245" class="code">onViewCreated() - 视图创建完成</text>
    <text x="475" y="260" class="code">onStart() - 开始</text>
    <text x="475" y="275" class="code">onResume() - 恢复</text>
    <text x="475" y="290" class="code">onPause() - 暂停</text>
    <text x="475" y="305" class="code">onDestroyView() - 销毁视图</text>
    
    <!-- Fragment示例代码 -->
    <rect x="660" y="170" width="210" height="150" class="fragment-box" rx="5"/>
    <text x="765" y="185" text-anchor="middle" class="text" font-weight="bold">实际代码示例</text>
    <text x="665" y="200" class="code">class FollowAbilityExplainFragment :</text>
    <text x="665" y="215" class="code">  BaseCommonFragment() {</text>
    <text x="665" y="230" class="code">  </text>
    <text x="665" y="245" class="code">  override fun getLayoutResId(): Int {</text>
    <text x="665" y="260" class="code">    return R.layout.fragment_follow</text>
    <text x="665" y="275" class="code">  }</text>
    <text x="665" y="290" class="code">  </text>
    <text x="665" y="305" class="code">  override fun initView() { ... }</text>
    <text x="665" y="320" class="code">}</text>
  </g>
  
  <!-- XML详解 -->
  <g id="xml-detail">
    <rect x="900" y="50" width="480" height="280" class="xml-box" rx="10"/>
    <text x="1140" y="75" text-anchor="middle" class="subtitle">XML - 界面布局定义</text>
    
    <!-- XML作用 -->
    <text x="910" y="95" class="text" font-weight="bold">🎯 主要作用：</text>
    <text x="910" y="110" class="small-text">• 声明式定义UI布局结构</text>
    <text x="910" y="125" class="small-text">• 分离界面设计和业务逻辑</text>
    <text x="910" y="140" class="small-text">• 支持多屏幕适配和国际化</text>
    <text x="910" y="155" class="small-text">• 提供可视化设计工具支持</text>
    
    <!-- XML类型 -->
    <rect x="910" y="170" width="220" height="150" class="xml-box" rx="5"/>
    <text x="1020" y="185" text-anchor="middle" class="text" font-weight="bold">XML文件类型</text>
    <text x="915" y="200" class="code">layout/ - 界面布局文件</text>
    <text x="915" y="215" class="code">values/ - 资源值定义</text>
    <text x="915" y="230" class="code">  ├─ colors.xml - 颜色</text>
    <text x="915" y="245" class="code">  ├─ strings.xml - 字符串</text>
    <text x="915" y="260" class="code">  ├─ dimens.xml - 尺寸</text>
    <text x="915" y="275" class="code">  └─ styles.xml - 样式</text>
    <text x="915" y="290" class="code">drawable/ - 图形资源</text>
    <text x="915" y="305" class="code">menu/ - 菜单定义</text>
    <text x="915" y="320" class="code">anim/ - 动画定义</text>
    
    <!-- XML示例代码 -->
    <rect x="1140" y="170" width="230" height="150" class="xml-box" rx="5"/>
    <text x="1255" y="185" text-anchor="middle" class="text" font-weight="bold">布局示例</text>
    <text x="1145" y="200" class="code">&lt;LinearLayout</text>
    <text x="1145" y="215" class="code">  android:layout_width="match_parent"</text>
    <text x="1145" y="230" class="code">  android:layout_height="match_parent"</text>
    <text x="1145" y="245" class="code">  android:orientation="vertical"&gt;</text>
    <text x="1145" y="260" class="code">  </text>
    <text x="1145" y="275" class="code">  &lt;TextView</text>
    <text x="1145" y="290" class="code">    android:id="@+id/tv_title"</text>
    <text x="1145" y="305" class="code">    android:text="标题" /&gt;</text>
    <text x="1145" y="320" class="code">&lt;/LinearLayout&gt;</text>
  </g>
  
  <!-- 三者关系图 -->
  <g id="relationship">
    <rect x="20" y="350" width="1360" height="180" class="flow-box" rx="10"/>
    <text x="700" y="375" text-anchor="middle" class="subtitle">Activity、Fragment、XML 三者关系</text>
    
    <!-- Activity框 -->
    <rect x="50" y="390" width="200" height="120" class="activity-box" rx="8"/>
    <text x="150" y="410" text-anchor="middle" class="text" font-weight="bold">Activity</text>
    <text x="150" y="430" text-anchor="middle" class="small-text">界面容器</text>
    <text x="60" y="450" class="small-text">• 管理整个屏幕</text>
    <text x="60" y="465" class="small-text">• 处理生命周期</text>
    <text x="60" y="480" class="small-text">• 协调Fragment</text>
    <text x="60" y="495" class="small-text">• 处理系统事件</text>
    
    <!-- Fragment框 -->
    <rect x="300" y="390" width="200" height="120" class="fragment-box" rx="8"/>
    <text x="400" y="410" text-anchor="middle" class="text" font-weight="bold">Fragment</text>
    <text x="400" y="430" text-anchor="middle" class="small-text">UI模块</text>
    <text x="310" y="450" class="small-text">• 可重用组件</text>
    <text x="310" y="465" class="small-text">• 独立生命周期</text>
    <text x="310" y="480" class="small-text">• 动态管理</text>
    <text x="310" y="495" class="small-text">• 业务逻辑处理</text>
    
    <!-- XML框 -->
    <rect x="550" y="390" width="200" height="120" class="xml-box" rx="8"/>
    <text x="650" y="410" text-anchor="middle" class="text" font-weight="bold">XML Layout</text>
    <text x="650" y="430" text-anchor="middle" class="small-text">界面定义</text>
    <text x="560" y="450" class="small-text">• 声明式布局</text>
    <text x="560" y="465" class="small-text">• 视图结构</text>
    <text x="560" y="480" class="small-text">• 样式属性</text>
    <text x="560" y="495" class="small-text">• 资源引用</text>
    
    <!-- 数据流向 -->
    <rect x="800" y="390" width="300" height="120" class="lifecycle-box" rx="8"/>
    <text x="950" y="410" text-anchor="middle" class="text" font-weight="bold">数据流向</text>
    <text x="810" y="430" class="small-text">1. Activity.setContentView(R.layout.xxx)</text>
    <text x="810" y="445" class="small-text">2. FragmentManager.replace(fragment)</text>
    <text x="810" y="460" class="small-text">3. Fragment.onCreateView(inflater, container)</text>
    <text x="810" y="475" class="small-text">4. LayoutInflater.inflate(R.layout.xxx)</text>
    <text x="810" y="490" class="small-text">5. findViewById() 获取View引用</text>
    <text x="810" y="505" class="small-text">6. 设置监听器和数据绑定</text>
    
    <!-- ViewBinding说明 -->
    <rect x="1120" y="390" width="250" height="120" class="module-box" rx="8"/>
    <text x="1245" y="410" text-anchor="middle" class="text" font-weight="bold">ViewBinding优化</text>
    <text x="1130" y="430" class="small-text">• 自动生成绑定类</text>
    <text x="1130" y="445" class="small-text">• 类型安全的View引用</text>
    <text x="1130" y="460" class="small-text">• 避免findViewById()</text>
    <text x="1130" y="475" class="small-text">• 编译时检查</text>
    <text x="1130" y="490" class="small-text">• 性能更好</text>
    <text x="1130" y="505" class="small-text">• 空安全保证</text>
    
    <!-- 连接箭头 -->
    <line x1="250" y1="450" x2="300" y2="450" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="500" y1="450" x2="550" y2="450" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="750" y1="450" x2="800" y2="450" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1100" y1="450" x2="1120" y2="450" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 新建模块流程 -->
  <g id="new-module-flow">
    <rect x="20" y="550" width="1360" height="430" class="module-box" rx="10"/>
    <text x="700" y="575" text-anchor="middle" class="subtitle">🚀 新建模块完整流程</text>
    
    <!-- 步骤1: 需求分析 -->
    <rect x="40" y="590" width="160" height="100" class="lifecycle-box" rx="8"/>
    <text x="120" y="610" text-anchor="middle" class="text" font-weight="bold">1. 需求分析</text>
    <text x="50" y="630" class="small-text">• 确定功能需求</text>
    <text x="50" y="645" class="small-text">• 设计UI界面</text>
    <text x="50" y="660" class="small-text">• 规划数据流</text>
    <text x="50" y="675" class="small-text">• 确定技术方案</text>
    
    <!-- 步骤2: 创建Activity -->
    <rect x="220" y="590" width="160" height="100" class="activity-box" rx="8"/>
    <text x="300" y="610" text-anchor="middle" class="text" font-weight="bold">2. 创建Activity</text>
    <text x="230" y="630" class="small-text">• 继承BaseActivity</text>
    <text x="230" y="645" class="small-text">• 实现生命周期</text>
    <text x="230" y="660" class="small-text">• 配置Intent参数</text>
    <text x="230" y="675" class="small-text">• 注册到Manifest</text>
    
    <!-- 步骤3: 设计XML布局 -->
    <rect x="400" y="590" width="160" height="100" class="xml-box" rx="8"/>
    <text x="480" y="610" text-anchor="middle" class="text" font-weight="bold">3. 设计XML布局</text>
    <text x="410" y="630" class="small-text">• 选择布局容器</text>
    <text x="410" y="645" class="small-text">• 添加UI组件</text>
    <text x="410" y="660" class="small-text">• 设置属性样式</text>
    <text x="410" y="675" class="small-text">• 适配多屏幕</text>
    
    <!-- 步骤4: 创建Fragment -->
    <rect x="580" y="590" width="160" height="100" class="fragment-box" rx="8"/>
    <text x="660" y="610" text-anchor="middle" class="text" font-weight="bold">4. 创建Fragment</text>
    <text x="590" y="630" class="small-text">• 继承BaseFragment</text>
    <text x="590" y="645" class="small-text">• 实现布局绑定</text>
    <text x="590" y="660" class="small-text">• 处理用户交互</text>
    <text x="590" y="675" class="small-text">• 管理状态数据</text>
    
    <!-- 步骤5: 业务逻辑 -->
    <rect x="760" y="590" width="160" height="100" class="flow-box" rx="8"/>
    <text x="840" y="610" text-anchor="middle" class="text" font-weight="bold">5. 业务逻辑</text>
    <text x="770" y="630" class="small-text">• 数据处理逻辑</text>
    <text x="770" y="645" class="small-text">• 事件响应处理</text>
    <text x="770" y="660" class="small-text">• 状态管理</text>
    <text x="770" y="675" class="small-text">• 错误处理</text>
    
    <!-- 步骤6: API接口 -->
    <rect x="940" y="590" width="160" height="100" class="module-box" rx="8"/>
    <text x="1020" y="610" text-anchor="middle" class="text" font-weight="bold">6. API接口</text>
    <text x="950" y="630" class="small-text">• 定义接口方法</text>
    <text x="950" y="645" class="small-text">• 配置网络请求</text>
    <text x="950" y="660" class="small-text">• 数据模型定义</text>
    <text x="950" y="675" class="small-text">• 异常处理</text>
    
    <!-- 步骤7: 数据库设计 -->
    <rect x="1120" y="590" width="160" height="100" class="lifecycle-box" rx="8"/>
    <text x="1200" y="610" text-anchor="middle" class="text" font-weight="bold">7. 数据库设计</text>
    <text x="1130" y="630" class="small-text">• Entity实体类</text>
    <text x="1130" y="645" class="small-text">• DAO接口定义</text>
    <text x="1130" y="660" class="small-text">• 数据库迁移</text>
    <text x="1130" y="675" class="small-text">• Repository封装</text>
    
    <!-- 流程箭头 -->
    <line x1="200" y1="640" x2="220" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="380" y1="640" x2="400" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="560" y1="640" x2="580" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="740" y1="640" x2="760" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="920" y1="640" x2="940" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1100" y1="640" x2="1120" y2="640" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- 具体实现代码示例 -->
    <text x="700" y="720" text-anchor="middle" class="subtitle">📝 具体实现代码示例</text>
    
    <!-- Activity创建示例 -->
    <rect x="40" y="740" width="320" height="120" class="activity-box" rx="8"/>
    <text x="200" y="760" text-anchor="middle" class="text" font-weight="bold">Activity创建示例</text>
    <text x="50" y="780" class="code">class NewModuleActivity : GTBaseActivity() {</text>
    <text x="50" y="795" class="code">  companion object {</text>
    <text x="50" y="810" class="code">    fun createIntent(context: Context): Intent {</text>
    <text x="50" y="825" class="code">      return Intent(context, NewModuleActivity::class.java)</text>
    <text x="50" y="840" class="code">    }</text>
    <text x="50" y="855" class="code">  }</text>
    
    <!-- Fragment创建示例 -->
    <rect x="380" y="740" width="320" height="120" class="fragment-box" rx="8"/>
    <text x="540" y="760" text-anchor="middle" class="text" font-weight="bold">Fragment创建示例</text>
    <text x="390" y="780" class="code">class NewModuleFragment : BaseCommonFragment() {</text>
    <text x="390" y="795" class="code">  companion object {</text>
    <text x="390" y="810" class="code">    fun newInstance(): NewModuleFragment {</text>
    <text x="390" y="825" class="code">      return NewModuleFragment()</text>
    <text x="390" y="840" class="code">    }</text>
    <text x="390" y="855" class="code">  }</text>
    
    <!-- XML布局示例 -->
    <rect x="720" y="740" width="320" height="120" class="xml-box" rx="8"/>
    <text x="880" y="760" text-anchor="middle" class="text" font-weight="bold">XML布局示例</text>
    <text x="730" y="780" class="code">&lt;!-- activity_new_module.xml --&gt;</text>
    <text x="730" y="795" class="code">&lt;FrameLayout</text>
    <text x="730" y="810" class="code">  android:id="@+id/fragment_container"</text>
    <text x="730" y="825" class="code">  android:layout_width="match_parent"</text>
    <text x="730" y="840" class="code">  android:layout_height="match_parent" /&gt;</text>
    
    <!-- Manifest注册示例 -->
    <rect x="1060" y="740" width="320" height="120" class="module-box" rx="8"/>
    <text x="1220" y="760" text-anchor="middle" class="text" font-weight="bold">Manifest注册示例</text>
    <text x="1070" y="780" class="code">&lt;!-- AndroidManifest.xml --&gt;</text>
    <text x="1070" y="795" class="code">&lt;activity</text>
    <text x="1070" y="810" class="code">  android:name=".NewModuleActivity"</text>
    <text x="1070" y="825" class="code">  android:configChanges="orientation|</text>
    <text x="1070" y="840" class="code">    keyboard|keyboardHidden|screenSize" /&gt;</text>
    
    <!-- 最佳实践 -->
    <rect x="40" y="880" width="1320" height="90" class="flow-box" rx="8"/>
    <text x="700" y="900" text-anchor="middle" class="text" font-weight="bold">🎯 最佳实践建议</text>
    <text x="50" y="920" class="small-text">1. 遵循单一职责原则：Activity负责界面管理，Fragment负责具体功能，XML负责布局定义</text>
    <text x="50" y="935" class="small-text">2. 使用ViewBinding替代findViewById，提高类型安全性和性能</text>
    <text x="50" y="950" class="small-text">3. Fragment间通信使用ViewModel或EventBus，避免直接引用</text>
    <text x="50" y="965" class="small-text">4. 合理使用生命周期方法，避免内存泄漏和资源浪费</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>

  <!-- 项目实际结构示例 -->
  <g id="project-structure" transform="translate(0, 1020)">
    <rect x="20" y="0" width="1360" height="400" class="lifecycle-box" rx="10"/>
    <text x="700" y="25" text-anchor="middle" class="subtitle">📁 项目实际结构示例 (基于当前项目)</text>

    <!-- 主要模块结构 -->
    <rect x="40" y="40" width="400" height="350" class="flow-box" rx="8"/>
    <text x="240" y="60" text-anchor="middle" class="text" font-weight="bold">主要模块结构</text>
    <text x="50" y="80" class="code">app/src/main/java/com/mitdd/gazetracker/</text>
    <text x="50" y="95" class="code">├── movement/                    # 眼动评估模块</text>
    <text x="50" y="110" class="code">│   ├── EyeMovementEvaluateActivity.kt</text>
    <text x="50" y="125" class="code">│   ├── gaze/                   # 凝视稳定性</text>
    <text x="50" y="140" class="code">│   │   └── GazeStabilityEvaluateActivity.kt</text>
    <text x="50" y="155" class="code">│   ├── saccade/                # 扫视能力</text>
    <text x="50" y="170" class="code">│   │   └── SaccadeAbilityEvaluateActivity.kt</text>
    <text x="50" y="185" class="code">│   ├── follow/                 # 追随能力</text>
    <text x="50" y="200" class="code">│   │   └── FollowAbilityEvaluateActivity.kt</text>
    <text x="50" y="215" class="code">│   └── roi/                    # ROI检测</text>
    <text x="50" y="230" class="code">│       └── ROIDetectionActivity.kt</text>
    <text x="50" y="245" class="code">├── medicalhome/                # 医疗主页模块</text>
    <text x="50" y="260" class="code">│   ├── HomeMainActivity.kt</text>
    <text x="50" y="275" class="code">│   └── train/                  # 训练模块</text>
    <text x="50" y="290" class="code">├── medicalhospital/            # 医院模块</text>
    <text x="50" y="305" class="code">│   └── HospitalMainFragment.kt</text>
    <text x="50" y="320" class="code">├── read/                       # 阅读模块</text>
    <text x="50" y="335" class="code">│   ├── ReadActivity.kt</text>
    <text x="50" y="350" class="code">│   └── home/</text>
    <text x="50" y="365" class="code">└── base/                       # 基础类</text>
    <text x="50" y="380" class="code">    └── GTBaseActivity.kt</text>

    <!-- 布局文件结构 -->
    <rect x="460" y="40" width="400" height="350" class="xml-box" rx="8"/>
    <text x="660" y="60" text-anchor="middle" class="text" font-weight="bold">布局文件结构</text>
    <text x="470" y="80" class="code">app/src/main/res/</text>
    <text x="470" y="95" class="code">├── layout/                     # 布局文件</text>
    <text x="470" y="110" class="code">│   ├── activity_main.xml</text>
    <text x="470" y="125" class="code">│   ├── activity_roi_detection.xml</text>
    <text x="470" y="140" class="code">│   ├── activity_config.xml</text>
    <text x="470" y="155" class="code">│   ├── fragment_follow_ability_explain.xml</text>
    <text x="470" y="170" class="code">│   └── view_visual_calibration.xml</text>
    <text x="470" y="185" class="code">├── values/                     # 资源值</text>
    <text x="470" y="200" class="code">│   ├── colors.xml             # 颜色定义</text>
    <text x="470" y="215" class="code">│   ├── strings.xml            # 字符串资源</text>
    <text x="470" y="230" class="code">│   ├── dimens.xml             # 尺寸定义</text>
    <text x="470" y="245" class="code">│   └── styles.xml             # 样式定义</text>
    <text x="470" y="260" class="code">├── drawable/                   # 图形资源</text>
    <text x="470" y="275" class="code">│   ├── icon_main_bg.xml</text>
    <text x="470" y="290" class="code">│   └── selector_button.xml</text>
    <text x="470" y="305" class="code">├── mipmap/                     # 应用图标</text>
    <text x="470" y="320" class="code">│   └── ic_launcher.png</text>
    <text x="470" y="335" class="code">└── menu/                       # 菜单定义</text>
    <text x="470" y="350" class="code">    └── main_menu.xml</text>

    <!-- Manifest配置 -->
    <rect x="880" y="40" width="480" height="350" class="module-box" rx="8"/>
    <text x="1120" y="60" text-anchor="middle" class="text" font-weight="bold">AndroidManifest.xml 配置示例</text>
    <text x="890" y="80" class="code">&lt;?xml version="1.0" encoding="utf-8"?&gt;</text>
    <text x="890" y="95" class="code">&lt;manifest xmlns:android="http://schemas.android.com/apk/res/android"&gt;</text>
    <text x="890" y="110" class="code">  </text>
    <text x="890" y="125" class="code">  &lt;!-- 权限声明 --&gt;</text>
    <text x="890" y="140" class="code">  &lt;uses-permission android:name="android.permission.CAMERA" /&gt;</text>
    <text x="890" y="155" class="code">  &lt;uses-permission android:name="android.permission.INTERNET" /&gt;</text>
    <text x="890" y="170" class="code">  </text>
    <text x="890" y="185" class="code">  &lt;application&gt;</text>
    <text x="890" y="200" class="code">    &lt;!-- 主Activity --&gt;</text>
    <text x="890" y="215" class="code">    &lt;activity android:name=".LauncherActivity"</text>
    <text x="890" y="230" class="code">      android:exported="true"&gt;</text>
    <text x="890" y="245" class="code">      &lt;intent-filter&gt;</text>
    <text x="890" y="260" class="code">        &lt;action android:name="android.intent.action.MAIN" /&gt;</text>
    <text x="890" y="275" class="code">        &lt;category android:name="android.intent.category.LAUNCHER" /&gt;</text>
    <text x="890" y="290" class="code">      &lt;/intent-filter&gt;</text>
    <text x="890" y="305" class="code">    &lt;/activity&gt;</text>
    <text x="890" y="320" class="code">    </text>
    <text x="890" y="335" class="code">    &lt;!-- ROI检测Activity --&gt;</text>
    <text x="890" y="350" class="code">    &lt;activity android:name=".movement.roi.ROIDetectionActivity"</text>
    <text x="890" y="365" class="code">      android:configChanges="orientation|keyboard" /&gt;</text>
    <text x="890" y="380" class="code">  &lt;/application&gt;</text>
  </g>

  <!-- 开发工作流程 -->
  <g id="development-workflow" transform="translate(0, 1440)">
    <rect x="20" y="0" width="1360" height="300" class="activity-box" rx="10"/>
    <text x="700" y="25" text-anchor="middle" class="subtitle">🔄 实际开发工作流程</text>

    <!-- 第一行流程 -->
    <rect x="40" y="40" width="180" height="80" class="lifecycle-box" rx="8"/>
    <text x="130" y="60" text-anchor="middle" class="text" font-weight="bold">1. 创建Activity</text>
    <text x="50" y="80" class="small-text">• 继承GTBaseActivity</text>
    <text x="50" y="95" class="small-text">• 实现createIntent方法</text>
    <text x="50" y="110" class="small-text">• 配置生命周期</text>

    <rect x="240" y="40" width="180" height="80" class="xml-box" rx="8"/>
    <text x="330" y="60" text-anchor="middle" class="text" font-weight="bold">2. 设计布局</text>
    <text x="250" y="80" class="small-text">• 创建activity_xxx.xml</text>
    <text x="250" y="95" class="small-text">• 选择合适的布局容器</text>
    <text x="250" y="110" class="small-text">• 添加UI组件</text>

    <rect x="440" y="40" width="180" height="80" class="fragment-box" rx="8"/>
    <text x="530" y="60" text-anchor="middle" class="text" font-weight="bold">3. 创建Fragment</text>
    <text x="450" y="80" class="small-text">• 继承BaseCommonFragment</text>
    <text x="450" y="95" class="small-text">• 实现getLayoutResId</text>
    <text x="450" y="110" class="small-text">• 重写initView方法</text>

    <rect x="640" y="40" width="180" height="80" class="flow-box" rx="8"/>
    <text x="730" y="60" text-anchor="middle" class="text" font-weight="bold">4. 绑定视图</text>
    <text x="650" y="80" class="small-text">• 使用ViewBinding</text>
    <text x="650" y="95" class="small-text">• 或findViewById获取引用</text>
    <text x="650" y="110" class="small-text">• 设置监听器</text>

    <rect x="840" y="40" width="180" height="80" class="module-box" rx="8"/>
    <text x="930" y="60" text-anchor="middle" class="text" font-weight="bold">5. 实现逻辑</text>
    <text x="850" y="80" class="small-text">• 处理用户交互</text>
    <text x="850" y="95" class="small-text">• 调用API接口</text>
    <text x="850" y="110" class="small-text">• 更新UI状态</text>

    <rect x="1040" y="40" width="180" height="80" class="lifecycle-box" rx="8"/>
    <text x="1130" y="60" text-anchor="middle" class="text" font-weight="bold">6. 注册配置</text>
    <text x="1050" y="80" class="small-text">• 添加到Manifest</text>
    <text x="1050" y="95" class="small-text">• 配置Intent跳转</text>
    <text x="1050" y="110" class="small-text">• 测试功能</text>

    <!-- 连接箭头 -->
    <line x1="220" y1="80" x2="240" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="420" y1="80" x2="440" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="620" y1="80" x2="640" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="820" y1="80" x2="840" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="1020" y1="80" x2="1040" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

    <!-- 具体代码实现步骤 -->
    <text x="700" y="150" text-anchor="middle" class="text" font-weight="bold">📋 具体代码实现步骤</text>

    <!-- 步骤详解 -->
    <rect x="40" y="170" width="640" height="120" class="activity-box" rx="8"/>
    <text x="360" y="190" text-anchor="middle" class="text" font-weight="bold">Activity实现步骤</text>
    <text x="50" y="210" class="code">1. class NewFeatureActivity : GTBaseActivity() {</text>
    <text x="50" y="225" class="code">2.   override fun onCreate(savedInstanceState: Bundle?) {</text>
    <text x="50" y="240" class="code">3.     super.onCreate(savedInstanceState)</text>
    <text x="50" y="255" class="code">4.     setContentView(R.layout.activity_new_feature)</text>
    <text x="50" y="270" class="code">5.     initView() // 初始化视图和监听器</text>
    <text x="50" y="285" class="code">6.   }</text>

    <rect x="700" y="170" width="640" height="120" class="fragment-box" rx="8"/>
    <text x="1020" y="190" text-anchor="middle" class="text" font-weight="bold">Fragment实现步骤</text>
    <text x="710" y="210" class="code">1. class NewFeatureFragment : BaseCommonFragment() {</text>
    <text x="710" y="225" class="code">2.   override fun getLayoutResId() = R.layout.fragment_new_feature</text>
    <text x="710" y="240" class="code">3.   override fun initView() {</text>
    <text x="710" y="255" class="code">4.     // 初始化UI组件和事件监听</text>
    <text x="710" y="270" class="code">5.     setupClickListeners()</text>
    <text x="710" y="285" class="code">6.   }</text>
  </g>
</svg>
