<svg width="2000" height="2200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 30px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .lifecycle-text { font-family: Arial, sans-serif; font-size: 18px; fill: #e74c3c; font-weight: bold; }
      .action-text { font-family: Arial, sans-serif; font-size: 14px; fill: #27ae60; font-weight: bold; }
      .scenario-text { font-family: Arial, sans-serif; font-size: 13px; fill: #8e44ad; font-weight: bold; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .fragment-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .lifecycle-box { fill: #f0f8ff; stroke: #4169e1; stroke-width: 2; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 2; }
      .scenario-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .flow-arrow { stroke: #e74c3c; stroke-width: 4; fill: none; marker-end: url(#redarrowhead); }
      .connect-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="1000" y="35" text-anchor="middle" class="title">Android 生命周期具体场景详解</text>
  <text x="1000" y="65" text-anchor="middle" class="subtitle">基于真实代码的多个Activity和Fragment生命周期实现</text>

  <!-- 场景1: ReadActivity 阅读追踪场景 -->
  <rect x="50" y="90" width="950" height="500" class="activity-box" rx="15"/>
  <text x="525" y="115" text-anchor="middle" class="subtitle">📖 场景1: ReadActivity 阅读追踪生命周期</text>

  <!-- onCreate -->
  <rect x="70" y="130" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="150" class="lifecycle-text">onCreate() - 阅读界面初始化</text>
  <text x="110" y="170" class="code-text">setContentView(R.layout.activity_read)  // 加载阅读界面布局</text>
  <text x="110" y="185" class="code-text">initParam() → 解析身份(mIdentity)和年级(mGrade)参数</text>
  <text x="110" y="200" class="code-text">initView() → 设置倒计时3秒，完成后自动开始阅读</text>

  <!-- onStart -->
  <rect x="70" y="220" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="240" class="lifecycle-text">onStart() - 绑定视线追踪服务</text>
  <text x="110" y="260" class="code-text">bindService(Intent(this, GazeTrackService::class.java), serviceConnection, BIND_AUTO_CREATE)</text>
  <text x="110" y="275" class="scenario-text">🎯 场景：准备开始阅读，需要连接摄像头和追踪服务</text>
  <text x="110" y="290" class="small-text">→ ServiceConnection.onServiceConnected() 建立通信通道</text>

  <!-- 用户交互阶段 -->
  <rect x="70" y="310" width="910" height="120" class="scenario-box" rx="10"/>
  <text x="90" y="330" class="lifecycle-text">用户交互阶段 - 阅读过程</text>
  <text x="110" y="350" class="code-text">倒计时结束 → startRead() → 发送消息序列：</text>
  <text x="120" y="365" class="code-text">  MSG_TURN_ON_CAMERA    // 开启摄像头</text>
  <text x="120" y="380" class="code-text">  MSG_START_TRACK       // 开始视线追踪</text>
  <text x="120" y="395" class="code-text">  MSG_START_APPLIED_READING  // 开始应用阅读模式</text>
  <text x="110" y="415" class="scenario-text">🎯 场景：用户正在阅读，系统记录视线轨迹数据</text>

  <!-- onStop -->
  <rect x="70" y="440" width="910" height="120" class="lifecycle-box" rx="10"/>
  <text x="90" y="460" class="lifecycle-text">onStop() - 停止追踪和清理资源</text>
  <text x="110" y="480" class="code-text">发送停止消息序列：</text>
  <text x="120" y="495" class="code-text">  MSG_STOP_APPLIED_READING  // 停止阅读模式</text>
  <text x="120" y="510" class="code-text">  MSG_STOP_TRACK           // 停止视线追踪</text>
  <text x="120" y="525" class="code-text">  MSG_TURN_OFF_CAMERA      // 关闭摄像头</text>
  <text x="110" y="545" class="code-text">unbindService(serviceConnection)  // 解绑服务</text>
  <text x="110" y="560" class="scenario-text">🎯 场景：阅读完成或用户离开，释放硬件资源</text>

  <!-- 场景2: TrainWebActivity 训练场景 -->
  <rect x="1020" y="90" width="950" height="500" class="activity-box" rx="15"/>
  <text x="1495" y="115" text-anchor="middle" class="subtitle">🎮 场景2: TrainWebActivity 训练生命周期</text>

  <!-- onStart -->
  <rect x="1040" y="130" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="150" class="lifecycle-text">onStart() - 启动训练服务</text>
  <text x="1080" y="170" class="code-text">bindService(Intent(this, GazeTrackService::class.java), serviceConnection, BIND_AUTO_CREATE)</text>
  <text x="1080" y="185" class="code-text">handler.postDelayed({ openGazeTracker() }, 200)  // 延迟200ms开启追踪</text>
  <text x="1080" y="200" class="scenario-text">🎯 场景：进入训练模式，准备开始视觉训练</text>

  <!-- onResume -->
  <rect x="1040" y="220" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="240" class="lifecycle-text">onResume() - 恢复训练状态</text>
  <text x="1080" y="260" class="code-text">if (isGameStart) { startKeepTime() }  // 如果游戏已开始，恢复计时</text>
  <text x="1080" y="275" class="scenario-text">🎯 场景：从后台回到前台，恢复训练进度</text>
  <text x="1080" y="290" class="small-text">→ 保持训练的连续性，不中断用户体验</text>

  <!-- onPause -->
  <rect x="1040" y="310" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="330" class="lifecycle-text">onPause() - 暂停训练</text>
  <text x="1080" y="350" class="code-text">stopKeepTime()  // 停止计时</text>
  <text x="1080" y="365" class="scenario-text">🎯 场景：用户切换到其他应用，暂停训练但保持状态</text>
  <text x="1080" y="380" class="small-text">→ 避免在后台消耗资源，但保留训练进度</text>

  <!-- onStop -->
  <rect x="1040" y="400" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="420" class="lifecycle-text">onStop() - 停止追踪服务</text>
  <text x="1080" y="440" class="code-text">closeGazeTracker()  // 关闭视线追踪器</text>
  <text x="1080" y="455" class="code-text">unbindService(serviceConnection)  // 解绑服务</text>
  <text x="1080" y="470" class="scenario-text">🎯 场景：训练界面不可见，释放追踪资源</text>

  <!-- onDestroy -->
  <rect x="1040" y="490" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="510" class="lifecycle-text">onDestroy() - 完全清理</text>
  <text x="1080" y="530" class="code-text">stopBehaviorAnalysis()  // 停止行为分析</text>
  <text x="1080" y="545" class="code-text">trainWebView.destroy()  // 销毁WebView</text>
  <text x="1080" y="560" class="scenario-text">🎯 场景：训练结束，彻底清理所有资源</text>

  <!-- 场景3: MHospitalMTActivity 医院治疗场景 -->
  <rect x="50" y="610" width="950" height="400" class="activity-box" rx="15"/>
  <text x="525" y="635" text-anchor="middle" class="subtitle">🏥 场景3: MHospitalMTActivity 医院治疗生命周期</text>

  <!-- onCreate -->
  <rect x="70" y="650" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="670" class="lifecycle-text">onCreate() - 医院治疗初始化</text>
  <text x="110" y="690" class="code-text">binding = ActivityMHospitalMtBinding.inflate(layoutInflater)  // ViewBinding</text>
  <text x="110" y="705" class="code-text">initParam() → mAccessToken = intent.getStringExtra("authorization")</text>
  <text x="110" y="720" class="scenario-text">🎯 场景：医院模式，需要授权token进行患者数据访问</text>

  <!-- onResume/onPause -->
  <rect x="70" y="740" width="910" height="100" class="lifecycle-box" rx="10"/>
  <text x="90" y="760" class="lifecycle-text">onResume()/onPause() - 服务绑定策略</text>
  <text x="110" y="780" class="code-text">onResume() → bindService(GazeTrackService)  // 在Resume时绑定</text>
  <text x="110" y="795" class="code-text">onPause() → unbindService(serviceConnection)  // 在Pause时解绑</text>
  <text x="110" y="815" class="scenario-text">🎯 场景：医院环境频繁切换，采用Resume/Pause绑定策略</text>
  <text x="110" y="830" class="small-text">→ 与ReadActivity不同，这里在Resume/Pause而非Start/Stop绑定服务</text>

  <!-- finish() 重写 -->
  <rect x="70" y="850" width="910" height="140" class="scenario-box" rx="10"/>
  <text x="90" y="870" class="lifecycle-text">finish() - 智能退出处理</text>
  <text x="110" y="890" class="code-text">override fun finish() {</text>
  <text x="120" y="905" class="code-text">  val state = DeviceManager.getMaskTherapyState()  // 检查治疗状态</text>
  <text x="120" y="920" class="code-text">  if (state) {  // 如果正在治疗</text>
  <text x="130" y="935" class="code-text">    sendMessageToService(MSG_STOP_APPLIED_CURE, MSG_STOP_TRACK, MSG_TURN_OFF_CAMERA)</text>
  <text x="130" y="950" class="code-text">    handler.postDelayed({ super.finish() }, 300)  // 延迟300ms确保停止完成</text>
  <text x="120" y="965" class="code-text">  } else { super.finish() }  // 直接退出</text>
  <text x="110" y="980" class="scenario-text">🎯 场景：智能判断治疗状态，确保安全退出</text>

  <!-- 场景4: Fragment生命周期对比 -->
  <rect x="1020" y="610" width="950" height="400" class="fragment-box" rx="15"/>
  <text x="1495" y="635" text-anchor="middle" class="subtitle">🧩 场景4: Fragment生命周期对比</text>

  <!-- FollowAbilityExplainFragment -->
  <rect x="1040" y="650" width="910" height="120" class="lifecycle-box" rx="10"/>
  <text x="1060" y="670" class="lifecycle-text">FollowAbilityExplainFragment - 说明页面</text>
  <text x="1080" y="690" class="code-text">getLayoutResId() → R.layout.fragment_follow_ability_explain</text>
  <text x="1080" y="705" class="code-text">initView() → 设置返回按钮和开始评估按钮</text>
  <text x="1080" y="720" class="code-text">Glide.load(R.drawable.follow_ability_explain).into(ivDemonstration)</text>
  <text x="1080" y="740" class="scenario-text">🎯 场景：静态说明页面，主要展示GIF动画和按钮交互</text>
  <text x="1080" y="755" class="small-text">→ 轻量级Fragment，无复杂业务逻辑</text>

  <!-- FollowAbilityEvaluatingFragment -->
  <rect x="1040" y="780" width="910" height="120" class="lifecycle-box" rx="10"/>
  <text x="1060" y="800" class="lifecycle-text">FollowAbilityEvaluatingFragment - 评估页面</text>
  <text x="1080" y="820" class="code-text">initParam() → 设置屏幕坐标范围参数</text>
  <text x="1080" y="835" class="code-text">initView() → startPromptCountdown(3000) 启动倒计时</text>
  <text x="1080" y="850" class="code-text">onStart() → bindService(GazeTrackService)  // Fragment也可以绑定服务</text>
  <text x="1080" y="870" class="scenario-text">🎯 场景：复杂评估页面，需要服务交互和实时数据处理</text>
  <text x="1080" y="885" class="small-text">→ 重量级Fragment，包含完整的业务逻辑和生命周期管理</text>

  <!-- HomeMainFragment -->
  <rect x="1040" y="910" width="910" height="80" class="lifecycle-box" rx="10"/>
  <text x="1060" y="930" class="lifecycle-text">HomeMainFragment - 主页Fragment</text>
  <text x="1080" y="950" class="code-text">onCreate() → startRefreshBindUser(), BehaviorGuidanceManager.initialize()</text>
  <text x="1080" y="965" class="code-text">onDestroyView() → cancelRefreshBindUser(), BehaviorGuidanceManager.release()</text>
  <text x="1080" y="980" class="scenario-text">🎯 场景：长期存在的主页Fragment，管理全局状态和资源</text>

  <!-- 生命周期对比总结 -->
  <rect x="50" y="1030" width="1920" height="200" class="code-box" rx="15"/>
  <text x="1010" y="1055" text-anchor="middle" class="subtitle">📊 不同场景下的生命周期策略对比</text>

  <!-- 对比表格 -->
  <rect x="70" y="1080" width="460" height="140" class="scenario-box" rx="10"/>
  <text x="300" y="1100" text-anchor="middle" class="action-text">ReadActivity (阅读场景)</text>
  <text x="80" y="1120" class="text">• 服务绑定：onStart/onStop</text>
  <text x="80" y="1135" class="text">• 硬件控制：消息序列控制摄像头</text>
  <text x="80" y="1150" class="text">• 数据处理：轨迹数据收集和传递</text>
  <text x="80" y="1165" class="text">• 退出策略：简单的资源清理</text>
  <text x="80" y="1180" class="text">• 特点：一次性任务，线性流程</text>
  <text x="80" y="1195" class="small-text">适用：短期功能性Activity</text>

  <rect x="550" y="1080" width="460" height="140" class="scenario-box" rx="10"/>
  <text x="780" y="1100" text-anchor="middle" class="action-text">TrainWebActivity (训练场景)</text>
  <text x="560" y="1120" class="text">• 服务绑定：onStart/onStop</text>
  <text x="560" y="1135" class="text">• 状态管理：onResume/onPause计时控制</text>
  <text x="560" y="1150" class="text">• 资源管理：WebView和行为分析</text>
  <text x="560" y="1165" class="text">• 退出策略：多层资源清理</text>
  <text x="560" y="1180" class="text">• 特点：长期运行，状态保持</text>
  <text x="560" y="1195" class="small-text">适用：长期交互性Activity</text>

  <rect x="1030" y="1080" width="460" height="140" class="scenario-box" rx="10"/>
  <text x="1260" y="1100" text-anchor="middle" class="action-text">MHospitalMTActivity (医院场景)</text>
  <text x="1040" y="1120" class="text">• 服务绑定：onResume/onPause</text>
  <text x="1040" y="1135" class="text">• 权限管理：授权token处理</text>
  <text x="1040" y="1150" class="text">• 智能退出：状态检查后退出</text>
  <text x="1040" y="1165" class="text">• 安全性：确保治疗过程完整</text>
  <text x="1040" y="1180" class="text">• 特点：专业场景，安全优先</text>
  <text x="1040" y="1195" class="small-text">适用：专业医疗环境</text>

  <rect x="1510" y="1080" width="460" height="140" class="scenario-box" rx="10"/>
  <text x="1740" y="1100" text-anchor="middle" class="action-text">Fragment场景对比</text>
  <text x="1520" y="1120" class="text">• 轻量级：说明页面，简单交互</text>
  <text x="1520" y="1135" class="text">• 重量级：评估页面，服务绑定</text>
  <text x="1520" y="1150" class="text">• 长期型：主页Fragment，全局管理</text>
  <text x="1520" y="1165" class="text">• 通信：Activity引用和回调</text>
  <text x="1520" y="1180" class="text">• 特点：模块化，可重用</text>
  <text x="1520" y="1195" class="small-text">适用：UI模块化设计</text>

  <!-- 关键技术点 -->
  <rect x="50" y="1250" width="1920" height="180" class="lifecycle-box" rx="15"/>
  <text x="1010" y="1275" text-anchor="middle" class="subtitle">🔧 关键技术点和最佳实践</text>

  <text x="70" y="1305" class="action-text">🎯 服务绑定策略选择：</text>
  <text x="90" y="1320" class="text">• onStart/onStop：适用于Activity可见期间需要服务的场景</text>
  <text x="90" y="1335" class="text">• onResume/onPause：适用于需要频繁暂停恢复的场景</text>

  <text x="70" y="1360" class="action-text">🎯 资源管理策略：</text>
  <text x="90" y="1375" class="text">• 硬件资源：摄像头、传感器在onStop时释放</text>
  <text x="90" y="1390" class="text">• 内存资源：WebView、大对象在onDestroy时清理</text>
  <text x="90" y="1405" class="text">• 网络资源：在适当的生命周期阶段取消请求</text>

  <text x="1000" y="1305" class="action-text">⚠️ 常见陷阱避免：</text>
  <text x="1020" y="1320" class="text">• 避免在onPause中进行耗时操作</text>
  <text x="1020" y="1335" class="text">• 确保服务绑定和解绑成对出现</text>
  <text x="1020" y="1350" class="text">• Fragment中避免持有Activity强引用</text>

  <text x="1000" y="1375" class="action-text">💡 性能优化：</text>
  <text x="1020" y="1390" class="text">• 延迟初始化非关键资源</text>
  <text x="1020" y="1405" class="text">• 使用生命周期感知组件</text>

  <!-- 流程箭头 -->
  <line x1="525" y1="210" x2="525" y2="220" class="flow-arrow"/>
  <line x1="525" y1="300" x2="525" y2="310" class="flow-arrow"/>
  <line x1="525" y1="430" x2="525" y2="440" class="flow-arrow"/>

  <line x1="1495" y1="210" x2="1495" y2="220" class="flow-arrow"/>
  <line x1="1495" y1="300" x2="1495" y2="310" class="flow-arrow"/>
  <line x1="1495" y1="390" x2="1495" y2="400" class="flow-arrow"/>
  <line x1="1495" y1="480" x2="1495" y2="490" class="flow-arrow"/>

  <!-- 底部总结 -->
  <text x="1010" y="1460" text-anchor="middle" class="subtitle">📈 这个详细图表展示了不同业务场景下Android生命周期的具体实现策略</text>
  <text x="1010" y="1490" text-anchor="middle" class="text">从简单的阅读功能到复杂的医疗应用，每种场景都有其特定的生命周期管理需求</text>

</svg>
