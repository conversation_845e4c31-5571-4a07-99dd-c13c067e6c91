<svg width="1900" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .lifecycle-text { font-family: Arial, sans-serif; font-size: 16px; fill: #e74c3c; font-weight: bold; }
      .action-text { font-family: Arial, sans-serif; font-size: 13px; fill: #27ae60; font-weight: bold; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .fragment-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .lifecycle-box { fill: #f0f8ff; stroke: #4169e1; stroke-width: 2; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 2; }
      .flow-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#redarrowhead); }
      .connect-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="950" y="30" text-anchor="middle" class="title">Android 生命周期详解：每个阶段具体做什么</text>
  <text x="950" y="55" text-anchor="middle" class="subtitle">基于 ReadActivity 和 FollowAbilityExplainFragment 的真实代码实现</text>

  <!-- Activity 生命周期详解 -->
  <rect x="50" y="80" width="900" height="800" class="activity-box" rx="15"/>
  <text x="500" y="105" text-anchor="middle" class="subtitle">🏗️ Activity 生命周期详解 (ReadActivity)</text>

  <!-- onCreate() -->
  <rect x="70" y="120" width="860" height="120" class="lifecycle-box" rx="10"/>
  <text x="90" y="145" class="lifecycle-text">1️⃣ onCreate(savedInstanceState: Bundle?)</text>
  <text x="110" y="165" class="action-text">🎯 主要作用：Activity创建时的一次性初始化</text>
  <text x="110" y="185" class="code-text">super.onCreate(savedInstanceState)  // 调用父类初始化</text>
  <text x="110" y="200" class="code-text">setContentView(R.layout.activity_read)  // 加载XML布局，创建View树</text>
  <text x="110" y="215" class="code-text">initParam()  // 解析Intent参数：mIdentity, mGrade</text>
  <text x="110" y="230" class="code-text">initView()   // 初始化UI控件，设置监听器，启动倒计时</text>

  <!-- onStart() -->
  <rect x="70" y="260" width="860" height="100" class="lifecycle-box" rx="10"/>
  <text x="90" y="285" class="lifecycle-text">2️⃣ onStart()</text>
  <text x="110" y="305" class="action-text">🎯 主要作用：Activity变为可见，绑定外部服务</text>
  <text x="110" y="325" class="code-text">super.onStart()</text>
  <text x="110" y="340" class="code-text">bindService(Intent(this, GazeTrackService::class.java), serviceConnection, BIND_AUTO_CREATE)</text>
  <text x="110" y="355" class="small-text">→ 绑定视线追踪服务，建立Activity与Service的通信连接</text>

  <!-- onResume() -->
  <rect x="70" y="380" width="860" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="405" class="lifecycle-text">3️⃣ onResume()</text>
  <text x="110" y="425" class="action-text">🎯 主要作用：Activity获得焦点，可以与用户交互</text>
  <text x="110" y="445" class="code-text">super.onResume()  // 在ReadActivity中没有额外实现，使用默认行为</text>

  <!-- 用户交互阶段 -->
  <rect x="70" y="480" width="860" height="120" class="code-box" rx="10"/>
  <text x="90" y="505" class="lifecycle-text">🎮 用户交互阶段</text>
  <text x="110" y="525" class="action-text">🎯 主要作用：响应用户操作，执行业务逻辑</text>
  <text x="110" y="545" class="code-text">倒计时完成 → startRead() → 发送消息到Service</text>
  <text x="110" y="560" class="code-text">用户点击完成 → endTime = System.currentTimeMillis() → 获取轨迹数据</text>
  <text x="110" y="575" class="code-text">parseMessage() → 处理Service返回的数据 → 跳转结果页面</text>
  <text x="110" y="590" class="code-text">LiveEventBus.post(readResult) → startActivity(ReadResultAnalysisActivity)</text>

  <!-- onPause() -->
  <rect x="70" y="620" width="860" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="645" class="lifecycle-text">4️⃣ onPause()</text>
  <text x="110" y="665" class="action-text">🎯 主要作用：Activity失去焦点但仍可见，暂停操作</text>
  <text x="110" y="685" class="code-text">super.onPause()  // 在ReadActivity中没有额外实现，使用默认行为</text>

  <!-- onStop() -->
  <rect x="70" y="720" width="860" height="120" class="lifecycle-box" rx="10"/>
  <text x="90" y="745" class="lifecycle-text">5️⃣ onStop()</text>
  <text x="110" y="765" class="action-text">🎯 主要作用：Activity不可见，停止服务和释放资源</text>
  <text x="110" y="785" class="code-text">super.onStop()</text>
  <text x="110" y="800" class="code-text">sendMessageToService(MSG_STOP_APPLIED_READING)  // 停止阅读模式</text>
  <text x="110" y="815" class="code-text">sendMessageToService(MSG_STOP_TRACK)           // 停止视线追踪</text>
  <text x="110" y="830" class="code-text">sendMessageToService(MSG_TURN_OFF_CAMERA)      // 关闭摄像头</text>
  <text x="110" y="845" class="code-text">unbindService(serviceConnection)               // 解绑服务</text>

  <!-- onDestroy() -->
  <rect x="70" y="860" width="860" height="80" class="lifecycle-box" rx="10"/>
  <text x="90" y="885" class="lifecycle-text">6️⃣ onDestroy()</text>
  <text x="110" y="905" class="action-text">🎯 主要作用：Activity销毁，最终清理</text>
  <text x="110" y="925" class="code-text">super.onDestroy()  // 在ReadActivity中没有额外实现，由父类处理资源清理</text>

  <!-- Fragment 生命周期详解 -->
  <rect x="980" y="80" width="870" height="800" class="fragment-box" rx="15"/>
  <text x="1415" y="105" text-anchor="middle" class="subtitle">🧩 Fragment 生命周期详解 (FollowAbilityExplainFragment)</text>

  <!-- newInstance() -->
  <rect x="1000" y="120" width="830" height="80" class="code-box" rx="10"/>
  <text x="1020" y="145" class="lifecycle-text">0️⃣ newInstance() - 静态工厂方法</text>
  <text x="1040" y="165" class="action-text">🎯 主要作用：创建Fragment实例，传递参数</text>
  <text x="1040" y="185" class="code-text">return FollowAbilityExplainFragment()  // 创建新实例，可以通过Bundle传递参数</text>

  <!-- onAttach() -->
  <rect x="1000" y="220" width="830" height="80" class="lifecycle-box" rx="10"/>
  <text x="1020" y="245" class="lifecycle-text">1️⃣ onAttach(context: Context)</text>
  <text x="1040" y="265" class="action-text">🎯 主要作用：Fragment附加到Activity，获取Context</text>
  <text x="1040" y="285" class="code-text">super.onAttach(context)  // 由BaseCommonFragment处理，保存Activity引用</text>

  <!-- onCreate() -->
  <rect x="1000" y="320" width="830" height="80" class="lifecycle-box" rx="10"/>
  <text x="1020" y="345" class="lifecycle-text">2️⃣ onCreate(savedInstanceState: Bundle?)</text>
  <text x="1040" y="365" class="action-text">🎯 主要作用：Fragment创建，初始化非UI相关的数据</text>
  <text x="1040" y="385" class="code-text">super.onCreate(savedInstanceState)  // 由BaseCommonFragment处理基础初始化</text>

  <!-- onCreateView() -->
  <rect x="1000" y="420" width="830" height="100" class="lifecycle-box" rx="10"/>
  <text x="1020" y="445" class="lifecycle-text">3️⃣ onCreateView(inflater, container, savedInstanceState)</text>
  <text x="1040" y="465" class="action-text">🎯 主要作用：创建Fragment的UI视图</text>
  <text x="1040" y="485" class="code-text">getLayoutResId() → R.layout.fragment_follow_ability_explain</text>
  <text x="1040" y="500" class="code-text">LayoutInflater.inflate(layoutResId, container, false)  // 加载XML布局</text>
  <text x="1040" y="515" class="small-text">→ 返回Fragment的根View，但此时还不能操作子View</text>

  <!-- onViewCreated() -->
  <rect x="1000" y="540" width="830" height="120" class="lifecycle-box" rx="10"/>
  <text x="1020" y="565" class="lifecycle-text">4️⃣ onViewCreated(view: View, savedInstanceState: Bundle?)</text>
  <text x="1040" y="585" class="action-text">🎯 主要作用：View创建完成，可以进行UI操作</text>
  <text x="1040" y="605" class="code-text">super.onViewCreated(view, savedInstanceState)</text>
  <text x="1040" y="620" class="code-text">initView()  // 调用自定义初始化方法</text>
  <text x="1040" y="635" class="code-text">initData()  // 初始化数据</text>
  <text x="1040" y="650" class="code-text">initObserver()  // 设置数据观察者</text>

  <!-- initView() 详细实现 -->
  <rect x="1000" y="680" width="830" height="140" class="code-box" rx="10"/>
  <text x="1020" y="705" class="lifecycle-text">📱 initView() 具体实现</text>
  <text x="1040" y="725" class="action-text">🎯 主要作用：设置UI控件的事件监听器和初始状态</text>
  <text x="1040" y="745" class="code-text">ivBack.setOnSingleClickListener { mActivity.finish() }</text>
  <text x="1040" y="760" class="code-text">tvStartEvaluating.setOnSingleClickListener {</text>
  <text x="1050" y="775" class="code-text">  (mActivity as? FollowAbilityEvaluateActivity)?.showFollowAbilityEvaluating()</text>
  <text x="1040" y="790" class="code-text">}</text>
  <text x="1040" y="805" class="code-text">Glide.load(R.drawable.follow_ability_explain).into(ivDemonstration)</text>

  <!-- onDestroyView() -->
  <rect x="1000" y="840" width="830" height="80" class="lifecycle-box" rx="10"/>
  <text x="1020" y="865" class="lifecycle-text">5️⃣ onDestroyView()</text>
  <text x="1040" y="885" class="action-text">🎯 主要作用：销毁Fragment的View，清理UI相关资源</text>
  <text x="1040" y="905" class="code-text">super.onDestroyView()  // 由BaseCommonFragment处理View清理</text>

  <!-- 生命周期流程箭头 -->
  <line x1="500" y1="240" x2="500" y2="260" class="flow-arrow"/>
  <line x1="500" y1="360" x2="500" y2="380" class="flow-arrow"/>
  <line x1="500" y1="460" x2="500" y2="480" class="flow-arrow"/>
  <line x1="500" y1="600" x2="500" y2="620" class="flow-arrow"/>
  <line x1="500" y1="700" x2="500" y2="720" class="flow-arrow"/>
  <line x1="500" y1="840" x2="500" y2="860" class="flow-arrow"/>

  <line x1="1415" y1="200" x2="1415" y2="220" class="flow-arrow"/>
  <line x1="1415" y1="300" x2="1415" y2="320" class="flow-arrow"/>
  <line x1="1415" y1="400" x2="1415" y2="420" class="flow-arrow"/>
  <line x1="1415" y1="520" x2="1415" y2="540" class="flow-arrow"/>
  <line x1="1415" y1="660" x2="1415" y2="680" class="flow-arrow"/>
  <line x1="1415" y1="820" x2="1415" y2="840" class="flow-arrow"/>

  <!-- 关键方法详解 -->
  <rect x="50" y="920" width="1800" height="300" class="code-box" rx="15"/>
  <text x="950" y="945" text-anchor="middle" class="subtitle">🔧 关键方法详细解析</text>

  <!-- initParam() -->
  <rect x="70" y="960" width="420" height="120" class="lifecycle-box" rx="10"/>
  <text x="280" y="980" text-anchor="middle" class="action-text">initParam() 参数初始化</text>
  <text x="80" y="1005" class="code-text">private fun initParam(){</text>
  <text x="90" y="1020" class="code-text">  val intent = intent</text>
  <text x="90" y="1035" class="code-text">  mIdentity = intent.getSerializableExtra("identity")</text>
  <text x="90" y="1050" class="code-text">    as? ReadIdentity ?: ReadIdentity.PRIMARY_SCHOOL</text>
  <text x="90" y="1065" class="code-text">  mGrade = intent.getSerializableExtra("grade")</text>
  <text x="90" y="1080" class="code-text">    as? ReadGrade ?: ReadGrade.GRADE_FIRST</text>

  <!-- initView() -->
  <rect x="510" y="960" width="420" height="120" class="lifecycle-box" rx="10"/>
  <text x="720" y="980" text-anchor="middle" class="action-text">initView() UI初始化</text>
  <text x="520" y="1005" class="code-text">private fun initView(){</text>
  <text x="530" y="1020" class="code-text">  initListener()  // 设置事件监听器</text>
  <text x="530" y="1035" class="code-text">  countdown(3000,1000,  // 启动3秒倒计时</text>
  <text x="540" y="1050" class="code-text">    onTick = { tvCountDown.text = (it/1000).toString() },</text>
  <text x="540" y="1065" class="code-text">    onCompletion = { startRead() }</text>
  <text x="530" y="1080" class="code-text">  )</text>

  <!-- initListener() -->
  <rect x="950" y="960" width="420" height="120" class="lifecycle-box" rx="10"/>
  <text x="1160" y="980" text-anchor="middle" class="action-text">initListener() 事件监听</text>
  <text x="960" y="1005" class="code-text">private fun initListener(){</text>
  <text x="970" y="1020" class="code-text">  tvFinishRead.setOnSingleClickListener {</text>
  <text x="980" y="1035" class="code-text">    isFinishRead = true</text>
  <text x="980" y="1050" class="code-text">    endTime = System.currentTimeMillis()</text>
  <text x="980" y="1065" class="code-text">    sendMessageToService(MSG_GET_GAZE_TRAJECTORY)</text>
  <text x="970" y="1080" class="code-text">  }</text>

  <!-- startRead() -->
  <rect x="1390" y="960" width="420" height="120" class="lifecycle-box" rx="10"/>
  <text x="1600" y="980" text-anchor="middle" class="action-text">startRead() 开始功能</text>
  <text x="1400" y="1005" class="code-text">private fun startRead(){</text>
  <text x="1410" y="1020" class="code-text">  clCountDown.isVisible = false</text>
  <text x="1410" y="1035" class="code-text">  sendMessageToService(MSG_TURN_ON_CAMERA)</text>
  <text x="1410" y="1050" class="code-text">  sendMessageToService(MSG_START_TRACK)</text>
  <text x="1410" y="1065" class="code-text">  sendMessageToService(MSG_START_APPLIED_READING)</text>
  <text x="1400" y="1080" class="code-text">}</text>

  <!-- 数据流和状态管理 -->
  <rect x="70" y="1100" width="860" height="100" class="fragment-box" rx="10"/>
  <text x="500" y="1125" text-anchor="middle" class="action-text">📊 数据流和状态管理</text>
  <text x="80" y="1150" class="text">• ServiceConnection: Activity与Service通信桥梁</text>
  <text x="80" y="1170" class="text">• Messenger: 跨进程消息传递机制</text>
  <text x="80" y="1190" class="text">• LiveEventBus: Activity间数据传递</text>

  <!-- Fragment通信机制 -->
  <rect x="950" y="1100" width="860" height="100" class="activity-box" rx="10"/>
  <text x="1380" y="1125" text-anchor="middle" class="action-text">🔗 Fragment通信机制</text>
  <text x="960" y="1150" class="text">• mActivity引用: Fragment调用Activity方法</text>
  <text x="960" y="1170" class="text">• FragmentTransaction: Activity管理Fragment切换</text>
  <text x="960" y="1190" class="text">• ViewBinding: 安全的View绑定方式</text>

  <!-- 总结 -->
  <rect x="50" y="1240" width="1800" height="120" class="lifecycle-box" rx="15"/>
  <text x="950" y="1265" text-anchor="middle" class="subtitle">💡 生命周期关键要点总结</text>
  
  <text x="70" y="1295" class="action-text">🎯 Activity职责:</text>
  <text x="200" y="1295" class="text">管理整体生命周期、服务绑定、Fragment事务、系统回调处理</text>
  
  <text x="70" y="1315" class="action-text">🎯 Fragment职责:</text>
  <text x="200" y="1315" class="text">管理UI模块、用户交互、数据展示、可重用组件</text>
  
  <text x="70" y="1335" class="action-text">⚠️ 注意事项:</text>
  <text x="200" y="1335" class="text">正确处理生命周期避免内存泄漏、合理使用commitAllowingStateLoss()、及时释放资源</text>

  <!-- 底部说明 -->
  <text x="950" y="1390" text-anchor="middle" class="subtitle">📈 这个详细图表展示了Android组件生命周期中每个阶段的具体实现和作用</text>
  <text x="950" y="1420" text-anchor="middle" class="text">从系统回调到业务逻辑，从资源管理到用户交互的完整生命周期管理</text>

</svg>
