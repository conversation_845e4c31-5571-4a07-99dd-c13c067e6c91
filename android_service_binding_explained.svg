<svg width="2000" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 30px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .highlight-text { font-family: Arial, sans-serif; font-size: 16px; fill: #e74c3c; font-weight: bold; }
      .purpose-text { font-family: Arial, sans-serif; font-size: 14px; fill: #27ae60; font-weight: bold; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .service-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 3; }
      .communication-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .purpose-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 2; }
      .flow-arrow { stroke: #e74c3c; stroke-width: 4; fill: none; marker-end: url(#redarrowhead); }
      .connect-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .bidirectional-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#greenarrowhead); marker-start: url(#greenarrowstart); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e74c3c" />
    </marker>
    <marker id="greenarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
    <marker id="greenarrowstart" markerWidth="10" markerHeight="7" refX="1" refY="3.5" orient="auto">
      <polygon points="10 0, 0 3.5, 10 7" fill="#27ae60" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="1000" y="35" text-anchor="middle" class="title">Android 绑定服务详解：为什么要绑定服务？</text>
  <text x="1000" y="65" text-anchor="middle" class="subtitle">基于 GazeTrackService 的具体实现分析</text>

  <!-- 服务的作用概述 -->
  <rect x="50" y="90" width="1900" height="120" class="purpose-box" rx="15"/>
  <text x="1000" y="115" text-anchor="middle" class="subtitle">🎯 绑定服务的核心作用</text>
  <text x="70" y="145" class="purpose-text">1. 跨进程通信：</text>
  <text x="200" y="145" class="text">Activity与Service在不同进程中运行，需要建立通信桥梁</text>
  <text x="70" y="165" class="purpose-text">2. 硬件资源管理：</text>
  <text x="200" y="165" class="text">统一管理摄像头、传感器等硬件资源，避免冲突</text>
  <text x="70" y="185" class="purpose-text">3. 后台任务执行：</text>
  <text x="200" y="185" class="text">在后台持续执行视线追踪、数据处理等耗时任务</text>

  <!-- Activity端：客户端 -->
  <rect x="50" y="230" width="900" height="500" class="activity-box" rx="15"/>
  <text x="500" y="255" text-anchor="middle" class="subtitle">📱 Activity端 (客户端)</text>

  <!-- 绑定服务代码 -->
  <rect x="70" y="270" width="860" height="120" class="code-box" rx="10"/>
  <text x="90" y="290" class="highlight-text">1. 绑定服务 - 建立连接</text>
  <text x="110" y="315" class="code-text">override fun onStart() {</text>
  <text x="120" y="330" class="code-text">  super.onStart()</text>
  <text x="120" y="345" class="code-text">  bindService(Intent(this, GazeTrackService::class.java),</text>
  <text x="130" y="360" class="code-text">    serviceConnection, Context.BIND_AUTO_CREATE)</text>
  <text x="110" y="375" class="code-text">}</text>

  <!-- ServiceConnection实现 -->
  <rect x="70" y="400" width="860" height="140" class="code-box" rx="10"/>
  <text x="90" y="420" class="highlight-text">2. ServiceConnection - 连接回调</text>
  <text x="110" y="445" class="code-text">private val serviceConnection = object : ServiceConnection {</text>
  <text x="120" y="460" class="code-text">  override fun onServiceConnected(name: ComponentName?, service: IBinder?) {</text>
  <text x="130" y="475" class="code-text">    mServiceMessage = Messenger(service)  // 获取服务的Messenger</text>
  <text x="130" y="490" class="code-text">    sendMessageToService(MSG_SERVICE_CONNECTED)  // 发送连接确认</text>
  <text x="120" y="505" class="code-text">  }</text>
  <text x="120" y="520" class="code-text">  override fun onServiceDisconnected() { mServiceMessage = null }</text>
  <text x="110" y="535" class="code-text">}</text>

  <!-- 发送消息 -->
  <rect x="70" y="550" width="860" height="100" class="code-box" rx="10"/>
  <text x="90" y="570" class="highlight-text">3. 发送消息 - 控制服务</text>
  <text x="110" y="595" class="code-text">private fun startRead() {</text>
  <text x="120" y="610" class="code-text">  sendMessageToService(MSG_TURN_ON_CAMERA)    // 开启摄像头</text>
  <text x="120" y="625" class="code-text">  sendMessageToService(MSG_START_TRACK)       // 开始追踪</text>
  <text x="120" y="640" class="code-text">  sendMessageToService(MSG_START_APPLIED_READING)  // 开始阅读模式</text>

  <!-- 解绑服务 -->
  <rect x="70" y="660" width="860" height="60" class="code-box" rx="10"/>
  <text x="90" y="680" class="highlight-text">4. 解绑服务 - 断开连接</text>
  <text x="110" y="705" class="code-text">override fun onStop() { unbindService(serviceConnection) }</text>

  <!-- Service端：服务端 -->
  <rect x="1050" y="230" width="900" height="500" class="service-box" rx="15"/>
  <text x="1500" y="255" text-anchor="middle" class="subtitle">⚙️ GazeTrackService端 (服务端)</text>

  <!-- 服务绑定 -->
  <rect x="1070" y="270" width="860" height="80" class="code-box" rx="10"/>
  <text x="1090" y="290" class="highlight-text">1. 提供绑定接口</text>
  <text x="1110" y="315" class="code-text">override fun onBind(intent: Intent?): IBinder {</text>
  <text x="1120" y="330" class="code-text">  return mServiceMessage.binder  // 返回Messenger的Binder</text>
  <text x="1110" y="345" class="code-text">}</text>

  <!-- 消息处理 -->
  <rect x="1070" y="360" width="860" height="180" class="code-box" rx="10"/>
  <text x="1090" y="380" class="highlight-text">2. 处理客户端消息</text>
  <text x="1110" y="405" class="code-text">open fun parseMessage(msg: Message) {</text>
  <text x="1120" y="420" class="code-text">  when(msg.what) {</text>
  <text x="1130" y="435" class="code-text">    MSG_TURN_ON_CAMERA → GTCameraManager.startCamera()</text>
  <text x="1130" y="450" class="code-text">    MSG_START_TRACK → startGazeTrack()</text>
  <text x="1130" y="465" class="code-text">    MSG_START_APPLIED_READING → startAppliedReading()</text>
  <text x="1130" y="480" class="code-text">    MSG_GET_GAZE_TRAJECTORY → getGazeTrajectory()</text>
  <text x="1130" y="495" class="code-text">    // ... 更多消息类型处理</text>
  <text x="1120" y="510" class="code-text">  }</text>
  <text x="1110" y="525" class="code-text">}</text>

  <!-- 硬件控制 -->
  <rect x="1070" y="550" width="860" height="100" class="code-box" rx="10"/>
  <text x="1090" y="570" class="highlight-text">3. 硬件资源管理</text>
  <text x="1110" y="595" class="code-text">private fun startGazeTrack() {</text>
  <text x="1120" y="610" class="code-text">  TrackingManager.startTracking(this)  // 启动视线追踪算法</text>
  <text x="1120" y="625" class="code-text">  sendMessageToClient(MSG_GAZE_TRACKING_STATE, true)  // 回复状态</text>
  <text x="1110" y="640" class="code-text">}</text>

  <!-- 前台服务 -->
  <rect x="1070" y="660" width="860" height="60" class="code-box" rx="10"/>
  <text x="1090" y="680" class="highlight-text">4. 前台服务保活</text>
  <text x="1110" y="705" class="code-text">startForeground(ServiceId.GAZE_TRACKER_SERVICE_ID, notification)</text>

  <!-- 通信机制详解 -->
  <rect x="50" y="750" width="1900" height="200" class="communication-box" rx="15"/>
  <text x="1000" y="775" text-anchor="middle" class="subtitle">📡 通信机制详解</text>

  <!-- Messenger通信 -->
  <rect x="70" y="800" width="580" height="130" class="code-box" rx="10"/>
  <text x="360" y="820" text-anchor="middle" class="purpose-text">Messenger 双向通信</text>
  <text x="80" y="845" class="text">📤 Activity → Service：</text>
  <text x="90" y="860" class="small-text">• 发送控制指令（开启摄像头、开始追踪等）</text>
  <text x="90" y="875" class="small-text">• 请求数据（获取轨迹数据等）</text>
  <text x="80" y="900" class="text">📥 Service → Activity：</text>
  <text x="90" y="915" class="small-text">• 返回状态信息（追踪状态、错误信息等）</text>
  <text x="90" y="930" class="small-text">• 推送数据（轨迹数据、分析结果等）</text>

  <!-- 进程隔离 -->
  <rect x="670" y="800" width="580" height="130" class="code-box" rx="10"/>
  <text x="960" y="820" text-anchor="middle" class="purpose-text">进程隔离的好处</text>
  <text x="680" y="845" class="text">🔒 安全性：</text>
  <text x="690" y="860" class="small-text">• Service崩溃不影响Activity</text>
  <text x="690" y="875" class="small-text">• 硬件资源独立管理</text>
  <text x="680" y="900" class="text">⚡ 性能：</text>
  <text x="690" y="915" class="small-text">• 避免阻塞UI线程</text>
  <text x="690" y="930" class="small-text">• 后台持续运行</text>

  <!-- 消息类型 -->
  <rect x="1270" y="800" width="680" height="130" class="code-box" rx="10"/>
  <text x="1610" y="820" text-anchor="middle" class="purpose-text">具体消息类型</text>
  <text x="1280" y="845" class="text">🎮 控制类消息：</text>
  <text x="1290" y="860" class="small-text">• MSG_TURN_ON_CAMERA / MSG_TURN_OFF_CAMERA</text>
  <text x="1290" y="875" class="small-text">• MSG_START_TRACK / MSG_STOP_TRACK</text>
  <text x="1280" y="900" class="text">📊 数据类消息：</text>
  <text x="1290" y="915" class="small-text">• MSG_GET_GAZE_TRAJECTORY</text>
  <text x="1290" y="930" class="small-text">• MSG_GAZE_TRACKING_STATE</text>

  <!-- 为什么需要绑定服务 -->
  <rect x="50" y="970" width="1900" height="300" class="purpose-box" rx="15"/>
  <text x="1000" y="995" text-anchor="middle" class="subtitle">🤔 为什么需要绑定服务？具体解决了什么问题？</text>

  <!-- 问题1 -->
  <rect x="70" y="1020" width="580" height="120" class="code-box" rx="10"/>
  <text x="360" y="1040" text-anchor="middle" class="highlight-text">问题1: 硬件资源冲突</text>
  <text x="80" y="1065" class="text">❌ 不使用服务的问题：</text>
  <text x="90" y="1080" class="small-text">• 多个Activity同时访问摄像头会冲突</text>
  <text x="90" y="1095" class="small-text">• 切换Activity时硬件状态丢失</text>
  <text x="80" y="1120" class="text">✅ 使用服务的解决方案：</text>
  <text x="90" y="1135" class="small-text">• 统一管理硬件资源，避免冲突</text>

  <!-- 问题2 -->
  <rect x="670" y="1020" width="580" height="120" class="code-box" rx="10"/>
  <text x="960" y="1040" text-anchor="middle" class="highlight-text">问题2: 后台任务中断</text>
  <text x="680" y="1065" class="text">❌ 不使用服务的问题：</text>
  <text x="690" y="1080" class="small-text">• Activity销毁时追踪任务停止</text>
  <text x="690" y="1095" class="small-text">• 无法在后台持续处理数据</text>
  <text x="680" y="1120" class="text">✅ 使用服务的解决方案：</text>
  <text x="690" y="1135" class="small-text">• 后台持续运行，不受Activity影响</text>

  <!-- 问题3 -->
  <rect x="1270" y="1020" width="680" height="120" class="code-box" rx="10"/>
  <text x="1610" y="1040" text-anchor="middle" class="highlight-text">问题3: 数据共享困难</text>
  <text x="1280" y="1065" class="text">❌ 不使用服务的问题：</text>
  <text x="1290" y="1080" class="small-text">• 多个Activity间数据传递复杂</text>
  <text x="1290" y="1095" class="small-text">• 状态同步困难</text>
  <text x="1280" y="1120" class="text">✅ 使用服务的解决方案：</text>
  <text x="1290" y="1135" class="small-text">• 集中管理状态和数据</text>

  <!-- 实际应用场景 -->
  <rect x="70" y="1160" width="1860" height="100" class="communication-box" rx="10"/>
  <text x="1000" y="1180" text-anchor="middle" class="highlight-text">🎯 在视线追踪应用中的具体作用</text>
  <text x="80" y="1205" class="text">1. 摄像头管理：多个功能（阅读、训练、评估）共享同一个摄像头，避免冲突</text>
  <text x="80" y="1225" class="text">2. 算法处理：视线追踪算法在后台持续运行，实时分析图像数据</text>
  <text x="80" y="1245" class="text">3. 数据收集：持续收集视线轨迹数据，即使用户切换界面也不中断</text>

  <!-- 连接箭头 -->
  <line x1="950" y1="400" x2="1050" y2="400" class="bidirectional-arrow"/>
  <text x="1000" y="390" text-anchor="middle" class="small-text">Messenger通信</text>

  <line x1="950" y1="500" x2="1050" y2="500" class="bidirectional-arrow"/>
  <text x="1000" y="490" text-anchor="middle" class="small-text">消息传递</text>

  <line x1="950" y1="600" x2="1050" y2="600" class="bidirectional-arrow"/>
  <text x="1000" y="590" text-anchor="middle" class="small-text">状态同步</text>

  <!-- 底部总结 -->
  <text x="1000" y="1310" text-anchor="middle" class="subtitle">📊 总结：绑定服务是Android中实现跨进程通信和后台任务管理的核心机制</text>
  <text x="1000" y="1340" text-anchor="middle" class="text">通过Messenger实现双向通信，统一管理硬件资源，确保应用的稳定性和性能</text>

</svg>
