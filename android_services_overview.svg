<svg width="2000" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 30px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .highlight-text { font-family: Arial, sans-serif; font-size: 16px; fill: #e74c3c; font-weight: bold; }
      .purpose-text { font-family: Arial, sans-serif; font-size: 14px; fill: #27ae60; font-weight: bold; }
      .service1-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .service2-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 3; }
      .manifest-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .calling-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 2; }
      .flow-arrow { stroke: #e74c3c; stroke-width: 4; fill: none; marker-end: url(#redarrowhead); }
      .connect-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="1000" y="35" text-anchor="middle" class="title">Android 项目中的所有Service及调用方式</text>
  <text x="1000" y="65" text-anchor="middle" class="subtitle">基于 MIT_DD_GazeTracker 项目的Service架构分析</text>

  <!-- AndroidManifest.xml 中的Service配置 -->
  <rect x="50" y="90" width="1900" height="140" class="manifest-box" rx="15"/>
  <text x="1000" y="115" text-anchor="middle" class="subtitle">📋 AndroidManifest.xml 中的Service配置</text>
  
  <rect x="70" y="140" width="900" height="80" class="code-box" rx="10"/>
  <text x="90" y="160" class="highlight-text">Service 1: GazeTrackService</text>
  <text x="110" y="180" class="code-text">&lt;service android:name=".gaze.track.GazeTrackService"</text>
  <text x="120" y="195" class="code-text">android:exported="true" android:enabled="true"</text>
  <text x="120" y="210" class="code-text">android:process=":GazeTracker" android:foregroundServiceType="camera"/&gt;</text>

  <rect x="1000" y="140" width="900" height="80" class="code-box" rx="10"/>
  <text x="1020" y="160" class="highlight-text">Service 2: DesktopService</text>
  <text x="1040" y="180" class="code-text">&lt;service android:name=".desktop.DesktopService"</text>
  <text x="1050" y="195" class="code-text">android:exported="true" android:enabled="true"</text>
  <text x="1050" y="210" class="code-text">android:process=":Desktop"/&gt;</text>

  <!-- Service 1: GazeTrackService 详解 -->
  <rect x="50" y="250" width="950" height="600" class="service1-box" rx="15"/>
  <text x="525" y="275" text-anchor="middle" class="subtitle">🎯 Service 1: GazeTrackService</text>
  <text x="525" y="295" text-anchor="middle" class="purpose-text">核心视线追踪服务 - 独立进程运行</text>

  <!-- 主要功能 -->
  <rect x="70" y="310" width="910" height="120" class="code-box" rx="10"/>
  <text x="90" y="330" class="highlight-text">🔧 主要功能</text>
  <text x="110" y="355" class="text">• 摄像头管理：开启/关闭摄像头，图像采集</text>
  <text x="110" y="375" class="text">• 视线追踪：启动/停止追踪算法，实时分析</text>
  <text x="110" y="395" class="text">• 校准功能：视觉校准、姿态校准</text>
  <text x="110" y="415" class="text">• 数据处理：轨迹数据收集、分析、上传</text>

  <!-- 进程配置 -->
  <rect x="70" y="440" width="910" height="80" class="code-box" rx="10"/>
  <text x="90" y="460" class="highlight-text">⚙️ 进程配置</text>
  <text x="110" y="485" class="text">• 独立进程：android:process=":GazeTracker"</text>
  <text x="110" y="505" class="text">• 前台服务：android:foregroundServiceType="camera"</text>

  <!-- 绑定方式 -->
  <rect x="70" y="530" width="910" height="160" class="code-box" rx="10"/>
  <text x="90" y="550" class="highlight-text">📱 Activity绑定方式</text>
  <text x="110" y="575" class="code-text">// ReadActivity, TrainWebActivity, MHospitalMTActivity 等</text>
  <text x="110" y="590" class="code-text">override fun onStart() {</text>
  <text x="120" y="605" class="code-text">  bindService(Intent(this, GazeTrackService::class.java),</text>
  <text x="130" y="620" class="code-text">    serviceConnection, Context.BIND_AUTO_CREATE)</text>
  <text x="110" y="635" class="code-text">}</text>
  <text x="110" y="655" class="code-text">override fun onStop() {</text>
  <text x="120" y="670" class="code-text">  unbindService(serviceConnection)</text>
  <text x="110" y="685" class="code-text">}</text>

  <!-- 消息处理 -->
  <rect x="70" y="700" width="910" height="140" class="code-box" rx="10"/>
  <text x="90" y="720" class="highlight-text">📡 消息处理机制</text>
  <text x="110" y="745" class="code-text">parseMessage(msg) {</text>
  <text x="120" y="760" class="code-text">  MSG_TURN_ON_CAMERA → GTCameraManager.startCamera()</text>
  <text x="120" y="775" class="code-text">  MSG_START_TRACK → startGazeTrack()</text>
  <text x="120" y="790" class="code-text">  MSG_START_APPLIED_READING → startAppliedReading()</text>
  <text x="120" y="805" class="code-text">  MSG_GET_GAZE_TRAJECTORY → getGazeTrajectory()</text>
  <text x="120" y="820" class="code-text">  // ... 30+ 种消息类型</text>
  <text x="110" y="835" class="code-text">}</text>

  <!-- Service 2: DesktopService 详解 -->
  <rect x="1020" y="250" width="930" height="600" class="service2-box" rx="15"/>
  <text x="1485" y="275" text-anchor="middle" class="subtitle">🖥️ Service 2: DesktopService</text>
  <text x="1485" y="295" text-anchor="middle" class="purpose-text">桌面管理服务 - 独立进程运行</text>

  <!-- 主要功能 -->
  <rect x="1040" y="310" width="890" height="100" class="code-box" rx="10"/>
  <text x="1060" y="330" class="highlight-text">🔧 主要功能</text>
  <text x="1080" y="355" class="text">• 桌面环境管理：提供桌面相关服务</text>
  <text x="1080" y="375" class="text">• 系统级服务：独立进程保证稳定性</text>
  <text x="1080" y="395" class="text">• 前台服务：持续运行不被系统杀死</text>

  <!-- 进程配置 -->
  <rect x="1040" y="420" width="890" height="80" class="code-box" rx="10"/>
  <text x="1060" y="440" class="highlight-text">⚙️ 进程配置</text>
  <text x="1080" y="465" class="text">• 独立进程：android:process=":Desktop"</text>
  <text x="1080" y="485" class="text">• 前台服务：防止被系统回收</text>

  <!-- 服务实现 -->
  <rect x="1040" y="510" width="890" height="180" class="code-box" rx="10"/>
  <text x="1060" y="530" class="highlight-text">📱 服务实现</text>
  <text x="1080" y="555" class="code-text">class DesktopService : Service(), LifecycleOwner {</text>
  <text x="1090" y="575" class="code-text">  override fun onCreate() {</text>
  <text x="1100" y="590" class="code-text">    startForegroundService()  // 启动前台服务</text>
  <text x="1090" y="605" class="code-text">  }</text>
  <text x="1090" y="625" class="code-text">  override fun onBind(intent: Intent?): IBinder? {</text>
  <text x="1100" y="640" class="code-text">    return null  // 不提供绑定接口</text>
  <text x="1090" y="655" class="code-text">  }</text>
  <text x="1080" y="675" class="code-text">}</text>

  <!-- 启动方式对比 -->
  <rect x="1040" y="700" width="890" height="140" class="code-box" rx="10"/>
  <text x="1060" y="720" class="highlight-text">🚀 启动方式</text>
  <text x="1080" y="745" class="text">• 不提供绑定接口 (onBind返回null)</text>
  <text x="1080" y="765" class="text">• 通过startService()启动</text>
  <text x="1080" y="785" class="text">• 主要用于后台任务，不需要与Activity通信</text>
  <text x="1080" y="805" class="text">• 生命周期独立，不依赖Activity</text>
  <text x="1080" y="825" class="text">• 系统启动时自动运行</text>

  <!-- 调用方式对比 -->
  <rect x="50" y="870" width="1900" height="200" class="calling-box" rx="15"/>
  <text x="1000" y="895" text-anchor="middle" class="subtitle">📞 Service调用方式对比</text>

  <!-- GazeTrackService调用 -->
  <rect x="70" y="920" width="900" height="140" class="code-box" rx="10"/>
  <text x="520" y="940" text-anchor="middle" class="highlight-text">GazeTrackService 调用方式</text>
  <text x="80" y="965" class="purpose-text">🔗 绑定服务 (Bound Service)</text>
  <text x="90" y="985" class="text">• 使用 bindService() 绑定</text>
  <text x="90" y="1005" class="text">• 通过 Messenger 进行双向通信</text>
  <text x="90" y="1025" class="text">• Activity生命周期绑定 (onStart/onStop)</text>
  <text x="90" y="1045" class="text">• 支持多个客户端同时绑定</text>

  <!-- DesktopService调用 -->
  <rect x="1000" y="920" width="900" height="140" class="code-box" rx="10"/>
  <text x="1450" y="940" text-anchor="middle" class="highlight-text">DesktopService 调用方式</text>
  <text x="1010" y="965" class="purpose-text">🚀 启动服务 (Started Service)</text>
  <text x="1020" y="985" class="text">• 使用 startService() 启动</text>
  <text x="1020" y="1005" class="text">• 不提供绑定接口，无法直接通信</text>
  <text x="1020" y="1025" class="text">• 独立运行，不依赖Activity</text>
  <text x="1020" y="1045" class="text">• 系统级服务，开机自启</text>

  <!-- 具体调用场景 -->
  <rect x="50" y="1090" width="1900" height="180" class="manifest-box" rx="15"/>
  <text x="1000" y="1115" text-anchor="middle" class="subtitle">🎯 具体调用场景</text>

  <!-- GazeTrackService场景 -->
  <rect x="70" y="1140" width="580" height="120" class="code-box" rx="10"/>
  <text x="360" y="1160" text-anchor="middle" class="purpose-text">GazeTrackService 使用场景</text>
  <text x="80" y="1185" class="text">• ReadActivity: 阅读追踪功能</text>
  <text x="80" y="1205" class="text">• TrainWebActivity: 训练模式</text>
  <text x="80" y="1225" class="text">• MHospitalMTActivity: 医院治疗</text>
  <text x="80" y="1245" class="text">• CalibrationActivity: 校准功能</text>

  <!-- DesktopService场景 -->
  <rect x="670" y="1140" width="580" height="120" class="code-box" rx="10"/>
  <text x="960" y="1160" text-anchor="middle" class="purpose-text">DesktopService 使用场景</text>
  <text x="680" y="1185" class="text">• 系统桌面管理</text>
  <text x="680" y="1205" class="text">• 后台系统服务</text>
  <text x="680" y="1225" class="text">• 开机自启动服务</text>
  <text x="680" y="1245" class="text">• 独立进程保证稳定性</text>

  <!-- 消息类型 -->
  <rect x="1270" y="1140" width="680" height="120" class="code-box" rx="10"/>
  <text x="1610" y="1160" text-anchor="middle" class="purpose-text">GazeTrackService 消息类型</text>
  <text x="1280" y="1185" class="text">• 摄像头控制: MSG_TURN_ON/OFF_CAMERA</text>
  <text x="1280" y="1205" class="text">• 追踪控制: MSG_START/STOP_TRACK</text>
  <text x="1280" y="1225" class="text">• 校准控制: MSG_START_VISUAL_CALIBRATION</text>
  <text x="1280" y="1245" class="text">• 数据获取: MSG_GET_GAZE_TRAJECTORY</text>

  <!-- 连接箭头 -->
  <line x1="525" y1="850" x2="525" y2="920" class="flow-arrow"/>
  <line x1="1485" y1="850" x2="1485" y2="920" class="flow-arrow"/>

  <!-- 底部总结 -->
  <rect x="50" y="1290" width="1900" height="120" class="manifest-box" rx="15"/>
  <text x="1000" y="1315" text-anchor="middle" class="subtitle">📊 Service架构总结</text>
  <text x="70" y="1345" class="text">🎯 GazeTrackService: 核心业务服务，提供视线追踪、摄像头管理、数据处理等功能，通过绑定方式与Activity通信</text>
  <text x="70" y="1365" class="text">🖥️ DesktopService: 系统级服务，独立运行，不需要与Activity通信，主要用于桌面环境管理</text>
  <text x="70" y="1385" class="text">🔧 两个Service都运行在独立进程中，确保稳定性和性能，避免相互影响</text>

  <!-- 底部说明 -->
  <text x="1000" y="1440" text-anchor="middle" class="subtitle">📈 这个架构设计实现了功能分离和进程隔离，提高了应用的稳定性和可维护性</text>

</svg>
