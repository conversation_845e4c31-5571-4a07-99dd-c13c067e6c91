<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1a1a1a; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #333333; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #1a1a1a; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #d63384; }
      .comment { font-family: Arial, sans-serif; font-size: 10px; fill: #6c757d; }

      .camera-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 3; }
      .thread-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 3; }
      .singleton-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 3; }
      .state-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 3; }
      .flow-box { fill: #fff8e1; stroke: #fbc02d; stroke-width: 3; }

      .arrow { stroke: #424242; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .check-arrow { stroke: #4caf50; stroke-width: 3; fill: none; marker-end: url(#arrowhead-green); }
      .block-arrow { stroke: #f44336; stroke-width: 3; fill: none; marker-end: url(#arrowhead-red); }
    </style>

    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#424242"/>
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f44336"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">防重复机制架构图</text>
  <text x="600" y="55" text-anchor="middle" class="subtitle">Camera, Thread, Execution Anti-Duplication Mechanisms</text>

  <!-- 相机管理防重复 -->
  <rect x="50" y="100" width="280" height="150" class="camera-box" rx="8"/>
  <text x="60" y="125" class="subtitle" fill="#1976d2">相机管理防重复</text>
  <text x="60" y="145" class="text" fill="#1565c0">GTCameraManager</text>

  <text x="60" y="170" class="code" fill="#d63384">private val isCameraStarted = AtomicBoolean(false)</text>
  <text x="60" y="190" class="text" fill="#1a1a1a">启动检查:</text>
  <text x="60" y="205" class="code" fill="#d63384">if (isCameraStarted.get()) return</text>
  <text x="60" y="225" class="text" fill="#1a1a1a">状态更新:</text>
  <text x="60" y="240" class="code" fill="#d63384">isCameraStarted.set(true)</text>

  <!-- 线程安全状态管理 -->
  <rect x="370" y="100" width="280" height="150" class="thread-box" rx="8"/>
  <text x="380" y="125" class="subtitle" fill="#f57c00">线程安全状态管理</text>
  <text x="380" y="145" class="text" fill="#ef6c00">TrackingManager</text>

  <text x="380" y="170" class="code" fill="#d63384">private val isInitialized = AtomicBoolean(false)</text>
  <text x="380" y="185" class="code" fill="#d63384">private val serviceMode = AtomicReference(NONE)</text>
  <text x="380" y="205" class="text" fill="#1a1a1a">模式检查:</text>
  <text x="380" y="220" class="code" fill="#d63384">if (serviceMode.get() == TRACK) return 2</text>
  <text x="380" y="240" class="text" fill="#1a1a1a">返回状态码: 0失败 1成功 2已启动</text>

  <!-- 单例模式防重复 -->
  <rect x="690" y="100" width="280" height="150" class="singleton-box" rx="8"/>
  <text x="700" y="125" class="subtitle" fill="#7b1fa2">单例模式防重复</text>
  <text x="700" y="145" class="text" fill="#6a1b9a">BehaviorGuidanceManager</text>

  <text x="700" y="170" class="code" fill="#d63384">object BehaviorGuidanceManager</text>
  <text x="700" y="190" class="text" fill="#1a1a1a">懒加载:</text>
  <text x="700" y="205" class="code" fill="#d63384">by lazy(LazyThreadSafetyMode.SYNCHRONIZED)</text>
  <text x="700" y="225" class="text" fill="#1a1a1a">全局唯一实例</text>
  <text x="700" y="240" class="text" fill="#1a1a1a">线程安全初始化</text>

  <!-- 核心防护策略 -->
  <rect x="50" y="300" width="920" height="120" class="state-box" rx="8"/>
  <text x="60" y="325" class="subtitle" fill="#388e3c">核心防护策略</text>

  <text x="60" y="350" class="text" fill="#1a1a1a">1. AtomicBoolean状态标志 - 线程安全的原子布尔值标记状态</text>
  <text x="60" y="370" class="text" fill="#1a1a1a">2. AtomicReference状态管理 - 管理复杂状态对象</text>
  <text x="60" y="390" class="text" fill="#1a1a1a">3. Object单例模式 - 确保全局只有一个实例</text>
  <text x="60" y="410" class="text" fill="#1a1a1a">4. 状态检查机制 - 执行前检查当前状态，避免重复执行</text>

  <!-- 工作流程 -->
  <rect x="50" y="460" width="920" height="300" class="flow-box" rx="8"/>
  <text x="60" y="485" class="subtitle" fill="#fbc02d">防重复执行工作流程</text>

  <!-- 流程步骤 -->
  <rect x="80" y="510" width="150" height="60" fill="#ffffff" stroke="#424242" stroke-width="2" rx="5"/>
  <text x="155" y="535" text-anchor="middle" class="text">请求启动</text>
  <text x="155" y="550" text-anchor="middle" class="text">(相机/线程/服务)</text>

  <rect x="280" y="510" width="150" height="60" fill="#ffffff" stroke="#424242" stroke-width="2" rx="5"/>
  <text x="355" y="535" text-anchor="middle" class="text">检查状态标志</text>
  <text x="355" y="550" text-anchor="middle" class="code">AtomicBoolean.get()</text>

  <rect x="480" y="450" width="150" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3" rx="5"/>
  <text x="555" y="475" text-anchor="middle" class="text">状态为false</text>
  <text x="555" y="490" text-anchor="middle" class="text">允许执行</text>

  <rect x="480" y="570" width="150" height="60" fill="#ffcdd2" stroke="#f44336" stroke-width="3" rx="5"/>
  <text x="555" y="595" text-anchor="middle" class="text">状态为true</text>
  <text x="555" y="610" text-anchor="middle" class="text">阻止重复执行</text>

  <rect x="680" y="450" width="150" height="60" fill="#ffffff" stroke="#424242" stroke-width="2" rx="5"/>
  <text x="755" y="470" text-anchor="middle" class="text">执行操作</text>
  <text x="755" y="485" text-anchor="middle" class="text">设置状态为true</text>
  <text x="755" y="500" text-anchor="middle" class="code">AtomicBoolean.set(true)</text>

  <rect x="680" y="570" width="150" height="60" fill="#ffffff" stroke="#424242" stroke-width="2" rx="5"/>
  <text x="755" y="590" text-anchor="middle" class="text">返回状态码</text>
  <text x="755" y="605" text-anchor="middle" class="text">2 = 已启动</text>

  <!-- 箭头连接 -->
  <line x1="230" y1="540" x2="280" y2="540" class="arrow"/>
  <line x1="430" y1="540" x2="480" y2="480" class="check-arrow"/>
  <line x1="430" y1="540" x2="480" y2="600" class="block-arrow"/>
  <line x1="630" y1="480" x2="680" y2="480" class="check-arrow"/>
  <line x1="630" y1="600" x2="680" y2="600" class="block-arrow"/>

  <!-- 状态说明 -->
  <text x="60" y="700" class="subtitle">关键实现类:</text>
  <text x="60" y="720" class="text">• GTCameraManager - 相机启动状态管理</text>
  <text x="60" y="735" class="text">• TrackingManager - 视线追踪服务状态管理</text>
  <text x="60" y="750" class="text">• AppliedManager - 应用模块初始化状态管理</text>

  <text x="500" y="700" class="subtitle">线程安全保障:</text>
  <text x="500" y="720" class="text">• AtomicBoolean - 原子操作保证线程安全</text>
  <text x="500" y="735" class="text">• AtomicReference - 复杂对象的原子引用</text>
  <text x="500" y="750" class="text">• LazyThreadSafetyMode.SYNCHRONIZED - 同步懒加载</text>

</svg>
