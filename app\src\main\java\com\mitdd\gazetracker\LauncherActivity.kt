package com.mitdd.gazetracker

import android.os.Bundle
import android.widget.TextView
import androidx.activity.viewModels
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.DeviceExceptionActivity
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.device.enumeration.PlacementType
import com.mitdd.gazetracker.device.vm.DeviceViewModel
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.medicalhome.HomeMainActivity
import com.mitdd.gazetracker.medicalhospital.HospitalMainActivity
import com.mitdd.gazetracker.read.home.ReadHomeMainActivity
import com.mitdd.gazetracker.tsc.TscMainActivity

/**
 * FileName: LauncherActivity
 * Author by lilin,Date on 2024/11/21 9:38
 * PS: Not easy to write code, please indicate.
 */
class LauncherActivity : GTBaseActivity() {

    companion object{
        private val TAG = LauncherActivity::class.java.simpleName
    }

    private val tvVersion by id<TextView>(R.id.tv_version)

    private val deviceVM by viewModels<DeviceViewModel>()

    private var mMaskTherapyState:Boolean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_launcher)
        initParam()
        initView()
        initObserver()
        initData()
    }

    private fun initParam(){
        val intent = intent
        val hasMaskTherapyState = intent.hasExtra(GazeConstants.KEY_MASK_THERAPY_STATE)
        Logger.d(TAG, msg = "initParam hasMaskTherapyState = $hasMaskTherapyState")
        if (hasMaskTherapyState){
            mMaskTherapyState = intent.getBooleanExtra(GazeConstants.KEY_MASK_THERAPY_STATE,false)
        }
    }

    private fun initView(){
    }

    private fun initObserver(){
        deviceVM.deviceInfoLiveData.observe(this){
            Logger.d(TAG, msg = "initObserver deviceInfoLiveData = $it")
            doStartActivity()
        }
    }

    private fun initData(){
        deviceVM.getDeviceBasicInfo()

        tvVersion.text = getString(R.string.str_version_number_s,PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
    }

    private fun doStartActivity(){
        when(DeviceManager.getPlacementType()){
            PlacementType.M_HOME ->{
                val createIntent = HomeMainActivity.createIntent(this)
                if (mMaskTherapyState != null){
                    createIntent.putExtra(GazeConstants.KEY_MASK_THERAPY_STATE,mMaskTherapyState)
                }
                startActivity(createIntent)
                finish()
            }
            PlacementType.M_HOSPITAL ->{
                startActivity(HospitalMainActivity.createIntent(this))
                finish()
            }
            PlacementType.R_HOME ->{
                val createIntent = ReadHomeMainActivity.createIntent(this)
                if (mMaskTherapyState != null){
                    createIntent.putExtra(GazeConstants.KEY_MASK_THERAPY_STATE,mMaskTherapyState)
                }
                startActivity(createIntent)
                finish()
            }
            PlacementType.TSC ->{
                startActivity(TscMainActivity.createIntent(this))
                finish()
            }
            else ->{
                startActivity(DeviceExceptionActivity.createIntent(this))
                finish()
            }
        }
    }

}