package com.mitdd.gazetracker.ai

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: AdaPreference
 * Author by lilin,Date on 2025/2/12 17:02
 * PS: Not easy to write code, please indicate.
 */
enum class AdaPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * 授权码
     */
    AUTH_CODE(null),

    /**
     * AI请求地址
     */
    CHAT_URL(null);

    override fun getNameSpace(): String {
        return "AdaPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }
}