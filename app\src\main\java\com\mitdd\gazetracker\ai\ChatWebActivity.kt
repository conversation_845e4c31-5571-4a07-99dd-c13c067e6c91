package com.mitdd.gazetracker.ai

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.widget.ImageView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView
import com.airdoc.component.common.ui.web.WebViewManager
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: ChatWebActivity
 * Author by lilin,Date on 2025/2/12 17:28
 * PS: Not easy to write code, please indicate.
 */
class ChatWebActivity : GTBaseActivity() {

    companion object{
        private val TAG = ChatWebActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ChatWebActivity::class.java)
            return intent
        }
    }

    private val chatRoot by id<ConstraintLayout>(R.id.chat_root)
    private val ivCloseAi by id<ImageView>(R.id.iv_close_ai)
    private val chatWeb by id<CustomWebView>(R.id.chat_web)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_chat_web)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(OVERRIDE_TRANSITION_OPEN,R.anim.anim_chat_web_activity_in,0)
        }else{
            overridePendingTransition(R.anim.anim_chat_web_activity_in,0)
        }

        initView()
        initObserver()
        initData()
    }

    private fun initView() {
        initListener()

        val chatUrl = MMKVManager.decodeString(AdaPreference.CHAT_URL)?:""
        Logger.d(TAG,msg = "initView chatUrl = $chatUrl")
        if (!TextUtils.isEmpty(chatUrl)){
            chatWeb.loadUrl(chatUrl)
        }else{
            Toast.makeText(this,getString(R.string.str_url_empty), Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun initObserver() {
        chatRoot.setOnSingleClickListener {
            finish()
        }
        ivCloseAi.setOnSingleClickListener {
            finish()
        }
    }

    private fun initData() {

    }

    private fun initListener(){

    }

    override fun finish() {
        super.finish()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(OVERRIDE_TRANSITION_CLOSE,0,R.anim.anim_chat_web_activity_out)
        }else{
            overridePendingTransition(0,R.anim.anim_chat_web_activity_out)
        }
    }

}