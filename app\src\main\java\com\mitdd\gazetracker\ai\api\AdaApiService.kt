package com.mitdd.gazetracker.ai.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.ai.bean.AuthCode
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * FileName: AdaApiService
 * Author by lilin,Date on 2025/2/12 16:42
 * PS: Not easy to write code, please indicate.
 */
interface AdaApiService {

    /**
     * 获取授权码
     */
    @POST("api/llm/authCode")
    suspend fun getAuthCode(@Body initChatRequest: RequestBody): ApiResponse<AuthCode>
}