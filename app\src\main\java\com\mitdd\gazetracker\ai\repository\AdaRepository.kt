package com.mitdd.gazetracker.ai.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.ai.api.AdaApiService
import com.mitdd.gazetracker.ai.bean.AuthCode
import com.mitdd.gazetracker.net.AdaRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: AdaRepository
 * Author by lilin,Date on 2025/2/12 16:47
 * PS: Not easy to write code, please indicate.
 */
class AdaRepository : BaseRepository() {

    /**
     * 获取授权码
     * @param userNo 传SN
     */
    suspend fun getAuthCode(userNo:String): ApiResponse<AuthCode> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["page"] = "default"
            hashMap["system"] = "mit-dd"
            hashMap["userNo"] = userNo
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            AdaRetrofitClient.createService(AdaApiService::class.java).getAuthCode(requestBody)
        }
    }
}