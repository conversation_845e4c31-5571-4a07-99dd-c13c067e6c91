package com.mitdd.gazetracker.ai.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.ai.AdaPreference
import com.mitdd.gazetracker.ai.bean.AuthCode
import com.mitdd.gazetracker.ai.repository.AdaRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: AdaViewModel
 * Author by lilin,Date on 2025/2/12 16:50
 * PS: Not easy to write code, please indicate.
 */
class AdaViewModel : ViewModel() {

    companion object{
        private val TAG = AdaViewModel::class.java.name
    }

    private val adaRepository by lazy { AdaRepository() }

    val authCodeLiveData = MutableLiveData<AuthCode?>()

    /**
     * 获取授权码
     * @param userNo 传SN
     */
    fun getAuthCode(userNo:String){
        viewModelScope.launch {
            MutableStateFlow(adaRepository.getAuthCode(userNo)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getAuthCode onSuccess")
                    updateAuthCode(it)
                    authCodeLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getAuthCode onDataEmpty")
                    updateAuthCode(null)
                    authCodeLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getAuthCode onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    updateAuthCode(null)
                    authCodeLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getAuthCode onError = $it")
                    updateAuthCode(null)
                    authCodeLiveData.postValue(null)
                }
            }
        }
    }

    private fun updateAuthCode(authCode:AuthCode?){
        MMKVManager.encodeString(AdaPreference.AUTH_CODE, authCode?.authCode)
        MMKVManager.encodeString(AdaPreference.CHAT_URL, authCode?.chatUrl)
    }
}