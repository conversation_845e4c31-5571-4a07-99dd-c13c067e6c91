package com.mitdd.gazetracker.anim

import android.graphics.PointF
import android.view.animation.Interpolator


/**
 * FileName: CubicBezierInterpolator
 * Author by lilin,Date on 2024/12/10 20:26
 * PS: Not easy to write code, please indicate.
 * 三阶贝塞尔曲线动画插值器
 * @param x1 控制点1的x坐标 0..1
 * @param y1 控制点1的y坐标 0..1
 * @param x2 控制点2的x坐标 0..1
 * @param y2 控制点2的y坐标 0..1
 */
class CubicBezierInterpolator(x1:Float,y1:Float,x2:Float,y2:Float) : Interpolator {

    /**
     * 精准度，相当于把差值器的输入值input分成4096份，然后对每一份进行插值计算，最终得到一个插值结果
     */
    private val accuracy = 4096

    /**
     * 记录当前输入值input的整数部分，用于记录上次计算时的整数部分，避免重复计算
     */
    private var mLastI = 0

    /**
     * 控制点1
     */
    private val mControlPoint1 = PointF()

    /**
     * 控制点2
     */
    private val mControlPoint2 = PointF()

    /**
     * 重写getInterpolation方法，用于计算并返回插值结果
     *
     * @param input 输入的浮点数，代表需要进行插值的自变量 0..1
     * @return 返回计算得到的插值结果，即因变量值
     */
    override fun getInterpolation(input: Float): Float {
        var t = input.toDouble()
        for ( i in mLastI until accuracy){
            t = 1.0 * i / accuracy
            val x = cubicCurves(t, 0.0, mControlPoint1.x.toDouble(), mControlPoint2.x.toDouble(), 1.0)
            if (x >= input) {
                mLastI = i
                break
            }
        }
        var value = cubicCurves(t, 0.0, mControlPoint1.y.toDouble(), mControlPoint2.y.toDouble(), 1.0)
        if (value > 0.999) {
            value = 1.0
            mLastI = 0
        }
        return value.toFloat()
    }

    /**
     * 三阶贝塞尔曲线公式计算(计算x或y 一个维度的值)
     *
     * @param t 输入的浮点数，代表需要进行插值的自变量 0..1
     * @param value0 起始点的值
     * @param value1 控制点1的值
     * @param value2 控制点2的值
     * @param value3 终点的值
     * @return 返回计算得到的插值结果，即因变量值
     */
    private fun cubicCurves(t:Double,value0:Double,value1:Double,value2:Double,value3:Double):Double{
        var value = 0.0
        val u = 1 - t
        val tt = t * t
        val uu = u * u
        val uuu = uu * u
        val ttt = tt * t

        value = uuu * value0
        value += 3 * uu * t * value1
        value += 3 * u * tt * value2
        value += ttt * value3

        return value
    }
}