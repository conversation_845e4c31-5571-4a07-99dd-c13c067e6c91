package com.mitdd.gazetracker.anim

import android.animation.TypeEvaluator
import android.graphics.PointF

/**
 * FileName: CubicBezierPointEvaluator
 * Author by lilin,Date on 2024/12/11 9:46
 * PS: Not easy to write code, please indicate.
 * 三阶贝塞尔曲线PointF估值器
 * @param pointF1 三阶贝塞尔曲线的第一个控制点
 * @param pointF2 三阶贝塞尔曲线的第二个控制点
 */
class CubicBezierPointEvaluator(
    private val pointF1: PointF, private val pointF2: PointF
) : TypeEvaluator<PointF> {

    /**
     * 评估并计算两个点之间的插值结果
     *
     * @param fraction 插值分数，表示从开始点到结束点的进度（0.0.。1.0）
     * @param startValue 开始点的坐标(三阶贝塞尔曲线的起始点)
     * @param endValue 结束点的坐标（三阶贝塞尔曲线的终点）
     * @return 计算得到的当前点的坐标
     */
    override fun evaluate(fraction: Float, startValue: PointF, endValue: PointF): PointF {
        val minusT = 1f - fraction
        val pointF = PointF()

        pointF.x =
            minusT * minusT * minusT * startValue.x + 3 * fraction * minusT * minusT * pointF1.x + 3 * fraction * fraction * minusT * pointF2.x + fraction * fraction * fraction * endValue.x
        pointF.y =
            minusT * minusT * minusT * startValue.y + 3 * fraction * minusT * minusT * pointF1.y + 3 * fraction * fraction * minusT * pointF2.y + fraction * fraction * fraction * endValue.y

        return pointF
    }
}