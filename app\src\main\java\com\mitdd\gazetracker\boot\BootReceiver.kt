package com.mitdd.gazetracker.boot

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import java.util.concurrent.TimeUnit

/**
 * FileName: BootReceiver
 * Author by lilin,Date on 2024/7/24 10:16
 * PS: Not easy to write code, please indicate.
 * 监听开机广播
 */
class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        if (intent?.action == Intent.ACTION_BOOT_COMPLETED){
//            startBootStartGTServiceWorker(context)
        }
    }

    /**
     * 启动开机启动GTService worker
     */
    private fun startBootStartGTServiceWorker(context: Context){
        val workRequest = OneTimeWorkRequestBuilder<BootStartGTServiceWorker>()
            // 设定一个初始延迟，确保设备重启后有足够时间完成系统初始化
            .setInitialDelay(5, TimeUnit.SECONDS)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork(
            "BootStartGTService",
            ExistingWorkPolicy.REPLACE,
            workRequest
        )
    }
}