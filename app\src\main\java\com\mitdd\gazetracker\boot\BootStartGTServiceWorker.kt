package com.mitdd.gazetracker.boot

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters

/**
 * FileName: BootStartGTServiceWork
 * Author by lilin,Date on 2024/7/23 20:07
 * PS: Not easy to write code, please indicate.
 * 开机启动GTService Worker
 */
class BootStartGTServiceWorker(var context: Context, workerParams: WorkerParameters) : Worker(context, workerParams) {

    companion object{
        private val TAG = BootStartGTServiceWorker::class.java.simpleName
    }

    override fun doWork(): Result {
        return Result.success()
    }
}