package com.mitdd.gazetracker.boot

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Looper
import android.os.Message
import android.os.Messenger
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.gaze.GazeConstants

/**
 * FileName: GlobalBootReceiver
 * Author by lilin,Date on 2024/10/12 11:50
 * PS: Not easy to write code, please indicate.
 */
class GlobalBootReceiver : BroadcastReceiver() {

    companion object{
        private val TAG = GlobalBootReceiver::class.java.simpleName
    }


    override fun onReceive(context: Context, intent: Intent?) {
        val action = intent?.action
        Logger.d(TAG, msg = "onReceive action = $action")
        when(action){
            GazeConstants.ACTION_START_UP_GAZE_TRACK ->{
//                GazeManager.startGazeTrackerService(context)
            }
            GazeConstants.ACTION_SHUT_DOWN_GAZE_TRACK ->{
//                GazeManager.stopGazeTrackerService(context)
            }
            GazeConstants.ACTION_START_UP_MASK_THERAPY ->{

            }
            GazeConstants.ACTION_SHUT_DOWN_MASK_THERAPY ->{

            }
            GazeConstants.ACTION_START_CALIBRATION ->{
//                GazeManager.stopGazeTrackerService(context)
//                GazeManager.startCalibration(context)
            }
        }
    }
}