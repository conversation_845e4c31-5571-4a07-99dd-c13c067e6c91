package com.mitdd.gazetracker.common

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: CommonPreference
 * Author by lilin,Date on 2024/10/8 15:17
 * PS: Not easy to write code, please indicate.
 */
enum class CommonPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * APP 域名
     */
    MAIN_DOMAIN(null),

    /**
     * 开/关AI 训练指导
     */
    SWITCH_AI_TRAIN_GUIDE(null),

    /**
     * 显示视点
     */
    DISPLAY_VIEWPOINT(false);

    override fun getNameSpace(): String {
        return "CommonPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }
}