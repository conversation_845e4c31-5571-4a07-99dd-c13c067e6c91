package com.mitdd.gazetracker.common

import java.net.IDN

/**
 * FileName: EmailValidator
 * Author by lilin,Date on 2025/4/21 16:13
 * PS: Not easy to write code, please indicate.
 */
object EmailValidator {

    private val usernameRegex = "^[A-Za-z0-9._%+-]+$".toRegex()
    private val domainRegex = "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\$".toRegex()

    fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {
        // 基础校验：非空、@符号存在且位置正确
        if (email.isBlank() || !email.contains('@')) return false
        val atIndex = email.indexOf('@')
        if (atIndex == 0 || atIndex == email.length - 1) return false

        // 分割用户名和域名
        val username = email.substring(0, atIndex)
        val domain = email.substring(atIndex + 1)

        // 验证用户名格式
        if (!username.matches(usernameRegex)) return false

        // 处理国际化域名（转换为 Punycode）
        return try {
            val normalizedDomain = IDN.toASCII(domain)
            // 验证域名格式
            normalizedDomain.matches(domainRegex)
        } catch (e: IllegalArgumentException) {
            false // 包含非法字符
        }
    }

}