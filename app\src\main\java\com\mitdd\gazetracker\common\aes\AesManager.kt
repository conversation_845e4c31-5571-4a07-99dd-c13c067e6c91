package com.mitdd.gazetracker.common.aes

import org.apache.commons.codec.binary.Hex
import java.nio.charset.StandardCharsets
import java.security.Key
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * FileName: AesManager
 * Author by lilin,Date on 2024/10/26 11:22
 * PS: Not easy to write code, please indicate.
 */
object AesManager {

    private const val AES = "AES"
    private const val AES_ECB_TRANSFORMATION = "AES/ECB/PKCS5Padding"

    /**
     *  配置AES密钥
     *  @param secret 16进制秘钥
     */
    fun getKey(secret: String): Key {
        val key = Hex.decodeHex(secret.toCharArray())
        return SecretKeySpec(key, AES)
    }

    /**
     * AES加密函数
     * @param input 要加密的字符串
     * @return 加密后的16进制字符串
     */
    fun encrypt(input: String, key: Key): String {
        return try {
            val cipher = Cipher.getInstance(AES_ECB_TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, key)
            val encryptedBytes = cipher.doFinal(input.toByteArray(StandardCharsets.UTF_8))
            bytesToHex(encryptedBytes)
        }catch (e:Exception){
            e.printStackTrace()
            ""
        }
    }

    /**
     * AES解密函数
     * @param input 要解密的16进制字符串
     * @return 解密后的字符串
     */
    fun decrypt(input: String, key: Key): String {
        return try {
            val cipher = Cipher.getInstance(AES_ECB_TRANSFORMATION)
            cipher.init(Cipher.DECRYPT_MODE, key)
            val encryptedBytes = Hex.decodeHex(input.toCharArray())
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            String(decryptedBytes, StandardCharsets.UTF_8)
        }catch (e: Exception){
            e.printStackTrace()
            ""
        }
    }

    /**
     * 16进制转字符串
     * @param hexString 16进制字符串
     */
    fun hexToString(hexString: String): String {
        val outputBuffer = StringBuilder()
        var i = 0
        while (i < hexString.length) {
            val str = hexString.substring(i, i + 2)
            outputBuffer.append(str.toInt(16).toChar())
            i += 2
        }
        return outputBuffer.toString()
    }

    /**
     * 字符串转16进制
     * @return 返回16进制字符串
     */
    fun stringToHex(str: String): String {
        val sb = StringBuilder()
        for (ch in str) {
            val hexString = Integer.toHexString(ch.code)
            sb.append(hexString.padStart(2, '0'))
        }
        return sb.toString()
    }

    /**
     * ByteArray转16进制
     * @return 返回16进制字符串
     */
    fun bytesToHex(bytes: ByteArray): String {
        val sb = java.lang.StringBuilder()
        for (b in bytes) {
            sb.append(String.format("%02x", b))
        }
        return sb.toString()
    }

    /**
     * 16进制转ByteArray
     * @param hex 16进制字符串
     */
    fun hexToBytes(hex: String): ByteArray {
        val len = hex.length
        val data = ByteArray(len / 2)
        var i = 0
        while (i < len) {
            data[i / 2] = ((hex[i].digitToIntOrNull(16)
                ?: (-1 shl 4)) + hex[i + 1].digitToIntOrNull(16)!!).toByte()
            i += 2
        }
        return data
    }

}