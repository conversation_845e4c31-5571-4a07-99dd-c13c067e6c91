package com.mitdd.gazetracker.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * FileName: CenterItemDecoration
 * Author by lilin,Date on 2025/1/6 11:18
 * PS: Not easy to write code, please indicate.
 * 类似于ViewPager
 */
class CenterItemDecoration : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val parentWidth = parent.width
        val itemWidth = view.width
        val margin = (parentWidth - itemWidth) / 2
        outRect[margin, 0, margin] = 0
    }

}