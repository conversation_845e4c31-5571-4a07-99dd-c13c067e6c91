package com.mitdd.gazetracker.common.dialog

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R

/**
 * FileName: CommonLoadingDialog
 * Author by lilin,Date on 2024/10/10 20:44
 * PS: Not easy to write code, please indicate.
 */
class CommonLoadingDialog(context: Context) : BaseCommonDialog(context) {

    private val clLoadingRoot by id<ConstraintLayout>(R.id.cl_loading_root)
    private val ivLoading by id<ImageView>(R.id.iv_loading)
    private val tvPrompt by id<TextView>(R.id.tv_prompt)

    private var mLoadingAnim: ObjectAnimator? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_common_loading)

        initView()
    }

    fun setLoadingImage(res:Int){
        ivLoading.setImageResource(res)
    }

    fun setPrompt(text:String){
        tvPrompt.text = text
    }

    fun setBackgroundByResource(res:Int){
        if (res != 0){
            clLoadingRoot.setBackgroundResource(res)
        }else{
            clLoadingRoot.background = null
        }
    }

    fun setBackgroundByColor(color:Int){
        if (color != 0){
            clLoadingRoot.setBackgroundColor(color)
        }else{
            clLoadingRoot.background = null
        }
    }

    private fun initView(){
        initLoadingAnim()
    }

    private fun initLoadingAnim(){
        mLoadingAnim = ObjectAnimator.ofFloat(ivLoading, "rotation", 0f, 359f)
        mLoadingAnim?.repeatCount = ValueAnimator.INFINITE
        mLoadingAnim?.duration = 2000
        mLoadingAnim?.interpolator = LinearInterpolator()
    }

    private fun startAnim(){
        if (mLoadingAnim?.isRunning != true){
            mLoadingAnim?.start()
        }
    }

    private fun pauseAnim(){
        if (mLoadingAnim?.isRunning == true){
            mLoadingAnim?.pause()
        }
    }

    override fun show() {
        super.show()
        startAnim()
    }

    override fun dismiss() {
        pauseAnim()
        super.dismiss()
    }
}