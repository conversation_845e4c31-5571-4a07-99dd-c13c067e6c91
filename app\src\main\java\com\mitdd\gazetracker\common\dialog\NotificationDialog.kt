package com.mitdd.gazetracker.common.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: TreatmentNoOpenDialog
 * Author by lilin,Date on 2024/10/16 19:07
 * PS: Not easy to write code, please indicate.
 * 一句话通知弹窗
 */
class NotificationDialog(context: Context) : BaseCommonDialog(context) {

    private val tvMessage by id<TextView>(R.id.tv_message)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val ivClose by id<ImageView>(R.id.iv_close)

    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_notification)

        initView()
    }

    private fun initView() {
        tvOk.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
        ivClose.setOnSingleClickListener {
            dismiss()
        }
    }

    /**
     * 设置消息
     */
    fun setMessage(msg:String){
        tvMessage.text = msg
    }

    /**
     * 设置确认按钮
     */
    fun setConfirm(confirm:String){
        tvOk.text = confirm
    }

    /**
     * 设置确定按钮点击监听
     */
    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

}