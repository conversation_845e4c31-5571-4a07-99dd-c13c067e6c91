package com.mitdd.gazetracker.common.dialog.task

/**
 * FileName: DialogTask
 * Author by lilin,Date on 2024/11/26 19:11
 * PS: Not easy to write code, please indicate.
 * 弹窗任务
 * @param priority 优先级值，值越大越优先
 * @param isImmediate 是否立即执行，默认false，立即执行会跳过优先级直接插入到队列头部
 */
abstract class DialogTask(val priority:Int,var isImmediate:Boolean = false) : Comparable<DialogTask> {

    var callback: DialogTaskCallback? = null

    abstract fun doTask()

    override fun compareTo(other: DialogTask): Int {
        return if (isImmediate && !other.isImmediate){
            1
        }else if (!isImmediate && other.isImmediate){
            -1
        }else {
            priority - other.priority
        }
    }
}