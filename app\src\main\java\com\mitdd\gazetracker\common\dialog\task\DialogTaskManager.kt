package com.mitdd.gazetracker.common.dialog.task

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.mitdd.gazetracker.base.GTBaseActivity
import java.util.PriorityQueue

/**
 * FileName: DialogTaskManager
 * Author by lilin,Date on 2024/11/26 19:18
 * PS: Not easy to write code, please indicate.
 * 弹窗任务管理器
 */
class DialogTaskManager(val activity: GTBaseActivity) : DefaultLifecycleObserver,
    DialogTaskCallback {

    companion object{
        //APP更新弹窗优先级
        const val DIALOG_PRIORITY_UPDATE = 1000
        //疗程相关弹窗优先级
        const val DIALOG_PRIORITY_TREATMENT = 10
        //一句话通知弹窗弹窗优先级
        const val DIALOG_PRIORITY_NOTIFICATION = 1
    }

    private val TAG = DialogTaskManager::class.java.simpleName

    // 弹窗任务队列
    private val taskQueue = PriorityQueue<DialogTask>()
    // 是否正在显示对话框
    private var isShowing = false
    //宿主页面是否暂停
    private var isStop = false

    init {
        activity.lifecycle.addObserver(this)
    }

    /**
     * 添加对话任务到任务队列。
     * 此方法将新的对话任务添加到任务队列中，以便对话系统按顺序执行。
     * @param task 要添加的对话任务，一个包含对话所需信息的 DialogTask 实例。
     */
    fun addTask(task: DialogTask) {
        taskQueue.add(task)
        executeNextTask()
    }

    /**
     * 执行下一个任务
     */
    private fun executeNextTask() {
        if (!isStop && !isShowing && taskQueue.isNotEmpty()) {
            val task = taskQueue.poll()
            task?.callback = this
            task?.doTask()
        }
    }

    override fun onShow() {
        isShowing = true
    }

    override fun onDismiss() {
        isShowing = false
        executeNextTask()
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        isStop = false
        executeNextTask()
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        isStop = true
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        taskQueue.clear()
        isShowing = false
    }

}