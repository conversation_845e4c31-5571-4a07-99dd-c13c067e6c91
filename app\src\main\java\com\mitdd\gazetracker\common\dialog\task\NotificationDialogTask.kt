package com.mitdd.gazetracker.common.dialog.task

import android.content.Context
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.dialog.NotificationDialog

/**
 * FileName: NotificationDialogTask
 * Author by lilin,Date on 2024/11/26 19:34
 * PS: Not easy to write code, please indicate.
 */
class NotificationDialogTask(val context: Context) : DialogTask(DialogTaskManager.DIALOG_PRIORITY_TREATMENT) {

    //通知消息
    private var message = ""
    //确认按钮文案
    private var confirm = context.getString(R.string.str_ok)
    //确定按钮点击监听
    private var onConfirmClick:(() -> Unit)? = null

    /**
     * 设置消息
     */
    fun setMessage(msg:String){
        message = msg
    }

    /**
     * 设置确认按钮
     */
    fun setConfirm(confirm:String){
        this.confirm = confirm
    }

    /**
     * 设置确定按钮点击监听
     */
    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    override fun doTask() {
        val notificationDialog = NotificationDialog(context).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
        }
        notificationDialog.show()
        notificationDialog.setMessage(message)
        notificationDialog.setConfirm(confirm)
    }
}