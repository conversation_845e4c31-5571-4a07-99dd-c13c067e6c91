package com.mitdd.gazetracker.common.widget

import android.content.Context
import android.content.pm.ApplicationInfo
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.ui.CustomItemDecoration
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.CommonApp
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType.ADD_APP
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType.APP
import com.mitdd.gazetracker.medicalhome.mask.CommonAppAdapter
import com.mitdd.gazetracker.user.UserPreference

/**
 * FileName: CommonAppView
 * Author by lilin,Date on 2025/4/11 14:06
 * PS: Not easy to write code, please indicate.
 * 常用APP页面
 */
class CommonAppView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : ConstraintLayout(context, attrs,defStyle){

    private val rvCommonApp by id<RecyclerView>(R.id.rv_common_application)
    private val ivAddCommonApp by id<ImageView>(R.id.iv_add_common_app)
    private val tvAddCommonApp by id<TextView>(R.id.tv_add_common_app)

    private var commonAppList: MutableList<CommonApp> = mutableListOf()
    private val commonAppAdapter = CommonAppAdapter(commonAppList)

    var onAddCommonApp: (() -> Unit)? = null
    private var itemDecoration:RecyclerView.ItemDecoration? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.view_common_app, this, true)

        initView()
        initListener()
    }

    private fun initView() {
        rvCommonApp.adapter = commonAppAdapter
        rvCommonApp.layoutManager = GridLayoutManager(context, 4)
        itemDecoration = CustomItemDecoration(20.dp2px(context), 10.dp2px(context), 4)

        loadCommonApp()
    }

    private fun initListener(){
        commonAppAdapter.setItemClickListener(object : CommonAppAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                if (position in commonAppList.indices){
                    val commonApp = commonAppList[position]
                    when(commonApp.type){
                        APP -> {
                            val applicationInfo = commonApp.applicationInfo
                            if (applicationInfo != null){
                                val packageManager = context.packageManager
                                val intent = packageManager.getLaunchIntentForPackage(applicationInfo.packageName)
                                if (intent != null){
                                    context.startActivity(intent)
                                }
                            }
                        }
                        ADD_APP -> {
                            onAddCommonApp?.invoke()
                        }
                    }
                }
            }

            override fun onItemDelete(position: Int) {
                if (position in commonAppList.indices){
                    removeCommonApp(commonAppList[position])
                }
            }
        })
        ivAddCommonApp.setOnSingleClickListener {
            onAddCommonApp?.invoke()
        }
    }

    fun setSpanCount(spanCount:Int){
        itemDecoration?.let {
            rvCommonApp.removeItemDecoration(it)
        }
        rvCommonApp.layoutManager = GridLayoutManager(context, spanCount)
        itemDecoration = CustomItemDecoration(20.dp2px(context), 10.dp2px(context), spanCount)
        rvCommonApp.addItemDecoration(itemDecoration!!)
        //强制刷新 RecyclerView
        rvCommonApp.invalidateItemDecorations()
    }

    private fun loadCommonApp(){
        commonAppList.clear()
        //当前应用包名
        val curPackageName = context.packageName
        val packageStr = MMKVManager.decodeString(UserPreference.COMMON_APPLICATION)?:""
        val packageNames = if (!TextUtils.isEmpty(packageStr)){
            packageStr.split(",").filter {
                !TextUtils.isEmpty(it)
            }
        }else{
            emptyList()
        }
        if (packageNames.isNotEmpty()){
            val packageManager = context.packageManager
            val installedApps = packageManager.getInstalledApplications(0).filter {
                packageManager.getLaunchIntentForPackage(it.packageName) != null
            }
            for (packageName in packageNames){
                if (packageName != curPackageName){
                    for (installedApp in installedApps){
                        if (packageName == installedApp.packageName){
                            commonAppList.add(CommonApp(APP,installedApp))
                            continue
                        }
                    }
                }
            }
            commonAppList.add(CommonApp(ADD_APP))
            commonAppAdapter.notifyDataSetChanged()
            rvCommonApp.isVisible = true
            ivAddCommonApp.isVisible = false
            tvAddCommonApp.isVisible = false
        }else{
            rvCommonApp.isVisible = false
            ivAddCommonApp.isVisible = true
            tvAddCommonApp.isVisible = true
        }
        updateCommonAppRecord()
    }

    fun addCommonApp(applicationInfo: ApplicationInfo){
        for (app in commonAppList){
            val info = app.applicationInfo
            if (info != null && info.packageName == applicationInfo.packageName){
                return
            }
        }
        rvCommonApp.isVisible = true
        ivAddCommonApp.isVisible = false
        tvAddCommonApp.isVisible = false
        commonAppList.add(0, CommonApp(APP,applicationInfo))
        val lastCommonApp = commonAppList[commonAppList.size - 1]
        if (lastCommonApp.type != ADD_APP){
            commonAppList.add(CommonApp(ADD_APP))
        }
        commonAppAdapter.notifyDataSetChanged()
        updateCommonAppRecord()
    }

    private fun removeCommonApp(commonApp: CommonApp){
        if (commonApp.type == APP){
            commonAppList.remove(commonApp)
            commonAppAdapter.notifyDataSetChanged()
            updateCommonAppRecord()
        }
    }

    private fun updateCommonAppRecord(){
        val packageNames = StringBuilder()
        for (app in commonAppList){
            val applicationInfo = app.applicationInfo
            if (applicationInfo != null){
                if (packageNames.isEmpty()){
                    packageNames.append(applicationInfo.packageName)
                }else{
                    packageNames.append(",")
                    packageNames.append(applicationInfo.packageName)
                }
            }
        }
        MMKVManager.encodeString(UserPreference.COMMON_APPLICATION, packageNames.toString())
    }

}