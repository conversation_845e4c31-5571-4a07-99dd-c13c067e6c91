package com.mitdd.gazetracker.common.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R

/**
 * FileName: CommonEmptyView
 * Author by lilin,Date on 2024/12/2 10:21
 * PS: Not easy to write code, please indicate.
 */
class CommonEmptyView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : FrameLayout(context, attrs,defStyle) {

    private val clEmptyRoot by id<ConstraintLayout>(R.id.cl_empty_root)
    private val ivEmpty by id<ImageView>(R.id.iv_empty)
    private val tvPrompt by id<TextView>(R.id.tv_prompt)

    init {
        val view = View.inflate(context, R.layout.view_common_empty, null)
        addView(view)
    }

    fun getEmptyRoot():ConstraintLayout{
        return clEmptyRoot
    }

    fun getEmptyIcon():ImageView{
        return ivEmpty
    }

    fun getEmptyPrompt():TextView{
        return tvPrompt
    }

}