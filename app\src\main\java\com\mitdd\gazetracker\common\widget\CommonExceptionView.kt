package com.mitdd.gazetracker.common.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: CommonExceptionView
 * Author by lilin,Date on 2024/12/3 9:44
 * PS: Not easy to write code, please indicate.
 */
class CommonExceptionView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : FrameLayout(context, attrs,defStyle) {

    private val clExceptionRoot by id<ConstraintLayout>(R.id.cl_exception_root)
    private val ivException by id<ImageView>(R.id.iv_exception)
    private val tvExceptionInfo by id<TextView>(R.id.tv_exception_info)
    private val tvExceptionPrompt1 by id<TextView>(R.id.tv_exception_prompt1)
    private val tvExceptionPrompt2 by id<TextView>(R.id.tv_exception_prompt2)
    private val ivRefresh by id<ImageView>(R.id.iv_refresh)

    var refreshClick: (() -> Unit)? = null

    init {
        val view = View.inflate(context, R.layout.view_common_exception, null)
        addView(view)

        initView()
    }

    private fun initView(){
        ivRefresh.setOnSingleClickListener {
            refreshClick?.invoke()
        }
    }

    fun getExceptionRoot():ConstraintLayout = clExceptionRoot

    fun getExceptionIcon():ImageView = ivException

    fun getExceptionInfo():TextView = tvExceptionInfo

    fun getExceptionPrompt1():TextView = tvExceptionPrompt1

    fun getExceptionPrompt2():TextView = tvExceptionPrompt2

    fun getRefreshIcon():ImageView = ivRefresh

}