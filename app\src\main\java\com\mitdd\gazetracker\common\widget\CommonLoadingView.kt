package com.mitdd.gazetracker.common.widget

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R

/**
 * FileName: CommonLoadingView
 * Author by lilin,Date on 2024/10/10 20:34
 * PS: Not easy to write code, please indicate.
 */
class CommonLoadingView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : FrameLayout(context, attrs,defStyle) {

    private val clLoadingRoot by id<ConstraintLayout>(R.id.cl_loading_root)
    private val ivLoading by id<ImageView>(R.id.iv_loading)
    private val tvPrompt by id<TextView>(R.id.tv_prompt)

    private var mLoadingAnim: ObjectAnimator? = null

    init {
        val view = View.inflate(context, R.layout.view_common_loading, null)
        addView(view)
        initLoadingAnim()
    }

    fun setLoadingImage(res:Int){
        ivLoading.setImageResource(res)
    }

    fun setPrompt(text:String){
        tvPrompt.text = text
    }

    fun setBackgroundByResource(res:Int){
        if (res != 0){
            clLoadingRoot.setBackgroundResource(res)
        }else{
            clLoadingRoot.background = null
        }
    }

    fun setBackgroundByColor(color:Int){
        if (color != 0){
            clLoadingRoot.setBackgroundColor(color)
        }else{
            clLoadingRoot.background = null
        }
    }

    private fun initLoadingAnim(){
        mLoadingAnim = ObjectAnimator.ofFloat(ivLoading, "rotation", 0f, 359f)
        mLoadingAnim?.repeatCount = ValueAnimator.INFINITE
        mLoadingAnim?.duration = 2000
        mLoadingAnim?.interpolator = LinearInterpolator()
    }

    fun startAnim(){
        if (mLoadingAnim?.isRunning != true){
            mLoadingAnim?.start()
        }
    }

    fun pauseAnim(){
        if (mLoadingAnim?.isRunning == true){
            mLoadingAnim?.pause()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        startAnim()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        pauseAnim()
    }

}