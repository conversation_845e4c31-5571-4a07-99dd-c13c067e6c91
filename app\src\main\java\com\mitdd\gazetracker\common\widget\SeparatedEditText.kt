package com.mitdd.gazetracker.common.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.util.AttributeSet
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.inputmethod.InputMethodManager
import androidx.annotation.IntDef
import androidx.appcompat.widget.AppCompatEditText
import com.mitdd.gazetracker.R
import java.util.Timer
import java.util.TimerTask

/**
 * FileName: SeparatedEditText
 * Author by lilin,Date on 2024/11/21 20:16
 * PS: Not easy to write code, please indicate.
 * 多方形输入框
 */
class SeparatedEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatEditText(context, attrs, defStyleAttr){

    companion object {
        private const val TYPE_HOLLOW = 1 //空心
        private const val TYPE_SOLID = 2 //实心
        private const val TYPE_UNDERLINE = 3 //下划线

        private const val STYLE_SOLID = 1 //实心
        private const val STYLE_BORDER = 2 // 边界
    }

    private val mHandler = Handler(Looper.getMainLooper())

    //边框画笔
    private lateinit var mBorderPaint: Paint
    //实心块画笔
    private lateinit var mBlockPaint: Paint
    //文字画笔
    private lateinit var mTextPaint: Paint
    //游标画笔
    private lateinit var mCursorPaint: Paint
    //边框矩形
    private lateinit var mBorderRectF: RectF
    //小方块矩形
    private lateinit var mBoxRectF: RectF

    //可绘制宽度
    private var mWidth = 0
    //可绘制高度
    private var mHeight = 0
    //方块宽度
    private var mBoxWidth = 0
    //方块高度
    private var mBoxHeight = 0
    //方块之间间隙
    private var mSpacing: Int = 0

    //圆角
    private var mCorner: Int = 0
    //最大位数
    private var mMaxLength: Int = 6
    //边界粗细
    private var mBorderWidth: Int = 5
    //是否是密码类型
    private var isPassword: Boolean = false
    //显示光标
    private var isShowCursor: Boolean = true
    //光标闪动间隔
    private var mCursorDuration: Int = 500
    //光标宽度
    private var mCursorWidth: Int = 2
    //光标颜色
    private var mCursorColor: Int = Color.parseColor("#EB4E89")
    //实心方式、空心方式
    private var mType: Int = TYPE_HOLLOW

    // 是否显示框框高亮
    private var isHighLightEnable: Boolean = false
    // 高亮样式，仅支持 solid
    private var mHighLightStyle: Int = STYLE_SOLID

    private var isShowKeyboard: Boolean = true
    //边框颜色
    private var mBorderColor: Int = Color.parseColor("#d3d3d3")
    //方块颜色
    private var mBlockColor: Int = Color.parseColor("#EFF3F6")
    //文字颜色
    private var mTextColor: Int = Color.parseColor("#333333")
    //文字大小
    private var mTextSize = 12f
    // 框框高亮颜色
    private var mHighLightColor: Int = Color.parseColor("#d3d3d3")
    // 框框错误颜色
    private var mErrorColor: Int = Color.parseColor("#FF4D4F")
    // 待输入之前的一并高亮
    private var isHighLightBefore = false
    private var isCursorShowing = false
    private var contentText: CharSequence = ""
    private var textChangedListener: TextChangedListener? = null
    private lateinit var timer: Timer
    private lateinit var timerTask: TimerTask

    private var isShowError = false

    init {
        setTextIsSelectable(false)
        customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(actionMode: ActionMode, menu: Menu): Boolean {
                return false
            }

            override fun onPrepareActionMode(actionMode: ActionMode, menu: Menu): Boolean {
                return false
            }

            override fun onActionItemClicked(actionMode: ActionMode, menuItem: MenuItem): Boolean {
                return false
            }

            override fun onDestroyActionMode(actionMode: ActionMode) {}
        }
        val ta = context.obtainStyledAttributes(attrs, R.styleable.SeparatedEditText)
        isPassword = ta.getBoolean(R.styleable.SeparatedEditText_password, false)
        isShowCursor = ta.getBoolean(R.styleable.SeparatedEditText_showCursor, true)
        isHighLightEnable = ta.getBoolean(R.styleable.SeparatedEditText_highLightEnable, false)
        mBorderColor = ta.getColor(R.styleable.SeparatedEditText_borderColor, Color.parseColor("#d3d3d3"))
        mBlockColor = ta.getColor(R.styleable.SeparatedEditText_blockColor, Color.parseColor("#EFF3F6"))
        mTextColor = ta.getColor(R.styleable.SeparatedEditText_textColor, Color.parseColor("#333333"))
        mTextSize = ta.getDimension(R.styleable.SeparatedEditText_textSize, 12f)
        mHighLightColor = ta.getColor(R.styleable.SeparatedEditText_highlightColor, Color.parseColor("#d3d3d3"))
        isHighLightBefore = ta.getBoolean(R.styleable.SeparatedEditText_highLightBefore, false)
        mCursorColor = ta.getColor(R.styleable.SeparatedEditText_cursorColor, Color.parseColor("#EB4E89"))
        mCorner = ta.getDimension(R.styleable.SeparatedEditText_corner, 0f).toInt()
        mSpacing = ta.getDimension(R.styleable.SeparatedEditText_blockSpacing, 0f).toInt()
        mType = ta.getInt(R.styleable.SeparatedEditText_separateType, TYPE_HOLLOW)
        mHighLightStyle = ta.getInt(R.styleable.SeparatedEditText_highlightStyle, STYLE_SOLID)
        mMaxLength = ta.getInt(R.styleable.SeparatedEditText_maxLength, 6)
        mCursorDuration = ta.getInt(R.styleable.SeparatedEditText_cursorDuration, 500)
        mCursorWidth = ta.getDimension(R.styleable.SeparatedEditText_cursorWidth, 2f).toInt()
        mBorderWidth = ta.getDimension(R.styleable.SeparatedEditText_borderWidth, 5f).toInt()
        isShowKeyboard = ta.getBoolean(R.styleable.SeparatedEditText_showKeyboard, true)
        mErrorColor = ta.getColor(R.styleable.SeparatedEditText_errorColor, Color.parseColor("#FF4D4F"))
        ta.recycle()
        init()
    }

    private fun init(){
        isFocusableInTouchMode = true
        isFocusable = true
        isCursorVisible = false
        filters = arrayOf<InputFilter>(LengthFilter(mMaxLength))
        if (isShowKeyboard) {
            mHandler.postDelayed({
                val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.showSoftInput(this,0)
            }, 500)
        }
        mBlockPaint = Paint().apply {
            isAntiAlias = true
            color = mBlockColor
            style = Paint.Style.FILL
        }
        mTextPaint = Paint().apply {
            isAntiAlias = true
            color = mTextColor
            textSize = mTextSize
            style = Paint.Style.FILL
        }
        mBorderPaint = Paint().apply {
            isAntiAlias = true
            color = mBorderColor
            style = Paint.Style.STROKE
            strokeWidth = mBorderWidth.toFloat()
        }
        mCursorPaint = Paint().apply {
            isAntiAlias = true
            color = mCursorColor
            style = Paint.Style.FILL
            strokeWidth = mCursorWidth.toFloat()
        }

        mBorderRectF = RectF()
        mBoxRectF = RectF()

        timerTask = object : TimerTask() {
            override fun run() {
                isCursorShowing = !isCursorShowing
                postInvalidate()
            }
        }
        timer = Timer()
    }

    fun setSpacing(spacing: Int) {
        mSpacing = spacing
        postInvalidate()
    }

    fun setCorner(corner: Int) {
        mCorner = corner
        postInvalidate()
    }

    fun setMaxLength(maxLength: Int) {
        mMaxLength = maxLength
        this.filters = arrayOf<InputFilter>(LengthFilter(maxLength))
        initBox()
        clearText()
    }

    fun setBorderWidth(borderWidth: Int) {
        mBorderWidth = borderWidth
        postInvalidate()
    }

    fun setPassword(password: Boolean) {
        isPassword = password
        postInvalidate()
    }

    fun setShowCursor(showCursor: Boolean) {
        isShowCursor = showCursor
        postInvalidate()
    }

    fun setHighLightEnable(enable: Boolean) {
        isHighLightEnable = enable
        postInvalidate()
    }

    fun setCursorDuration(cursorDuration: Int) {
        mCursorDuration = cursorDuration
        postInvalidate()
    }

    fun setCursorWidth(cursorWidth: Int) {
        mCursorWidth = cursorWidth
        postInvalidate()
    }

    fun setCursorColor(cursorColor: Int) {
        mCursorColor = cursorColor
        postInvalidate()
    }

    fun setType(@TypeDef type: Int) {
        mType = type
        postInvalidate()
    }

    fun setBorderColor(borderColor: Int) {
        mBorderColor = borderColor
        postInvalidate()
    }

    fun setBlockColor(blockColor: Int) {
        mBlockColor = blockColor
        postInvalidate()
    }

    override fun setTextColor(color: Int) {
        mTextColor = color
        super.setTextColor(color)
    }

    override fun setTextSize(size: Float) {
        mTextSize = size
        super.setTextSize(size)
    }

    fun setHighLightColor(color: Int) {
        mHighLightColor = color
        postInvalidate()
    }

    fun setErrorColor(color: Int) {
        mErrorColor = color
        postInvalidate()
    }

    fun showError() {
        if (mType in listOf(TYPE_SOLID, TYPE_UNDERLINE)) {
            isShowError = true
            postInvalidate()
        }
    }

    fun setHighlightStyle(@StyleDef style: Int) {
        mHighLightStyle = style
        postInvalidate()
    }

    fun setTextChangedListener(listener: TextChangedListener?) {
        textChangedListener = listener
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mWidth = w
        mHeight = h
        initBox()
    }

    override fun onDraw(canvas: Canvas) {
        drawRect(canvas)
        drawText(canvas, contentText)
        drawCursor(canvas)
    }

    override fun onTextChanged(
        text: CharSequence,
        start: Int,
        lengthBefore: Int,
        lengthAfter: Int
    ) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)
        isShowError = false
        contentText = text
        invalidate()
        textChangedListener?.also {
            if (text.length == mMaxLength)
                it.textCompleted(text)
            else
                it.textChanged(text)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        //cursorFlashTime为光标闪动的间隔时间
        timer.schedule(timerTask, 0, mCursorDuration.toLong())
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        timer.cancel()
    }

    override fun onTextContextMenuItem(id: Int): Boolean {
        return true
    }

    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        val text: CharSequence? = text
        if (text != null) {
            if (selStart != text.length || selEnd != text.length) {
                setSelection(text.length, text.length)
                return
            }
        }
        super.onSelectionChanged(selStart, selEnd)
    }

    private fun clearText() {
        setText("")
    }

    private fun initBox() {
        mBoxWidth = (mWidth - mSpacing * (mMaxLength - 1)) / mMaxLength
        mBoxHeight = mHeight
        mBorderRectF.set(0f, 0f, mWidth.toFloat(), mHeight.toFloat())
    }

    private fun drawCursor(canvas: Canvas) {
        if (!isCursorShowing && isShowCursor && contentText.length < mMaxLength && hasFocus()) {
            val cursorPosition = contentText.length + 1
            val startX =
                mSpacing * (cursorPosition - 1) + mBoxWidth * (cursorPosition - 1) + mBoxWidth / 2
            val startY = mBoxHeight / 4
            val endY = mBoxHeight - mBoxHeight / 4
            canvas.drawLine(
                startX.toFloat(),
                startY.toFloat(),
                startX.toFloat(),
                endY.toFloat(),
                mCursorPaint
            )
        }
    }

    private fun drawRect(canvas: Canvas) {
        val currentPos = contentText.length
        loop@ for (i in 0 until mMaxLength) {
            mBoxRectF.set(mSpacing * i + mBoxWidth * i.toFloat(), 0f, mSpacing * i + mBoxWidth * i + mBoxWidth.toFloat(),mBoxHeight.toFloat())
            val light = if (isHighLightBefore) currentPos >= i else currentPos == i
            when (mType) {
                TYPE_SOLID -> {
                    if (isShowError) {
                        if (mHighLightStyle == STYLE_SOLID) {
                            canvas.drawRoundRect(
                                mBoxRectF,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBlockPaint.apply { color = mErrorColor })
                        } else {
                            canvas.drawRoundRect(
                                mBoxRectF,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBlockPaint.apply { color = mBlockColor })
                            val tempRect = RectF(
                                mBoxRectF.left,
                                mBoxRectF.top,
                                mBoxRectF.right,
                                mBoxRectF.bottom
                            )
                            canvas.drawRoundRect(
                                tempRect,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBorderPaint.apply { color = mErrorColor })
                        }
                        continue@loop
                    }
                    if (isHighLightEnable && hasFocus() && light) {
                        if (mHighLightStyle == STYLE_SOLID) {
                            canvas.drawRoundRect(
                                mBoxRectF,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBlockPaint.apply { color = mHighLightColor })
                        } else {
                            canvas.drawRoundRect(
                                mBoxRectF,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBlockPaint.apply { color = mBlockColor })
                            val tempRect = RectF(
                                mBoxRectF.left,
                                mBoxRectF.top,
                                mBoxRectF.right,
                                mBoxRectF.bottom
                            )
                            canvas.drawRoundRect(
                                tempRect,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBorderPaint.apply { color = mHighLightColor })
                        }
                    } else {
                        canvas.drawRoundRect(
                            mBoxRectF,
                            mCorner.toFloat(),
                            mCorner.toFloat(),
                            mBlockPaint.apply { color = mBlockColor })
                        if (mHighLightStyle == STYLE_BORDER) {
                            val tempRect = RectF(
                                mBoxRectF.left,
                                mBoxRectF.top,
                                mBoxRectF.right,
                                mBoxRectF.bottom
                            )
                            canvas.drawRoundRect(
                                tempRect,
                                mCorner.toFloat(),
                                mCorner.toFloat(),
                                mBorderPaint.apply { color = mBorderColor })
                        }
                    }
//
                }
                TYPE_UNDERLINE -> {
                    if (isShowError) {
                        canvas.drawLine(
                            mBoxRectF.left,
                            mBoxRectF.bottom,
                            mBoxRectF.right,
                            mBoxRectF.bottom,
                            mBorderPaint.apply { color = mErrorColor })
                        continue@loop
                    }
                    canvas.drawLine(
                        mBoxRectF.left,
                        mBoxRectF.bottom,
                        mBoxRectF.right,
                        mBoxRectF.bottom,
                        mBorderPaint.apply {
                            color = if (isHighLightEnable && hasFocus() && light){
                                mHighLightColor
                            }else{
                                mBorderColor
                            }
                        })

                }
                TYPE_HOLLOW -> {
                    if (i == 0 || i == mMaxLength) continue@loop
                    canvas.drawLine(
                        mBoxRectF.left,
                        mBoxRectF.top,
                        mBoxRectF.left,
                        mBoxRectF.bottom,
                        mBorderPaint.apply { color = mBorderColor })
                }

            }
        }
        if (mType == TYPE_HOLLOW) canvas.drawRoundRect(
            mBorderRectF,
            mCorner.toFloat(),
            mCorner.toFloat(),
            mBorderPaint
        )
    }

    private fun drawText(canvas: Canvas, charSequence: CharSequence) {
        for (i in charSequence.indices) {
            val startX = mSpacing * i + mBoxWidth * i
            val startY = 0
            val baseX = (startX + mBoxWidth / 2 - mTextPaint.measureText(charSequence[i].toString()) / 2).toInt()
            val baseY = (startY + mBoxHeight / 2 - (mTextPaint.descent() + mTextPaint.ascent()) / 2).toInt()
            val centerX = startX + mBoxWidth / 2
            val centerY = startY + mBoxHeight / 2
            val radius = mBoxWidth.coerceAtMost(mBoxHeight) / 6
            if (isPassword) canvas.drawCircle(
                centerX.toFloat(),
                centerY.toFloat(),
                radius.toFloat(),
                mTextPaint
            ) else canvas.drawText(
                charSequence[i].toString(),
                baseX.toFloat(),
                baseY.toFloat(),
                mTextPaint
            )
        }
    }

    /**
     * 密码监听者
     */
    interface TextChangedListener {
        /**
         * 输入/删除监听
         *
         * @param changeText 输入/删除的字符
         */
        fun textChanged(changeText: CharSequence)

        /**
         * 输入完成
         */
        fun textCompleted(text: CharSequence)
    }

    @Retention(AnnotationRetention.SOURCE)
    @IntDef(STYLE_SOLID, STYLE_BORDER)
    annotation class StyleDef

    @Retention(AnnotationRetention.SOURCE)
    @IntDef(TYPE_HOLLOW, TYPE_SOLID, TYPE_UNDERLINE)
    annotation class TypeDef

}