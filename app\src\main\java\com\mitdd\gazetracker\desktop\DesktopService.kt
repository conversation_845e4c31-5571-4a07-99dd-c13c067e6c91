package com.mitdd.gazetracker.desktop

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.ServiceId

/**
 * FileName: DesktopService
 * Author by lilin,Date on 2024/12/9 20:36
 * PS: Not easy to write code, please indicate.
 */
class DesktopService : Service(), LifecycleOwner {

    companion object{
        private val TAG = DesktopService::class.java.simpleName
    }

    private val lifecycleRegistry = LifecycleRegistry(this)

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    override fun onCreate() {
        super.onCreate()
        Logger.d(TAG, msg = "onCreate")
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        startForegroundService()
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        Logger.d(TAG, msg = "onStartCommand")
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
        lifecycleRegistry.currentState = Lifecycle.State.RESUMED
        return START_REDELIVER_INTENT
    }

    override fun onBind(intent: Intent?): IBinder? {
        Logger.d(TAG, msg = "onBind")
        return null
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Logger.d(TAG, msg = "onUnbind")
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        Logger.d(TAG, msg = "onDestroy")
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        super.onDestroy()
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val channelId = "com.mitdd.gazetracker"
        val channelName = "DesktopService"
        (getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager).createNotificationChannel(
            NotificationChannel(channelId,channelName, NotificationManager.IMPORTANCE_NONE)
                .apply {
                    lockscreenVisibility = Notification.VISIBILITY_SECRET
                }
        )
        startForeground(ServiceId.GAZE_TRACKER_SERVICE_ID, Notification.Builder(this,channelId).build())
    }

}