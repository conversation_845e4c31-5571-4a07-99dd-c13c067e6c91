package com.mitdd.gazetracker.device

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.network.NetworkManager
import com.airdoc.component.common.network.NetworkState
import com.airdoc.videobox.MultiClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.widget.CommonExceptionView
import com.mitdd.gazetracker.device.enumeration.PlacementType
import com.mitdd.gazetracker.device.vm.DeviceViewModel
import com.mitdd.gazetracker.medicalhome.HomeMainActivity
import com.mitdd.gazetracker.medicalhospital.HospitalMainActivity
import com.mitdd.gazetracker.read.home.ReadHomeMainActivity
import com.mitdd.gazetracker.tsc.TscMainActivity
import com.mitdd.gazetracker.update.UpdateDialog
import com.mitdd.gazetracker.update.vm.UpdateViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * FileName: DeviceExceptionActivity
 * Author by lilin,Date on 2025/4/10 15:05
 * PS: Not easy to write code, please indicate.
 * 设置状态异常页面
 */
class DeviceExceptionActivity : GTBaseActivity() {

    companion object{
        private val TAG = DeviceExceptionActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, DeviceExceptionActivity::class.java)
            return intent
        }
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val ivMore by id<ImageView>(R.id.iv_more)
    private val clException by id<CommonExceptionView>(R.id.cl_exception)
    private val viewConfig by id<View>(R.id.view_config)

    private val updateVM by viewModels<UpdateViewModel>()
    private val deviceVM by viewModels<DeviceViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_exception)

        initView()
        initListener()
        initObserver()
    }

    private fun initView(){
        showExceptionView()
        ImageLoader.loadImage(this,DeviceManager.getDeviceInfo()?.logo?:"",ivLogo)
    }

    private fun initListener(){
        ivMore.setOnClickListener {
            val menuPopupWindow = DeviceExceptionMenu(this)
            menuPopupWindow.showAsDropDown(ivMore,0,
                10.dp2px(this), Gravity.END)
            menuPopupWindow.show()
            menuPopupWindow.onVersionClick = {
                updateVM.getAppUpdateInfo()
            }
        }
        clException.refreshClick = {
            clException.isVisible = false
            deviceVM.getDeviceBasicInfo()
        }
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                DeviceManager.startConfigActivity(this@DeviceExceptionActivity)
            }
        })
    }

    private fun initObserver(){
        updateVM.appUpdateInfoLiveData.observe(this){
            it?.let {
                val url = it.appVersion?.url?:""
                val version = it.appVersion?.version ?: ""
                val introduction = it.appVersion?.introduce?:""
                val appSize = it.appVersion?.appSize?:0
                if (!TextUtils.isEmpty(url)){
                    UpdateDialog(this,url,version,introduction, it.forceUpdate?:false,appSize).show()
                }
            }
        }
        deviceVM.deviceInfoLiveData.observe(this){
            Logger.d(TAG, msg = "initObserver deviceInfoLiveData = $it")
            doStartActivity()
        }
    }

    private fun showExceptionView(){
        lifecycleScope.launch(Dispatchers.Default) {
            val networkState = NetworkManager.obtainRealTimeNetworkState()
            withContext(Dispatchers.Main){
                clException.isVisible = true
                if(networkState == NetworkState.NORMAL){
                    clException.apply {
                        getExceptionIcon().setImageResource(R.drawable.icon_device_status_abnormal)
                        getExceptionInfo().apply {
                            textSize = 20f
                            text = getString(R.string.str_device_status_abnormal)
                        }
                        getExceptionPrompt1().apply {
                            isVisible = true
                            textSize = 15f
                            text = getString(R.string.str_please_contact_service_hotline)
                        }
                        getExceptionPrompt2().apply {
                            isVisible = true
                            textSize = 15f
                            text = getString(R.string.str_device_sn,DeviceManager.getDeviceSn())
                        }
                    }
                }else{
                    clException.apply {
                        getExceptionIcon().setImageResource(R.drawable.icon_launcher_network_anomaly)
                        getExceptionInfo().apply {
                            textSize = 15f
                            text = getString(R.string.str_network_anomaly_prompt)
                        }
                        getExceptionPrompt1().isVisible = false
                        getExceptionPrompt2().isVisible = false
                    }
                }
            }
        }
    }

    private fun doStartActivity(){
        when(DeviceManager.getPlacementType()){
            PlacementType.M_HOME ->{
                startActivity(HomeMainActivity.createIntent(this))
                finish()
            }
            PlacementType.M_HOSPITAL ->{
                startActivity(HospitalMainActivity.createIntent(this))
                finish()
            }
            PlacementType.R_HOME ->{
                startActivity(ReadHomeMainActivity.createIntent(this))
                finish()
            }
            PlacementType.TSC ->{
                startActivity(TscMainActivity.createIntent(this))
                finish()
            }
            else ->{
                showExceptionView()
            }
        }
    }

}