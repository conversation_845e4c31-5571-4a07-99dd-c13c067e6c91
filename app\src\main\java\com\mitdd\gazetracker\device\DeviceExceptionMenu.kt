package com.mitdd.gazetracker.device

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.R

/**
 * FileName: DeviceExceptionMenu
 * Author by lilin,Date on 2025/4/10 15:50
 * PS: Not easy to write code, please indicate.
 */
class DeviceExceptionMenu(val context: Context) : PopupWindow() {

    private var llVersion: LinearLayout? = null
    private var tvVersion: TextView? = null

    var onVersionClick:(() -> Unit)? = null

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.layout_device_exception_menu, null)
        contentView = view

        isFocusable = true
        isTouchable = true
        isOutsideTouchable = true

        initView(view)

        initListener()

    }

    fun show(){
        tvVersion?.text = context.getString(R.string.str_version_number_s,
            PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
    }

    private fun initView(view: View) {
        llVersion = view.findViewById(R.id.ll_version)
        tvVersion = view.findViewById(R.id.tv_version)

        width = 150.dp2px(context)
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    }

    private fun initListener() {
        llVersion?.setOnSingleClickListener {
            onVersionClick?.invoke()
            dismiss()
        }
    }
}