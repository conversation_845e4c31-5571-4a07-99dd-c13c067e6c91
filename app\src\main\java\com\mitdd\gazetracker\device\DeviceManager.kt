package com.mitdd.gazetracker.device

import android.content.Context
import android.os.Build
import android.text.TextUtils
import com.airdoc.component.common.utils.DeviceUtils
import com.mitdd.gazetracker.config.ConfigActivity
import com.mitdd.gazetracker.device.bean.DeviceInfo
import com.mitdd.gazetracker.device.enumeration.PlacementType
import com.mitdd.gazetracker.mqtt.MQTTConstants
import com.mitdd.gazetracker.mqtt.MQTTManager
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import java.lang.reflect.Method
import java.util.Locale
import java.util.concurrent.atomic.AtomicReference


/**
 * FileName: DeviceManager
 * Author by lilin,Date on 2024/9/27 15:23
 * PS: Not easy to write code, please indicate.
 */
object DeviceManager {

    private val TAG = DeviceManager::class.java.name

    /**
     * 当前设备信息
     */
    private val deviceInfo = AtomicReference<DeviceInfo?>()

    /**
     * 设备SN
     */
    private var deviceSn = AtomicReference("")

    /**
     * 保存设备基础信息
     */
    fun setDeviceInfo(info: DeviceInfo?){
        deviceInfo.set(info)
        MQTTManager.instanceId.set(info?.iotConfig?.instanceId?:"")
        MQTTManager.region.set(info?.iotConfig?.region?:"")
    }

    /**
     * 获取当前设备信息
     */
    fun getDeviceInfo(): DeviceInfo? {
        return deviceInfo.get()
    }

    /**
     * 获取是否是演示模式
     */
    fun isDemoMode():Boolean{
        return deviceInfo.get()?.isDemoMode == true
    }

    /**
     * 设备是否是海外
     */
    fun isOverseas(): Boolean{
        return deviceInfo.get()?.isOverseas == true
    }

    /**
     * 获取placementType
     */
    fun getPlacementType():PlacementType?{
        return when(getDeviceInfo()?.placementType){
            PlacementType.M_HOME.called -> PlacementType.M_HOME
            PlacementType.M_HOSPITAL.called -> PlacementType.M_HOSPITAL
            PlacementType.R_HOME.called -> PlacementType.R_HOME
            PlacementType.R_STORE.called -> PlacementType.R_STORE
            PlacementType.TSC.called -> PlacementType.TSC
            else -> null
        }
    }

    /**
     * 获取设备SN
     */
    fun getDeviceSn():String{
        if (TextUtils.isEmpty(deviceSn.get())){
            deviceSn.set(DeviceUtils.getDeviceSerial())
        }
        return deviceSn.get()
    }

    /**
     * 获取系统OS版本
     */
    fun getOSVersion():String{
        return Build.VERSION.INCREMENTAL
    }

    fun getProductModel():String{
//        val model = Build.MODEL
//        return if (!TextUtils.isEmpty(model)){
//            model
//        }else{
//            "MIT-100"
//        }
        return "Airdoc-dd01"
    }

    /**
     * 设置遮盖疗法状态
     * @param isOn true 表示开，false 表示关
     */
    fun saveMaskTherapyState(isOn: Boolean){
        val data = if (isOn) "1" else "0"
        try {
            val fops = FileOutputStream(DeviceConstants.MASK_THERAPY)
            fops.write(data.toByteArray())
            fops.flush()
            fops.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 获取遮盖疗法状态
     * @return true 表示开，false 表示关
     */
    fun getMaskTherapyState():Boolean{
        val file = File(DeviceConstants.MASK_THERAPY)
        try {
            // 读取文件内容
            val reader = BufferedReader(FileReader(file))
            val line = reader.readLine()
            reader.close()
            // 解析布尔值
            return line.toBoolean()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 获取MQTT 四元组信息
     */
    fun getMQTTFourTuples():String{
        return getSysProperty(MQTTConstants.SYSTEM_PROPERTIES_KEY_MQTT_QUAD, "")
    }

    /**
     * 获取系统属性
     * @param key 属性key
     * @param defaultValue 默认值
     */
    fun getSysProperty(key:String,defaultValue:String): String {
        return try {
//            SystemProperties.get(key,defaultValue)?:defaultValue
            val c = Class.forName("android.os.SystemProperties")
            val get: Method = c.getMethod("get", String::class.java, String::class.java)
            get.invoke(c, key, defaultValue)?.toString()?:defaultValue
        } catch (e: Exception) {
            e.printStackTrace()
            defaultValue
        }
    }

    /**
     * 设置系统属性
     * @param key 属性key
     * @param value 属性值
     */
    fun setSysProperty(key: String, value: String) {
        try {
//            SystemProperties.set(key,value)
            val c = Class.forName("android.os.SystemProperties")
            val set = c.getMethod("set", String::class.java, String::class.java)
            set.invoke(c, key, value)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 获取设备当前语言
     */
    fun getLanguage(context: Context):Locale{
        val locales = context.resources.configuration.locales
        val locale = locales.get(0)
        return locale
    }

    /**
     * 启动配置页面
     */
    fun startConfigActivity(context: Context){
        if (deviceInfo.get()?.isDebugEnable == true){
            context.startActivity(ConfigActivity.createIntent(context))
        }
    }

}