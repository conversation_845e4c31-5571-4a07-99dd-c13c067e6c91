package com.mitdd.gazetracker.device.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.device.bean.DeviceInfo
import com.mitdd.gazetracker.device.bean.MaterialList
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * FileName: DeviceApiService
 * Author by lilin,Date on 2024/10/30 14:03
 * PS: Not easy to write code, please indicate.
 */
interface DeviceApiService {

    /**
     * 获取设备基础信息
     */
    @GET("dt/api/device/v1/basic")
    suspend fun getDeviceBasicInfo(): ApiResponse<DeviceInfo>

    /**
     * 获取指定类型素材
     * @param type 类型 "help"：帮助中心资源
     */
    @GET("dt/portal/ai-box/v1/{type}")
    suspend fun getSpecifiedTypeMaterial(@Path("type") type:String): ApiResponse<MaterialList>

    /**
     * 切换设备placementType
     */
    @POST("dt/api/device/v1/config")
    suspend fun switchPlacementType(@Body placementTypeRequest: RequestBody): ApiResponse<Any>
}