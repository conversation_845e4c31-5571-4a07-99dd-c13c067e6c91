package com.mitdd.gazetracker.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: DeviceInfo
 * Author by lilin,Date on 2024/11/27 9:43
 * PS: Not easy to write code, please indicate.
 * 设备基础信息
 */
@Parcelize
data class DeviceInfo(
    //品牌
    var brand:Int? = null,
    //iot的配置
    var iotConfig: IotConfig? = null,
    //是否是演示模式
    var isDemoMode:Boolean? = null,
    //是否开启调试模式
    var isDebugEnable:Boolean? = null,
    //是否是海外设备
    var isOverseas:Boolean? = null,
    //首页logo
    var logo:String? = null,
    //设备的环境类型 m-home,m-hospital,r-home,r-store
    var placementType:String? = null,
    //支持SKU切换
    var placementTypeSwitch:Boolean? = null,
    //设备sn
    var sn:String? = null,
) : Parcelable
