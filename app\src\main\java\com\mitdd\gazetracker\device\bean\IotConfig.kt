package com.mitdd.gazetracker.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: IotConfig
 * Author by lilin,Date on 2024/10/26 11:43
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class IotConfig(
    //实例ID
    var instanceId:String? = null,
    //实例所在域
    var region:String? = null,
    //产品key
    var productKey:String? = null
): Parcelable {
    companion object{
        val EMPTY = IotConfig()
    }
}
