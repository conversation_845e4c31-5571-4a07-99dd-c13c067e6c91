package com.mitdd.gazetracker.device.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Material
 * Author by lilin,Date on 2024/10/30 14:06
 * PS: Not easy to write code, please indicate.
 * 素材
 */
@Parcelize
data class Material(
    //编号
    var adNo:String? = null,
    //分类,可用值:help,screenSaver
    var category:String? = null,
    //封面地址
    var image:String? = null,
    //链接地址
    var link:String? = null,
    //素材类型,可用值:image,video
    var materialType:String? = null,
    //标题
    var title:String? = null,
): Parcelable
