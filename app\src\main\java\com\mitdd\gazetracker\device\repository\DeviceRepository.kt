package com.mitdd.gazetracker.device.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.device.api.DeviceApiService
import com.mitdd.gazetracker.device.bean.DeviceInfo
import com.mitdd.gazetracker.device.bean.MaterialList
import com.mitdd.gazetracker.net.MainRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: DeviceRepository
 * Author by lilin,Date on 2024/10/30 14:15
 * PS: Not easy to write code, please indicate.
 */
class DeviceRepository : BaseRepository() {

    /**
     * 获取设备基础信息
     */
    suspend fun getDeviceBasicInfo(): ApiResponse<DeviceInfo> {
        return executeHttp {
            MainRetrofitClient.createService(DeviceApiService::class.java).getDeviceBasicInfo()
        }
    }

    /**
     * 获取指定类型素材
     * @param type 类型 "help"：帮助中心资源
     */
    suspend fun getSpecifiedTypeMaterial(type:String): ApiResponse<MaterialList> {
        return executeHttp {
            MainRetrofitClient.createService(DeviceApiService::class.java).getSpecifiedTypeMaterial(type)
        }
    }

    /**
     * 切换设备placementType
     * @param placementType SKU类型
     */
    suspend fun switchPlacementType(placementType:String): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, String>()
            hashMap["placementType"] = placementType
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(DeviceApiService::class.java).switchPlacementType(requestBody)
        }
    }
}