package com.mitdd.gazetracker.device.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.device.bean.DeviceInfo
import com.mitdd.gazetracker.device.bean.MaterialList
import com.mitdd.gazetracker.device.repository.DeviceRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: DeviceViewModel
 * Author by lilin,Date on 2024/10/30 14:17
 * PS: Not easy to write code, please indicate.
 */
class DeviceViewModel : ViewModel() {

    companion object{
        private val TAG = DeviceViewModel::class.java.name
    }

    private val deviceRepository by lazy { DeviceRepository() }

    //设备基础信息
    val deviceInfoLiveData = MutableLiveData<DeviceInfo?>()
    //素材列表
    val materialsLiveData = MutableLiveData<MaterialList?>()
    //切换设备placementType
    val switchPlacementTypeLiveData = MutableLiveData<Any?>()

    /**
     * 获取设备基础信息
     */
    fun getDeviceBasicInfo(){
        viewModelScope.launch {
            MutableStateFlow(deviceRepository.getDeviceBasicInfo()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getDeviceBasicInfo onSuccess")
                    DeviceManager.setDeviceInfo(it)
                    deviceInfoLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getDeviceBasicInfo onDataEmpty")
                    DeviceManager.setDeviceInfo(null)
                    deviceInfoLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getDeviceBasicInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    DeviceManager.setDeviceInfo(null)
                    deviceInfoLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getDeviceBasicInfo onError = $it")
                    DeviceManager.setDeviceInfo(null)
                    deviceInfoLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 获取指定类型素材
     * @param type 类型 "help"：帮助中心资源
     */
    fun getSpecifiedTypeMaterial(type:String){
        viewModelScope.launch {
            MutableStateFlow(deviceRepository.getSpecifiedTypeMaterial(type)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getSpecifiedTypeMaterial onSuccess")
                    materialsLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getSpecifiedTypeMaterial onDataEmpty")
                    materialsLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getSpecifiedTypeMaterial onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    materialsLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getSpecifiedTypeMaterial onError = $it")
                    materialsLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 切换设备placementType
     * @param placementType SKU类型
     */
    fun switchPlacementType(placementType:String){
        viewModelScope.launch {
            MutableStateFlow(deviceRepository.switchPlacementType(placementType)).collectResponse {
                onSuccess = { _, _, _ ->
                    Logger.d(TAG, msg = "switchPlacementType onSuccess")
                    switchPlacementTypeLiveData.postValue(Any())
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "switchPlacementType onDataEmpty")
                    switchPlacementTypeLiveData.postValue(Any())
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "switchPlacementType onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    switchPlacementTypeLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "switchPlacementType onError = $it")
                    switchPlacementTypeLiveData.postValue(null)
                }
            }
        }
    }

}