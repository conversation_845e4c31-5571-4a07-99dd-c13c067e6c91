package com.mitdd.gazetracker.flipbeat

import android.bluetooth.BluetoothDevice
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState

/**
 * FileName: FlipBeatListener
 * Author by lilin,Date on 2024/10/29 14:32
 * PS: Not easy to write code, please indicate.
 */
interface FlipBeatListener {

    fun onConnectionStateChange(device: BluetoothDevice,state: FlipBeatState){

    }

    fun onScanResult(device: BluetoothDevice){

    }
}