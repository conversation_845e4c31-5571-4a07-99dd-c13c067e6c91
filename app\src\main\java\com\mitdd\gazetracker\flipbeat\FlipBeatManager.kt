package com.mitdd.gazetracker.flipbeat

import android.Manifest
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PermissionUtils
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

/**
 * FileName: FlipBeatManager
 * Author by lilin,Date on 2024/10/29 13:51
 * PS: Not easy to write code, please indicate.
 * 翻转拍管理
 */
object FlipBeatManager {

    private val TAG = FlipBeatManager::class.java.simpleName

    //蓝牙翻转拍前缀
    const val BLUETOOTH_FLIP_PREFIX_DM = "DM"
    const val BLUETOOTH_FLIP_PREFIX_AF = "AF"
    //蓝牙翻转拍写服务UUID
    private const val FLIP_WRITE_UUID = "0000ffe5-0000-1000-8000-00805f9b34fb"
    //蓝牙翻转拍写服务特征UUID
    private const val FLIP_WRITE_CHARACTERISTIC_UUID = "0000ffe9-0000-1000-8000-00805f9b34fb"
    //蓝牙翻转拍读服务UUID
    const val FLIP_READ_UUID = "0000ffe0-0000-1000-8000-00805f9b34fb"
    //蓝牙翻转拍读服务特征UUID
    const val FLIP_READ_CHARACTERISTIC_UUID = "0000ffe4-0000-1000-8000-00805f9b34fb"

    private val mCoroutineScope = CoroutineScope(Dispatchers.Main)
    private val mListenerSet: HashSet<FlipBeatListener> = HashSet()
    //翻转拍蓝牙广播监听
    private val mFlipBeatReceiver = object : BroadcastReceiver(){
        override fun onReceive(context: Context?, intent: Intent?) {
            if (context == null || intent == null) return
            handlerBroadcastReceiver(intent)
        }
    }
    //搜索监听
    private val mScanCallback = object : ScanCallback(){
        override fun onScanResult(callbackType: Int, result: ScanResult?) {
            super.onScanResult(callbackType, result)
            handlerScanResult(result)
        }
    }
    private var mFlipBeatState = FlipBeatState.DISCONNECTED
    private lateinit var mBluetoothManager:BluetoothManager
    private var bluetoothGatt: BluetoothGatt? = null
    private var writeBluetoothGattService: BluetoothGattService? = null

    fun init(){
        mBluetoothManager = BaseCommonApplication.instance.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        registerReceiver()
    }

    fun release(){
        unRegisterReceiver()
    }

    fun getFlipBeatState(): FlipBeatState {
        return mFlipBeatState
    }

    /**
     * 获取连接的翻转盘
     */
    fun getConnectedFlipBeat():BluetoothDevice?{
        return bluetoothGatt?.device
    }

    /**
     * 开始扫描蓝牙翻转拍
     */
    fun startScanFlip(){
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance, Manifest.permission.BLUETOOTH_SCAN)){
            mBluetoothManager.adapter?.bluetoothLeScanner?.startScan(mScanCallback)
        }
    }

    /**
     * 停止扫描翻转拍
     */
    fun stopScanFlip(){
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_SCAN)){
            mBluetoothManager.adapter?.bluetoothLeScanner?.stopScan(mScanCallback)
        }
    }

    /**
     * 连接翻转拍
     */
    fun connectFlipBeat(device: BluetoothDevice){
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
            val name = device.name
            if (name != null && (name.startsWith(BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(BLUETOOTH_FLIP_PREFIX_AF))){
                bluetoothGatt?.disconnect()
                bluetoothGatt?.close()
                bluetoothGatt = null
                bluetoothGatt = device.connectGatt(BaseCommonApplication.instance, false, object : BluetoothGattCallback(){
                    override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                        super.onConnectionStateChange(gatt, status, newState)
                        handlerConnectionStateChange(gatt,status, newState)
                    }

                    override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                        super.onServicesDiscovered(gatt, status)
                        handlerServicesDiscovered(gatt, status)
                    }

                    override fun onCharacteristicWrite(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?, status: Int) {
                        super.onCharacteristicWrite(gatt, characteristic, status)
                        handlerCharacteristicWrite(status)
                    }
                })
            }
        }
    }

    /**
     * 断开翻转拍连接
     */
    fun disconnectFlipBeat(){
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
            bluetoothGatt?.disconnect()
        }
    }

    /**
     * 向翻转拍写入命令
     */
    fun writeDataToFlipBeat(data:Array<String>){
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
            writeBluetoothGattService?.let { service ->
                val characteristic = service.getCharacteristic(UUID.fromString(
                    FLIP_WRITE_CHARACTERISTIC_UUID
                ))
                Logger.d(TAG, msg = "writeDataToFlipBeat properties = ${characteristic?.properties}")
                if (characteristic != null){
                    characteristic.writeType = BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
                    // 解析十六进制字符串数组并转换为字节数组,Byte -128 到 127。
                    val bytes = data.map { it.substring(2).toInt(radix = 16).toByte() }.toByteArray()
                    Logger.d(TAG, msg = "writeDataToFlipBeat byteArray = ${bytes.toUByteArray().contentToString()}")
                    characteristic.setValue(bytes)
                    bluetoothGatt?.writeCharacteristic(characteristic)
                }
            }
        }
    }

    /**
     * 注册翻转拍监听
     */
    fun registerFlipBeatListener(listener: FlipBeatListener){
        mListenerSet.add(listener)
    }

    /**
     * 解注册翻转拍监听
     */
    fun unRegisterFlipBeatListener(listener: FlipBeatListener){
        mListenerSet.remove(listener)
    }

    //给满足条件的属性配置上消息通知
    private fun prepareBroadcastDataNotify(gatt:BluetoothGatt,characteristic: BluetoothGattCharacteristic){
        Logger.d(TAG, msg = "prepareBroadcastDataNotify uuid = ${characteristic.uuid}")
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
            val properties = characteristic.properties
            Logger.d(TAG, msg = "prepareBroadcastDataNotify properties = $properties")
            if (properties or  BluetoothGattCharacteristic.PROPERTY_NOTIFY > 0){
                //"00002902-0000-1000-8000-00805f9b34fb" 是Android系统保留的固定值
                val descriptor = characteristic.getDescriptor(UUID.fromString("00002902-0000-1000-8000-00805f9b34fb"))
                if (descriptor != null){
                    //注册消息通知
                    gatt.setCharacteristicNotification(characteristic, true)
                    descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE)
                    gatt.writeDescriptor(descriptor);
                }
            }
        }
    }

    //处理扫描结果
    private fun handlerScanResult(result: ScanResult?){
        val device = result?.device ?: return
        if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
            device.name?.let { name ->
                if (name.startsWith(BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(BLUETOOTH_FLIP_PREFIX_AF)){
                    mCoroutineScope.launch {
                        mListenerSet.forEach {
                            it.onScanResult(device)
                        }
                    }
                }
            }
        }
    }

    //处理连接状态变化
    private fun handlerConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int){
        Logger.d(TAG, msg = "handlerConnectionStateChange status = $status, newState = $newState")
        when(newState){
            BluetoothProfile.STATE_CONNECTED ->{
                mFlipBeatState = FlipBeatState.CONNECTED
                if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
                    gatt.discoverServices()
                }
                mCoroutineScope.launch {
                    mListenerSet.forEach {
                        it.onConnectionStateChange(gatt.device, FlipBeatState.CONNECTED)
                    }
                }
            }
            BluetoothProfile.STATE_CONNECTING ->{
                mFlipBeatState = FlipBeatState.CONNECTING
                mCoroutineScope.launch {
                    mListenerSet.forEach {
                        it.onConnectionStateChange(gatt.device, FlipBeatState.CONNECTING)
                    }
                }
            }
            BluetoothProfile.STATE_DISCONNECTING->{
                mFlipBeatState = FlipBeatState.DISCONNECTING
                mCoroutineScope.launch {
                    mListenerSet.forEach {
                        it.onConnectionStateChange(gatt.device, FlipBeatState.DISCONNECTING)
                    }
                }
            }
            BluetoothProfile.STATE_DISCONNECTED ->{
                mFlipBeatState = FlipBeatState.DISCONNECTED
                bluetoothGatt?.close()
                bluetoothGatt =null
                mCoroutineScope.launch {
                    mListenerSet.forEach {
                        it.onConnectionStateChange(gatt.device, FlipBeatState.DISCONNECTED)
                    }
                }
            }
        }
    }

    private fun handlerServicesDiscovered(gatt: BluetoothGatt, status: Int){
        Logger.d(TAG, msg = "handlerServicesDiscovered status = $status")
        if (status == BluetoothGatt.GATT_SUCCESS){
            gatt.services.forEachIndexed { _, bluetoothGattService ->
                if (FLIP_WRITE_UUID == bluetoothGattService.uuid.toString()){
                    Logger.d(TAG, msg = "handlerServicesDiscovered Service UUID = ${bluetoothGattService.uuid}, type = ${bluetoothGattService.type}")
                    writeBluetoothGattService = bluetoothGattService
                    bluetoothGattService.characteristics.forEach { characteristic ->
                        prepareBroadcastDataNotify(gatt, characteristic)
                    }
                }
            }
        }
    }

    private fun handlerCharacteristicWrite(status: Int){
        Logger.d(TAG, msg = "handlerCharacteristicWrite status = $status")
    }

    private fun handlerBroadcastReceiver(intent: Intent){
        Logger.d(TAG, msg = "handlerBroadcastReceiver ${intent.action}")
        val device  = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
        when(intent.action){
            BluetoothDevice.ACTION_BOND_STATE_CHANGED ->{
            }
            BluetoothDevice.ACTION_ACL_CONNECTED ->{
                //蓝牙设备已连接
                if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
                    device?.name?.let { name ->
                        if (name.startsWith(BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(BLUETOOTH_FLIP_PREFIX_AF)){
                            mCoroutineScope.launch {
                                mListenerSet.forEach {
                                    it.onConnectionStateChange(device, FlipBeatState.CONNECTED)
                                }
                            }
                        }
                    }
                }
            }
            BluetoothDevice.ACTION_ACL_DISCONNECTED ->{
                //蓝牙设备断开连接
                if (PermissionUtils.checkSelfPermission(BaseCommonApplication.instance,Manifest.permission.BLUETOOTH_CONNECT)){
                    device?.name?.let { name ->
                        if (name.startsWith(BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(BLUETOOTH_FLIP_PREFIX_AF)){
                            mCoroutineScope.launch {
                                mListenerSet.forEach {
                                    it.onConnectionStateChange(device, FlipBeatState.DISCONNECTED)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 注册蓝牙相关广播监听
     */
    private fun registerReceiver(){
        val filter = IntentFilter()
        //配对状态改变 配对开始时，配对成功时
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
        //蓝牙设备已连接
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
        //蓝牙设备断开连接
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        BaseCommonApplication.instance.registerReceiver(mFlipBeatReceiver, filter)
    }

    /**
     * 解注册蓝牙相关广播监听
     */
    private fun unRegisterReceiver(){
        BaseCommonApplication.instance.unregisterReceiver(mFlipBeatReceiver)
    }

}