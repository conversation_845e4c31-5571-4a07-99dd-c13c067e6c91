package com.mitdd.gazetracker.gaze

/**
 * FileName: GazeConstants
 * Author by lilin,Date on 2024/12/5 15:26
 * PS: Not easy to write code, please indicate.
 */
object GazeConstants {

    // 图像分析宽
    const val IMAGE_WIDTH = 4048
    // 图像分析高
    const val IMAGE_HEIGHT = 3040
    // 模型文件夹名称
    const val MODEL_DIR_NAME = "configs"
    // 模型文件扩展名
    const val MODEL_FILE_EXTENSION = ".rknn"

    /**
     * 眼动服务进程与客户端进程通信消息
     * 1开头表示服务接收的消息，2开头表示服务发送的消息
     */
    //绑定服务时发生一个连接消息传递客户端Messenger
    const val MSG_SERVICE_CONNECTED = 10000
    //开启视线追踪
    const val MSG_START_TRACK = 10001
    //停止视线追踪
    const val MSG_STOP_TRACK = 10002
    //开启视标校准
    const val MSG_START_VISUAL_CALIBRATION = 10003
    //停止视标校准
    const val MSG_STOP_VISUAL_CALIBRATION = 10004
    //开启姿势校准
    const val MSG_START_POSTURE_CALIBRATION = 10005
    //停止姿势校准
    const val MSG_STOP_POSTURE_CALIBRATION = 10006
    //视标校准时通知视标准备好
    const val MSG_VISUAL_READY = 10007
    //启动眼动应用-治疗（弱视、近视）
    const val MSG_START_APPLIED_CURE = 10008
    //停止眼动应用-治疗（弱视、近视）
    const val MSG_STOP_APPLIED_CURE = 10009
    //启动眼动应用-阅读
    const val MSG_START_APPLIED_READING = 10010
    //停止眼动应用-阅读
    const val MSG_STOP_APPLIED_READING = 10011
    //启动眼动应用-眼动能力检查-注视
    const val MSG_START_APPLIED_STARE = 10012
    //停止眼动应用-眼动能力检查-注视
    const val MSG_STOP_APPLIED_STARE = 10013
    //启动眼动应用-眼动能力检查-追随
    const val MSG_START_APPLIED_FOLLOW = 10014
    //停止眼动应用-眼动能力检查-追随
    const val MSG_STOP_APPLIED_FOLLOW = 10015
    //启动眼动应用-眼动能力检查-扫视
    const val MSG_START_APPLIED_GLANCE = 10016
    //停止眼动应用-眼动能力检查-扫视
    const val MSG_STOP_APPLIED_GLANCE = 10017
    //获取眼动轨迹数据结果
    const val MSG_GET_GAZE_TRAJECTORY = 10018
    //设置扫视点
    const val MSG_SET_GLANCE_POINT = 10019
    //打开相机
    const val MSG_TURN_ON_CAMERA = 10020
    //关闭相机
    const val MSG_TURN_OFF_CAMERA = 10021
    // 上报校准数据
    const val MSG_START_UPLOAD_CLOUD = 10022
    //停止眼动应用
    const val MSG_STOP_APPLIED = 10023

    //视线追踪状态,可以通过[KEY_STATE]获取状态值
    const val MSG_GAZE_TRACKING_STATE = 20001
    //眼动应用-治疗（弱视、近视）状态,可以通过[KEY_STATE]获取状态值
    const val MSG_APPLIED_CURE_STATE = 20008
    //眼动应用-阅读状态,可以通过[KEY_STATE]获取状态值
    const val MSG_APPLIED_READING_STATE = 20010
    //眼动应用-眼动能力检查-注视状态,可以通过[KEY_STATE]获取状态值
    const val MSG_APPLIED_STARE_STATE = 20012
    //眼动应用-眼动能力检查-追随状态,可以通过[KEY_STATE]获取状态值
    const val MSG_APPLIED_FOLLOW_STATE = 20014
    //眼动应用-眼动能力检查-扫视状态,可以通过[KEY_STATE]获取状态值
    const val MSG_APPLIED_GLANCE_STATE = 20016
    //眼动轨迹数据结果,可以通过[KEY_GAZE_TRAJECTORY]获取结果
    const val MSG_GAZE_TRAJECTORY_RESULT = 20018
    // MSG 更新治疗时长
    const val MSG_UPDATE_TREATMENT_DURATION = 20101
    // MSG 上报的治疗结果
    const val MSG_REPORT_TREATMENT_RESULT = 20102
    // MSG 设置的扫视点扫视完成
    const val MSG_SACCADE_POINT_COMPLETE = 20103

    // 事件 切换治疗状态
    const val EVENT_SWITCH_CURE = "SWITCH_CURE"
    // 事件 切换显示视点状态
    const val EVENT_SWITCH_DISPLAY_VIEWPOINT = "SWITCH_DISPLAY_VIEWPOINT"

    // KEY 状态 true 开启，false 关闭
    const val KEY_STATE = "state"
    // KEY 是否成功
    const val KEY_IS_SUCCESS = "is_success"
    // KEY 视线追踪过程中进行校准时为true，普通校准时为false
    const val KEY_IS_CORRECTION = "is_correction"
    // KEY 视标校准视标是否准备好,true表示准备好，false表示未准备好
    const val KEY_VISUAL_READY = "visual_ready"
    // KEY 校准模式
    const val KEY_CALIBRATION_MODE = "calibration_mode"
    // KEY 阅读视线追踪数据
    const val KEY_READING_TRACK_DATA = "reading_track_data"
    // KEY 遮盖疗法启用状态。true 开启，false 关闭
    const val KEY_MASK_THERAPY_STATE = "mask_therapy_state"
    // KEY 计划治疗时长。秒
    const val KEY_PLANNED_DURATION = "planned_duration"
    // KEY 已治疗时长。秒
    const val KEY_TREATMENT_DURATION = "treatment_duration"
    // KEY 上报的治疗结果
    const val KEY_REPORT_CURE_RESULT = "report_cure_result"
    // KEY x坐标 [0~1]
    const val KEY_X = "x"
    // KEY y坐标 [0~1]
    const val KEY_Y = "y"
    // KEY 眼动轨迹数据
    const val KEY_GAZE_TRAJECTORY = "gaze_trajectory"
    //用于上报治疗结果的baseUrl
    const val KEY_BASE_URL = "baseUrl"
    //用于上报治疗结果的Url
    const val KEY_REPORT_URL = "reportUrl"
    //用于上报治疗结果的上报参数
    const val KEY_REPORT_PARAM = "reportParam"
    //用于上报治疗结果的上报请求头
    const val KEY_REPORT_HEADER = "reportHeader"

    // Broadcast Action：开启视线追踪服务，不会开启遮盖疗法
    const val ACTION_START_UP_GAZE_TRACK = "com.mitdd.gazetracker.START_UP_GAZE_TRACK"
    // Broadcast Action：关闭视线追踪服务，会关闭遮盖疗法
    const val ACTION_SHUT_DOWN_GAZE_TRACK = "com.mitdd.gazetracker.SHUT_DOWN_GAZE_TRACK"
    // Broadcast Action：开启遮盖疗法，会先开启视线追踪服务
    const val ACTION_START_UP_MASK_THERAPY = "com.mitdd.gazetracker.START_UP_MASK_THERAPY"
    // Broadcast Action：关闭遮盖疗法，会关闭视线追踪服务
    const val ACTION_SHUT_DOWN_MASK_THERAPY = "com.mitdd.gazetracker.SHUT_DOWN_MASK_THERAPY"
    // Broadcast Action：启动视线追踪校准
    const val ACTION_START_CALIBRATION = "com.mitdd.gazetracker.START_CALIBRATION"
}