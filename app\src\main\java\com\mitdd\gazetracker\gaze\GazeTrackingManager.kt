package com.mitdd.gazetracker.gaze

import android.content.Context
import com.airdoc.component.common.cache.MMKVManager
import com.mitdd.gazetracker.utils.GTUtils
import com.mitdd.gazetracker.common.CommonPreference
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

/**
 * FileName: GazeTrackManager
 * Author by lilin,Date on 2024/7/23 15:49
 * PS: Not easy to write code, please indicate.
 */
object GazeTrackingManager {

    //Log前缀
    const val PREFIX = "GazeTracking"

    //公共协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    /**
     * 初始化视线追踪
     */
    fun initGazeTracking(context: Context){
        coroutineScope.launch {
            copyModel2Dir(context)
        }
    }

    /**
     * 获取是否显示视点
     */
    fun getDisplayViewpoint():Boolean{
        return MMKVManager.decodeBool(CommonPreference.DISPLAY_VIEWPOINT)?:false
    }

    /**
     * 设置是否显示视点
     */
    fun setDisplayViewpoint(isShow:Boolean){
        MMKVManager.encodeBool(CommonPreference.DISPLAY_VIEWPOINT,isShow)
    }

    /**
     * 拷贝模型到本地文件
     */
    private suspend fun copyModel2Dir(context: Context){
        val weightDir = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE)
        val assetManager = context.assets
        try {
            // 列出assets文件夹下的所有文件和子文件夹
            val assetsList = assetManager.list(GazeConstants.MODEL_DIR_NAME)
            if (!assetsList.isNullOrEmpty()){
                for (assets in assetsList) {
                    if (assets.endsWith(GazeConstants.MODEL_FILE_EXTENSION)) {
                        withContext(Dispatchers.IO){
                            // 如果文件以 .rknn 结尾，就添加到列表中
                            val weightFile = File(weightDir, assets)
                            GTUtils.copyAssets2Dir(context, weightFile, "${GazeConstants.MODEL_DIR_NAME}/$assets")
                        }
                    }
                }
            }
        } catch (e: IOException) {
            // 处理异常情况
            e.printStackTrace()
        }
    }

}