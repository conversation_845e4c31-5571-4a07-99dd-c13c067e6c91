package com.mitdd.gazetracker.gaze

import com.airdoc.component.common.cache.MMKVManager
import com.mitdd.gazetracker.gaze.enumeration.CoverChannel
import com.mitdd.gazetracker.gaze.enumeration.CoverMode
import com.mitdd.gazetracker.gaze.enumeration.CoverRange
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.mask.MaskPreference

/**
 * FileName: MaskManager
 * Author by lilin,Date on 2024/11/9 17:22
 * PS: Not easy to write code, please indicate.
 */
object MaskManager {

    /**
     * 设置遮盖疗法-遮盖通道
     * @param channel 1-7
     */
    fun setCoverChannel(channel:Int?){
        MMKVManager.encodeInt(MaskPreference.MT_COVER_CHANNEL, channel?: CoverChannel.CHANNEL_R.channel)
    }

    /**
     * 获取遮盖疗法-遮盖通道
     */
    fun getCoverChannel(): CoverChannel {
        return transitionCoverChannel(MMKVManager.decodeInt(MaskPreference.MT_COVER_CHANNEL))
    }

    /**
     * 设置遮盖疗法-遮盖模式
     * @param mode 只支持0~3的整数值，表示4个可选模式。0：内部高斯模糊（默认模式），1：外部高斯模糊，2：内部区域置黑，3：外部区域置黑
     */
    fun setCoverMode(mode:Int?){
        MMKVManager.encodeInt(MaskPreference.MT_COVER_MODE, mode?: CoverMode.INTERNAL_GAUSSIAN_BLUR.mode)
    }

    /**
     * 获取遮盖疗法-遮盖模式
     */
    fun getCoverMode(): CoverMode {
        return transitionCoverMode(MMKVManager.decodeInt(MaskPreference.MT_COVER_MODE))
    }

    /**
     * 设置遮盖疗法-遮盖区域
     * @param area 0.5～5.5黄斑区的遮盖区域直径值
     */
    fun setCoverArea(area:Float?){
        MMKVManager.encodeFloat(MaskPreference.MT_COVER_AREA, area?:4.0f)
    }

    /**
     * 获取遮盖疗法-遮盖区域
     */
    fun getCoverArea():Float?{
        return MMKVManager.decodeFloat(MaskPreference.MT_COVER_AREA)
    }

    /**
     * 设置遮盖疗法-遮盖幅度
     * @param range
     */
    fun setCoverRange(range:Float?){
        MMKVManager.encodeFloat(MaskPreference.MT_COVER_RANGE, range?: CoverRange.RANGER_MILD.ranger)
    }

    /**
     * 获取遮盖疗法-遮盖幅度
     */
    fun getCoverRange(): CoverRange {
        return transitionCoverRange(MMKVManager.decodeFloat(MaskPreference.MT_COVER_RANGE))
    }

    /**
     * 设置遮盖疗法-弱视眼
     * @param eye 左眼：left，右眼：right
     */
    fun setAmblyopicEye(eye:String?){
        MMKVManager.encodeString(MaskPreference.MT_AMBLYOPIC_EYE, eye)
    }

    /**
     * 获取遮盖疗法-弱视眼
     */
    fun getAmblyopicEye(): AmblyopicEye {
        return transitionAmblyopiaEye(MMKVManager.decodeString(MaskPreference.MT_AMBLYOPIC_EYE))
    }

    /**
     * 转换遮盖通道
     */
    private fun transitionCoverChannel(channel:Int?): CoverChannel {
        return when(channel){
            CoverChannel.CHANNEL_R.channel ->{
                CoverChannel.CHANNEL_R
            }
            CoverChannel.CHANNEL_G.channel ->{
                CoverChannel.CHANNEL_G
            }
            CoverChannel.CHANNEL_GR.channel ->{
                CoverChannel.CHANNEL_GR
            }
            CoverChannel.CHANNEL_B.channel->{
                CoverChannel.CHANNEL_B
            }
            CoverChannel.CHANNEL_BR.channel ->{
                CoverChannel.CHANNEL_BR
            }
            CoverChannel.CHANNEL_BG.channel ->{
                CoverChannel.CHANNEL_BG
            }
            CoverChannel.CHANNEL_BGR.channel ->{
                CoverChannel.CHANNEL_BGR
            }
            else ->{
                CoverChannel.CHANNEL_R
            }
        }
    }

    /**
     * 转换遮盖模式
     */
    private fun transitionCoverMode(mode:Int?): CoverMode {
        return when(mode){
            CoverMode.INTERNAL_GAUSSIAN_BLUR.mode ->{
                CoverMode.INTERNAL_GAUSSIAN_BLUR
            }
            CoverMode.EXTERNAL_GAUSSIAN_BLUR.mode ->{
                CoverMode.EXTERNAL_GAUSSIAN_BLUR
            }
            CoverMode.INTERNAL_AREAS_BLACKENED.mode ->{
                CoverMode.INTERNAL_AREAS_BLACKENED
            }
            CoverMode.EXTERNAL_AREAS_BLACKENED.mode ->{
                CoverMode.EXTERNAL_AREAS_BLACKENED
            }
            else ->{
                CoverMode.INTERNAL_GAUSSIAN_BLUR
            }
        }
    }

    /**
     * 转换遮盖幅度
     */
    private fun transitionCoverRange(range:Float?): CoverRange {
        return when(range){
            CoverRange.RANGER_MILD.ranger ->{
                CoverRange.RANGER_MILD
            }
            CoverRange.RANGER_MODERATE.ranger ->{
                CoverRange.RANGER_MODERATE
            }
            CoverRange.RANGER_HEIGHT.ranger ->{
                CoverRange.RANGER_HEIGHT
            }
            CoverRange.RANGER_COMPLETELY.ranger ->{
                CoverRange.RANGER_COMPLETELY
            }
            else ->{
                CoverRange.RANGER_MILD
            }
        }
    }

    /**
     * 转换弱视眼
     */
    private fun transitionAmblyopiaEye(eye:String?): AmblyopicEye {
        return when(eye){
            AmblyopicEye.LEFT.value ->{
                AmblyopicEye.LEFT
            }
            AmblyopicEye.RIGHT.value ->{
                AmblyopicEye.RIGHT
            }
            else ->{
                AmblyopicEye.LEFT
            }
        }
    }

}