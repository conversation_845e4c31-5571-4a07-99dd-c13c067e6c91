package com.mitdd.gazetracker.gaze.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Url

/**
 * FileName: ReportApiService
 * Author by lilin,Date on 2025/2/14 16:13
 * PS: Not easy to write code, please indicate.
 */
interface ReportApiService {

    /**
     * 上报治疗结果
     * "dt/api/train/v1/occlusion-therapy/event/report"
     * @param dynamicUrl 动态url
     * @param occlusionSectionReport 治疗结果
     * @param headers 请求头
     */
    @POST
    suspend fun reportCureResult(
        @Url dynamicUrl: String,
        @Body occlusionSectionReport: RequestBody,
        @HeaderMap headers: HashMap<String, Any>
    ): ApiResponse<CureInfo>

}