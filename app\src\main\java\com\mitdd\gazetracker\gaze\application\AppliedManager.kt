package com.mitdd.gazetracker.gaze.application

import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.bean.GazeTrackResult
import com.mitdd.gazetracker.gaze.enumeration.AppliedMode
import com.mitdd.gazetracker.gaze.enumeration.AppliedMode.*
import com.mitdd.gazetracker.gaze.listener.IGazeAppliedListener
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference


/**
 * FileName: GazeApplicationHolder
 * Author by lilin,Date on 2025/1/13 20:03
 * PS: Not easy to write code, please indicate.
 * 视线追踪应用持有者
 */
object AppliedManager : IGazeAppliedListener {

    private val TAG = AppliedManager::class.java.simpleName

    //是否已经初始化
    private val isInitialized = AtomicBoolean(false)
    //眼动应用模式
    private val appliedMode = AtomicReference(NONE)

    private val gazeApplied = GazeApplied()

    //外部监听
    private var externalListener: IGazeAppliedListener? = null

    fun init(){
        if (!isInitialized.get()){
            gazeApplied.init()
            gazeApplied.setGazeAppliedListener(this@AppliedManager)
            isInitialized.set(true)
        }
    }

    /**
     * 设置外部监听
     */
    fun setGazeAppliedListener(listener: IGazeAppliedListener){
        externalListener = listener
    }

    fun getAppliedMode():AppliedMode{
        return appliedMode.get()
    }

    fun destroy(){
        Logger.d(TAG, msg = "destroy")
        stopApplied()
        gazeApplied.release()
        appliedMode.set(NONE)
        isInitialized.set(false)
        externalListener = null
    }

    /**
     * 启动眼动应用-治疗
     * @return 0 失败，1 成功，2 已启动
     */
    fun startAppliedCure():Int{
        if (appliedMode.get() == CURE) return 2
        init()
        if (gazeApplied.startApplied(CURE).also {
            Logger.d(TAG, msg = "startAppliedCure isSuccess = $it")
        }){
            setBlurParams(MaskManager.getCoverArea()?:4.0f,
                MaskManager.getCoverRange().ranger,
                MaskManager.getCoverMode().mode,
                MaskManager.getCoverChannel().channel)
            //打开遮盖疗法
            DeviceManager.saveMaskTherapyState(true)
            return 1
        }
        return 0
    }

    /**
     * 停止眼动应用-治疗
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopAppliedCure():Int{
        if (gazeApplied.stopApplied().also {
            Logger.d(TAG, msg = "stopAppliedCure isSuccess = $it")
        }){
            DeviceManager.saveMaskTherapyState(false)
            return 1
        }
        return 0
    }

    /**
     * 启动眼动应用-阅读
     * @return 0 失败，1 成功，2 已启动
     */
    fun startAppliedReading():Int{
        if (appliedMode.get() == READING) return 2
        init()
        return if (gazeApplied.startApplied(READING).also {
            Logger.d(TAG, msg = "startAppliedReading isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 停止眼动应用-阅读
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopAppliedReading():Int{
        return if (gazeApplied.stopApplied().also {
            Logger.d(TAG, msg = "stopAppliedReading isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 启动眼动应用-眼动能力检查-注视
     * @param x 注视点x坐标 [0~1]
     * @param y 注视点y坐标 [0~1]
     * @return 0 失败，1 成功，2 已启动
     */
    fun startAppliedStare(x:Float,y:Float):Int{
        if (appliedMode.get() == STARE) return 2
        init()
        if (gazeApplied.startApplied(STARE).also {
            Logger.d(TAG, msg = "startAppliedStare isSuccess = $it")
        }){
            setStarePoint(x,y)
            return 1
        }
        return 0
    }

    /**
     * 停止眼动应用-眼动能力检查-注视
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopAppliedStare():Int{
        return if (gazeApplied.stopApplied().also {
            Logger.d(TAG, msg = "stopAppliedStare isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 启动眼动应用-眼动能力检查-追随
     * @return 0 失败，1 成功，2 已启动
     */
    fun startAppliedFollow():Int{
        if (appliedMode.get() == FOLLOW) return 2
        init()
        return if (gazeApplied.startApplied(FOLLOW).also {
            Logger.d(TAG, msg = "startAppliedFollow isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 停止眼动应用-眼动能力检查-追随
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopAppliedFollow():Int{
        return if (gazeApplied.stopApplied().also {
            Logger.d(TAG, msg = "stopAppliedFollow isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 启动眼动应用-眼动能力检查-扫视
     * @return 0 失败，1 成功，2 已启动
     */
    fun startAppliedGlance():Int{
        if (appliedMode.get() == GLANCE) return 2
        init()
        return if (gazeApplied.startApplied(GLANCE).also {
            Logger.d(TAG, msg = "startAppliedGlance isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 停止眼动应用-眼动能力检查-扫视
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopAppliedGlance():Int{
        return if (gazeApplied.stopApplied().also {
            Logger.d(TAG, msg = "stopAppliedGlance isSuccess = $it")
        }) 1 else 0
    }

    /**
     * 停止视线追踪应用
     */
    fun stopApplied():Int{
        return when(appliedMode.get()){
            CURE -> stopAppliedCure()
            READING -> stopAppliedReading()
            STARE -> stopAppliedStare()
            FOLLOW -> stopAppliedFollow()
            GLANCE -> stopAppliedGlance()
            else -> 2
        }
    }

    /**
     * 设置屏幕虚化参数，[AppliedMode.CURE]模式下使用
     * @param blurRadius 遮盖的黄斑直径值，支持0.5~5.5mm
     * @param blurSigma 高斯模糊的sigma值
     * @param blurMode 虚化区域模式设置。
     * @param channel 虚化区域的通道对象。类型为int，1~7之间。默认为1，即虚化红色通道
     */
    fun setBlurParams(blurRadius: Float, blurSigma: Float, blurMode: Int, channel: Int){
        Logger.d(TAG, msg = "setBlurParams blurRadius = $blurRadius, blurSigma = $blurSigma, blurMode = $blurMode, channel = $channel")
        gazeApplied.setBlurParams(blurRadius, blurSigma, blurMode, channel)
    }

    /**
     * 设置注视的目标点，[AppliedMode.STARE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    fun setStarePoint(x: Float, y: Float){
        gazeApplied.setStarePoint(x, y)
    }

    /**
     * 设置扫视的目标点，[AppliedMode.GLANCE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    fun setGlancePoint(x: Float, y: Float){
        gazeApplied.setGlancePoint(x, y)
    }

    /**
     * 获取眼动轨迹数据结果
     */
    fun getGazeTrajectory(): String?{
        return gazeApplied.getGazeTrajectory(appliedMode.get())
    }

    /**
     * 分析追踪数据结果
     */
    fun analyseTrackResult(result: GazeTrackResult){
        Logger.d(TAG, msg = "sendGazeTrackResult result = $result, appliedMode = $appliedMode")
        if (appliedMode.get() == NONE) return
        if (result.checkResult()){
            collectGaze(result.valid, result.x, result.y, result.dist, result.duration.toFloat())
        }
    }

    /**
     * 将实时得到的眼动视标点传入native层
     * @param valid 镜头前是否检测到用户
     * @param x 视标点 - x [0~1]
     * @param y 视标点 - y [0~1]
     * @param dist 离屏幕距离 cm
     * @param duration 持续时间 ms
     */
    private fun collectGaze(valid: Boolean, x: Float, y: Float, dist: Float, duration: Float){
        val isFinish = gazeApplied.collectGaze(valid, x, y, dist, duration)
        if (appliedMode.get() == GLANCE){
            if (isFinish){
                externalListener?.onSaccadePointComplete()
            }
        }
    }

    override fun onGazeAppliedModeChange(appliedMode: AppliedMode) {
        this.appliedMode.set(appliedMode)
    }

}