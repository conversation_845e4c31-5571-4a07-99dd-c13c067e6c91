package com.mitdd.gazetracker.gaze.application

import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.gaze.enumeration.AppliedMode
import com.mitdd.gazetracker.gaze.listener.IGazeAppliedListener
import java.util.concurrent.atomic.AtomicLong


/**
 * FileName: GazeApplication
 * Author by lilin,Date on 2025/1/10 17:26
 * PS: Not easy to write code, please indicate.
 * 视线追踪应用
 */
class GazeApplied {

    companion object {

        private val TAG = GazeApplied::class.java.simpleName

        private const val NO_NATIVE_OBJ = 0L

        init {
            System.loadLibrary("GazeTracker")
        }
    }

    private var nativeObj = AtomicLong(NO_NATIVE_OBJ)

    private var internalListener: IGazeAppliedListener? = null

    fun init(){
        nativeObj.set(nativeCreateGazeApplied())
        nativeSetGazeAppliedCallback(nativeObj.get(), NativeGazeAppliedCallback())
    }

    /**
     * 释放
     */
    fun release() {
        nativeDestroyGazeApplied(nativeObj.get())
        nativeObj.set(NO_NATIVE_OBJ)
    }

    /**
     * 设置内部监听
     */
    fun setGazeAppliedListener(listener: IGazeAppliedListener?){
        internalListener = listener
    }

    /**
     * 启动视线追踪应用
     * @param appliedMode 应用模式
     */
    fun startApplied(appliedMode: AppliedMode):Boolean{
        return nativeStartApplication(nativeObj.get(), appliedMode.code)
    }

    /**
     * 停止视线追踪应用
     */
    fun stopApplied():Boolean{
        return nativeStopApplication(nativeObj.get())
    }

    /**
     * 设置屏幕虚化参数，[AppliedMode.CURE]模式下使用
     * @param blurRadius 遮盖的黄斑直径值，支持0.5~5.5mm
     * @param blurSigma 高斯模糊的sigma值
     * @param blurMode 虚化区域模式设置。
     * @param channel 虚化区域的通道对象。类型为int，1~7之间。默认为1，即虚化红色通道
     */
    fun setBlurParams(blurRadius: Float, blurSigma: Float, blurMode: Int, channel: Int): Boolean{
        return nativeSetBlurParams(nativeObj.get(), blurRadius, blurSigma, blurMode, channel)
    }

    /**
     * 设置注视的目标点，[AppliedMode.STARE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    fun setStarePoint(x: Float, y: Float): Boolean{
        return nativeSetStarePoint(nativeObj.get(), x, y)
    }

    /**
     * 设置扫视的目标点，[AppliedMode.GLANCE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    fun setGlancePoint(x: Float, y: Float): Boolean{
        return nativeSetGlancePoint(nativeObj.get(), x, y)
    }

    /**
     * 将实时得到的眼动视标点传入native层
     * @param valid 镜头前是否检测到用户
     * @param x 视标点 - x [0~1]
     * @param y 视标点 - y [0~1]
     * @param dist 离屏幕距离 cm
     * @param duration 持续时间 ms
     */
    fun collectGaze(valid: Boolean, x: Float, y: Float, dist: Float, duration: Float):Boolean{
        return nativeCollectGaze(nativeObj.get(), valid, x, y, dist, duration)
    }

    /**
     * 获取眼动轨迹数据结果
     * @param mode 应用模式
     */
    fun getGazeTrajectory(mode: AppliedMode): String?{
        return nativeGetGazeTrajectory(nativeObj.get(), mode.code)
    }

    /**
     * Native层眼动追踪服务回调
     */
    inner class NativeGazeAppliedCallback {

        /**
         * 当眼动应用模式改变时回调
         * @param mode 应用模式
         */
        fun onGazeAppliedModeChange(mode: Int){
            Logger.d(TAG, msg = "onGazeAppliedModeChange: mode = $mode")
            when (mode){
                AppliedMode.CURE.code -> {
                    internalListener?.onGazeAppliedModeChange(AppliedMode.CURE)
                }
                AppliedMode.READING.code -> {
                    internalListener?.onGazeAppliedModeChange(AppliedMode.READING)
                }
                AppliedMode.STARE.code -> {
                    internalListener?.onGazeAppliedModeChange(AppliedMode.STARE)
                }
                AppliedMode.FOLLOW.code -> {
                    internalListener?.onGazeAppliedModeChange(AppliedMode.FOLLOW)
                }
                AppliedMode.GLANCE.code -> {
                    internalListener?.onGazeAppliedModeChange(AppliedMode.GLANCE)
                }
                else ->{
                    internalListener?.onGazeAppliedModeChange(AppliedMode.NONE)
                }
            }
        }
    }

    //创建native层对象
    private external fun nativeCreateGazeApplied(): Long

    //设置眼动应用回调
    private external fun nativeSetGazeAppliedCallback(thiz: Long,callbackCalib: NativeGazeAppliedCallback)

    //启动视线追踪应用
    private external fun nativeStartApplication(thiz: Long, mode: Int): Boolean

    //停止视线追踪应用
    private external fun nativeStopApplication(thiz: Long): Boolean

    //设置屏幕虚化参数
    private external fun nativeSetBlurParams(
        thiz: Long,
        blurRadius: Float,
        blurSigma: Float,
        blurMode: Int,
        channel: Int
    ): Boolean

    // 设置注视的目标点
    private external fun nativeSetStarePoint(thiz: Long, x: Float, y: Float): Boolean

    // 设置扫视的目标点
    private external fun nativeSetGlancePoint(thiz: Long, x: Float, y: Float): Boolean

    //将实时得到的眼动视标点传入native层
    private external fun nativeCollectGaze(
        thiz: Long,
        valid: Boolean,
        x: Float,
        y: Float,
        dist: Float,
        duration: Float
    ): Boolean

    //获取眼动轨迹数据结果
    private external fun nativeGetGazeTrajectory(thiz: Long, mode: Int): String?

    //销毁native层对象
    private external fun nativeDestroyGazeApplied(thiz: Long)

}