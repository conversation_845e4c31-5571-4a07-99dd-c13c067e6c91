package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * FileName: CalibrateCoordinate
 * Author by lilin,Date on 2024/7/25 15:26
 * PS: Not easy to write code, please indicate.
 * 视标校准调整视标参数
 */
@Parcelize
data class CalibrateCoordinate(
    /**
     * 校准点的横坐标,（0～1）根据屏幕宽度计算具体值
     */
    val x:Float,

    /**
     * 校准点的纵坐标,（0～1）根据屏幕高度计算具体值
     */
    val y:Float,

    /**
     * 校准点的索引，从0到8，共9个点（从左到右，从上到下，分别是0,1,2,3,4,5,6,7,8）
     */
    val index:Int,

    /**
     * 判断校准过程是否完成，当为false，则继续显示图标和校准。当为true，则完成了校准。
     */
    val state:Boolean,

    /**
     * 判断校准结果是否成功，当为false，则校准失败。当为true，则成功完成校准。
     */
    val succeed:Boolean,

    /**
     * 评估校准结果的配合度，0~1的分数值。
     */
    val score:Float,

    /**
     * 评估左眼的9个校准点中每个的成功与否，2进制编码（使用时转成9位的二进制串,如101010101,从右往左依次为第一个点到第九个点的校准结果，1表示成功，0表示失败）
     */
    @SerializedName("calib_points_left") val calibLeft:Int,

    /**
     * 评估左眼的9个校准点中每个的成功与否，2进制编码（使用时转成9位的二进制串,如101010101,从右往左依次为第一个点到第九个点的校准结果，1表示成功，0表示失败）
     */
    @SerializedName("calib_points_right") val calibRight:Int
): Parcelable
