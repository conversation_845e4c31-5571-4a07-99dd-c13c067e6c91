package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * FileName: VisualCalibrationResult
 * Author by lilin,Date on 2024/8/9 14:29
 * PS: Not easy to write code, please indicate.
 * 视标校准结果
 */
@Parcelize
data class CalibrationResult(
    //校准是否完成
    @SerializedName("calib_finish") var calibFinish: Boolean = false,
    //表示左眼对当前点的校准进程 {0, 1, 2, 3}，当≥2的情况下，注视有效。
    @SerializedName("left_consist_num") var leftConsistNum: Int = -1,
    //表示右眼对当前点的校准进程 {0, 1, 2, 3}，当≥2的情况下，注视有效。
    @SerializedName("right_consist_num")var rightConsistNum: Int = -1
) : Parcelable
