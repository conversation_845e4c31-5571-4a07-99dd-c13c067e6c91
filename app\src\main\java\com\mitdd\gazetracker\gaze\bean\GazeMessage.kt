package com.mitdd.gazetracker.gaze.bean

/**
 * FileName: GazeMessage
 * Author by lilin,Date on 2025/1/16 15:54
 * PS: Not easy to write code, please indicate.
 * 主要用于WebSocket发送消息
 */
class GazeMessage<T> {
    // 消息类型
    var action:String = ""
    // 消息体
    var data:T? = null

    companion object{
        //姿势校准处理结果
        const val ACTION_POSTURE_CALIBRATION_RESULT = "ACTION_POSTURE_CALIBRATION_RESULT"
        //视标校准处理结果
        const val ACTION_CALIBRATION_RESULT = "ACTION_CALIBRATION_RESULT"
        //视标校准调整视标参数
        const val ACTION_CALIBRATE_COORDINATE = "ACTION_CALIBRATE_COORDINATE"
    }
}