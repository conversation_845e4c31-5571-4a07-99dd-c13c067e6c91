package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * FileName: GazeTrackResult
 * Author by lilin,Date on 2024/8/5 10:07
 * PS: Not easy to write code, please indicate.
 * 视线追踪单点结果
 */
@Parcelize
data class GazeTrackResult(
    // 镜头前是否检测到用户
    var valid:Boolean = false,
    // 姿势是否偏移 true-> 姿势发生偏转，false -> 姿势正常
    var skew:Boolean = false,
    // 视标点 - x [0~1]
    var x: Float = -1f,
    // 视标点 - y [0~1]
    var y: Float = -1f,
    //人眼离屏幕距离，正常数值在35~65之间。单位为cm
    var dist: Float = -1f,
    //视线在该视标点持续时间,毫秒
    var duration:Int = 0
) : Parcelable{
    fun checkResult():Bo<PERSON>an{
        return x in 0.0..1.0 && y in 0.0..1.0
    }
}