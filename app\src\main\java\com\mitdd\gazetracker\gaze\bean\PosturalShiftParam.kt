package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * FileName: PosturalShiftParam
 * Author by lilin,Date on 2024/7/25 10:20
 * PS: Not easy to write code, please indicate.
 * 姿势偏移参数
 */
@Parcelize
data class PosturalShiftParam(
    //右眼瞳孔中心x坐标（0～1）根据屏幕宽度计算具体值
    val rightX:Float,
    //右眼瞳孔中心y坐标（0～1）根据屏幕高度计算具体值
    val rightY:Float,
    //左眼瞳孔中心x坐标（0～1）根据屏幕宽度计算具体值
    val leftX:Float,
    //左眼瞳孔中心y坐标（0～1）根据屏幕高度计算具体值
    val leftY:Float,
    //区域半径值（0～1）。指示用户的眼睛需要对准的区域大小。比例按照屏幕的宽度计算得到。例如屏幕分辨率是1920x1080，值为0.1的情况下，半径大小为1920x0.1=192。
    val radius:Float
): Parcelable
