package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * FileName: PostureRecalibratingResult
 * Author by lilin,Date on 2024/7/25 16:03
 * PS: Not easy to write code, please indicate.
 * 姿势校准结果
 */
@Parcelize
data class PostureCalibrationResult(
    //姿势校准是否完成
    var state:Boolean = false,
    //当前姿势是否在正确位置上
    var aligned:Boolean = false,
    //眼睛离屏幕的距离。0~1之间，当处于0.45~0.55表示距离适中，当0.4~0.45之间为较近，0~0.4是很近。当0.55~0.6之间为较远，0.6~1.0是很远。
    var dist:Float = -1f,
    //倒计时。例如设定3秒，返回3，2，1，0
    var countdown:Int = -1,
    //右眼瞳孔中心x坐标（0～1）根据屏幕宽度计算具体值
    @SerializedName("right_x") var rightX:Float = -1f,
    //右眼瞳孔中心y坐标（0～1）根据屏幕高度计算具体值
    @SerializedName("right_y") var rightY:Float = -1f,
    //左眼瞳孔中心x坐标（0～1）根据屏幕宽度计算具体值
    @SerializedName("left_x") var leftX:Float = -1f,
    //左眼瞳孔中心y坐标（0～1）根据屏幕高度计算具体值
    @SerializedName("left_y") var leftY:Float = -1f,
): Parcelable{
    fun checkPostureCorrectionResult():Boolean{
        return leftX in 0.0..1.0 && leftY in 0.0..1.0 && rightX in 0.0..1.0 && rightY in 0.0..1.0
    }

}
