package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * FileName: ReadPointData
 * Author by lilin,Date on 2024/12/5 14:59
 * PS: Not easy to write code, please indicate.
 * 阅读视点数据
 */
@Parcelize
data class ReadPointData(
    //视点顺序，从1开始
    var index:Int? = null,
    //视点半径值等级，1/2/3
    var grade:Int? = null,
    //横坐标，像素值
    var x:Int? = null,
    //纵坐标，像素值
    var y:Int? = null
): Parcelable