package com.mitdd.gazetracker.gaze.calibration

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.FrameLayout.LayoutParams
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.airdoc.component.common.R as CommonR
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.bean.GazeMessage
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.gaze.vm.CalibrationViewModel
import com.mitdd.gazetracker.gaze.widget.PostureCalibrationView
import com.mitdd.gazetracker.gaze.widget.VisualCalibrationView
import com.mitdd.gazetracker.net.UrlConfig
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONObject
import java.net.URI

/**
 * FileName: CalibrationActivity
 * Author by lilin,Date on 2025/5/22 16:54
 * PS: Not easy to write code, please indicate.
 */
class CalibrationActivity : BaseCommonActivity() {

    companion object{
        private val TAG = CalibrationActivity::class.java.simpleName

        fun createIntent(context: Context,mode: CalibrationMode,isCorrection: Boolean): Intent {
            val intent = Intent(context, CalibrationActivity::class.java)
            intent.putExtra(GazeConstants.KEY_CALIBRATION_MODE,mode)
            intent.putExtra(GazeConstants.KEY_IS_CORRECTION,isCorrection)
            return intent
        }
    }

    private val flCalibrationRoot by id<FrameLayout>(R.id.fl_calibration_root)

    private val gson = Gson()

    private val mHandler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                sendMessageToService(Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                })
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(mHandler)
    private val mCalibrationWSClient = CalibrationWSClient(URI("ws://127.0.0.1:9200/Tracker"))

    private val calibrationVM by viewModels<CalibrationViewModel>()
    private var calibrationMode = CalibrationMode.CALIBRATION
    //视线追踪过程中进行校准时为true，普通校准时为false
    private var isCorrection = false
    //是否是关闭校准
    private var isCLoseCalibration = false

    //校准View
    private var calibrationView: View? = null

    @ExperimentalCamera2Interop
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_calibration)

        initParam()
        initView()
        initListener()
        initObserver()

        startForegroundService(Intent(this, GazeTrackService::class.java))

        lifecycleScope.launch {
            delay(2000)
            mCalibrationWSClient.connect()
        }
    }

    private fun initParam(){
        calibrationMode = (intent.getSerializableExtra(GazeConstants.KEY_CALIBRATION_MODE) as? CalibrationMode)?:CalibrationMode.CALIBRATION
        isCorrection = intent.getBooleanExtra(GazeConstants.KEY_IS_CORRECTION,false)
    }

    private fun initView(){
        lifecycleScope.launch {
            showPostureCalibration()
            delay(500)
            turnOnCamera()
            stopTrack()
            stopAppliedCure()
            startPostureCalibration()
        }
    }

    private fun initListener(){
        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                isCLoseCalibration = true
                when(calibrationView){
                    is PostureCalibrationView -> {
                        stopPostureCalibration()
                    }
                    is VisualCalibrationView -> {
                        stopVisualCalibration()
                    }
                }
            }
        })
    }

    private fun initObserver(){
        calibrationVM.postureCalibrationResultLivedata.observe(this){
            if (it.state && !isCLoseCalibration){
                Logger.d(TAG, msg = "postureCalibrationResultLivedata result = $it")
                when(calibrationMode){
                    CalibrationMode.CALIBRATION -> {
                        lifecycleScope.launch {
                            showVisualCalibration()
                            delay(200)
                            startVisualCalibration()
                        }
                    }
                    CalibrationMode.POSTURE ->{
                        lifecycleScope.launch {
                            if (isCorrection){
                                startTrack()
                                startAppliedCure()
                            }else{
                                turnOffCamera()
                            }
                            delay(100)
                            finish()
                        }
                    }
                }
            }
            (calibrationView as? PostureCalibrationView)?.setPostureCorrectionResult(it)
        }
        calibrationVM.calibrationResultLivedata.observe(this){
            (calibrationView as? VisualCalibrationView)?.setCalibrationResult(it)
        }
        calibrationVM.calibrateCoordinateLivedata.observe(this){
            (calibrationView as? VisualCalibrationView)?.setCalibrateCoordinate(it)
        }
    }

    /**
     * 显示姿势校准
     */
    private fun showPostureCalibration(){
        flCalibrationRoot.removeAllViews()
        if (isCorrection){
            flCalibrationRoot.setBackgroundColor(ContextCompat.getColor(this, CommonR.color.black_70))
        }else{
            flCalibrationRoot.setBackgroundColor("#191443".toColorInt())
        }
        calibrationView = PostureCalibrationView(this).apply {
            this.calibrationType = if (isCorrection){
                PostureCalibrationView.TYPE_POSTURE_CORRECTION
            }else{
                PostureCalibrationView.TYPE_POSTURE_CALIBRATION
            }
            setPostureCorrectionParam(PostureCalibrationView.DEFAULT_REFERENCE_X, PostureCalibrationView.DEFAULT_REFERENCE_Y)
            this.onCloseCalibrationClick = {
                Logger.d(TAG, msg = "PostureCalibrationView onCloseCalibrationClick")
                isCLoseCalibration = true
                stopPostureCalibration()
            }
            this.onCalibrationClick = {
                Logger.d(TAG, msg = "PostureCalibrationView onCalibrationClick")
                calibrationMode = CalibrationMode.CALIBRATION
                flCalibrationRoot.setBackgroundColor("#191443".toColorInt())
                this.updateType(PostureCalibrationView.TYPE_POSTURE_CALIBRATION)
            }
        }
        flCalibrationRoot.addView(calibrationView,LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.MATCH_PARENT))
    }

    /**
     * 显示视标校准
     */
    private fun showVisualCalibration(){
        flCalibrationRoot.removeAllViews()
        flCalibrationRoot.setBackgroundColor("#191443".toColorInt())
        calibrationView = VisualCalibrationView(this).apply {
            this.onCalibrationComplete = {
                Logger.d(TAG, msg = "VisualCalibrationView onCalibrationComplete")
                lifecycleScope.launch {
                    delay(100)
                    if (isCorrection){
                        startTrack()
                        startAppliedCure()
                    }else{
                        turnOffCamera()
                    }
                    startUploadCloud()
                    delay(200)
                    calibrationComplete(it)
                }
            }
            this.onNotifyVisualReady = {
                Logger.d(TAG, msg = "VisualCalibrationView onNotifyVisualReady")
                notifyVisualReady(it)
            }
        }
        flCalibrationRoot.addView(calibrationView, LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
    }

    /**
     * 开始姿势校准
     */
    private fun startPostureCalibration(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_START_POSTURE_CALIBRATION
            data.putBoolean(GazeConstants.KEY_IS_CORRECTION,isCorrection)
        })
    }

    //停止姿势校准
    private fun stopPostureCalibration(){
        lifecycleScope.launch {
            sendMessageToService(Message.obtain().apply {
                what = GazeConstants.MSG_STOP_POSTURE_CALIBRATION
                data.putBoolean(GazeConstants.KEY_IS_CORRECTION,isCorrection)
            })
            delay(100)
            if (isCorrection){
                startTrack()
                startAppliedCure()
            }else{
                turnOffCamera()
                startUploadCloud()
            }
            delay(100)
            finish()
        }
    }

    /**
     * 开始视标校准
     */
    private fun startVisualCalibration(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_START_VISUAL_CALIBRATION
        })
    }

    /**
     * 停止视标校准
     */
    private fun stopVisualCalibration(){
        lifecycleScope.launch {
            sendMessageToService(Message.obtain().apply {
                what = GazeConstants.MSG_STOP_VISUAL_CALIBRATION
            })
            delay(100)
            turnOffCamera()
            startUploadCloud()
            delay(100)
            finish()
        }
    }

    /**
     * 通知视标准备好
     */
    private fun notifyVisualReady(isReady:Boolean){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_VISUAL_READY
            data.putBoolean(GazeConstants.KEY_VISUAL_READY,isReady)
        })
    }

    /**
     * 校准完成
     */
    private fun calibrationComplete(isSuccess:Boolean){
        val intent = Intent()
        intent.putExtra(GazeConstants.KEY_IS_SUCCESS,isSuccess)
        setResult(RESULT_OK,intent)
        finish()
    }

    /**
     * 启动追踪
     */
    private fun startTrack(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_START_TRACK
        })
    }

    /**
     * 停止追踪
     */
    private fun stopTrack(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_STOP_TRACK
        })
    }

    /**
     * 停止遮盖疗法
     */
    private fun startAppliedCure(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_START_APPLIED_CURE
        })
    }

    /**
     * 停止遮盖疗法
     */
    private fun stopAppliedCure(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_STOP_APPLIED_CURE
        })
    }

    /**
     * 上报校准数据
     */
    private fun startUploadCloud(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_START_UPLOAD_CLOUD
        })
    }

    /**
     * 打开相机
     */
    private fun turnOnCamera(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_TURN_ON_CAMERA
        })
    }

    /**
     * 关闭相机
     */
    private fun turnOffCamera(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_TURN_OFF_CAMERA
        })
    }

    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        unbindService(serviceConnection)
    }

    override fun onDestroy() {
        mCalibrationWSClient.close()
        super.onDestroy()
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

    /**
     * ws://127.0.0.1:9200/Tracker
     */
    inner class CalibrationWSClient(serverUri: URI): WebSocketClient(serverUri){

        override fun onOpen(handshakedata: ServerHandshake?) {
            Logger.d(TAG, msg = "onOpen")
        }

        override fun onMessage(message: String?) {
            if (message != null && !TextUtils.isEmpty(message)){
                lifecycleScope.launch {
                    val jsonObject = JSONObject(message)
                    try {
                        val action = jsonObject.opt("action")
                        when(action){
                            GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT -> {
                                val type = object : TypeToken<GazeMessage<PostureCalibrationResult>?>() {}.type
                                val gazeMessage = gson.fromJson<GazeMessage<PostureCalibrationResult>>(message,type)
                                calibrationVM.setPostureCalibrationResult(gazeMessage.data)
                            }
                            GazeMessage.ACTION_CALIBRATION_RESULT -> {
                                val type = object : TypeToken<GazeMessage<CalibrationResult>?>() {}.type
                                val gazeMessage = gson.fromJson<GazeMessage<CalibrationResult>>(message,type)
                                calibrationVM.setCalibrationResult(gazeMessage.data)
                            }
                            GazeMessage.ACTION_CALIBRATE_COORDINATE -> {
                                val type = object : TypeToken<GazeMessage<CalibrateCoordinate>?>() {}.type
                                val gazeMessage = gson.fromJson<GazeMessage<CalibrateCoordinate>>(message,type)
                                calibrationVM.setCalibrateCoordinate(gazeMessage.data)
                            }
                        }
                    }catch (e:Exception){
                        Logger.e(TAG, msg = "onMessage Exception $e")
                        if (BuildConfig.DEBUG){
                            e.printStackTrace()
                        }
                    }
                }
            }
        }

        override fun onClose(code: Int, reason: String?, remote: Boolean) {
            Logger.d(TAG, msg = "onClose code = $code, reason = $reason, remote = $remote")
        }

        override fun onError(ex: Exception?) {
            Logger.e(TAG, msg = "onError ex = $ex")
        }

        override fun connect() {
            Logger.d(TAG, msg ="connect")
            try {
                super.connect()
            }catch (e:Exception){
                Logger.e(TAG, msg ="connect Exception ${e.message}")
                if (BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            }
        }

        override fun reconnect() {
            Logger.d(TAG, msg ="reconnect")
            try {
                super.reconnect()
            }catch (e:Exception){
                Logger.e(TAG, msg ="reconnect Exception ${e.message}")
                if (BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            }
        }

        override fun close(code: Int) {
            Logger.d(TAG, msg ="close code $code")
            try {
                super.close(code)
            }catch (e:Exception){
                Logger.e(TAG, msg ="close Exception ${e.message}")
                if (BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            }
        }

    }

}