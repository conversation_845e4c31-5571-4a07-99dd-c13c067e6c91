package com.mitdd.gazetracker.gaze.calibration

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import kotlinx.coroutines.Runnable

/**
 * FileName: CalibrationFailureDialog
 * Author by lilin,Date on 2024/12/6 11:26
 * PS: Not easy to write code, please indicate.
 */
class CalibrationFailureDialog(context: Context) : BaseCommonDialog(context) {

    private val playerView by id<PlayerView>(R.id.player_view)
    private val ivPlayControlPlay by id<ImageView>(R.id.iv_play_control_play)
    private val tvCancelCalibration by id<TextView>(R.id.tv_cancel_calibration)
    private val tvRecalibration by id<TextView>(R.id.tv_recalibration)

    private var mUrl = "https://img3.airdoc.com/md20/static/20241206104610/EyeTrackingCalibrationTutorial.mp4"

    var onRecalibration:(() -> Unit)? = null

    private var exoPlayer: ExoPlayer? = null
    private var hidePlayControlR = Runnable {
        ivPlayControlPlay.isVisible = false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_calibration_failure)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()

        initListener()

        initData()
    }

    private fun initView() {
        exoPlayer = ExoPlayer.Builder(context).build()
        playerView.player = exoPlayer
        ivPlayControlPlay.isVisible = false
    }

    private fun initListener() {
        tvCancelCalibration.setOnSingleClickListener {
            dismiss()
        }
        tvRecalibration.setOnSingleClickListener {
            onRecalibration?.invoke()
            dismiss()
        }
        ivPlayControlPlay.setOnSingleClickListener {
            playerView.removeCallbacks(hidePlayControlR)
            exoPlayer?.let {
                if (it.isPlaying){
                    it.pause()
                    ivPlayControlPlay.isSelected = false
                }else{
                    it.playWhenReady = true
                    ivPlayControlPlay.isSelected = true
                }
            }
            playerView.postDelayed(hidePlayControlR,2000)
        }
        playerView.setOnSingleClickListener {
            playerView.removeCallbacks(hidePlayControlR)
            ivPlayControlPlay.isVisible = true
            playerView.postDelayed(hidePlayControlR,2000)
        }
        exoPlayer?.addListener(object : Player.Listener{

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                ivPlayControlPlay.isSelected = isPlaying
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                ivPlayControlPlay.isSelected = false
            }

        })
        setOnDismissListener {
            exoPlayer?.stop()
            exoPlayer?.release()
        }
    }

    private fun initData() {
        play()
    }

    private fun play(){
        val mediaItem = MediaItem.fromUri(Uri.parse(mUrl))
        exoPlayer?.setMediaItem(mediaItem)
        exoPlayer?.prepare()
        exoPlayer?.playWhenReady = true
    }

}