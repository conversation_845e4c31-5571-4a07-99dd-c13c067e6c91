package com.mitdd.gazetracker.gaze.camera

import android.content.Context
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.GazeTrackingManager
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: GTCameraManager
 * Author by lilin,Date on 2025/4/27 10:01
 * PS: Not easy to write code, please indicate.
 * 视线追踪相机管理
 */
object GTCameraManager {

    private val TAG = GTCameraManager::class.java.simpleName

    //补光灯开关操作节点
    private const val FILL_LIGHT_LAMP = "/sys/class/C3535_class/C3535_dev/C3535"

    //图像分析配置
    private val resolutionStrategy = ResolutionSelector.Builder()
        .setResolutionStrategy(
            ResolutionStrategy(
                Size(GazeConstants.IMAGE_WIDTH, GazeConstants.IMAGE_HEIGHT),
                ResolutionStrategy.FALLBACK_RULE_NONE)
        )
        .setAllowedResolutionMode(ResolutionSelector.PREFER_HIGHER_RESOLUTION_OVER_CAPTURE_RATE)
        .build()

    //相机是否已启动
    private val isCameraStarted = AtomicBoolean(false)

    private var cameraListener:ICameraListener? = null

    fun setCameraListener(listener:ICameraListener?){
        cameraListener = listener
    }

    /**
     * 启动相机
     */
    fun startCamera(context: Context,lifecycleOwner: LifecycleOwner){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startCamera isCameraStarted = ${isCameraStarted.get()}")
        if (isCameraStarted.get()) return
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            try {
                cameraProvider.unbindAll()
                val imageAnalysis = ImageAnalysis.Builder()
                    .setResolutionSelector(resolutionStrategy)
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build().apply {
                        setAnalyzer(ContextCompat.getMainExecutor(context)) { image ->
                            cameraListener?.onAnalyze(image)
                        }
                    }
                cameraProvider.bindToLifecycle(lifecycleOwner, cameraSelector, imageAnalysis)
                isCameraStarted.set(true)
                //打开补光灯
                setFillLightLampState(true)
                cameraListener?.onCameraStatusChange(true)
            } catch (e: Exception) {
                // 处理相机绑定错误
                Logger.e(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startCamera Exception e = ${e.message}")
                stopCamera(context)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * 停止相机
     */
    fun stopCamera(context: Context){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopCamera isCameraStarted = ${isCameraStarted.get()}")
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()
            try {
                // 解除所有已绑定的用例
                cameraProvider.unbindAll()
            }finally {
                //关闭补光灯
                setFillLightLampState(false)
                isCameraStarted.set(false)
                cameraListener?.onCameraStatusChange(false)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * 设置补光灯状态
     * @param isOn true 表示开，false 表示关
     */
    private fun setFillLightLampState(isOn: Boolean) {
        val data = if (isOn) "1" else "0"
        try {
            val fops = FileOutputStream(FILL_LIGHT_LAMP)
            fops.write(data.toByteArray())
            fops.flush()
            fops.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 获取补光灯状态
     * @return true 表示开，false 表示关
     */
    private fun getFillLightLampState(): Boolean {
        val file = File(FILL_LIGHT_LAMP)
        try {
            // 读取文件内容
            val reader = BufferedReader(FileReader(file))
            val line = reader.readLine()
            reader.close()
            // 解析布尔值
            return line.toBoolean()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return false
    }

}