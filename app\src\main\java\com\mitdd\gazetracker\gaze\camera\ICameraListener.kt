package com.mitdd.gazetracker.gaze.camera

import androidx.camera.core.ImageProxy

/**
 * FileName: ICameraListener
 * Author by lilin,Date on 2025/5/23 15:23
 * PS: Not easy to write code, please indicate.
 */
interface ICameraListener {

    /**
     * 相机状态改变
     * @param isOn true:打开相机，false:关闭相机
     */
    fun onCameraStatusChange(isOn:Boolean){}

    /**
     * 识别图片
     * @param image 图片信息
     */
    fun onAnalyze(image: ImageProxy){}
}