package com.mitdd.gazetracker.gaze.enumeration

/**
 * FileName: PostureException
 * Author by lilin,Date on 2025/5/7 14:28
 * PS: Not easy to write code, please indicate.
 * 姿势异常
 * @param degree 异常程度 从小到大 0：绿 ,1：黄,2：红
 */
enum class PostureException(var degree:Int) {
    //远
    FAR(0),
    //近
    NEARLY(0),
    //左
    LEFT(0),
    //右
    RIGHT(0),
    //上
    UP(0),
    //下
    DOWN(0),
    //倾斜
    SKEW(0)
}