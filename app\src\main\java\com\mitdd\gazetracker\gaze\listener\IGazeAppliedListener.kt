package com.mitdd.gazetracker.gaze.listener

import com.mitdd.gazetracker.gaze.enumeration.AppliedMode

/**
 * FileName: IGazeAppliedListener
 * Author by lilin,Date on 2025/2/5 15:48
 * PS: Not easy to write code, please indicate.
 */
interface IGazeAppliedListener {

    /**
     * 当眼动应用模式改变时回调
     * @param appliedMode 服务模式
     */
    fun onGazeAppliedModeChange(appliedMode: AppliedMode){}

    /**
     * 当设置的扫视点扫视完成时回调
     */
    fun onSaccadePointComplete(){}

}