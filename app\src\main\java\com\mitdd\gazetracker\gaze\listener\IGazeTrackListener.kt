package com.mitdd.gazetracker.gaze.listener

import com.mitdd.gazetracker.gaze.GazeError
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult
import com.mitdd.gazetracker.gaze.bean.GazeTrackResult
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.enumeration.ServiceMode

/**
 * FileName: IGazeTrackListener
 * Author by lilin,Date on 2024/7/25 10:15
 * PS: Not easy to write code, please indicate.
 */
interface IGazeTrackListener {

    /**
     * 视线跟踪处理函数回调
     * @param gazeTrackResult 处理结果
     */
    fun onGazeTracking(gazeTrackResult: GazeTrackResult){}

    /**
     * 姿势校准处理结果回调
     * @param result 处理结果
     */
    fun onPostureCalibration(result: PostureCalibrationResult){}

    /**
     * 视标校准处理函数回调
     */
    fun onCalibrating(result: CalibrationResult){}

    /**
     * 校准点以及校准结果回调函数
     */
    fun onCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate){}

    /**
     * 当眼动追踪服务模式改变时回调
     * @param serviceMode 服务模式
     */
    fun onGazeServiceModeChange(serviceMode: ServiceMode){}

    /**
     * 发送异常时回调
     * @param error 异常
     */
    fun onError(error: GazeError){}
}