package com.mitdd.gazetracker.gaze.listener

import com.mitdd.gazetracker.gaze.GazeError

/**
 * FileName: IServiceStopListener
 * Author by lilin,Date on 2025/1/15 17:14
 * PS: Not easy to write code, please indicate.
 * 停止眼动服务时回调
 */
interface IStopListener {
    /**
     * 停止完成时回调
     */
    fun onStopDone(){}

    /**
     * 发送异常时回调
     * @param error 异常
     */
    fun onError(error: GazeError){}
}