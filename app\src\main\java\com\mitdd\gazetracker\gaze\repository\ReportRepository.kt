package com.mitdd.gazetracker.gaze.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.api.ReportApiService
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.gaze.upload.ReportRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: ReportRepository
 * Author by lilin,Date on 2025/2/14 16:15
 * PS: Not easy to write code, please indicate.
 */
class ReportRepository : BaseRepository() {

    /**
     * 上报遮盖疗法时间
     * @param dynamicUrl 动态url
     * @param params 参数
     * @param headers 请求头
     */
    suspend fun reportCureResult(dynamicUrl: String,params:HashMap<String,Any>,headers: HashMap<String, Any>): ApiResponse<CureInfo> {
        return executeHttp {
            val json = gson.toJson(params)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            ReportRetrofitClient.createService(ReportApiService::class.java).reportCureResult(dynamicUrl,requestBody,headers)
        }
    }

}