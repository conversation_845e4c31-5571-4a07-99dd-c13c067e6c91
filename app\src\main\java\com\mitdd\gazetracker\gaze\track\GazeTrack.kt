package com.mitdd.gazetracker.gaze.track

import androidx.camera.core.ImageProxy
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult
import com.mitdd.gazetracker.gaze.bean.GazeTrackResult
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.enumeration.ServiceMode
import com.mitdd.gazetracker.gaze.listener.IGazeTrackListener
import com.mitdd.gazetracker.utils.GTUtils
import java.util.concurrent.atomic.AtomicLong

/**
 * FileName: GazeTrack
 * Author by lilin,Date on 2024/7/23 14:08
 * PS: Not easy to write code, please indicate.
 * 视线追踪
 */
class GazeTrack {

    companion object {

        private val TAG = GazeTrack::class.java.simpleName

        private const val NO_NATIVE_OBJ = 0L

        init {
            System.loadLibrary("GazeTracker")
        }
    }

    private var nativeObj = AtomicLong(NO_NATIVE_OBJ)

    private var internalListener: IGazeTrackListener? = null

    /**
     * 实例初始化，耗时操作
     * @param configDir 配置文件路径
     * @param sn 设备序列号
     * @return true 表示初始化成功
     */
    fun init(configDir: String,sn:String){
        nativeObj.set(nativeCreateObject(configDir,sn))
        //设置眼动追踪回调
        nativeSetGazeTrackCallback(nativeObj.get(),NativeGazeTrackCallback())
    }

    /**
     * 释放
     */
    fun release() {
        nativeDestroyObject(nativeObj.get())
        nativeObj.set(NO_NATIVE_OBJ)
    }

    /**
     * 设置内部监听
     */
    fun setGazeTrackListener(listener: IGazeTrackListener?){
        internalListener = listener
    }

    /**
     * 开始视线追踪
     */
    fun startTracking():Boolean {
        return nativeStartTracking(nativeObj.get())
    }

    /**
     * 停止视线追踪
     */
    fun stopTracking():Boolean {
        return nativeStopTracking(nativeObj.get())
    }

    /**
     * 视线跟踪过程
     * @param image  图像数据
     */
    fun gazeTracking(image: ImageProxy): GazeTrackResult {
        val imageBytes = GTUtils.getDataFromImageY(image)
        val result = nativeGazeTracking(nativeObj.get(), imageBytes,image.width,image.height)
        return GazeTrackResult().apply {
            valid = (result?.get("valid") as? Boolean)?:false
            skew = (result?.get("skew") as? Boolean)?:false
            x = (result?.get("gaze_x") as? Float)?:-1f
            y = (result?.get("gaze_y") as? Float)?:-1f
            dist = (result?.get("dist") as? Float)?:-1f
            duration = (result?.get("duration") as? Int)?:0
        }
    }

    /**
     * 开始姿势校准
     * @param isCorrection 是否是姿势重校准
     */
    fun startPostureCalibration(isCorrection:Boolean):Boolean {
        return if (isCorrection){
            nativeStartPostureCorrection(nativeObj.get())
        }else{
            nativeStartPostureCalibration(nativeObj.get())
        }
    }

    /**
     * 中断姿势校准,正常完成结束不需要调用
     */
    fun stopPostureCalibration():Boolean {
        return nativeStopPostureCalibration(nativeObj.get())
    }

    /**
     * 姿势矫正处理函数
     * @param image  图像数据
     */
    fun postureCorrection(image: ImageProxy): PostureCalibrationResult {
        val imageBytes = GTUtils.getDataFromImageY(image)
        val result = nativePostureCorrection(nativeObj.get(), imageBytes, image.width, image.height)
        return PostureCalibrationResult().apply {
            state = (result?.get("state") as? Boolean)?:false
            aligned = (result?.get("aligned") as? Boolean)?:false
            dist = (result?.get("dist") as? Float)?:0f
            countdown = (result?.get("countdown") as? Int)?:-1
            rightX = (result?.get("right_x") as? Float)?:-1f
            rightY = (result?.get("right_y") as? Float)?:-1f
            leftX = (result?.get("left_x") as? Float)?:-1f
            leftY = (result?.get("left_y") as? Float)?:-1f
        }
    }

    /**
     * 开始视标校准
     * @return true 表示开始成功
     */
    fun startVisualCalibration():Boolean {
        return nativeStartCalibration(nativeObj.get())
    }

    /**
     * 停止视标校准，正常完成结束不需要调用
     * @return true 表示停止成功
     */
    fun stopVisualCalibration():Boolean {
        return nativeStopCalibration(nativeObj.get())
    }

    /**
     * 视标校准处理函数
     * @param image  图像数据
     */
    fun calibrating(image: ImageProxy) : CalibrationResult {
        val imageBytes = GTUtils.getDataFromImageY(image)
        val result = nativeCalibrating(nativeObj.get(), imageBytes,image.width,image.height)
        return CalibrationResult().apply {
            calibFinish = (result?.get("calib_finish") as? Boolean)?:false
            leftConsistNum = (result?.get("left_consist_num") as? Int)?:0
            rightConsistNum = (result?.get("right_consist_num") as? Int)?:0
        }
    }

    /**
     * 检查校准参数是否加载正常
     * @return true 表示正常
     */
    fun checkCalibrationParam():Boolean{
        val isAvailable = nativeGetCalibState(nativeObj.get())
        return isAvailable
    }

    /**
     * 跟新眼动参数，类实例从本地文件读取最新的校准参数信息
     */
    fun updateParams() {
        //类对象加载参数文件
        nativeUpdateGazeParams(nativeObj.get())
    }

    /**
     * Native层眼动追踪服务回调
     */
    inner class NativeGazeTrackCallback {

        /**
         * 校准点以及校准结果回调函数
         * @param x 校准点的横坐标,（0～1）根据屏幕宽度计算具体值
         * @param y 校准点的纵坐标,（0～1）根据屏幕高度计算具体值
         * @param state 判断校准过程是否完成，当为false，则继续显示图标和校准。当为true，则完成了校准。
         * @param succeed 判断校准结果是否成功，当为false，则校准失败。当为true，则成功完成校准。
         * @param score 评估校准结果的配合度，0~1的分数值。
         * @param calibLeft 评估左眼的9个校准点中每个的成功与否，2进制编码（使用时转成9位的二进制串,如101010101,从右往左依次为第一个点到第九个点的校准结果，1表示成功，0表示失败）
         * @param calibRight 评估左眼的9个校准点中每个的成功与否，2进制编码（使用时转成9位的二进制串,如101010101,从右往左依次为第一个点到第九个点的校准结果，1表示成功，0表示失败）
         * @param index 校准点的索引，从0到8，共9个点（从左到右，从上到下，分别是0,1,2,3,4,5,6,7,8）
         */
        fun onCalibrateCoordinate(state: Boolean, succeed: Boolean, score: Float, calibLeft: Int, calibRight: Int, x: Float, y: Float, index: Int) {
            // 处理完成后的回调函数
            Logger.d(TAG, msg = "onCalibrateCoordinate: state = $state, succeed = $succeed, score = $score, left = $calibLeft, right = $calibRight, x = $x, y = $y, index = $index")
            internalListener?.onCalibrateCoordinate(CalibrateCoordinate(x, y, index, state, succeed, score, calibLeft, calibRight))
        }

        /**
         * 当眼动追踪服务模式改变时回调
         * @param mode 服务模式
         */
        fun onGazeServiceModeChange(mode: Int){
            Logger.d(TAG, msg = "onGazeServiceModeChange: mode = $mode")
            when (mode){
                ServiceMode.TRACK.code -> {
                    internalListener?.onGazeServiceModeChange(ServiceMode.TRACK)
                }
                ServiceMode.POSTURE_CALIBRATION.code -> {
                    internalListener?.onGazeServiceModeChange(ServiceMode.POSTURE_CALIBRATION)
                }
                ServiceMode.VISUAL_CALIBRATION.code -> {
                    internalListener?.onGazeServiceModeChange(ServiceMode.VISUAL_CALIBRATION)
                }
                else ->{
                    internalListener?.onGazeServiceModeChange(ServiceMode.NONE)
                }
            }
        }
    }

    /**
     * 初始化视线追踪服务，返回 native对象句柄
     */
    private external fun nativeCreateObject(configDir: String, snSerial: String): Long

    /**
     * 启动视线追踪
     */
    private external fun nativeStartTracking(thiz: Long): Boolean

    /**
     * 停止视线追踪
     */
    private external fun nativeStopTracking(thiz: Long): Boolean

    /**
     * 跟新眼动参数，类实例从本地文件读取最新的校准参数信息
     */
    private external fun nativeUpdateGazeParams(thiz: Long)

    /**
     * 读取是否成功加载校准参数
     */
    private external fun nativeGetCalibState(thiz: Long): Boolean

    /**
     * 视线跟踪处理函数
     */
    private external fun nativeGazeTracking(
        thiz: Long,
        inputImage: ByteArray,
        width: Int,
        height: Int
    ): HashMap<String?, Any?>?

    /**
     * 开始姿势校准
     */
    private external fun nativeStartPostureCalibration(thiz: Long): Boolean

    /**
     * 开始姿势矫正（使用眼动过程中的姿势偏差导致的）
     */
    private external fun nativeStartPostureCorrection(thiz: Long): Boolean

    /**
     * 停止姿势校准
     */
    private external fun nativeStopPostureCalibration(thiz: Long): Boolean

    /**
     * 姿势矫正处理函数
     */
    private external fun nativePostureCorrection(
        thiz: Long,
        inputImage: ByteArray,
        width: Int,
        height: Int
    ): HashMap<String?, Any?>?

    /**
     * 开始眼动校准
     */
    private external fun nativeStartCalibration(thiz: Long): Boolean

    /**
     * 停止眼动校准
     */
    private external fun nativeStopCalibration(thiz: Long): Boolean

    /**
     * 设置眼动追踪回调
     */
    private external fun nativeSetGazeTrackCallback(thiz: Long,callbackCalib: NativeGazeTrackCallback)

    /**
     * 眼动校准处理函数
     */
    private external fun nativeCalibrating(
        thiz: Long,
        inputImage: ByteArray,
        width: Int,
        height: Int
    ): HashMap<String?, Any?>?

    /**
     * 销毁native对象
     */
    private external fun nativeDestroyObject(thiz: Long)
}
