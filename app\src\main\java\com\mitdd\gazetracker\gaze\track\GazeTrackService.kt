package com.mitdd.gazetracker.gaze.track

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.camera.core.ImageProxy
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.GTApplication
import com.mitdd.gazetracker.gaze.GazeTrackingManager
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.ServiceId
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.bean.GazeTrackResult
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeError
import com.mitdd.gazetracker.gaze.camera.GTCameraManager
import com.mitdd.gazetracker.gaze.application.AppliedManager
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.bean.GazeMessage
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.camera.ICameraListener
import com.mitdd.gazetracker.gaze.enumeration.AppliedMode
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.gaze.listener.IGazeAppliedListener
import com.mitdd.gazetracker.gaze.listener.IGazeTrackListener
import com.mitdd.gazetracker.gaze.upload.ReportManager
import com.mitdd.gazetracker.gaze.upload.UploadCloudHolder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.jessyan.autosize.AutoSizeCompat
import java.net.InetAddress
import java.net.InetSocketAddress

/**
 * FileName: GazeTrackService
 * Author by lilin,Date on 2024/7/23 14:08
 * PS: Not easy to write code, please indicate.
 * 用于执行视线追踪的服务
 */
open class GazeTrackService : Service(), LifecycleOwner, CoroutineScope by MainScope(),
    ICameraListener, IGazeTrackListener, IGazeAppliedListener {

    companion object{
        private val TAG = GazeTrackService::class.java.simpleName
    }

    private lateinit var lifecycleRegistry:LifecycleRegistry

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    private val mGson = Gson()

    private lateinit var mHandler: LifecycleHandler
    private lateinit var mServiceMessage: Messenger
    private var mClientMessage:Messenger? = null

    //屏幕宽高
    private var mScreenWidth = 0
    private var mScreenHeight = 0

    //WebSocket服务,用于对外发送眼动数据
    private var mGazeWebSocketService: GazeWebSocketService? = null

    //是否显示注视点
    private var isDisplayViewpoint = GazeTrackingManager.getDisplayViewpoint()

    //遮盖疗法已治疗时长，单位秒
    private var mTreatmentDuration = 0
    //遮盖疗法计划治疗时长，单位秒
    private var mPlannedDuration = 0
    //弱视眼位置 左left 右right
    private var mEyePosition = MaskManager.getAmblyopicEye()
    //记录遮盖疗法时长
    private var mRecordDurationTreatmentJob:Job? = null
    //治疗开始时间
    private var mCureStartTime = 0L

    override fun onCreate() {
        super.onCreate()
        Logger.d(TAG, msg = "onCreate")
        lifecycleRegistry = LifecycleRegistry(this)
        lifecycleRegistry.currentState = Lifecycle.State.CREATED

        AutoSizeCompat.autoConvertDensityOfGlobal(resources)

        mHandler = object : LifecycleHandler(Looper.getMainLooper(),this){
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                parseMessage(msg)
            }
        }
        mServiceMessage = Messenger(mHandler)

        mScreenWidth = ScreenUtil.getScreenWidth(this)
        mScreenHeight = ScreenUtil.getScreenHeight(this)

        //启动前台服务
        startForegroundService()
        //启动WebSocket服务
        startWebSocketServer()

        initListener()
        //初始化观察者
        initObserver()
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        Logger.d(TAG, msg = "onStartCommand")
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
        lifecycleRegistry.currentState = Lifecycle.State.RESUMED
        return START_REDELIVER_INTENT
    }

    override fun onBind(intent: Intent?): IBinder {
        Logger.d(TAG, msg = "onBind")
        return mServiceMessage.binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Logger.d(TAG, msg = "onUnbind")
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        Logger.d(TAG, msg = "onDestroy")
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        //关闭眼动应用
        AppliedManager.destroy()
        //关闭眼动服务
        TrackingManager.destroy()
        //关闭相机
        GTCameraManager.stopCamera(this)
        //关闭WebSocket服务
        stopWebSocketServer()
        super.onDestroy()
    }

    private fun initListener(){
        GTCameraManager.setCameraListener(this)
        TrackingManager.setGazeTrackListener(this)
        TrackingManager.setLifecycleOwner(this)
        AppliedManager.setGazeAppliedListener(this)
    }

    /**
     * 初始化观察者
     */
    private fun initObserver(){
        LiveEventBus.get<Boolean>(GTApplication.EVENT_APP_FOREGROUND_STATE).observe(this){
            Logger.d(TAG, msg = "EVENT_APP_FOREGROUND_STATE : $it")
            val maskTherapyState = DeviceManager.getMaskTherapyState()
            if (!it && maskTherapyState){
                WidgetManager.showTreatmentProgress(this,mTreatmentDuration,mPlannedDuration,mEyePosition)
            }else{
                WidgetManager.removeTreatmentProgress()
            }
        }
        LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_DISPLAY_VIEWPOINT).observe(this){
            Logger.d(TAG, msg = "EVENT_SWITCH_DISPLAY_VIEWPOINT : $it")
            isDisplayViewpoint = it
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val channelId = "com.mitdd.gazetracker"
        val channelName = "GazeTrackerService"
        (getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager).createNotificationChannel(
            NotificationChannel(channelId,channelName, NotificationManager.IMPORTANCE_NONE)
                .apply {
                    lockscreenVisibility = Notification.VISIBILITY_SECRET
                }
        )
        startForeground(ServiceId.GAZE_TRACKER_SERVICE_ID, Notification.Builder(this,channelId).build())
    }

    /**
     * 处理消息，包括进程间消息、服务间消息
     */
    open fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
        when(msg.what){
            GazeConstants.MSG_SERVICE_CONNECTED ->{
                mClientMessage = msg.replyTo
            }
            GazeConstants.MSG_TURN_ON_CAMERA ->{
                GTCameraManager.startCamera(this,this)
            }
            GazeConstants.MSG_TURN_OFF_CAMERA ->{
                GTCameraManager.stopCamera(this)
            }
            GazeConstants.MSG_START_TRACK ->{
                startGazeTrack()
            }
            GazeConstants.MSG_STOP_TRACK ->{
                stopGazeTrack()
            }
            GazeConstants.MSG_START_VISUAL_CALIBRATION ->{
                startVisualCalibration()
            }
            GazeConstants.MSG_STOP_VISUAL_CALIBRATION ->{
                stopVisualCalibration()
            }
            GazeConstants.MSG_START_POSTURE_CALIBRATION ->{
                val isCorrection = msg.data.getBoolean(GazeConstants.KEY_IS_CORRECTION)
                startPostureCalibration(isCorrection)
            }
            GazeConstants.MSG_STOP_POSTURE_CALIBRATION ->{
                val isCorrection = msg.data.getBoolean(GazeConstants.KEY_IS_CORRECTION)
                stopPostureCalibration(isCorrection)
            }
            GazeConstants.MSG_VISUAL_READY ->{
                val isReady = msg.data.getBoolean(GazeConstants.KEY_VISUAL_READY)
                TrackingManager.setVisualReady(isReady)
            }
            GazeConstants.MSG_STOP_APPLIED ->{
                stopApplied()
            }
            GazeConstants.MSG_START_APPLIED_CURE ->{
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_PARAM)){
                    try {
                        val params = msg.data.getString(GazeConstants.KEY_REPORT_PARAM).orEmpty()
                        val type = object : TypeToken<HashMap<String, Any>>() {}.type
                        ReportManager.setReportParams(mGson.fromJson<HashMap<String, Any>>(params, type))
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_HEADER)){
                    try {
                        val headers = msg.data.getString(GazeConstants.KEY_REPORT_HEADER).orEmpty()
                        val type = object : TypeToken<HashMap<String, Any>>() {}.type
                        ReportManager.setReportHeaders(mGson.fromJson<HashMap<String, Any>>(headers, type))
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_URL)){
                    ReportManager.setReportUrl(msg.data.getString(GazeConstants.KEY_REPORT_URL).orEmpty())
                }
                if (msg.data.containsKey(GazeConstants.KEY_BASE_URL)){
                    ReportManager.setBaseUrl(msg.data.getString(GazeConstants.KEY_BASE_URL).orEmpty())
                }
                val plannedDuration = if (msg.data.containsKey(GazeConstants.KEY_PLANNED_DURATION)){
                    msg.data.getInt(GazeConstants.KEY_PLANNED_DURATION)
                }else{
                    mPlannedDuration
                }
                val treatmentDuration = if (msg.data.containsKey(GazeConstants.KEY_TREATMENT_DURATION)){
                    msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)
                }else{
                    mTreatmentDuration
                }
                startAppliedCure(plannedDuration, treatmentDuration)
            }
            GazeConstants.MSG_STOP_APPLIED_CURE ->{
                stopAppliedCure()
            }
            GazeConstants.MSG_START_APPLIED_READING ->{
                startAppliedReading()
            }
            GazeConstants.MSG_STOP_APPLIED_READING ->{
                stopAppliedReading()
            }
            GazeConstants.MSG_START_APPLIED_STARE ->{
                val x = msg.data.getFloat(GazeConstants.KEY_X)
                val y = msg.data.getFloat(GazeConstants.KEY_Y)
                startAppliedStare(x,y)
            }
            GazeConstants.MSG_STOP_APPLIED_STARE ->{
                stopAppliedStare()
            }
            GazeConstants.MSG_START_APPLIED_FOLLOW ->{
                startAppliedFollow()
            }
            GazeConstants.MSG_STOP_APPLIED_FOLLOW ->{
                stopAppliedFollow()
            }
            GazeConstants.MSG_START_APPLIED_GLANCE ->{
                startAppliedGlance()
            }
            GazeConstants.MSG_STOP_APPLIED_GLANCE ->{
                stopAppliedGlance()
            }
            GazeConstants.MSG_GET_GAZE_TRAJECTORY ->{
                getGazeTrajectory()
            }
            GazeConstants.MSG_SET_GLANCE_POINT ->{
                val x = msg.data.getFloat(GazeConstants.KEY_X)
                val y = msg.data.getFloat(GazeConstants.KEY_Y)
                setGlancePoint(x,y)
            }
            GazeConstants.MSG_START_UPLOAD_CLOUD ->{
                startUploadCloud()
            }
        }
    }

    /**
     * 启动视线追踪
     */
    private fun startGazeTrack(){
        TrackingManager.startTracking(this).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startGazeTrack isSuccess = $it")
            when(it){
                0 ->{ // 失败
                    if (!TrackingManager.checkCalibrationParam()){
                        Toast.makeText(this,getString(R.string.str_invalid_parameters_required_calibration),Toast.LENGTH_LONG).show()
                    }
                }
                1 ->{ // 成功
                    sendMessageToClient(Message.obtain().apply {
                        what = GazeConstants.MSG_GAZE_TRACKING_STATE
                        data.putBoolean(GazeConstants.KEY_STATE,true)
                    })
                }
            }
        }
    }

    /**
     * 停止视线追踪
     */
    private fun stopGazeTrack(){
        TrackingManager.stopTracking().also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopGazeTrack isSuccess = $it")
            WidgetManager.removeDotView()
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_GAZE_TRACKING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动视标校准
     */
    private fun startVisualCalibration(){
        TrackingManager.startVisualCalibration(this).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startCalibration isSuccess = $it")
        }
    }

    /**
     * 停止视标校准
     */
    private fun stopVisualCalibration(){
        TrackingManager.stopVisualCalibration().also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopVisualCalibration isSuccess = $it")
        }
    }

    /**
     * 开始姿势校准
     * @param  isCorrection true表示姿势矫正(视线追踪过程中的姿势校准)，false表示普通姿势校准
     */
    private fun startPostureCalibration(isCorrection:Boolean){
        TrackingManager.startPostureCalibration(this,isCorrection).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startPostureCalibration isSuccess = $it, isCorrection = $isCorrection")
        }
    }

    /**
     * 中断姿势校准
     * @param  isCorrection true表示姿势矫正(视线追踪过程中的姿势校准)，false表示普通姿势校
     */
    private fun stopPostureCalibration(isCorrection:Boolean){
        TrackingManager.stopPostureCalibration().also {
            Logger.d(TAG, msg = "stopPostureCalibration isSuccess = $it, isCorrection = $isCorrection")
        }
    }

    /**
     * 上报校准数据
     */
    private fun startUploadCloud(){
        launch(Dispatchers.IO) {
            // 上报数据到云
            UploadCloudHolder.startUploadCloud()
        }
    }

    /**
     * 停止视线追踪应用
     */
    private fun stopApplied(){
        when(AppliedManager.getAppliedMode()){
            AppliedMode.CURE -> stopAppliedCure()
            AppliedMode.READING -> stopAppliedReading()
            AppliedMode.STARE -> stopAppliedStare()
            AppliedMode.FOLLOW -> stopAppliedFollow()
            AppliedMode.GLANCE -> stopAppliedGlance()
            else -> {
            }
        }
    }

    /**
     * 启动眼动应用-治疗
     * @param plannedDuration  计划训练时长，单位秒
     * @param treatmentDuration 已治疗训练时长，单位秒
     */
    private fun startAppliedCure(plannedDuration:Int,treatmentDuration:Int){
        mTreatmentDuration = maxOf(0, treatmentDuration)
        mPlannedDuration = if (plannedDuration <= mTreatmentDuration) mTreatmentDuration + 5400 else plannedDuration
        Logger.d(TAG, msg = "startAppliedCure plannedDuration = $mPlannedDuration, treatmentDuration = $mTreatmentDuration")
        AppliedManager.startAppliedCure().also {
            Logger.d(TAG, msg = "startAppliedCure isSuccess = $it")
            if (it == 1){
                startTreatmentTimer()
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_CURE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-治疗
     */
    private fun stopAppliedCure(){
        Logger.d(TAG, msg = "stopAppliedCure")
        AppliedManager.stopAppliedCure().also {
            Logger.d(TAG, msg = "stopAppliedCure isSuccess = $it")
            if (it == 1){
                WidgetManager.removeTreatmentProgress()
                stopTreatmentTimer()
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_CURE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-阅读
     */
    private fun startAppliedReading(){
        Logger.d(TAG, msg = "startAppliedReading")
        AppliedManager.startAppliedReading().also {
            Logger.d(TAG, msg = "startAppliedReading isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_READING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-阅读
     */
    private fun stopAppliedReading(){
        Logger.d(TAG, msg = "stopAppliedReading")
        AppliedManager.stopAppliedReading().also {
            Logger.d(TAG, msg = "stopAppliedReading isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_READING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-注视
     * @param x 注视点x坐标 [0~1]
     * @param y 注视点y坐标 [0~1]
     */
    private fun startAppliedStare(x:Float,y:Float){
        Logger.d(TAG, msg = "startAppliedStare x = $x, y = $y")
        AppliedManager.startAppliedStare(x,y).also {
            Logger.d(TAG, msg = "startAppliedStare isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_STARE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-注视
     */
    private fun stopAppliedStare(){
        Logger.d(TAG, msg = "stopAppliedStare")
        AppliedManager.stopAppliedStare().also {
            Logger.d(TAG, msg = "stopAppliedStare isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_STARE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-追随
     */
    private fun startAppliedFollow(){
        Logger.d(TAG, msg = "startAppliedFollow")
        AppliedManager.startAppliedFollow().also {
            Logger.d(TAG, msg = "startAppliedFollow isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_FOLLOW_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-追随
     */
    private fun stopAppliedFollow(){
        Logger.d(TAG, msg = "stopAppliedFollow")
        AppliedManager.stopAppliedFollow().also {
            Logger.d(TAG, msg = "stopAppliedFollow isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_FOLLOW_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-扫视
     */
    private fun startAppliedGlance(){
        Logger.d(TAG, msg = "startAppliedGlance")
        AppliedManager.startAppliedGlance().also {
            Logger.d(TAG, msg = "startAppliedGlance isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_GLANCE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-扫视
     */
    private fun stopAppliedGlance(){
        Logger.d(TAG, msg = "stopAppliedGlance")
        AppliedManager.stopAppliedGlance().also {
            Logger.d(TAG, msg = "stopAppliedGlance isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_GLANCE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 获取眼动轨迹数据结果
     */
    private fun getGazeTrajectory(){
        val gazeTrajectory = AppliedManager.getGazeTrajectory()
        Logger.d(TAG, msg = "getGazeTrajectory = $gazeTrajectory")
        sendMessageToClient(Message.obtain().apply {
            what = GazeConstants.MSG_GAZE_TRAJECTORY_RESULT
            data.putString(GazeConstants.KEY_GAZE_TRAJECTORY,gazeTrajectory)
        })
    }

    /**
     * 设置扫视的目标点，[AppliedMode.GLANCE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    private fun setGlancePoint(x: Float, y: Float){
        AppliedManager.setGlancePoint(x,y)
    }

    /**
     * 发送消息到客户端
     */
    private fun sendMessageToClient(msg: Message){
        mClientMessage?.send(msg)
    }

    /**
     * 开启WebSocketServer
     */
    private fun startWebSocketServer(){
        Log.d(TAG,"startWebSocketServer")
        lifecycleScope.launch(Dispatchers.Default) {
            val localHost = withContext(Dispatchers.IO) {
                InetAddress.getLocalHost()
            }
            val inetSocketAddress = InetSocketAddress(localHost, 9200)
            mGazeWebSocketService = GazeWebSocketService(inetSocketAddress).apply {
                isReuseAddr = true
            }
            mGazeWebSocketService?.start()
            Logger.d(TAG, msg = "startWebSocketServer hostname = $localHost -- port = 9200")
        }

    }

    /**
     * 停止WebSocketServer
     */
    private fun stopWebSocketServer(){
        mGazeWebSocketService?.stop()
        mGazeWebSocketService = null
    }

    /**
     * 开始治疗计时
     */
    private fun startTreatmentTimer(){
        //60秒记录一次
        val interval = 60
        stopTreatmentTimer()
        val maskTherapyState = DeviceManager.getMaskTherapyState()
        if (maskTherapyState){
            mCureStartTime = System.currentTimeMillis()
            mRecordDurationTreatmentJob = lifecycleScope.launch(Dispatchers.Default) {
                while(true){
                    delay(interval.toLong() * 1000)
                    withContext(Dispatchers.Main){
                        mTreatmentDuration += 60
                        WidgetManager.updateTreatmentProgress(mTreatmentDuration,mPlannedDuration)
                        //发送治疗时长给客户端
                        sendMessageToClient(Message.obtain().apply {
                            what = GazeConstants.MSG_UPDATE_TREATMENT_DURATION
                            data.putInt(GazeConstants.KEY_TREATMENT_DURATION,mTreatmentDuration)
                        })
                        if (mTreatmentDuration >= mPlannedDuration){
                            //治疗结束，关闭服务
                            stopAppliedCure()
                            stopGazeTrack()
                        }
                    }
                }
            }.apply {
                invokeOnCompletion {
                    Logger.d(TAG, msg = "invokeOnCompletion")
                    val duration = (System.currentTimeMillis() - mCureStartTime) / 1000
                    onStopTreatmentTimer(duration.toInt())
                }
            }
        }
    }

    /**
     * 停止治疗计时
     */
    private fun stopTreatmentTimer(){
        mRecordDurationTreatmentJob?.cancel()
        mRecordDurationTreatmentJob = null
    }

    open fun onStopTreatmentTimer(duration: Int){
        ReportManager.reportCureResult(duration)
    }

    //---------------Begin: for ICameraListener ----------
    override fun onCameraStatusChange(isOn: Boolean) {
//        if (isOn){
//            TrackingManager.startImageAnalysis(this)
//        }else{
//            TrackingManager.stopImageAnalysis()
//        }
    }

    override fun onAnalyze(image: ImageProxy) {
        TrackingManager.sendImageProxy(image)
    }
    //---------------End: for ICameraListener ----------

    //---------------Begin: for IGazeTrackListener ----------
    override fun onGazeTracking(gazeTrackResult: GazeTrackResult) {
        val appliedMode = AppliedManager.getAppliedMode()
        if (gazeTrackResult.skew && appliedMode == AppliedMode.CURE){
            if (TrackingManager.isSkewing.get()) return
            //姿势偏移
            val intent = CalibrationActivity.createIntent(this@GazeTrackService, CalibrationMode.POSTURE,true)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
        }else{
            if (isDisplayViewpoint){
                WidgetManager.showDotView(this@GazeTrackService,gazeTrackResult,mScreenWidth,mScreenHeight)
            }else{
                WidgetManager.removeDotView()
            }
            //发布眼动数据到WebSocket
            val resultJson = mGson.toJson(gazeTrackResult)
            mGazeWebSocketService?.broadcast(resultJson)
            //发布眼动数据到应用
            AppliedManager.analyseTrackResult(gazeTrackResult)
        }
    }

    override fun onPostureCalibration(result: PostureCalibrationResult) {
        val gazeMessage = GazeMessage<PostureCalibrationResult>().apply {
            action = GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT
            data = result
        }
        mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
    }

    override fun onCalibrating(result: CalibrationResult) {
        val gazeMessage = GazeMessage<CalibrationResult>().apply {
            action = GazeMessage.ACTION_CALIBRATION_RESULT
            data = result
        }
        mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
    }

    override fun onCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate) {
        lifecycleScope.launch {
            val gazeMessage = GazeMessage<CalibrateCoordinate>().apply {
                action = GazeMessage.ACTION_CALIBRATE_COORDINATE
                data = calibrateCoordinate
            }
            mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
        }
    }

    override fun onError(error: GazeError) {
        lifecycleScope.launch {
            handleError(error)
        }
    }
    //---------------End: for IGazeTrackListener ----------

    //---------------Begin: for IGazeAppliedListener ----------

    override fun onSaccadePointComplete() {
        Logger.d(TAG, msg = "onSaccadePointComplete")
        sendMessageToClient(Message.obtain().apply {
            what = GazeConstants.MSG_SACCADE_POINT_COMPLETE
        })
    }

    //---------------End: for IGazeAppliedListener ----------

    private fun handleError(error: GazeError){
        Logger.e(TAG, msg = "handleError code = ${error.code}, msg = ${error.msg}")
        if (!TextUtils.isEmpty(error.msg)){
            Toast.makeText(this@GazeTrackService,error.msg,Toast.LENGTH_SHORT).show()
        }
    }

}