package com.mitdd.gazetracker.gaze.track

import com.airdoc.component.common.log.Logger
import org.java_websocket.WebSocket
import org.java_websocket.handshake.ClientHandshake
import org.java_websocket.server.WebSocketServer
import java.net.InetSocketAddress

/**
 * FileName: GazeWebSocketService
 * Author by lilin,Date on 2025/5/23 15:01
 * PS: Not easy to write code, please indicate.
 */
class GazeWebSocketService(address: InetSocketAddress) : WebSocketServer(address) {

    private val TAG = GazeWebSocketService::class.java.simpleName

    override fun onOpen(conn: WebSocket?, handshake: ClientHandshake?) {
        Logger.d(TAG, msg = "onOpen conn = $conn")
    }

    override fun onClose(conn: WebSocket?, code: Int, reason: String?, remote: Boolean) {
        Logger.d(TAG, msg = "onClose conn = $conn, code = $code, reason = $reason, remote = $remote")
    }

    override fun onMessage(conn: WebSocket?, message: String?) {
        Logger.d(TAG, msg = "onMessage message = $message")
    }

    override fun onError(conn: WebSocket?, ex: java.lang.Exception?) {
        Logger.d(TAG, msg = "onError ex = ${ex?.message}")
    }

    override fun onStart() {
        Logger.d(TAG, msg = "WebSocketService onStart")
    }

    override fun start() {
        try {
            super.start()
        }catch (e:IllegalStateException){
            e.printStackTrace()
        }

    }
    override fun stop() {
        try {
            super.stop()
        }catch (e: InterruptedException){
            e.printStackTrace()
        }
    }
}