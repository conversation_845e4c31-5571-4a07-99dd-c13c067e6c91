package com.mitdd.gazetracker.gaze.track

import android.content.Context
import androidx.camera.core.ImageProxy
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.GazeTrackingManager
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.enumeration.ServiceMode
import com.mitdd.gazetracker.gaze.enumeration.ServiceMode.*
import com.mitdd.gazetracker.gaze.listener.IGazeTrackListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * FileName: GazeTrackingHolder
 * Author by lilin,Date on 2024/12/7 15:35
 * PS: Not easy to write code, please indicate.
 * 视线追踪持有者
 */
object TrackingManager : IGazeTrackListener {

    private val TAG = TrackingManager::class.java.simpleName

    //是否已经初始化
    private val isInitialized = AtomicBoolean(false)
    //视标校准时视标是否准备好
    private val isVisualReady = AtomicBoolean(true)
    //视线追踪过程中进行校准时为true，普通校准时为false
    private val isCorrection = AtomicBoolean(false)
    //眼动服务模式
    private val serviceMode = AtomicReference(NONE)

    private val gazeTrack = GazeTrack()

    //姿势是否正在偏移
    val isSkewing = AtomicBoolean(false)

    //生命周期管理
    private var mLifecycleOwner:LifecycleOwner? = null
    //外部监听
    private var externalListener: IGazeTrackListener? = null

    /**
     * 初始化
     * @param context 上下文
     */
    fun init(context: Context){
        if (!isInitialized.get()){
            val absolutePath = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE).absolutePath
            gazeTrack.init(absolutePath, DeviceManager.getDeviceSn())
            isInitialized.set(true)
            gazeTrack.setGazeTrackListener(this@TrackingManager)
        }
    }

    /**
     * 设置生命周期管理
     */
    fun setLifecycleOwner(owner: LifecycleOwner?){
        mLifecycleOwner = owner
    }

    /**
     * 设置外部监听
     */
    fun setGazeTrackListener(listener: IGazeTrackListener){
        externalListener = listener
    }

    fun getServiceMode():ServiceMode{
        return serviceMode.get()
    }

    fun destroy(){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "destroy")
        when(serviceMode.get()){
            TRACK -> stopTracking()
            POSTURE_CALIBRATION -> stopPostureCalibration()
            VISUAL_CALIBRATION -> stopVisualCalibration()
            else -> {}
        }
        gazeTrack.release()
        serviceMode.set(NONE)
        isInitialized.set(false)
        externalListener = null
    }

    /**
     * 检查校准参数是否加载正常
     * @return true 表示正常
     */
    fun checkCalibrationParam():Boolean{
        return gazeTrack.checkCalibrationParam()
    }

    /**
     * 开始视线追踪
     * @return 0 失败，1 成功，2 已启动
     */
    fun startTracking(context: Context):Int{
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startTracking serviceMode = ${serviceMode.get()}")
        if (serviceMode.get() == TRACK) return 2
        init(context)
        return if (gazeTrack.startTracking()) 1 else 0
    }

    /**
     * 停止视线追踪
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopTracking():Int {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopTracking serviceMode = ${serviceMode.get()}")
        return if (gazeTrack.stopTracking()) 1 else 0
    }

    /**
     * 开始视标校准
     * @return 0 失败，1 成功，2 已启动
     */
    fun startVisualCalibration(context: Context):Int {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startVisualCalibration serviceMode = ${serviceMode.get()}")
        if (serviceMode.get() == VISUAL_CALIBRATION) return 2
        init(context)
        return if (gazeTrack.startVisualCalibration()) 1 else 0
    }

    /**
     * 停止视标校准,正常完成不需要调用
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopVisualCalibration():Int {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopVisualCalibration serviceMode = ${serviceMode.get()}")
        return if (gazeTrack.stopVisualCalibration()) 1 else 0
    }

    /**
     * 开始姿势校准
     * @param isCorrection true 视线追踪过程中的姿势校准，false表示普通姿势校准
     * @return 0 失败，1 成功，2 已启动
     */
    fun startPostureCalibration(context: Context,isCorrection:Boolean):Int {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startPostureCalibration serviceMode = ${serviceMode.get()},isCorrection = $isCorrection")
        if (serviceMode.get() == POSTURE_CALIBRATION) return 2
        init(context)
        this.isCorrection.set(isCorrection)
        return if (gazeTrack.startPostureCalibration(isCorrection)) 1 else 0
    }

    /**
     * 中断姿势校准，正常结束不需要调用
     * @return 0 失败，1 成功，2 已停止
     */
    fun stopPostureCalibration():Int {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopPostureCalibration serviceMode = ${serviceMode.get()}")
        return if (gazeTrack.stopPostureCalibration()) 1 else 0
    }

    /**
     * 发送ImageProxy到渠道
     */
    fun sendImageProxy(image: ImageProxy){
        when(serviceMode.get()){
            TRACK -> gazeTracking(image)
            POSTURE_CALIBRATION -> postureCalibration(image)
            VISUAL_CALIBRATION -> calibrating(image)
            else ->{
                image.close()
            }
        }
    }

    private fun gazeTracking(image: ImageProxy){
        mLifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
            val result = gazeTrack.gazeTracking(image)
            withContext(Dispatchers.Main){
                externalListener?.onGazeTracking(result)
                isSkewing.set(result.skew)
                image.close()
            }
        }
    }

    private fun calibrating(image: ImageProxy){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "calibrating isVisualReady = $isVisualReady")
        if (!isVisualReady.get()){
            image.close()
        }else{
            mLifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
                val result = gazeTrack.calibrating(image)
                withContext(Dispatchers.Main){
                    externalListener?.onCalibrating(result)
                    image.close()
                }
            }
        }
    }

    private fun postureCalibration(image: ImageProxy){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "postureCalibration")
        mLifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
            val result = gazeTrack.postureCorrection(image)
            withContext(Dispatchers.Main){
                externalListener?.onPostureCalibration(result)
                image.close()
            }
        }
    }

    fun setVisualReady(isReady:Boolean){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "setVisualReady isReady = $isReady")
        isVisualReady.set(isReady)
    }

    override fun onCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate) {
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "onCalibrateCoordinate state = $calibrateCoordinate")
        if (calibrateCoordinate.state){
            setVisualReady(true)
        }else{
            setVisualReady(false)
        }
        externalListener?.onCalibrateCoordinate(calibrateCoordinate)
    }

    override fun onGazeServiceModeChange(serviceMode: ServiceMode) {
        this.serviceMode.set(serviceMode)
    }

}