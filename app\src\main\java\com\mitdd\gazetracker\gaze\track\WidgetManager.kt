package com.mitdd.gazetracker.gaze.track

import android.content.Context
import com.mitdd.gazetracker.gaze.bean.GazeTrackResult
import com.mitdd.gazetracker.gaze.widget.DotView
import com.mitdd.gazetracker.gaze.widget.TreatmentProgressView
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye

/**
 * FileName: WidgetHolder
 * Author by lilin,Date on 2024/12/13 16:51
 * PS: Not easy to write code, please indicate.
 */
object WidgetManager {

    //治疗进度view
    private var treatmentProgressView: TreatmentProgressView? = null
    //注视点view
    private var dotView: DotView? = null

    /**
     * 显示遮盖疗法治疗进度
     * @param context
     * @param treatmentDuration 当前治疗时长
     * @param plannedDuration  计划治疗时长
     * @param eyePosition  弱视眼位置
     */
    fun showTreatmentProgress(context: Context,treatmentDuration:Int,plannedDuration:Int,eyePosition: AmblyopicEye){
        treatmentProgressView = TreatmentProgressView(context).apply {
            show()
            setEyePosition(eyePosition.value)
            setDuration(treatmentDuration,plannedDuration)
        }
    }

    /**
     * 移除遮盖疗法治疗进度
     */
    fun removeTreatmentProgress(){
        treatmentProgressView?.remove()
        treatmentProgressView = null
    }

    /**
     * 更新遮盖疗法治疗进度
     * @param treatmentDuration 当前治疗时长
     * @param plannedDuration  计划治疗时长
     */
    fun updateTreatmentProgress(treatmentDuration:Int,plannedDuration:Int){
        treatmentProgressView?.setDuration(treatmentDuration,plannedDuration)
    }

    /**
     * 显示视点
     */
    fun showDotView(context: Context, result: GazeTrackResult, screenWidth:Int, screenHeight:Int){
        if (result.checkResult()){
            if (dotView == null){
                dotView = DotView(context).apply {
                    show((result.x * screenWidth - 10).toInt(),(result.y * screenHeight - 10).toInt())
                }
            }else{
                dotView?.update((result.x * screenWidth - 10).toInt(),(result.y * screenHeight - 10).toInt())
            }
        }else{
            removeDotView()
        }
    }

    /**
     * 移除视点
     */
    fun removeDotView(){
        dotView?.remove()
        dotView = null
    }

}