package com.mitdd.gazetracker.gaze.upload

import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.repository.ReportRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: ReportManager
 * Author by lilin,Date on 2025/6/6 14:51
 * PS: Not easy to write code, please indicate.
 * 数据上报管理
 */
object ReportManager {

    private val TAG = ReportManager::class.java.simpleName

    //公共协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private val gson = Gson()
    //网络请求
    private val reportRepository by lazy { ReportRepository() }
    private var baseUrl = ""
    //用于上报治疗结果的Url
    private var reportUrl = ""
    //上报参数
    private var reportParams:HashMap<String,Any>? = null
    //上报接口header
    private var headers = HashMap<String, Any>()

    fun setBaseUrl(url: String){
        baseUrl = url
    }

    fun getBaseUrl(): String {
        return baseUrl
    }

    fun setReportUrl(url: String){
        reportUrl = url
    }

    fun setReportParams(params: HashMap<String,Any>){
        reportParams = params
    }

    fun setReportHeaders(headers: HashMap<String,Any>){
        this.headers = headers
    }

    /**
     * 上报遮盖疗法时间
     * @param duration 遮盖疗法时间
     */
    fun reportCureResult(duration: Int){
        Logger.d(TAG, msg = "reportCureResult duration = $duration")
        if (duration <= 0 || reportUrl.isBlank() || baseUrl.isBlank()) return
        coroutineScope.launch {
            val params = HashMap<String, Any>()
            reportParams?.forEach { (key, value) ->
                params[key] = value
            }
            params["duration"] = duration
            MutableStateFlow(reportRepository.reportCureResult(reportUrl,params,headers)).collectResponse{
                onSuccess = { it,_,_->
                    Logger.d(TAG, msg = "reportCureResult onSuccess it = $it")
                }
                onDataEmpty = { _,_->
                    Logger.d(TAG, msg = "reportCureResult onDataEmpty")
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "reportCureResult onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                }
                onError = {
                    Logger.e(TAG, msg = "reportCureResult onError = $it")
                }
            }
        }
    }

}