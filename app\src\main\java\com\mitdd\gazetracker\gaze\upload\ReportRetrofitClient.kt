package com.mitdd.gazetracker.gaze.upload

import android.annotation.SuppressLint
import com.airdoc.component.common.net.base.BaseRetrofitClient
import okhttp3.OkHttpClient
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.X509TrustManager

/**
 * FileName: ReportRetrofitClient
 * Author by lilin,Date on 2025/6/6 15:15
 * PS: Not easy to write code, please indicate.
 */
object ReportRetrofitClient : BaseRetrofitClient() {

    private val trustAllCerts = arrayOf<X509TrustManager>(@SuppressLint("CustomX509TrustManager")
    object : X509TrustManager {
        @SuppressLint("TrustAllX509TrustManager")
        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
            // 允许所有客户端证书
        }

        @SuppressLint("TrustAllX509TrustManager")
        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
            // 允许所有服务端证书
        }

        override fun getAcceptedIssuers(): Array<X509Certificate> {
            return emptyArray()
        }
    })

    private val sslContext = SSLContext.getInstance("SSL").apply {
        init(null, trustAllCerts, SecureRandom())
    }
    private val sslSocketFactory = sslContext.socketFactory

    override fun fetchBaseUrl(): String {
        return "${ReportManager.getBaseUrl()}/"
    }

    override fun handleBuilder(builder: OkHttpClient.Builder) {
        builder.connectTimeout(10L, TimeUnit.SECONDS)
        builder.readTimeout(15L, TimeUnit.SECONDS)
        builder.writeTimeout(15L, TimeUnit.SECONDS)
        builder.sslSocketFactory(sslSocketFactory, trustAllCerts[0])
        builder.hostnameVerifier(HostnameVerifier { _, _ -> true })
    }

}