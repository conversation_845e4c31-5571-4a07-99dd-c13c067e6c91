package com.mitdd.gazetracker.gaze.upload

import java.util.concurrent.atomic.AtomicLong

/**
 * FileName: UploadCloud
 * Author by lilin,Date on 2024/10/11 15:19
 * PS: Not easy to write code, please indicate.
 * 数据上传
 */
class UploadCloud {

    companion object {

        private val TAG = UploadCloud::class.java.simpleName

        private const val NO_NATIVE_OBJ = 0L

        init {
            System.loadLibrary("GazeTracker")
        }
    }

    private var nativeObj = AtomicLong(NO_NATIVE_OBJ)

    init {
        val createObject = nativeCreateObject()
        nativeObj.set(createObject)
    }

    /**
     * 开始上传
     */
    fun startUploadCloud(){
        nativeStartUploadCloud(nativeObj.get())
    }

    /**
     * 释放
     */
    fun release() {
        nativeDestroyObject(nativeObj.get())
        nativeObj.set(NO_NATIVE_OBJ)
    }

    private external fun nativeCreateObject(): Long

    private external fun nativeStartUploadCloud(thiz: Long)

    private external fun nativeDestroyObject(thiz: <PERSON>)

}