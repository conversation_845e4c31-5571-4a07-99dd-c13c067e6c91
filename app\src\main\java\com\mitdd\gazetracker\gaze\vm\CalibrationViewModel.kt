package com.mitdd.gazetracker.gaze.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult

/**
 * FileName: CalibrationViewModel
 * Author by lilin,Date on 2025/2/20 14:58
 * PS: Not easy to write code, please indicate.
 */
class CalibrationViewModel : ViewModel() {

    //姿势校准结果
    val postureCalibrationResultLivedata = MutableLiveData<PostureCalibrationResult>()
    //视标校准结果
    val calibrationResultLivedata = MutableLiveData<CalibrationResult>()
    //视标校准调整视标参数
    val calibrateCoordinateLivedata = MutableLiveData<CalibrateCoordinate>()

    /**
     * 设置姿势校准结果
     */
    fun setPostureCalibrationResult(result: PostureCalibrationResult?){
        result?.let {
            postureCalibrationResultLivedata.postValue(it)
        }
    }

    /**
     * 设置视标校准结果
     */
    fun setCalibrationResult(result: CalibrationResult?){
        result?.let {
            calibrationResultLivedata.postValue(it)
        }
    }

    /**
     * 设置视标校准调整视标参数
     */
    fun setCalibrateCoordinate(result: CalibrateCoordinate?){
        result?.let {
            calibrateCoordinateLivedata.postValue(it)
        }
    }
}