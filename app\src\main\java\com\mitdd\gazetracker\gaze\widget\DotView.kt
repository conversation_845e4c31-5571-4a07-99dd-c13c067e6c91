package com.mitdd.gazetracker.gaze.widget

import android.content.Context
import android.graphics.PixelFormat
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import com.mitdd.gazetracker.R

/**
 * FileName: DotView
 * Author by lilin,Date on 2024/12/13 17:45
 * PS: Not easy to write code, please indicate.
 * 注视点view
 */
class DotView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    private val windowManager: WindowManager by lazy {
        context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    //注视点参数
    val dotParams = WindowManager.LayoutParams(
        20,
        20,
        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
        PixelFormat.TRANSLUCENT
    ).apply {
        gravity = Gravity.LEFT or Gravity.TOP
    }

    init {
        setBackgroundResource(R.drawable.common_green_round_bg)
    }

    fun show(x:Int,y:Int){
        dotParams.x = x
        dotParams.y = y
        windowManager.addView(this, dotParams)
    }

    fun update(x:Int,y:Int){
        dotParams.x = x
        dotParams.y = y
        windowManager.updateViewLayout(this, dotParams)
    }

    fun remove(){
        windowManager.removeView(this)
    }

}