package com.mitdd.gazetracker.gaze.widget

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.source.ConcatenatingMediaSource
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import kotlin.math.atan2
import com.mitdd.gazetracker.gaze.enumeration.PostureException
import com.mitdd.gazetracker.gaze.enumeration.PostureException.*

/**
 * FileName: PostureCalibrationView
 * Author by lilin,Date on 2024/7/30 11:17
 * PS: Not easy to write code, please indicate.
 * 姿势校准
 */
class PostureCalibrationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object{
        private val TAG = PostureCalibrationView::class.java.simpleName

        const val DEFAULT_REFERENCE_X = 0.5f
        const val DEFAULT_REFERENCE_Y = 0.5f

        //姿势重矫正
        const val TYPE_POSTURE_CORRECTION = 1
        //姿势校准
        const val TYPE_POSTURE_CALIBRATION = 2
    }

    private val postureCorrectionRoot by id<ConstraintLayout>(R.id.posture_correction_root)
    private val ivReferencePosition by id<ImageView>(R.id.iv_reference_position)
    private val ivRealLocation by id<ImageView>(R.id.iv_real_location)
    private val tvCorrectionPrompt by id<TextView>(R.id.tv_correction_prompt)
    private val llCloseCorrection by id<LinearLayout>(R.id.ll_close_correction)
    private val tvCloseCorrection by id<TextView>(R.id.tv_close_correction)
    private val llCalibration by id<LinearLayout>(R.id.ll_calibration)

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)

    //姿势矫正头像原始宽度
    private val avatarOriginalW = 240.dp2px(context)
    //姿势矫正头像原始高度
    private val avatarOriginalH = 271.dp2px(context)
    //校准参考点x坐标（0～1）根据屏幕宽度计算具体值
    private var referenceX = DEFAULT_REFERENCE_X
    //校准参考点y坐标（0～1）根据屏幕宽度计算具体值
    private var referenceY = DEFAULT_REFERENCE_Y

    //当手动关闭校准
    var onCloseCalibrationClick:(() -> Unit)? = null
    //当点击眼动校准
    var onCalibrationClick:(() -> Unit)? = null

    var calibrationType = TYPE_POSTURE_CALIBRATION

    private var isOpenSound = true

    init {
        LayoutInflater.from(context).inflate(R.layout.view_posture_calibration, this,true)

        initListener()
    }

    private fun initListener(){

        llCloseCorrection.setOnSingleClickListener {
            onCloseCalibrationClick?.invoke()
        }

        llCalibration.setOnSingleClickListener {
            onCalibrationClick?.invoke()
        }
    }
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        updateType(calibrationType)
    }

    @OptIn(UnstableApi::class)
    fun updateType(type:Int){
        calibrationType = type
        if (calibrationType == TYPE_POSTURE_CALIBRATION){
            tvCloseCorrection.text = context.getString(R.string.str_off_calibration)

            val mediaSources = mutableListOf<MediaSource>()
            val comfortablePositionSpeech = RawMedia(R.raw.calibration_find_comfortable_position).createMediaSource(context)
            if (comfortablePositionSpeech != null) mediaSources.add(comfortablePositionSpeech)
            val keepHeadPositionSpeech = RawMedia(R.raw.calibration_keep_head_position).createMediaSource(context)
            if (keepHeadPositionSpeech != null) mediaSources.add(keepHeadPositionSpeech)
            val concatenatingMediaSource = ConcatenatingMediaSource(*mediaSources.toTypedArray())
            PlayManager.playMediaSource(concatenatingMediaSource)

        }else if (calibrationType == TYPE_POSTURE_CORRECTION){
            tvCloseCorrection.text = context.getString(R.string.str_close_prompt)

            playSound(R.raw.calibration_return_correct_position)
        }
        llCalibration.isVisible = calibrationType == TYPE_POSTURE_CORRECTION
    }

    /**
     * 设置姿势矫正参数
     * @param referenceX 校准参考点x坐标（0～1）根据屏幕宽度计算具体值
     * @param referenceY 校准参考点y坐标（0～1）根据屏幕宽度计算具体值
     */
    fun setPostureCorrectionParam(referenceX:Float,referenceY:Float){
        this.referenceX = when{
            referenceX in 0f..1f -> referenceX
            else -> DEFAULT_REFERENCE_X
        }
        this.referenceY = when{
            referenceY in 0f..1f -> referenceX
            else -> DEFAULT_REFERENCE_Y
        }
        //计算两眼之间中心位置
        val centerX = this.referenceX * screenWidth
        val centerY = this.referenceY * screenHeight
        //更新矫正基准头像位置
        val marginLeft = centerX - avatarOriginalW / 2
        val marginTop = centerY - avatarOriginalH / 2
        val constraintSet = ConstraintSet()
        constraintSet.clone(postureCorrectionRoot)
        constraintSet.connect(R.id.iv_reference_position,ConstraintSet.LEFT,ConstraintSet.PARENT_ID,ConstraintSet.LEFT)
        constraintSet.clear(R.id.iv_reference_position,ConstraintSet.RIGHT)
        constraintSet.connect(R.id.iv_reference_position,ConstraintSet.TOP,ConstraintSet.PARENT_ID,ConstraintSet.TOP)
        constraintSet.setMargin(R.id.iv_reference_position,ConstraintSet.LEFT, marginLeft.toInt())
        constraintSet.setMargin(R.id.iv_reference_position,ConstraintSet.TOP, marginTop.toInt())
        constraintSet.applyTo(postureCorrectionRoot)
    }

    /**
     * 设置姿势矫正结果
     */
    fun setPostureCorrectionResult(data: PostureCalibrationResult){
        if (data.checkPostureCorrectionResult()){
            ivRealLocation.isVisible = true
            val leftPoint = PointF(data.leftX * screenWidth,data.leftY * screenHeight)
            val rightPoint = PointF(data.rightX * screenWidth,data.rightY * screenHeight)
            //计算两眼之间中心位置
            val centerX = leftPoint.x + (rightPoint.x - leftPoint.x) / 2
            val centerY = leftPoint.y + (rightPoint.y - leftPoint.y) / 2
            //真实位置图像宽高
            var width = avatarOriginalW
            var height = avatarOriginalH
            //缩放比例
            val scale = if (data.dist > 0 && data.dist < 1){
                val s = 0.5f / data.dist
                if (s < 0.5){
                    0.5f
                }else if (s > 1.5){
                    1.5f
                }else{
                    s
                }
            }else{
                1f
            }
            //重新计算宽高
            width = (width * scale).toInt()
            height = (height * scale).toInt()
            val params = ivRealLocation.layoutParams as ConstraintLayout.LayoutParams
            params.width = width
            params.height = height
            ivRealLocation.layoutParams = params

            //更新矫正基准头像位置
            val marginLeft = centerX - width / 2
            val marginTop = centerY - height / 2
            val constraintSet = ConstraintSet()
            constraintSet.clone(postureCorrectionRoot)
            constraintSet.connect(R.id.iv_real_location,ConstraintSet.LEFT,ConstraintSet.PARENT_ID,ConstraintSet.LEFT)
            constraintSet.clear(R.id.iv_real_location,ConstraintSet.RIGHT)
            constraintSet.connect(R.id.iv_real_location,ConstraintSet.TOP,ConstraintSet.PARENT_ID,ConstraintSet.TOP)
            constraintSet.setMargin(R.id.iv_real_location,ConstraintSet.LEFT, marginLeft.toInt())
            constraintSet.setMargin(R.id.iv_real_location,ConstraintSet.TOP, marginTop.toInt())
            constraintSet.applyTo(postureCorrectionRoot)

            //计算倾斜角度
            val tiltAngle = calculateAngleOfTilt(leftPoint, rightPoint)
            //计算旋转角度
            val rotationAngle = convertIntoAngleOfRotation(tiltAngle)
            ivRealLocation.rotation = rotationAngle.toFloat()

            //姿势正确
            if (data.aligned){
                ivRealLocation.setImageResource(R.drawable.icon_postural_correction_avatar_green)
                tvCorrectionPrompt.text = context.getString(R.string.str_please_remain_position)
            }else{
                val postureException = createPostureException(data)
                handlePostureException(postureException)
            }
        }else{
            ivRealLocation.isVisible = false
            tvCorrectionPrompt.text = context.getString(R.string.str_no_face_detected_please_sit_properly)
        }
    }

    private fun playSound(resId:Int){
        if (resId != 0 && isOpenSound){
            PlayManager.playRawMedia(context, RawMedia(resId))
        }
    }

    /**
     * 处理姿势异常
     */
    private fun handlePostureException(postureException: PostureException?){
        Logger.d(TAG, msg = "handlePostureException ${postureException?.name} ${postureException?.degree}")
        when(postureException?.degree){
            1 ->{
                ivRealLocation.setImageResource(R.drawable.icon_postural_correction_avatar_yellow)
            }
            2 ->{
                ivRealLocation.setImageResource(R.drawable.icon_postural_correction_avatar_red)
            }
            else ->{
                ivRealLocation.setImageResource(R.drawable.icon_postural_correction_avatar_yellow)
            }
        }
        when(postureException){
            FAR -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_come_closer)
            }
            NEARLY -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_stay_away)
            }
            LEFT -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_turn_to_the_right)
            }
            RIGHT -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_turn_to_the_left)
            }
            UP -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_go_down)
            }
            DOWN -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_go_up)
            }
            SKEW -> {
                tvCorrectionPrompt.text = context.getString(R.string.str_please_do_not_tilt_your_head)
            }
            else -> {
                tvCorrectionPrompt.text = ""
            }
        }
    }

    /**
     * 创建姿势异常
     */
    private fun createPostureException(data: PostureCalibrationResult): PostureException?{
        var postureException: PostureException? = null
        //判断远近
        Logger.d(TAG, msg = "createPostureException dist = ${data.dist}")
        when {
            data.dist < 0.4f -> {
                postureException = NEARLY.apply {
                    degree = 2
                }
            }
            data.dist >= 0.4f && data.dist < 0.45f -> {
                postureException = NEARLY.apply {
                    degree = 1
                }
            }
            data.dist > 0.55f && data.dist <= 0.6f -> {
                postureException = FAR.apply {
                    degree = 1
                }
            }
            data.dist > 0.6f ->{
                postureException = FAR.apply {
                    degree = 2
                }
            }
        }
        //计算两眼之间中心位置
        val centerX = (data.leftX + (data.rightX - data.leftX) / 2) * screenWidth
        val centerY = (data.leftY + (data.rightY - data.leftY) / 2) * screenHeight
        //判断左右
        val horizontalDistance = centerX - referenceX * screenWidth
        Logger.d(TAG, msg = "createPostureException horizontalDistance = $horizontalDistance")
        when{
            horizontalDistance < -384 ->{
                if (postureException == null || postureException.degree < 2){
                    postureException = LEFT.apply {
                        degree = 2
                    }
                }
            }
            horizontalDistance >= -384 && horizontalDistance < -192 ->{
                if (postureException == null || postureException.degree < 1){
                    postureException = LEFT.apply {
                        degree = 1
                    }
                }
            }
            horizontalDistance > 192 && horizontalDistance <= 384 ->{
                if (postureException == null || postureException.degree < 1){
                    postureException = RIGHT.apply {
                        degree = 1
                    }
                }
            }
            horizontalDistance > 384 ->{
                if (postureException == null || postureException.degree < 2){
                    postureException = RIGHT.apply {
                        degree = 2
                    }
                }
            }
        }
        //判断上下
        val verticalDistance = centerY - referenceY * screenWidth
        Logger.d(TAG, msg = "createPostureException verticalDistance = $verticalDistance")
        when{
            verticalDistance < -324 ->{
                if (postureException == null || postureException.degree < 2){
                    postureException = UP.apply {
                        degree = 2
                    }
                }
            }
            horizontalDistance >= -324 && horizontalDistance < -216 ->{
                if (postureException == null || postureException.degree < 1){
                    postureException = UP.apply {
                        degree = 1
                    }
                }
            }
            horizontalDistance > 216 && horizontalDistance <= 324 ->{
                if (postureException == null || postureException.degree < 1){
                    postureException = DOWN.apply {
                        degree = 1
                    }
                }
            }
            horizontalDistance > 324 ->{
                if (postureException == null || postureException.degree < 2){
                    postureException = DOWN.apply {
                        degree = 2
                    }
                }
            }
        }
        //判断倾斜
        val tiltAngle = calculateAngleOfTilt(PointF(data.leftX * screenWidth,data.leftY * screenHeight),PointF(data.rightX * screenWidth,data.rightY * screenHeight))
        Logger.d(TAG, msg = "createPostureException tiltAngle = $tiltAngle")
        when{
            tiltAngle < -15f || tiltAngle > 15f->{
                if (postureException == null || postureException.degree < 2){
                    postureException = SKEW.apply {
                        degree = 2
                    }
                }
            }
            tiltAngle in -15f..-10f || tiltAngle in 10f..15f ->{
                if (postureException == null || postureException.degree < 1){
                    postureException = SKEW.apply {
                        degree = 1
                    }
                }
            }
        }

        return postureException
    }

    /**
     * 计算双眼倾斜角度，值是 -180 ~ 180
     */
    private fun calculateAngleOfTilt(p1: PointF, p2: PointF): Double {
        val deltaX = (p2.x - p1.x).toDouble()
        val deltaY = (p2.y - p1.y).toDouble()

        // atan2 返回的是弧度值，我们需要转换为度数
        // tiltAngle 的取值是-180 到 180
        return Math.toDegrees(atan2(deltaY, deltaX))
    }

    /**
     * 将倾斜角度转成需要旋转的角度
     */
    private fun convertIntoAngleOfRotation(tiltAngle:Double) : Double{
        val rotationAngle = if (tiltAngle < 0) {
            tiltAngle + 360
        }else{
            tiltAngle
        }
        return rotationAngle
    }
}