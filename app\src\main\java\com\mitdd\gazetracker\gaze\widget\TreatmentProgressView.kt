package com.mitdd.gazetracker.gaze.widget

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.TimeProgress
import com.mitdd.gazetracker.gaze.widget.TreatmentProgressView.State.*
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.Gender
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.cancel

/**
 * FileName: TreatmentProgressView
 * Author by lilin,Date on 2024/10/26 16:10
 * PS: Not easy to write code, please indicate.
 * 遮盖疗法治疗进度
 */
class TreatmentProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val TAG = TreatmentProgressView::class.java.simpleName

    private val clRoot by id<ConstraintLayout>(R.id.cl_root)
    private val tvTitle by id<TextView>(R.id.tv_title)
    private val timeProgress by id<TimeProgress>(R.id.treatment_time_progress)
    private val ivGoHome by id<ImageView>(R.id.iv_go_home)

    private val windowManager: WindowManager by lazy {
        context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private var mState = PUT_AWAY

    private var eyePosition = ""

    private val handler = Handler(Looper.getMainLooper())
    private var putAwayRunnable = Runnable {
        updateState(PUT_AWAY)
    }

    init {
        val view = View.inflate(context, R.layout.view_treatment_progress,null)
        addView(view)

        val trainDurationView = timeProgress.getTrainDurationView()
        trainDurationView.setTextColor(Color.parseColor("#8D9EAC"))
        trainDurationView.textSize = 11f
        val plannedDurationView = timeProgress.getPlannedDurationView()
        plannedDurationView.setTextColor(Color.parseColor("#8D9EAC"))
        plannedDurationView.textSize = 11f

        initListener()
    }

    private fun initListener(){
        clRoot.setOnSingleClickListener {
            Logger.d(TAG, msg = "clRoot.setOnSingleClick mState = $mState")
            handler.removeCallbacks(putAwayRunnable)
            when(mState){
                UNFOLD -> {
                    updateState(PUT_AWAY)
                }
                PUT_AWAY -> {
                    updateState(UNFOLD)
                }
            }
        }
        ivGoHome.setOnSingleClickListener {
//            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
//            if (intent != null){
//                context.startActivity(intent)
//            }
            simulateLauncherLaunch(context)
        }
    }

    fun simulateLauncherLaunch(context: Context, packageName: String = context.packageName) {
        val launchIntent = context.packageManager.getLaunchIntentForPackage(packageName)?.apply {
            // 关键：添加重置任务栈标志
            addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
        } ?: run {
            // 备用方案（极少需要）
            Intent().apply {
                action = Intent.ACTION_MAIN
                addCategory(Intent.CATEGORY_LAUNCHER)
                setPackage(packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                        Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED
            }
        }

        context.startActivity(launchIntent)
    }

    fun show(){
        val width = 400.dp2px(context)
        var height = 90.dp2px(context)
        if (mState == PUT_AWAY){
            height = 23.dp2px(context)
        }
        val layoutParams = WindowManager.LayoutParams(
            width,
            height,
            WindowManager.LayoutParams.TYPE_KEYGUARD_DIALOG,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            PixelFormat.TRANSLUCENT
        )
        layoutParams.gravity = Gravity.TOP or Gravity.CLIP_HORIZONTAL
        windowManager.addView(this, layoutParams)
        updateState(mState)
    }

    fun remove(){
        windowManager.removeView(this)
    }

    /**
     * 设置弱视眼
     */
    fun setEyePosition(eyePosition:String){
        this.eyePosition = eyePosition
    }

    /**
     * 设置治疗时长
     * @param trainDuration 训练时长 单位秒
     * @param plannedDuration 计划时长 单位秒
     */
    fun setDuration(trainDuration:Int,plannedDuration:Int){
        timeProgress.setDuration(trainDuration,plannedDuration)
    }

    private fun unfold(){
        val param = this.layoutParams as WindowManager.LayoutParams
        param.height = 90.dp2px(context)
        windowManager.updateViewLayout(this,param)

        clRoot.setBackgroundResource(R.drawable.treatment_progress_unfold_bg)
        val rootParam = clRoot.layoutParams as LayoutParams
        rootParam.height = 90.dp2px(context)
        clRoot.layoutParams = rootParam

        tvTitle.isVisible = true
        ivGoHome.isVisible = true

        val progressBarParam = timeProgress.layoutParams as ConstraintLayout.LayoutParams
        progressBarParam.height = 25.dp2px(context)
        progressBarParam.setMargins(0,50.dp2px(context),0,0)
        timeProgress.layoutParams = progressBarParam

        timeProgress.getTrainDurationView().isVisible = true
        timeProgress.getPlannedDurationView().isVisible = true

        val progressBar = timeProgress.getSeekBar()
        progressBar.progressDrawable = ContextCompat.getDrawable(context,R.drawable.seekbar_treatment_progress_drawable)

        val thumbView = timeProgress.getThumbView()
        thumbView.isVisible = true
        when(UserManager.getAccountInfo()?.gender){
            Gender.MALE.value ->{
                thumbView.setImageResource(R.drawable.icon_seekbar_thumb_male)
            }
            else ->{
                thumbView.setImageResource(R.drawable.icon_seekbar_thumb_female)
            }
        }
    }

    private fun putAway(){
        val param = this.layoutParams as WindowManager.LayoutParams
        param.height = 23.dp2px(context)
        windowManager.updateViewLayout(this,param)

        clRoot.setBackgroundResource(R.drawable.treatment_progress_put_away_bg)
        val rootParam = clRoot.layoutParams as LayoutParams
        rootParam.height = 23.dp2px(context)
        clRoot.layoutParams = rootParam

        tvTitle.isVisible = false
        ivGoHome.isVisible = false

        val progressBarParam = timeProgress.layoutParams as ConstraintLayout.LayoutParams
        progressBarParam.height = 5.dp2px(context)
        progressBarParam.setMargins(0,9.dp2px(context),0,0)
        timeProgress.layoutParams = progressBarParam

        timeProgress.getTrainDurationView().isVisible = false
        timeProgress.getPlannedDurationView().isVisible = false

        val progressBar = timeProgress.getSeekBar()
        progressBar.progressDrawable = ContextCompat.getDrawable(context,R.drawable.seekbar_treatment_progress_drawable_put_away)

        timeProgress.getThumbView().isVisible = false
    }

    private fun updateState(state: State){
        mState = state
        when(state){
            UNFOLD -> {
                unfold()
                timeProgress.post {
                    timeProgress.updateThumbPosition()
                }
                handler.postDelayed(putAwayRunnable,3000)
            }
            PUT_AWAY -> {
                putAway()
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        handler.removeCallbacksAndMessages(null)
        coroutineScope.cancel()
    }

    enum class State{
        //展开
        UNFOLD,
        //收起
        PUT_AWAY
    }

}