package com.mitdd.gazetracker.gaze.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.PointFEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.PointF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate
import com.mitdd.gazetracker.gaze.bean.CalibrationResult
import com.mitdd.gazetracker.gaze.bean.VisualPoint
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: VisualCalibrationView
 * Author by lilin,Date on 2025/5/6 19:51
 * PS: Not easy to write code, please indicate.
 * 视标校准View
 */
class VisualCalibrationView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : ConstraintLayout(context, attrs,defStyle){

    private val TAG = VisualCalibrationView::class.java.simpleName

    private val clVisualContainer by id<ConstraintLayout>(R.id.cl_visual_container)
    private val ivVisualPoint by id<ImageView>(R.id.iv_visual_point)
    private val tvVisualCalibrationPrompt by id<TextView>(R.id.tv_visual_calibration_prompt)

    init {
        LayoutInflater.from(context).inflate(R.layout.view_visual_calibration, this, true)

        initView()
        initListener()
    }

    //视标校准时视标是否准备好
    private val isVisualReady = AtomicBoolean(false)

    //前一个校准点的索引（0~8）共9个点
    private var lastPointIndex:Int = -1

    //屏幕宽高
    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)

    private val visualAnimSet = AnimatorSet()
    private val isCalibrationComplete = AtomicBoolean(false)

    //视标校准完成，true为校准成功，false为校准失败
    var onCalibrationComplete:((Boolean) -> Unit)? = null
    //通知试标准备完成
    var onNotifyVisualReady:((Boolean) -> Unit)? = null

    private fun initView() {
        PlayManager.playRawMedia(context, RawMedia(R.raw.calibration_watch_stars_until_explode))
        tvVisualCalibrationPrompt.text = context.getString(R.string.str_watch_icon_until_bursts)
        tvVisualCalibrationPrompt.postDelayed({
            tvVisualCalibrationPrompt.isVisible = false
        },3000)
    }

    private fun initListener() {
    }

    //设置视标校准结果
    fun setCalibrationResult(result: CalibrationResult){
        if (isVisualReady.get()){
            if (result.leftConsistNum >= 2 && result.rightConsistNum >=2){
                startVisualRotationAnim()
            }else{
                stopVisualRotationAnim()
            }
        }
    }

    //设置视标校准调整视标参数
    fun setCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate){
        Logger.d(TAG, msg = "calibrateCoordinateLivedata = $calibrateCoordinate")
        if (calibrateCoordinate.state){
            isVisualReady.set(false)
            stopVisualRotationAnim()
            playExplosionAnimation(calibrateCoordinate) {
                //视标校准完成
                visualCalibrationComplete(calibrateCoordinate)
            }
        }else{
            notifyVisualReady(false)
            stopVisualRotationAnim()
            updateVisualPoint(calibrateCoordinate){
                notifyVisualReady(true)
            }
        }
    }

    /**
     * 通知视标准备好
     */
    private fun notifyVisualReady(isReady:Boolean){
        Logger.d(TAG, msg = "notifyVisualReady isReady = $isReady")
        isVisualReady.set(isReady)
        onNotifyVisualReady?.invoke(isReady)
    }

    private fun startVisualRotationAnim(){
        if (!visualAnimSet.isRunning){
            val rotation = ObjectAnimator.ofFloat(ivVisualPoint, "rotation", 0f, 360f).apply {
                duration = 300
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
            val scaleX = ObjectAnimator.ofFloat(ivVisualPoint, "scaleX", 1.0f, 0.8f).apply {
                duration = 300
                repeatCount = 1
            }
            val scaleY = ObjectAnimator.ofFloat(ivVisualPoint, "scaleY", 1.0f, 0.8f).apply {
                duration = 300
                repeatCount = 1
            }

            visualAnimSet.playTogether(rotation,scaleX,scaleY)
            visualAnimSet.start()
        }
    }

    private fun stopVisualRotationAnim(){
        ivVisualPoint.clearAnimation()
        visualAnimSet.cancel()
        ivVisualPoint.scaleX = 1.0f
        ivVisualPoint.scaleY = 1.0f
        ivVisualPoint.rotation = 0f
    }

    /**
     * 更新视标点
     */
    private fun updateVisualPoint(calibrateCoordinate: CalibrateCoordinate, animCompleted:() -> Unit){
        val point = VisualPoint(calibrateCoordinate.x, calibrateCoordinate.y)
        val visualWidth = 180.dp2px(context)
        val visualHeight = 180.dp2px(context)
        val isFirstPoint = lastPointIndex == -1
        Logger.d(TAG, msg = "updateVisualPoint point.x = ${point.x},point.y = ${point.y},isFirstPoint = $isFirstPoint")
        if (isFirstPoint){
            ivVisualPoint.setImageResource(R.drawable.icon_visual_logo)
            ivVisualPoint.isVisible = true
            ivVisualPoint.alpha = 1f
            val marginLeft = (point.x * screenWidth - visualWidth / 2).toInt()
            val marginTop = (point.y * screenHeight - visualHeight / 2).toInt()

            val constraintSet = ConstraintSet()
            constraintSet.clone(clVisualContainer)
            constraintSet.connect(R.id.iv_visual_point, ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT)
            constraintSet.clear(R.id.iv_visual_point, ConstraintSet.RIGHT)
            constraintSet.connect(R.id.iv_visual_point, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.setMargin(R.id.iv_visual_point, ConstraintSet.LEFT, marginLeft)
            constraintSet.setMargin(R.id.iv_visual_point, ConstraintSet.TOP, marginTop)
            constraintSet.applyTo(clVisualContainer)

            animCompleted.invoke()
        }else{
            playExplosionAnimation(calibrateCoordinate) {
                ivVisualPoint.setImageResource(R.drawable.icon_visual_logo)
                ivVisualPoint.isVisible = true
                ivVisualPoint.alpha = 0f
                val translationAnim = ValueAnimator.ofObject(
                    PointFEvaluator(),
                    PointF(ivVisualPoint.x, ivVisualPoint.y),
                    PointF(point.x * screenWidth - visualWidth / 2, point.y * screenHeight - visualHeight / 2)
                ).apply {
                    addUpdateListener { animation ->
                        val animatedValue = animation.animatedValue as PointF
                        ivVisualPoint.x = animatedValue.x
                        ivVisualPoint.y = animatedValue.y
                    }
                    duration = 600
                }
                val alphaAnim = ObjectAnimator.ofFloat(ivVisualPoint, "alpha", 0f, 1f).apply {
                    duration = 300
                }
                //展示动画
                val displayAnim = AnimatorSet().apply {
                    addListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            animCompleted.invoke()
                        }

                        override fun onAnimationCancel(animation: Animator) {
                            super.onAnimationCancel(animation)
                            animCompleted.invoke()
                        }
                    })
                }
                displayAnim.playTogether(translationAnim,alphaAnim)
                displayAnim.start()
            }

        }
        lastPointIndex = calibrateCoordinate.index
    }

    /**
     * 播放视标爆破动画
     * @param calibrateCoordinate 校准结果
     */
    private fun playExplosionAnimation(calibrateCoordinate: CalibrateCoordinate, animCompleted:() -> Unit){
        val calibLeft = intToNineBitBinarySimplified(calibrateCoordinate.calibLeft).reversed()
        val calibRight = intToNineBitBinarySimplified(calibrateCoordinate.calibRight).reversed()
        Logger.d(TAG, msg = "playExplosionAnimation calibLeft = $calibLeft, calibRight = $calibRight, lastPointIndex = $lastPointIndex")
        val calibLeftCode = if (lastPointIndex in calibLeft.indices) calibLeft[lastPointIndex].digitToIntOrNull()?:0 else 0
        val calibRightCode = if (lastPointIndex in calibRight.indices) calibRight[lastPointIndex].digitToIntOrNull()?:0 else 0
        Logger.d(TAG, msg = "playExplosionAnimation calibLeftCode = $calibLeftCode, calibRightCode = $calibRightCode")
        if (calibLeftCode == 1 && calibRightCode == 1){
            playVisualCalibrationSuccess()
        }else{
            playVisualCalibrationFailed()
        }
        Glide.with(this)
            .asGif()
            .load(R.drawable.visual_logo_explosion)
            .listener(object : RequestListener<GifDrawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<GifDrawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    return false
                }

                override fun onResourceReady(
                    resource: GifDrawable?,
                    model: Any?,
                    target: Target<GifDrawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    if (resource != null){
                        resource.setLoopCount(1)
                        resource.registerAnimationCallback(object : Animatable2Compat.AnimationCallback(){
                            override fun onAnimationEnd(drawable: Drawable?) {
                                super.onAnimationEnd(drawable)
                                animCompleted.invoke()
                            }
                        })
                    }else{
                        animCompleted.invoke()
                    }
                    return false
                }

            })
            .into(ivVisualPoint)
    }

    private fun intToNineBitBinarySimplified(decimalValue: Int): String {
        // 将整数转换为二进制字符串
        val binaryString = Integer.toBinaryString(decimalValue)
        // 确保二进制字符串长度为9位，不足的部分在前面补0
        return binaryString.padStart(9, '0')
    }

    /**
     * 播放单个视标校准成功语音
     */
    private fun playVisualCalibrationSuccess(){
        PlayManager.playRawMedia(context,RawMedia(R.raw.calibration_visual_success))
    }

    /**
     * 播放单个视标校准失败语音
     */
    private fun playVisualCalibrationFailed(){
        PlayManager.playRawMedia(context,RawMedia(R.raw.calibration_visual_failed))
    }

    /**
     * 视标校准完成
     */
    private fun visualCalibrationComplete(calibrateCoordinate: CalibrateCoordinate){
        isCalibrationComplete.set(true)
        lastPointIndex = -1

        if (calibrateCoordinate.succeed){
            //校准成功
            PlayManager.playRawMedia(context,RawMedia(R.raw.calibration_success))
        }else{
            //校准失败
            PlayManager.playRawMedia(context,RawMedia(R.raw.calibration_failure))
        }
        onCalibrationComplete?.invoke(calibrateCoordinate.succeed)
    }

}