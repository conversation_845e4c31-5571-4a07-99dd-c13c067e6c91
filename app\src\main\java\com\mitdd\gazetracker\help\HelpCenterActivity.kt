package com.mitdd.gazetracker.help

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.ui.CustomItemDecoration
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.vm.DeviceViewModel

/**
 * FileName: HelpCenterActivity
 * Author by lilin,Date on 2024/10/30 14:26
 * PS: Not easy to write code, please indicate.
 */
class HelpCenterActivity : GTBaseActivity() {

    companion object{
        private val TAG = HelpCenterActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, HelpCenterActivity::class.java)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val rvMaterial by id<RecyclerView>(R.id.rv_material)

    private val deviceVM by viewModels<DeviceViewModel>()

    private val helpCenterAdapter = HelpCenterAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_help_center)

        initView()

        initObserver()

        initData()
    }

    private fun initView() {
        rvMaterial.layoutManager = GridLayoutManager(this, 3)
        rvMaterial.addItemDecoration(CustomItemDecoration(20.dp2px(this), 20.dp2px(this), 3))
        rvMaterial.adapter = helpCenterAdapter

        helpCenterAdapter.onItemClick = { material ->
            val link = material.link?:""
            if (material.materialType == "video" && !TextUtils.isEmpty(link)){
                startActivity(TutorialActivity.createIntent(this, link))
            }
        }

        ivBack.setOnSingleClickListener {
            finish()
        }
    }

    private fun initObserver() {
        deviceVM.materialsLiveData.observe(this){
            val list = it?.list
            if (!list.isNullOrEmpty()){
                helpCenterAdapter.setHelpVideoData(list)
                helpCenterAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun initData() {
        deviceVM.getSpecifiedTypeMaterial("help")
    }

}