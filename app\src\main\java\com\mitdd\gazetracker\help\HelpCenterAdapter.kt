package com.mitdd.gazetracker.help

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.bumptech.glide.Glide
import com.google.android.material.imageview.ShapeableImageView
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.bean.Material

/**
 * FileName: HelpVideoAdapter
 * Author by lilin,Date on 2024/8/23 11:36
 * PS: Not easy to write code, please indicate.
 */
class HelpCenterAdapter : RecyclerView.Adapter<HelpCenterAdapter.HelpHolder>() {

    private var materials: MutableList<Material> = mutableListOf()

    var onItemClick:((Material) -> Unit)? = null

    fun setHelpVideoData(data:List<Material>){
        materials.clear()
        materials.addAll(data)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HelpHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_help_center, parent, false)
        return HelpHolder(view)
    }

    override fun getItemCount(): Int {
        return materials.size
    }

    override fun onBindViewHolder(holder: HelpHolder, position: Int) {
        if (position in materials.indices){
            holder.bind(materials[position])
        }
    }

    inner class HelpHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val ivMaterialIcon: ShapeableImageView = itemView.findViewById(R.id.iv_material_icon)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_title)

        fun bind(material: Material){
            itemView.setOnSingleClickListener {
                onItemClick?.invoke(material)
            }
            material.image?.let {
                Glide.with(itemView.context).load(it).into(ivMaterialIcon)
            }
            tvTitle.text = material.title
        }
    }

}