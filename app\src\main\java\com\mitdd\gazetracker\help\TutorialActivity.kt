package com.mitdd.gazetracker.help

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * FileName: TutorialActivity
 * Author by lilin,Date on 2024/10/18 13:46
 * PS: Not easy to write code, please indicate.
 * 使用教程
 */
class TutorialActivity : GTBaseActivity() {

    companion object{
        private val TAG = TutorialActivity::class.java.simpleName

        const val INPUT_PARAM_URL = "input_url"

        fun createIntent(context: Context,url:String): Intent {
            val intent = Intent(context, TutorialActivity::class.java)
            intent.putExtra(INPUT_PARAM_URL,url)
            return intent
        }
    }

    private val clTutorialRoot by id<ConstraintLayout>(R.id.cl_tutorial_root)
    private val clOuterFrame by id<ConstraintLayout>(R.id.cl_outer_frame)
    private val tvBringIntoUse by id<TextView>(R.id.tv_bring_into_use)
    private val clPlayContainer by id<ConstraintLayout>(R.id.cl_play_container)
    private val playerView by id<PlayerView>(R.id.player_view)
    private val clController by id<ConstraintLayout>(R.id.cl_controller)
    private val ivPlayControlPlay by id<ImageView>(R.id.iv_play_control_play)
    private val ivPlayControlFullScreen by id<ImageView>(R.id.iv_play_control_full_screen)
    private val tvPlayedDuration by id<TextView>(R.id.tv_played_duration)
    private val tvTotalDuration by id<TextView>(R.id.tv_total_duration)
    private val playedProgressBar by id<SeekBar>(R.id.played_progress_bar)
    private val tvPlayControlBringIntoUse by id<TextView>(R.id.tv_play_control_bring_into_use)

    private var exoPlayer: ExoPlayer? = null
    private var updatePlayProgressJob:Job? = null

    private var mUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_tutorial)

        initParam()

        initView()

        initListener()

        initData()
    }

    override fun onResume() {
        super.onResume()
        exoPlayer?.playWhenReady = true
    }

    override fun onPause() {
        super.onPause()
        exoPlayer?.playWhenReady = false
        stopUpdatePlayProgress()
    }

    override fun onDestroy() {
        exoPlayer?.release()
        super.onDestroy()
    }

    private fun initParam(){
        mUrl = intent?.getStringExtra(INPUT_PARAM_URL)?:""
    }

    private fun initView() {
        exoPlayer = ExoPlayer.Builder(this).build()
        playerView.player = exoPlayer

        playedProgressBar.isEnabled = false

        tvBringIntoUse.isEnabled = true
        tvPlayControlBringIntoUse.isEnabled = true
    }

    private fun initData(){
        play()
    }

    private fun play(){
        if (!TextUtils.isEmpty(mUrl)){
            val mediaItem = MediaItem.fromUri(Uri.parse(mUrl))
            exoPlayer?.setMediaItem(mediaItem)
            exoPlayer?.prepare()
            exoPlayer?.playWhenReady = true
        }else{
            finish()
        }
    }

    private fun initListener(){
        exoPlayer?.addListener(object :Player.Listener{

            override fun onPlaybackStateChanged(playbackState: Int) {
                super.onPlaybackStateChanged(playbackState)
                when (playbackState) {
                    Player.STATE_IDLE -> {
                        //空闲状态：表示当前没有任何媒体数据可供播放
                    }

                    Player.STATE_BUFFERING -> {
                        //缓冲状态：表示当前正在获取媒体数据进行缓冲
                    }

                    Player.STATE_READY -> {
                        //就绪状态：表示已经准备好播放媒体的数据
                    }

                    Player.STATE_ENDED -> {
                        //结束状态：表示已经播放完所有媒体数据
                        tvBringIntoUse.isEnabled = true
                        tvPlayControlBringIntoUse.isEnabled = true
                    }

                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                ivPlayControlPlay.isSelected = isPlaying
                if (isPlaying){
                    updateDuration()
                    startUpdatePlayProgress()
                }else{
                    stopUpdatePlayProgress()
                    updateCurrentPosition()
                }
            }

            override fun onIsLoadingChanged(isLoading: Boolean) {
                super.onIsLoadingChanged(isLoading)
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                ivPlayControlPlay.isSelected = false
            }

        })

        ivPlayControlPlay.setOnSingleClickListener {
            exoPlayer?.let {
                if (it.isPlaying){
                    it.playWhenReady = false
                    ivPlayControlPlay.isSelected = false
                }else{
                    it.playWhenReady = true
                    ivPlayControlPlay.isSelected = true
                }
            }
        }
        ivPlayControlFullScreen.setOnSingleClickListener {
            val selected = ivPlayControlFullScreen.isSelected
            ivPlayControlFullScreen.isSelected = !selected
            if (selected){
                //切成半屏
                clOuterFrame.isVisible = true
                val params = clPlayContainer.layoutParams as LayoutParams
                params.width = 435.dp2px(this)
                params.height = 245.dp2px(this)
                clPlayContainer.layoutParams = params
                val constraintSet = ConstraintSet()
                constraintSet.clone(clTutorialRoot)
                constraintSet.setMargin(R.id.cl_play_container,ConstraintSet.TOP, 137.dp2px(this))
                constraintSet.applyTo(clTutorialRoot)

                tvPlayControlBringIntoUse.isVisible = false

                val controllerParams = clController.layoutParams as LayoutParams
                controllerParams.height = 40.dp2px(this)
                clController.layoutParams = controllerParams
            }else{
                //切成全屏
                clOuterFrame.isVisible = false
                val params = clPlayContainer.layoutParams as LayoutParams
                params.width = LayoutParams.MATCH_PARENT
                params.height = LayoutParams.MATCH_PARENT
                clPlayContainer.layoutParams = params
                val constraintSet = ConstraintSet()
                constraintSet.clone(clTutorialRoot)
                constraintSet.setMargin(R.id.cl_play_container,ConstraintSet.TOP, 0)
                constraintSet.applyTo(clTutorialRoot)
                tvPlayControlBringIntoUse.isVisible = true

                val controllerParams = clController.layoutParams as LayoutParams
                controllerParams.height = 60.dp2px(this)
                clController.layoutParams = controllerParams
            }
        }
        tvBringIntoUse.setOnSingleClickListener {
            exoPlayer?.playWhenReady = false
            finish()
        }
        tvPlayControlBringIntoUse.setOnSingleClickListener {
            exoPlayer?.playWhenReady = false
            finish()
        }
    }

    //开始更新播放进度
    private fun startUpdatePlayProgress(){
        stopUpdatePlayProgress()
        updatePlayProgressJob = lifecycleScope.launch(Dispatchers.Default) {
            while (true){
                withContext(Dispatchers.Main){
                    updateCurrentPosition()
                }
                delay(1000)
            }
        }
    }

    private fun stopUpdatePlayProgress(){
        updatePlayProgressJob?.cancel()
        updatePlayProgressJob = null
    }

    private fun updateDuration(){
        val duration = exoPlayer?.duration?:0
        playedProgressBar.max = duration.toInt()
        if (duration > 0){
            val totalDuration = TimeUtils.parseTimeToTimeString(duration, "mm:ss")
            tvTotalDuration.text = totalDuration
        }else{
            tvTotalDuration.text = "00:00"
        }
    }

    private fun updateCurrentPosition(){
        val currentPosition = exoPlayer?.currentPosition ?: 0
        playedProgressBar.progress = currentPosition.toInt()
        if (currentPosition > 0){
            val playedDuration = TimeUtils.parseTimeToTimeString(currentPosition, "mm:ss")
            tvPlayedDuration.text = playedDuration
        }else{
            tvPlayedDuration.text = "00:00"
        }
    }

}