package com.mitdd.gazetracker.medicalhome

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Looper
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.videobox.MultiClickListener
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.mqtt.MQTTInitManager
import com.mitdd.gazetracker.mqtt.listener.IConnectNotifyCallBack
import com.mitdd.gazetracker.user.BindActivity
import com.mitdd.gazetracker.user.vm.UserViewModel

/**
 * FileName: HomeMainActivity
 * Author by lilin,Date on 2024/12/24 15:04
 * PS: Not easy to write code, please indicate.
 */
class HomeMainActivity : GTBaseActivity() {

    companion object{
        private val TAG = HomeMainActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, HomeMainActivity::class.java)
            return intent
        }
    }

    private val viewConfig by id<View>(R.id.view_config)

    val mainDialogTaskManager = DialogTaskManager(this)
    private val userVM by viewModels<UserViewModel>()

    private val mHandler = LifecycleHandler(Looper.getMainLooper(), this)

    private var bindLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            userVM.getAccountInfo()
        }
    }

    //MQTT消息监听
    private val iConnectNotifyCallBack = object : IConnectNotifyCallBack {
        override fun onDeviceUnbind() {
            super.onDeviceUnbind()
            userVM.getAccountInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_home_main)

        initParam()
        initView()
        initListener()
        initObserver()
        initData()
        initMQTT()
        startGazeTrackerService()
    }

    private fun initParam(){
        val intent = intent
        val hasMaskTherapyState = intent.hasExtra(GazeConstants.KEY_MASK_THERAPY_STATE)
        Logger.d(TAG, msg = "initParam hasMaskTherapyState = $hasMaskTherapyState")
        if (hasMaskTherapyState){
            val state = intent.getBooleanExtra(GazeConstants.KEY_MASK_THERAPY_STATE,false)
            Logger.d(TAG, msg = "initParam state = $state")
            mHandler.postDelayed({
                LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_CURE).post(state)
            },1000)
        }
    }

    private fun initView(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_home_main, HomeMainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    private fun initListener(){
        MQTTInitManager.addIConnectNotifyCallBack(iConnectNotifyCallBack)
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                DeviceManager.startConfigActivity(this@HomeMainActivity)
            }
        })
    }

    private fun initObserver() {

    }

    private fun initData(){
        userVM.getAccountInfo()
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        initParam()
    }

    /**
     * 去绑定
     */
    fun toBind(){
        bindLauncher.launch(BindActivity.createIntent(this))
    }

    /**
     * 开启眼动追踪服务
     */
    private fun startGazeTrackerService(){
        val intent = Intent(this, GazeTrackService::class.java)
        startForegroundService(intent)
    }

    override fun onDestroy() {
        MQTTInitManager.removeIConnectNotifyCallBack(iConnectNotifyCallBack)
        super.onDestroy()
    }

}