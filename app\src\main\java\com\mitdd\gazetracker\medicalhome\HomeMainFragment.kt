package com.mitdd.gazetracker.medicalhome

import android.app.Activity
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.behaviorguidance.BehaviorGuidanceManager
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.ai.ChatWebActivity
import com.mitdd.gazetracker.ai.vm.AdaViewModel
import com.mitdd.gazetracker.common.widget.CommonExceptionView
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.medicalhome.bean.Module
import com.mitdd.gazetracker.medicalhome.receiver.RefreshBindUserReceiver
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentModule
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleAdapter
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleItemDecoration
import com.mitdd.gazetracker.medicalhome.vm.HomeViewModel
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import com.airdoc.component.media.bean.UrlMedia
import com.mitdd.gazetracker.medicalhome.menu.MenuPopupWindow
import com.mitdd.gazetracker.help.HelpCenterActivity
import com.mitdd.gazetracker.update.UpdateDialog
import com.mitdd.gazetracker.update.vm.UpdateViewModel
import com.mitdd.gazetracker.medicalhome.preview.ParamSettingActivity
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.vm.MaskViewModel
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentManager
import com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.AccountInfo
import com.mitdd.gazetracker.user.bean.Gender
import com.mitdd.gazetracker.user.vm.UserViewModel
import com.mitdd.gazetracker.utils.LocaleManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * FileName: HomeMainFragment
 * Author by lilin,Date on 2024/9/26 10:50
 * PS: Not easy to write code, please indicate.
 * 家庭版主页
 */
class HomeMainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = HomeMainFragment::class.java.simpleName

        fun newInstance(): HomeMainFragment {
            return HomeMainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_home_main
    }

    private val ivHomeLogo by id<ImageView>(R.id.iv_home_logo)
    private val ivMore by id<ImageView>(R.id.iv_more)
    private val ivHelpCenter by id<ImageView>(R.id.iv_help_center)
    private val ivCalibration by id<ImageView>(R.id.iv_calibration)
    private val ivParamSetting by id<ImageView>(R.id.iv_param_setting)
    private val clUserInfo by id<ConstraintLayout>(R.id.cl_user_info)
    private val viewUserBg by id<View>(R.id.view_user_bg)
    private val ivUserAvatar by id<ImageView>(R.id.iv_user_avatar)
    private val tvUserName by id<TextView>(R.id.tv_user_name)
    private val rvTreatmentModule by id<RecyclerView>(R.id.rv_treatment_module)
    private val clNetworkException by id<CommonExceptionView>(R.id.cl_network_exception)
    private val tvUserUnbind by id<TextView>(R.id.tv_user_unbind)
    private val ivAi by id<ImageView>(R.id.iv_ai)

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false)?:false
            if (isSucceed){
                Toast.makeText(mActivity,mActivity.getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                val calibrationFailureDialog = CalibrationFailureDialog(mActivity).apply {
                    onRecalibration = {
                        startCalibration()
                    }
                }
                calibrationFailureDialog.show()
            }
        }
    }

    private val homeVM by activityViewModels<HomeViewModel>()
    private val treatmentVM by activityViewModels<TreatmentViewModel>()
    private val updateVM by activityViewModels<UpdateViewModel>()
    private val maskVM by activityViewModels<MaskViewModel>()
    private val userVM by activityViewModels<UserViewModel>()
    private val adaVM by activityViewModels<AdaViewModel>()

    private val mModuleAdapter = TreatmentModuleAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        startRefreshBindUser()
        launch(Dispatchers.IO) {
            BehaviorGuidanceManager.initialize(mActivity)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cancelRefreshBindUser()
        BehaviorGuidanceManager.release()
    }

    override fun initView() {
        super.initView()
        initListener()

        clNetworkException.apply {
            refreshClick = {
                clNetworkException.isVisible = false
                homeVM.getMedicalHomeProfile()
            }
            getExceptionIcon().apply {
                val params = layoutParams as ConstraintLayout.LayoutParams
                params.width = 250.dp2px(mActivity)
                params.height = 223.dp2px(mActivity)
                layoutParams = params
                setImageResource(R.drawable.icon_home_network_exception)
            }
            getExceptionInfo().apply {
                val params = layoutParams as ConstraintLayout.LayoutParams
                params.setMargins(0,28.dp2px(mActivity),0,0)
                layoutParams = params
                setTextColor(ContextCompat.getColor(mActivity, R.color.white))
                text = getString(R.string.str_network_anomaly_prompt)
                textSize = 15f
            }
            getExceptionPrompt1().isVisible = false
            getExceptionPrompt2().isVisible = false
        }

        rvTreatmentModule.layoutManager = LinearLayoutManager(mActivity,RecyclerView.HORIZONTAL,false)
        rvTreatmentModule.addItemDecoration(TreatmentModuleItemDecoration(15.dp2px(mActivity)))
        rvTreatmentModule.adapter = mModuleAdapter

        val locales = resources.configuration.locales
        val locale = locales.get(0)
        val language = locale.language
        val country = locale.country
        Logger.d(TAG, msg = "initView locale = ${locale}")
        Logger.d(TAG, msg = "initView language = $language, country = $country")
    }

    override fun initObserver() {
        super.initObserver()
        userVM.accountInfoLiveData.observe(this){
            homeVM.getMedicalHomeProfile()
            treatmentVM.getCurrentTreatment()
            val isBind = UserManager.isBind()
            updateUserAvatar(it)
            updateUserName(it)
            tvUserUnbind.isVisible = !isBind
            if (isBind){
                val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
                dialogTaskManager?.let {
                    TreatmentManager.showReviewRemindDialog(mActivity,dialogTaskManager,3)
                }
            }else{
                PlayManager.playRawMedia(mActivity, RawMedia(R.raw.welcome_please_bind_the_patient_file_first))
            }
        }
        homeVM.medicalHomeProfileLiveData.observe(this){
            updateTreatmentModule(it?.modules)
        }
        treatmentVM.curTreatmentLiveData.observe(this){
            Logger.d(TAG, msg = "curTreatment courseId = ${it?.courseId}")
            if (it != null){
                handlerTreatmentDialog(it)
            }else{
                if (UserManager.isBind()){
                    if (LocaleManager.getLanguage() == "en"){
                        PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/treatment_not_open.wav"))
                    }else{
                        PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/treatment_not_open.wav"))
                    }
                }
            }
        }
        treatmentVM.activationTreatmentSuccessLiveData.observe(this){ treatmentInfo ->
            Logger.d(TAG, msg = "activationTreatmentSuccess courseId = ${treatmentInfo.courseId}")
            val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
            dialogTaskManager?.let {
                TreatmentManager.showTreatmentActivationSuccessDialog(mActivity,dialogTaskManager, treatmentInfo ,true)
            }
            treatmentVM.getCurrentTreatment()
        }
        updateVM.appUpdateInfoLiveData.observe(this){
            it?.let {
                val url = it.appVersion?.url?:""
                val version = it.appVersion?.version ?: ""
                val introduction = it.appVersion?.introduce?:""
                val appSize = it.appVersion?.appSize?:0
                if (!TextUtils.isEmpty(url)){
                    UpdateDialog(mActivity,url,version,introduction, it.forceUpdate?:false,appSize).show()
                }
            }
        }
        homeVM.maskTherapyStateLivedata.observe(this){
            Logger.d(TAG, msg = "maskTherapyStateLivedata state = $it")
            updateUserAvatar(UserManager.getAccountInfo())
        }
        LiveEventBus.get<Any>(RefreshBindUserReceiver.EVENT_REFRESH_BIND_USER).observe(this){
            Logger.d(TAG, msg = "EVENT_REFRESH_BIND_USER")
            homeVM.getMedicalHomeProfile()
        }
        adaVM.authCodeLiveData.observe(this){
            Logger.d(TAG, msg = "authCodeLiveData = $it")
            ivAi.isVisible = !TextUtils.isEmpty(it?.authCode) && !TextUtils.isEmpty(it?.chatUrl)
        }
    }

    override fun initData() {
        super.initData()
        updateDeviceInfo()
        adaVM.getAuthCode(DeviceManager.getDeviceSn())
    }

    private fun initListener() {
        ivMore.setOnSingleClickListener {
            val menuPopupWindow = MenuPopupWindow(mActivity)
            menuPopupWindow.showAsDropDown(ivMore,0,
                10.dp2px(mActivity), Gravity.END)
            menuPopupWindow.show()
            menuPopupWindow.onVersionClick = {
                updateVM.getAppUpdateInfo()
            }
        }
        ivHelpCenter.setOnSingleClickListener {
            startActivity(HelpCenterActivity.createIntent(mActivity))
        }
        ivCalibration.setOnSingleClickListener {
            val isBind = UserManager.isBind()
            Logger.d(TAG, msg = "Calibration click isBind = $isBind")
            if (isBind){
                startCalibration()
            }else{
                (mActivity as? HomeMainActivity)?.toBind()
            }
        }
        clUserInfo.setOnSingleClickListener {
            val isBind = UserManager.isBind()
            Logger.d(TAG, msg = "clUserInfo click isBind = $isBind")
            if (!isBind){
                (mActivity as? HomeMainActivity)?.toBind()
            }
        }
        tvUserUnbind.setOnSingleClickListener {
            (mActivity as? HomeMainActivity)?.toBind()
        }
        ivParamSetting.setOnSingleClickListener {
            startActivity(ParamSettingActivity.createIntent(mActivity, ParamSettingActivity.MODE_AMBLYOPIA))
        }
        ivAi.setOnSingleClickListener {
            startActivity(ChatWebActivity.createIntent(mActivity))
        }
    }

    private fun updateTreatmentModule(modules:List<Module>?){
        if (!modules.isNullOrEmpty()){
            rvTreatmentModule.isVisible = true
            clNetworkException.isVisible = false
            val filterModules = modules.filter { module ->
                module.moduleEnable == true &&
                        (module.moduleKey == TreatmentModule.OCCLUSION_THERAPY.moduleKey || module.moduleKey == TreatmentModule.VISION_THERAPY.moduleKey)
            }
            mModuleAdapter.setTreatmentModuleData(filterModules,childFragmentManager)
            mModuleAdapter.notifyDataSetChanged()
        }else{
            rvTreatmentModule.isVisible = false
            clNetworkException.isVisible = true
        }
    }

    private fun updateUserAvatar(accountInfo: AccountInfo?){
        if (UserManager.isBind()){
            viewUserBg.isSelected = true
            val maskTherapyState = DeviceManager.getMaskTherapyState()
            //弱视眼
            val eyePosition = maskVM.eyePosition
            if (accountInfo?.gender == Gender.MALE.value){
                if (maskTherapyState){
                    if (eyePosition == AmblyopicEye.LEFT.value){
                        ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_male_left_blue_right_red_glasses)
                    }else{
                        ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_male_left_rad_right_blue_glasses)
                    }
                }else{
                    ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_male_normal)
                }
            }else{
                if (maskTherapyState){
                    if (eyePosition == AmblyopicEye.LEFT.value){
                        ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_female_left_blue_right_red_glasses)
                    }else{
                        ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_female_left_rad_right_blue_glasses)
                    }
                }else{
                    ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_female_normal)
                }
            }
        }else{
            viewUserBg.isSelected = false
            ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_unbound)
        }
    }

    private fun updateUserName(accountInfo: AccountInfo?){
        if (UserManager.isBind()){
            tvUserName.text = accountInfo?.accountName?:""
        }else{
            tvUserName.text = getString(R.string.str_unbound)
        }
    }

    private fun updateDeviceInfo(){
        ivParamSetting.isVisible = DeviceManager.isDemoMode()
        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(mActivity,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivHomeLogo)
        }else{
            ivHomeLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }

    /**
     * 处理当前疗程弹窗
     */
    private fun handlerTreatmentDialog(treatmentInfo: TreatmentInfo){
        val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
        dialogTaskManager?.let {
            TreatmentManager.showTreatmentExpirationRemindDialog(mActivity,dialogTaskManager,treatmentInfo,7)
        }
        when(treatmentInfo.courseStatus){
            TreatmentStatus.INACTIVE.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentActivationRemindDialog(mActivity,dialogTaskManager,treatmentInfo, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
            }
            TreatmentStatus.PENDING.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentPauseDialog(mActivity,dialogTaskManager, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
            }
            TreatmentStatus.COMPLETED.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentExpirationDialog(mActivity,dialogTaskManager,treatmentInfo)
                }
            }
        }
    }

    //启动眼动校准
    private fun startCalibration(){
        calibrationLauncher.launch(CalibrationActivity.createIntent(mActivity, CalibrationMode.CALIBRATION,false))
    }

    /**
     * 开启刷新绑定用户信息定时任务
     */
    private fun startRefreshBindUser(){
        cancelRefreshBindUser()
        // 获取AlarmManager实例
        val alarmManager = mActivity.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        // 创建一个Intent，指向ArchiveRefreshBroadcastReceiver
        val intent = Intent(mActivity, RefreshBindUserReceiver::class.java)
        intent.action = RefreshBindUserReceiver.ACTION_REFRESH_BIND_USER
        // 创建一个PendingIntent，用于在闹钟触发时发送广播
        val pendingIntent = PendingIntent.getBroadcast(mActivity, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        // 设置定时任务时间为每天凌晨1点
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY,1)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)

        // 如果当前时间已经过了今天的1点，设置为明天的1点
        if (calendar.timeInMillis < System.currentTimeMillis()) {
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }

        // 设置重复的定时任务，每天1点执行
        alarmManager?.setRepeating(
            AlarmManager.RTC_WAKEUP,calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY,pendingIntent)
    }

    /**
     * 取消刷新绑定用户信息定时任务
     */
    private fun cancelRefreshBindUser(){
        val alarmManager = mActivity.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        val intent = Intent(mActivity, RefreshBindUserReceiver::class.java)
        intent.action = RefreshBindUserReceiver.ACTION_REFRESH_BIND_USER
        val pendingIntent = PendingIntent.getBroadcast(mActivity, 0, intent, PendingIntent.FLAG_IMMUTABLE)
        pendingIntent?.cancel()
        alarmManager?.cancel(pendingIntent)
    }

    override fun onResume() {
        super.onResume()
        //检查升级
        updateVM.getAppUpdateInfo()
    }

    override fun onPause() {
        super.onPause()
        PlayManager.pause()
    }

}