package com.mitdd.gazetracker.medicalhome

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R
import kotlin.math.ceil
import kotlin.math.floor

/**
 * FileName: TimeProgress
 * Author by lilin,Date on 2024/11/4 17:00
 * PS: Not easy to write code, please indicate.
 * 时间进度条
 */
class TimeProgress @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object{
        private val TAG = TimeProgress::class.java.simpleName
    }

    private val tvTrainDuration by id<TextView>(R.id.tv_train_duration)
    private val tvPlannedDuration by id<TextView>(R.id.tv_planned_duration)
    private val progressBar by id<SeekBar>(R.id.progress_bar)
    private val ivThumb by id<ImageView>(R.id.iv_thumb)

    init {
        val view = View.inflate(context, R.layout.view_time_progress,null)
        addView(view)

        progressBar.isEnabled = false

        initListener()
    }

    fun getTrainDurationView():TextView{
        return tvTrainDuration
    }

    fun getPlannedDurationView():TextView{
        return tvPlannedDuration
    }

    fun getThumbView():ImageView{
        return ivThumb
    }

    fun getSeekBar():SeekBar{
        return progressBar
    }

    /**
     * 设置治疗时长
     * @param trainDuration 训练时长 单位秒
     * @param plannedDuration 计划时长 单位秒
     */
    fun setDuration(trainDuration:Int,plannedDuration:Int){
        setPlannedDuration(plannedDuration)
        setTreatmentDuration(trainDuration)
    }

    /**
     * 更新计划治疗时长
     * @param duration 单位秒
     */
    fun setPlannedDuration(duration:Int){
        //向上取整
        val minutes = ceil(duration.toFloat() / 60).toInt()
        tvPlannedDuration.text = "${minutes}min"
        progressBar.max = minutes
    }

    /**
     * 更新已治疗时长
     * @param duration 单位秒
     */
    fun setTreatmentDuration(duration:Int){
        //向下取整
        val minutes = floor(duration.toFloat() / 60).toInt()
        tvTrainDuration.text = "${minutes}min"
        progressBar.progress = minutes
    }

    private fun initListener() {
        progressBar.setOnSeekBarChangeListener(object : OnSeekBarChangeListener{
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                seekBar?.post {
                    updateThumbPosition()
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
            }

        })
    }

    fun updateThumbPosition(){
        val max = progressBar.max
        val progress = progressBar.progress
        val progressWidth = progressBar.width - progressBar.paddingStart - progressBar.paddingEnd
        val thumbWidth = ivThumb.width
        if (max > 0){
            val proportion = progress.toFloat() / max
            var marginLeft = (proportion * progressWidth - thumbWidth / 2).toInt()
            if (marginLeft < 0){
                marginLeft = 0
            }else if (marginLeft > progressWidth - thumbWidth){
                marginLeft = progressWidth - thumbWidth
            }
            val params = ivThumb.layoutParams as ConstraintLayout.LayoutParams
            params.setMargins(marginLeft,0,0,0)
            ivThumb.layoutParams = params
        }else{
            val params = ivThumb.layoutParams as ConstraintLayout.LayoutParams
            params.setMargins(0,0,0,0)
            ivThumb.layoutParams = params
        }
    }

}