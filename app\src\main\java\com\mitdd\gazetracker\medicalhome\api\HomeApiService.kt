package com.mitdd.gazetracker.medicalhome.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhome.bean.MedicalHomeProfile
import retrofit2.http.GET

/**
 * FileName: HomeApiService
 * Author by lilin,Date on 2024/11/27 11:10
 * PS: Not easy to write code, please indicate.
 */
interface HomeApiService {

    /**
     * 获取家庭版配置信息
     */
    @GET("dt/api/device/v1/profile/home")
    suspend fun getMedicalHomeProfile(): ApiResponse<MedicalHomeProfile>

}