package com.mitdd.gazetracker.medicalhome.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import retrofit2.http.GET

/**
 * FileName: MaskApiService
 * Author by lilin,Date on 2024/10/11 11:29
 * PS: Not easy to write code, please indicate.
 */
interface MaskApiService {

    /**
     * 获取今日数字遮盖疗法信息
     */
    @GET("dt/api/train/v1/today/occlusion-therapy")
    suspend fun getTodayOcclusionTherapy(): ApiResponse<CureInfo>

}