package com.mitdd.gazetracker.medicalhome.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhome.bean.TrainConfig
import com.mitdd.gazetracker.medicalhome.bean.VisionTherapy
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * FileName: TrainApiService
 * Author by lilin,Date on 2024/10/10 9:33
 * PS: Not easy to write code, please indicate.
 */
interface TrainApiService {

    /**
     * 获取今日视觉训练疗法信息
     */
    @GET("dt/api/train/v1/today/vision-therapy")
    suspend fun getTodayVisionTherapy(): ApiResponse<VisionTherapy>

    /**
     * 提交训练结果
     */
    @POST("dt/api/train/v1/result/submit")
    suspend fun submitTrainResult(@Body trainResultSubmit: RequestBody): ApiResponse<Any>

    /**
     * 获取训练配置
     */
    @GET("dt/api/train/v1/getTrainConfig")
    suspend fun getTrainConfig(
        @Query("trainId") trainId:Int
    ): ApiResponse<TrainConfig>

}