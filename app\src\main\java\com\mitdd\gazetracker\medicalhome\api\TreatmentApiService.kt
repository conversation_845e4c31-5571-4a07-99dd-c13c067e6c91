package com.mitdd.gazetracker.medicalhome.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhome.bean.CurrentTreatment
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.bean.Treatments
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.PUT
import retrofit2.http.Path

/**
 * FileName: TreatmentApiService
 * Author by lilin,Date on 2024/11/28 13:49
 * PS: Not easy to write code, please indicate.
 * 疗程API
 */
interface TreatmentApiService {

    /**
     * 获取当前疗程
     */
    @GET("dt/api/course/v1/current")
    suspend fun getCurrentTreatment(): ApiResponse<CurrentTreatment>

    @PUT("dt/api/course/v1/activate/{courseId}")
    suspend fun activationTreatment(@Path("courseId") courseId:String, @Body occlusionSectionReport: RequestBody): ApiResponse<TreatmentInfo>

    /**
     * 获取当前设备绑定用户的疗程列表
     */
    @GET("dt/api/course/v1/list")
    suspend fun getTreatments(): ApiResponse<Treatments>
}