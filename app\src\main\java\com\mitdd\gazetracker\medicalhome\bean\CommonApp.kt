package com.mitdd.gazetracker.medicalhome.bean

import android.content.pm.ApplicationInfo
import android.os.Parcelable
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType
import kotlinx.android.parcel.Parcelize

/**
 * FileName: CommonApp
 * Author by lilin,Date on 2024/8/23 17:45
 * PS: Not easy to write code, please indicate.
 * 常用APP
 */
@Parcelize
data class CommonApp(
    val type: CommonAppType,
    val applicationInfo: ApplicationInfo? = null
) : Parcelable
