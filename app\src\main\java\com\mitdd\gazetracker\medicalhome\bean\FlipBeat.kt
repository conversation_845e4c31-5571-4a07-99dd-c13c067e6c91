package com.mitdd.gazetracker.medicalhome.bean

import android.bluetooth.BluetoothDevice
import android.os.Parcelable
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import kotlinx.parcelize.Parcelize

/**
 * FileName: FlipBeat
 * Author by lilin,Date on 2024/10/24 16:38
 * PS: Not easy to write code, please indicate.
 * 翻转盘
 */
@Parcelize
data class FlipBeat(
    var state: FlipBeatState = FlipBeatState.DISCONNECTED,
    var flipDevice: BluetoothDevice? = null
): Parcelable
