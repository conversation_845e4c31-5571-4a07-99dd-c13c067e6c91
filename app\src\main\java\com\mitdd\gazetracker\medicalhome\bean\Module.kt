package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Module
 * Author by lilin,Date on 2024/11/27 10:45
 * PS: Not easy to write code, please indicate.
 * 首页模块
 */
@Parcelize
data class Module(
    //模块开通状态
    var moduleEnable:Boolean? = null,
    //模块代号
    var moduleKey:String? = null,
    //模块名称
    var moduleName:String? = null,
): Parcelable
