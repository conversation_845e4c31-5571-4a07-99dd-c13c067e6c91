package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: OcclusionTherapy
 * Author by lilin,Date on 2024/10/11 11:30
 * PS: Not easy to write code, please indicate.
 * 数字遮盖疗法信息
 */
@Parcelize
data class OcclusionTherapy(
    //遮盖模糊的通道对象 1-7
    var blurChannel:Int? = null,
    //	遮盖模糊模式，只支持0~3的整数值，表示4个可选模式。0：内部高斯模糊（默认模式），1：外部高斯模糊，2：内部区域置黑，3：外部区域置黑
    var blurMode:Int? = null,
    //模糊半径 0.5～5.5黄斑区的遮盖区域直径值
    var blurRadius:Float? = null,
    //模糊程度 5.0~50.0
    var blurSigma:Float? = null,
    //是否已经彻底完成
    var isFinishUp:Boolean? = null,
    //计划训练时长 秒
    var plannedDuration:Int? = null,
    //已训练时长 单位秒
    var trainingDuration:Int? = null,
    //弱势眼 左left 右right
    var position:String? = null,
): Parcelable
