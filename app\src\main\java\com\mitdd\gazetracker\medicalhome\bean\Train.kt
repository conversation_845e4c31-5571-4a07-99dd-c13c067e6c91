package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Train
 * Author by lilin,Date on 2024/10/9 20:51
 * PS: Not easy to write code, please indicate.
 * 训练
 */
@Parcelize
data class Train(
    //是否允许训练
    val allowTraining:Boolean? = null,
    //训练图标
    val icon:String? = null,
    //训练的在线访问地址
    val onlineUrl:String? = null,
    //训练id
    val trainId:Int? = null,
    //训练名称
    val trainName:String? = null,
    //计划训练时长 单位秒
    val plannedDuration:Int? = null,
    //已训练时长 单位秒
    val trainingDuration:Int? = null,
    //剩余时长 单位秒
    val remainingDuration:Int? = null,
    //限制类型 单次once 分类category 今日today,可用值:category,once,today
    val limitType:String? = null,
    //是否是眼动训练
    val isEyeMovementTraining:Boolean? = null,
    //是否需要翻转拍
    val mustBluetoothFlip:Boolean? = null,
    //是否需要红蓝眼镜
    val mustGlasses:Boolean? = null
): Parcelable{
    companion object{
        val EMPTY = Train()
    }
}
