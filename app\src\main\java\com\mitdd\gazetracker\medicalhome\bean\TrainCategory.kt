package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: TrainCategory
 * Author by lilin,Date on 2024/10/9 20:55
 * PS: Not easy to write code, please indicate.
 * 训练分类
 */
@Parcelize
data class TrainCategory(
    //是否允许训练
    var allowTraining:Boolean? = null,
    //分类编号
    var categoryCode:String? = null,
    //分类名称
    var categoryName:String? = null,
    //分类图标
    var icon:String? = null,
    //训练列表
    var items:List<Train>? = null,
    //计划训练时长 单位秒
    val plannedDuration:Int? = null,
    //已训练时长 单位秒
    val trainingDuration:Int? = null,
    //剩余时长 单位秒
    val remainingDuration:Int? = null
) : Parcelable{
    companion object{
        val EMPTY = TrainCategory()
    }
}
