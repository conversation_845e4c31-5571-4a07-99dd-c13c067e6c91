package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: TrainConfig
 * Author by lilin,Date on 2024/10/31 16:52
 * PS: Not easy to write code, please indicate.
 * 训练配置
 */
@Parcelize
data class TrainConfig(
    //训练id
    val trainId:Int? = null,
    //训练名称
    val trainName:String? = null,
    //训练的在线访问地址
    val onlineUrl:String? = null,
    //分类编号
    var categoryCode:String? = null,
): Parcelable
