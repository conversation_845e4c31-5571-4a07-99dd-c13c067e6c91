package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: TreatmentInfo
 * Author by lilin,Date on 2024/11/22 19:42
 * PS: Not easy to write code, please indicate.
 * 疗程信息
 */
@Parcelize
data class TreatmentInfo(
    //首次激活时间,yyyy-MM-dd HH:mm:ss
    val activationTime:String? = null,
    //数量(数量和单位合并=疗程周期)
    val amount:Int? = null,
    //课程id(卡号)
    val courseId:String? = null,
    //疗程状态 inactive未激活 running运行中 pending已暂停 completed已结束
    val courseStatus:String? = null,
    //创建时间(开卡时间),yyyy-MM-dd HH:mm:ss
    val createTime:String? = null,
    //过期日期,yyyy-MM-dd HH:mm:ss
    val dueTime:String? = null,
    //过期提醒时间,yyyy-MM-dd HH:mm:ss
    val dueRemindTime:String? = null,
    //时长单位 day、week、month、times
    val unit:String? = null,
    //时长单位名称 天、周、月、次数
    val unitName:String? = null,
    //已使用数量
    val used:Int? = null,
    //类型 1标题 2疗程
    var type:Int = -1
) : Parcelable{

    companion object{
        const val TYPE_TITLE = 1
        const val TYPE_TREATMENT = 2
    }
}
