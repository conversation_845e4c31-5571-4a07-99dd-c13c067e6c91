package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Treatments
 * Author by lilin,Date on 2024/11/23 9:52
 * PS: Not easy to write code, please indicate.
 * 疗程列表
 */
@Parcelize
data class Treatments(
    //当前页码
    val current:Int? = null,
    //总页数
    val pages:Int? = null,
    //每页显示条数
    val size:Int? = null,
    //总条数
    val total:Int? = null,
    //数据列表
    val records:List<TreatmentInfo>? = null
) : Parcelable
