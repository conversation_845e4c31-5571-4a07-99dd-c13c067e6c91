package com.mitdd.gazetracker.medicalhome.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: TodayVisionTherapy
 * Author by lilin,Date on 2024/10/9 21:00
 * PS: Not easy to write code, please indicate.
 * 视觉训练疗法
 */
@Parcelize
data class VisionTherapy(
    //是否允许训练
    var allowTraining:Boolean? = null,
    //是否显示为分类
    var isCategory:Boolean? = null,
    //训练列表
    var list:List<TrainCategory>? = null,
    //计划训练时长 秒
    var plannedDuration:Int? = null,
    //已训练时长 秒
    var trainingDuration:Int? = null
) : Parcelable
