package com.mitdd.gazetracker.medicalhome.dialog

import android.Manifest
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.bluetooth.BluetoothAdmin
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.PermissionUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.flipbeat.FlipBeatListener
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.mitdd.gazetracker.medicalhome.bean.FlipBeat
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import com.mitdd.gazetracker.medicalhome.train.ConnectFlipAdapter

/**
 * FileName: ConnectFlipDialog
 * Author by lilin,Date on 2024/10/23 11:51
 * PS: Not easy to write code, please indicate.
 * 连接蓝牙翻转拍弹窗
 */
class ConnectFlipDialog(context: Context) : BaseCommonDialog(context) {

    private val TAG = ConnectFlipDialog::class.java.simpleName

    private val ivClose by id<ImageView>(R.id.iv_close)
    private val ivRefreshFlip by id<ImageView>(R.id.iv_refresh_flip)
    private val rvFlip by id<RecyclerView>(R.id.rv_flip)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)
    private val tvNoAvailableDevice by id<TextView>(R.id.tv_no_available_device)
    private val ivScan by id<ImageView>(R.id.iv_scan)

    private val handler = Handler(Looper.getMainLooper())

    private var flipDevices: MutableList<FlipBeat> = mutableListOf()
    private val connectFlipAdapter = ConnectFlipAdapter(context,flipDevices)

    private val flipBeatListener = object : FlipBeatListener{
        override fun onConnectionStateChange(device: BluetoothDevice, state: FlipBeatState) {
            updateFlipDevice(device,state)
        }

        override fun onScanResult(device: BluetoothDevice) {
            addFlipDevice(device)
        }
    }
    private val noAvailableDeviceR = Runnable {
        stopScanFlip()
        showNoAvailableDevice(true)
    }

    private var mLoadingAnim: ObjectAnimator? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_connect_flip)
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        BluetoothAdmin.openBluetooth(context)

        initListener()
        initView()
        initData()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        handler.removeCallbacksAndMessages(null)
    }

    private fun initView() {
        rvFlip.layoutManager = LinearLayoutManager(context,RecyclerView.VERTICAL,false)
        rvFlip.adapter = connectFlipAdapter
    }

    private fun initData() {
        refreshFlipDevice()
    }

    private fun initListener(){

        setOnDismissListener {
            stopScanFlip()
            FlipBeatManager.unRegisterFlipBeatListener(flipBeatListener)
            handler.removeCallbacksAndMessages(null)
        }
        ivClose.setOnSingleClickListener {
            dismiss()
        }
        tvConfirm.setOnSingleClickListener {
            dismiss()
        }
        ivRefreshFlip.setOnSingleClickListener {
            refreshFlipDevice()
        }
        connectFlipAdapter.onItemClick = { flipBeat ->
            flipBeat.flipDevice?.let {
                if (flipBeat.state == FlipBeatState.CONNECTED){
                    disconnectFlipBeat()
                }else if (flipBeat.state == FlipBeatState.DISCONNECTED){
                    connectFlipBeat(it)
                }
            }
        }
        FlipBeatManager.registerFlipBeatListener(flipBeatListener)
    }

    //刷新翻转拍设备列表
    private fun refreshFlipDevice(){
        stopScanFlip()
        handler.removeCallbacks(noAvailableDeviceR)
        flipDevices.clear()
        connectFlipAdapter.notifyDataSetChanged()
        val connectedFlip = FlipBeatManager.getConnectedFlipBeat()
        if (connectedFlip != null){
            flipDevices.add(FlipBeat(FlipBeatState.CONNECTED,connectedFlip))
            connectFlipAdapter.notifyItemChanged(0)
        }
        startScanFlip()
    }

    //开始扫描翻转拍
    private fun startScanFlip(){
        if (flipDevices.isEmpty()){
            showScanLoading(true)
            handler.postDelayed(noAvailableDeviceR,10000)
        }
        FlipBeatManager.startScanFlip()
    }

    //停止扫描翻转拍
    private fun stopScanFlip(){
        FlipBeatManager.stopScanFlip()
    }

    //连接翻转拍
    private fun connectFlipBeat(device: BluetoothDevice){
        FlipBeatManager.connectFlipBeat(device)
    }

    private fun disconnectFlipBeat(){
        FlipBeatManager.disconnectFlipBeat()
    }

    private fun addFlipDevice(device: BluetoothDevice){
        if (PermissionUtils.checkSelfPermission(context,Manifest.permission.BLUETOOTH_CONNECT)){
            device.name?.let { name ->
                if (name.startsWith(FlipBeatManager.BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(FlipBeatManager.BLUETOOTH_FLIP_PREFIX_AF)){
                    val bluetoothDevice = flipDevices.find { flip ->
                        flip.flipDevice?.name == name
                    }
                    if (bluetoothDevice == null){
                        val isConnected = BluetoothAdmin.isBluetoothDeviceConnected(context, device)
                        if (isConnected){
                            flipDevices.add(FlipBeat(FlipBeatState.CONNECTED,device))
                        }else{
                            flipDevices.add(FlipBeat(FlipBeatState.DISCONNECTED,device))
                        }
                        handler.removeCallbacks(noAvailableDeviceR)
                        showNoAvailableDevice(false)
                        showScanLoading(false)
                        connectFlipAdapter.notifyItemChanged(flipDevices.size - 1)
                    }
                }
            }
        }
    }

    //更新列表中的翻转拍设备
    private fun updateFlipDevice(device: BluetoothDevice,state: FlipBeatState){
        if (PermissionUtils.checkSelfPermission(context,Manifest.permission.BLUETOOTH_CONNECT)){
            device.name?.let { name ->
                if (name.startsWith(FlipBeatManager.BLUETOOTH_FLIP_PREFIX_DM) || name.startsWith(FlipBeatManager.BLUETOOTH_FLIP_PREFIX_AF)){
                    val bluetoothDevice = flipDevices.find {
                        it.flipDevice?.name == name
                    }
                    if (bluetoothDevice != null){
                        bluetoothDevice.state = state
                        bluetoothDevice.flipDevice = device
                        val position = flipDevices.indexOf(bluetoothDevice)
                        if (position in flipDevices.indices){
                            connectFlipAdapter.notifyItemChanged(position)
                        }
                    }else{
                        flipDevices.add(FlipBeat(state,device))
                        connectFlipAdapter.notifyItemChanged(flipDevices.size - 1)
                    }
                }
            }
        }
    }

    private fun initLoadingAnim(){
        mLoadingAnim = ObjectAnimator.ofFloat(ivScan, "rotation", 0f, 359f)
        mLoadingAnim?.repeatCount = ValueAnimator.INFINITE
        mLoadingAnim?.duration = 2000
        mLoadingAnim?.interpolator = LinearInterpolator()
    }

    private fun showScanLoading(isShow:Boolean){
        if (isShow){
            showNoAvailableDevice(false)
            ivScan.isVisible = true
            initLoadingAnim()
            mLoadingAnim?.start()
        }else{
            ivScan.isVisible = false
            if (mLoadingAnim?.isRunning == true){
                mLoadingAnim?.pause()
            }
        }
    }

    private fun showNoAvailableDevice(isShow:Boolean){
        if (isShow){
            showScanLoading(false)
        }
        tvNoAvailableDevice.isVisible = isShow
    }

}