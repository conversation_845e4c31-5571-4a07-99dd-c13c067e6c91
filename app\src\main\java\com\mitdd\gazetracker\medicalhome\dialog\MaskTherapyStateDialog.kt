package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import androidx.core.graphics.toColorInt
import androidx.core.graphics.drawable.toDrawable

/**
 * FileName: MaskTherapyStateDialog
 * Author by lilin,Date on 2024/10/14 19:56
 * PS: Not easy to write code, please indicate.
 * 遮盖疗法开启或关闭通知弹窗
 */
class MaskTherapyStateDialog(context: Context) : BaseCommonDialog(context) {

    private val tvTitle by id<TextView>(R.id.tv_title)
    private val tvSubtitle by id<TextView>(R.id.tv_subtitle)
    private val ivClose by id<ImageView>(R.id.iv_close)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val ivGlasses by id<ImageView>(R.id.iv_glasses)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_mask_therapy_state)

        initView()
    }

    private fun initView() {
        ivClose.setOnSingleClickListener {
            dismiss()
        }
        tvOk.setOnSingleClickListener {
            dismiss()
        }
    }

    fun setData(isStart:Boolean,eye: AmblyopicEye){
        if (isStart){
            setTitle(context.getString(R.string.str_airdoc_masking_therapy_on))
            when(eye){
                AmblyopicEye.LEFT -> {
                    setGlasses(R.drawable.icon_left_blue_right_red_glasses_white)
                    setSubtitleBySpannable(context.getString(R.string.str_please_wear_blue_red_glasses_watch_content))
                }
                AmblyopicEye.RIGHT -> {
                    setGlasses(R.drawable.icon_left_red_right_blue_glasses_white)
                    setSubtitleBySpannable(context.getString(R.string.str_please_wear_red_blue_glasses_watch_content))
                }
            }
            PlayManager.playRawMedia(context, RawMedia(R.raw.mask_therapy_started_please_wear_glasses))
        }else{
            setTitle(context.getString(R.string.str_airdoc_masking_therapy_off))
            setSubtitle(context.getString(R.string.str_please_cancel_red_blue_glasses))
            when(eye){
                AmblyopicEye.LEFT -> {
                    setGlasses(R.drawable.icon_left_blue_right_red_glasses_white)
                }
                AmblyopicEye.RIGHT -> {
                    setGlasses(R.drawable.icon_left_red_right_blue_glasses_white)
                }
            }
            PlayManager.playRawMedia(context, RawMedia(R.raw.mask_therapy_closed))
        }
    }

    fun setTitle(title:String){
        tvTitle.text = title
    }

    fun setSubtitleBySpannable(subtitle:String){
        val spannableString = SpannableString(subtitle)
        spannableString.setSpan(ForegroundColorSpan("#EB4E89".toColorInt()),3,7, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        tvSubtitle.text = spannableString
    }

    fun setSubtitle(subtitle:String){
        tvSubtitle.text = subtitle
    }

    fun setGlasses(res:Int){
        ivGlasses.setImageResource(res)
    }

    override fun isFullScreen(): Boolean {
        return true
    }
}