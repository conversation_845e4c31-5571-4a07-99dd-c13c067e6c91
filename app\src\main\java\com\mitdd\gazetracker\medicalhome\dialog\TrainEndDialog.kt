package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R

/**
 * FileName: TrainEndDialog
 * Author by lilin,Date on 2024/11/23 14:32
 * PS: Not easy to write code, please indicate.
 * 训练结束弹窗
 */
class TrainEndDialog(context: Context) : BaseCommonDialog(context) {

    private val tvTrainName by id<TextView>(R.id.tv_train_name)
    private val tvTrainTime by id<TextView>(R.id.tv_train_time)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_train_end)

        val lp = window?.attributes
        lp?.gravity = Gravity.TOP
        lp?.y = 20.dp2px(context)
        window?.attributes = lp

        initView()
    }

    private fun initView() {
        Handler(Looper.getMainLooper()).postDelayed({
            dismiss()
        },3000)
    }

    fun setData(name:String,time:Long){
        tvTrainName.text = name
        val minutes = time / 1000 / 60
        val seconds = time / 1000 % 60
        tvTrainTime.text = context.getString(R.string.str_training_time_m_s,minutes,seconds)
    }

}