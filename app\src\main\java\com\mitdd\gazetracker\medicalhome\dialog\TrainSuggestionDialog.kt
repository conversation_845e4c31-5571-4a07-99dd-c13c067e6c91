package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import kotlinx.coroutines.Job

/**
 * FileName: TrainSuggestionDialog
 * Author by lilin,Date on 2024/10/11 10:14
 * PS: Not easy to write code, please indicate.
 * 训练建议弹窗
 */
class TrainSuggestionDialog(context: Context) : BaseCommonDialog(context) {

    private val tvPrompt by id<TextView>(R.id.tv_prompt)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    var onConfirmClick:(() -> Unit)? = null
    private var countdownJob:Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_train_suggestion)
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
    }

    private fun initView() {
        tvConfirm.setOnSingleClickListener {
            countdownJob?.cancel()
            countdownJob = null
            onConfirmClick?.invoke()
            dismiss()
        }
    }

    fun setPrompt(prompt:String){
        tvPrompt.text = prompt
    }

    /**
     * @param countDown 倒计时时间 毫秒
     * @param interval 倒计时间隔时间 毫秒
     */
    fun startCountDown(lifecycleOwner: LifecycleOwner, countDown:Int,interval:Int = 1000){
        countdownJob = countdown(
            countDown.toLong(), interval.toLong(), lifecycleOwner,
            onTick = {
                tvConfirm.text = context.getString(R.string.str_ok_int,it / 1000)
            },
            onCompletion = {
                if (it == null){
                    onConfirmClick?.invoke()
                    dismiss()
                }
            })
    }
}