package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.widget.SeparatedEditText
import com.mitdd.gazetracker.utils.CommonUtils

/**
 * FileName: TreatmentActivationDialog
 * Author by lilin,Date on 2024/11/22 11:08
 * PS: Not easy to write code, please indicate.
 * 疗程激活弹窗
 */
class TreatmentActivationDialog(context: Context,private val phone:String)
    : BaseCommonDialog(context) {

    private val TAG = TreatmentActivationDialog::class.java.simpleName

    private val tvNumber1 by id<TextView>(R.id.tv_number1)
    private val tvNumber2 by id<TextView>(R.id.tv_number2)
    private val tvNumber3 by id<TextView>(R.id.tv_number3)
    private val tvNumber4 by id<TextView>(R.id.tv_number4)
    private val tvNumber5 by id<TextView>(R.id.tv_number5)
    private val tvNumber6 by id<TextView>(R.id.tv_number6)
    private val tvNumber7 by id<TextView>(R.id.tv_number7)
    private val etPhoneTailNumber by id<SeparatedEditText>(R.id.et_phone_tail_number)
    private val tvErrorPrompt by id<TextView>(R.id.tv_error_prompt)
    private val tvCancel by id<TextView>(R.id.tv_cancel)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    //手机号尾号
    private var mTailNumber = ""
    //输入的手机号尾号
    private var mEnterTailNumber = ""

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_activation)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()

        initData()
    }

    private fun initData() {
        Logger.d(TAG, msg = "initData phone = $phone")
        if (CommonUtils.isValidChinesePhoneNumber(phone)){
            tvNumber1.text = phone.substring(0,1)
            tvNumber2.text = phone.substring(1,2)
            tvNumber3.text = phone.substring(2,3)
            tvNumber4.text = phone.substring(3,4)
            tvNumber5.text = phone.substring(4,5)
            tvNumber6.text = phone.substring(5,6)
            tvNumber7.text = phone.substring(6,7)
            mTailNumber = phone.substring(7)
        }else{
            tvErrorPrompt.isVisible = true
            tvErrorPrompt.text = context.getString(R.string.str_reserved_phone_number_invalid,phone)
        }
    }

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }

    private fun initListener() {
        tvCancel.setOnSingleClickListener {
            onCancelClick?.invoke()
            dismiss()
        }
        tvConfirm.setOnSingleClickListener {
            if (checkTailNumber(mEnterTailNumber)){
                onConfirmClick?.invoke()
                dismiss()
            }else{
                tvErrorPrompt.isVisible = true
                tvErrorPrompt.text = context.getString(R.string.str_end_number_incorrectly_entered)
            }
        }
        etPhoneTailNumber.setTextChangedListener(object :SeparatedEditText.TextChangedListener{
            override fun textChanged(changeText: CharSequence) {
                mEnterTailNumber = changeText.toString()
                tvErrorPrompt.isVisible = false
            }

            override fun textCompleted(text: CharSequence) {
                mEnterTailNumber = text.toString()
                tvErrorPrompt.isVisible = false
                if (!checkTailNumber(mEnterTailNumber)){
                    tvErrorPrompt.isVisible = true
                    tvErrorPrompt.text = context.getString(R.string.str_end_number_incorrectly_entered)
                }
            }

        })
    }

    private fun checkTailNumber(tailNumber:String):Boolean{
        return mTailNumber == tailNumber
    }

}