package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R

/**
 * FileName: TreatmentActivationRemindDialog
 * Author by lilin,Date on 2024/11/27 16:40
 * PS: Not easy to write code, please indicate.
 * 疗程激活提醒弹窗
 */
class TreatmentActivationRemindDialog(context: Context, private val cardNumber:String, private val period:String)
    : BaseCommonDialog(context) {

    private val TAG = TreatmentActivationRemindDialog::class.java.simpleName

    private val tvCardNumber by id<TextView>(R.id.tv_card_number)
    private val tvPeriod by id<TextView>(R.id.tv_period)
    private val tvCancel by id<TextView>(R.id.tv_cancel)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_activation_remind)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()

        initData()
    }

    private fun initData(){
        Logger.d(TAG, msg = "initData cardNumber = $cardNumber, period = $period")

        tvCardNumber.text = context.getString(R.string.str_card_number_s,cardNumber)
        tvPeriod.text = context.getString(R.string.str_treatment_period_s,period)
    }

    /**
     * 设置确定按钮点击监听
     */
    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }

    private fun initListener() {
        tvCancel.setOnSingleClickListener {
            onCancelClick?.invoke()
            dismiss()
        }
        tvConfirm.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

}