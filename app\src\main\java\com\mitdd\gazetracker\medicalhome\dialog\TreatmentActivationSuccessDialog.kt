package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R

/**
 * FileName: TreatmentActivationSuccessDialog
 * Author by lilin,Date on 2024/11/22 17:14
 * PS: Not easy to write code, please indicate.
 * 疗程激活成功
 */
class TreatmentActivationSuccessDialog(context: Context,private val dueTime:String,private val hospitalName:String)
    : BaseCommonDialog(context) {

    private val TAG = TreatmentActivationSuccessDialog::class.java.simpleName

    private val tvReviewDate by id<TextView>(R.id.tv_review_date)
    private val tvHospitalName by id<TextView>(R.id.tv_hospital_name)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    private var onConfirmClick:(() -> Unit)? = null

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_activation_successful)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()

        initData()

    }

    private fun initData() {

        Logger.d(TAG, msg = "initData dueTime = $dueTime, hospitalName = $hospitalName")

        tvReviewDate.text = context.getString(R.string.str_expiration_date_s,dueTime)
        tvHospitalName.text = context.getString(R.string.str_hospital_name,hospitalName)
    }

    private fun initListener() {
        tvConfirm.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

}