package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.airdoc.component.media.PlayManager
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.utils.LocaleManager
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.media.bean.UrlMedia

/**
 * FileName: TreatmentExpirationDialog
 * Author by lilin,Date on 2024/11/22 17:31
 * PS: Not easy to write code, please indicate.
 * 疗程到期弹窗
 */
class TreatmentExpirationDialog(context: Context,private val dueTime:String,private val hospitalName:String)
    : BaseCommonDialog(context) {

    private val TAG = TreatmentExpirationDialog::class.java.simpleName

    private val tvReviewDate by id<TextView>(R.id.tv_review_date)
    private val tvHospitalName by id<TextView>(R.id.tv_hospital_name)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_expiration)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()
        initData()
    }

    private fun initData() {

        Logger.d(TAG, msg = "initData dueTime = $dueTime, hospitalName = $hospitalName")

        tvReviewDate.text = context.getString(R.string.str_expiration_date_s,dueTime)
        tvHospitalName.text = context.getString(R.string.str_hospital_name,hospitalName)

        if (LocaleManager.getLanguage() == "en"){
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/treatment_expiration.wav"))
        }else{
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/treatment_expiration.wav"))
        }
    }

    private fun initListener() {
        tvConfirm.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

}