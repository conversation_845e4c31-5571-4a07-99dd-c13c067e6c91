package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.R
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.UrlMedia
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.user.UserPreference
import com.mitdd.gazetracker.utils.LocaleManager
import java.time.ZoneId
import androidx.core.graphics.toColorInt
import androidx.core.graphics.drawable.toDrawable

/**
 * FileName: TreatmentExpirationRemindDialog
 * Author by lilin,Date on 2024/11/22 16:19
 * PS: Not easy to write code, please indicate.
 * 疗程到期提醒弹窗
 */
class TreatmentExpirationRemindDialog(context: Context, private val days:Int,private val dueTime:String,private val hospitalName:String)
    : BaseCommonDialog(context) {

    private val TAG = TreatmentExpirationRemindDialog::class.java.simpleName

    private val tvExpirationDays by id<TextView>(R.id.tv_expiration_days)
    private val tvDueDate by id<TextView>(R.id.tv_due_date)
    private val tvHospitalName by id<TextView>(R.id.tv_hospital_name)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_expiration_remind)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()
        initData()
    }

    private fun initData(){

        val showDate = TimeUtils.parseTimeToTimeString(System.currentTimeMillis(), "yyyy-MM-dd",
            ZoneId.of("Asia/Shanghai"))
        MMKVManager.encodeString(UserPreference.SHOW_TREATMENT_EXPIRATION_REMIND_DIALOG_DATE,showDate)

        Logger.d(TAG, msg = "initData days = $days, dueTime = $dueTime, hospitalName = $hospitalName, showDate = $showDate")

        val spannableString = SpannableString(context.getString(R.string.str_treatment_due_in_days,days))
        spannableString.setSpan(ForegroundColorSpan("#EB4E89".toColorInt()),5,6, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        tvExpirationDays.text = spannableString

        tvDueDate.text = context.getString(R.string.str_expiration_date_s,dueTime)
        tvHospitalName.text = context.getString(R.string.str_hospital_name,hospitalName)

        if (LocaleManager.getLanguage() == "en"){
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/treatment_expiration_remind.wav"))
        }else{
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/treatment_expiration_remind.wav"))
        }

    }

    private fun initListener() {
        tvConfirm.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

}