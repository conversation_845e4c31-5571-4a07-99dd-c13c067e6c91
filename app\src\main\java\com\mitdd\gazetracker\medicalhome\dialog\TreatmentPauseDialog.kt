package com.mitdd.gazetracker.medicalhome.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.UrlMedia
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.utils.LocaleManager
import androidx.core.graphics.drawable.toDrawable

/**
 * FileName: TreatmentPauseDialog
 * Author by lilin,Date on 2024/11/22 17:38
 * PS: Not easy to write code, please indicate.
 * 疗程暂停弹窗
 */
class TreatmentPauseDialog(context: Context,private val hospitalName:String) : BaseCommonDialog(context) {

    private val TAG = TreatmentPauseDialog::class.java.simpleName
    private val tvHospitalName by id<TextView>(R.id.tv_hospital_name)
    private val tvCancel by id<TextView>(R.id.tv_cancel)
    private val tvConfirm by id<TextView>(R.id.tv_confirm)

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_treatment_pause)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()

        initDate()
    }

    private fun initDate(){
        Logger.d(TAG, msg = "initDate hospitalName = $hospitalName")

        tvHospitalName.text = context.getString(R.string.str_hospital_name,hospitalName)
        
        if (LocaleManager.getLanguage() == "en"){
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/treatment_pause.wav"))
        }else{
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/treatment_pause.wav"))
        }
    }

    private fun initListener() {
        tvCancel.setOnSingleClickListener {
            onCancelClick?.invoke()
            dismiss()
        }
        tvConfirm.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }

}