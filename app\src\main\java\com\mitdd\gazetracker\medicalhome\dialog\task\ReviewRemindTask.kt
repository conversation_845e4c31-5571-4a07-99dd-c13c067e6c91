package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.ReviewRemindDialog

/**
 * FileName: ReviewRemindTask
 * Author by lilin,Date on 2024/11/27 17:56
 * PS: Not easy to write code, please indicate.
 * 复查提醒弹窗task
 */
class ReviewRemindTask(val context: Context, private val days:Int, private val reviewDate:String, private val hospitalName:String)
    : DialogTask(DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onConfirmClick:(() -> Unit)? = null

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    override fun doTask() {
        val reviewRemindDialog = ReviewRemindDialog(context, days, reviewDate, hospitalName).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
        }
        reviewRemindDialog.show()
    }
}