package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.TreatmentActivationRemindDialog

/**
 * FileName: TreatmentActivationRemindTask
 * Author by lilin,Date on 2024/11/27 17:20
 * PS: Not easy to write code, please indicate.
 * 疗程激活提醒弹窗task
 */
class TreatmentActivationRemindTask(val context: Context, private val cardNumber:String, private val period:String)
    : DialogTask(DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    override fun doTask() {
        val treatmentActivationRemindDialog = TreatmentActivationRemindDialog(context,cardNumber,period).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
            setCancelClick(onCancelClick)
        }
        treatmentActivationRemindDialog.show()
    }

    /**
     * 设置确定按钮点击监听
     */
    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }

}