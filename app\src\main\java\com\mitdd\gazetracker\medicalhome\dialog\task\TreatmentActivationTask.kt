package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.TreatmentActivationDialog

/**
 * FileName: TreatmentActivationTask
 * Author by lilin,Date on 2024/11/27 17:10
 * PS: Not easy to write code, please indicate.
 * 疗程激活弹窗task
 */
class TreatmentActivationTask(val context: Context, private val phone:String) : DialogTask(
    DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    override fun doTask() {
        val treatmentActivationDialog = TreatmentActivationDialog(context,phone).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
            setCancelClick(onCancelClick)
        }
        treatmentActivationDialog.show()
    }

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }
}