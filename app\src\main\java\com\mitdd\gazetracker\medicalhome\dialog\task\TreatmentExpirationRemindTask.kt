package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.TreatmentExpirationRemindDialog

/**
 * FileName: TreatmentExpirationRemindTask
 * Author by lilin,Date on 2024/11/27 17:44
 * PS: Not easy to write code, please indicate.
 * 疗程到期提醒弹窗task
 */
class TreatmentExpirationRemindTask(val context: Context, private val days:Int,private val dueTime:String,private val hospitalName:String)
    : DialogTask(DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onConfirmClick:(() -> Unit)? = null

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    override fun doTask() {
        val treatmentExpirationRemindDialog = TreatmentExpirationRemindDialog(context, days, dueTime, hospitalName).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
        }
        treatmentExpirationRemindDialog.show()
    }
}