package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.TreatmentExpirationDialog

/**
 * FileName: TreatmentExpirationTask
 * Author by lilin,Date on 2024/11/27 17:39
 * PS: Not easy to write code, please indicate.
 */
class TreatmentExpirationTask(val context: Context, private val dueTime:String,private val hospitalName:String)
    : DialogTask(DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onConfirmClick:(() -> Unit)? = null

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    override fun doTask() {
        val treatmentExpirationDialog = TreatmentExpirationDialog(context,dueTime, hospitalName).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
        }
        treatmentExpirationDialog.show()
    }
}