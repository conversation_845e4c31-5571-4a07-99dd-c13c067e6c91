package com.mitdd.gazetracker.medicalhome.dialog.task

import android.content.Context
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.common.dialog.task.DialogTask
import com.mitdd.gazetracker.medicalhome.dialog.TreatmentPauseDialog

/**
 * FileName: TreatmentPauseTask
 * Author by lilin,Date on 2024/11/27 17:51
 * PS: Not easy to write code, please indicate.
 */
class TreatmentPauseTask(val context: Context, private val hospitalName:String)
    : DialogTask(DialogTaskManager.DIALOG_PRIORITY_NOTIFICATION) {

    private var onCancelClick:(() -> Unit)? = null
    private var onConfirmClick:(() -> Unit)? = null

    fun setConfirmClick(confirmClick:(() -> Unit)?){
        onConfirmClick = confirmClick
    }

    fun setCancelClick(cancelClick:(() -> Unit)?){
        onCancelClick = cancelClick
    }

    override fun doTask() {
        val treatmentPauseDialog = TreatmentPauseDialog(context, hospitalName).apply {
            setOnShowListener {
                callback?.onShow()
            }
            setOnDismissListener {
                callback?.onDismiss()
            }
            setConfirmClick(onConfirmClick)
            setCancelClick(onCancelClick)
        }
        treatmentPauseDialog.show()
    }

}