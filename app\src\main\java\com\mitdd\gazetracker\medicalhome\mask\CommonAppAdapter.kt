package com.mitdd.gazetracker.medicalhome.mask

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType.*
import com.mitdd.gazetracker.medicalhome.bean.CommonApp

/**
 * FileName: CommonAppAdapter
 * Author by lilin,Date on 2024/8/23 17:37
 * PS: Not easy to write code, please indicate.
 */
class CommonAppAdapter(private var commonApps: MutableList<CommonApp>) : RecyclerView.Adapter<CommonAppAdapter.CommonAppHolder>() {

    private var mListener: ItemClickListener? = null

    fun setItemClickListener(listener: ItemClickListener){
        mListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CommonAppHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_common_app, parent, false)
        return CommonAppHolder(view)
    }

    override fun getItemCount(): Int {
        return commonApps.size
    }

    override fun onBindViewHolder(holder: CommonAppHolder, position: Int) {
        if (position in commonApps.indices){
            holder.bind(commonApps[position])
        }
    }

    inner class CommonAppHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val ivAppIcon: ImageView = itemView.findViewById(R.id.iv_app_icon)
        private val tvAppName: TextView = itemView.findViewById(R.id.tv_app_name)
        private val ivDelete: ImageView = itemView.findViewById(R.id.iv_delete)

        init {
            itemView.setOnSingleClickListener {
                mListener?.onItemClick(bindingAdapterPosition)
            }
            itemView.setOnLongClickListener {
                val commonApp = commonApps[bindingAdapterPosition]
                if (commonApp.type == APP){
                    ivDelete.isVisible = !ivDelete.isVisible
                }
                true
            }
            ivDelete.setOnSingleClickListener {
                ivDelete.isVisible = false
                mListener?.onItemDelete(bindingAdapterPosition)
            }
        }

        fun bind(commonApp: CommonApp){
            when(commonApp.type){
                APP -> {
                    val packageManager = itemView.context.packageManager
                    commonApp.applicationInfo?.let {
                        tvAppName.text = it.loadLabel(packageManager)
                        val loadIcon = it.loadIcon(packageManager)
                        if (loadIcon != null){
                            ivAppIcon.setImageDrawable(loadIcon)
                        }else{
                            val loadLogo = it.loadLogo(packageManager)
                            if (loadLogo != null){
                                ivAppIcon.setImageDrawable(loadLogo)
                            }else{
                                ivAppIcon.setImageResource(it.icon)
                            }
                        }
                    }
                }
                ADD_APP -> {
                    ivAppIcon.setImageResource(R.drawable.icon_add_common_app)
                    tvAppName.text = itemView.context.getString(R.string.str_add)
                }
            }
        }
    }

    interface ItemClickListener{
        fun onItemClick(position: Int)
        fun onItemDelete(position: Int)
    }

}