package com.mitdd.gazetracker.medicalhome.mask

import android.content.pm.ApplicationInfo
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: InstallAppAdapter
 * Author by lilin,Date on 2024/8/26 10:23
 * PS: Not easy to write code, please indicate.
 */
class InstallAppAdapter : RecyclerView.Adapter<InstallAppAdapter.InstallAppHolder>(){

    private var applicationInfoList: MutableList<ApplicationInfo> = mutableListOf()

    fun setInstallAppData(data:List<ApplicationInfo>){
        applicationInfoList.clear()
        applicationInfoList.addAll(data)
    }

    private var mListener: ItemClickListener? = null

    fun setItemClickListener(listener: ItemClickListener){
        mListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InstallAppHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_common_app, parent, false)
        return InstallAppHolder(view)
    }

    override fun getItemCount(): Int {
        return applicationInfoList.size
    }

    override fun onBindViewHolder(holder: InstallAppHolder, position: Int) {
        if (position in applicationInfoList.indices){
            holder.bind(applicationInfoList[position])
        }
    }

    inner class InstallAppHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val ivAppIcon: ImageView = itemView.findViewById(R.id.iv_app_icon)
        private val tvAppName: TextView = itemView.findViewById(R.id.tv_app_name)

        init {
            itemView.setOnSingleClickListener {
                mListener?.onItemClick(adapterPosition)
            }
        }

        fun bind(applicationInfo: ApplicationInfo){
            val packageManager = itemView.context.packageManager
            tvAppName.text = applicationInfo.loadLabel(packageManager)
            val loadIcon = applicationInfo.loadIcon(packageManager)
            if (loadIcon != null){
                ivAppIcon.setImageDrawable(loadIcon)
            }else{
                val loadLogo = applicationInfo.loadLogo(packageManager)
                if (loadLogo != null){
                    ivAppIcon.setImageDrawable(loadLogo)
                }else{
                    ivAppIcon.setImageResource(applicationInfo.icon)
                }
            }
        }
    }

    interface ItemClickListener{
        fun onItemClick(position: Int)
    }

}