package com.mitdd.gazetracker.medicalhome.mask

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: GazeTrackPreference
 * Author by lilin,Date on 2024/8/6 16:40
 * PS: Not easy to write code, please indicate.
 */
enum class MaskPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * 遮盖疗法-遮盖区域 0.5～5.5黄斑区的遮盖区域直径值
     */
    MT_COVER_AREA(null),

    /**
     * 遮盖疗法-遮盖幅度
     */
    MT_COVER_RANGE(null),

    /**
     * 遮盖疗法-遮盖模糊的通道对象 1-7
     */
    MT_COVER_CHANNEL(null),

    /**
     * 遮盖疗法-遮盖模糊模式，只支持0~3的整数值，表示4个可选模式。0：内部高斯模糊（默认模式），1：外部高斯模糊，2：内部区域置黑，3：外部区域置黑
     */
    MT_COVER_MODE(null),

    /**
     * 遮盖疗法-弱视眼
     */
    MT_AMBLYOPIC_EYE(null);

    override fun getNameSpace(): String {
        return "GazeTrackPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}