package com.mitdd.gazetracker.medicalhome.mask

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ApplicationInfo
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.common.dialog.task.NotificationDialogTask
import com.mitdd.gazetracker.common.widget.CommonAppView
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.medicalhome.HomeMainActivity
import com.mitdd.gazetracker.medicalhome.TimeProgress
import com.mitdd.gazetracker.medicalhome.dialog.MaskTherapyStateDialog
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.vm.MaskViewModel
import com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentManager
import com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.Gender


/**
 * FileName: TreatmentFragment
 * Author by lilin,Date on 2024/8/21 16:03
 * PS: Not easy to write code, please indicate.
 * 数字遮盖疗法
 */
class MaskTherapyFragment : BaseCommonFragment() {

    companion object{
        private val TAG = MaskTherapyFragment::class.java.simpleName
        const val INPUT_PARAM_MODULE_NAME = "moduleName"
        const val INPUT_PARAM_IS_FULL = "isFull"

        fun newInstance(moduleName:String,isFull:Boolean): MaskTherapyFragment {
            val fragment = MaskTherapyFragment()
            val args = Bundle()
            args.putString(INPUT_PARAM_MODULE_NAME,moduleName)
            args.putBoolean(INPUT_PARAM_IS_FULL,isFull)
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_mask_therapy
    }

    private val tvDigitalMaskTherapy by id<TextView>(R.id.tv_digital_mask_therapy)
    private val switchMaskTherapy by id<SwitchCompat>(R.id.switch_mask_therapy)
    private val ivRedBlueGlasses by id<ImageView>(R.id.iv_red_blue_glasses)
    private val timeProgress by id<TimeProgress>(R.id.treatment_time_progress)
    private val commonAppView by id<CommonAppView>(R.id.common_app_view)

    //选择常用应用
    private var selectCommonAppLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val applicationInfo = data?.getParcelableExtra<ApplicationInfo>(SelectCommonAppActivity.OUTPUT_PARAM_COMMON_APP)
            if (applicationInfo != null){
                commonAppView.addCommonApp(applicationInfo)
            }
        }
    }

    private val treatmentVM by activityViewModels<TreatmentViewModel>()
    private val maskVM by activityViewModels<MaskViewModel>()

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    //模块名称
    private var mModuleName = ""
    private var isFUll = false

    override fun initParam() {
        super.initParam()
        val arguments = arguments
        if (arguments != null){
            mModuleName = arguments.getString(INPUT_PARAM_MODULE_NAME,getString(R.string.str_digital_masking_therapy))
            isFUll = arguments.getBoolean(INPUT_PARAM_IS_FULL)
        }
    }

    override fun initView() {
        super.initView()

        initListener()

        val accountInfo = UserManager.getAccountInfo()
        when(accountInfo?.gender){
            Gender.MALE.value ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_male)
            }
            else ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_female)
            }
        }
        timeProgress.getSeekBar().progressDrawable = ContextCompat.getDrawable(mActivity,R.drawable.seekbar_mask_therapy_duration_progress_drawable)

        tvDigitalMaskTherapy.text = mModuleName

        val spanCount = if (isFUll) 8 else 4
        commonAppView.setSpanCount(spanCount)
    }

    override fun initObserver() {
        super.initObserver()
        treatmentVM.curTreatmentLiveData.observe(this){
            maskVM.getTodayOcclusionTherapy()
        }
        maskVM.occlusionTherapyLiveData.observe(this){
            updateEyePosition()
            updatePlannedDuration()
            updateTreatmentDuration()
        }
        LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_CURE).observe(this){
            switchMaskTherapy(it)
        }
        maskVM.mtStateLiveData.observe(this){
            switchMaskTherapy.isChecked = it
            ivRedBlueGlasses.isVisible = it
        }
    }

    override fun initData() {
        super.initData()
    }

    private fun initListener(){
        switchMaskTherapy.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            switchMaskTherapy(isChecked)
        }
        commonAppView.onAddCommonApp = {
            selectCommonAppLauncher.launch(
                SelectCommonAppActivity.createIntent(mActivity)
            )
        }
    }

    private fun showNotificationDialog(prompt:String,confirmClick:(() -> Unit)? = null){
        (mActivity as HomeMainActivity).mainDialogTaskManager.addTask(NotificationDialogTask(mActivity).apply {
            setConfirmClick(confirmClick)
            setMessage(prompt)
        })
    }

    /**
     * 更新计划治疗时长
     */
    private fun updatePlannedDuration(){
        timeProgress.getPlannedDurationView().isVisible = maskVM.occlusionTherapy != null
        timeProgress.setPlannedDuration(maskVM.plannedDuration)
    }

    /**
     * 更新已治疗
     */
    private fun updateTreatmentDuration(){
        timeProgress.getTrainDurationView().isVisible = maskVM.occlusionTherapy != null
        timeProgress.setTreatmentDuration(maskVM.treatmentDuration)
    }

    private fun updateEyePosition(){
        when(maskVM.eyePosition){
            AmblyopicEye.LEFT.value ->{
                ivRedBlueGlasses.setImageResource(R.drawable.icon_left_blue_right_red_glasses_small)
            }
            AmblyopicEye.RIGHT.value ->{
                ivRedBlueGlasses.setImageResource(R.drawable.icon_left_red_right_blue_glasses_small)
            }
        }
    }

    private fun switchMaskTherapy(state:Boolean){
        Logger.d(TAG, msg = "switchMaskTherapy state = $state")
        if (!state){
            switchMaskTherapy.isChecked = false
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
            return
        }
        val isBind = UserManager.isBind()
        if (!isBind){
            switchMaskTherapy.isChecked = false
            showNotificationDialog(getString(R.string.str_please_bind_user_then_start_masking_therapy)){
                (mActivity as? HomeMainActivity)?.toBind()
            }
            return
        }
        val treatmentInfo = UserManager.getTreatmentInfo()
        if (maskVM.occlusionTherapy == null || treatmentInfo == null){
            switchMaskTherapy.isChecked = false
            showNotificationDialog(getString(R.string.str_no_treatment_available_please_contact_doctor))
            return
        }
        val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
        when (treatmentInfo.courseStatus){
            TreatmentStatus.INACTIVE.called ->{
                switchMaskTherapy.isChecked = false
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentActivationRemindDialog(mActivity,dialogTaskManager,treatmentInfo, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
                return
            }
            TreatmentStatus.PENDING.called ->{
                switchMaskTherapy.isChecked = false
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentPauseDialog(mActivity,dialogTaskManager, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
                return
            }
            TreatmentStatus.COMPLETED.called ->{
                switchMaskTherapy.isChecked = false
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentExpirationDialog(mActivity,dialogTaskManager,treatmentInfo)
                }
                return
            }
        }
        val isFinishUp = maskVM.isFinishUp
        val plannedDuration = maskVM.plannedDuration
        val treatmentDuration = maskVM.treatmentDuration
        if (isFinishUp || plannedDuration <= treatmentDuration){
            switchMaskTherapy.isChecked = false
            showNotificationDialog(getString(R.string.str_treatment_has_been_completed_today))
            return
        }
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_CURE
                data.putInt(GazeConstants.KEY_PLANNED_DURATION,plannedDuration)
                data.putInt(GazeConstants.KEY_TREATMENT_DURATION,treatmentDuration)
                data.putString(GazeConstants.KEY_REPORT_URL,"dt/api/train/v1/occlusion-therapy/event/report")
                data.putString(GazeConstants.KEY_BASE_URL, UrlConfig.MAIN_DOMAIN)
                data.putString(GazeConstants.KEY_REPORT_PARAM, "")
                //添加headers
                val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)
                data.putString(GazeConstants.KEY_REPORT_HEADER, mGson.toJson(HashMap<String, String>().apply {
                    put("X-Rom-Version",DeviceManager.getOSVersion())
                    put("X-Device-Sn",DeviceManager.getDeviceSn())
                    put("X-App-Version",PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
                    put("X-Device-Mode",DeviceManager.getProductModel())
                    put("X-Airdoc-Client","d409e47e-95e6-41fc-868c-e5748957d546")
                    put("Accept-Language","${locale.language}-${locale.country}")
                }))
            }
        )
    }

    override fun onResume() {
        super.onResume()
        switchMaskTherapy.postDelayed({
            switchMaskTherapy.isChecked = DeviceManager.getMaskTherapyState()
        },500)
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onPause() {
        super.onPause()
        mActivity.unbindService(serviceConnection)
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
        when(msg.what){
            GazeConstants.MSG_GAZE_TRACKING_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_GAZE_TRACKING_STATE state = $state")
            }
            GazeConstants.MSG_APPLIED_CURE_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_CURE_STATE state = $state")
                maskVM.setMTState(state)
                showMaskTherapyStateDialog(state)
            }
            GazeConstants.MSG_UPDATE_TREATMENT_DURATION ->{
                val treatmentDuration = msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)
                Logger.d(TAG, msg = "MSG_UPDATE_TREATMENT_DURATION treatmentDuration = $treatmentDuration")
                maskVM.setOcclusionTherapy(maskVM.occlusionTherapy?.apply {
                    trainingDuration = treatmentDuration
                })
            }
            GazeConstants.MSG_REPORT_TREATMENT_RESULT ->{
                val resultJson = msg.data?.getString(GazeConstants.KEY_REPORT_CURE_RESULT)
                Logger.d(TAG, msg = "MSG_REPORT_TREATMENT_RESULT resultJson = $resultJson")
                val result = try {
                    mGson.fromJson(resultJson, CureInfo::class.java)
                }catch (e:Exception){
                    if (BuildConfig.DEBUG){
                        e.printStackTrace()
                    }
                    null
                }
                if (result != null){
                    maskVM.setOcclusionTherapy(result)
                }
            }
        }
    }

    private fun showMaskTherapyStateDialog(isStart:Boolean){
        when(maskVM.eyePosition){
            AmblyopicEye.LEFT.value ->{
                val maskTherapyStateDialog = MaskTherapyStateDialog(mActivity)
                maskTherapyStateDialog.show()
                maskTherapyStateDialog.setData(isStart, AmblyopicEye.LEFT)
            }
            AmblyopicEye.RIGHT.value ->{
                val maskTherapyStateDialog = MaskTherapyStateDialog(mActivity)
                maskTherapyStateDialog.show()
                maskTherapyStateDialog.setData(isStart, AmblyopicEye.RIGHT)
            }
        }
    }

    fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}