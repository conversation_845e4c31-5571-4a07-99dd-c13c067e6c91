package com.mitdd.gazetracker.medicalhome.mask

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ui.CustomItemDecoration
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: SelectCommonAppActivity
 * Author by lilin,Date on 2024/8/26 10:19
 * PS: Not easy to write code, please indicate.
 */
class SelectCommonAppActivity : GTBaseActivity() {

    companion object{
        private val TAG = SelectCommonAppActivity::class.java.simpleName

        const val OUTPUT_PARAM_COMMON_APP = "common_app"

        fun createIntent(context: Context): Intent {
            return Intent(context, SelectCommonAppActivity::class.java)
        }
    }

    private val rvApp by id<RecyclerView>(R.id.rv_app)

    private val installAppAdapter = InstallAppAdapter()

    private var installAppList: MutableList<ApplicationInfo> = mutableListOf()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_select_common_app)

        initView()

        initData()
    }

    private fun initView(){

        initListener()

        rvApp.layoutManager = GridLayoutManager(this, 6)
        rvApp.addItemDecoration(CustomItemDecoration(20.dp2px(this), 10.dp2px(this), 6))
        rvApp.adapter = installAppAdapter
    }

    private fun initData(){
        loadCommonApp()
    }

    private fun initListener(){
        installAppAdapter.setItemClickListener(object : InstallAppAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                if (position in installAppList.indices){
                    val applicationInfo = installAppList[position]
                    val intent = Intent()
                    intent.putExtra(OUTPUT_PARAM_COMMON_APP,applicationInfo)
                    setResult(RESULT_OK,intent)
                    finish()
                }
            }

        })
    }

    private fun loadCommonApp(){
        installAppList.clear()
        //当前应用包名
        val curPackageName = packageName
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledApplications(0).filter {
            packageManager.getLaunchIntentForPackage(it.packageName) != null
        }
        for (installedApp in installedApps){
            if (curPackageName != installedApp.packageName){
                installAppList.add(installedApp)
            }
        }
        installAppAdapter.setInstallAppData(installAppList)
        installAppAdapter.notifyDataSetChanged()
    }

}