package com.mitdd.gazetracker.medicalhome.menu

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.read.ReadMainActivity
import com.mitdd.gazetracker.medicalhome.train.AITrainGuideActivity
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentManagementActivity
import com.mitdd.gazetracker.movement.EyeMovementEvaluateActivity

/**
 * FileName: MenuPopupWindow
 * Author by lilin,Date on 2024/10/16 16:47
 * PS: Not easy to write code, please indicate.
 */
class MenuPopupWindow(val context: Context) : PopupWindow() {

    private var llTreatmentManagement: LinearLayout? = null
    private var llAiTrainGuide: LinearLayout? = null
    private var llReadAbility: LinearLayout? = null
    private var llEyeMovement: LinearLayout? = null
    private var llVersion: LinearLayout? = null
    private var tvVersion:TextView? = null

    var onVersionClick:(() -> Unit)? = null

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.layout_menu_popup_window, null)
        contentView = view

        isFocusable = true
        isTouchable = true
        isOutsideTouchable = true

        initView(view)

        initListener()

    }

    fun show(){
        tvVersion?.text = context.getString(R.string.str_version_number_s,PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
    }

    private fun initView(view:View){
        llTreatmentManagement = view.findViewById(R.id.ll_treatment_management)
        llAiTrainGuide = view.findViewById(R.id.ll_ai_train_guide)
        llReadAbility = view.findViewById(R.id.ll_read_ability)
        llEyeMovement = view.findViewById(R.id.ll_eye_movement)
        llVersion = view.findViewById(R.id.ll_version)
        tvVersion = view.findViewById(R.id.tv_version)

        width = 150.dp2px(context)
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        llReadAbility?.isVisible = DeviceManager.isDemoMode()
        llEyeMovement?.isVisible = DeviceManager.isDemoMode()
        llAiTrainGuide?.isVisible = false
    }

    private fun initListener(){
        llTreatmentManagement?.setOnSingleClickListener {
            context.startActivity(TreatmentManagementActivity.createIntent(context))
            dismiss()
        }

        llAiTrainGuide?.setOnSingleClickListener {
            context.startActivity(AITrainGuideActivity.createIntent(context))
            dismiss()
        }

        llReadAbility?.setOnSingleClickListener {
            context.startActivity(ReadMainActivity.createIntent(context))
            dismiss()
        }

        llEyeMovement?.setOnSingleClickListener {
            context.startActivity(EyeMovementEvaluateActivity.createIntent(context))
            dismiss()
        }

        llVersion?.setOnSingleClickListener {
            onVersionClick?.invoke()
            dismiss()
        }
    }
}