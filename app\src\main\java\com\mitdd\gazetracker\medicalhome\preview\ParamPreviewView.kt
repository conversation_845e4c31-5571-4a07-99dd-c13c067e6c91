package com.mitdd.gazetracker.medicalhome.preview

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.airdoc.component.common.utils.ScreenUtil
import com.airdoc.component.common.R.color
import kotlin.math.abs

/**
 * FileName: ParamPreviewView
 * Author by lilin,Date on 2024/8/13 11:02
 * PS: Not easy to write code, please indicate.
 */
class ParamPreviewView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr){

    //预览区域中心坐标
    private var centerX: Float = ScreenUtil.getScreenWidth(context).toFloat() / 2
    private var centerY: Float = ScreenUtil.getScreenHeight(context).toFloat() / 2
    //预览区域半径
    private var radius: Float = 200f
    private val strokePaint = Paint()
    private val paint = Paint()
    private val path = Path()

    private var scaledTouchSlop = ViewConfiguration.get(context).scaledTouchSlop
    private var mLastX = 0f
    private var mLastY = 0f

    init {
        setLayerType(LAYER_TYPE_HARDWARE, null)
        strokePaint.setColor(ContextCompat.getColor(context,color.white_10))
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }

    /**
     * 设置预览区域半径
     */
    fun setPreviewRadius(radius: Float){
        this.radius = radius
        invalidate()
    }

    /**
     * 更新预览中心坐标
     */
    fun updateCircle(centerX: Float, centerY: Float) {
        this.centerX = centerX
        this.centerY = centerY
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        path.reset()
        //画描边
        path.addCircle(centerX, centerY, radius + 20, Path.Direction.CW)
        canvas.drawPath(path, strokePaint)

        //画预览区域
        path.reset()
        path.addCircle(centerX, centerY, radius, Path.Direction.CW)
        canvas.drawPath(path, paint)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when(event?.action){
            MotionEvent.ACTION_DOWN ->{
                mLastX = event.x
                mLastY = event.y
            }
            MotionEvent.ACTION_MOVE ->{
                val dx = abs(mLastX - event.x)
                val dy = abs(mLastY - event.y)
                if (dx >= scaledTouchSlop || dy >= scaledTouchSlop){
                    updateCircle(event.x,event.y)
                    mLastX = event.x
                    mLastY = event.y
                }
            }
        }
        return true
    }

}