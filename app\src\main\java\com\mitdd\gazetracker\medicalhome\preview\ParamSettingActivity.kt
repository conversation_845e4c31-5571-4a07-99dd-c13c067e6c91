package com.mitdd.gazetracker.medicalhome.preview

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.ImageView
import android.widget.RadioGroup
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.enumeration.CoverChannel
import com.mitdd.gazetracker.gaze.enumeration.CoverChannel.*
import com.mitdd.gazetracker.gaze.enumeration.CoverMode
import com.mitdd.gazetracker.gaze.enumeration.CoverRange
import com.mitdd.gazetracker.gaze.enumeration.CoverRange.*
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import jp.wasabeef.glide.transformations.BlurTransformation

/**
 * FileName: ParamSettingActivity
 * Author by lilin,Date on 2024/8/13 10:27
 * PS: Not easy to write code, please indicate.
 */
class ParamSettingActivity : GTBaseActivity() {

    companion object{
        private val TAG = ParamSettingActivity::class.java.simpleName

        //弱视模式
        const val MODE_AMBLYOPIA = "amblyopia"
        //近视模式
        const val MODE_MYOPIA = "myopia"

        private const val INPUT_PARAM_MODE = "mode"

        fun createIntent(context: Context,mode: String): Intent {
            val intent = Intent(context, ParamSettingActivity::class.java)
            intent.putExtra(INPUT_PARAM_MODE,mode)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val ivBlur by id<ImageView>(R.id.iv_blur)
    private val paramPreview by id<ParamPreviewView>(R.id.param_preview)
    private val clParamSetting by id<ConstraintLayout>(R.id.cl_param_setting)
    private val ivParamSettingScale by id<ImageView>(R.id.iv_param_setting_scale)
//    private val tvModeSelection by id<TextView>(R.id.tv_mode_selection)
//    private val rgModeSelection by id<RadioGroup>(R.id.rg_mode_selection)

    private val tvCoverArea by id<TextView>(R.id.tv_cover_area)
    private val seekbarCoverArea by id<SeekBar>(R.id.seekbar_cover_area)
    private val tvShadedArea by id<TextView>(R.id.tv_shaded_area)

    private val tvCoverRange by id<TextView>(R.id.tv_cover_range)
    private val rgCoverRange by id<RadioGroup>(R.id.rg_cover_range)

    //遮盖区域列表
    private val coverAreas = mutableListOf(0.5f,1.0f,1.5f,2.0f,2.5f,3.0f,3.5f,4.0f,4.5f,5.0f,5.5f)
    //遮盖区域转px基数
    private val coverAreaCardinality = 81.472f
    private var mMode = MODE_AMBLYOPIA

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                sendMessageToService(Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                })
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_param_setting)

        initParam()

        initView()

        initData()
    }

    private fun initParam(){
        mMode = when(intent.getStringExtra(INPUT_PARAM_MODE)){
            MODE_AMBLYOPIA ->{
                MODE_AMBLYOPIA
            }
            MODE_MYOPIA ->{
                MODE_MYOPIA
            }
            else ->{
                MODE_AMBLYOPIA
            }
        }
    }

    private fun initView(){
        initListener()

        ivParamSettingScale.isSelected = true
    }

    private fun initData(){

        switchModeSelection(mMode)

        val coverArea = MaskManager.getCoverArea()?:4.0f
        seekbarCoverArea.max = coverAreas.size - 1
        val index = coverAreas.indexOf(coverArea)
        Logger.d(TAG, msg = "initData index = $index,coverArea = $coverArea")
        seekbarCoverArea.progress = index

        val coverRange = MaskManager.getCoverRange()
        Logger.d(TAG, msg = "initData coverRange = $coverRange")
        when(coverRange){
            RANGER_MILD ->{
                rgCoverRange.check(R.id.rb_cover_range_mild)
            }
            RANGER_MODERATE -> {
                rgCoverRange.check(R.id.rb_cover_range_moderate)
            }
            RANGER_HEIGHT -> {
                rgCoverRange.check(R.id.rb_cover_range_height)
            }
            RANGER_COMPLETELY -> {
                rgCoverRange.check(R.id.rb_cover_range_complete)
            }
        }
    }

    private fun initListener(){
        ivBack.setOnSingleClickListener {
            finish()
        }
        ivParamSettingScale.setOnSingleClickListener {
            val isSelected = ivParamSettingScale.isSelected
            ivParamSettingScale.isSelected = !isSelected
//            tvModeSelection.isVisible = !isSelected
//            rgModeSelection.isVisible = !isSelected
            tvCoverArea.isVisible = !isSelected
            seekbarCoverArea.isVisible = !isSelected
            tvShadedArea.isVisible = !isSelected
            tvCoverRange.isVisible = !isSelected
            rgCoverRange.isVisible = !isSelected
            val params = clParamSetting.layoutParams as ConstraintLayout.LayoutParams
            if (ivParamSettingScale.isSelected){
                params.width = 235.dp2px(this)
                params.height = 375.dp2px(this)
            }else{
                params.width = 50.dp2px(this)
                params.height = 50.dp2px(this)
            }
            clParamSetting.layoutParams = params
        }
        seekbarCoverArea.setOnSeekBarChangeListener(object :OnSeekBarChangeListener{
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Logger.d(TAG, msg = "seekbarCoverArea onProgressChanged progress = $progress, fromUser = $fromUser")
                // 当进度改变时调用
                if (progress in coverAreas.indices){
                    updateCoverArea(coverAreas[progress])
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // 当开始触摸（滑动）SeekBar时调用
            }
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
            }

        })
        rgCoverRange.setOnCheckedChangeListener { _, checkedId ->
            Logger.d(TAG, msg = "rgCoverRange checkedId = $checkedId")
            when(checkedId){
                R.id.rb_cover_range_mild ->{
                    if (mMode == MODE_MYOPIA){
                        updateCoverMode(CoverMode.EXTERNAL_GAUSSIAN_BLUR)
                    }else{
                        updateCoverMode(CoverMode.INTERNAL_GAUSSIAN_BLUR)
                    }
                    updateCoverRange(RANGER_MILD)
                }
                R.id.rb_cover_range_moderate ->{
                    if (mMode == MODE_MYOPIA){
                        updateCoverMode(CoverMode.EXTERNAL_GAUSSIAN_BLUR)
                    }else{
                        updateCoverMode(CoverMode.INTERNAL_GAUSSIAN_BLUR)
                    }
                    updateCoverRange(RANGER_MODERATE)
                }
                R.id.rb_cover_range_height ->{
                    if (mMode == MODE_MYOPIA){
                        updateCoverMode(CoverMode.EXTERNAL_GAUSSIAN_BLUR)
                    }else{
                        updateCoverMode(CoverMode.INTERNAL_GAUSSIAN_BLUR)
                    }
                    updateCoverRange(RANGER_HEIGHT)
                }
                R.id.rb_cover_range_complete ->{
                    updateCoverMode(CoverMode.INTERNAL_AREAS_BLACKENED)
                    updateCoverRange(RANGER_COMPLETELY)
                }
            }
        }
    }

    private fun switchModeSelection(mode: String){
        Logger.d(TAG, msg = "switchModeSelection mode = $mode")
        when(mode){
            MODE_MYOPIA ->{
                mMode = MODE_MYOPIA
                val coverRange = MaskManager.getCoverRange()
                blur(coverRange.ranger)
                updateCoverMode(CoverMode.EXTERNAL_GAUSSIAN_BLUR)
                updateCoverChannel(CHANNEL_BGR)
            }
            else ->{
                mMode = MODE_AMBLYOPIA
                val coverRange = MaskManager.getCoverRange()
                blur(coverRange.ranger)
                updateCoverMode(CoverMode.INTERNAL_GAUSSIAN_BLUR)
                updateCoverChannel(CHANNEL_R)
            }
        }
    }

    private fun blur(range:Float){
        if (mMode == MODE_MYOPIA){
            Glide.with(this).load(R.drawable.icon_param_preview_bg)
                .apply(RequestOptions.bitmapTransform(BlurTransformation(range.toInt()))).into(paramPreview)
            ivBlur.setImageResource(R.drawable.icon_param_preview_bg)
        }else{
            Glide.with(this).load(R.drawable.icon_param_preview_bg)
                .apply(RequestOptions.bitmapTransform(BlurTransformation(range.toInt()))).into(ivBlur)
            paramPreview.setImageResource(R.drawable.icon_param_preview_bg)
        }
    }

    private fun updateCoverMode(coverMode: CoverMode){
        Logger.d(TAG, msg = "updateCoverMode coverMode = $coverMode")
        MaskManager.setCoverMode(coverMode.mode)
    }

    private fun updateCoverChannel(coverChannel: CoverChannel){
        Logger.d(TAG, msg = "updateCoverChannel coverChannel = $coverChannel")
        MaskManager.setCoverChannel(coverChannel.channel)
    }

    private fun updateCoverArea(coverArea:Float){
        Logger.d(TAG, msg = "updateCoverArea coverArea = $coverArea")
        MaskManager.setCoverArea(coverArea)
        tvShadedArea.text = coverArea.toString()
        seekbarCoverArea.progress = coverAreas.indexOf(coverArea)
        val radius = coverArea * coverAreaCardinality
        paramPreview.setPreviewRadius(radius)
    }

    private fun updateCoverRange(coverRange: CoverRange){
        Logger.d(TAG, msg = "updateCoverRange coverRange = $coverRange")
        if (coverRange != RANGER_COMPLETELY){
            MaskManager.setCoverRange(coverRange.ranger)
            blur(coverRange.ranger)
        }else{
            MaskManager.setCoverRange(RANGER_HEIGHT.ranger)
            blur(RANGER_HEIGHT.ranger)
        }
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
    }

    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
        handler.postDelayed({
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
        },500)
    }


    override fun onStop() {
        super.onStop()
        unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}