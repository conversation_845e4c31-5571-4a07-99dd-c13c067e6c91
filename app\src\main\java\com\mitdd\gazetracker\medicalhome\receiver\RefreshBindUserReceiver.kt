package com.mitdd.gazetracker.medicalhome.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * FileName: RefreshBindUserReceiver
 * Author by lilin,Date on 2024/10/17 14:35
 * PS: Not easy to write code, please indicate.
 * 定时刷新绑定用户信息广播接收器
 */
class RefreshBindUserReceiver : BroadcastReceiver()  {

    companion object{

        //广播action
        const val ACTION_REFRESH_BIND_USER = "com.mitdd.gazetracker.REFRESH_BIND_USER"

        //广播对应的事件
        const val EVENT_REFRESH_BIND_USER = "REFRESH_BIND_USER"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        when(intent?.action){
            ACTION_REFRESH_BIND_USER ->{
                LiveEventBus.get<Any>(EVENT_REFRESH_BIND_USER).post(Any())
            }
        }
    }
}