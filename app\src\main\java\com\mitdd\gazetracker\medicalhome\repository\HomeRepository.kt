package com.mitdd.gazetracker.medicalhome.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhome.api.HomeApiService
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.medicalhome.bean.MedicalHomeProfile

/**
 * FileName: HomeRepository
 * Author by lilin,Date on 2024/11/27 11:11
 * PS: Not easy to write code, please indicate.
 */
class HomeRepository : BaseRepository() {

    /**
     * 获取家庭版配置信息
     */
    suspend fun getMedicalHomeProfile(): ApiResponse<MedicalHomeProfile> {
        return executeHttp {
            MainRetrofitClient.createService(HomeApiService::class.java).getMedicalHomeProfile()
        }
    }

}