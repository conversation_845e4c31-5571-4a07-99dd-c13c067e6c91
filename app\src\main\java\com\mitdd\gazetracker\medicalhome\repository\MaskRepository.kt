package com.mitdd.gazetracker.medicalhome.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhome.api.MaskApiService
import com.mitdd.gazetracker.net.MainRetrofitClient

/**
 * FileName: MaskRepository
 * Author by lilin,Date on 2024/10/11 11:46
 * PS: Not easy to write code, please indicate.
 * 遮盖疗法Repository
 */
class MaskRepository : BaseRepository() {

    /**
     * 获取今日数字遮盖疗法信息
     */
    suspend fun getTodayOcclusionTherapy(): ApiResponse<CureInfo> {
        return executeHttp {
            MainRetrofitClient.createService(MaskApiService::class.java).getTodayOcclusionTherapy()
        }
    }


}