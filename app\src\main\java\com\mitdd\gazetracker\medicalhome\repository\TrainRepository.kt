package com.mitdd.gazetracker.medicalhome.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.medicalhome.api.TrainApiService
import com.mitdd.gazetracker.medicalhome.bean.TrainConfig
import com.mitdd.gazetracker.medicalhome.bean.VisionTherapy
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: TrainRepository
 * Author by lilin,Date on 2024/10/10 9:56
 * PS: Not easy to write code, please indicate.
 * 训练仓储
 */
class TrainRepository : BaseRepository() {

    /**
     * 获取今日视觉训练疗法信息
     */
    suspend fun getTodayVisionTherapy(): ApiResponse<VisionTherapy> {
        return executeHttp {
            MainRetrofitClient.createService(TrainApiService::class.java).getTodayVisionTherapy()
        }
    }

    /**
     * 提交训练结果
     * @param trainId 训练游戏ID
     * @param startTime 开始时间（yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（yyyy-MM-dd HH:mm:ss）
     * @param score 训练结果分数
     */
    suspend fun submitTrainResult(trainId:Int,startTime:String,endTime:String,score:Int): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["trainId"] = trainId
            hashMap["startTime"] = startTime
            hashMap["endTime"] = endTime
            hashMap["score"] = score
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(TrainApiService::class.java).submitTrainResult(requestBody)
        }
    }

    /**
     * 获取训练配置
     * @param trainId 训练id
     */
    suspend fun getTrainConfig(trainId:Int): ApiResponse<TrainConfig> {
        return executeHttp {
            MainRetrofitClient.createService(TrainApiService::class.java).getTrainConfig(trainId)
        }
    }

}