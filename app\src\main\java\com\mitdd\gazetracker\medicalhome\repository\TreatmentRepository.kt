package com.mitdd.gazetracker.medicalhome.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhome.api.TreatmentApiService
import com.mitdd.gazetracker.medicalhome.bean.CurrentTreatment
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.bean.Treatments
import com.mitdd.gazetracker.net.MainRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: TreatmentRepository
 * Author by lilin,Date on 2024/11/28 13:50
 * PS: Not easy to write code, please indicate.
 * 疗程repository
 */
class TreatmentRepository : BaseRepository(){

    /**
     * 获取当前疗程
     */
    suspend fun getCurrentTreatment(): ApiResponse<CurrentTreatment> {
        return executeHttp {
            MainRetrofitClient.createService(TreatmentApiService::class.java).getCurrentTreatment()
        }
    }

    /**
     * 激活疗程
     * @param courseId 卡号
     * @param phone 手机号
     */
    suspend fun activationTreatment(courseId:String,phone:String): ApiResponse<TreatmentInfo> {
        return executeHttp {
            val hashMap = HashMap<String, String>()
            hashMap["phone"] = phone
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(TreatmentApiService::class.java).activationTreatment(courseId, requestBody)
        }
    }

    /**
     * 获取当前设备绑定用户的疗程列表
     */
    suspend fun getTreatments(): ApiResponse<Treatments>{
        return executeHttp {
            MainRetrofitClient.createService(TreatmentApiService::class.java).getTreatments()
        }
    }
}