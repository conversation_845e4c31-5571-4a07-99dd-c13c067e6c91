package com.mitdd.gazetracker.medicalhome.train

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.widget.SwitchCompat
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.CommonPreference

/**
 * FileName: AITrainGuideActivity
 * Author by lilin,Date on 2024/10/26 14:49
 * PS: Not easy to write code, please indicate.
 * AI 训练指导
 */
class AITrainGuideActivity : GTBaseActivity() {

    companion object{
        private val TAG = AITrainGuideActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, AITrainGuideActivity::class.java)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val switchBehaviorGuidance by id<SwitchCompat>(R.id.switch_behavior_guidance)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ai_train_guide)

        initView()

        switchBehaviorGuidance.isChecked = MMKVManager.decodeBool(CommonPreference.SWITCH_AI_TRAIN_GUIDE)?:false

    }

    private fun initView() {
        ivBack.setOnSingleClickListener {
            finish()
        }
        switchBehaviorGuidance.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.SWITCH_AI_TRAIN_GUIDE,isChecked)
        }
    }

}