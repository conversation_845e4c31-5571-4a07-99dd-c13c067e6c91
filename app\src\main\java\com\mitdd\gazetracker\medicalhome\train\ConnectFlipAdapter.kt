package com.mitdd.gazetracker.medicalhome.train

import android.Manifest
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.PermissionUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.FlipBeat
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState.*

/**
 * FileName: ConnectFlipAdapter
 * Author by lilin,Date on 2024/10/23 15:07
 * PS: Not easy to write code, please indicate.
 */
class ConnectFlipAdapter(val context: Context, private val flipDevices: MutableList<FlipBeat>) : RecyclerView.Adapter<ConnectFlipAdapter.FlipHolder>() {

    private val TAG = ConnectFlipAdapter::class.java.name

    var onItemClick:((FlipBeat) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FlipHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_connect_flip, parent, false)
        return FlipHolder(view)
    }

    override fun getItemCount(): Int {
        return flipDevices.size
    }

    override fun onBindViewHolder(holder: FlipHolder, position: Int) {
        if (position in flipDevices.indices){
            holder.bind(flipDevices[position])
        }
    }

    inner class FlipHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val tvName: TextView = itemView.findViewById(R.id.tv_name)
        private val llState: LinearLayout = itemView.findViewById(R.id.ll_state)
        private val ivState: ImageView = itemView.findViewById(R.id.iv_state)
        private val tvStatePrompt: TextView = itemView.findViewById(R.id.tv_state_prompt)
        private val viewLine: View = itemView.findViewById(R.id.view_line)

        fun bind(flipBeat: FlipBeat){
            itemView.setOnSingleClickListener {
                onItemClick?.invoke(flipBeat)
            }
            if (PermissionUtils.checkSelfPermission(context,Manifest.permission.BLUETOOTH_CONNECT)){
                tvName.text = flipBeat.flipDevice?.name?:""
                when(flipBeat.state){
                    CONNECTED ->{
                        llState.isVisible = true
                        ivState.isSelected = true
                        tvStatePrompt.text = itemView.context.getString(R.string.str_connected)
                    }

                    CONNECTING -> {
                        llState.isVisible = true
                        ivState.isSelected = false
                        tvStatePrompt.text = itemView.context.getString(R.string.str_connecting)
                    }
                    DISCONNECTING -> {
                        llState.isVisible = true
                        ivState.isSelected = false
                        tvStatePrompt.text = itemView.context.getString(R.string.str_disconnecting)
                    }
                    DISCONNECTED -> {
                        llState.isVisible = false
                        ivState.isSelected = false
                        tvStatePrompt.text = ""
                    }
                }
            }
            val position = bindingAdapterPosition
            viewLine.isVisible = position != flipDevices.size - 1
        }
    }

}