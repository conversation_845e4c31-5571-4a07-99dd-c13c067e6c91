package com.mitdd.gazetracker.medicalhome.train

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.ui.CustomItemDecoration
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.medicalhome.bean.TrainCategory

/**
 * FileName: SelectTrainActivity
 * Author by lilin,Date on 2024/10/10 17:47
 * PS: Not easy to write code, please indicate.
 */
class SelectTrainActivity : GTBaseActivity() {

    companion object{
        private val TAG = SelectTrainActivity::class.java.simpleName

        private const val INPUT_PARAM_TRAIN_CATEGORY = "input_train_category"
        const val OUTPUT_PARAM_TRAIN = "output_train"

        fun createIntent(context: Context, trainCategory: TrainCategory): Intent {
            val intent = Intent(context, SelectTrainActivity::class.java)
            intent.putExtra(INPUT_PARAM_TRAIN_CATEGORY,trainCategory)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val tvName by id<TextView>(R.id.tv_name)
    private val rvTrain by id<RecyclerView>(R.id.rv_train)

    private val selectTrainAdapter = SelectTrainAdapter()

    private var mTrainCategory: TrainCategory = TrainCategory.EMPTY

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_select_train)
        initParam()
        initView()
        initObserver()
        initData()
    }

    private fun initParam() {
        mTrainCategory = intent.getParcelableExtra(INPUT_PARAM_TRAIN_CATEGORY)?: TrainCategory.EMPTY
    }

    private fun initView() {
        initListener()
        rvTrain.layoutManager = GridLayoutManager(this, 4)
        rvTrain.addItemDecoration(CustomItemDecoration(20.dp2px(this), 20.dp2px(this), 4))
        rvTrain.adapter = selectTrainAdapter
    }

    private fun initObserver() {
    }

    private fun initData() {
        tvName.text = mTrainCategory.categoryName
        val trainList = mTrainCategory.items?: emptyList()
        selectTrainAdapter.setTrainData(trainList)
    }

    private fun initListener(){
        selectTrainAdapter.onItemClick = { train ->
            val returnIntent = Intent()
            returnIntent.putExtra(OUTPUT_PARAM_TRAIN,train)
            setResult(Activity.RESULT_OK, returnIntent)
            finish()
        }
        ivBack.setOnSingleClickListener {
            finish()
        }
    }

}