package com.mitdd.gazetracker.medicalhome.train

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.TrainCategory

/**
 * FileName: TrainCategoryListAdapter
 * Author by lilin,Date on 2024/10/10 11:23
 * PS: Not easy to write code, please indicate.
 */
class TrainCategoryListAdapter : RecyclerView.Adapter<TrainCategoryListAdapter.TrainCategoryHolder>() {

    private val TAG = TrainCategoryListAdapter::class.java.simpleName

    private var trainCategories: MutableList<TrainCategory> = mutableListOf()

    var onItemClick:((TrainCategory) -> Unit)? = null

    fun setTrainCategoryData(data:List<TrainCategory>){
        trainCategories.clear()
        trainCategories.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TrainCategoryHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_visual_therapy_train_category_list, parent, false)
        return TrainCategoryHolder(view)
    }

    override fun getItemCount(): Int {
        return trainCategories.size
    }

    override fun onBindViewHolder(holder: TrainCategoryHolder, position: Int) {
        if (position in trainCategories.indices){
            holder.bind(trainCategories[position])
        }
    }

    inner class TrainCategoryHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val ivTrainCategory: ImageView = itemView.findViewById(R.id.iv_train_category)
        private val ivTrainDuration: ImageView = itemView.findViewById(R.id.iv_train_duration)
        private val tvTrainDuration: TextView = itemView.findViewById(R.id.tv_train_duration)
        private val tvName: TextView = itemView.findViewById(R.id.tv_name)

        fun bind(trainCategory: TrainCategory){
            with(trainCategory){
                if (icon != null && !TextUtils.isEmpty(icon)){
                    ImageLoader.loadImage(itemView.context, icon!!,ivTrainCategory)
                }
                val trainingDuration = trainingDuration?:0
                if (trainingDuration > 0){
                    tvTrainDuration.isVisible = true
                    ivTrainDuration.isVisible = true
                    tvTrainDuration.text = TimeUtils.parseTimeToTimeString((trainingDuration * 1000).toLong(),"mm:ss")
                }else{
                    tvTrainDuration.isVisible = false
                    ivTrainDuration.isVisible = false
                }
                tvName.text = categoryName
            }
            itemView.setOnSingleClickListener {
                onItemClick?.invoke(trainCategory)
            }
        }
    }
}