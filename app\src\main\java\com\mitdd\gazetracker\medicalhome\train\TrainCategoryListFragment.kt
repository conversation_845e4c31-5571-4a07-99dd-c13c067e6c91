package com.mitdd.gazetracker.medicalhome.train

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ui.CustomItemDecoration
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.Train
import com.mitdd.gazetracker.medicalhome.bean.TrainCategory
import com.mitdd.gazetracker.medicalhome.enumeration.LimitType
import com.mitdd.gazetracker.medicalhome.vm.TrainViewModel
import com.mitdd.gazetracker.medicalhome.dialog.TrainEndDialog
import com.mitdd.gazetracker.medicalhome.dialog.TrainSuggestionDialog
import kotlin.math.ceil

/**
 * FileName: TrainCategoryListFragment
 * Author by lilin,Date on 2024/10/10 11:14
 * PS: Not easy to write code, please indicate.
 */
class TrainCategoryListFragment : BaseCommonFragment() {

    companion object{
        private val TAG = TrainCategoryListFragment::class.java.simpleName
        const val INPUT_PARAM_TRAIN_CATEGORY_LIST = "trainCategoryList"
        const val INPUT_PARAM_IS_FULL = "isFull"

        fun newInstance(isFull:Boolean): TrainCategoryListFragment {
            val fragment = TrainCategoryListFragment()
            val args = Bundle()
            args.putBoolean(INPUT_PARAM_IS_FULL,isFull)
            fragment.arguments = args
            return fragment
        }
    }

    private val rvTrainCategory by id<RecyclerView>(R.id.rv_train_category)

    override fun getLayoutResId(): Int {
        return R.layout.fragment_train_category_list
    }

    private val trainVM by activityViewModels<TrainViewModel>()

    //训练启动器
    private var trainLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            //更新今日训练疗法信息
            trainVM.getTodayVisionTherapy()
            val data = result.data
            val trainName = data?.getStringExtra(TrainWebActivity.OUTPUT_PARAM_TRAIN_NAME)?:""
            val trainTime = data?.getLongExtra(TrainWebActivity.OUTPUT_PARAM_TRAIN_TIME,0)?:0
            if (!TextUtils.isEmpty(trainName) && trainTime > 0){
                val trainEndDialog = TrainEndDialog(mActivity)
                trainEndDialog.show()
                trainEndDialog.setData(trainName,trainTime)
            }
        }
    }

    //选择训练启动器
    private var selectTrainLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val train = data?.getParcelableExtra(SelectTrainActivity.OUTPUT_PARAM_TRAIN)?: Train.EMPTY
            if (train.allowTraining != true){
                when(train.limitType){
                    LimitType.TODAY.value ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.TODAY)
                    }
                    LimitType.CATEGORY.value ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.CATEGORY)
                    }
                    else ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.ONCE)
                    }
                }
                return@registerForActivityResult
            }
            trainLauncher.launch(TrainWebActivity.createIntent(mActivity, train))
        }
    }

    private val trainCategoryListAdapter = TrainCategoryListAdapter()

    private var isFull = false

    override fun initParam() {
        super.initParam()
        val arguments = arguments
        if (arguments != null){
            isFull = arguments.getBoolean(INPUT_PARAM_IS_FULL)
        }
    }

    override fun initView() {
        super.initView()

        initListener()

        val spanCount = if (isFull) 8 else 4
        rvTrainCategory.layoutManager = GridLayoutManager(mActivity, spanCount)
        rvTrainCategory.addItemDecoration(CustomItemDecoration(40.dp2px(mActivity), 20.dp2px(mActivity), spanCount))
        rvTrainCategory.adapter = trainCategoryListAdapter
    }

    override fun initObserver() {
        super.initObserver()
        LiveEventBus.get<List<TrainCategory>>(INPUT_PARAM_TRAIN_CATEGORY_LIST).observeSticky(this){
            if (it != null){
                trainCategoryListAdapter.setTrainCategoryData(it)
            }
        }
    }

    private fun initListener(){
        trainCategoryListAdapter.onItemClick = onItemClick@{ trainCategory ->
            if (!trainVM.allowTraining){
                showTrainSuggestionDialog(trainVM.plannedDuration, LimitType.TODAY)
                return@onItemClick
            }
            if (trainCategory.allowTraining != true){
                showTrainSuggestionDialog(trainCategory.plannedDuration?:0, LimitType.CATEGORY)
                return@onItemClick
            }
            selectTrainLauncher.launch(SelectTrainActivity.createIntent(mActivity, trainCategory))
        }
    }

    private fun showTrainSuggestionDialog(duration:Int,limitType: LimitType){
        val trainSuggestionDialog = TrainSuggestionDialog(mActivity)
        trainSuggestionDialog.show()
        //向上取整
        val minutes = ceil(duration.toFloat() / 60).toInt()
        when(limitType){
            LimitType.TODAY ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everyday_train_time_do_not_exceed,minutes))
            }
            LimitType.CATEGORY ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_type_train_time_do_not_exceed,minutes))
            }
            LimitType.ONCE ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everytime_train_time_do_not_exceed,minutes))
            }
        }
    }
}