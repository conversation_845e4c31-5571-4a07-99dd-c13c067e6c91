package com.mitdd.gazetracker.medicalhome.train

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.Train

/**
 * FileName: TrainListAdapter
 * Author by lilin,Date on 2024/10/10 10:53
 * PS: Not easy to write code, please indicate.
 */
class TrainListAdapter : RecyclerView.Adapter<TrainListAdapter.TrainHolder>() {

    private val TAG = TrainListAdapter::class.java.simpleName

    private var trains: MutableList<Train> = mutableListOf()

    var onItemClick:((Train) -> Unit)? = null

    fun setTrainData(data:List<Train>){
        trains.clear()
        trains.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TrainHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_visual_therapy_train_list, parent, false)
        return TrainHolder(view)
    }

    override fun getItemCount(): Int {
        return trains.size
    }

    override fun onBindViewHolder(holder: TrainHolder, position: Int) {
        if (position in trains.indices){
            holder.bind(trains[position])
        }
    }

    inner class TrainHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val ivTrain: ImageView = itemView.findViewById(R.id.iv_train)
        private val ivTrainDuration: ImageView = itemView.findViewById(R.id.iv_train_duration)
        private val tvTrainDuration: TextView = itemView.findViewById(R.id.tv_train_duration)
        private val tvName: TextView = itemView.findViewById(R.id.tv_name)

        fun bind(train: Train){
            with(train){
                if (icon != null && !TextUtils.isEmpty(icon)){
                    ImageLoader.loadImage(itemView.context,icon,ivTrain)
                }
                val trainingDuration = trainingDuration?:0
                if (trainingDuration > 0){
                    tvTrainDuration.isVisible = true
                    ivTrainDuration.isVisible = true
                    tvTrainDuration.text = TimeUtils.parseTimeToTimeString((trainingDuration * 1000).toLong(),"mm:ss")
                }else{
                    tvTrainDuration.isVisible = false
                    ivTrainDuration.isVisible = false
                }
                tvName.text = trainName
            }
            itemView.setOnSingleClickListener {
                onItemClick?.invoke(train)
            }
        }
    }

}