package com.mitdd.gazetracker.medicalhome.train

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ui.CustomItemDecoration
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.Train
import com.mitdd.gazetracker.medicalhome.dialog.TrainEndDialog
import com.mitdd.gazetracker.medicalhome.enumeration.LimitType
import com.mitdd.gazetracker.medicalhome.vm.TrainViewModel
import com.mitdd.gazetracker.medicalhome.dialog.TrainSuggestionDialog
import kotlin.math.ceil

/**
 * FileName: TrainListFragment
 * Author by lilin,Date on 2024/10/10 10:41
 * PS: Not easy to write code, please indicate.
 * 训练列表
 */
class TrainListFragment : BaseCommonFragment() {

    companion object{
        private val TAG = TrainListFragment::class.java.simpleName
        const val INPUT_PARAM_TRAIN_LIST = "trainList"
        const val INPUT_PARAM_IS_FULL = "isFull"

        fun newInstance(isFull:Boolean): TrainListFragment {
            val fragment = TrainListFragment()
            val args = Bundle()
            args.putBoolean(INPUT_PARAM_IS_FULL,isFull)
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_train_list
    }

    private val rvTrain by id<RecyclerView>(R.id.rv_train)

    private val trainVM by activityViewModels<TrainViewModel>()

    //训练启动器
    private var trainLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            //更新今日训练疗法信息
            trainVM.getTodayVisionTherapy()
            val data = result.data
            val trainName = data?.getStringExtra(TrainWebActivity.OUTPUT_PARAM_TRAIN_NAME)?:""
            val trainTime = data?.getLongExtra(TrainWebActivity.OUTPUT_PARAM_TRAIN_TIME,0)?:0
            if (!TextUtils.isEmpty(trainName) && trainTime > 0){
                val trainEndDialog = TrainEndDialog(mActivity)
                trainEndDialog.show()
                trainEndDialog.setData(trainName,trainTime)
            }
        }
    }

    private val trainListAdapter = TrainListAdapter()

    private var isFull = false

    override fun initParam() {
        super.initParam()
        val arguments = arguments
        if (arguments != null){
            isFull = arguments.getBoolean(INPUT_PARAM_IS_FULL)
        }
    }

    override fun initView() {
        super.initView()

        initListener()

        val spanCount = if (isFull) 6 else 3
        rvTrain.layoutManager = GridLayoutManager(mActivity, spanCount)
        rvTrain.addItemDecoration(CustomItemDecoration(15.dp2px(mActivity), 15.dp2px(mActivity), spanCount))
        rvTrain.adapter = trainListAdapter
    }

    override fun initObserver() {
        super.initObserver()
        LiveEventBus.get<List<Train>>(INPUT_PARAM_TRAIN_LIST).observeSticky(this){
            if (it != null){
                trainListAdapter.setTrainData(it)
            }
        }
    }

    private fun initListener(){
        trainListAdapter.onItemClick = onItemClick@{ train ->
            if (!trainVM.allowTraining){
                showTrainSuggestionDialog(trainVM.plannedDuration, LimitType.TODAY)
                return@onItemClick
            }
            if (train.allowTraining != true){
                when(train.limitType){
                    LimitType.TODAY.value ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.TODAY)
                    }
                    LimitType.CATEGORY.value ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.CATEGORY)
                    }
                    else ->{
                        showTrainSuggestionDialog(train.plannedDuration?:0, LimitType.ONCE)
                    }
                }
                return@onItemClick
            }
            trainLauncher.launch(TrainWebActivity.createIntent(mActivity, train))
        }
    }

    private fun showTrainSuggestionDialog(duration:Int,limitType: LimitType){
        val trainSuggestionDialog = TrainSuggestionDialog(mActivity)
        trainSuggestionDialog.show()
        //向上取整
        val minutes = ceil(duration.toFloat() / 60).toInt()
        when(limitType){
            LimitType.TODAY ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everyday_train_time_do_not_exceed,minutes))
            }
            LimitType.CATEGORY ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_type_train_time_do_not_exceed,minutes))
            }
            LimitType.ONCE ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everytime_train_time_do_not_exceed,minutes))
            }
        }
    }
}