package com.mitdd.gazetracker.medicalhome.train

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import android.util.Size
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.airdoc.component.common.utils.TimeUtils
import com.google.gson.Gson
import com.mitdd.behaviorguidance.BehaviorGuidanceManager
import com.mitdd.behaviorguidance.bean.MonitorResult
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.utils.GTUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.UrlMedia
import com.mitdd.gazetracker.common.CommonPreference
import com.mitdd.gazetracker.common.dialog.CommonLoadingDialog
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.medicalhome.bean.Train
import com.mitdd.gazetracker.medicalhome.bean.TrainConfig
import com.mitdd.gazetracker.medicalhome.dialog.TrainSuggestionDialog
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import com.mitdd.gazetracker.medicalhome.enumeration.LimitType
import com.mitdd.gazetracker.medicalhome.vm.TrainViewModel
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.utils.LocaleManager
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.time.ZoneId
import kotlin.math.ceil


/**
 * FileName: TrainWebActivity
 * Author by lilin,Date on 2024/10/10 15:23
 * PS: Not easy to write code, please indicate.
 */
class TrainWebActivity : GTBaseActivity(), TrainWebView.TrainActionListener {

    companion object{
        private val TAG = TrainWebActivity::class.java.simpleName

        const val INPUT_PARAM_TRAIN = "Train"
        const val OUTPUT_PARAM_TRAIN_NAME = "TRAIN_NAME"
        const val OUTPUT_PARAM_TRAIN_TIME = "TRAIN_TIME"

        private const val ACTION_GAME_OVER = "gameOver"
        private const val ACTION_GAME_START = "gameStart"
        private const val ACTION_FLIP_FLIP_CLAP = "flipFlipClap"

        fun createIntent(context: Context,train: Train): Intent {
            val intent = Intent(context, TrainWebActivity::class.java)
            intent.putExtra(INPUT_PARAM_TRAIN,train)
            return intent
        }
    }

    private val trainWebView by id<TrainWebView>(R.id.wv_train)
    private val tvKeepTime by id<TextView>(R.id.tv_keep_time)
    private val ivCloseTrain by id<ImageView>(R.id.iv_close_train)
    private val llTrainWarning by id<LinearLayout>(R.id.ll_train_warning)
    private val tvTrainWarning by id<TextView>(R.id.tv_train_warning)

    private val gson = Gson()
    //行为指导图像分析组件
    private val imageAnalysis by lazy {
        val resolutionStrategy = ResolutionSelector.Builder()
            .setResolutionStrategy(
                ResolutionStrategy(
                    Size(3264, 2448),
                    ResolutionStrategy.FALLBACK_RULE_NONE)
            )
            .setAllowedResolutionMode(ResolutionSelector.PREFER_HIGHER_RESOLUTION_OVER_CAPTURE_RATE)
            .build()
        ImageAnalysis.Builder()
            .setResolutionSelector(resolutionStrategy)
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build().apply {
                setAnalyzer(ContextCompat.getMainExecutor(this@TrainWebActivity)) { image ->
                    sendImageToBehaviorQueue(image)
                }
            }
    }
    //行为指导使用相机的Fov
    private var cameraFov = 77.8f
    //行为指导图像分析队列
    private var behaviorAnalysisQueue: Channel<ImageProxy>? = null
    //行为指导图像分析任务
    private var behaviorAnalysisJob: Job? = null

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    //训练VM
    private val trainVM by viewModels<TrainViewModel>()

    private var mTrain: Train = Train.EMPTY

    //开始时间戳
    private var startTime = 0L
    //结束时间戳
    private var endTime = 0L
    //本次训练时间，单位秒
    private var duration = 0
    //本次训练最大时长，单位秒
    private var maxDuration = 0
    //训练计时任务
    private var keepTimeJob: Job? = null
    private var isGameStart = false

    private var commonLoadingDialog: CommonLoadingDialog? = null
    //上一次显示训练行为警示的时间
    private var lastShowTrainWarningTime = 0L
    //是否打开行为指导
    private val isOpenDisplayViewpoint = MMKVManager.decodeBool(CommonPreference.SWITCH_AI_TRAIN_GUIDE)?:false

    @ExperimentalCamera2Interop
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_train_web)

        initParam()
        initView()
        initObserver()
        initData()
    }

    private fun initParam() {
        mTrain = intent.getParcelableExtra(INPUT_PARAM_TRAIN)?: Train.EMPTY
        maxDuration = mTrain.remainingDuration?:0
    }

    private fun initView() {
        initListener()
        trainWebView.addJavascriptInterface(trainWebView.TrainAction(),"ReactNativeWebView")
        trainWebView.setTrainActionListener(this)
    }

    @ExperimentalCamera2Interop
    private fun initObserver() {
        trainVM.trainConfigLiveData.observe(this){
            loadTrain(it)
        }
        trainVM.submitTrainResultLiveData.observe(this){
            finishTrain()
        }
    }

    private fun initData() {
        val trainId = mTrain.trainId
        Logger.d(TAG, msg = "initData trainId = $trainId")
        if (trainId != null){
            trainVM.getTrainConfig(trainId)
        }else{
            Toast.makeText(this,getString(R.string.str_training_data_anomalies),Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun initListener(){
        ivCloseTrain.setOnSingleClickListener {
            trainWebView.gameOver()
        }
    }

    //初始化翻转拍
    private fun initFlipBeat(){
        if (mTrain.mustBluetoothFlip == true){
            val flipBeatState = FlipBeatManager.getFlipBeatState()
            if (flipBeatState != FlipBeatState.CONNECTED){
                Toast.makeText(this,getString(R.string.str_flip_beat_not_connected),Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun openGazeTracker(){
        if (mTrain.isEyeMovementTraining == true){
            //如果是眼动训练，打开视线追踪服务
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_START_TRACK
                }
            )
        }
    }

    private fun closeGazeTracker(){
        if (mTrain.isEyeMovementTraining == true){
            //如果是眼动训练，关闭视线追踪服务
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
        }
    }

    @ExperimentalCamera2Interop
    private fun startBehavior(){
        if (isOpenDisplayViewpoint){
            //启动执行行为分析相机
            startBehaviorCamera()
        }
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
    }

    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
        handler.postDelayed({
            openGazeTracker()
        },200)
    }

    override fun onResume() {
        super.onResume()
        if (isGameStart){
            startKeepTime()
        }
    }

    override fun onPause() {
        super.onPause()
        stopKeepTime()
    }

    override fun onStop() {
        super.onStop()
        closeGazeTracker()
        unbindService(serviceConnection)
    }

    override fun onDestroy() {
        stopBehaviorAnalysis()
        trainWebView.destroy()
        super.onDestroy()
    }

    @ExperimentalCamera2Interop
    override fun onPostMessage(msg: String) {
        val jsonObject = JSONObject(msg)
        val action = jsonObject.optString("action")
        when(action){
            ACTION_GAME_START ->{
                handlerGameStart()
            }
            ACTION_GAME_OVER ->{
                //分数
                val score = jsonObject.optInt("score")
                handlerGameOver(score)
            }
            ACTION_FLIP_FLIP_CLAP ->{
                FlipBeatManager.writeDataToFlipBeat(arrayOf("0xAA","0x55","0xA5","0x5A","0x21","0x01","0x00","0xCC","0x66","0xC6","0x6C"))
            }
        }
    }

    //加载训练
    private fun loadTrain(trainConfig: TrainConfig?){
        Logger.d(TAG, msg = "loadTrain trainConfig = $trainConfig")
        val onlineUrl = trainConfig?.onlineUrl?:""
        if (!TextUtils.isEmpty(onlineUrl)){
            trainWebView.loadUrl(onlineUrl)
            initFlipBeat()
        }else{
            Toast.makeText(this,getString(R.string.str_abnormal_training_configuration),Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    @ExperimentalCamera2Interop
    private fun handlerGameStart(){
        isGameStart = true
        startTime = System.currentTimeMillis()
        startKeepTime()
        startBehavior()
    }

    private fun handlerGameOver(score: Int){
        isGameStart = false
        endTime = System.currentTimeMillis()
        stopKeepTime()

        val trainId = mTrain.trainId
        Logger.d(TAG, msg = "handlerGameOver trainId = $trainId")
        if (trainId != null && startTime > 0 && endTime > 0 && endTime > startTime){
            submitTrainResult(trainId,score)
        }else{
            finishTrain()
        }
    }

    /**
     * 开始计时
     */
    private fun startKeepTime(){
        stopKeepTime()
        keepTimeJob = launch(Dispatchers.Default) {
            while(true){
                delay(1000)
                withContext(Dispatchers.Main){
                    duration++
                    val time = TimeUtils.parseTimeToTimeString((duration * 1000).toLong(), "mm:ss")
                    tvKeepTime.text = time
                    if (duration >= maxDuration){
                        trainOverTime()
                    }
                }
            }
        }
    }

    /**
     * 本次训练超时
     */
    private fun trainOverTime(){
        stopKeepTime()
        //显示训练建议弹窗
        val trainSuggestionDialog = TrainSuggestionDialog(this)
        trainSuggestionDialog.show()
        val plannedDuration = mTrain.plannedDuration?:0
        //向上取整
        val minutes = ceil(plannedDuration.toFloat() / 60).toInt()
        Logger.d(TAG, msg = "trainOverTime plannedDuration = $plannedDuration, limitType = ${mTrain.limitType}")
        when(mTrain.limitType){
            LimitType.TODAY.value ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everyday_train_time_do_not_exceed,minutes))
            }
            LimitType.CATEGORY.value ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_type_train_time_do_not_exceed,minutes))
            }
            else ->{
                trainSuggestionDialog.setPrompt(getString(R.string.str_doctor_recommend_everytime_train_time_do_not_exceed,minutes))
            }
        }
        trainSuggestionDialog.onConfirmClick = {
            trainWebView.gameOver()
        }
        trainSuggestionDialog.startCountDown(this,3000)
    }

    /**
     * 停止计时
     */
    private fun stopKeepTime(){
        keepTimeJob?.cancel()
        keepTimeJob = null
    }

    /**
     * 提交训练结果
     * @param score 训练结果分数
     */
    private fun submitTrainResult(trainId:Int,score:Int){
        commonLoadingDialog = CommonLoadingDialog(this)
        commonLoadingDialog?.show()
        val startTimeStr = TimeUtils.parseTimeToTimeString(startTime, "yyyy-MM-dd HH:mm:ss", ZoneId.of("Asia/Shanghai"))
        val endTimeStr = TimeUtils.parseTimeToTimeString(endTime, "yyyy-MM-dd HH:mm:ss", ZoneId.of("Asia/Shanghai"))
        Logger.d(TAG, msg = "submitTrainResult trainId = $trainId, score = $score, startTimeStr = $startTimeStr, endTimeStr = $endTimeStr")
        trainVM.submitTrainResult(trainId,startTimeStr,endTimeStr,score)
    }

    /**
     * 完成训练
     */
    private fun finishTrain(){
        commonLoadingDialog?.dismiss()
        commonLoadingDialog = null
        val returnIntent = Intent()
        if (startTime > 0 && endTime > 0 && endTime > startTime){
            returnIntent.putExtra(OUTPUT_PARAM_TRAIN_NAME,mTrain.trainName)
            returnIntent.putExtra(OUTPUT_PARAM_TRAIN_TIME,endTime - startTime)
        }
        setResult(Activity.RESULT_OK, returnIntent)
        finish()
    }

    /**
     * 启动行为分析相机
     */
    @ExperimentalCamera2Interop
    private fun startBehaviorCamera(){
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
            try {
                cameraProvider.unbindAll()
                cameraProvider.bindToLifecycle(this, cameraSelector, imageAnalysis)

                startBehaviorAnalysis()

            } catch (e: Exception) {
                // 处理相机绑定错误
                Logger.e(TAG, msg = "startBehaviorCamera Exception e = ${e.message}")
                e.printStackTrace()
                stopBehaviorAnalysis()
            }
        }, ContextCompat.getMainExecutor(this))
    }

    /**
     * 发送ImageProxy到行为指导图像分析队列
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun sendImageToBehaviorQueue(image: ImageProxy){
        launch {
            try {
                behaviorAnalysisQueue?.let {
                    val closedForSend = it.isClosedForSend
                    if (!closedForSend){
                        it.send(image)
                    }
                }
            }catch (e:Exception){
                if (BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            }
        }
    }

    /**
     * 启动行为分析任务
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun startBehaviorAnalysis(){
        Logger.d(TAG, msg = "startBehaviorAnalysis")
        behaviorAnalysisJob = launch(Dispatchers.Default) {
            while (true){
                behaviorAnalysisQueue?.let {
                    val closedForReceive = it.isClosedForReceive
                    if (!closedForReceive){
                        val image = it.receive()
                        val result = withContext(Dispatchers.IO){
                            if (BehaviorGuidanceManager.checkIsInit()){
                                val imageBytes = GTUtils.getDataFromImage(image)
                                BehaviorGuidanceManager.frameProcess(imageBytes, image.width, image.height,0,cameraFov)
                            }else{
                                null
                            }
                        }
                        withContext(Dispatchers.Main){
                            if (result != null){
                                handlerBehaviorAnalysisResult(result)
                            }
                            image.close()
                        }
                    }
                }
            }
        }.also { job ->
            //当发送任务完成时，关闭Channel。cause 参数为 null，因异常而完成，cause 参数将包含异常对象
            job.invokeOnCompletion { cause ->
                Logger.d(TAG, msg = "startBehaviorAnalysis invokeOnCompletion $cause")
                behaviorAnalysisQueue?.close()
                behaviorAnalysisQueue = null
            }
        }
        behaviorAnalysisQueue = Channel(1, BufferOverflow.DROP_OLDEST) {
            it.close()
        }
    }

    /**
     * 停止行为分析任务
     */
    private fun stopBehaviorAnalysis(){
        Logger.d(TAG, msg = "stopBehaviorAnalysis")
        behaviorAnalysisJob?.cancel()
        behaviorAnalysisJob = null
    }

    /**
     * 处理行为分析结果
     */
    private fun handlerBehaviorAnalysisResult(result: HashMap<String, Any>){
        val json = gson.toJson(result)
        val monitorResult = gson.fromJson(json, MonitorResult::class.java)
        when {
            monitorResult.findRes?.label == true -> {
                //未检测到人脸
                showTrainWarning(getString(R.string.str_come_back_to_practice),"train_no_detect_face.wav")
            }
            mTrain.mustGlasses == true && monitorResult.wearingColorGlassesRedBlueRes?.label == true -> {
                //需要佩戴红蓝眼镜的训练
                showTrainWarning(getString(R.string.str_please_wear_red_blue_glasses_correctly),"train_red_blue_glasses.wav")
            }
            mTrain.mustGlasses == true && monitorResult.wearingColorGlassesBlueRedRes?.label == true -> {
                //需要佩戴蓝红眼镜的训练
                showTrainWarning(getString(R.string.str_please_wear_blue_red_glasses_correctly),"train_blue_red_glasses.wav")
            }
            monitorResult.longDistanceRes?.label == true -> {
                //距离太远
                showTrainWarning(getString(R.string.str_too_far_from_the_screen),"train_keep_distance_long.wav")
            }
            monitorResult.shortDistanceRes?.label == true -> {
                //距离太近
                showTrainWarning(getString(R.string.str_too_close_to_the_screen),"train_keep_distance_short.wav")
            }
            monitorResult.isInScreenCenterRes?.label == true -> {
                //未在屏幕中心
                showTrainWarning(getString(R.string.str_please_face_the_screen),"train_keep_center.wav")
            }
            monitorResult.bodyRes?.label == true
                    || monitorResult.headHorizontalRes?.label == true
                    || monitorResult.headVerticalRes?.label == true -> {
                //高低肩/歪头/低头
                showTrainWarning(getString(R.string.str_please_keep_correct_posture),"train_correct_sit.wav")
            }
            monitorResult.focusRes?.label == true -> {
                //不专注
                showTrainWarning(getString(R.string.str_please_concentrate_on_your_training),"train_stay_focus.wav")
            }
        }
    }

    //显示训练行为警示
    private fun showTrainWarning(msg: String,speechName:String){
        val timeMillis = System.currentTimeMillis()
        if (timeMillis - lastShowTrainWarningTime > 5000){
            lastShowTrainWarningTime = timeMillis
            llTrainWarning.isVisible = true
            tvTrainWarning.text = msg
            llTrainWarning.postDelayed({
                llTrainWarning.isVisible = false
            },3000)

            if (LocaleManager.getLanguage() == "en"){
                PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/$speechName"))
            }else{
                PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/$speechName"))
            }
        }
    }

    override fun enableBack(): Boolean {
        return false
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}