package com.mitdd.gazetracker.medicalhome.train

import android.content.Context
import android.util.AttributeSet
import android.webkit.JavascriptInterface
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView

/**
 * FileName: TrainWebView
 * Author by lilin,Date on 2024/10/10 14:48
 * PS: Not easy to write code, please indicate.
 */
class TrainWebView : CustomWebView {

    companion object{
        private val TAG = TrainWebView::class.java.simpleName
    }

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    private var trainActionListener: TrainActionListener? = null

    fun setTrainActionListener(listener: TrainActionListener?){
        trainActionListener = listener
    }

    /**
     * 训练结束
     */
    fun gameOver(){
        val event = HashMap<String, String>()
        event["action"] = "outerGameOver"
        val json = gson.toJson(event)
        Logger.d(TAG, msg = "gameOver json = $json")
        evaluateJavascript("javascript:window.postMessage('$json')",null)
    }

    interface TrainActionListener{
        fun onPostMessage(msg:String)
    }


    inner class TrainAction{

        //训练回调消息
        @JavascriptInterface
        fun postMessage(msg:String){
            Logger.d(TAG, msg = "postMessage msg = $msg")
            trainActionListener?.onPostMessage(msg)
        }
    }

}