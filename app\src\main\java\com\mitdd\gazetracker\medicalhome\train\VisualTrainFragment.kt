package com.mitdd.gazetracker.medicalhome.train

import android.bluetooth.BluetoothDevice
import android.os.Bundle
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.flipbeat.FlipBeatListener
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.mitdd.gazetracker.medicalhome.TimeProgress
import com.mitdd.gazetracker.medicalhome.bean.Train
import com.mitdd.gazetracker.medicalhome.bean.TrainCategory
import com.mitdd.gazetracker.medicalhome.bean.VisionTherapy
import com.mitdd.gazetracker.medicalhome.dialog.ConnectFlipDialog
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus
import com.mitdd.gazetracker.medicalhome.vm.TrainViewModel
import com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.Gender

/**
 * FileName: VisualTrainFragment
 * Author by lilin,Date on 2024/9/26 14:20
 * PS: Not easy to write code, please indicate.
 * 视觉训练疗法页面
 */
class VisualTrainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = VisualTrainFragment::class.java.simpleName
        const val INPUT_PARAM_MODULE_NAME = "moduleName"
        const val INPUT_PARAM_IS_FULL = "isFull"

        fun newInstance(moduleName:String,isFull:Boolean): VisualTrainFragment {
            val fragment = VisualTrainFragment()
            val args = Bundle()
            args.putString(INPUT_PARAM_MODULE_NAME,moduleName)
            args.putBoolean(INPUT_PARAM_IS_FULL,isFull)
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_visual_train
    }

    private val tvVisualTrain by id<TextView>(R.id.tv_visual_train)
    private val llFlipBeatState by id<LinearLayout>(R.id.ll_flip_beat_state)
    private val tvFlipBeatState by id<TextView>(R.id.tv_flip_beat_state)
    private val timeProgress by id<TimeProgress>(R.id.treatment_time_progress)

    private val treatmentVM by activityViewModels<TreatmentViewModel>()
    private val trainVM by activityViewModels<TrainViewModel>()

    //模块名称
    private var mModuleName = ""
    private var isFUll = false

    override fun initParam() {
        super.initParam()
        val arguments = arguments
        if (arguments != null){
            mModuleName = arguments.getString(INPUT_PARAM_MODULE_NAME,getString(R.string.str_visual_training_therapy))
            isFUll = arguments.getBoolean(INPUT_PARAM_IS_FULL)
        }
    }

    override fun initView() {
        super.initView()
        FlipBeatManager.registerFlipBeatListener(flipBeatListener)
        initListener()

        tvVisualTrain.text = mModuleName

        val accountInfo = UserManager.getAccountInfo()
        when(accountInfo?.gender){
            Gender.MALE.value ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_male)
            }
            else ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_female)
            }
        }
        timeProgress.getSeekBar().progressDrawable = ContextCompat.getDrawable(mActivity,R.drawable.seekbar_visual_train_duration_progress_drawable)
    }

    override fun initData() {
        super.initData()
    }

    override fun initObserver() {
        super.initObserver()
        treatmentVM.curTreatmentLiveData.observe(this){
            trainVM.getTodayVisionTherapy()
        }
        trainVM.todayVisionTherapyLiveData.observe(this){
            refreshVisionTherapy(it)
        }
    }

    private fun initListener(){
        llFlipBeatState.setOnSingleClickListener {
            ConnectFlipDialog(mActivity).show()
        }
    }

    private fun refreshVisionTherapy(visionTherapy: VisionTherapy?){
        updatePlannedDuration()
        updateTreatmentDuration()
        val isBind = UserManager.isBind()
        if (!isBind){
            llFlipBeatState.isVisible = false
            //未绑定
            showVisualTrainUnBind()
            return
        }
        val treatmentInfo = UserManager.getTreatmentInfo()
        val trainCategories = visionTherapy?.list
        if (trainCategories.isNullOrEmpty() || treatmentInfo == null || treatmentInfo.courseStatus != TreatmentStatus.RUNNING.called){
            llFlipBeatState.isVisible = false
            //未开通
            showVisualTrainNoOpen()
            return
        }
        llFlipBeatState.isVisible = checkMustBluetoothFlip(visionTherapy)
        if (visionTherapy.isCategory == true){
            showTrainCategory(trainCategories)
        }else{
            showTrainList(trainCategories)
        }
    }

    private fun showVisualTrainUnBind(){
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_train_container, VisualTrainUnBindFragment.newInstance())
            .commitAllowingStateLoss()
    }

    private fun showVisualTrainNoOpen(){
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_train_container, VisualTrainNoOpenFragment.newInstance())
            .commitAllowingStateLoss()
    }

    private fun showTrainCategory(trainCategories:List<TrainCategory>){
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_train_container,
            TrainCategoryListFragment.newInstance(isFUll)
        )
            .commitAllowingStateLoss()
        LiveEventBus.get<List<TrainCategory>>(TrainCategoryListFragment.INPUT_PARAM_TRAIN_CATEGORY_LIST).post(trainCategories)
    }

    private fun showTrainList(trainCategories:List<TrainCategory>){
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_train_container, TrainListFragment.newInstance(isFUll))
            .commitAllowingStateLoss()
        val trains: MutableList<Train> = mutableListOf()
        trainCategories.forEachIndexed { _, trainCategory ->
            trainCategory.items?.forEachIndexed { _, train ->
                trains.add(train)
            }
        }
        LiveEventBus.get<List<Train>>(TrainListFragment.INPUT_PARAM_TRAIN_LIST).post(trains)
    }

    /**
     * 更新计划治疗时长
     */
    private fun updatePlannedDuration(){
        timeProgress.getPlannedDurationView().isVisible = trainVM.visionTherapy != null
        timeProgress.setPlannedDuration(trainVM.plannedDuration)
    }

    /**
     * 更新已治疗时长
     */
    private fun updateTreatmentDuration(){
        timeProgress.getTrainDurationView().isVisible = trainVM.visionTherapy != null
        timeProgress.setTreatmentDuration(trainVM.treatmentDuration)
    }

    /**
     * 检查是否需要翻转拍
     */
    private fun checkMustBluetoothFlip(visionTherapy: VisionTherapy?) : Boolean{
        if (visionTherapy != null){
            val trainCategoryList = visionTherapy.list?: emptyList()
            for (trainCategory in trainCategoryList){
                val trainList = trainCategory.items?: emptyList()
                for (train in trainList){
                    if (train.mustBluetoothFlip == true){
                        return true
                    }
                }
            }
        }
        return false
    }

    private fun updateFlipConnected(isConnected:Boolean){
        Logger.d(TAG, msg = "updateFlipConnected isConnected = $isConnected")
        llFlipBeatState.isSelected = isConnected
        tvFlipBeatState.text = if (isConnected){
            getString(R.string.str_connected)
        }else{
            getString(R.string.str_unconnected)
        }
    }

    override fun onDestroyView() {
        FlipBeatManager.unRegisterFlipBeatListener(flipBeatListener)
        super.onDestroyView()
    }

    private val flipBeatListener = object : FlipBeatListener {
        override fun onConnectionStateChange(device: BluetoothDevice, state: FlipBeatState) {
            updateFlipConnected(state == FlipBeatState.CONNECTED)
        }
    }

}