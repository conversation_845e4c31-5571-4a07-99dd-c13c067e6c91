package com.mitdd.gazetracker.medicalhome.train

import com.airdoc.component.common.base.BaseCommonFragment
import com.mitdd.gazetracker.R

/**
 * FileName: VisualTrainNoOpenFragment
 * Author by lilin,Date on 2024/10/16 19:44
 * PS: Not easy to write code, please indicate.
 * 视觉训练未开通
 */
class VisualTrainNoOpenFragment : BaseCommonFragment(){

    companion object{
        private val TAG = VisualTrainNoOpenFragment::class.java.simpleName

        fun newInstance(): VisualTrainNoOpenFragment {
            val fragment = VisualTrainNoOpenFragment()
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_visual_train_no_open
    }
}