package com.mitdd.gazetracker.medicalhome.train

import com.airdoc.component.common.base.BaseCommonFragment
import com.mitdd.gazetracker.R

/**
  * FileName: VisualTrainUnBindFragment
  * Author by lilin,Date on 2024/10/17 16:35
  * PS: Not easy to write code, please indicate.
 * 未绑定账号时，训练疗法缺省页
*/
class VisualTrainUnBindFragment : BaseCommonFragment() {

    companion object{
        private val TAG = VisualTrainUnBindFragment::class.java.simpleName

        fun newInstance(): VisualTrainUnBindFragment {
            val fragment = VisualTrainUnBindFragment()
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_visual_train_unbind
    }
}