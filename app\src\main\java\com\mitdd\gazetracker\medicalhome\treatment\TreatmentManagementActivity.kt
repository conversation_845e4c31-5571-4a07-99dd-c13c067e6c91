package com.mitdd.gazetracker.medicalhome.treatment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.widget.CommonEmptyView
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel

/**
 * FileName: TreatmentManagementActivity
 * Author by lilin,Date on 2024/11/22 19:09
 * PS: Not easy to write code, please indicate.
 */
class TreatmentManagementActivity : GTBaseActivity() {

    companion object{
        private val TAG = TreatmentManagementActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, TreatmentManagementActivity::class.java)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val rvTreatments by id<RecyclerView>(R.id.rv_treatments)
    private val viewEmpty by id<CommonEmptyView>(R.id.view_empty)

    private val treatmentVM by viewModels<TreatmentViewModel>()
    private val treatmentManagementAdapter = TreatmentManagementAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_treatment_management)

        initView()
        initObserver()
        initData()
    }

    private fun initView(){
        initListener()

        rvTreatments.layoutManager = LinearLayoutManager(this)
        rvTreatments.adapter = treatmentManagementAdapter

        viewEmpty.getEmptyRoot().setBackgroundResource(R.drawable.common_white_round_25_bg)
        viewEmpty.getEmptyIcon().setImageResource(R.drawable.icon_treatment_empty)
        viewEmpty.getEmptyPrompt().apply {
            text = getString(R.string.str_no_data_available)
            textSize = 12f
            setTextColor(ContextCompat.getColor(this@TreatmentManagementActivity,
                com.airdoc.component.common.R.color.color_333333))
        }
    }

    private fun initListener(){
        ivBack.setOnSingleClickListener {
            finish()
        }
    }

    private fun initObserver(){
        treatmentVM.treatmentsLiveData.observe(this){
            val treatments = mutableListOf<TreatmentInfo>()
            val records = it?.records
            if (!records.isNullOrEmpty()){
                rvTreatments.isVisible = true
                viewEmpty.isVisible = false
                treatments.add(TreatmentInfo().apply {
                    type = TreatmentInfo.TYPE_TITLE
                })
                records.forEach { treatment ->
                    treatments.add(treatment.apply {
                        type = TreatmentInfo.TYPE_TREATMENT
                    })
                }
                treatmentManagementAdapter.setTreatmentData(treatments)
            }else{
                rvTreatments.isVisible = false
                viewEmpty.isVisible = true
            }
        }
    }

    private fun initData(){
        treatmentVM.getTreatments()
    }

}