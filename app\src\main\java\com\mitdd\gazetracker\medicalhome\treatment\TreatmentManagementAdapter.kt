package com.mitdd.gazetracker.medicalhome.treatment

import android.graphics.Color
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.isVisible
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus

/**
 * FileName: TreatmentManagementAdapter
 * Author by lilin,Date on 2024/11/22 19:41
 * PS: Not easy to write code, please indicate.
 */
class TreatmentManagementAdapter : RecyclerView.Adapter<TreatmentManagementAdapter.TreatmentHolder>() {

    private var treatments: MutableList<TreatmentInfo> = mutableListOf()

    fun setTreatmentData(data:List<TreatmentInfo>){
        treatments.clear()
        treatments.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TreatmentHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_treatment_management, parent, false)
        return TreatmentHolder(view)
    }

    override fun getItemCount(): Int {
        return treatments.size
    }

    override fun onBindViewHolder(holder: TreatmentHolder, position: Int) {
        if (position in treatments.indices){
            holder.bind(treatments[position])
        }
    }

    inner class TreatmentHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val clRoot: ConstraintLayout = itemView.findViewById(R.id.cl_root)
        private val horizontalLine1: View = itemView.findViewById(R.id.horizontal_line1)
        private val horizontalLine2: View = itemView.findViewById(R.id.horizontal_line2)
        private val tvCardNumber: TextView = itemView.findViewById(R.id.tv_card_number)
        private val tvTreatmentPeriod: TextView = itemView.findViewById(R.id.tv_treatment_period)
        private val tvCardOpenTime: TextView = itemView.findViewById(R.id.tv_card_open_time)
        private val tvExpirationTime: TextView = itemView.findViewById(R.id.tv_expiration_time)

        fun bind(treatmentInfo: TreatmentInfo){
            val position = bindingAdapterPosition
            horizontalLine2.isVisible = position == treatments.size - 1
            if (treatmentInfo.type == TreatmentInfo.TYPE_TITLE){
                clRoot.setBackgroundColor(Color.parseColor("#EFF3F6"))
                tvCardNumber.text = itemView.context.getString(R.string.str_card_number)
                tvTreatmentPeriod.text = itemView.context.getString(R.string.str_treatment_period)
                tvCardOpenTime.text = itemView.context.getString(R.string.str_card_opening_date)
                tvExpirationTime.text = itemView.context.getString(R.string.str_expiration_date)
            }else{
                clRoot.background = null
                tvCardNumber.text = treatmentInfo.courseId?:""
                val amount = treatmentInfo.amount
                val unitName = treatmentInfo.unitName
                if (amount != null && !TextUtils.isEmpty(unitName)){
                    tvTreatmentPeriod.text = "${amount}${unitName}"
                }else{
                    tvTreatmentPeriod.text = itemView.context.getString(R.string.str_unknown)
                }
                try {
                    tvCardOpenTime.text = (treatmentInfo.createTime?:"").split(" ")[0]
                }catch (e: Exception){
                    tvCardOpenTime.text = itemView.context.getString(R.string.str_unknown)
                    if (BuildConfig.DEBUG){
                        e.printStackTrace()
                    }
                }
                when (treatmentInfo.courseStatus) {
                    TreatmentStatus.PENDING.called -> {
                        tvExpirationTime.text = itemView.context.getString(R.string.str_suspended)
                    }
                    TreatmentStatus.INACTIVE.called -> {
                        tvExpirationTime.text = itemView.context.getString(R.string.str_not_activated)
                    }
                    TreatmentStatus.COMPLETED.called -> {
                        tvExpirationTime.text = itemView.context.getString(R.string.str_over)
                    }
                    else -> {
                        try {
                            tvExpirationTime.text = (treatmentInfo.dueTime?:"").split(" ")[0]
                        }catch (e: Exception){
                            tvExpirationTime.text = itemView.context.getString(R.string.str_unknown)
                            if (BuildConfig.DEBUG){
                                e.printStackTrace()
                            }
                        }
                    }
                }
            }
        }

    }
}