package com.mitdd.gazetracker.medicalhome.treatment

import android.content.Context
import android.text.TextUtils
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.dialog.task.ReviewRemindTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentActivationRemindTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentActivationSuccessTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentActivationTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentExpirationRemindTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentExpirationTask
import com.mitdd.gazetracker.medicalhome.dialog.task.TreatmentPauseTask
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.UserPreference
import java.time.ZoneId

/**
 * FileName: TreatmentManager
 * Author by lilin,Date on 2024/11/28 16:07
 * PS: Not easy to write code, please indicate.
 */
object TreatmentManager {

    private val TAG = TreatmentManager::class.java.simpleName

    /**
     * 显示疗程激活提醒弹窗
     */
    fun showTreatmentActivationRemindDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                            treatmentInfo: TreatmentInfo, isImmediate:Boolean = false,
                                            confirmClick:(() -> Unit)? = null, cancelClick:(() -> Unit)? = null){
        val courseId = treatmentInfo.courseId?:""
        val amount = treatmentInfo.amount?:""
        val unitName = treatmentInfo.unitName?:""
        val period = "${amount}${unitName}"
        Logger.d(TAG, msg = "showTreatmentActivationRemindDialog courseId = $courseId, period = $period")
        if (!TextUtils.isEmpty(courseId)){
            dialogTaskManager.addTask(TreatmentActivationRemindTask(context,courseId,period).apply {
                this.isImmediate = isImmediate
                setConfirmClick(confirmClick)
                setCancelClick(cancelClick)
            })
        }
    }

    /**
     * 显示疗程激活弹窗
     */
    fun showTreatmentActivationDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                      treatmentInfo: TreatmentInfo, isImmediate:Boolean = false,
                                      confirmClick:(() -> Unit)? = null, cancelClick:(() -> Unit)? = null){
        val phone = UserManager.getAccountInfo()?.phone?:""
        val courseId = treatmentInfo.courseId?:""
        Logger.d(TAG, msg = "showTreatmentActivationDialog phone = $phone, courseId = $courseId")
        if (!TextUtils.isEmpty(phone) &&!TextUtils.isEmpty(courseId)){
            dialogTaskManager.addTask(TreatmentActivationTask(context,phone).apply {
                this.isImmediate = isImmediate
                setConfirmClick(confirmClick)
                setCancelClick(cancelClick)
            })
        }
    }

    /**
     * 显示疗程激活成功弹窗
     */
    fun showTreatmentActivationSuccessDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                             treatmentInfo: TreatmentInfo, isImmediate:Boolean = false,
                                             confirmClick:(() -> Unit)? = null){
        val dueTime = treatmentInfo.dueTime?.split(" ")?.get(0)?:""
        val hospitalName = UserManager.getAccountInfo()?.hospitalName?:context.getString(R.string.str_unknown)
        Logger.d(TAG, msg = "showTreatmentActivationSuccessDialog dueTime = $dueTime, hospitalName = $hospitalName")
        if (TextUtils.isEmpty(dueTime)){
            return
        }
        dialogTaskManager.addTask(TreatmentActivationSuccessTask(context,dueTime,hospitalName).apply {
            this.isImmediate = isImmediate
            setConfirmClick(confirmClick)
        })
    }

    /**
     * 显示疗程暂停弹窗
     */
    fun showTreatmentPauseDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                 isImmediate:Boolean = false, confirmClick:(() -> Unit)? = null,
                                 cancelClick:(() -> Unit)? = null){
        val hospitalName = UserManager.getAccountInfo()?.hospitalName?:""
        Logger.d(TAG, msg = "showTreatmentPauseDialog hospitalName = $hospitalName")
        dialogTaskManager.addTask(TreatmentPauseTask(context,hospitalName).apply {
            this.isImmediate = isImmediate
            setConfirmClick(confirmClick)
            setCancelClick(cancelClick)
        })
    }

    /**
     * 显示疗程过期弹窗
     */
    fun showTreatmentExpirationDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                      treatmentInfo: TreatmentInfo, isImmediate:Boolean = false,
                                      confirmClick:(() -> Unit)? = null){
        val dueTime = treatmentInfo.dueTime?.split(" ")?.get(0)?:""
        val hospitalName = UserManager.getAccountInfo()?.hospitalName?:context.getString(R.string.str_unknown)
        Logger.d(TAG, msg = "showTreatmentExpirationDialog dueTime = $dueTime, hospitalName = $hospitalName")
        if (TextUtils.isEmpty(dueTime)){
            return
        }
        dialogTaskManager.addTask(TreatmentExpirationTask(context,dueTime,hospitalName).apply {
            this.isImmediate = isImmediate
            setConfirmClick(confirmClick)
        })
    }

    /**
     * 显示疗程过期提醒弹窗
     * @param advanceDays 提前天数
     */
    fun showTreatmentExpirationRemindDialog(context: Context, dialogTaskManager: DialogTaskManager,
                                            treatmentInfo: TreatmentInfo, advanceDays:Int, isImmediate:Boolean = false,
                                            confirmClick:(() -> Unit)? = null){
        //过期提醒时间
        val dueRemindTime = treatmentInfo.dueRemindTime?.split(" ")?.get(0)?:""
        val dueTime = treatmentInfo.dueTime?.split(" ")?.get(0)?:""
        Logger.d(TAG, msg = "showTreatmentExpirationRemindDialog dueTime = $dueTime, dueRemindTime = $dueRemindTime")
        if (TextUtils.isEmpty(dueTime) ||TextUtils.isEmpty(dueRemindTime)){
            return
        }
        try {
            //将时间从"yyyy-MM-dd HH:mm:ss"转成"yyyy-MM-dd"
            val remindTimes = dueRemindTime.split(" ")
            val remindTime = remindTimes[0]
            val oldRemindTime = MMKVManager.decodeString(UserPreference.SHOW_TREATMENT_EXPIRATION_REMIND_DIALOG_DATE)
            val today = TimeUtils.parseTimeToTimeString(System.currentTimeMillis(), "yyyy-MM-dd", ZoneId.of("Asia/Shanghai"))
            Logger.d(TAG, msg = "showTreatmentExpirationRemindDialog remindTime = $remindTime, oldRemindTime = $oldRemindTime, today = $today")
            if (!TextUtils.isEmpty(remindTime) && remindTime == today &&
                (oldRemindTime == null || oldRemindTime != remindTime)){
                val hospitalName = UserManager.getAccountInfo()?.hospitalName?:context.getString(R.string.str_unknown)
                dialogTaskManager.addTask(TreatmentExpirationRemindTask(context,advanceDays,dueTime,hospitalName).apply {
                    this.isImmediate = isImmediate
                    setConfirmClick(confirmClick)
                })
            }
        }catch (e:Exception){
            if (BuildConfig.DEBUG){
                e.printStackTrace()
            }
        }

    }

    /**
     * 显示复查提醒弹窗
     */
    fun showReviewRemindDialog(context: Context, dialogTaskManager: DialogTaskManager,
                               advanceDays:Int,isImmediate:Boolean = false, confirmClick:(() -> Unit)? = null){
        //复查日期
        val reviewDate = UserManager.getAccountInfo()?.reviewDate?.split(" ")?.get(0)?:""
        val reviewRemindDate = UserManager.getAccountInfo()?.reviewRemindDate?.split(" ")?.get(0)?:""
        Logger.d(TAG, msg = "showReviewRemindDialog reviewDate = $reviewDate, reviewRemindDate = $reviewRemindDate")
        if (TextUtils.isEmpty(reviewDate) || TextUtils.isEmpty(reviewRemindDate)){
            return
        }
        try {
            val oldRemindTime = MMKVManager.decodeString(UserPreference.SHOW_REVIEW_REMIND_DIALOG_DATE)
            val today = TimeUtils.parseTimeToTimeString(System.currentTimeMillis(), "yyyy-MM-dd", ZoneId.of("Asia/Shanghai"))
            Logger.d(TAG, msg = "showReviewRemindDialog reviewRemindDate = $reviewRemindDate, oldRemindTime = $oldRemindTime, today = $today")
            if (!TextUtils.isEmpty(reviewRemindDate) && reviewRemindDate == today &&
                (oldRemindTime == null || oldRemindTime != reviewRemindDate)){
                val hospitalName = UserManager.getAccountInfo()?.hospitalName?:context.getString(R.string.str_unknown)
                dialogTaskManager.addTask(ReviewRemindTask(context,advanceDays,reviewDate,hospitalName).apply {
                    this.isImmediate = isImmediate
                    setConfirmClick(confirmClick)
                })
            }
        }catch (e:Exception){
            if (BuildConfig.DEBUG){
                e.printStackTrace()
            }
        }
    }

}