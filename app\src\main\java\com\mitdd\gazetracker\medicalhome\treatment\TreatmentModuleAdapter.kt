package com.mitdd.gazetracker.medicalhome.treatment

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.FrameLayout
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.dp2px
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhome.mask.MaskTherapyFragment
import com.mitdd.gazetracker.medicalhome.train.VisualTrainFragment
import com.mitdd.gazetracker.medicalhome.bean.Module

/**
 * FileName: TreatmentModuleAdapter
 * Author by lilin,Date on 2024/10/9 11:01
 * PS: Not easy to write code, please indicate.
 * 治疗模块Adapter
 */
class TreatmentModuleAdapter: RecyclerView.Adapter<TreatmentModuleAdapter.TreatmentModuleHolder>() {

    private val TAG = TreatmentModuleAdapter::class.java.simpleName

    private var treatmentModules: MutableList<Module> = mutableListOf()
    private var fragmentManager: FragmentManager? = null

    fun setTreatmentModuleData(data:List<Module>, fragmentManager:FragmentManager){
        treatmentModules.clear()
        treatmentModules.addAll(data)
        this.fragmentManager = fragmentManager
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TreatmentModuleHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_treatment_module, parent, false)
        return TreatmentModuleHolder(view)
    }

    override fun getItemCount(): Int {
        return treatmentModules.size
    }

    override fun onBindViewHolder(holder: TreatmentModuleHolder, position: Int) {
        if (position in treatmentModules.indices){
            holder.bind(treatmentModules[position],position)
        }
    }

    inner class TreatmentModuleHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val flModuleRoot: FrameLayout = itemView.findViewById(R.id.fl_module_root)

        fun bind(module: Module, position:Int){
            flModuleRoot.id += position
            val isFull = treatmentModules.size == 1
            val rootParams = flModuleRoot.layoutParams as LayoutParams
            if (isFull){
                rootParams.width = LayoutParams.MATCH_PARENT
            }else{
                rootParams.width = 452.dp2px(itemView.context)
            }
            flModuleRoot.layoutParams = rootParams
            when(module.moduleKey){
                TreatmentModule.OCCLUSION_THERAPY.moduleKey ->{
                    //数字遮盖疗法
                    fragmentManager?.let {
                        val beginTransaction = it.beginTransaction()
                        beginTransaction.replace(flModuleRoot.id, MaskTherapyFragment.newInstance(module.moduleName?:"",isFull))
                        beginTransaction.commitAllowingStateLoss()
                    }
                }
                TreatmentModule.VISION_THERAPY.moduleKey ->{
                    //视觉训练疗法
                    fragmentManager?.let {
                        val beginTransaction = it.beginTransaction()
                        beginTransaction.replace(flModuleRoot.id, VisualTrainFragment.newInstance(module.moduleName?:"",isFull))
                        beginTransaction.commitAllowingStateLoss()
                    }
                }
            }
        }
    }

}