package com.mitdd.gazetracker.medicalhome.treatment

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * FileName: TreatmentModuleItemDecoration
 * Author by lilin,Date on 2024/10/9 15:22
 * PS: Not easy to write code, please indicate.
 */
class TreatmentModuleItemDecoration(private var space:Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val position = parent.getChildAdapterPosition(view)
        if (position != 0){
            outRect.left = space
        }
    }

}