package com.mitdd.gazetracker.medicalhome.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.medicalhome.repository.HomeRepository
import com.mitdd.gazetracker.medicalhome.bean.MedicalHomeProfile
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: HomeViewModel
 * Author by lilin,Date on 2024/11/27 11:12
 * PS: Not easy to write code, please indicate.
 */
class HomeViewModel : ViewModel() {

    companion object{
        private val TAG = HomeViewModel::class.java.name
    }

    private val homeRepository by lazy { HomeRepository() }

    //遮盖疗法状态
    val maskTherapyStateLivedata = MutableLiveData<Boolean>()
    //家庭版配置信息
    val medicalHomeProfileLiveData = MutableLiveData<MedicalHomeProfile?>()

    /**
     * 获取家庭版配置信息
     */
    fun getMedicalHomeProfile(){
        viewModelScope.launch {
            MutableStateFlow(homeRepository.getMedicalHomeProfile()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getHomeEditionProfile onSuccess")
                    medicalHomeProfileLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getHomeEditionProfile onDataEmpty")
                    medicalHomeProfileLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getHomeEditionProfile onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    medicalHomeProfileLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getHomeEditionProfile onError = $it")
                    medicalHomeProfileLiveData.postValue(null)
                }
            }
        }
    }

}