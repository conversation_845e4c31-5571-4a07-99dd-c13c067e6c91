package com.mitdd.gazetracker.medicalhome.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.repository.MaskRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: MaskViewModel
 * Author by lilin,Date on 2024/10/11 11:48
 * PS: Not easy to write code, please indicate.
 * 遮盖疗法界面ViewModel
 */
class MaskViewModel : ViewModel() {

    companion object{
        private val TAG = MaskViewModel::class.java.name
    }

    private val maskRepository by lazy { MaskRepository() }

    //今日数字遮盖疗法信息
    val occlusionTherapyLiveData = MutableLiveData<CureInfo?>()
    val occlusionTherapy get() = occlusionTherapyLiveData.value
    //当前治疗时长，单位秒
    val treatmentDuration get() = occlusionTherapy?.trainingDuration?:0
    //计划治疗时长，单位秒
    val plannedDuration get() = occlusionTherapy?.plannedDuration?:0
    //弱势眼 左left 右right
    val eyePosition get() = occlusionTherapy?.position?: AmblyopicEye.LEFT.value
    //是否已完成今日治疗
    val isFinishUp get() = occlusionTherapy?.isFinishUp?:false

    //遮盖疗法状态 true表示开启，false表示关闭
    val mtStateLiveData = MutableLiveData<Boolean>()

    /**
     * 获取今日数字遮盖疗法信息
     */
    fun getTodayOcclusionTherapy(){
        viewModelScope.launch {
            MutableStateFlow(maskRepository.getTodayOcclusionTherapy()).collectResponse{
                onSuccess = { it,_,_->
                    Logger.d(TAG, msg = "getTodayOcclusionTherapy onSuccess")
                    setOcclusionTherapy(it)
                }
                onDataEmpty = {_,_->
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onDataEmpty")
                    setOcclusionTherapy(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    setOcclusionTherapy(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onError = $it")
                    setOcclusionTherapy(null)
                }
            }
        }
    }

    fun setOcclusionTherapy(cureInfo: CureInfo?){
        //保存遮盖疗法参数
        MaskManager.setCoverChannel(cureInfo?.blurChannel)
        MaskManager.setCoverMode(cureInfo?.blurMode)
        MaskManager.setCoverArea(cureInfo?.blurRadius)
        MaskManager.setCoverRange(cureInfo?.blurSigma)
        MaskManager.setAmblyopicEye(cureInfo?.position)

        occlusionTherapyLiveData.postValue(cureInfo)
    }

    /**
     * 设置遮盖疗法状态
     * @param state true表示开启，false表示关闭
     */
    fun setMTState(state:Boolean){
        mtStateLiveData.postValue(state)
    }

}