package com.mitdd.gazetracker.medicalhome.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.medicalhome.bean.TrainConfig
import com.mitdd.gazetracker.medicalhome.bean.VisionTherapy
import com.mitdd.gazetracker.medicalhome.repository.TrainRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: TrainViewModel
 * Author by lilin,Date on 2024/10/10 10:02
 * PS: Not easy to write code, please indicate.
 * 训练ViewModel
 */
class TrainViewModel : ViewModel() {

    companion object{
        private val TAG = TrainViewModel::class.java.name
    }

    private val trainRepository by lazy { TrainRepository() }

    //今日视觉训练疗法信息
    val todayVisionTherapyLiveData = MutableLiveData<VisionTherapy?>()
    val visionTherapy get() = todayVisionTherapyLiveData.value
    //当前治疗时长，单位秒
    val treatmentDuration get() = visionTherapy?.trainingDuration?:0
    //计划治疗时长，单位秒
    val plannedDuration get() = visionTherapy?.plannedDuration?:0
    //是否允许训练
    val allowTraining get() = visionTherapy?.allowTraining?:false
    //上报训练结果
    val submitTrainResultLiveData = MutableLiveData<Any?>()
    //训练配置
    val trainConfigLiveData = MutableLiveData<TrainConfig?>()

    /**
     * 获取今日视觉训练疗法信息
     */
    fun getTodayVisionTherapy(){
        viewModelScope.launch {
            MutableStateFlow(trainRepository.getTodayVisionTherapy()).collectResponse{
                onSuccess = { it,_,_->
                    Logger.d(TAG, msg = "getTodayVisionTherapy onSuccess")
                    todayVisionTherapyLiveData.postValue(it)
                }
                onDataEmpty = { _,_->
                    Logger.e(TAG, msg = "getTodayVisionTherapy onDataEmpty")
                    todayVisionTherapyLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTodayVisionTherapy onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    todayVisionTherapyLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTodayVisionTherapy onError = $it")
                    todayVisionTherapyLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 提交训练结果
     * @param trainId 训练游戏ID
     * @param startTime 开始时间（yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（yyyy-MM-dd HH:mm:ss）
     * @param score 训练结果分数
     */
    fun submitTrainResult(trainId:Int,startTime:String,endTime:String,score:Int){
        viewModelScope.launch {
            MutableStateFlow(trainRepository.submitTrainResult(trainId, startTime, endTime, score)).collectResponse{
                onSuccess = { _, _, _->
                    Logger.d(TAG, msg = "submitTrainResult onSuccess")
                    submitTrainResultLiveData.postValue(Any())
                }
                onDataEmpty = { _,_->
                    Logger.d(TAG, msg = "submitTrainResult onDataEmpty")
                    submitTrainResultLiveData.postValue(Any())
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "submitTrainResult onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    submitTrainResultLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "submitTrainResult onError = $it")
                    submitTrainResultLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 获取训练配置
     * @param trainId 训练id
     */
    fun getTrainConfig(trainId:Int){
        viewModelScope.launch {
            MutableStateFlow(trainRepository.getTrainConfig(trainId)).collectResponse{
                onSuccess = { it, _, _->
                    Logger.d(TAG, msg = "getTrainConfig onSuccess")
                    trainConfigLiveData.postValue(it)
                }
                onDataEmpty = { _,_->
                    Logger.d(TAG, msg = "getTrainConfig onDataEmpty")
                    trainConfigLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTrainConfig onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    trainConfigLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTrainConfig onError = $it")
                    trainConfigLiveData.postValue(null)
                }
            }
        }
    }

}