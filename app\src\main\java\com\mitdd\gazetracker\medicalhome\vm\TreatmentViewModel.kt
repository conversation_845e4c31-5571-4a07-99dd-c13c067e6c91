package com.mitdd.gazetracker.medicalhome.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.bean.Treatments
import com.mitdd.gazetracker.medicalhome.repository.TreatmentRepository
import com.mitdd.gazetracker.user.UserManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: TreatmentViewModel
 * Author by lilin,Date on 2024/11/28 13:51
 * PS: Not easy to write code, please indicate.
 * 疗程ViewModel
 */
class TreatmentViewModel : ViewModel(){

    companion object{
        private val TAG = TreatmentViewModel::class.java.name
    }

    private val treatmentRepository by lazy { TreatmentRepository() }

    //当前疗程
    val curTreatmentLiveData = MutableLiveData<TreatmentInfo?>()
    //疗程激活成功
    val activationTreatmentSuccessLiveData = MutableLiveData<TreatmentInfo>()
    //当前设备绑定用户的疗程列表
    val treatmentsLiveData = MutableLiveData<Treatments?>()

    /**
     * 获取当前疗程
     */
    fun getCurrentTreatment(){
        viewModelScope.launch {
            MutableStateFlow(treatmentRepository.getCurrentTreatment()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getCurrentTreatment onSuccess")
                    UserManager.setTreatmentInfo(it.current)
                    curTreatmentLiveData.postValue(it.current)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getCurrentTreatment onDataEmpty")
                    UserManager.setTreatmentInfo(null)
                    curTreatmentLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getCurrentTreatment onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    UserManager.setTreatmentInfo(null)
                    curTreatmentLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getCurrentTreatment onError = $it")
                    UserManager.setTreatmentInfo(null)
                    curTreatmentLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 激活疗程
     * @param courseId 卡号
     * @param phone 手机号
     */
    fun activationTreatment(courseId:String,phone:String){
        viewModelScope.launch {
            MutableStateFlow(treatmentRepository.activationTreatment(courseId, phone)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "activationTreatment onSuccess")
                    activationTreatmentSuccessLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "activationTreatment onDataEmpty")
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "activationTreatment onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                }
                onError = {
                    Logger.e(TAG, msg = "activationTreatment onError = $it")
                }
            }
        }
    }

    /**
     * 获取当前设备绑定用户的疗程列表
     */
    fun getTreatments(){
        viewModelScope.launch {
            MutableStateFlow(treatmentRepository.getTreatments()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getTreatments onSuccess")
                    treatmentsLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getTreatments onDataEmpty")
                    treatmentsLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTreatments onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    treatmentsLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTreatments onError = $it")
                    treatmentsLiveData.postValue(null)
                }
            }
        }
    }

}