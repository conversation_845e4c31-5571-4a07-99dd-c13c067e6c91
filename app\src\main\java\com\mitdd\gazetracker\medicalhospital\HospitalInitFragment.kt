package com.mitdd.gazetracker.medicalhospital

import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager

/**
 * FileName: HospitalInitFragment
 * Author by lilin,Date on 2024/12/25 17:38
 * PS: Not easy to write code, please indicate.
 */
class HospitalInitFragment : BaseCommonFragment() {

    companion object{
        private val TAG = HospitalInitFragment::class.java.simpleName

        fun newInstance(): HospitalInitFragment {
            return HospitalInitFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_hospital_init
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val tvGetStarted by id<TextView>(R.id.tv_get_started)

    override fun initView() {
        super.initView()
        initListener()
    }

    override fun initData() {
        super.initData()
        updateDeviceInfo()
    }

    override fun initObserver() {
        super.initObserver()
    }

    private fun initListener(){
        tvGetStarted.setOnSingleClickListener {
            //去绑定
            (mActivity as? com.mitdd.gazetracker.medicalhospital.HospitalMainActivity)?.toBind()
        }
    }

    private fun updateDeviceInfo(){
        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(mActivity,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }
}