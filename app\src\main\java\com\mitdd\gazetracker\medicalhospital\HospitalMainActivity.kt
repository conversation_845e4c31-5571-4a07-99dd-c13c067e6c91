package com.mitdd.gazetracker.medicalhospital

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.airdoc.component.common.ktx.id
import com.airdoc.videobox.MultiClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.mqtt.MQTTInitManager
import com.mitdd.gazetracker.mqtt.listener.IConnectNotifyCallBack
import com.mitdd.gazetracker.update.UpdateDialog
import com.mitdd.gazetracker.update.vm.UpdateViewModel
import com.mitdd.gazetracker.user.BindActivity
import com.mitdd.gazetracker.user.vm.UserViewModel

/**
 * FileName: HospitalMainActivity
 * Author by lilin,Date on 2024/12/24 15:15
 * PS: Not easy to write code, please indicate.
 */
class HospitalMainActivity : GTBaseActivity() {

    companion object{
        private val TAG = HospitalMainActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, HospitalMainActivity::class.java)
            return intent
        }
    }

    private val viewConfig by id<View>(R.id.view_config)

    private val updateVM by viewModels<UpdateViewModel>()
    private val userVM by viewModels<UserViewModel>()

    private var bindLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            userVM.getAccountInfo()
        }
    }

    //MQTT消息监听
    private val iConnectNotifyCallBack = object : IConnectNotifyCallBack {
        override fun onDeviceUnbind() {
            super.onDeviceUnbind()
            userVM.getAccountInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_hospital_main)

        initView()
        initObserver()
        initData()

        initMQTT()
        startGazeTrackerService()
    }

    private fun initView(){
        initListener()
    }

    private fun initListener(){
        MQTTInitManager.addIConnectNotifyCallBack(iConnectNotifyCallBack)
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                DeviceManager.startConfigActivity(this@HospitalMainActivity)
            }
        })
    }

    private fun initObserver(){
        userVM.accountInfoLiveData.observe(this){
            if (it?.accountId != null){
                showHospitalMain()
            }else{
                showHospitalInit()
            }
        }
        updateVM.appUpdateInfoLiveData.observe(this){
            it?.let {
                val url = it.appVersion?.url?:""
                val version = it.appVersion?.version ?: ""
                val introduction = it.appVersion?.introduce?:""
                val appSize = it.appVersion?.appSize?:0
                if (!TextUtils.isEmpty(url)){
                    UpdateDialog(this,url,version,introduction, it.forceUpdate?:false,appSize).show()
                }
            }
        }
    }

    private fun initData(){
        userVM.getAccountInfo()
    }

    override fun onResume() {
        super.onResume()
        updateVM.getAppUpdateInfo()
    }

    fun showHospitalMain(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_hospital_main, HospitalMainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    fun showHospitalInit(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_hospital_main, HospitalInitFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    /**
     * 去绑定
     */
    fun toBind(){
        bindLauncher.launch(BindActivity.createIntent(this))
    }

    /**
     * 开启眼动追踪服务
     */
    private fun startGazeTrackerService(){
        startForegroundService(Intent(this, GazeTrackService::class.java))
    }

    override fun onDestroy() {
        MQTTInitManager.removeIConnectNotifyCallBack(iConnectNotifyCallBack)
        super.onDestroy()
    }

}