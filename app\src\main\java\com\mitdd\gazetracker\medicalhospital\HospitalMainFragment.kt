package com.mitdd.gazetracker.medicalhospital

import android.text.TextUtils
import android.view.Gravity
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.ai.ChatWebActivity
import com.mitdd.gazetracker.ai.vm.AdaViewModel
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.medicalhospital.bean.MHospitalMode
import com.mitdd.gazetracker.medicalhospital.enumeration.HospitalModuleKey
import com.mitdd.gazetracker.medicalhospital.inspection.InspectionCenterActivity
import com.mitdd.gazetracker.medicalhospital.mt.MHospitalMTActivity
import com.mitdd.gazetracker.medicalhospital.train.TrainCenterActivity
import com.mitdd.gazetracker.medicalhospital.vm.HospitalViewModel
import com.mitdd.gazetracker.update.UpdateManager
import com.mitdd.gazetracker.update.vm.UpdateViewModel

/**
 * FileName: HospitalMainFragment
 * Author by lilin,Date on 2024/11/27 16:28
 * PS: Not easy to write code, please indicate.
 * 到院版主页
 */
class HospitalMainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = HospitalMainFragment::class.java.simpleName

        fun newInstance(): HospitalMainFragment {
            return HospitalMainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_hospital_main
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val clMore by id<ConstraintLayout>(R.id.cl_more)
    private val ivMoreRedDot by id<ImageView>(R.id.iv_more_red_dot)
    private val tvHospitalName by id<TextView>(R.id.tv_hospital_name)
    private val rvModule by id<RecyclerView>(R.id.rv_module)
    private val ivAi by id<ImageView>(R.id.iv_ai)

    private val updateVM by activityViewModels<UpdateViewModel>()
    private val hospitalVM by activityViewModels<HospitalViewModel>()
    private val adaVM by activityViewModels<AdaViewModel>()

    private val mModuleAdapter = HospitalModuleAdapter()
    private var mItemDecoration:HospitalModuleItemDecoration? = null

    override fun initView() {
        super.initView()
        initListener()

        rvModule.layoutManager = LinearLayoutManager(mActivity,RecyclerView.HORIZONTAL,false)
        mItemDecoration = HospitalModuleItemDecoration(60.dp2px(mActivity))
        rvModule.addItemDecoration(mItemDecoration!!)
        rvModule.adapter = mModuleAdapter

        ivAi.isVisible = DeviceManager.getDeviceInfo()?.isOverseas != true
    }

    override fun initObserver() {
        super.initObserver()
        hospitalVM.mHospitalProfileLiveData.observe(this){
            updateModule(it?.bizModules)
            tvHospitalName.text = it?.accountInfo?.hospitalName?:""
        }
        adaVM.authCodeLiveData.observe(this){
            Logger.d(TAG, msg = "authCodeLiveData = $it")
            ivAi.isVisible = !TextUtils.isEmpty(it?.authCode) && !TextUtils.isEmpty(it?.chatUrl)
        }
        updateVM.appUpdateInfoLiveData.observe(this) {
            updateSettingReaDot()
        }
    }

    override fun initData() {
        super.initData()
        updateDeviceInfo()
        hospitalVM.getHospitalEditionProfile()
        if (DeviceManager.getDeviceInfo()?.isOverseas != true){
            adaVM.getAuthCode(DeviceManager.getDeviceSn())
        }
    }

    private fun initListener(){
        clMore.setOnSingleClickListener {
            val popupWindow = HospitalSettingsPopupWindow(mActivity)
            popupWindow.showAsDropDown(clMore,0,
                10.dp2px(mActivity), Gravity.END)
            popupWindow.show()
            popupWindow.onVersionClick = {
                updateVM.getAppUpdateInfo()
            }
        }
        mModuleAdapter.onItemClick = {
            when(it.moduleKey){
                HospitalModuleKey.INSPECTION_CENTER.moduleKey -> {
                    startActivity(InspectionCenterActivity.createIntent(mActivity,it.url?:""))
                }
                HospitalModuleKey.TRAINING_CENTER.moduleKey -> {
                    startActivity(TrainCenterActivity.createIntent(mActivity,it.url?:""))
                }
                HospitalModuleKey.MASKING_THERAPY.moduleKey -> {
                    startActivity(MHospitalMTActivity.createIntent(mActivity,it.loginAuthToken?:""))
                }
            }
        }
        ivAi.setOnSingleClickListener {
            startActivity(ChatWebActivity.createIntent(mActivity))
        }
    }

    private fun updateModule(modules:List<MHospitalMode>?){
        if (!modules.isNullOrEmpty()){
            val filterModules = modules.filter { module ->
                module.moduleEnable == true
            }
            mItemDecoration?.let {
                rvModule.removeItemDecoration(it)
            }
            mItemDecoration = if (filterModules.size <= 2){
                HospitalModuleItemDecoration(60.dp2px(mActivity))
            }else{
                HospitalModuleItemDecoration(20.dp2px(mActivity))
            }
            rvModule.addItemDecoration(mItemDecoration!!)
            mModuleAdapter.setHospitalModuleData(filterModules)
            mModuleAdapter.notifyDataSetChanged()
            rvModule.post {
                val size = filterModules.size
                if (size > 0){
                    val space = if (size <= 2) 60.dp2px(mActivity) else 20.dp2px(mActivity)
                    val width = rvModule.width
                    val itemWidth = size * 250.dp2px(mActivity) + (size - 1) * space
                    if (width > itemWidth){
                        rvModule.setPadding((width - itemWidth) / 2,0,(width - itemWidth) / 2,0)
                    }
                }
            }
        }
    }

    private fun updateDeviceInfo(){
        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(mActivity,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }

    private fun updateSettingReaDot(){
        ivMoreRedDot.isVisible = UpdateManager.isCanUpgraded()
    }

}