package com.mitdd.gazetracker.medicalhospital

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.medicalhospital.bean.MHospitalMode

/**
 * FileName: HospitalModuleAdapter
 * Author by lilin,Date on 2025/1/6 10:41
 * PS: Not easy to write code, please indicate.
 * 进院版业务模块adapter
 */
class HospitalModuleAdapter : RecyclerView.Adapter<HospitalModuleAdapter.HospitalModuleHolder>() {

    private var hospitalModules: MutableList<MHospitalMode> = mutableListOf()

    var onItemClick:((MHospitalMode) -> Unit)? = null

    fun setHospitalModuleData(data:List<MHospitalMode>){
        hospitalModules.clear()
        hospitalModules.addAll(data)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HospitalModuleHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_hospital_module, parent, false)
        return HospitalModuleHolder(view)
    }

    override fun getItemCount(): Int {
        return hospitalModules.size
    }

    override fun onBindViewHolder(holder: HospitalModuleHolder, position: Int) {
        if (position in hospitalModules.indices){
            holder.bind(hospitalModules[position])
        }
    }

    inner class HospitalModuleHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val ivModeCover: ImageView = itemView.findViewById(R.id.iv_mode_cover)
        private val tvModeName: TextView = itemView.findViewById(R.id.tv_mode_name)

        fun bind(module: MHospitalMode){
            tvModeName.text = module.moduleName
            Logger.d("TAG", msg = "language = ${DeviceManager.getLanguage(itemView.context).language}")
            when(DeviceManager.getLanguage(itemView.context).language){
                "en" -> {
                    tvModeName.textSize = 18f
                }
                else -> {
                    tvModeName.textSize = 21f
                }
            }
            ImageLoader.loadImage(itemView.context, module.cover?:"",ivModeCover)
            itemView.setOnSingleClickListener {
                onItemClick?.invoke(module)
            }
        }
    }
}