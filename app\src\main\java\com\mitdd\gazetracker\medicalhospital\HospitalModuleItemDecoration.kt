package com.mitdd.gazetracker.medicalhospital

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * FileName: HospitalModuleItemDecoration
 * Author by lilin,Date on 2025/1/6 14:37
 * PS: Not easy to write code, please indicate.
 */
class HospitalModuleItemDecoration(private var space:Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val position = parent.getChildAdapterPosition(view)
        if (position != 0){
            outRect.left = space
        }
    }

}