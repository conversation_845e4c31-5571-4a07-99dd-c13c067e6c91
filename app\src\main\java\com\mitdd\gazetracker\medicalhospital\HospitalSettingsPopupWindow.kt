package com.mitdd.gazetracker.medicalhospital

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.databinding.LayoutHospitalSettingsPopupWindowBinding
import com.mitdd.gazetracker.flipbeat.FlipBeatListener
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.mitdd.gazetracker.medicalhome.dialog.ConnectFlipDialog
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import com.mitdd.gazetracker.help.HelpCenterActivity
import com.mitdd.gazetracker.update.UpdateManager
import androidx.core.graphics.drawable.toDrawable

/**
 * FileName: HospitalSettingsPopupWindow
 * Author by lilin,Date on 2024/12/25 19:52
 * PS: Not easy to write code, please indicate.
 */
class HospitalSettingsPopupWindow(val context: Context) : PopupWindow() {

    private val TAG = HospitalSettingsPopupWindow::class.java.simpleName

    private var binding = LayoutHospitalSettingsPopupWindowBinding.inflate(LayoutInflater.from(context))
    var onVersionClick:(() -> Unit)? = null

    init {
        contentView = binding.root

        isFocusable = true
        isTouchable = true
        isOutsideTouchable = true

        initView()

        initListener()

    }

    private fun initView() {

        width = ViewGroup.LayoutParams.WRAP_CONTENT
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        setBackgroundDrawable(Color.TRANSPARENT.toDrawable())

        binding.ivVersionRedDot.isVisible = UpdateManager.isCanUpgraded()
    }

    private fun initListener() {
        setOnDismissListener {
            FlipBeatManager.unRegisterFlipBeatListener(flipBeatListener)
        }
        binding.clFlipBeat.setOnSingleClickListener {
            ConnectFlipDialog(context).show()
            dismiss()
        }
        binding.clPrinterConfiguration.setOnSingleClickListener {
            context.startActivity(PrinterConfigurationActivity.createIntent(context))
            dismiss()
        }
        binding.clHelpCenter.setOnSingleClickListener {
            context.startActivity(HelpCenterActivity.createIntent(context))
            dismiss()
        }
        binding.clVersion.setOnSingleClickListener {
            onVersionClick?.invoke()
            dismiss()
        }
    }

    fun show(){
        FlipBeatManager.registerFlipBeatListener(flipBeatListener)
        binding.tvVersion.text = context.getString(R.string.str_version_number_s,
            PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
        val flipBeatState = FlipBeatManager.getFlipBeatState()
        updateFlipConnected(flipBeatState == FlipBeatState.CONNECTED)
    }

    private fun updateFlipConnected(isConnected:Boolean){
        Logger.d(TAG, msg = "updateFlipConnected isConnected = $isConnected")
        binding.tvFlipBeatState.isSelected = isConnected
        binding.tvFlipBeatState.text = if (isConnected){
            context.getString(R.string.str_connected)
        }else{
            context.getString(R.string.str_unconnected)
        }
    }

    private val flipBeatListener = object : FlipBeatListener {
        override fun onConnectionStateChange(device: BluetoothDevice, state: FlipBeatState) {
            updateFlipConnected(state == FlipBeatState.CONNECTED)
        }
    }

}