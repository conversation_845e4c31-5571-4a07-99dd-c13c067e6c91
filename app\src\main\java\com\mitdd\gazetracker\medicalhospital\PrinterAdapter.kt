package com.mitdd.gazetracker.medicalhospital

import android.os.Environment
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.download.DownloadUtils
import com.airdoc.component.common.download.IHttpListener
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.databinding.ItemPrinterBinding
import com.mitdd.gazetracker.medicalhospital.bean.Printer
import okhttp3.Request
import java.io.File

/**
 * FileName: PrinterAdapter
 * Author by lilin,Date on 2025/6/24 16:51
 * PS: Not easy to write code, please indicate.
 */
class PrinterAdapter : RecyclerView.Adapter<PrinterAdapter.PrinterHolder>() {

    companion object{
        private val TAG = PrinterAdapter::class.java.simpleName
    }

    private val printers = mutableListOf<Printer>()

    fun submitList(newList: List<Printer>) {
        printers.clear()
        printers.addAll(newList)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrinterHolder {
        return PrinterHolder(
            ItemPrinterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )
    }

    override fun onBindViewHolder(holder: PrinterHolder, position: Int) {
        if (position in printers.indices){
            holder.bind(printers[position])
        }
    }

    override fun getItemCount(): Int {
        return printers.size
    }

    inner class PrinterHolder(val binding: ItemPrinterBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(printer: Printer){
            binding.apply {
                tvName.text = printer.name
                if (PackageUtils.checkAppExistsByPackageName(binding.root.context, printer.packageName)){
                    tvState.text = root.context.getString(R.string.str_installed)
                    tvFunction.text = root.context.getString(R.string.str_configuration)
                    progressBar.isVisible = false
                }else{
                    tvState.text = root.context.getString(R.string.str_not_installed)
                    tvFunction.text = root.context.getString(R.string.str_install)
                    progressBar.isVisible = false
                }
                tvFunction.setOnSingleClickListener {
                    val intent = binding.root.context.packageManager.getLaunchIntentForPackage(printer.packageName)
                    if (intent != null){
                        binding.root.context.startActivity(intent)
                    }else{
                        download(printer,binding)
                        tvState.text = root.context.getString(R.string.str_installing)
                        tvFunction.isVisible = false
                        progressBar.isVisible = true
                    }
                }
            }
        }
    }

    private fun download(printer: Printer,binding: ItemPrinterBinding){
        val absolutePath = binding.root.context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath?:""
        DownloadUtils.download(printer.url,absolutePath,"${printer.apkName}.apk",object : IHttpListener.DownloadCallback{
            override fun onPreDownload(request: Request, id: Int) {
                Logger.d(TAG, msg = "${printer.name} onPreDownload id = $id")
            }

            override fun onAfterDownload(id: Int) {
                Logger.d(TAG, msg = "${printer.name} onAfterDownload id = $id")
            }

            override fun onProgressUpdate(progress: Float, total: Long) {
                binding.root.post {
                    binding.progressBar.progress = (progress * 100).toInt()
                }
            }

            override fun onPostDownload(file: File?) {
                binding.root.post {
                    val path = file?.absolutePath
                    Logger.d(TAG, msg = "${printer.name} postDownload path = $path")
                    if (!path.isNullOrEmpty()){
                        PackageUtils.installApk(binding.root.context,path)
                        binding.progressBar.isVisible = false
                        binding.tvFunction.isVisible = true
                        binding.tvState.text = binding.root.context.getString(R.string.str_installed)
                        binding.tvFunction.text = binding.root.context.getString(R.string.str_configuration)
                        Toast.makeText(binding.root.context,binding.root.context.getString(R.string.str_install_successful),Toast.LENGTH_SHORT).show()
                    }else{
                        binding.progressBar.isVisible = false
                        binding.tvFunction.isVisible = true
                        binding.tvState.text = binding.root.context.getString(R.string.str_not_installed)
                        binding.tvFunction.text = binding.root.context.getString(R.string.str_install)
                        Toast.makeText(binding.root.context,binding.root.context.getString(R.string.str_install_failed),Toast.LENGTH_SHORT).show()
                    }
                }
            }

            override fun onErrorDownload(error: String) {
                Logger.d(TAG, msg = "${printer.name} onErrorDownload error = $error")
                binding.root.post {
                    binding.progressBar.isVisible = false
                    binding.tvState.text = binding.root.context.getString(R.string.str_not_installed)
                    binding.tvFunction.text = binding.root.context.getString(R.string.str_install)
                    Toast.makeText(binding.root.context,binding.root.context.getString(R.string.str_install_failed),Toast.LENGTH_SHORT).show()
                }
            }

        })
    }

}