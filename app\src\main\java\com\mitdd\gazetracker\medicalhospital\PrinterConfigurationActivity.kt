package com.mitdd.gazetracker.medicalhospital

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.databinding.ActivityPrinterConfigurationBinding
import com.mitdd.gazetracker.medicalhospital.bean.Printer

/**
 * FileName: PrinterConfigurationActivity
 * Author by lilin,Date on 2025/6/24 15:41
 * PS: Not easy to write code, please indicate.
 * 打印机配置
 */
class PrinterConfigurationActivity : BaseCommonActivity() {

    companion object{
        private val TAG = PrinterConfigurationActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, PrinterConfigurationActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityPrinterConfigurationBinding
    private val printerAdapter = PrinterAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPrinterConfigurationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initData()
    }

    private fun initView() {
        initListener()
        binding.rvPrinter.adapter = printerAdapter
        binding.rvPrinter.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rvPrinter.addItemDecoration(PrinterItemDecoration(8.dp2px(this)))
    }

    private fun initData() {
        printerAdapter.submitList(listOf(
            Printer(getString(R.string.str_hp), "https://assets.babyeye.com/resources/printer-driver/HPSmart.apk", "com.hp.printercontrol", "HPSmart"),
            Printer(getString(R.string.str_fuji_xerox), "https://assets.babyeye.com/resources/printer-driver/FuJiSmartPanel.apk", "com.fujifilm.fb.prt.PrintUtility", "FuJiSmartPanel"),
            Printer(getString(R.string.str_epson), "https://assets.babyeye.com/resources/printer-driver/EpsonSmartPanel.apk", "com.epson.epsonsmart", "EpsonSmartPanel")
        ))
    }

    private fun initListener() {
        binding.ivBack.setOnSingleClickListener {
            finish()
        }
    }

}