package com.mitdd.gazetracker.medicalhospital

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * FileName: PrinterItemDecoration
 * Author by lilin,Date on 2025/6/24 19:14
 * PS: Not easy to write code, please indicate.
 */
class PrinterItemDecoration(private val space: Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        if (position != 0){
            outRect.left = space
        }
    }

}