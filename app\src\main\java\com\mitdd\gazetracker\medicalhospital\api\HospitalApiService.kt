package com.mitdd.gazetracker.medicalhospital.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhospital.bean.MHospitalProfile
import retrofit2.http.GET

/**
 * FileName: HospitalApiService
 * Author by lilin,Date on 2025/1/6 10:13
 * PS: Not easy to write code, please indicate.
 */
interface HospitalApiService {

    /**
     * 获取医疗进院版设备信息
     */
    @GET("dt/api/device/v1/profile/m-hospital")
    suspend fun getHospitalEditionProfile(): ApiResponse<MHospitalProfile>
}