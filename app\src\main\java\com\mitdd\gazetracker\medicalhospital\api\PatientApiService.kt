package com.mitdd.gazetracker.medicalhospital.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.medicalhospital.bean.PatientAdd
import com.mitdd.gazetracker.medicalhospital.bean.PatientList
import com.mitdd.gazetracker.medicalhospital.bean.PatientTrainDataList
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.Url

/**
 * FileName: PatientApiService
 * Author by lilin,Date on 2025/4/23 10:21
 * PS: Not easy to write code, please indicate.
 */
interface PatientApiService {

    /**
     * 获取患者列表
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/patient" 国内 "dt/api/patient"
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    @GET
    suspend fun getPatientList(
        @Url dynamicUrl: String,
        @Query("page") page:Int,
        @Query("size") size:Int,
        @Query("sort") sort:String?,
        @Query("gender") gender:Int?,
        @Query("keywords") keywords:String?,
        @Header("Authorization") authorization: String
    ): ApiResponse<PatientList>

    /**
     * 添加患者
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/patient" 国内 "dt/api/patient"
     */
    @POST
    suspend fun addPatient(
        @Url dynamicUrl: String,
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<PatientAdd>

    /**
     * 修改患者
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/patient/{patientId}" 国内 "dt/api/patient/{patientId}"
     */
    @PUT
    suspend fun modifyPatient(
        @Url dynamicUrl: String,
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<Any>

    /**
     * 查询患者信息
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/patient/{patientId}" 国内 "dt/api/patient/{patientId}"
     */
    @GET
    suspend fun queryPatient(
        @Url dynamicUrl: String,
        @Header("Authorization") authorization: String
    ): ApiResponse<Patient>

    /**
     * 指定患者的每日训练数据
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param type amblyopia(弱视)
     * @param authorization 签名
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/occlusion-therapy/stats/{patientId}" 国内 "dt/api/occlusion-therapy/stats/{patientId}"
     */
    @GET
    suspend fun getPatientTrainData(
        @Url dynamicUrl: String,
        @Query("page") page:Int,
        @Query("size") size:Int,
        @Query("type") type:String,
        @Header("Authorization") authorization: String
    ): ApiResponse<PatientTrainDataList>

    /**
     * 获取指定患者遮盖疗法信息
     * @param dynamicUrl 接口地址 国外 "vision-v2/api/occlusion-therapy/today" 国内 "dt/api/occlusion-therapy/today"
     * @param patientId 患者id
     * @param authorization 签名
     */
    @GET
    suspend fun getPatientMT(
        @Url dynamicUrl: String,
        @Query("patientId") patientId:String,
        @Header("Authorization") authorization: String
    ): ApiResponse<CureInfo>

}