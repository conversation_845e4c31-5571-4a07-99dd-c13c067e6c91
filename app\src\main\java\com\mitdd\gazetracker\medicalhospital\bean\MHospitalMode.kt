package com.mitdd.gazetracker.medicalhospital.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: MHospitalMode
 * Author by lilin,Date on 2025/1/6 10:19
 * PS: Not easy to write code, please indicate.
 * 进院版业务模块
 */
@Parcelize
data class MHospitalMode(
    //封面图
    var cover:String? = null,
    //模块开通状态
    var moduleEnable:Boolean? = null,
    //模块key
    var moduleKey:String? = null,
    //模块名称
    var moduleName:String? = null,
    //模块地址
    var url:String? = null,
    //模块的登录token
    var loginAuthToken:String? = null
) : Parcelable
