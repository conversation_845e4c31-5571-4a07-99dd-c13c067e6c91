package com.mitdd.gazetracker.medicalhospital.bean

import android.os.Parcelable
import com.mitdd.gazetracker.user.bean.AccountInfo
import kotlinx.parcelize.Parcelize

/**
 * FileName: MHospitalProfile
 * Author by lilin,Date on 2025/1/6 10:16
 * PS: Not easy to write code, please indicate.
 * 进院版配置信息
 */
@Parcelize
data class MHospitalProfile(
    //是否已绑定
    var isBind:Boolean? = null,
    //账号用户
    var accountInfo: AccountInfo? = null,
    //业务模块
    var bizModules:List<MHospitalMode>? = null,
) : Parcelable
