package com.mitdd.gazetracker.medicalhospital.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Patient
 * Author by lilin,Date on 2025/4/17 15:16
 * PS: Not easy to write code, please indicate.
 * 患者
 */
@Parcelize
data class Patient(
    //患者ID
    var id:String? = null,
    //患者账号
    var username:String? = null,
    //邮箱
    var email:String? = null,
    //患者姓名
    var name:String? = null,
    //患者性别{0=未知, 1=男, 2=女},可用值:0,1,2
    var gender:Int? = null,
    //出生日期
    var birthday:String? = null,
    //年龄
    var age:Int? = null,
    //手机国别码
    var phoneCountryCode:Int? = null,
    //手机号
    var phoneNumber:String? = null,
    //头像
    var avatar:String? = null,
    //患者个性签名
    var description:String? = null,
    //机构ID
    var organizationId:String? = null,
    //三方患者编号
    var thirdBizId:String? = null
) : Parcelable