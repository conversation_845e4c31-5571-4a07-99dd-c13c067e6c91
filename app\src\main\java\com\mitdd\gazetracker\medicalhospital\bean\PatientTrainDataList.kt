package com.mitdd.gazetracker.medicalhospital.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: PatientTrainDataList
 * Author by lilin,Date on 2025/6/10 11:06
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class PatientTrainDataList(
    //患者列表
    var list: List<PatientTrainData>? = null,
    //总记录数
    var total: Int? = null
): Parcelable

@Parcelize
data class PatientTrainData(
    //类型 1标题 2疗程
    var type:Int = -1,
    //统计日期
    var statDate: String? = null,
    //总时长
    var totalDuration: Int? = null,
): Parcelable{
    companion object{
        const val TYPE_TITLE = 1
        const val TYPE_DATA = 2
    }
}
