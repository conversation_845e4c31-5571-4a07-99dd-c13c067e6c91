package com.mitdd.gazetracker.medicalhospital.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Printer
 * Author by lilin,Date on 2025/6/24 16:52
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class Printer(
    //名称
    var name: String = "",
    //下载地址
    var url: String = "",
    //包名
    var packageName: String = "",
    //apk名称
    var apkName: String = ""
): Parcelable
