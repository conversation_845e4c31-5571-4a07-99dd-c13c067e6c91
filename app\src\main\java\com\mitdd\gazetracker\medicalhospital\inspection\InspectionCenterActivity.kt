package com.mitdd.gazetracker.medicalhospital.inspection

import android.app.Instrumentation
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.print.PrintAttributes
import android.print.PrintManager
import android.text.TextUtils
import android.view.KeyEvent
import android.webkit.WebView
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * FileName: InspectionCenterActivity
 * Author by lilin,Date on 2024/12/26 10:39
 * PS: Not easy to write code, please indicate.
 * 进院版-检查中心
 */
class InspectionCenterActivity : GTBaseActivity(),
    InspectionCenterWebView.InspectionActionListener {

    companion object{
        private val TAG = InspectionCenterActivity::class.java.simpleName
        const val INPUT_PARAM_URL = "url"

        fun createIntent(context: Context,url:String): Intent {
            val intent = Intent(context, InspectionCenterActivity::class.java)
            intent.putExtra(INPUT_PARAM_URL,url)
            return intent
        }
    }

    private val wvInspectionCenter by id<InspectionCenterWebView>(R.id.wv_inspection_center)

    private val mOnBackPressedCallback = object : OnBackPressedCallback(true){
        override fun handleOnBackPressed() {
            Logger.d(TAG, msg = "handleOnBackPressed")
            onHandleOnBackPressed()
        }

    }

    private var mUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_inspection_center)

        onBackPressedDispatcher.addCallback(this,mOnBackPressedCallback)

        initParam()
        initView()
        initData()
    }

    private fun initParam(){
        mUrl = intent.getStringExtra(INPUT_PARAM_URL)?:""
    }

    private fun initView() {
        wvInspectionCenter.addJavascriptInterface(wvInspectionCenter.InspectionCenterAction(),"Inspection")
        wvInspectionCenter.setActionListener(this)
    }

    private fun initData() {
        if (!TextUtils.isEmpty(mUrl)){
            wvInspectionCenter.loadUrl(mUrl)
        }
    }

    /**
     * 拦截方向键并重新发送一次，解决在检查训练中方向键不起作用问题
     */
    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        Logger.d(TAG, msg = "dispatchKeyEvent event = $event")
        when(event?.keyCode){
            KeyEvent.KEYCODE_DPAD_UP ->{
                if (event.deviceId != -1){
                    if (event.action == KeyEvent.ACTION_DOWN){
                        sendKeyCode(event.keyCode)
                    }
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN ->{
                if (event.deviceId != -1){
                    if (event.action == KeyEvent.ACTION_DOWN){
                        sendKeyCode(event.keyCode)
                    }
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_LEFT ->{
                if (event.deviceId != -1){
                    if (event.action == KeyEvent.ACTION_DOWN){
                        sendKeyCode(event.keyCode)
                    }
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_RIGHT ->{
                if (event.deviceId != -1){
                    if (event.action == KeyEvent.ACTION_DOWN){
                        sendKeyCode(event.keyCode)
                    }
                    return true
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }

    private fun sendKeyCode(keyCode: Int){
        lifecycleScope.launch(Dispatchers.Default) {
            try {
                val instrumentation = Instrumentation()
                instrumentation.sendKeyDownUpSync(keyCode)
            }catch (e: Exception){
                if (BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onDestroy() {
        wvInspectionCenter.destroy()
        super.onDestroy()
    }

    override fun onFlipClap() {
        lifecycleScope.launch(Dispatchers.Main) {
            FlipBeatManager.writeDataToFlipBeat(arrayOf("0xAA","0x55","0xA5","0x5A","0x21","0x01","0x00","0xCC","0x66","0xC6","0x6C"))
        }
    }

    override fun onFlipRecover() {
        lifecycleScope.launch(Dispatchers.Main) {
            FlipBeatManager.writeDataToFlipBeat(arrayOf("0xAA","0x55","0xA5","0x5A","0x21","0x01","0x01","0xCC","0x66","0xC6","0x6C"))
        }
    }

    override fun onFinish() {
        lifecycleScope.launch(Dispatchers.Main) {
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(wvInspectionCenter)
        }
    }

    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }

    private fun onHandleOnBackPressed(){
        if (wvInspectionCenter.isHome.get()){
            finish()
        }else{
            wvInspectionCenter.backPressed()
        }
    }

}