package com.mitdd.gazetracker.medicalhospital.mt

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.isVisible
import com.mitdd.gazetracker.R
import androidx.core.graphics.toColorInt
import com.mitdd.gazetracker.medicalhospital.bean.PatientTrainData
import kotlin.math.roundToInt

/**
 * FileName: MHMTTrainDataAdapter
 * Author by lilin,Date on 2025/6/7 14:45
 * PS: Not easy to write code, please indicate.
 * 医疗-进院版-训练数据适配器
 */
class MHMTTrainDataAdapter(private var trainDataList: MutableList<PatientTrainData>) : RecyclerView.Adapter<MHMTTrainDataAdapter.TrainDataHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TrainDataHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_mh_mt_train_data, parent, false)
        return TrainDataHolder(view)
    }

    override fun onBindViewHolder(holder: TrainDataHolder, position: Int) {
        if (position in trainDataList.indices){
            holder.bind(trainDataList[position])
        }
    }

    override fun getItemCount(): Int {
        return trainDataList.size
    }

    inner class TrainDataHolder(itemView: View) : RecyclerView.ViewHolder(itemView){

        private val clRoot: ConstraintLayout = itemView.findViewById(R.id.cl_root)
        private val horizontalLine2: View = itemView.findViewById(R.id.horizontal_line2)
        private val tvTrainDate: TextView = itemView.findViewById(R.id.tv_train_date)
        private val tvTrainDuration: TextView = itemView.findViewById(R.id.tv_train_duration)

        fun bind(trainData: PatientTrainData){
            val position = bindingAdapterPosition
            horizontalLine2.isVisible = position == trainDataList.size - 1
            if (trainData.type == PatientTrainData.TYPE_TITLE){
                clRoot.setBackgroundColor("#EFF3F6".toColorInt())
                tvTrainDate.text = itemView.context.getString(R.string.str_train_date)
                tvTrainDuration.text = itemView.context.getString(R.string.str_train_duration)
            }else{
                clRoot.background = null
                tvTrainDate.text = trainData.statDate
                tvTrainDuration.text = itemView.context.getString(R.string.str_duration_int_m,(trainData.totalDuration?:0) / 60)
            }
        }

    }

}