package com.mitdd.gazetracker.medicalhospital.mt

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.airdoc.component.common.R as CommonR
import com.mitdd.gazetracker.databinding.FragmentMhMtTrainDataBinding
import com.mitdd.gazetracker.medicalhospital.bean.PatientTrainData
import com.mitdd.gazetracker.medicalhospital.vm.PatientViewModel
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.getValue

/**
 * FileName: MHMTTrainDataFragment
 * Author by lilin,Date on 2025/6/7 14:11
 * PS: Not easy to write code, please indicate.
 * 医疗-进院版-遮盖疗法训练数据
 */
class MHMTTrainDataFragment : BaseBindingFragment<FragmentMhMtTrainDataBinding>() {

    companion object{
        private val TAG = MHMTTrainDataFragment::class.java.simpleName

        const val FRAGMENT_TAG = "MTTrainData"
        const val INPUT_PARAM_PATIENT_ID = "patientId"

        fun newInstance(patientId: String): MHMTTrainDataFragment {
            val fragment = MHMTTrainDataFragment()
            val bundle = Bundle()
            bundle.putString(INPUT_PARAM_PATIENT_ID,patientId)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun createBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMhMtTrainDataBinding {
        return FragmentMhMtTrainDataBinding.inflate(inflater,container,false)
    }

    private val patientVM by activityViewModels<PatientViewModel>()
    private val trainDataList = mutableListOf<PatientTrainData>()
    private val trainDataAdapter = MHMTTrainDataAdapter(trainDataList)
    //是否是刷新数据列表
    private val isRefresh = AtomicBoolean(false)
    //是否是加载更多数据列表
    private val isLoadMore = AtomicBoolean(false)
    //训练数据列表页码，从1开始,0表示尚未加载数据
    private var patientPage = 0
    private var patientId = ""

    override fun initParam() {
        patientId = arguments?.getString(INPUT_PARAM_PATIENT_ID) ?: ""
    }

    override fun initView() {
        initListener()
        binding.rvTrainData.adapter = trainDataAdapter
        binding.rvTrainData.layoutManager = LinearLayoutManager(mActivity, LinearLayoutManager.VERTICAL, false)

        binding.smartRefresh.setRefreshHeader(ClassicsHeader(mActivity))
        binding.smartRefresh.setRefreshFooter(ClassicsFooter(mActivity))
        binding.smartRefresh.setEnableLoadMoreWhenContentNotFull(false)

        binding.commonEmpty.getEmptyPrompt().apply {
            text = getString(R.string.str_no_data_available)
            textSize = 18f
            setTextColor(ContextCompat.getColor(mActivity, CommonR.color.color_333333))
        }
    }

    override fun initData() {
        loadTrainData(1)
    }

    override fun initObserver() {
        patientVM.patientTrainDataListLiveData.observe(this){
            if (isRefresh.get()){//刷新
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                binding.smartRefresh.finishRefresh()
                binding.smartRefresh.isVisible = list.isNotEmpty()
                binding.commonEmpty.isVisible = list.isEmpty()
                trainDataList.clear()
                if (list.isNotEmpty()){
                    trainDataList.add(PatientTrainData().apply {
                        type = PatientTrainData.TYPE_TITLE
                    })
                    trainDataList.addAll(list)
                    trainDataAdapter.notifyDataSetChanged()
                }
                patientPage = 1
                if (trainDataList.size - 1 >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                isRefresh.set(false)
            }else if (isLoadMore.get()){//加载更多
                val list = it?.list
                val total = it?.total?:0
                if (!list.isNullOrEmpty()){
                    trainDataList.addAll(list)
                    trainDataAdapter.notifyItemRangeInserted(trainDataList.size - list.size,list.size)
                    patientPage++
                }
                if (trainDataList.size - 1 >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }else{
                    binding.smartRefresh.finishLoadMore(true)
                }
                isLoadMore.set(false)
            }else{//首次加载数据
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                binding.smartRefresh.isVisible = list.isNotEmpty()
                binding.commonEmpty.isVisible = list.isEmpty()
                trainDataList.clear()
                if (list.isNotEmpty()){
                    trainDataList.add(PatientTrainData().apply {
                        type = PatientTrainData.TYPE_TITLE
                    })
                    trainDataList.addAll(list)
                    trainDataAdapter.notifyDataSetChanged()
                }
                patientPage = 1
                if (trainDataList.size - 1 >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
            }
        }
        patientVM.curPatientLiveData.observe(this) {
            val patientId = it?.id
            Logger.d(TAG, msg = "curPatientLiveData patientId = $id")
            if (!patientId.isNullOrEmpty()){
                this.patientId = patientId
                loadTrainData(1)
            }
        }
    }

    private fun initListener() {
        binding.smartRefresh.setOnRefreshListener{
            refreshTrainData(1)
        }
        binding.smartRefresh.setOnLoadMoreListener {
            loadMoreTrainData(patientPage + 1)
        }
    }

    /**
     * 更新患者训练数据
     */
    fun updatePatient(patientId: String){
        Logger.d(TAG, msg = "updatePatient patientId = $patientId")
        this.patientId = patientId
        loadTrainData(1)
    }

    /**
     * 加载训练数据
     */
    private fun loadTrainData(page:Int,size:Int = 10){
        Logger.d(TAG, msg = "loadTrainData page = $page,size = $size")
        patientVM.getPatientTrainData(patientId,page, size)
    }

    /**
     * 下拉刷新训练数据
     */
    private fun refreshTrainData(page:Int,size:Int = 10){
        Logger.d(TAG, msg = "refreshTrainData page = $page,size = $size")
        isRefresh.set(true)
        patientVM.getPatientTrainData(patientId,page, size)
    }

    /**
     * 上划加载更多训练数据
     */
    private fun loadMoreTrainData(page:Int,size:Int = 10){
        Logger.d(TAG, msg = "loadMoreTrainData page = $page,size = $size")
        isLoadMore.set(true)
        patientVM.getPatientTrainData(patientId,page, size)
    }

}