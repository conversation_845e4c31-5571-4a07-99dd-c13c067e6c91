package com.mitdd.gazetracker.medicalhospital.mt

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.google.gson.Gson
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.databinding.ActivityMHospitalMtBinding
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import com.mitdd.gazetracker.medicalhome.preview.ParamSettingActivity
import com.mitdd.gazetracker.medicalhospital.preference.MHPreference
import com.mitdd.gazetracker.medicalhospital.vm.MHospitalMTViewModel
import com.mitdd.gazetracker.medicalhospital.vm.PatientViewModel
import com.mitdd.gazetracker.user.bean.Gender
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: MHospitalMTActivity
 * Author by lilin,Date on 2025/4/16 17:24
 * PS: Not easy to write code, please indicate.
 * 医疗到院版数字遮盖疗法Activity
 */
class MHospitalMTActivity : GTBaseActivity() {

    companion object{
        private val TAG = MHospitalMTActivity::class.java.simpleName
        const val INPUT_PARAM_AUTHORIZATION = "authorization"

        fun createIntent(context: Context,loginAuthToken:String): Intent {
            val intent = Intent(context, MHospitalMTActivity::class.java)
            intent.putExtra(INPUT_PARAM_AUTHORIZATION,loginAuthToken)
            return intent
        }
    }

    private lateinit var binding: ActivityMHospitalMtBinding

    val dialogTaskManager = DialogTaskManager(this)
    private val mHospitalMTVM by viewModels<MHospitalMTViewModel>()
    private val patientVM by viewModels<PatientViewModel>()

    private val gson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                mServiceMessage?.send(Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                })
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false) == true
            if (isSucceed){
                Toast.makeText(this,getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                val calibrationFailureDialog = CalibrationFailureDialog(this).apply {
                    onRecalibration = {
                        startCalibration()
                    }
                }
                calibrationFailureDialog.show()
            }
        }
    }
    //是否正在关闭
    private val isFinishing = AtomicBoolean(false)
    //模块的登录token
    private var mAccessToken = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMHospitalMtBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initParam()
        initView()
        initObserver()
        initData()
    }

    override fun onResume() {
        super.onResume()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, BIND_AUTO_CREATE)
    }

    override fun onPause() {
        super.onPause()
        unbindService(serviceConnection)
    }

    private fun initParam(){
        mAccessToken = intent.getStringExtra(INPUT_PARAM_AUTHORIZATION)?: ""
        patientVM.authorization = "Bearer $mAccessToken"
    }

    private fun initView(){
        initListener()
        showMaskTherapy()
        showPatientLibrary()
        binding.llPatientLibrary.isSelected = true
        binding.flPatientLibrary.post {
            val layoutParams = binding.flPatientLibrary.layoutParams
            layoutParams.width = if (binding.llPatientLibrary.isSelected) 270.dp2px(this) else 0
            binding.flPatientLibrary.layoutParams = layoutParams
        }

    }

    private fun initListener(){
        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true){
            override fun handleOnBackPressed() {
                if (supportFragmentManager.popBackStackImmediate()) {
                    if (supportFragmentManager.backStackEntryCount == 0){
                        showMaskTherapy()
                    }
                    return
                }
                finish()
            }
        })
        binding.llPatientLibrary.setOnSingleClickListener {
            if (binding.llPatientLibrary.isSelected){
                startWidthAnimation(binding.flPatientLibrary,270.dp2px(this),0,300,object :AnimatorListenerAdapter(){
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        val fragment =
                            supportFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)
                        if (fragment is MHospitalMTFragment){
                            fragment.setAppSpanCount(8)
                        }
                    }
                })
                binding.llPatientLibrary.isSelected = false
            }else{
                startWidthAnimation(binding.flPatientLibrary,0,270.dp2px(this),300,object :AnimatorListenerAdapter(){
                    override fun onAnimationStart(animation: Animator, isReverse: Boolean) {
                        super.onAnimationStart(animation, isReverse)
                        val fragment =
                            supportFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)
                        if (fragment is MHospitalMTFragment){
                            fragment.setAppSpanCount(6)
                        }
                    }
                })
                binding.llPatientLibrary.isSelected = true
            }
        }
        binding.llNewPatient.setOnSingleClickListener {
            val newUserDialog = NewUserDialog(this,true).apply {
                onOkClick = { patient,isNewUser ->
                    if (isNewUser){
                        patientVM.addPatient(patient.name?:"",patient.gender?:Gender.MALE.num,
                            patient.birthday?:"",patient.phoneNumber?:"",
                            patient.phoneCountryCode?:86,patient.email?:""
                        )
                        MMKVManager.encodeInt(MHPreference.PHONE_COUNTRY_CODE,patient.phoneCountryCode?:86)
                    }
                }
            }
            newUserDialog.show()
        }
        binding.llBack.setOnSingleClickListener {
            if (supportFragmentManager.popBackStackImmediate()) {
                if (supportFragmentManager.backStackEntryCount == 0){
                    showMaskTherapy()
                }
                return@setOnSingleClickListener
            }
            finish()
        }
        binding.llCalibrate.setOnSingleClickListener {
            startCalibration()
        }
        binding.llSetting.setOnSingleClickListener {
            startActivity(
                ParamSettingActivity.createIntent(this@MHospitalMTActivity,
                    ParamSettingActivity.MODE_AMBLYOPIA))
        }
    }

    private fun initObserver(){
        patientVM.curPatientLiveData.observe(this){
            if (it != null){
                binding.tvCurrentPatient.text = getString(R.string.str_current_patient_s,it.name?:"")
            }else{
                binding.tvCurrentPatient.text = ""
            }
            //当前患者改变时关闭遮盖疗法
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
        }
        patientVM.patientAddLiveData.observe(this){
            if (!it?.id.isNullOrEmpty()) {
                patientVM.getPatientList(1)
                Toast.makeText(this,getString(R.string.str_patient_added_successfully), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(this,getString(R.string.str_patient_added_failed), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initData(){

    }

    private fun showMaskTherapy(){
        supportFragmentManager.commit {
            val fragment =
                supportFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)
            if (fragment == null){
                replace(R.id.fl_content_container, MHospitalMTFragment.newInstance(),MHospitalMTFragment.FRAGMENT_TAG)
            }else{
                show(fragment)
            }
        }
    }

    private fun showPatientLibrary(){
        supportFragmentManager.commit {
            replace(R.id.fl_patient_library, PatientLibraryFragment.newInstance(),PatientLibraryFragment.FRAGMENT_TAG)
        }
    }

    /**
     * 使用 ValueAnimator 改变 View 的宽度
     *
     * @param view 要改变宽度的 View
     * @param startWidth 初始宽度
     * @param endWidth 目标宽度
     * @param duration 动画持续时间（毫秒）
     */
    private fun startWidthAnimation(view: View, startWidth: Int, endWidth: Int, duration: Long,listener: Animator.AnimatorListener? = null) {
        val animator = ValueAnimator.ofInt(startWidth, endWidth).apply {
            this.duration = duration

            addUpdateListener { animation ->
                // 获取当前动画值并更新 View 的宽度
                val animatedValue = animation.animatedValue as Int
                val layoutParams = view.layoutParams
                layoutParams.width = animatedValue
                view.layoutParams = layoutParams
            }

            if (listener != null){
                addListener(listener)
            }
        }
        // 启动动画
        animator.start()
    }

    //启动眼动校准
    private fun startCalibration(){
        calibrationLauncher.launch(CalibrationActivity.createIntent(this, CalibrationMode.CALIBRATION,false))
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
        when(msg.what){
            GazeConstants.MSG_GAZE_TRACKING_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_GAZE_TRACKING_STATE state = $state")
            }
            GazeConstants.MSG_APPLIED_CURE_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_CURE_STATE state = $state")
                mHospitalMTVM.setOcclusionTherapyState(state)
                if (state){
                    PlayManager.playRawMedia(this, RawMedia(R.raw.mask_therapy_started_please_wear_glasses))
                    //显示遮盖疗法开启弹窗
                    showMHospitalMTStateDialog(true)
                }else{
                    PlayManager.playRawMedia(this, RawMedia(R.raw.mask_therapy_closed))
                    if (!isFinishing.get()){
                        //显示遮盖疗法关闭弹窗
                        showMHospitalMTStateDialog(false)
                    }
                }
            }
            GazeConstants.MSG_UPDATE_TREATMENT_DURATION ->{
                val treatmentDuration = msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)
                Logger.d(TAG, msg = "MSG_UPDATE_TREATMENT_DURATION treatmentDuration = $treatmentDuration")
                patientVM.setPatientMT(patientVM.patientMT?.apply {
                    trainingDuration = treatmentDuration
                })
            }
            GazeConstants.MSG_REPORT_TREATMENT_RESULT ->{
                val resultJson = msg.data?.getString(GazeConstants.KEY_REPORT_CURE_RESULT)
                Logger.d(TAG, msg = "MSG_REPORT_TREATMENT_RESULT resultJson = $resultJson")
                val result = try {
                    gson.fromJson(resultJson, CureInfo::class.java)
                }catch (e:Exception){
                    if (BuildConfig.DEBUG) e.printStackTrace()
                    null
                }
                if (result != null){
                    patientVM.setPatientMT(result)
                }
            }
        }
    }

    private fun showMHospitalMTStateDialog(isStart:Boolean){
        val maskTherapyStateDialog = MHospitalMTStateDialog(this)
        maskTherapyStateDialog.show()
        maskTherapyStateDialog.setData(isStart)
    }

    fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

    override fun finish() {
        val state = DeviceManager.getMaskTherapyState()
        if (state){
            isFinishing.set(true)
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
            handler.postDelayed({
                super.finish()
            },300)
        }else{
            super.finish()
        }
    }

}