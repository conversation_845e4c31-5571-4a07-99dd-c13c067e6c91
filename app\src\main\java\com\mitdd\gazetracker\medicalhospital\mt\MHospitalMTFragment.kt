package com.mitdd.gazetracker.medicalhospital.mt

import android.app.Activity
import android.content.pm.ApplicationInfo
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.google.gson.Gson
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.dialog.task.NotificationDialogTask
import com.mitdd.gazetracker.databinding.FragmentMHospitalMtBinding
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.mask.SelectCommonAppActivity
import com.mitdd.gazetracker.medicalhospital.vm.MHospitalMTViewModel
import com.mitdd.gazetracker.medicalhospital.vm.PatientViewModel
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.Gender

/**
 * FileName: MHospitalMTFragment
 * Author by lilin,Date on 2025/4/16 15:42
 * PS: Not easy to write code, please indicate.
 * 医疗-到院版-遮盖疗法Fragment
 */
class MHospitalMTFragment : BaseBindingFragment<FragmentMHospitalMtBinding>(){

    companion object{
        private val TAG = MHospitalMTFragment::class.java.simpleName

        const val FRAGMENT_TAG = "MT"

        fun newInstance(): MHospitalMTFragment {
            val fragment = MHospitalMTFragment()
            return fragment
        }
    }

    override fun createBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentMHospitalMtBinding {
        return FragmentMHospitalMtBinding.inflate(inflater, container,false)
    }

    private val mHospitalMTVM by activityViewModels<MHospitalMTViewModel>()
    private val patientVM by activityViewModels<PatientViewModel>()

    private val gson = Gson()
    private val handler = LifecycleHandler(Looper.getMainLooper(), this)

    //选择常用应用
    private var selectCommonAppLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val applicationInfo = data?.getParcelableExtra<ApplicationInfo>(SelectCommonAppActivity.OUTPUT_PARAM_COMMON_APP)
            if (applicationInfo != null){
                binding.commonAppView.addCommonApp(applicationInfo)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        handler.postDelayed({
            binding.switchMaskTherapy.isChecked = DeviceManager.getMaskTherapyState()
        },500)
    }

    override fun initParam() {
    }

    override fun initView() {

        initListener()

        val accountInfo = UserManager.getAccountInfo()
        when(accountInfo?.gender){
            Gender.MALE.value ->{
                binding.treatmentTimeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_male)
            }
            else ->{
                binding.treatmentTimeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_female)
            }
        }

        setAppSpanCount(6)
    }

    override fun initObserver() {
        mHospitalMTVM.mtStateLiveData.observe(this){
            binding.switchMaskTherapy.isChecked = it
            binding.ivRedBlueGlasses.isVisible = it
        }
        patientVM.patientMTLiveData.observe(this){
            updateEyePosition()
            updatePlannedDuration()
            updateTreatmentDuration()
        }
        patientVM.curPatientLiveData.observe(this){
            val patientId = it?.id
            Logger.d(TAG, msg = "curPatientLiveData patientId = $patientId")
            if (patientId != null){
                patientVM.getPatientMT(patientId)
            }
        }
    }

    override fun initData() {
    }

    private fun initListener(){
        binding.switchMaskTherapy.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            switchMaskTherapy(isChecked)
        }
        binding.commonAppView.onAddCommonApp = {
            selectCommonAppLauncher.launch(
                SelectCommonAppActivity.createIntent(mActivity)
            )
        }
    }

    fun setAppSpanCount(spanCount:Int){
        binding.commonAppView.setSpanCount(spanCount)
    }

    private fun switchMaskTherapy(state:Boolean){
        Logger.d(TAG, msg = "switchMaskTherapy state = $state")
        if (!state){
            binding.switchMaskTherapy.isChecked = false
            (mActivity as? MHospitalMTActivity)?.sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
            return
        }
        val currentPatient = patientVM.currentPatient
        if (currentPatient == null){
            binding.switchMaskTherapy.isChecked = false
            showNotificationDialog(getString(R.string.str_please_select_patient))
            return
        }
        val isFinishUp = patientVM.isFinishUp
        var plannedDuration = patientVM.plannedDuration
        var treatmentDuration = patientVM.treatmentDuration
        if (isFinishUp || plannedDuration <= treatmentDuration){
            binding.switchMaskTherapy.isChecked = false
            showNotificationDialog(getString(R.string.str_treatment_has_been_completed_today))
            return
        }
        (mActivity as? MHospitalMTActivity)?.sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_CURE
                data.putInt(GazeConstants.KEY_PLANNED_DURATION,plannedDuration)
                data.putInt(GazeConstants.KEY_TREATMENT_DURATION,treatmentDuration)
                data.putString(GazeConstants.KEY_REPORT_URL,if (DeviceManager.isOverseas()) "vision-v2/api/occlusion-therapy/section/report" else "dt/api/occlusion-therapy/section/report")
                data.putString(GazeConstants.KEY_BASE_URL, if (DeviceManager.isOverseas()) UrlConfig.OVERSEAS_DOMAIN else UrlConfig.MAIN_DOMAIN)
                //添加参数
                data.putString(GazeConstants.KEY_REPORT_PARAM, gson.toJson(HashMap<String, String>().apply {
                    put("patientId",patientVM.currentPatient?.id.orEmpty())
                }))
                //添加headers
                val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)
                data.putString(GazeConstants.KEY_REPORT_HEADER, gson.toJson(HashMap<String, String>().apply {
                    put("X-Rom-Version",DeviceManager.getOSVersion())
                    put("X-Device-Sn",DeviceManager.getDeviceSn())
                    put("X-App-Version",PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
                    put("X-Device-Mode",DeviceManager.getProductModel())
                    put("X-Airdoc-Client","d409e47e-95e6-41fc-868c-e5748957d546")
                    put("Accept-Language","${locale.language}-${locale.country}")
                    put("Authorization",patientVM.authorization)
                }))
            }
        )
    }

    private fun updatePlannedDuration(){
        binding.treatmentTimeProgress.getPlannedDurationView().isVisible = patientVM.patientMT != null
        binding.treatmentTimeProgress.setPlannedDuration(patientVM.plannedDuration)
    }

    private fun updateTreatmentDuration(){
        binding.treatmentTimeProgress.getTrainDurationView().isVisible = patientVM.patientMT != null
        binding.treatmentTimeProgress.setTreatmentDuration(patientVM.treatmentDuration)
    }

    private fun updateEyePosition(){
        when(patientVM.eyePosition){
            AmblyopicEye.LEFT.value ->{
                binding.ivRedBlueGlasses.setImageResource(R.drawable.icon_left_blue_right_red_glasses_small)
            }
            AmblyopicEye.RIGHT.value ->{
                binding.ivRedBlueGlasses.setImageResource(R.drawable.icon_left_red_right_blue_glasses_small)
            }
        }
    }

    private fun showNotificationDialog(prompt:String,confirmClick:(() -> Unit)? = null){
        (mActivity as MHospitalMTActivity).dialogTaskManager.addTask(NotificationDialogTask(mActivity).apply {
            setConfirmClick(confirmClick)
            setMessage(prompt)
        })
    }

}