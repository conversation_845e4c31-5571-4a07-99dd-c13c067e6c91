package com.mitdd.gazetracker.medicalhospital.mt

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: MHospitalMTStateDialog
 * Author by lilin,Date on 2025/4/22 14:41
 * PS: Not easy to write code, please indicate.
 * 医疗到院版遮盖疗法状态弹窗
 */
class MHospitalMTStateDialog(context: Context) : BaseCommonDialog(context) {

    private val tvTitle by id<TextView>(R.id.tv_title)
    private val tvSubtitle by id<TextView>(R.id.tv_subtitle)
    private val ivClose by id<ImageView>(R.id.iv_close)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val ivGlasses by id<ImageView>(R.id.iv_glasses)
    private val tvRedLens by id<TextView>(R.id.tv_red_lens)
    private val tvDominantEye by id<TextView>(R.id.tv_dominant_eye)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_m_hospital_mt_state)

        initView()
    }

    private fun initView() {
        ivClose.setOnSingleClickListener {
            dismiss()
        }
        tvOk.setOnSingleClickListener {
            dismiss()
        }
    }

    fun setData(isStart:Boolean){
        if (isStart){
            setTitle(context.getString(R.string.str_airdoc_masking_therapy_on))
            setGlasses(R.drawable.icon_m_hospital_mt_open_glasses)

            val strRed = context.getString(R.string.str_red)
            val text = context.getString(R.string.str_please_select_correct_glasses_red_s,strRed)
            val spannable = SpannableString(text)
            val redIndex = text.indexOf(strRed)
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FFEB4E89")),
                redIndex, redIndex + strRed.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            tvSubtitle.text = spannable
            tvRedLens.isVisible = true
            tvDominantEye.isVisible = true
        }else{
            setTitle(context.getString(R.string.str_airdoc_masking_therapy_off))
            setSubtitle(context.getString(R.string.str_please_cancel_red_blue_glasses))
            setGlasses(R.drawable.icon_left_red_right_blue_glasses_white)
            tvRedLens.isVisible = false
            tvDominantEye.isVisible = false
        }
    }

    fun setTitle(title:String){
        tvTitle.text = title
    }

    fun setSubtitle(subtitle:String){
        tvSubtitle.text = subtitle
    }

    fun setGlasses(res:Int){
        ivGlasses.setImageResource(res)
    }

}