package com.mitdd.gazetracker.medicalhospital.mt

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.EmailValidator
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.user.bean.Gender
import java.time.LocalDate
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.mitdd.gazetracker.databinding.DialogNewUserBinding
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.medicalhospital.preference.MHPreference

/**
 * FileName: NewUserDialog
 * Author by lilin,Date on 2025/4/18 9:59
 * PS: Not easy to write code, please indicate.
 * 新增用户弹窗
 */
class NewUserDialog(context: Context,private var isNewUser:Boolean) : BaseCommonDialog(context)  {

    companion object{
        private val TAG = NewUserDialog::class.java.simpleName
    }

    private lateinit var binding: DialogNewUserBinding

    private val phoneUtil = PhoneNumberUtil.getInstance()
    var onOkClick:((patient: Patient, isNewUser:Boolean) -> Unit)? = null
    private var patient = Patient().apply {
        phoneCountryCode = MMKVManager.decodeInt(MHPreference.PHONE_COUNTRY_CODE)?:86
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogNewUserBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        window?.apply {
            val width = 450.dp2px(context)
            val height = 395.dp2px(context)
            setLayout(width, height)
        }

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
        initListener()
    }

    private fun initView() {
        if (isNewUser){
            binding.tvTitle.text = context.getString(R.string.str_new_user)
        }else{
            binding.tvTitle.text = context.getString(R.string.str_basic_information)
        }
        binding.ccp.setCcpClickable(DeviceManager.isOverseas())
        binding.etEmail.isVisible = DeviceManager.isOverseas()
        binding.tvEmail.isVisible = DeviceManager.isOverseas()
        setPatient(patient)
    }

    private fun initListener() {
        binding.tvOk.setOnSingleClickListener {
            if (checkPatient(patient)){
                onOkClick?.invoke(this.patient,isNewUser)
                dismiss()
            }
        }
        binding.tvCancel.setOnSingleClickListener {
            dismiss()
        }
        binding.etName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                patient.name = s.toString()
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.etBirthday.setOnSingleClickListener {
            val selectionAgeDialog = SelectionAgeDialog(context).apply {
                onOkClick = { year, month, day ->
                    val birthday = "${year}-${if (month < 10) "0$month" else month}-${if (day < 10) "0$day" else day}"
                    patient.birthday = birthday
                    binding.etBirthday.text = birthday
                }
            }
            selectionAgeDialog.show()
            val birthday = patient.birthday
            if (!birthday.isNullOrEmpty()){
                try {
                    // 解析日期字符串
                    val localDate = LocalDate.parse(birthday)
                    // 获取年、月、日的整数值
                    selectionAgeDialog.setDate(localDate.year,localDate.monthValue,localDate.dayOfMonth)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        binding.etPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                patient.phoneNumber = s.toString().replace(" ", "")
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.ccp.setOnCountryChangeListener {
            val countryCodeAsInt = binding.ccp.selectedCountryCodeAsInt
            Logger.d(TAG, msg = "selectedCountryCode = $countryCodeAsInt")
            patient.phoneCountryCode = countryCodeAsInt
        }
        binding.rgGender.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_male ->{
                    patient.gender = Gender.MALE.num
                    binding.ivAvatar.setImageResource(R.drawable.icon_patient_avatar_male)
                }
                R.id.rb_female ->{
                    patient.gender = Gender.FEMALE.num
                    binding.ivAvatar.setImageResource(R.drawable.icon_patient_avatar_female)
                }
            }
        }
        binding.etEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                patient.email = s.toString().replace(" ", "")
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        // 监听窗口触摸事件
        window?.decorView?.setOnTouchListener(object :OnTouchListener{
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                if (event?.action == MotionEvent.ACTION_UP) {
                    isOutsideDialog(event).also {
                        Logger.d(TAG, msg = "isOutsideDialog = $it")
                        if (it) {
                            handleOutsideClick()
                            return true
                        } else {
                            return false
                        }
                    }
                }
                return false
            }

        })
    }

    fun setPatient(patient: Patient){
        this.patient = patient
        binding.etName.setText(patient.name?:"")
        when(patient.gender){
            Gender.FEMALE.num ->{
                binding.rgGender.check(R.id.rb_female)
                binding.ivAvatar.setImageResource(R.drawable.icon_patient_avatar_female)
                this.patient.gender = Gender.FEMALE.num
            }
            else ->{
                binding.rgGender.check(R.id.rb_male)
                binding.ivAvatar.setImageResource(R.drawable.icon_patient_avatar_male)
                this.patient.gender = Gender.MALE.num
            }
        }
        binding.etBirthday.text = patient.birthday?:""
        binding.etPhone.setText(patient.phoneNumber?:"")
        // 初始化国家选择器
        binding.ccp.setCountryForPhoneCode(patient.phoneCountryCode?:86)
        binding.ccp.registerCarrierNumberEditText(binding.etPhone)
        binding.etEmail.setText(patient.email?:"")
    }

    private fun checkPatient(patient: Patient):Boolean{
        if (patient.name?.trim().isNullOrEmpty()){
            Toast.makeText(context,context.getString(R.string.str_invalid_name_input),Toast.LENGTH_SHORT).show()
            return false
        }
        if (patient.birthday.isNullOrEmpty()){
            Toast.makeText(context,context.getString(R.string.str_invalid_age_input),Toast.LENGTH_SHORT).show()
            return false
        }
        val phone = patient.phoneNumber
        if (!phone.isNullOrEmpty()){
            try {
                val phoneNumber = phoneUtil.parse(phone, binding.ccp.selectedCountryNameCode)
                val validNumber = phoneUtil.isValidNumber(phoneNumber)
                Logger.d(TAG, msg = "checkPatient phoneNumber = $phoneNumber, validNumber = $validNumber")
                if (!validNumber){
                    Toast.makeText(context,context.getString(R.string.str_invalid_phone_number_format),Toast.LENGTH_SHORT).show()
                    return false
                }
            }catch (e:Exception){
                Toast.makeText(context,context.getString(R.string.str_invalid_phone_number_format),Toast.LENGTH_SHORT).show()
                return false
            }
        }
        val email = patient.email
        if (!email.isNullOrEmpty() && !EmailValidator.isValidEmail(email)){
            Toast.makeText(context,context.getString(R.string.str_invalid_email_format),Toast.LENGTH_SHORT).show()
            return false
        }
        return true
    }

    /**
     * 点击了外部区域
     */
    private fun handleOutsideClick() {
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        val currentFocus = window?.decorView?.findFocus() ?: window?.currentFocus
        if (currentFocus != null){
            currentFocus.clearFocus()
            imm.hideSoftInputFromWindow(currentFocus.windowToken, 0)
        }
    }

    private fun isOutsideDialog(event: MotionEvent): Boolean {
        // 获取 clRoot 在屏幕中的绝对位置
        val location = IntArray(2)
        binding.clRoot.getLocationOnScreen(location)
        val left = location[0]
        val top = location[1]
        val right = left + binding.clRoot.width
        val bottom = top + binding.clRoot.height

        // 构建修正后的 Rect
        val adjustedRect = Rect(left, top, right, bottom)
        return !adjustedRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }


}