package com.mitdd.gazetracker.medicalhospital.mt

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.user.bean.Gender

/**
 * FileName: PatientAdapter
 * Author by lilin,Date on 2025/4/17 15:09
 * PS: Not easy to write code, please indicate.
 */
class PatientAdapter(private var patients: MutableList<Patient>) : RecyclerView.Adapter<PatientAdapter.PatientHolder>() {

    private var mListener: ItemClickListener? = null
    private var mSelectPosition = RecyclerView.NO_POSITION

    fun setItemClickListener(listener: ItemClickListener){
        mListener = listener
    }

    fun setSelectPosition(position: Int){
        if (position in patients.indices){
            mSelectPosition = position
            notifyDataSetChanged()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_patient_information_layout, parent, false)
        return PatientHolder(view)
    }

    override fun getItemCount(): Int {
        return patients.size
    }

    override fun onBindViewHolder(holder: PatientHolder, position: Int) {
        if (position in patients.indices){
            holder.bind(patients[position],position)
        }
    }

    inner class PatientHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val clPatientInfo: ConstraintLayout = itemView.findViewById(R.id.cl_patient_info)
        private val tvPatientName: TextView = itemView.findViewById(R.id.tv_patient_name)
        private val tvPatientGender: TextView = itemView.findViewById(R.id.tv_patient_gender)
        private val tvPatientAge: TextView = itemView.findViewById(R.id.tv_patient_age)
        private val tvPatientId: TextView = itemView.findViewById(R.id.tv_patient_id)
        private val tvPatientPhone: TextView = itemView.findViewById(R.id.tv_patient_phone)
        private val tvPatientFileNum: TextView = itemView.findViewById(R.id.tv_patient_file_num)
        private val tvBasicInformation: TextView = itemView.findViewById(R.id.tv_basic_information)
        private val tvTrainData: TextView = itemView.findViewById(R.id.tv_train_data)

        init {
            itemView.setOnSingleClickListener {
                mListener?.onItemClick(bindingAdapterPosition)
            }
            tvBasicInformation.setOnSingleClickListener {
                mListener?.onBasicInformationClick(bindingAdapterPosition)
            }
            tvTrainData.setOnSingleClickListener {
                mListener?.onTrainDataClick(bindingAdapterPosition)
            }
        }

        fun bind(patient: Patient,position: Int){
            clPatientInfo.isSelected = mSelectPosition == position
            tvPatientName.text = patient.name
            when(patient.gender){
                Gender.MALE.num ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_male)
                }
                Gender.FEMALE.num ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_female)
                }
                else ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_unknown)
                }
            }
            if ((patient.age ?: 0) > 0){
                tvPatientAge.text = itemView.context.getString(R.string.str_age_int,patient.age)
            }else{
                tvPatientAge.text = itemView.context.getString(R.string.str_unknown)
            }
            tvPatientId.text = itemView.context.getString(R.string.str_id_s, patient.thirdBizId?:itemView.context.getString(R.string.str_no_number))
            patient.phoneNumber.isNullOrEmpty().also {
                if (it){
                    tvPatientPhone.text = itemView.context.getString(R.string.str_phone_s, itemView.context.getString(R.string.str_no_phone_number_yet))
                }else{
                    tvPatientPhone.text = itemView.context.getString(R.string.str_phone_s, patient.phoneNumber)
                }
            }
            patient.username.isNullOrEmpty().also {
                if (it){
                    tvPatientFileNum.text = itemView.context.getString(R.string.str_file_num_s, itemView.context.getString(R.string.str_no_file_number_yet))
                }else{
                    tvPatientFileNum.text = itemView.context.getString(R.string.str_file_num_s, patient.username)
                }
            }
        }

    }

    interface ItemClickListener{
        fun onItemClick(position: Int)
        fun onBasicInformationClick(position: Int)
        fun onTrainDataClick(position: Int)
    }

}