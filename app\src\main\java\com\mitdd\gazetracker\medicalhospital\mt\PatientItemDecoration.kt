package com.mitdd.gazetracker.medicalhospital.mt

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * FileName: PatientItemDecoration
 * Author by lilin,Date on 2025/4/17 16:40
 * PS: Not easy to write code, please indicate.
 */
class PatientItemDecoration(private val space: Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        if (position != 0){
            outRect.top = space
        }
    }

}