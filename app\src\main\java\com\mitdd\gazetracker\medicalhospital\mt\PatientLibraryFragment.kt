package com.mitdd.gazetracker.medicalhospital.mt

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.databinding.FragmentPatientLibraryBinding
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.medicalhospital.vm.MHospitalMTViewModel
import com.mitdd.gazetracker.medicalhospital.vm.PatientViewModel
import com.mitdd.gazetracker.user.bean.Gender
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: PatientLibraryFragment
 * Author by lilin,Date on 2025/4/17 11:46
 * PS: Not easy to write code, please indicate.
 * 患者库Fragment
 */
class PatientLibraryFragment : BaseBindingFragment<FragmentPatientLibraryBinding>() {

    companion object{
        private val TAG = PatientLibraryFragment::class.java.simpleName

        const val FRAGMENT_TAG = "PATIENT_LIBRARY"

        fun newInstance(): PatientLibraryFragment {
            val fragment = PatientLibraryFragment()
            return fragment
        }
    }

    override fun createBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentPatientLibraryBinding {
        return FragmentPatientLibraryBinding.inflate(inflater,container,false)
    }

    private var patients: MutableList<Patient> = mutableListOf()
    private val patientAdapter = PatientAdapter(patients)

    private val mHospitalMTVM by activityViewModels<MHospitalMTViewModel>()
    private val patientVM by activityViewModels<PatientViewModel>()
    //是否是刷新患者列表
    private val isRefreshPatient = AtomicBoolean(false)
    //是否是加载更多患者列表
    private val isLoadMorePatient = AtomicBoolean(false)
    //患者列表页码，从1开始,0表示尚未加载数据
    private var patientPage = 0

    override fun initParam() {
    }

    override fun initView() {
        initListener()

        binding.rvPatient.adapter = patientAdapter
        binding.rvPatient.layoutManager = LinearLayoutManager(mActivity, LinearLayoutManager.VERTICAL, false)
        binding.rvPatient.addItemDecoration(PatientItemDecoration(8.dp2px(mActivity)))

        binding.smartRefresh.setRefreshHeader(ClassicsHeader(mActivity))
        binding.smartRefresh.setRefreshFooter(ClassicsFooter(mActivity))
        binding.smartRefresh.setEnableLoadMoreWhenContentNotFull(false)

    }

    override fun initObserver() {
        mHospitalMTVM.addPatientLiveData.observe(this){
            if (it != null){
                patients.add(0,it)
                patientAdapter.notifyItemInserted(0)
                binding.rvPatient.scrollToPosition(0)
            }
        }
        patientVM.patientListLiveData.observe(this){
            if (isRefreshPatient.get()){//刷新
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                binding.smartRefresh.finishRefresh()
                patients.clear()
                patients.addAll(list)
                patientAdapter.notifyDataSetChanged()
                patientPage = 1
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                isRefreshPatient.set(false)
            }else if (isLoadMorePatient.get()){//加载更多
                val list = it?.list
                val total = it?.total?:0
                if (!list.isNullOrEmpty()){
                    patients.addAll(list)
                    patientAdapter.notifyItemRangeInserted(patients.size - list.size,list.size)
                    patientPage++
                }
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }else{
                    binding.smartRefresh.finishLoadMore(true)
                }
                isLoadMorePatient.set(false)
            }else{//首次加载数据
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                patients.clear()
                patients.addAll(list)
                patientAdapter.notifyDataSetChanged()
                patientPage = 1
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
            }
        }
        patientVM.modifyPatientLiveData.observe(this){
            if (it != null){
                loadPatient(1)
                Toast.makeText(mActivity,getString(R.string.str_patient_info_modified_successfully),Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_patient_info_modified_failed),Toast.LENGTH_SHORT).show()
            }
        }
        patientVM.queryPatientLiveData.observe(this){
            if (it != null){
                val newUserDialog = NewUserDialog(mActivity,false).apply {
                    onOkClick = { patient,isNewUser ->
                        if (!isNewUser){
                            patientVM.modifyPatient(patient.id?:"",patient.name?:"",patient.gender?: Gender.MALE.num,
                                patient.birthday?:"",patient.phoneNumber?:"",
                                patient.phoneCountryCode?:86,patient.email?:"")
                        }
                    }
                }
                newUserDialog.show()
                newUserDialog.setPatient(it)
                Toast.makeText(mActivity,getString(R.string.str_patient_info_queried_successfully),Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_patient_info_queried_failed),Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun initData() {
        loadPatient(1)
    }

    private fun initListener(){
        binding.etSearch.setOnEditorActionListener { view, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val keywords = binding.etSearch.text.toString()
                Logger.d(TAG, msg = "etSearch ACTION_SEARCH keywords = $keywords")
                loadPatient(1,keywords = keywords)
                hideKeyboard(view)
                // 返回 true 表示事件已处理
                true
            } else {
                // 其他动作不处理
                false
            }
        }
        binding.ivSearchPatients.setOnSingleClickListener {
            val keywords = binding.etSearch.text.toString()
            Logger.d(TAG, msg = "ivSearchPatients keywords = $keywords")
            loadPatient(1,keywords = keywords)
        }
        patientAdapter.setItemClickListener(object : PatientAdapter.ItemClickListener{
            override fun onItemClick(position: Int) {
                if (position in patients.indices){
                    val patient = patients[position]
                    Logger.d(TAG, msg = "onItemClick id = $${patient.id},name = ${patient.name}")
                    val currentPatient = patientVM.currentPatient
                    if (currentPatient == null || currentPatient.id != patient.id){
                        patientAdapter.setSelectPosition(position)
                        patientVM.setCurrentPatient(patients[position])
                    }
                }
            }

            override fun onBasicInformationClick(position: Int) {
                if (position in patients.indices){
                    val id = patients[position].id
                    Logger.d(TAG, msg = "onBasicInformationClick id = $id,name = ${patients[position].name}")
                    if (!id.isNullOrEmpty()){
                        patientVM.queryPatient(id)
                    }
                }
            }

            override fun onTrainDataClick(position: Int) {
                if (position in patients.indices){
                    val id = patients[position].id
                    Logger.d(TAG, msg = "onTrainDataClick id = $id,name = ${patients[position].name}")
                    if (!id.isNullOrEmpty()){
                        showTrainData(id)
                    }
                }
            }

        })
        binding.smartRefresh.setOnRefreshListener{
            val keywords = binding.etSearch.text.toString()
            refreshPatient(1,keywords = keywords)
        }
        binding.smartRefresh.setOnLoadMoreListener {
            val keywords = binding.etSearch.text.toString()
            loadMorePatient(patientPage + 1,keywords = keywords)
        }
    }

    /**
     * 加载患者列表
     */
    private fun loadPatient(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        Logger.d(TAG, msg = "loadPatient page = $page,size = $size,sort = $sort,gender = $gender,keywords = $keywords")
        patientVM.getPatientList(page, size, sort, gender, keywords)
    }

    /**
     * 下拉刷新患者列表
     */
    private fun refreshPatient(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        Logger.d(TAG, msg = "refreshPatient page = $page,size = $size,sort = $sort,gender = $gender,keywords = $keywords")
        isRefreshPatient.set(true)
        patientVM.getPatientList(page, size, sort, gender, keywords)
    }

    /**
     * 上划加载更多患者列表
     */
    private fun loadMorePatient(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        Logger.d(TAG, msg = "loadMorePatient page = $page,size = $size,sort = $sort,gender = $gender,keywords = $keywords")
        isLoadMorePatient.set(true)
        patientVM.getPatientList(page, size, sort, gender, keywords)
    }

    // 关闭键盘方法（优化版）
    private fun hideKeyboard(view: View) {
        val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun showTrainData(patientId: String){
        val fragment =
            parentFragmentManager.findFragmentByTag(MHMTTrainDataFragment.FRAGMENT_TAG)
        if (fragment == null){
            parentFragmentManager.commit {
                parentFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)?.let {
                    hide(it)
                }
                add(R.id.fl_content_container, MHMTTrainDataFragment.newInstance(patientId),MHMTTrainDataFragment.FRAGMENT_TAG)
                addToBackStack(MHMTTrainDataFragment.FRAGMENT_TAG)
            }
        }else{
            (fragment as? MHMTTrainDataFragment)?.updatePatient(patientId)
        }
    }

}