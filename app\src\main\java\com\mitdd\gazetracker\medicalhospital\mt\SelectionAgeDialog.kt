package com.mitdd.gazetracker.medicalhospital.mt

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.DatePicker
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R
import java.util.Calendar

/**
 * FileName: SelectionAgeDialog
 * Author by lilin,Date on 2025/4/21 16:28
 * PS: Not easy to write code, please indicate.
 */
class SelectionAgeDialog(context: Context) : BaseCommonDialog(context)  {

    companion object{
        private val TAG = SelectionAgeDialog::class.java.simpleName
    }

    private val datePicker by id<DatePicker>(R.id.date_picker)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val tvCancel by id<TextView>(R.id.tv_cancel)

    var onOkClick:((year:Int,month:Int,day:Int) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_selection_age)

        initView()
        initListener()
    }

    private fun initView() {

        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        val startYear = currentYear - 100  // 起始年份为当前年份-100年
        val endYear = currentYear          // 结束年份为当前年份

        // 设置最小日期（起始年份的1月1日）
        val calendarMin = Calendar.getInstance()
        calendarMin.set(startYear, 0, 1)
        datePicker.minDate = calendarMin.timeInMillis

        // 设置最大日期（结束年份的12月31日）
        val calendarMax = Calendar.getInstance()
        calendarMax.set(endYear, 11, 31)
        datePicker.maxDate = calendarMax.timeInMillis
    }

    private fun initListener() {
        tvOk.setOnClickListener {
            val year = datePicker.year
            val month = datePicker.month + 1 // 月份从0开始（0=1月）
            val dayOfMonth = datePicker.dayOfMonth
            onOkClick?.invoke(year,month,dayOfMonth)
            dismiss()
        }
        tvCancel.setOnClickListener {
            dismiss()
        }
    }

    fun setDate(year:Int,month:Int,day:Int){
        datePicker.init(year, month, day, null)
    }

}