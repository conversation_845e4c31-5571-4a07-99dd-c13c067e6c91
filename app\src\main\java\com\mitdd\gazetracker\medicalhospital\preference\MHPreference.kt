package com.mitdd.gazetracker.medicalhospital.preference

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: MHPreference
 * Author by lilin,Date on 2025/6/12 17:35
 * PS: Not easy to write code, please indicate.
 * 医疗进院版Preference
 */
enum class MHPreference(private val defaultValue:Any?) : INameSpace  {

    /**
     * 手机国别码
     */
    PHONE_COUNTRY_CODE(null);

    override fun getNameSpace(): String {
        return "MHPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}