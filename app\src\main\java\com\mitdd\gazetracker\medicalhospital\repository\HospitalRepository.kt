package com.mitdd.gazetracker.medicalhospital.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.medicalhospital.api.HospitalApiService
import com.mitdd.gazetracker.medicalhospital.bean.MHospitalProfile
import com.mitdd.gazetracker.net.MainRetrofitClient

/**
 * FileName: HospitalRepository
 * Author by lilin,Date on 2025/1/6 10:24
 * PS: Not easy to write code, please indicate.
 */
class HospitalRepository : BaseRepository() {

    /**
     * 获取医疗进院版设备信息
     */
    suspend fun getHospitalEditionProfile(): ApiResponse<MHospitalProfile> {
        return executeHttp {
            MainRetrofitClient.createService(HospitalApiService::class.java).getHospitalEditionProfile()
        }
    }
}