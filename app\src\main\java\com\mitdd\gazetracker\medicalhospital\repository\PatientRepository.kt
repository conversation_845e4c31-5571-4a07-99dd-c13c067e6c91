package com.mitdd.gazetracker.medicalhospital.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhospital.api.PatientApiService
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.medicalhospital.bean.PatientAdd
import com.mitdd.gazetracker.medicalhospital.bean.PatientList
import com.mitdd.gazetracker.medicalhospital.bean.PatientTrainDataList
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.net.OverseasRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: PatientRepository
 * Author by lilin,Date on 2025/4/23 10:22
 * PS: Not easy to write code, please indicate.
 */
class PatientRepository : BaseRepository() {

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)
     */
    suspend fun getPatientList(page:Int,size:Int,sort:String?,
                               gender:Int?,keywords:String?,
                               authorization: String
    ): ApiResponse<PatientList> {
        return executeHttp {
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).getPatientList("vision-v2/api/patient",page, size, sort, gender, keywords,authorization)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).getPatientList("dt/api/patient",page, size, sort, gender, keywords,authorization)
            }
        }
    }

    /**
     * 添加患者
     * @param name 姓名,示例值(张三)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param phoneCountryCode 手机号国家代码,示例值(86)
     * @param email 邮箱,示例值(<EMAIL>)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun addPatient(name:String,gender:Int,birthday:String,
                           phoneNumber:String,phoneCountryCode:Int,email:String,authorization: String
    ): ApiResponse<PatientAdd> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["name"] = name
            hashMap["gender"] = gender
            hashMap["birthday"] = birthday
            hashMap["phoneNumber"] = phoneNumber
            hashMap["phoneCountryCode"] = phoneCountryCode
            hashMap["email"] = email
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).addPatient("vision-v2/api/patient",authorization,requestBody)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).addPatient("dt/api/patient",authorization,requestBody)
            }
        }
    }

    /**
     * 修改患者
     * @param patientId 患者ID,示例值(1)
     * @param name 姓名,示例值(张三)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param phoneCountryCode 手机号国家代码,示例值(86)
     * @param email 邮箱,示例值(<EMAIL>)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun modifyPatient(patientId:String,name:String,gender:Int,birthday:String,
                           phoneNumber:String,phoneCountryCode:Int,email:String, authorization: String
    ): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["name"] = name
            hashMap["gender"] = gender
            hashMap["birthday"] = birthday
            hashMap["phoneNumber"] = phoneNumber
            hashMap["phoneCountryCode"] = phoneCountryCode
            hashMap["email"] = email
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).modifyPatient("vision-v2/api/patient/$patientId",authorization,requestBody)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).modifyPatient("dt/api/patient/$patientId",authorization,requestBody)
            }
        }
    }

    /**
     * 查询患者信息
     * @param patientId 患者ID,示例值(1)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun queryPatient(patientId:String,authorization: String): ApiResponse<Patient> {
        return executeHttp {
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).queryPatient("vision-v2/api/patient/$patientId",authorization)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).queryPatient("dt/api/patient/$patientId",authorization)
            }
        }
    }

    /**
     * 指定患者的每日训练数据
     * @param patientId 患者id
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param type amblyopia(弱视)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)
     */
    suspend fun getPatientTrainData(patientId:String,page:Int,size:Int,type:String, authorization: String): ApiResponse<PatientTrainDataList> {
        return executeHttp {
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).getPatientTrainData("vision-v2/api/occlusion-therapy/stats/$patientId",page,size,type,authorization)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).getPatientTrainData("dt/api/occlusion-therapy/stats/$patientId",page,size,type,authorization)
            }
        }
    }

    /**
     * 获取指定患者遮盖疗法信息
     * @param patientId 患者id
     * @param authorization 签名
     */
    suspend fun getPatientMT(patientId:String,authorization: String): ApiResponse<CureInfo> {
        return executeHttp {
            if (DeviceManager.isOverseas()){
                OverseasRetrofitClient.createService(PatientApiService::class.java).getPatientMT("vision-v2/api/occlusion-therapy/today",patientId,authorization)
            }else{
                MainRetrofitClient.createService(PatientApiService::class.java).getPatientMT("dt/api/occlusion-therapy/today",patientId,authorization)
            }
        }
    }

}