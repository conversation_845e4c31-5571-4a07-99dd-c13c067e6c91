package com.mitdd.gazetracker.medicalhospital.train

import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.print.PrintAttributes
import android.print.PrintManager
import android.text.TextUtils
import android.webkit.WebView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.google.gson.Gson
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.flipbeat.FlipBeatListener
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import com.mitdd.gazetracker.medicalhospital.inspection.InspectionCenterActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * FileName: TrainCenterActivity
 * Author by lilin,Date on 2024/12/26 10:46
 * PS: Not easy to write code, please indicate.
 * 进院版-训练中心
 */
class TrainCenterActivity : GTBaseActivity(), TrainCenterWebView.TrainCenterActionListener {

    companion object{
        private val TAG = TrainCenterActivity::class.java.simpleName
        private const val INPUT_PARAM_URL = "url"

        fun createIntent(context: Context,url:String): Intent {
            val intent = Intent(context, TrainCenterActivity::class.java)
            intent.putExtra(INPUT_PARAM_URL,url)
            return intent
        }
    }

    private val flipBeatListener = object : FlipBeatListener {
        override fun onConnectionStateChange(device: BluetoothDevice, state: FlipBeatState) {
            updateFlipConnected(state)
        }
    }

    private val wvTrainCenter by id<TrainCenterWebView>(R.id.wv_train_center)

    private val gson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false)?:false
            if (isSucceed){
                Toast.makeText(this,getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(this,getString(R.string.str_calibration_failure), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private val mOnBackPressedCallback = object : OnBackPressedCallback(true){
        override fun handleOnBackPressed() {
            Logger.d(TAG, msg = "handleOnBackPressed")
            onHandleOnBackPressed()
        }

    }

    private var mUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_train_center)

        onBackPressedDispatcher.addCallback(this,mOnBackPressedCallback)

        initParam()
        initView()
        initData()
    }

    private fun initParam(){
        mUrl = intent.getStringExtra(InspectionCenterActivity.INPUT_PARAM_URL)?:""
    }

    private fun initView() {
        initListener()
        wvTrainCenter.addJavascriptInterface(wvTrainCenter.TrainCenterAction(),"Train")
        wvTrainCenter.setActionListener(this)
    }

    private fun initData() {
        if (!TextUtils.isEmpty(mUrl)){
            wvTrainCenter.loadUrl(mUrl)
        }
    }

    private fun initListener(){
        FlipBeatManager.registerFlipBeatListener(flipBeatListener)
    }

    private fun updateFlipConnected(state: FlipBeatState){
        val isConnected = state == FlipBeatState.CONNECTED
        val hashMap = HashMap<String, Any>()
        hashMap["type"] = "fliperStatus"
        hashMap["data"] = if (isConnected) 1 else 0
        wvTrainCenter.sendDataToWebView(gson.toJson(hashMap))
    }

    override fun onFlipClap() {
        lifecycleScope.launch(Dispatchers.Main) {
            FlipBeatManager.writeDataToFlipBeat(arrayOf("0xAA","0x55","0xA5","0x5A","0x21","0x01","0x00","0xCC","0x66","0xC6","0x6C"))
        }
    }

    override fun onFlipRecover() {
        lifecycleScope.launch(Dispatchers.Main) {
            FlipBeatManager.writeDataToFlipBeat(arrayOf("0xAA","0x55","0xA5","0x5A","0x21","0x01","0x01","0xCC","0x66","0xC6","0x6C"))
        }
    }

    override fun onCalibration() {
        lifecycleScope.launch(Dispatchers.Main) {
            calibrationLauncher.launch(CalibrationActivity.createIntent(this@TrainCenterActivity, CalibrationMode.CALIBRATION,false))
        }
    }

    override fun onStartGazeTracker() {
        lifecycleScope.launch(Dispatchers.Main) {
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_START_TRACK
                }
            )
        }
    }

    override fun onStopGazeTracker() {
        lifecycleScope.launch(Dispatchers.Main) {
            sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
        }
    }

    override fun onFinish() {
        lifecycleScope.launch(Dispatchers.Main) {
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(wvTrainCenter)
        }
    }

    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }


    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage: ${msg.what}")
    }

    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        unbindService(serviceConnection)
    }

    override fun onDestroy() {
        super.onDestroy()
        wvTrainCenter.destroy()
        FlipBeatManager.unRegisterFlipBeatListener(flipBeatListener)
    }

    private fun onHandleOnBackPressed(){
        if (wvTrainCenter.isHome.get()){
            finish()
        }else{
            wvTrainCenter.backPressed()
        }
    }

}