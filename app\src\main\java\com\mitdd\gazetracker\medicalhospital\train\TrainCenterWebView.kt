package com.mitdd.gazetracker.medicalhospital.train

import android.content.Context
import android.util.AttributeSet
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.flipbeat.FlipBeatManager
import com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: TrainCenterWebView
 * Author by lilin,Date on 2024/12/26 11:45
 * PS: Not easy to write code, please indicate.
 */
class TrainCenterWebView : CustomWebView {

    companion object{
        private val TAG = TrainCenterWebView::class.java.simpleName
        private const val ACTION_FLIP_FLIP_CLAP = "flipFlipClap"
        private const val ACTION_FLIP_FLIP_RECOVER = "flipFlipRecover"
    }

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    init {
        //设置缓存模式
        settings.cacheMode = WebSettings.LOAD_NO_CACHE
        setWebViewClient(TrainCenterWebViewClient())
    }

    val isHome = AtomicBoolean(true)

    private var actionListener: TrainCenterActionListener? = null

    fun setActionListener(listener: TrainCenterActionListener?){
        actionListener = listener
    }

    /**
     * 发送数据到WebView
     * @param data 数据 json格式
     */
    fun sendDataToWebView(data:String){
        Logger.d(TAG, msg = "sendDataToWebView data = $data")
        evaluateJavascript("javascript:window.sendDataToWebView('$data')",null)
    }

    /**
     * 通知Web端返回事件
     */
    fun backPressed(){
        Logger.d(TAG, msg = "backPressed")
        val hashMap = HashMap<String, Any>()
        hashMap["type"] = "goBack"
        sendDataToWebView(gson.toJson(hashMap))
    }

    interface TrainCenterActionListener{
        //翻转拍翻转
        fun onFlipClap()
        //翻转牌恢复
        fun onFlipRecover()
        fun onCalibration()
        fun onStartGazeTracker()
        fun onStopGazeTracker()
        fun onFinish()
        fun onPrintPage()
    }

    inner class TrainCenterAction{

        //训练回调消息
        @JavascriptInterface
        fun postMessage(msg:String){
            Logger.d(TAG, msg = "postMessage msg = $msg")
            val jsonObject = JSONObject(msg)
            val action = jsonObject.optString("action")
            when(action){
                ACTION_FLIP_FLIP_CLAP ->{
                    actionListener?.onFlipClap()
                }
                ACTION_FLIP_FLIP_RECOVER ->{
                    actionListener?.onFlipRecover()
                }
            }
        }

        @JavascriptInterface
        fun printPage() {
            Logger.d(TAG, msg = "printPage")
            actionListener?.onPrintPage()
        }

        //校准
        @JavascriptInterface
        fun calibration(){
            Logger.d(TAG, msg = "calibration")
            actionListener?.onCalibration()
        }

        //启动眼动服务
        @JavascriptInterface
        fun startGazeTracker(){
            Logger.d(TAG, msg = "startGazeTracker")
            actionListener?.onStartGazeTracker()
        }

        //关闭眼动服务
        @JavascriptInterface
        fun stopGazeTracker(){
            Logger.d(TAG, msg = "stopGazeTracker")
            actionListener?.onStopGazeTracker()
        }

        //关闭页面
        @JavascriptInterface
        fun finish(){
            Logger.d(TAG, msg = "finish")
            actionListener?.onFinish()
        }

        @JavascriptInterface
        fun home(isHome:Boolean){
            Logger.d(TAG, msg = "home isHome = $isHome")
            <EMAIL>(isHome)
        }

        @JavascriptInterface
        fun getDeviceSn():String{
            Logger.d(TAG, msg = "getDeviceSn")
            return DeviceManager.getDeviceSn()
        }
    }

    inner class TrainCenterWebViewClient : CustomWebViewClient(){
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            val isConnected = FlipBeatManager.getFlipBeatState() == FlipBeatState.CONNECTED
            val hashMap = HashMap<String, Any>()
            hashMap["type"] = "fliperStatus"
            hashMap["data"] = if (isConnected) 1 else 0
            sendDataToWebView(gson.toJson(hashMap))
        }
    }

}