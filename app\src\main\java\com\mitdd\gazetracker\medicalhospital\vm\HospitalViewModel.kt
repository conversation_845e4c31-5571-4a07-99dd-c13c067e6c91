package com.mitdd.gazetracker.medicalhospital.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.medicalhome.vm.HomeViewModel
import com.mitdd.gazetracker.medicalhospital.bean.MHospitalProfile
import com.mitdd.gazetracker.medicalhospital.repository.HospitalRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: HospitalViewModel
 * Author by lilin,Date on 2025/1/6 10:25
 * PS: Not easy to write code, please indicate.
 */
class HospitalViewModel : ViewModel() {

    companion object{
        private val TAG = HomeViewModel::class.java.name
    }

    private val hospitalRepository by lazy { HospitalRepository() }

    //进院版配置信息
    val mHospitalProfileLiveData = MutableLiveData<MHospitalProfile?>()

    /**
     * 获取进院版配置信息
     */
    fun getHospitalEditionProfile(){
        viewModelScope.launch {
            MutableStateFlow(hospitalRepository.getHospitalEditionProfile()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getHospitalEditionProfile onSuccess")
                    mHospitalProfileLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getHospitalEditionProfile onDataEmpty")
                    mHospitalProfileLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getHospitalEditionProfile onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    mHospitalProfileLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getHospitalEditionProfile onError = $it")
                    mHospitalProfileLiveData.postValue(null)
                }
            }
        }
    }

}