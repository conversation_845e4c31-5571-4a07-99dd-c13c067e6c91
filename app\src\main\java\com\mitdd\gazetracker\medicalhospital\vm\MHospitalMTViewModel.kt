package com.mitdd.gazetracker.medicalhospital.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.mitdd.gazetracker.medicalhospital.bean.Patient

/**
 * FileName: MHospitalMTVM
 * Author by lilin,Date on 2025/4/17 19:08
 * PS: Not easy to write code, please indicate.
 * 医疗-到院版-遮盖疗法 模块ViewModel
 */
class MHospitalMTViewModel : ViewModel(){

    companion object{
        private val TAG = MHospitalMTViewModel::class.java.name
    }

    //遮盖疗法状态 true表示开启，false表示关闭
    val mtStateLiveData = MutableLiveData<Boolean>()

    //新增患者
    val addPatientLiveData = MutableLiveData<Patient>()

    /**
     * 设置遮盖疗法状态
     * @param state true表示开启，false表示关闭
     */
    fun setOcclusionTherapyState(state:Boolean){
        mtStateLiveData.postValue(state)
    }

}