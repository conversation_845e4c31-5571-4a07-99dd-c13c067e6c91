package com.mitdd.gazetracker.medicalhospital.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.medicalhome.vm.MaskViewModel
import com.mitdd.gazetracker.medicalhospital.bean.Patient
import com.mitdd.gazetracker.medicalhospital.bean.PatientAdd
import com.mitdd.gazetracker.medicalhospital.bean.PatientList
import com.mitdd.gazetracker.medicalhospital.bean.PatientTrainDataList
import com.mitdd.gazetracker.medicalhospital.repository.PatientRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: PatientViewModel
 * Author by lilin,Date on 2025/4/23 10:23
 * PS: Not easy to write code, please indicate.
 */
class PatientViewModel : ViewModel() {

    companion object{
        private val TAG = PatientViewModel::class.java.name
    }

    private val patientRepository by lazy { PatientRepository() }

    //授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)
    var authorization = ""
    //当前患者
    val curPatientLiveData = MutableLiveData<Patient?>()
    val currentPatient get() = curPatientLiveData.value
    //患者列表
    val patientListLiveData = MutableLiveData<PatientList?>()
    //添加患者
    val patientAddLiveData = MutableLiveData<PatientAdd?>()
    //修改患者
    val modifyPatientLiveData = MutableLiveData<Any?>()
    //查询患者
    val queryPatientLiveData = MutableLiveData<Patient?>()
    //患者训练数据列表
    val patientTrainDataListLiveData = MutableLiveData<PatientTrainDataList?>()
    //患者遮盖疗法信息
    val patientMTLiveData = MutableLiveData<CureInfo?>()
    val patientMT get() = patientMTLiveData.value
    //当前治疗时长，单位秒
    val treatmentDuration get() = patientMT?.trainingDuration?:0
    //计划治疗时长，单位秒
    val plannedDuration get() = patientMT?.plannedDuration?:0
    //弱势眼 左left 右right
    val eyePosition get() = patientMT?.position?: AmblyopicEye.LEFT.value
    //是否已完成今日治疗
    val isFinishUp get() = patientMT?.isFinishUp == true

    /**
     * 设置当前患者
     */
    fun setCurrentPatient(patient: Patient?){
        curPatientLiveData.postValue(patient)
    }

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    fun getPatientList(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.getPatientList(page, size,
                sort ?: "createTime,desc", gender, keywords?.ifEmpty { null },authorization)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getPatientList onSuccess")
                    patientListLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getPatientList onDataEmpty")
                    patientListLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getPatientList onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    patientListLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getPatientList onError = $it")
                    patientListLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 添加患者
     * @param name 姓名,示例值(张三)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param phoneCountryCode 手机号国家代码,示例值(86)
     * @param email 邮箱,示例值(<EMAIL>)
     */
    fun addPatient(name:String,gender:Int,birthday:String,
                       phoneNumber:String,phoneCountryCode:Int,email:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.addPatient(name, gender, birthday, phoneNumber, phoneCountryCode, email,authorization)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "addPatient onSuccess")
                    patientAddLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "addPatient onDataEmpty")
                    patientAddLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "addPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    patientAddLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "addPatient onError = $it")
                    patientAddLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 修改患者信息
     * @param id 患者ID,示例值(1)
     * @param name 姓名,示例值(张三)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param phoneCountryCode 手机号国家代码,示例值(86)
     * @param email 邮箱,示例值(<EMAIL>)
     */
    fun modifyPatient(id:String,name:String,gender:Int,birthday:String,
                      phoneNumber:String,phoneCountryCode:Int,email:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.modifyPatient(id, name, gender, birthday, phoneNumber, phoneCountryCode, email,authorization)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "modifyPatient onSuccess")
                    modifyPatientLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "modifyPatient onDataEmpty")
                    modifyPatientLiveData.postValue(Any())
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "modifyPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    modifyPatientLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "modifyPatient onError = $it")
                    modifyPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 查询患者信息
     * @param id 患者ID,示例值(1)
     */
    fun queryPatient(id:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.queryPatient(id,authorization)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "queryPatient onSuccess")
                    queryPatientLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "queryPatient onDataEmpty")
                    queryPatientLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "queryPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    queryPatientLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "queryPatient onError = $it")
                    queryPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 指定患者的每日训练数据
     * @param patientId 患者id
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     */
    fun getPatientTrainData(patientId:String,page:Int,size:Int){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.getPatientTrainData(patientId,  page, size, "amblyopia",authorization)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getPatientTrainData onSuccess")
                    patientTrainDataListLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getPatientTrainData onDataEmpty")
                    patientTrainDataListLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getPatientTrainData onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    patientTrainDataListLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getPatientTrainData onError = $it")
                    patientTrainDataListLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 获取指定患者遮盖疗法信息
     * @param patientId 患者id
     */
    fun getPatientMT(patientId:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.getPatientMT(patientId,authorization)).collectResponse{
                onSuccess = { it,_,_->
                    Logger.d(TAG, msg = "getTodayOcclusionTherapy onSuccess")
                    setPatientMT(it)
                }
                onDataEmpty = {_,_->
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onDataEmpty")
                    setPatientMT(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    setPatientMT(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTodayOcclusionTherapy onError = $it")
                    setPatientMT(null)
                }
            }
        }
    }

    /**
     * 设置患者遮盖疗法信息
     */
    fun setPatientMT(cureInfo: CureInfo?){
        //保存遮盖疗法参数
        MaskManager.setCoverChannel(cureInfo?.blurChannel)
        MaskManager.setCoverMode(cureInfo?.blurMode)
        MaskManager.setCoverArea(cureInfo?.blurRadius)
        MaskManager.setCoverRange(cureInfo?.blurSigma)
        MaskManager.setAmblyopicEye(cureInfo?.position)
        patientMTLiveData.postValue(cureInfo)
    }

}