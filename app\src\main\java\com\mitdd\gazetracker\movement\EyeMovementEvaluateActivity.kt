package com.mitdd.gazetracker.movement

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.movement.follow.FollowAbilityEvaluateActivity
import com.mitdd.gazetracker.movement.gaze.GazeStabilityEvaluateActivity
import com.mitdd.gazetracker.movement.patient.EMPatientInfoActivity
import com.mitdd.gazetracker.movement.patient.EMPatientManager
import com.mitdd.gazetracker.movement.roi.ROIDetectionActivity
import com.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluateActivity

/**
 * FileName: EyeMovementEvaluationActivity
 * Author by lilin,Date on 2024/12/10 15:00
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估系统首页
 */
class EyeMovementEvaluateActivity : GTBaseActivity(){

    companion object{
        private val TAG = EyeMovementEvaluateActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, EyeMovementEvaluateActivity::class.java)
        }
    }

    private val tvGazeStability by id<TextView>(R.id.tv_gaze_stability)
    private val tvFollowAbility by id<TextView>(R.id.tv_follow_ability)
    private val tvSaccadeAbility by id<TextView>(R.id.tv_saccade_ability)
    private val tvRoiDetection by id<TextView>(R.id.tv_roi_detection)
    private val tvCalibration by id<TextView>(R.id.tv_calibration)
    private val tvPatientInformation by id<TextView>(R.id.tv_patient_information)

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false)?:false
            if (isSucceed){
                Toast.makeText(this,getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                val calibrationFailureDialog = CalibrationFailureDialog(this).apply {
                    onRecalibration = {
                        startCalibration()
                    }
                }
                calibrationFailureDialog.show()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_eye_movement_evaluate)

        Logger.d(TAG, msg = "眼球运动评估系统首页启动")
        val currentPatient = EMPatientManager.getEMPatient()
        Logger.d(TAG, msg = "当前患者状态: ${if (currentPatient != null) "已选择患者 - ID: ${currentPatient.id}, 姓名: ${currentPatient.name}" else "未选择患者"}")

        initView()
    }

    private fun initView(){
        tvGazeStability.setOnSingleClickListener {
            if (EMPatientManager.getEMPatient() != null){
                startActivity(GazeStabilityEvaluateActivity.createIntent(this))
            }else{
                startActivity(EMPatientInfoActivity.createIntent(this))
            }
        }
        tvFollowAbility.setOnSingleClickListener {
            if (EMPatientManager.getEMPatient() != null){
                startActivity(FollowAbilityEvaluateActivity.createIntent(this))
            }else{
                startActivity(EMPatientInfoActivity.createIntent(this))
            }
        }
        tvSaccadeAbility.setOnSingleClickListener {
            if (EMPatientManager.getEMPatient() != null){
                startActivity(SaccadeAbilityEvaluateActivity.createIntent(this))
            }else{
                startActivity(EMPatientInfoActivity.createIntent(this))
            }
        }
        tvRoiDetection.setOnSingleClickListener {
            if (EMPatientManager.getEMPatient() != null){
                startActivity(ROIDetectionActivity.createIntent(this))
            }else{
                startActivity(EMPatientInfoActivity.createIntent(this))
            }
        }
        tvCalibration.setOnSingleClickListener {
            startCalibration()
        }
        tvPatientInformation.setOnSingleClickListener {
            startActivity(EMPatientInfoActivity.createIntent(this))
        }
    }

    private fun startCalibration() {
        calibrationLauncher.launch(CalibrationActivity.createIntent(this,CalibrationMode.CALIBRATION,false))
    }

}