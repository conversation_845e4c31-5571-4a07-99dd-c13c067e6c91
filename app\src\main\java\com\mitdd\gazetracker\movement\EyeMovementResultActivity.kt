package com.mitdd.gazetracker.movement

import android.content.ContentValues
import android.graphics.Bitmap
import android.provider.MediaStore
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.drawToBitmap
import androidx.core.view.isInvisible
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.TimeUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: EyeMovementResultActivity
 * Author by lilin,Date on 2024/12/13 11:38
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估结果页
 */
abstract class EyeMovementResultActivity : GTBaseActivity(){

    companion object{
        private val TAG = EyeMovementResultActivity::class.java.simpleName
    }

    private val clToolbar by id<ConstraintLayout>(R.id.cl_toolbar)
    private val ivBack by id<ImageView>(R.id.iv_back)
    public val resultContainer by id<FrameLayout>(R.id.result_container)
    private val tvTitle by id<TextView>(R.id.tv_title)
    private val tvEvaluateResult by id<TextView>(R.id.tv_evaluate_result)
    private val tvSerialNumber by id<TextView>(R.id.tv_serial_number)
    private val tvDate by id<TextView>(R.id.tv_date)
    private val tvPointNumber by id<TextView>(R.id.tv_point_number)
    private val tvAverageDuration by id<TextView>(R.id.tv_average_duration)
    private val tvExportData by id<TextView>(R.id.tv_export_data)
    private val tvSave by id<TextView>(R.id.tv_save)

    override fun setContentView(layoutResID: Int) {
        super.setContentView(R.layout.activity_eye_movement_result)
        val contentView = LayoutInflater.from(this).inflate(layoutResID, null)
        val contentParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT)
        resultContainer.addView(contentView, contentParams)

        initListener()
        initView()
    }

    private fun initView(){
        clToolbar.isSelected = true
        val timeString =
            TimeUtils.parseTimeToTimeString(System.currentTimeMillis(), "yyyy-MM-dd HHmmss")
        val timeSplit = timeString.split(" ")
        val ymd = timeSplit[0]
        val hms = timeSplit[1]
        val ymdSplit = ymd.split("-")
        val month = ymdSplit[1]
        val day = ymdSplit[2]
        getSerialNumberView().text = "NO.$month$day${getSerialNumberType()}$hms"
        getDateView().text = ymd
    }

    private fun initListener(){
        ivBack.setOnSingleClickListener {
            finish()
        }
        tvSave.setOnSingleClickListener {
            save()
        }
        tvExportData.setOnSingleClickListener {

        }
        resultContainer.setOnSingleClickListener {
            clToolbar.isSelected = !clToolbar.isSelected
            clToolbar.isInvisible = !clToolbar.isSelected
        }
    }

    abstract fun getSerialNumberType():String

    fun getToolbar():ConstraintLayout{
        return clToolbar
    }

    fun getTitleView():TextView{
        return tvTitle
    }

    fun getEvaluateResultView():TextView{
        return tvEvaluateResult
    }

    fun getSerialNumberView():TextView{
        return tvSerialNumber
    }

    fun getDateView():TextView{
        return tvDate
    }

    fun getPointNumberView():TextView{
        return tvPointNumber
    }

    fun getAverageDurationView():TextView{
        return tvAverageDuration
    }

    open fun save(){
        val bitmap = resultContainer.drawToBitmap()
        saveImageToGallery(bitmap)
    }

    fun exportData(){

    }

    private fun saveImageToGallery(bitmap: Bitmap) {
        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, "screenshot_${TimeUtils.parseTimeToTimeString(System.currentTimeMillis(),"yyyyMMdd-HHmmss")}.png")
            put(MediaStore.Images.Media.MIME_TYPE, "image/png")
            put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/Screenshots")
        }

        val resolver = contentResolver
        val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

        uri?.let {
            try {
                resolver.openOutputStream(it)?.use { fos ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                }
                Toast.makeText(this, "图片保存成功", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this, "图片保存失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

}