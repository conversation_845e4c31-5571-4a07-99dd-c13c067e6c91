package com.mitdd.gazetracker.movement.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.mitdd.gazetracker.movement.bean.EMPatientAdd
import com.mitdd.gazetracker.movement.bean.EMPatientList
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * FileName: EMPatientApiService
 * Author by lilin,Date on 2025/5/14 10:00
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者API服务
 */
interface EMPatientApiService {

    /**
     * 添加眼球运动评估患者
     * @param empatientReq 眼球运动评估患者信息请求体
     */
    @POST("api/patients")
    suspend fun addEMPatient(
        @Body empatientReq: RequestBody
    ): ApiResponse<EMPatientAdd>

    /**
     * 获取眼球运动评估患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    @GET("api/patients")
    suspend fun getEMPatientList(
        @Query("page") page: Int,
        @Query("size") size: Int,
        @Query("sort") sort: String?,
        @Query("gender") gender: Int?,
        @Query("keywords") keywords: String?
    ): ApiResponse<EMPatientList>

    /**
     * 查询眼球运动评估患者信息
     * @param id 患者ID,示例值(1)
     */
    @GET("api/patients/{id}")
    suspend fun queryEMPatient(
        @Path("id") id: String
    ): ApiResponse<EMPatient>

    /**
     * 修改眼球运动评估患者
     * @param id 患者ID,示例值(1)
     * @param empatientReq 眼球运动评估患者信息请求体
     */
    @PUT("api/patients/{id}")
    suspend fun modifyEMPatient(
        @Path("id") id: String,
        @Body empatientReq: RequestBody
    ): ApiResponse<Any>

    /**
     * 删除眼球运动评估患者
     * @param id 患者ID,示例值(1)
     */
    @DELETE("api/patients/{id}")
    suspend fun deleteEMPatient(
        @Path("id") id: String
    ): ApiResponse<Any>

}