package com.mitdd.gazetracker.movement.api

import com.airdoc.component.common.net.entity.ApiResponse

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

/**
 * FileName: FollowAbilityApiService
 * Author by AI Assistant, Date on 2025/6/23
 * PS: Not easy to write code, please indicate.
 * 追随能力检测API服务
 */
interface FollowAbilityApiService {

    /**
     * 提交追随能力检测结果
     */
    @POST("api/movement/follow-ability/submit")
    suspend fun addFollowAbility(
        @Body followAbilityReq: RequestBody
    ): ApiResponse<Long>

    /**
     * 上传图片
     */
    @Multipart
    @POST("api/files/upload/image")
    suspend fun uploadImage(
        @Part file: MultipartBody.Part
    ): FileUploadResponse
}
