package com.mitdd.gazetracker.movement.api

import com.airdoc.component.common.net.entity.ApiResponse

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

interface GazeStabilityApiService {

    @POST("api/movement/gaze-stability/submit")
    suspend fun addGazeStability(
        @Body gazeStabilityReq: RequestBody
    ): ApiResponse<Long>

    @Multipart
    @POST("api/files/upload/image")
    suspend fun uploadImage(
        @Part file: MultipartBody.Part
    ): FileUploadResponse

}