package com.mitdd.gazetracker.movement.api

import com.airdoc.component.common.net.entity.ApiResponse

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

/**
 * FileName: ROIDetectionApiService
 * Author by AI Assistant, Date on 2025/6/24
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测API服务接口
 */
interface ROIDetectionApiService {

    @POST("api/movement/roi-detection/submit")
    suspend fun addROIDetection(
        @Body roiDetectionReq: RequestBody
    ): ApiResponse<Long>

    @Multipart
    @POST("api/files/upload/image")
    suspend fun uploadImage(
        @Part file: MultipartBody.Part
    ): FileUploadResponse

}
