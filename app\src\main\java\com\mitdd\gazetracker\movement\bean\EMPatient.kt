package com.mitdd.gazetracker.movement.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: EMPatient
 * Author by lilin,Date on 2025/5/14 10:00
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者信息
 */
@Parcelize
data class EMPatient(
    //ID
    var id:String? = null,
    //姓名
    var name:String? = null,
    //住院号
    var inpatientNum:String? = null,
    //病例卡号
    var caseCardNum:String? = null,
    //生日
    var birthday:String? = null,
    //年龄
    var age:Int? = null,
    //性别{1=男, 2=女}
    var gender:Int? = null,
    //患者类型（1=阳性、2=阴性）
    var patientType:Int? = null,
    //手机号
    var phone:String? = null,
    //诊断信息
    var diagnosisInformation:String? = null
) : Parcelable{
    fun deepCopy():EMPatient{
        return EMPatient().apply {
            id = <EMAIL>
            name = <EMAIL>
            inpatientNum = <EMAIL>
            caseCardNum = <EMAIL>
            birthday = <EMAIL>
            gender = <EMAIL>
            patientType = <EMAIL>
            phone = <EMAIL>
            diagnosisInformation = <EMAIL>
        }
    }
}
