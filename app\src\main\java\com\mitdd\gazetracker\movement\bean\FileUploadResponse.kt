package com.mitdd.gazetracker.movement.bean

import com.google.gson.annotations.SerializedName

/**
 * FileName: FileUploadResponse
 * Author by lilin,Date on 2025/6/20
 * PS: Not easy to write code, please indicate.
 * 文件上传响应数据类
 */
data class FileUploadResponse(

    val code: Int = 0,

    val message: String? = null,

    val data: FileUploadData? = null,

    val timestamp: Long = 0
)

data class FileUploadData(

    val url: String = "",

    val originalFileName: String = "",

    val fileName: String = "",

    val fileSize: Long = 0,

    val contentType: String = ""
)
