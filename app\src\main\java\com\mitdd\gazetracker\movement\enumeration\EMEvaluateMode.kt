package com.mitdd.gazetracker.movement.enumeration

/**
 * FileName: EMEvaluateMode
 * Author by lilin,Date on 2025/5/15 11:00
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估模式
 */
enum class EMEvaluateMode(val value:String) {
    //注视稳定性
    GAZE_STABILITY("gaze_stability"),
    //追随能力
    FOLLOW_ABILITY("follow_ability"),
    //扫视能力
    SACCADE_ABILITY("saccade_ability"),
    //兴趣区域检测
    ROI_DETECTION("roi_detection")
}