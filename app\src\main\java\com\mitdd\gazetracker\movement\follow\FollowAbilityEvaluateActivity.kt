package com.mitdd.gazetracker.movement.follow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: FollowAbilityEvaluateActivity
 * Author by lilin,Date on 2024/12/10 19:33
 * PS: Not easy to write code, please indicate.
 * 追随能力评估
 */
class FollowAbilityEvaluateActivity : GTBaseActivity() {

    companion object{
        private val TAG = FollowAbilityEvaluateActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, FollowAbilityEvaluateActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_follow_ability_evaluate)

        showFollowAbilityEvaluateExplain()
    }

    /**
     * 显示追随能力评估说明页面
     */
    private fun showFollowAbilityEvaluateExplain(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_follow_ability, FollowAbilityExplainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    /**
     * 显示追随能力评估页面
     */
    fun showFollowAbilityEvaluating(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_follow_ability, FollowAbilityEvaluatingFragment2.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

}