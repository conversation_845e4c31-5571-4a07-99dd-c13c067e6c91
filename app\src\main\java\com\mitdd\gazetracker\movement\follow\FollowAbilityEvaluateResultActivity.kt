package com.mitdd.gazetracker.movement.follow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.movement.EyeMovementResultActivity
import com.mitdd.gazetracker.movement.follow.bean.FollowAbilityEvaluateResult

/**
 * FileName: FollowAbilityEvaluateResultActivity
 * Author by lilin,Date on 2024/12/11 11:01
 * PS: Not easy to write code, please indicate.
 * 追随能力评估结果页面
 */
class FollowAbilityEvaluateResultActivity : EyeMovementResultActivity() {

    companion object{
        private val TAG = FollowAbilityEvaluateResultActivity::class.java.simpleName

        const val INPUT_PARAM_EVALUATE_RESULT = "evaluate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, FollowAbilityEvaluateResultActivity::class.java)
        }
    }

    private val evaluateResultView by id<FollowAbilityEvaluateResultView>(R.id.evaluate_result_view)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_follow_ability_evaluate_result)

        initView()
        initObserver()
        initData()
    }

    private fun initView(){
        getTitleView().text = getString(R.string.str_follow_ability_evaluate_result)
        getEvaluateResultView().apply {
            text = getString(R.string.str_instability)
            setBackgroundResource(R.drawable.common_eb4e89_round_5_bg)
        }
        getPointNumberView().isVisible = false
        getAverageDurationView().isVisible = false
    }

    private fun initObserver(){
        LiveEventBus.get<FollowAbilityEvaluateResult>(INPUT_PARAM_EVALUATE_RESULT).observeSticky(this){
            if (it != null){
                evaluateResultView.drawResult(it)
            }
        }
    }

    private fun initData() {

    }

    override fun getSerialNumberType():String {
        return "02"
    }

}