package com.mitdd.gazetracker.movement.follow

import android.content.Context
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.follow.bean.FollowAbilityEvaluateResult
import androidx.core.graphics.toColorInt

/**
 * FileName: FollowAbilityEvaluateResultView
 * Author by lilin,Date on 2024/12/11 11:07
 * PS: Not easy to write code, please indicate.
 * 追随能力评估结果View
 */
class FollowAbilityEvaluateResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    private val TAG = FollowAbilityEvaluateResultView::class.java.simpleName

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)
    //视点半径
    private var gazePointRadius = 10.dp2px(context)
    //追随路径
    private val followPath = Path()
    //视点列表
    private val gazePoints = mutableListOf<GazePoint>()
    //视点路径
    private val gazePath = Path()
    //视点画笔
    private val gazePointPaint = Paint().apply {
        color = "#F28225".toColorInt()
        style = Paint.Style.FILL
        //抗锯齿
        isAntiAlias = true
    }
    //视点序号画笔
    private val gazeIndexPaint = Paint().apply {
        color = "#333333".toColorInt()
        style = Paint.Style.FILL
        isAntiAlias = true
        textSize = 10f
        textAlign = Paint.Align.CENTER
    }
    //视点路径画笔
    private val gazePathPaint = Paint().apply {
        color = "#F28225".toColorInt()
        style = Paint.Style.STROKE
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 1f, resources.displayMetrics)
        strokeJoin = Paint.Join.ROUND
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }
    //追随路径画笔
    private val followPathPaint = Paint().apply {
        color = "#A1ADC8".toColorInt()
        style = Paint.Style.STROKE
        // 定义虚线的模式：实线部分长度、空白部分长度、相位（偏移量）
        pathEffect = DashPathEffect(floatArrayOf(5f, 2f), 0f)
        //宽度0.5dp
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 0.5f, resources.displayMetrics)
        //设置了当两条线段相交或连接时，连接处的样式为圆角连接。
        strokeJoin = Paint.Join.ROUND
        //设置了线段端点的样式为圆形帽。当绘制线条时，线段的开始和结束位置将会是半圆。
        strokeCap = Paint.Cap.ROUND
        //抗锯齿
        isAntiAlias = true
    }

    // 复用 Rect 对象
    private val textBounds = Rect()

    /**
     * 绘制评估结果
     */
    fun drawResult(result: FollowAbilityEvaluateResult){
        followPath.reset()
        result.followPathPoints.forEachIndexed { index, pointFS ->
            if (index == 0){
                followPath.moveTo(pointFS[0].x,pointFS[0].y)
            }
            followPath.cubicTo(pointFS[1].x,pointFS[1].y,pointFS[2].x,pointFS[2].y,pointFS[3].x,pointFS[3].y)
        }
        val points = result.gazePoints.filter {
            it.checkValid()
        }
        gazePoints.clear()
        gazePoints.addAll(points)
        gazePath.reset()
        gazePoints.forEachIndexed { index, gazePoint ->
            if (gazePoint.checkValid()){
                val x = gazePoint.x!! * screenWidth
                val y = gazePoint.y!! * screenHeight
                if (index == 0){
                    gazePath.moveTo(x,y)
                }else{
                    gazePath.lineTo(x,y)
                }
            }
        }

        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawPath(followPath, followPathPaint)
        canvas.drawPath(gazePath, gazePathPaint)
        gazePoints.forEachIndexed { index, result ->
            if (result.checkValid()){
                val circleX = result.x!! * screenWidth
                val circleY = result.y!! * screenHeight
                val serialNumber = (index + 1).toString()
                canvas.drawCircle(circleX, circleY, gazePointRadius.toFloat(), gazePointPaint)
                // 计算文本边界框以确定文本尺寸
                textBounds.setEmpty()
                gazeIndexPaint.getTextBounds(serialNumber, 0, serialNumber.length, textBounds)
                canvas.drawText(serialNumber, circleX, circleY - textBounds.exactCenterY(), gazeIndexPaint)
            }
        }
    }

}