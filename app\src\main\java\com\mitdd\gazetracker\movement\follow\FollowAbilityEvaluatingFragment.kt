package com.mitdd.gazetracker.movement.follow

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.PointF
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.movement.follow.bean.FollowAbilityEvaluateResult
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

/**
 * FileName: FollowAbilityEvaluatingFragment
 * Author by lilin,Date on 2024/12/10 20:05
 * PS: Not easy to write code, please indicate.
 * 追随能力评估页面
 */
class FollowAbilityEvaluatingFragment : BaseCommonFragment(){

    companion object{
        private val TAG = FollowAbilityEvaluatingFragment::class.java.simpleName

        fun newInstance(): FollowAbilityEvaluatingFragment {
            return FollowAbilityEvaluatingFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_follow_ability_evaluating
    }

    private val tvTime by id<TextView>(R.id.tv_time)
    private val evaluatingView by id<FollowAbilityEvaluatingView>(R.id.evaluating_view)
    private val clCountDown by id<ConstraintLayout>(R.id.cl_count_down)
    private val tvCountDown by id<TextView>(R.id.tv_count_down)

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    //是否正在评估
    private val isEvaluating = AtomicBoolean(false)
    //追随路线起点
    private var startPoint = PointF()
    private var controlPoint1 = PointF()
    private var controlPoint2 = PointF()
    //追随路线终点
    private var endPoint = PointF()
    //追随路径贝塞尔点列表
    private val followPathPoints = mutableListOf<List<PointF>>()
    private val rangeX1 = IntArray(2)
    private val rangeX2 = IntArray(2)
    private val rangeX3 = IntArray(2)
    private val rangeX4 = IntArray(2)
    private val rangeY = IntArray(2)

    /**
     * 追随次数，每完成一次增加1
     * 奇数从左侧向右，偶数从右向左。0表示未开始，1代表是第一次，第一次从左向右
     */
    private var followNumber = AtomicInteger(0)

    private var mGazeTrajectory:GazeTrajectory? = null

    override fun initParam() {
        super.initParam()
        rangeX1.apply {
            this[0] = 80.dp2px(mActivity)
            this[1] = 160.dp2px(mActivity)
        }
        rangeX2.apply {
            this[0] = 320.dp2px(mActivity)
            this[1] = 420.dp2px(mActivity)
        }
        rangeX3.apply {
            this[0] = 560.dp2px(mActivity)
            this[1] = 640.dp2px(mActivity)
        }
        rangeX4.apply {
            this[0] = 800.dp2px(mActivity)
            this[1] = 920.dp2px(mActivity)
        }
        rangeY.apply {
            this[0] = 100.dp2px(mActivity)
            this[1] = 440.dp2px(mActivity)
        }
    }

    override fun initView() {
        super.initView()

        startPromptCountdown()
    }

    override fun initData() {
        super.initData()
    }

    /**
     * 开始评估
     */
    private fun startEvaluating(){
        clCountDown.isVisible = false
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_FOLLOW
            }
        )
    }

    /**
     * 停止评估
     */
    private fun stopEvaluating(){
        isEvaluating.set(false)
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_GET_GAZE_TRAJECTORY
        })
    }

    /**
     * 评估
     */
    private fun evaluating(){
        lifecycleScope.launch {
            try {
                while (isEvaluating.get()){
                    val number = followNumber.incrementAndGet()
                    startPoint.set(
                        if (number == 1) {
                            generateRandomPoint(rangeX1[0], rangeX1[1], rangeY[0], rangeY[1])
                        } else {
                            endPoint
                        }
                    )
                    if (number %2 == 1){
                        endPoint.set(generateRandomPoint(rangeX4[0], rangeX4[1],rangeY[0],rangeY[1]))
                        //控制点1
                        controlPoint1.set(generateRandomPoint(rangeX2[0], rangeX2[1],rangeY[0],rangeY[1]))
                        //控制点2
                        controlPoint2.set(generateRandomPoint(rangeX3[0], rangeX3[1],rangeY[0],rangeY[1]))
                    }else{
                        endPoint.set(generateRandomPoint(rangeX1[0], rangeX1[1],rangeY[0],rangeY[1]))
                        //控制点1
                        controlPoint1.set(generateRandomPoint(rangeX3[0], rangeX3[1],rangeY[0],rangeY[1]))
                        //控制点2
                        controlPoint2.set(generateRandomPoint(rangeX2[0], rangeX2[1],rangeY[0],rangeY[1]))
                    }
                    followPathPoints.add(listOf(PointF(startPoint), PointF(controlPoint1), PointF(controlPoint2),PointF(endPoint)))
                    evaluatingView.startEvaluating(startPoint,controlPoint1,controlPoint2,endPoint)
                }
            } catch (e:Exception){
                if(BuildConfig.DEBUG){
                    e.printStackTrace()
                }
            } finally {
            }
        }
    }

    /**
     * 提示倒计时
     */
    private fun startPromptCountdown(){
        countdown(3000,1000,
            onTick = {
                tvCountDown.text = (it / 1000).toString()
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCompletion th = $th")
                if (th == null){
                    startEvaluating()
                }
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCatch th = $th")
            }
        )
    }

    /**
     * 时间倒计时
     */
    private fun startTimeCountdown(){
        countdown(20 * 1000,1000,
            onTick = {
                if (it == 0L){
                    tvTime.text = "00:00"
                }else{
                    tvTime.text = TimeUtils.parseTimeToTimeString(it,"mm:ss")
                }
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCompletion th = $th")
                stopEvaluating()
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCatch ${th.message}")
            }
        )
    }

    /**
     * 生成随机点
     * @param minX x坐标最小值
     * @param maxX x坐标最大值
     * @param minY y坐标最小值
     * @param maxY y坐标最大值
     */
    private fun generateRandomPoint(minX:Int,maxX:Int,minY:Int,maxY:Int):PointF{
        val x = Random.nextInt(minX, maxX).toFloat()
        val y = Random.nextInt(minY, maxY).toFloat()
        return PointF(x,y)
    }

    private fun parseMessage(msg: Message){
        when(msg.what){
            GazeConstants.MSG_APPLIED_FOLLOW_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_FOLLOW_STATE state = $state")
                if (state){
                    isEvaluating.set(true)
                    startTimeCountdown()
                    evaluating()
                }
            }
            GazeConstants.MSG_GAZE_TRAJECTORY_RESULT ->{
                lifecycleScope.launch {
                    val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)
                    Logger.d(TAG, msg = "MSG_GET_GAZE_TRAJECTORY_CALLBACK = $json")
                    mGazeTrajectory = try {
                        mGson.fromJson(json, GazeTrajectory::class.java)
                    }catch (e:Exception){
                        if (BuildConfig.DEBUG){
                            e.printStackTrace()
                        }
                        null
                    }
                    sendMessageToService(
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_APPLIED_FOLLOW
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_TRACK
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_TURN_OFF_CAMERA
                        }
                    )
                    delay(200)
                    val followAbilityEvaluateResult = FollowAbilityEvaluateResult(followPathPoints, mGazeTrajectory?.gaze?: emptyList())
                    LiveEventBus.get<FollowAbilityEvaluateResult>(FollowAbilityEvaluateResultActivity.INPUT_PARAM_EVALUATE_RESULT).post(followAbilityEvaluateResult)
                    startActivity(FollowAbilityEvaluateResultActivity.createIntent(mActivity))
                    mActivity.finish()
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_APPLIED_FOLLOW
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
            }
        )
        mActivity.unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}