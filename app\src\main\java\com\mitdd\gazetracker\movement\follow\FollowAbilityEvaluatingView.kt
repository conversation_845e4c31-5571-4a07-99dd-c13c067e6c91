package com.mitdd.gazetracker.movement.follow

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.util.AttributeSet
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.anim.CubicBezierPointEvaluator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume

/**
 * FileName: FollowAbilityEvaluatingView
 * Author by lilin,Date on 2024/12/11 10:30
 * PS: Not easy to write code, please indicate.
 * 追随能力评估iew
 */
class FollowAbilityEvaluatingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr){

    //视点画笔
    private val pointPaint = Paint().apply {
        color = Color.parseColor("#000000")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private var mRadius = 25.dp2px(context)
    //视觉点
    private val pointF = PointF()

    private val targetView = ImageView(context)

    init {
        val params = LayoutParams(mRadius * 2,mRadius * 2)
        targetView.setBackgroundResource(R.drawable.common_black_round_bg)
        addView(targetView,params)
        targetView.isVisible = false
    }

    /**
     * 开始评估
     * @param pointF0 起始点
     * @param pointF1 第一个控制点
     * @param pointF2 第二个控制点
     * @param pointF3 结束点
     */
    suspend fun startEvaluating(
        pointF0: PointF, pointF1: PointF, pointF2: PointF, pointF3: PointF
    ): Boolean = withContext(Dispatchers.Main) {
        return@withContext suspendCancellableCoroutine { continuation ->
            val animator = ValueAnimator.ofObject(CubicBezierPointEvaluator(pointF1, pointF2), pointF0, pointF3)
            animator.interpolator = LinearInterpolator()

            // 设置初始值
            targetView.translationX = pointF0.x - mRadius
            targetView.translationY = pointF0.y - mRadius
            targetView.isVisible = true
//            pointF.set(pointF0)
//            invalidate()

            // 添加更新监听器
            animator.addUpdateListener {
                val value = it.animatedValue as PointF
                targetView.translationX = value.x - mRadius
                targetView.translationY = value.y - mRadius
//                pointF.set(value)
//                invalidate()
            }

            // 添加结束监听器
            animator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 动画结束后设置最终值并刷新视图
                    targetView.translationX = pointF3.x - mRadius
                    targetView.translationY = pointF3.y - mRadius
//                    pointF.set(pointF3)
//                    invalidate()
                    // 动画正常完成，返回 true
                    if (!continuation.isCancelled) {
                        continuation.resume(true)
                    }
                }

                override fun onAnimationCancel(animation: Animator) {
                    // 动画被取消，返回 false
                    continuation.resume(false)
                }
            })
            // 如果协程被取消，取消动画
            continuation.invokeOnCancellation {
                animator.cancel()
            }
            // 启动动画
            animator.duration = 3000
            animator.start()
        }
    }


//    override fun onDraw(canvas: Canvas) {
//        super.onDraw(canvas)
//        if (pointF.x > 0 && pointF.y > 0){
//            canvas.drawCircle(pointF.x, pointF.y, mRadius.toFloat(), pointPaint)
//        }
//    }

}