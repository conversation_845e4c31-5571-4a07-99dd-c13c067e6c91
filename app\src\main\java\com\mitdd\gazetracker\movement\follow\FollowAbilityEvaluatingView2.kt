package com.mitdd.gazetracker.movement.follow

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Path
import android.graphics.PathMeasure
import android.util.AttributeSet
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.R

/**
 * FileName: FollowAbilityEvaluatingView2
 * Author by lilin,Date on 2025/5/8 15:14
 * PS: Not easy to write code, please indicate.
 * 九宫格路径追随能力测试View
 */
class FollowAbilityEvaluatingView2 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr){

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)
    //定义路径
    private val path = Path()
    //获取位置：getPosTan 方法会返回路径上某个比例点的坐标（position）和切线方向（tangent）。
    private val pathMeasure = PathMeasure()
    private val position = FloatArray(2)
    private val tangent = FloatArray(2)
    private var currentProgress = 0f
    private var animator: ValueAnimator? = null

    private var mRadius = 25.dp2px(context)

    private val targetView = ImageView(context)

    init {
        path.moveTo(0.5f * screenWidth, 0.5f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.5f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.8f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.8f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.7f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.7f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.6f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.6f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.5f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.5f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.4f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.4f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.3f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.3f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.2f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.2f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.1f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.2f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.2f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.3f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.3f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.4f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.4f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.5f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.5f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.6f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.6f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.7f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.7f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.8f * screenWidth, 0.9f * screenHeight)
        path.lineTo(0.8f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.1f * screenHeight)
        path.lineTo(0.9f * screenWidth, 0.9f * screenHeight)

        pathMeasure.setPath(path, false)

        val params = LayoutParams(mRadius * 2,mRadius * 2)
        targetView.setBackgroundResource(R.drawable.common_black_round_bg)
        addView(targetView,params)
        targetView.isVisible = false
    }

    // 开始检测
    fun startEvaluating(onComplete: () -> Unit) {
        targetView.translationX = 0.5f * screenWidth - mRadius
        targetView.translationY = 0.5f * screenHeight - mRadius
        targetView.isVisible = true
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 60 * 1000
            // 线性插值器，确保匀速
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                currentProgress = animation.animatedValue as Float
                updateViewPosition()
            }
            addListener(object :AnimatorListenerAdapter(){
                override fun onAnimationEnd(animation: Animator) {
                    onComplete.invoke()
                }
            })
            start()
        }
    }

    private fun updateViewPosition() {
        // 获取路径上的当前位置和切线方向
        pathMeasure.getPosTan(pathMeasure.length * currentProgress, position, tangent)

        // 设置 View 的位置（假设 View 的初始位置是 (0,0)）
        targetView.translationX = position[0] - mRadius
        targetView.translationY = position[1] - mRadius

        // 可选：根据路径切线方向旋转 View
//        rotation = atan2(tangent[1].toDouble(), tangent[0].toDouble()).toFloat() * 180 / Math.PI.toFloat()
    }

}