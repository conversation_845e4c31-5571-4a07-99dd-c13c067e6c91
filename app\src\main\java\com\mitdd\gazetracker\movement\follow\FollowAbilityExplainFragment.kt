package com.mitdd.gazetracker.movement.follow

import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.mitdd.gazetracker.R

/**
 * FileName: FollowAbilityEvaluateExplainFragment
 * Author by lilin,Date on 2024/12/10 19:40
 * PS: Not easy to write code, please indicate.
 * 追随能力评估说明页面
 */
class FollowAbilityExplainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = FollowAbilityExplainFragment::class.java.simpleName

        fun newInstance(): FollowAbilityExplainFragment {
            return FollowAbilityExplainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_follow_ability_explain
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val ivDemonstration by id<ImageView>(R.id.iv_demonstration)
    private val tvStartEvaluating by id<TextView>(R.id.tv_start_evaluating)

    override fun initView() {
        super.initView()

        ivBack.setOnSingleClickListener {
            mActivity.finish()
        }
        tvStartEvaluating.setOnSingleClickListener {
            (mActivity as? FollowAbilityEvaluateActivity)?.showFollowAbilityEvaluating()
        }

        Glide.with(this)
            .asGif()
            .load(R.drawable.follow_ability_explain)
            .listener(object : RequestListener<GifDrawable> {
                override fun onLoadFailed(e: GlideException?, model: Any?,
                    target: Target<GifDrawable>?, isFirstResource: Boolean
                ): Boolean {
                    return false
                }

                override fun onResourceReady(resource: GifDrawable?, model: Any?, target: Target<GifDrawable>?,
                    dataSource: DataSource?, isFirstResource: Boolean
                ): Boolean {
                    resource?.setLoopCount(GifDrawable.LOOP_FOREVER)
                    return false
                }

            })
            .into(ivDemonstration)
    }
}