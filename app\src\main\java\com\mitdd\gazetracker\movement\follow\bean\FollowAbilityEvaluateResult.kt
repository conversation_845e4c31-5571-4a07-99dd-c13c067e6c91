package com.mitdd.gazetracker.movement.follow.bean

import android.graphics.PointF
import android.os.Parcelable
import com.mitdd.gazetracker.gaze.bean.GazePoint
import kotlinx.android.parcel.Parcelize

/**
 * FileName: FollowAbilityEvaluateResult
 * Author by lilin,Date on 2024/12/12 14:18
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class FollowAbilityEvaluateResult(
    var followPathPoints:List<List<PointF>>,
    var gazePoints:List<GazePoint>
): Parcelable
