package com.mitdd.gazetracker.movement.gaze

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: GazeStabilityEvaluateActivity
 * Author by lilin,Date on 2024/12/10 15:37
 * PS: Not easy to write code, please indicate.
 * 注视稳定性评估
 */
class GazeStabilityEvaluateActivity : GTBaseActivity() {

    companion object{
        private val TAG = GazeStabilityEvaluateActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, GazeStabilityEvaluateActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_gaze_stability_evaluate)

        showGazeStabilityEvaluateExplain()
    }

    /**
     * 显示注视稳定性说明页面
     */
    private fun showGazeStabilityEvaluateExplain(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_gaze_stability, GazeStabilityExplainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    /**
     * 显示注视稳定性评估页面
     */
    fun showGazeStabilityEvaluating(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_gaze_stability, GazeStabilityEvaluatingFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }



}