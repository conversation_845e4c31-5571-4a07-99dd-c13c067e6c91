package com.mitdd.gazetracker.movement.gaze

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PointF
import android.os.Bundle
import android.widget.Toast
import androidx.core.view.drawToBitmap
import androidx.lifecycle.ViewModelProvider
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.movement.EyeMovementResultActivity

import com.mitdd.gazetracker.movement.patient.EMPatientManager
import com.mitdd.gazetracker.movement.vm.GazeStabilityViewModel
import java.io.File
import java.io.FileOutputStream

/**
 * FileName: GazeStabilityEvaluateResultActivity
 * Author by lilin,Date on 2024/12/10 17:12
 * PS: Not easy to write code, please indicate.
 * 注视稳定性评估结果
 */
class GazeStabilityEvaluateResultActivity : EyeMovementResultActivity(){

    companion object{
        private val TAG = GazeStabilityEvaluateResultActivity::class.java.simpleName
        const val INPUT_PARAM_X = "x"
        const val INPUT_PARAM_Y = "y"
        const val INPUT_PARAM_GAZE_TRAJECTORY = "gazeTrajectory"

        fun createIntent(context: Context,x:Float,y:Float,gazeTrajectory:GazeTrajectory?): Intent {
            val intent = Intent(context, GazeStabilityEvaluateResultActivity::class.java)
            intent.putExtra(INPUT_PARAM_X,x)
            intent.putExtra(INPUT_PARAM_Y,y)
            intent.putExtra(INPUT_PARAM_GAZE_TRAJECTORY,gazeTrajectory)
            return intent
        }
    }

    private val evaluateResultView by id<GazeStabilityEvaluateResultView>(R.id.evaluate_result_view)

    private val starePoint = PointF(0f,0f)

    private var mGazeTrajectory:GazeTrajectory? = null

    private lateinit var gazeStabilityViewModel: GazeStabilityViewModel

    // 标记是否已经上传图片
    private var isImageUploaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_gaze_stability_evaluate_result)

        initParam()
        initView()
        initViewModel()
        initObserver()
        initData()
    }

    private fun initParam(){
        val x = intent.getFloatExtra(INPUT_PARAM_X,0f)
        val y = intent.getFloatExtra(INPUT_PARAM_Y,0f)
        starePoint.set(x,y)
        mGazeTrajectory = intent.getParcelableExtra(INPUT_PARAM_GAZE_TRAJECTORY)
    }

    private fun initView(){
        getTitleView().text = getString(R.string.str_gaze_stability_evaluate_result)
        getEvaluateResultView().apply {
            text = getString(R.string.str_instability)
            setBackgroundResource(R.drawable.common_eb4e89_round_5_bg)
        }
        getPointNumberView().isVisible = false
        getAverageDurationView().isVisible = false
    }

    private fun initViewModel() {
        Logger.d(TAG, msg = "初始化GazeStabilityViewModel")
        gazeStabilityViewModel = ViewModelProvider(this)[GazeStabilityViewModel::class.java]
    }

    private fun initObserver() {
        // 观察图片上传结果
        gazeStabilityViewModel.uploadImageResultLiveData.observe(this) { result ->
            if (result != null) {
                Logger.d(TAG, msg = "图片上传成功，URL: ${result.data?.url}")
                isImageUploaded = true
                // 图片上传成功后，提交数据到服务器
                submitDataToServerWithImage(result.data?.url)
            } else {
                Logger.e(TAG, msg = "图片上传失败")
                // 图片上传失败，仍然提交数据但不包含图片URL
                submitDataToServerWithImage(null)
            }
        }

        // 观察图片上传错误信息
        gazeStabilityViewModel.uploadImageErrorLiveData.observe(this) { errorMessage: String ->
            Toast.makeText(this, "网络错误，请检查网络连接", Toast.LENGTH_LONG).show()
        }

        // 观察提交结果
        gazeStabilityViewModel.submitResultLiveData.observe(this) { result: ApiResponse<Long>? ->
            Logger.d(TAG, msg = "收到提交结果回调")
            if (result != null) {
                // code判断已在ViewModel中处理，这里只处理成功情况
                Logger.d(TAG, msg = "注视稳定性检测结果提交成功 - 记录ID: ${result.data}")
                Toast.makeText(this, "数据提交成功", Toast.LENGTH_SHORT).show()
            }
            // 注意：失败情况和网络错误会通过submitErrorLiveData处理
        }

        // 观察数据提交错误信息（网络错误等）
        gazeStabilityViewModel.submitErrorLiveData.observe(this) { errorMessage: String ->
            Logger.e(TAG, msg = "注视稳定性检测数据提交错误: $errorMessage")
            Toast.makeText(this, "网络错误，请检查网络连接", Toast.LENGTH_LONG).show()
        }
    }

    private fun initData(){
        evaluateResultView.drawResult(starePoint,mGazeTrajectory?.gaze?: emptyList(),25.dp2px(this),12.dp2px(this),37.dp2px(this),4)
        Logger.d(TAG, msg = "gazeTrajectory = $mGazeTrajectory")

        // 等待视图绘制完成后获取截图并上传
        evaluateResultView.post {
            captureAndUploadResultImage()
        }
    }

    override fun save() {
        // 保存图片到相册
        super.save()

        // 如果还没有上传图片，则重新上传
        if (!isImageUploaded) {
            captureAndUploadResultImage()
        }
    }

    /**
     * 获取结果页面截图并上传
     */
    private fun captureAndUploadResultImage() {
        try {
            // 获取结果容器的截图
            val bitmap = resultContainer.drawToBitmap()
            val imageFile = saveBitmapToFile(bitmap)

            // 上传图片
            gazeStabilityViewModel.uploadImage(imageFile)
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取结果截图失败: ${e.message}")
            // 如果截图失败，直接提交数据不包含图片
            submitDataToServerWithImage(null)
        }
    }

    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmapToFile(bitmap: Bitmap): File {
        val fileName = "gaze_stability_result_${System.currentTimeMillis()}.png"
        val file = File(cacheDir, fileName)

        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }

        return file
    }

    /**
     * 提交数据到服务器（包含图片URL）
     */
    private fun submitDataToServerWithImage(imageUrl: String?) {
        val currentPatient = EMPatientManager.getEMPatient()
        if (currentPatient?.id == null) {
            Logger.e(TAG, msg = "当前患者信息为空，无法提交数据")
            Toast.makeText(this, "患者信息缺失，无法提交数据", Toast.LENGTH_SHORT).show()
            return
        }

        val patientId = try {
            currentPatient.id!!.toLong()
        } catch (e: NumberFormatException) {
            Toast.makeText(this, "患者ID格式错误", Toast.LENGTH_SHORT).show()
            return
        }

        val gazePoints = mGazeTrajectory?.gaze ?: emptyList()
        if (gazePoints.isEmpty()) {
            Logger.e(TAG, msg = "视线轨迹数据为空，无法提交")
            Toast.makeText(this, "测试数据为空，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        Logger.d(TAG, msg = "开始提交注视稳定性检测数据")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点: (${starePoint.x}, ${starePoint.y}), 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "图片URL: $imageUrl")

        // 获取实际使用的参数（与绘制时保持一致）
        val targetPointRadius = 25.dp2px(this)
        val gazePointRadius = 12.dp2px(this)
        val loopRadiusIncreases = 37.dp2px(this)
        val loopsCount = 4

        gazeStabilityViewModel.submitGazeStabilityResult(
            patientId = patientId,
            targetX = starePoint.x,
            targetY = starePoint.y,
            gazePoints = gazePoints,
            duration = 30000,
            notes = "注视稳定性测试",
            imageUrl = imageUrl,
            targetPointRadius = targetPointRadius,
            gazePointRadius = gazePointRadius,
            loopRadiusIncreases = loopRadiusIncreases,
            loopsCount = loopsCount
        )
    }

    override fun getSerialNumberType():String {
        return "01"
    }
}