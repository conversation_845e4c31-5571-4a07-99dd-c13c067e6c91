package com.mitdd.gazetracker.movement.gaze

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.gaze.bean.GazePoint

/**
 * FileName: GazeStabilityEvaluateResultView
 * Author by lilin,Date on 2024/12/10 17:48
 * PS: Not easy to write code, please indicate.
 * 注视稳定性评估结果View
 */
class GazeStabilityEvaluateResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)
    //目标视点
    private val targetPoint = PointF()
    //基准点半径
    private var targetPointRadius = 10.dp2px(context)
    //环半径增长值
    private var loopRadiusIncreases = 10.dp2px(context)
    //环数
    private var loops = 4
    //视点列表
    private val gazePoints = mutableListOf<GazePoint>()
    //视点半径
    private var gazePointRadius = 10.dp2px(context)
    //视点路径
    private val gazePath = Path()
    //目标点画笔
    private val targetPointPaint = Paint().apply {
        color = Color.parseColor("#000000")
        style = Paint.Style.FILL
        //抗锯齿
        isAntiAlias = true
    }
    //视点画笔
    private val gazePointPaint = Paint().apply {
        color = Color.parseColor("#F28225")
        style = Paint.Style.FILL
        //抗锯齿
        isAntiAlias = true
    }
    //环画笔
    private val loopPaint = Paint().apply {
        color = Color.parseColor("#A1ADC8")
        style = Paint.Style.STROKE
        //// 定义虚线的模式：实线部分长度、空白部分长度、相位（偏移量）
        pathEffect = DashPathEffect(floatArrayOf(5f, 2f), 0f)
        //宽度0.5dp
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 0.5f, resources.displayMetrics)
        //设置了当两条线段相交或连接时，连接处的样式为圆角连接。
        strokeJoin = Paint.Join.ROUND
        //设置了线段端点的样式为圆形帽。当绘制线条时，线段的开始和结束位置将会是半圆。
        strokeCap = Paint.Cap.ROUND
        //抗锯齿
        isAntiAlias = true
    }
    //视点序号画笔
    private val gazeIndexPaint = Paint().apply {
        color = Color.parseColor("#333333")
        style = Paint.Style.FILL
        isAntiAlias = true
        textSize = 10f
        textAlign = Paint.Align.CENTER
    }
    //视点路径画笔
    private val gazePathPaint = Paint().apply {
        color = Color.parseColor("#F28225")
        style = Paint.Style.STROKE
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 1f, resources.displayMetrics)
        strokeJoin = Paint.Join.ROUND
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }

    /**
     * 绘制结果
     * @param targetPoint 目标视点
     * @param points 视点列表
     * @param targetPointRadius 目标视点半径
     * @param gazePointRadius 视点半径
     * @param radiusIncreases 环半径增长值
     * @param loops 环数
     */
    fun drawResult(targetPoint:PointF,points:List<GazePoint>,targetPointRadius:Int,
                   gazePointRadius:Int,radiusIncreases:Int,loops:Int) {
        this.targetPoint.set(targetPoint)
        this.targetPointRadius = targetPointRadius
        this.gazePointRadius = gazePointRadius
        loopRadiusIncreases = radiusIncreases
        this.loops = loops

        val list = points.filter { it.checkValid() }
        gazePoints.clear()
        gazePoints.addAll(list)
        gazePath.reset()
        gazePoints.forEachIndexed { index, result ->
            val x = result.x!! * screenWidth
            val y = result.y!! * screenHeight
            if (index == 0){
                gazePath.moveTo(x,y)
            }else{
                gazePath.lineTo(x,y)
            }
        }

        invalidate()
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (targetPoint.x > 0 && targetPoint.y > 0){
            for (i in 1 .. loops) {
                canvas.drawCircle(targetPoint.x * screenWidth, targetPoint.y * screenHeight, (targetPointRadius + loopRadiusIncreases * i).toFloat(), loopPaint)
            }
            canvas.drawCircle(targetPoint.x * screenWidth, targetPoint.y * screenHeight, targetPointRadius.toFloat(), targetPointPaint)
            canvas.drawPath(gazePath, gazePathPaint)
            gazePoints.forEachIndexed { index, result ->
                val circleX = result.x!! * screenWidth
                val circleY = result.y!! * screenHeight
                val serialNumber = (index + 1).toString()
                canvas.drawCircle(circleX, circleY, gazePointRadius.toFloat(), gazePointPaint)
                // 计算文本边界框以确定文本尺寸
                val textBounds = Rect()
                gazeIndexPaint.getTextBounds(serialNumber, 0, serialNumber.length, textBounds)
                canvas.drawText(serialNumber, circleX, circleY - textBounds.exactCenterY(), gazeIndexPaint)
            }
        }
    }

}