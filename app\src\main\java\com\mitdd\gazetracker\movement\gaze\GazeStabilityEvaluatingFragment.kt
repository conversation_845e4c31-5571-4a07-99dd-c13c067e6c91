package com.mitdd.gazetracker.movement.gaze

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.PointF
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.airdoc.component.common.utils.TimeUtils
import com.google.gson.Gson
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * FileName: GazeStabilityEvaluatingFragment
 * Author by lilin,Date on 2024/12/10 16:21
 * PS: Not easy to write code, please indicate.
 * 注视稳定性评估页面
 */
class GazeStabilityEvaluatingFragment : BaseCommonFragment() {

    companion object{
        private val TAG = GazeStabilityEvaluatingFragment::class.java.simpleName

        fun newInstance(): GazeStabilityEvaluatingFragment {
            return GazeStabilityEvaluatingFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_gaze_stability_evaluating
    }

    private val clStareRoot by id<ConstraintLayout>(R.id.cl_stare_root)
    private val tvTime by id<TextView>(R.id.tv_time)
    private val viewPoint by id<View>(R.id.view_point)
    private val clCountDown by id<ConstraintLayout>(R.id.cl_count_down)
    private val tvCountDown by id<TextView>(R.id.tv_count_down)

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private val starePoint = PointF(0.5f,0.55f)
    //屏幕宽高
    private var screenWidth = 0
    private var screenHeight = 0

    private var mGazeTrajectory:GazeTrajectory? = null

    override fun initView() {
        super.initView()

        screenWidth = ScreenUtil.getScreenWidth(mActivity)
        screenHeight = ScreenUtil.getScreenHeight(mActivity)
        val pointWidth = 50.dp2px(mActivity)
        val pointHeight = 50.dp2px(mActivity)

        val marginLeft = (starePoint.x * screenWidth - pointWidth / 2).toInt()
        val marginTop = (starePoint.y * screenHeight - pointHeight / 2).toInt()

        val constraintSet = ConstraintSet()
        constraintSet.clone(clStareRoot)
        constraintSet.connect(R.id.view_point, ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT)
        constraintSet.clear(R.id.view_point, ConstraintSet.RIGHT)
        constraintSet.connect(R.id.view_point, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
        constraintSet.setMargin(R.id.view_point, ConstraintSet.LEFT, marginLeft)
        constraintSet.setMargin(R.id.view_point, ConstraintSet.TOP, marginTop)
        constraintSet.applyTo(clStareRoot)

        startPromptCountdown()
    }

    /**
     * 提示倒计时
     */
    private fun startPromptCountdown(){
        countdown(3000,1000,
            onTick = {
                tvCountDown.text = (it / 1000).toString()
            },
            onCompletion = {
                startEvaluating()
            },
            onCatch = {
                startEvaluating()
            }
        )
    }

    /**
     * 开始评估
     */
    private fun startEvaluating(){
        clCountDown.isVisible = false
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_STARE
                data.putFloat(GazeConstants.KEY_X,starePoint.x)
                data.putFloat(GazeConstants.KEY_Y,starePoint.y)
            }
        )
    }

    /**
     * 停止评估
     */
    private fun stopEvaluating(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_GET_GAZE_TRAJECTORY
        })
    }

    /**
     * 时间倒计时
     */
    private fun startTimeCountdown(){
        countdown(10 * 1000,1000,
            onTick = {
                if (it == 0L){
                    tvTime.text = "00:00"
                }else{
                    tvTime.text = TimeUtils.parseTimeToTimeString(it,"mm:ss")
                }
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCompletion ${th?.message}")
                stopEvaluating()
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCatch ${th.message}")
            }
        )
    }

    private fun parseMessage(msg: Message){
        when(msg.what){
            GazeConstants.MSG_GAZE_TRACKING_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_GAZE_TRACKING_STATE state = $state")
            }
            GazeConstants.MSG_APPLIED_STARE_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_STARE_STATE state = $state")
                if (state){
                    startTimeCountdown()
                }
            }
            GazeConstants.MSG_GAZE_TRAJECTORY_RESULT ->{
                lifecycleScope.launch {
                    val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)
                    Logger.d(TAG, msg = "MSG_GET_GAZE_TRAJECTORY_CALLBACK = $json")
                    mGazeTrajectory = try {
                        mGson.fromJson(json, GazeTrajectory::class.java)
                    }catch (e:Exception){
                        if (BuildConfig.DEBUG){
                            e.printStackTrace()
                        }
                        null
                    }
                    sendMessageToService(
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_APPLIED_STARE
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_TRACK
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_TURN_OFF_CAMERA
                        }
                    )
                    delay(200)
                    startActivity(GazeStabilityEvaluateResultActivity.createIntent(mActivity,starePoint.x,starePoint.y,mGazeTrajectory))
                    mActivity.finish()
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_STOP_APPLIED_STARE
        })
        mActivity.unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}