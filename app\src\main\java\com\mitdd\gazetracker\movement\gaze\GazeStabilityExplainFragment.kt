package com.mitdd.gazetracker.movement.gaze

import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.movement.follow.FollowAbilityExplainFragment

/**
 * FileName: GazeStabilityExplainFragment
 * Author by lilin,Date on 2024/12/10 15:50
 * PS: Not easy to write code, please indicate.
 * 注视稳定性评估说明页
 */
class GazeStabilityExplainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = GazeStabilityExplainFragment::class.java.simpleName

        fun newInstance(): GazeStabilityExplainFragment {
            return GazeStabilityExplainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_gaze_stability_explain
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val tvStartEvaluating by id<TextView>(R.id.tv_start_evaluating)

    override fun initView() {
        super.initView()

        ivBack.setOnSingleClickListener {
            mActivity.finish()
        }
        tvStartEvaluating.setOnSingleClickListener {
            (mActivity as? GazeStabilityEvaluateActivity)?.showGazeStabilityEvaluating()
        }
    }
}