package com.mitdd.gazetracker.movement.manager

/**
 * FileName: ImageUploadManager
 * Author by lilin,Date on 2025/6/20
 * PS: Not easy to write code, please indicate.
 * 图片上传管理器
 */
object ImageUploadManager {
    
    private var uploadedImageUrl: String? = null
    
    /**
     * 设置上传的图片URL
     */
    fun setUploadedImageUrl(url: String?) {
        uploadedImageUrl = url
    }
    
    /**
     * 获取上传的图片URL
     */
    fun getUploadedImageUrl(): String? {
        return uploadedImageUrl
    }
    
    /**
     * 清除图片URL
     */
    fun clearUploadedImageUrl() {
        uploadedImageUrl = null
    }
}
