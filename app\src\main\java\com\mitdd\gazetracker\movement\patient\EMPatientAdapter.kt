package com.mitdd.gazetracker.movement.patient

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.mitdd.gazetracker.user.bean.Gender

/**
 * FileName: EMPatientAdapter
 * Author by lilin,Date on 2025/5/19 15:13
 * PS: Not easy to write code, please indicate.
 */
class EMPatientAdapter(private var patients: MutableList<EMPatient>) : RecyclerView.Adapter<EMPatientAdapter.PatientHolder>() {

    private var mListener: ItemClickListener? = null
    private var mSelectPosition = RecyclerView.NO_POSITION

    fun setItemClickListener(listener: ItemClickListener){
        mListener = listener
    }

    fun setSelectPosition(position: Int){
        if (position in patients.indices){
            mSelectPosition = position
            notifyDataSetChanged()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EMPatientAdapter.PatientHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_em_patient_information_layout, parent, false)
        return PatientHolder(view)
    }

    override fun getItemCount(): Int {
        return patients.size
    }

    override fun onBindViewHolder(holder: EMPatientAdapter.PatientHolder, position: Int) {
        if (position in patients.indices){
            holder.bind(patients[position],position)
        }
    }

    inner class PatientHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val clPatientInfo: ConstraintLayout = itemView.findViewById(R.id.cl_patient_info)
        private val tvPatientName: TextView = itemView.findViewById(R.id.tv_patient_name)
        private val tvPatientGender: TextView = itemView.findViewById(R.id.tv_patient_gender)
        private val tvPatientAge: TextView = itemView.findViewById(R.id.tv_patient_age)
        private val tvPatientId: TextView = itemView.findViewById(R.id.tv_patient_id)
        private val tvPatientPhone: TextView = itemView.findViewById(R.id.tv_patient_phone)

        init {
            itemView.setOnSingleClickListener {
                mListener?.onItemClick(bindingAdapterPosition)
            }
        }

        fun bind(patient: EMPatient,position: Int){
            clPatientInfo.isSelected = mSelectPosition == position
            tvPatientName.text = patient.name
            when(patient.gender){
                Gender.MALE.num ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_male)
                }
                Gender.FEMALE.num ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_female)
                }
                else ->{
                    tvPatientGender.text = itemView.context.getString(R.string.str_unknown)
                }
            }
            if ((patient.age ?: 0) > 0){
                tvPatientAge.text = itemView.context.getString(R.string.str_age_int,patient.age)
            }else{
                tvPatientAge.text = itemView.context.getString(R.string.str_unknown)
            }
            tvPatientId.text = itemView.context.getString(
                R.string.str_id_s, patient.id?:itemView.context.getString(
                    R.string.str_no_number))
            patient.phone.isNullOrEmpty().also {
                if (it){
                    tvPatientPhone.text = itemView.context.getString(
                        R.string.str_phone_s, itemView.context.getString(
                            R.string.str_no_phone_number_yet))
                }else{
                    tvPatientPhone.text = itemView.context.getString(R.string.str_phone_s, patient.phone)
                }
            }
        }

    }

    interface ItemClickListener{
        fun onItemClick(position: Int)
    }

}