package com.mitdd.gazetracker.movement.patient

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.medicalhospital.mt.MHospitalMTFragment
import com.mitdd.gazetracker.movement.bean.EMPatient

/**
 * FileName: EMPatientInfoActivity
 * Author by lilin,Date on 2025/5/13 17:01
 * PS: Not easy to write code, please indicate.
 * 眼球运动患者信息Activity
 */
class EMPatientInfoActivity : GTBaseActivity(){

    companion object{
        private val TAG = EMPatientInfoActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, EMPatientInfoActivity::class.java)
        }
    }

    private val llPatientLibrary by id<LinearLayout>(R.id.ll_patient_library)
    private val llNewPatient by id<LinearLayout>(R.id.ll_new_patient)
    private val llBack by id<LinearLayout>(R.id.ll_back)
    private val tvCurrentPatient by id<TextView>(R.id.tv_current_patient)
    private val flPatientLibrary by id<FrameLayout>(R.id.fl_patient_library)

    private val emPatient = EMPatientManager.getEMPatient()?.deepCopy() ?: EMPatient()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_em_patient_info)

        initView()
        initListener()
    }

    private fun initView(){
        showMaskTherapy()
        showPatientLibrary()
        llPatientLibrary.isSelected = false
    }

    private fun initListener(){
        llPatientLibrary.setOnSingleClickListener {
            if (llPatientLibrary.isSelected){
                startWidthAnimation(flPatientLibrary,270.dp2px(this),0,300,object :
                    AnimatorListenerAdapter(){
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        val fragment =
                            supportFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)
                        if (fragment is MHospitalMTFragment){
                            fragment.setAppSpanCount(8)
                        }
                    }
                })
                llPatientLibrary.isSelected = false
            }else{
                startWidthAnimation(flPatientLibrary,0,270.dp2px(this),300,object :
                    AnimatorListenerAdapter(){
                    override fun onAnimationStart(animation: Animator, isReverse: Boolean) {
                        super.onAnimationStart(animation, isReverse)
                        val fragment =
                            supportFragmentManager.findFragmentByTag(MHospitalMTFragment.FRAGMENT_TAG)
                        if (fragment is MHospitalMTFragment){
                            fragment.setAppSpanCount(6)
                        }
                    }
                })
                llPatientLibrary.isSelected = true
            }
        }
        llNewPatient.setOnSingleClickListener {
        }
        llBack.setOnSingleClickListener {
            finish()
        }
    }

    fun showMaskTherapy(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_content_container, EMPatientInfoFragment.newInstance(),
            EMPatientInfoFragment.FRAGMENT_TAG_PATIENT_INFO)
        beginTransaction.commitAllowingStateLoss()
    }

    fun showPatientLibrary(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_patient_library, EMPatientLibraryFragment.newInstance(),
            EMPatientLibraryFragment.FRAGMENT_TAG_PATIENT_LIBRARY)
        beginTransaction.commitAllowingStateLoss()
    }

    /**
     * 使用 ValueAnimator 改变 View 的宽度
     *
     * @param view 要改变宽度的 View
     * @param startWidth 初始宽度
     * @param endWidth 目标宽度
     * @param duration 动画持续时间（毫秒）
     */
    private fun startWidthAnimation(view: View, startWidth: Int, endWidth: Int, duration: Long, listener: Animator.AnimatorListener? = null) {
        val animator = ValueAnimator.ofInt(startWidth, endWidth).apply {
            this.duration = duration

            addUpdateListener { animation ->
                // 获取当前动画值并更新 View 的宽度
                val animatedValue = animation.animatedValue as Int
                val layoutParams = view.layoutParams
                layoutParams.width = animatedValue
                view.layoutParams = layoutParams
            }

            if (listener != null){
                addListener(listener)
            }
        }
        // 启动动画
        animator.start()
    }

}