package com.mitdd.gazetracker.movement.patient

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.BuildConfig
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhospital.mt.SelectionAgeDialog
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.mitdd.gazetracker.movement.enumeration.EMPatientType
import com.mitdd.gazetracker.movement.repository.EMPatientRepository
import com.mitdd.gazetracker.movement.vm.EMPatientViewModel
import com.mitdd.gazetracker.user.bean.Gender
import com.mitdd.gazetracker.utils.CommonUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate

/**
 * FileName: EMPatientInfoFragment
 * Author by lilin,Date on 2025/5/15 16:34
 * PS: Not easy to write code, please indicate.
 */
class EMPatientInfoFragment : BaseCommonFragment() {

    companion object{
        private val TAG = EMPatientInfoFragment::class.java.simpleName

        const val FRAGMENT_TAG_PATIENT_INFO = "PATIENT_INFO"

        fun newInstance(): EMPatientInfoFragment {
            return EMPatientInfoFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_em_patient_info
    }

    private val tvOk by id<TextView>(R.id.tv_ok)
    private val etName by id<EditText>(R.id.et_name)
    private val etInpatientNumber by id<EditText>(R.id.et_inpatient_number)
    private val etCaseCardNumber by id<EditText>(R.id.et_case_card_number)
    private val etBirthday by id<TextView>(R.id.et_birthday)
    private val rgGender by id<RadioGroup>(R.id.rg_gender)
    private val rgPatientType by id<RadioGroup>(R.id.rg_patient_type)
    private val etPhone by id<EditText>(R.id.et_phone)
    private val etDiagnosisInformation by id<EditText>(R.id.et_diagnosis_information)

    private val emPatient = EMPatientManager.getEMPatient()?.deepCopy() ?: EMPatient()

    private lateinit var emPatientViewModel: EMPatientViewModel

    override fun initView() {
        initViewModel()
        initListener()
        etName.setText(emPatient.name)
        etInpatientNumber.setText(emPatient.inpatientNum)
        etCaseCardNumber.setText(emPatient.caseCardNum)
        etBirthday.text = emPatient.birthday
        rgGender.check(if(emPatient.gender == Gender.MALE.num) R.id.rb_male else R.id.rb_female)
        rgPatientType.check(if(emPatient.patientType == EMPatientType.POSITIVE.num) R.id.rb_positive else R.id.rb_negative)
        etPhone.setText(emPatient.phone)
        etDiagnosisInformation.setText(emPatient.diagnosisInformation)
    }

    private fun initViewModel() {
        Logger.d(TAG, msg = "初始化EMPatientViewModel")
        emPatientViewModel = ViewModelProvider(this)[EMPatientViewModel::class.java]

        // 观察添加患者结果
        emPatientViewModel.addPatientLiveData.observe(this) { result ->
            Logger.d(TAG, msg = "收到添加患者结果回调")
            if (result != null) {
                Logger.d(TAG, msg = "添加患者成功 - 新患者ID: ${result.id}")
                Toast.makeText(mActivity, getString(R.string.str_patient_added_successfully), Toast.LENGTH_SHORT).show()

                // 更新患者ID
                emPatient.id = result.id
                Logger.d(TAG, msg = "更新本地患者ID并设置为当前患者")
                EMPatientManager.setEMPatient(emPatient)
                mActivity.finish()
            } else {
                Logger.e(TAG, msg = "添加患者失败 - 返回结果为null")
                Toast.makeText(mActivity, getString(R.string.str_patient_added_failed), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initListener() {
        Logger.d(TAG, msg = "初始化患者信息界面监听器")

        tvOk.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击确定按钮，开始验证患者信息")

            if (emPatient.name.isNullOrEmpty()){
                Logger.e(TAG, msg = "患者姓名验证失败 - 姓名为空")
                Toast.makeText(mActivity,getString(R.string.str_invalid_name_input), Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }

            // 校验手机号格式
            val phone = emPatient.phone
            if (!phone.isNullOrEmpty() && !CommonUtils.isValidChinesePhoneNumber(phone)) {
                Logger.e(TAG, msg = "手机号验证失败 - 格式不正确: $phone")
                Toast.makeText(mActivity, getString(R.string.str_invalid_phone_number_format), Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }

            Logger.d(TAG, msg = "患者信息验证通过，准备提交患者信息")
            Logger.d(TAG, msg = "提交患者信息 - 姓名: ${emPatient.name}, 性别: ${emPatient.gender}, 年龄: ${emPatient.age}")
            Logger.d(TAG, msg = "提交医疗信息 - 住院号: ${emPatient.inpatientNum}, 病例卡号: ${emPatient.caseCardNum}, 患者类型: ${emPatient.patientType}")

            // 发送HTTP请求添加患者
            emPatientViewModel.addEMPatient(emPatient)
        }
        etName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val newName = s.toString()
                Logger.d(TAG, msg = "患者姓名输入变更: $newName")
                emPatient.name = newName
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        etInpatientNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val newInpatientNum = s.toString()
                Logger.d(TAG, msg = "住院号输入变更: $newInpatientNum")
                emPatient.inpatientNum = newInpatientNum
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        etCaseCardNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val newCaseCardNum = s.toString()
                Logger.d(TAG, msg = "病例卡号输入变更: $newCaseCardNum")
                emPatient.caseCardNum = newCaseCardNum
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        etBirthday.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击生日选择")
            val selectionAgeDialog = SelectionAgeDialog(mActivity).apply {
                onOkClick = { year, month, day ->
                    val birthday = "${year}-${if (month < 10) "0$month" else month}-${day}"
                    Logger.d(TAG, msg = "用户选择生日: $birthday")
                    emPatient.birthday = birthday
                    etBirthday.text = birthday
                }
            }
            selectionAgeDialog.show()
            val birthday = emPatient.birthday
            if (!birthday.isNullOrEmpty()){
                Logger.d(TAG, msg = "设置当前生日到选择器: $birthday")
                try {
                    // 解析日期字符串
                    val localDate = LocalDate.parse(birthday)
                    // 获取年、月、日的整数值
                    selectionAgeDialog.setDate(localDate.year,localDate.monthValue,localDate.dayOfMonth)
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "解析生日字符串失败: $birthday, 异常: ${e.message}")
                    if (BuildConfig.DEBUG) {
                        e.printStackTrace()
                    }
                }
            }
        }
        rgGender.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_male ->{
                    Logger.d(TAG, msg = "用户选择性别: 男性")
                    emPatient.gender = Gender.MALE.num
                }
                R.id.rb_female ->{
                    Logger.d(TAG, msg = "用户选择性别: 女性")
                    emPatient.gender = Gender.FEMALE.num
                }
            }
        }
        rgPatientType.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_positive ->{
                    Logger.d(TAG, msg = "用户选择患者类型: 阳性")
                    emPatient.patientType = EMPatientType.POSITIVE.num
                }
                R.id.rb_negative ->{
                    Logger.d(TAG, msg = "用户选择患者类型: 阴性")
                    emPatient.patientType = EMPatientType.NEGATIVE.num
                }
            }
        }
        etPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val newPhone = s.toString().replace(" ", "")
                Logger.d(TAG, msg = "手机号输入变更: $newPhone")
                emPatient.phone = newPhone
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        etDiagnosisInformation.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val newDiagnosis = s.toString()
                Logger.d(TAG, msg = "诊断信息输入变更: $newDiagnosis")
                emPatient.diagnosisInformation = newDiagnosis
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
    }

}