package com.mitdd.gazetracker.movement.patient

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.medicalhospital.mt.PatientItemDecoration
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.scwang.smart.refresh.header.ClassicsHeader
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: EMPatientLibraryFragment
 * Author by lilin,Date on 2025/5/19 15:02
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者库
 */
class EMPatientLibraryFragment : BaseCommonFragment() {

    companion object{
        private val TAG = EMPatientLibraryFragment::class.java.simpleName

        const val FRAGMENT_TAG_PATIENT_LIBRARY = "PATIENT_LIBRARY"

        fun newInstance(): EMPatientLibraryFragment {
            val fragment = EMPatientLibraryFragment()
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_em_patient_library
    }

    private val smartRefresh by id<SmartRefreshLayout>(R.id.smart_refresh)
    private val rvPatient by id<RecyclerView>(R.id.rv_patient)

    private var patients: MutableList<EMPatient> = mutableListOf()
    private val patientAdapter = EMPatientAdapter(patients)
    //是否是刷新患者列表
    private val isRefreshPatient = AtomicBoolean(false)

    override fun initView() {
        super.initView()
        initListener()

        rvPatient.adapter = patientAdapter
        rvPatient.layoutManager = LinearLayoutManager(mActivity, LinearLayoutManager.VERTICAL, false)
        rvPatient.addItemDecoration(PatientItemDecoration(8.dp2px(mActivity)))

        smartRefresh.setRefreshHeader(ClassicsHeader(mActivity))

    }

    override fun initObserver() {
        super.initObserver()

    }

    override fun initData() {
        super.initData()
        loadPatient()
    }

    private fun initListener(){
        patientAdapter.setItemClickListener(object : EMPatientAdapter.ItemClickListener{
            override fun onItemClick(position: Int) {
            }

        })
        smartRefresh.setOnRefreshListener{
            refreshPatient()
        }
    }

    /**
     * 加载患者列表
     */
    private fun loadPatient(){
    }

    /**
     * 下拉刷新患者列表
     */
    private fun refreshPatient(){
        isRefreshPatient.set(true)
    }

}