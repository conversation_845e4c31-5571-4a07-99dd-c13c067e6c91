package com.mitdd.gazetracker.movement.patient

import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.bean.EMPatient
import java.util.concurrent.atomic.AtomicReference

/**
 * FileName: EMPatientManager
 * Author by lilin,Date on 2025/5/14 10:08
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者管理
 */
object EMPatientManager {

    /**
     * 当前检测的患者
     */
    private val emPatient = AtomicReference<EMPatient?>()

    /**
     * 设置当前检测的患者
     */
    fun setEMPatient(emPatient: EMPatient?) {
        EMPatientManager.emPatient.set(emPatient)
    }

    /**
     * 获取当前检测的患者
     */
    fun getEMPatient(): EMPatient? {
        val currentPatient = emPatient.get()
        return currentPatient
    }

}