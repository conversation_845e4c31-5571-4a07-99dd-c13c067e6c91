package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.api.GazeStabilityApiService

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.utils.NetworkUtils
import com.mitdd.gazetracker.net.MovementClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.io.IOException

/**
 * 错误信息数据类
 */
data class ErrorInfo(
    val errorType: ErrorType,
    val message: String,
    val originalException: Exception? = null
)

/**
 * 错误类型枚举
 */
enum class ErrorType {
    NETWORK_ERROR,      // 网络错误
    TIMEOUT_ERROR,      // 超时错误
    SERVER_ERROR,       // 服务器错误
    UNKNOWN_ERROR       // 未知错误
}

class GazeStabilityRepository : BaseRepository() {

    companion object {
        private val TAG = GazeStabilityRepository::class.java.simpleName
    }

    /**
     * 上传图片
     * @param file 图片文件
     * @return Pair<FileUploadResponse?, ErrorInfo?> 成功时返回响应和null，失败时返回null和错误信息
     */
    suspend fun uploadImage(file: MultipartBody.Part): Pair<FileUploadResponse?, ErrorInfo?> {
        return try {
            val response = MovementClient.createService(GazeStabilityApiService::class.java)
                .uploadImage(file)
            Logger.d(TAG, msg = "注视稳定性检测结果图片上传完成")
            Pair(response, null)
        } catch (e: Exception) {
            val networkStatus = NetworkUtils.getCurrentNetworkStatus()
            Logger.e(TAG, msg = "注视稳定性检测结果图片上传失败: ${e.message}\n网络状态: $networkStatus")

            // 统一返回网络错误信息，不显示具体错误码和详细信息
            val errorInfo = ErrorInfo(ErrorType.NETWORK_ERROR, "网络连接失败，请检查网络设置", e)
            Pair(null, errorInfo)
        }
    }

    /**
     * 提交注视稳定性检测结果
     * @param params 提交参数
     * @return Pair<ApiResponse<Long>?, ErrorInfo?> 成功时返回响应和null，失败时返回null和错误信息
     */
    suspend fun submitGazeStabilityResult(params: HashMap<String, Any>): Pair<ApiResponse<Long>?, ErrorInfo?> {
        return try {
            Logger.d(TAG, msg = "开始提交注视稳定性检测结果")
            Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")

            val response = executeHttp {
                val json = gson.toJson(params)
                val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
                MovementClient.createService(GazeStabilityApiService::class.java)
                    .addGazeStability(requestBody)
            }
            Logger.d(TAG, msg = "注视稳定性检测结果提交完成")
            Pair(response, null)
        } catch (e: Exception) {
            val networkStatus = NetworkUtils.getCurrentNetworkStatus()
            Logger.e(TAG, msg = "注视稳定性检测结果提交失败: ${e.message}\n网络状态: $networkStatus")

            // 统一返回网络错误信息，不显示具体错误码和详细信息
            val errorInfo = ErrorInfo(ErrorType.NETWORK_ERROR, "网络连接失败，请检查网络设置", e)
            Pair(null, errorInfo)
        }
    }
}