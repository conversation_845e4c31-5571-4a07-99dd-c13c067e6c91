package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.api.SaccadeAbilityApiService

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.utils.NetworkUtils
import com.mitdd.gazetracker.net.MovementClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.io.IOException

/**
 * FileName: SaccadeAbilityRepository
 * Author by lilin,Date on 2025/6/21 10:30
 * PS: Not easy to write code, please indicate.
 * 扫视能力检测Repository
 */
class SaccadeAbilityRepository : BaseRepository() {

    companion object {
        private val TAG = SaccadeAbilityRepository::class.java.simpleName
    }

    /**
     * 上传图片
     * @param file 图片文件
     * @return Pair<FileUploadResponse?, ErrorInfo?> 成功时返回响应和null，失败时返回null和错误信息
     */
    suspend fun uploadImage(file: MultipartBody.Part): Pair<FileUploadResponse?, ErrorInfo?> {
        Logger.d(TAG, msg = "开始上传扫视能力检测结果图片")
        return try {
            val response = MovementClient.createService(SaccadeAbilityApiService::class.java)
                .uploadImage(file)
            Logger.d(TAG, msg = "扫视能力检测结果图片上传完成")
            Pair(response, null)
        } catch (e: Exception) {
            val networkStatus = NetworkUtils.getCurrentNetworkStatus()
            Logger.e(TAG, msg = "扫视能力检测结果图片上传失败: ${e.message}\n网络状态: $networkStatus")

            // 统一返回网络错误信息，不显示具体错误码和详细信息
            val errorInfo = ErrorInfo(ErrorType.NETWORK_ERROR, "网络连接失败，请检查网络设置", e)
            Pair(null, errorInfo)
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param params 提交参数
     * @return Pair<ApiResponse<Long>?, ErrorInfo?> 成功时返回响应和null，失败时返回null和错误信息
     */
    suspend fun submitSaccadeAbilityResult(params: HashMap<String, Any>): Pair<ApiResponse<Long>?, ErrorInfo?> {
        Logger.d(TAG, msg = "开始提交扫视能力检测结果")
        Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")

        return try {
            val response = executeHttp {
                val json = gson.toJson(params)
                val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())

                Logger.d(TAG, msg = "发送扫视能力检测结果HTTP请求")
                val result = MovementClient.createService(SaccadeAbilityApiService::class.java)
                    .addSaccadeAbility(requestBody)
                Logger.d(TAG, msg = "扫视能力检测结果HTTP请求完成")
                result
            }
            Pair(response, null)
        } catch (e: Exception) {
            val networkStatus = NetworkUtils.getCurrentNetworkStatus()
            Logger.e(TAG, msg = "扫视能力检测结果提交失败: ${e.message}\n网络状态: $networkStatus")

            // 统一返回网络错误信息，不显示具体错误码和详细信息
            val errorInfo = ErrorInfo(ErrorType.NETWORK_ERROR, "网络连接失败，请检查网络设置", e)
            Pair(null, errorInfo)
        }
    }
}
