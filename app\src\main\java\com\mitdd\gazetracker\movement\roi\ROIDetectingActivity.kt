package com.mitdd.gazetracker.movement.roi

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Bitmap
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.movement.roi.ROIDetectionActivity.Companion.OUTPUT_PARAM_PICTURE_BITMAP
import com.mitdd.gazetracker.movement.roi.ROIDetectionResultActivity.Companion.INPUT_PARAM_EVALUATE_RESULT

/**
 * FileName: ROIDetectingActivity
 * Author by lilin,Date on 2025/5/15 11:29
 * PS: Not easy to write code, please indicate.
 * 兴趣区域测试页面
 */
class ROIDetectingActivity : GTBaseActivity() {

    companion object{
        private val TAG = ROIDetectingActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, ROIDetectingActivity::class.java)
        }
    }

    private val ivPicture by id<ImageView>(R.id.iv_picture)
    private val clCountDown by id<ConstraintLayout>(R.id.cl_count_down)
    private val tvCountDown by id<TextView>(R.id.tv_count_down)
    private val tvTime by id<TextView>(R.id.tv_time)

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                sendMessageToService(Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                })
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private var mGazeTrajectory: GazeTrajectory? = null

    private var mPictureBitmap:Bitmap? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_roi_detecting)

        initView()
        initListener()
        initObserver()
    }

    private fun initView() {
        startPromptCountdown()
    }

    private fun initListener() {

    }

    private fun initObserver(){
        LiveEventBus.get<Bitmap?>(OUTPUT_PARAM_PICTURE_BITMAP).observeSticky(this){
            mPictureBitmap = it
            ivPicture.setImageBitmap(it)
        }
    }

    /**
     * 开始评估
     */
    private fun startEvaluating(){
        clCountDown.isVisible = false
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_FOLLOW
            }
        )
    }

    /**
     * 停止评估
     */
    private fun stopEvaluating(){
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_APPLIED_FOLLOW
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
            }
        )
    }

    /**
     * 提示倒计时
     */
    private fun startPromptCountdown(){
        countdown(3000,1000,
            onTick = {
                tvCountDown.text = (it / 1000).toString()
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCompletion th = $th")
                if (th == null){
                    startEvaluating()
                }
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCatch th = $th")
            }
        )
    }

    /**
     * 时间倒计时
     */
    private fun startTimeCountdown(){
        countdown(20 * 1000,1000,
            onTick = {
                if (it == 0L){
                    tvTime.text = "00:00"
                }else{
                    tvTime.text = TimeUtils.parseTimeToTimeString(it,"mm:ss")
                }
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCompletion ${th?.message}")
                sendMessageToService(Message.obtain().apply {
                    what = GazeConstants.MSG_GET_GAZE_TRAJECTORY
                })
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCatch ${th?.message}")
            }
        )
    }

    private fun parseMessage(msg: Message){
        when(msg.what){
            GazeConstants.MSG_APPLIED_FOLLOW_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_FOLLOW_STATE state = $state")
                if (state){
                    startTimeCountdown()
                }
            }
            GazeConstants.MSG_GAZE_TRAJECTORY_RESULT ->{
                val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)
                Logger.d(TAG, msg = "MSG_GET_GAZE_TRAJECTORY_CALLBACK = $json")
                mGazeTrajectory = try {
                    mGson.fromJson(json, GazeTrajectory::class.java)
                }catch (e:Exception){
                    if (BuildConfig.DEBUG){
                        e.printStackTrace()
                    }
                    null
                }

                stopEvaluating()

                LiveEventBus.get<List<GazePoint>>(INPUT_PARAM_EVALUATE_RESULT).post(mGazeTrajectory?.gaze?: emptyList())
                startActivity(ROIDetectionResultActivity.createIntent(this))
                finish()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        stopEvaluating()
        unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}