package com.mitdd.gazetracker.movement.roi

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import com.airdoc.component.common.ktx.dp2px
import com.mitdd.gazetracker.R
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.ktx.setOnSingleClickListener

/**
 * FileName: ROIDetectionMenu
 * Author by lilin,Date on 2025/5/14 19:38
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测菜单
 */
class ROIDetectionMenu(val context: Context) : PopupWindow() {

    private var tvSelectPicture: TextView? = null
    private var tvMarkRoi: TextView? = null
    private var tvStartDetection: TextView? = null

    var onSelectPictureClick:(() -> Unit)? = null
    var onMarkRoiClick:(() -> Unit)? = null
    var onStartDetectionClick:(() -> Unit)? = null

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.layout_roi_detection_menu, null)
        contentView = view

        isFocusable = true
        isTouchable = true
        isOutsideTouchable = true

        initView(view)
        initListener()

    }

    private fun initView(view: View) {
        tvSelectPicture = view.findViewById(R.id.tv_select_picture)
        tvMarkRoi = view.findViewById(R.id.tv_mark_roi)
        tvStartDetection = view.findViewById(R.id.tv_start_detection)

        width = 150.dp2px(context)
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
    }

    private fun initListener() {
        tvSelectPicture?.setOnSingleClickListener {
            onSelectPictureClick?.invoke()
            dismiss()
        }
        tvMarkRoi?.setOnSingleClickListener {
            onMarkRoiClick?.invoke()
            dismiss()
        }
        tvStartDetection?.setOnSingleClickListener {
            onStartDetectionClick?.invoke()
            dismiss()
        }
    }
}