package com.mitdd.gazetracker.movement.roi

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Path
import android.graphics.PathMeasure
import android.graphics.RectF
import android.os.Bundle
import android.widget.ImageView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.drawToBitmap
import androidx.lifecycle.ViewModelProvider
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.EyeMovementResultActivity
import com.mitdd.gazetracker.movement.roi.ROIDetectionActivity.Companion.OUTPUT_PARAM_PICTURE_BITMAP
import com.mitdd.gazetracker.movement.roi.ROIDetectionActivity.Companion.OUTPUT_PARAM_ROI_PATH
import com.mitdd.gazetracker.movement.vm.EMPatientViewModel
import com.mitdd.gazetracker.movement.vm.ROIDetectionViewModel
import com.mitdd.gazetracker.movement.patient.EMPatientManager
import java.io.File
import java.io.FileOutputStream

/**
 * FileName: ROIDetectionResultActivity
 * Author by lilin,Date on 2025/5/15 11:53
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测结果页
 */
class ROIDetectionResultActivity : EyeMovementResultActivity() {

    companion object{
        private val TAG = ROIDetectionResultActivity::class.java.simpleName

        const val INPUT_PARAM_EVALUATE_RESULT = "evaluate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, ROIDetectionResultActivity::class.java)
        }
    }

    private val ivPicture by id<ImageView>(R.id.iv_picture)
    private val roiPathView by id<ROIPathView>(R.id.roi_path_view)
    private val roiDetectionResult by id<ROIDetectionResultView>(R.id.roi_detection_result)

    // ViewModel
    private lateinit var roiDetectionViewModel: ROIDetectionViewModel
    private val gson = Gson()

    // 数据存储
    private var mPictureBitmap: Bitmap? = null
    private var mROIPaths: List<Path>? = null
    private var mGazePoints: List<GazePoint>? = null
    private var isImageUploaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_roi_detection_result)

        initViewModel()
        initView()
        initObserver()
    }

    private fun initViewModel() {
        roiDetectionViewModel = ViewModelProvider(this)[ROIDetectionViewModel::class.java]
    }

    private fun initView(){
        getToolbar().setBackgroundColor(ContextCompat.getColor(this, com.airdoc.component.common.R.color.white_40))
        getTitleView().apply {
            text = getString(R.string.str_roi_detection_result)
            setTextColor(ContextCompat.getColor(this@ROIDetectionResultActivity, com.airdoc.component.common.R.color.color_333333))
        }
        getSerialNumberView().setTextColor(ContextCompat.getColor(this@ROIDetectionResultActivity, com.airdoc.component.common.R.color.color_333333))
        getDateView().setTextColor(ContextCompat.getColor(this@ROIDetectionResultActivity, com.airdoc.component.common.R.color.color_333333))
    }

    private fun initObserver(){
        // 观察图片数据
        LiveEventBus.get<Bitmap?>(OUTPUT_PARAM_PICTURE_BITMAP).observeSticky(this){ bitmap ->
            mPictureBitmap = bitmap
            ivPicture.setImageBitmap(bitmap)
        }

        // 观察ROI路径数据
        LiveEventBus.get<List<Path>>(OUTPUT_PARAM_ROI_PATH).observeSticky(this){ paths ->
            Logger.d(TAG, msg = "OUTPUT_PARAM_ROI_PATH = ${paths.size}")
            mROIPaths = paths
            roiPathView.setPaths(paths)
            roiDetectionResult.setPaths(paths)
        }

        // 观察视线轨迹数据
        LiveEventBus.get<List<GazePoint>>(INPUT_PARAM_EVALUATE_RESULT).observeSticky(this){ gazePoints ->
            mGazePoints = gazePoints
            roiDetectionResult.drawResult(gazePoints)

            // 分析视点在ROI区域内的分布情况
            if (gazePoints.isNotEmpty()) {
                val roiAnalysisResult = roiDetectionResult.analyzeGazePointsInROI(gazePoints)
                Logger.d(TAG, msg = "ROI区域分析结果: $roiAnalysisResult")

                // 打印详细的ROI分析日志
                logROIAnalysisDetails(roiAnalysisResult)
            }

            // 数据准备完成后，等待视图绘制完成后开始上传图片和提交数据
            if (mPictureBitmap != null && mROIPaths != null && gazePoints.isNotEmpty()) {
                // 等待视图绘制完成后获取截图并上传
                roiDetectionResult.post {
                    captureAndUploadResultImage()
                }
            }
        }

        // 观察图片上传结果
        roiDetectionViewModel.uploadImageResultLiveData.observe(this) { result ->
            if (result != null) {
                Logger.d(TAG, msg = "图片上传成功，URL: ${result.data?.url}")
                isImageUploaded = true
                // 图片上传成功后，提交数据到服务器
                submitDataToServerWithImage(result.data?.url)
            } else {
                Logger.e(TAG, msg = "图片上传失败")
                // 图片上传失败，仍然提交数据但不包含图片URL
                submitDataToServerWithImage(null)
            }
        }

        // 观察图片上传错误信息
        roiDetectionViewModel.uploadImageErrorLiveData.observe(this) { errorMessage ->
            Toast.makeText(this, "图片上传失败：$errorMessage", Toast.LENGTH_LONG).show()
        }

        // 观察提交结果
        roiDetectionViewModel.submitResultLiveData.observe(this) { result ->
            Logger.d(TAG, msg = "收到提交结果回调")
            if (result != null) {
                // code判断已在ViewModel中处理，这里只处理成功情况
                Logger.d(TAG, msg = "兴趣区域检测结果提交成功 - 记录ID: ${result.data}")
                Toast.makeText(this, "数据提交成功", Toast.LENGTH_SHORT).show()
            }
            // 注意：失败情况和网络错误会通过submitErrorLiveData处理
        }

        // 观察数据提交错误信息（网络错误等）
        roiDetectionViewModel.submitErrorLiveData.observe(this) { errorMessage ->
            Logger.e(TAG, msg = "兴趣区域检测数据提交错误: $errorMessage")
            Toast.makeText(this, "网络错误，请检查网络连接", Toast.LENGTH_LONG).show()
        }
    }

    override fun save() {
        // 保存图片到相册
        super.save()

        // 如果还没有上传图片，则重新上传
        if (!isImageUploaded) {
            roiDetectionResult.post {
                captureAndUploadResultImage()
            }
        }
    }

    /**
     * 获取结果页面截图并上传
     */
    private fun captureAndUploadResultImage() {
        try {
            Logger.d(TAG, msg = "开始获取兴趣区域检测结果截图")

            // 获取结果容器的截图
            val bitmap = resultContainer.drawToBitmap()
            val imageFile = saveBitmapToFile(bitmap)

            Logger.d(TAG, msg = "结果截图保存成功: ${imageFile.absolutePath}")
            Logger.d(TAG, msg = "图片大小: ${imageFile.length()} bytes")

            // 上传图片
            roiDetectionViewModel.uploadImage(imageFile)
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取结果截图失败: ${e.message}")
            // 如果截图失败，直接提交数据不包含图片
            submitDataToServerWithImage(null)
        }
    }

    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmapToFile(bitmap: Bitmap): File {
        val fileName = "roi_detection_result_${System.currentTimeMillis()}.png"
        val file = File(cacheDir, fileName)

        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }

        return file
    }

    /**
     * 提交数据到服务器（包含图片URL）
     */
    private fun submitDataToServerWithImage(imageUrl: String?) {
        val currentPatient = EMPatientManager.getEMPatient()
        if (currentPatient?.id == null) {
            Logger.e(TAG, msg = "当前患者信息为空，无法提交数据")
            Toast.makeText(this, "患者信息缺失，无法提交数据", Toast.LENGTH_SHORT).show()
            return
        }

        val patientId = try {
            currentPatient.id!!.toLong()
        } catch (e: NumberFormatException) {
            Toast.makeText(this, "患者ID格式错误", Toast.LENGTH_SHORT).show()
            return
        }

        val gazePoints = mGazePoints ?: emptyList()
        if (gazePoints.isEmpty()) {
            Logger.e(TAG, msg = "视线轨迹数据为空，无法提交")
            Toast.makeText(this, "测试数据为空，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        val bitmap = mPictureBitmap
        if (bitmap == null) {
            Logger.e(TAG, msg = "测试图片为空，无法提交")
            Toast.makeText(this, "测试图片缺失，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        val roiPaths = mROIPaths ?: emptyList()
        if (roiPaths.isEmpty()) {
            Logger.e(TAG, msg = "ROI区域数据为空，无法提交")
            Toast.makeText(this, "ROI区域数据缺失，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        Logger.d(TAG, msg = "开始提交兴趣区域检测数据")
        Logger.d(TAG, msg = "患者ID: $patientId, 图像尺寸: ${bitmap.width}x${bitmap.height}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "ROI区域数量: ${roiPaths.size}, 图片URL: $imageUrl")

        // 构建ROI区域定义JSON
        val roiRegions = buildROIRegionsJson(roiPaths, bitmap.width, bitmap.height)

        roiDetectionViewModel.submitROIDetectionResult(
            patientId = patientId,
            imagePath = imageUrl ?: "",
            imageWidth = bitmap.width,
            imageHeight = bitmap.height,
            roiRegions = roiRegions,
            gazePoints = gazePoints,
            duration = 30000,
            notes = "兴趣区域检测测试",
            imageUrl = imageUrl
        )
    }

    /**
     * 构建ROI区域定义JSON
     */
    private fun buildROIRegionsJson(roiPaths: List<Path>, imageWidth: Int, imageHeight: Int): String {
        val roiRegions = roiPaths.mapIndexed { index, path ->
            mapOf(
                "id" to index,
                "name" to "ROI_$index",
                "type" to "path",
                "coordinates" to extractPathCoordinates(path),
                "area" to calculatePathArea(path, imageWidth, imageHeight)
            )
        }
        return gson.toJson(roiRegions)
    }

    /**
     * 打印ROI分析详细日志
     */
    private fun logROIAnalysisDetails(analysisResult: Map<String, Any>) {
        Logger.d(TAG, msg = "=== ROI区域分析详细报告 ===")

        val totalGazePoints = analysisResult["total_gaze_points"] as? Int ?: 0
        val totalROIRegions = analysisResult["total_roi_regions"] as? Int ?: 0
        val outsideROIPoints = analysisResult["outside_roi_points"] as? Int ?: 0
        val outsideROICoverage = analysisResult["outside_roi_coverage"] as? Double ?: 0.0

        Logger.d(TAG, msg = "总视线点数: $totalGazePoints")
        Logger.d(TAG, msg = "ROI区域总数: $totalROIRegions")
        Logger.d(TAG, msg = "ROI区域外的点数: $outsideROIPoints (${String.format("%.2f", outsideROICoverage * 100)}%)")

        // 打印每个ROI区域的详细信息
        for (i in 0 until totalROIRegions) {
            val roiPoints = analysisResult["roi_${i}_points"] as? Int ?: 0
            val roiCoverage = analysisResult["roi_${i}_coverage"] as? Double ?: 0.0
            val roiDwellTime = analysisResult["roi_${i}_dwell_time"] as? Long ?: 0L

            Logger.d(TAG, msg = "ROI区域 $i:")
            Logger.d(TAG, msg = "  - 视线点数: $roiPoints")
            Logger.d(TAG, msg = "  - 覆盖率: ${String.format("%.2f", roiCoverage * 100)}%")
            Logger.d(TAG, msg = "  - 停留时间: ${roiDwellTime}ms")
        }

        Logger.d(TAG, msg = "=== ROI区域分析报告结束 ===")
    }

    /**
     * 提取路径坐标
     */
    private fun extractPathCoordinates(path: Path): List<Map<String, Float>> {
        val coordinates = mutableListOf<Map<String, Float>>()

        try {
            // 使用PathMeasure来获取路径上的点
            val pathMeasure = PathMeasure(path, false)
            val pathLength = pathMeasure.length

            if (pathLength > 0) {
                // 每隔一定距离采样一个点
                val sampleDistance = maxOf(10f, pathLength / 50) // 最多采样50个点
                var distance = 0f
                val pos = FloatArray(2)

                while (distance <= pathLength) {
                    if (pathMeasure.getPosTan(distance, pos, null)) {
                        coordinates.add(mapOf(
                            "x" to pos[0],
                            "y" to pos[1]
                        ))
                    }
                    distance += sampleDistance
                }

                // 确保包含终点
                if (distance - sampleDistance < pathLength) {
                    if (pathMeasure.getPosTan(pathLength, pos, null)) {
                        coordinates.add(mapOf(
                            "x" to pos[0],
                            "y" to pos[1]
                        ))
                    }
                }
            }

            Logger.d(TAG, msg = "提取路径坐标成功，共${coordinates.size}个点")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "提取路径坐标失败: ${e.message}")
        }

        return coordinates
    }

    /**
     * 计算路径面积
     */
    private fun calculatePathArea(path: Path, imageWidth: Int, imageHeight: Int): Double {
        try {
            // 获取路径的边界矩形
            val bounds = RectF()
            path.computeBounds(bounds, true)

            // 使用边界矩形估算面积（像素）
            val areaPixels = (bounds.width() * bounds.height()).toDouble()

            // 转换为相对面积（相对于整个图像的比例）
            val totalImageArea = imageWidth * imageHeight
            val relativeArea = areaPixels / totalImageArea

            Logger.d(TAG, msg = "计算路径面积: ${areaPixels}像素, 相对面积: ${relativeArea}")

            return areaPixels
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算路径面积失败: ${e.message}")
            return 0.0
        }
    }

    override fun getSerialNumberType(): String {
        return "04"
    }
}