package com.mitdd.gazetracker.movement.roi

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Region
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import androidx.core.graphics.toColorInt
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.gaze.bean.GazePoint

/**
 * FileName: ROIDetectionResultView
 * Author by lilin,Date on 2025/5/15 13:42
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测结果界面
 */
class ROIDetectionResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    private val TAG = ROIDetectionResultView ::class.java.simpleName

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)

    //兴趣区域路径
    private val roiPaths = mutableListOf<Path>()
    //视点半径
    private var gazePointRadius = 8.dp2px(context)
    //视点列表
    private val gazePoints = mutableListOf<GazePoint>()
    //视点路径
    private val gazePath = Path()
    //视点画笔
    private val gazePointPaint = Paint().apply {
        color = "#F28225".toColorInt()
        style = Paint.Style.FILL
        //抗锯齿
        isAntiAlias = true
    }
    //视点序号画笔
    private val gazeIndexPaint = Paint().apply {
        color = "#333333".toColorInt()
        style = Paint.Style.FILL
        isAntiAlias = true
        textSize = 10f
        textAlign = Paint.Align.CENTER
    }
    //视点路径画笔
    private val gazePathPaint = Paint().apply {
        color = "#F28225".toColorInt()
        style = Paint.Style.STROKE
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 1f, resources.displayMetrics)
        strokeJoin = Paint.Join.ROUND
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }
    // 复用 Rect 对象
    private val textBounds = Rect()
    //检测点是否在roiPaths范围内
    private val region = Region()

    /**
     * 绘制评估结果
     */
    fun drawResult(pointList:List<GazePoint>){
        val points = pointList.filter {
            it.checkValid()
        }
        gazePoints.clear()
        gazePoints.addAll(points)
        gazePath.reset()
        gazePoints.forEachIndexed { index, gazePoint ->
            if (gazePoint.checkValid()){
                val x = gazePoint.x!! * screenWidth
                val y = gazePoint.y!! * screenHeight
                if (index == 0){
                    gazePath.moveTo(x,y)
                }else{
                    gazePath.lineTo(x,y)
                }
            }
        }

        invalidate()
    }

    //设置兴趣区域路径
    fun setPaths(paths:List<Path>){
        this.roiPaths.clear()
        this.roiPaths.addAll(paths)
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawPath(gazePath, gazePathPaint)
        gazePoints.forEachIndexed { index, result ->
            if (result.checkValid()){
                val circleX = result.x!! * screenWidth
                val circleY = result.y!! * screenHeight
                val serialNumber = (index + 1).toString()
                canvas.drawCircle(circleX, circleY, gazePointRadius.toFloat(), gazePointPaint)
                // 计算文本边界框以确定文本尺寸
                textBounds.setEmpty()
                gazeIndexPaint.getTextBounds(serialNumber, 0, serialNumber.length, textBounds)
                canvas.drawText(serialNumber, circleX, circleY - textBounds.exactCenterY(), gazeIndexPaint)
            }
        }
    }

    fun isPointInPath(path: Path, x: Float, y: Float): Boolean {
        // 计算 Path 的边界
        val bounds = RectF()
        path.computeBounds(bounds, true)

        // 将 Path 转换为 Region
        return if (region.setPath(path, Region(bounds.left.toInt(), bounds.top.toInt(), bounds.right.toInt(), bounds.bottom.toInt()))) {
            // 检查点是否在 Region 内（坐标转为整数）
            region.contains(x.toInt(), y.toInt())
        } else {
            false
        }
    }

    /**
     * 分析视点在ROI区域内的分布情况
     * @param gazePoints 视线轨迹点列表
     * @return ROI区域统计数据
     */
    fun analyzeGazePointsInROI(gazePoints: List<GazePoint>): Map<String, Any> {
        val roiStats = mutableMapOf<Int, MutableList<GazePoint>>()
        val outsideROIPoints = mutableListOf<GazePoint>()

        // 遍历每个视线点，判断是否在ROI区域内
        gazePoints.forEach { gazePoint ->
            if (gazePoint.checkValid()) {
                val x = gazePoint.x!! * screenWidth
                val y = gazePoint.y!! * screenHeight

                var foundInROI = false

                // 检查每个ROI区域
                roiPaths.forEachIndexed { roiIndex, roiPath ->
                    if (isPointInPath(roiPath, x, y)) {
                        // 视点在当前ROI区域内
                        if (!roiStats.containsKey(roiIndex)) {
                            roiStats[roiIndex] = mutableListOf()
                        }
                        roiStats[roiIndex]?.add(gazePoint)
                        foundInROI = true
                        return@forEachIndexed // 找到第一个包含该点的ROI区域后退出
                    }
                }

                // 如果不在任何ROI区域内
                if (!foundInROI) {
                    outsideROIPoints.add(gazePoint)
                }
            }
        }

        // 计算统计数据
        val totalPoints = gazePoints.size
        val roiCoverageData = mutableMapOf<String, Any>()

        roiStats.forEach { (roiIndex, points) ->
            val coverage = if (totalPoints > 0) points.size.toDouble() / totalPoints else 0.0
            val dwellTime = points.sumOf { it.duration?.toLong() ?: 100L }

            roiCoverageData["roi_${roiIndex}_points"] = points.size
            roiCoverageData["roi_${roiIndex}_coverage"] = coverage
            roiCoverageData["roi_${roiIndex}_dwell_time"] = dwellTime
        }

        roiCoverageData["outside_roi_points"] = outsideROIPoints.size
        roiCoverageData["outside_roi_coverage"] = if (totalPoints > 0) outsideROIPoints.size.toDouble() / totalPoints else 0.0
        roiCoverageData["total_roi_regions"] = roiPaths.size
        roiCoverageData["total_gaze_points"] = totalPoints

        return roiCoverageData
    }

}