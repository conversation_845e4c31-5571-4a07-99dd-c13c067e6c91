package com.mitdd.gazetracker.movement.roi

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View

/**
 * FileName: ROIPathView
 * Author by lilin,Date on 2025/5/14 17:31
 * PS: Not easy to write code, please indicate.
 * 兴趣区域路径
 */
class ROIPathView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    // 存储所有绘制路径的列表
    private val paths = mutableListOf<Path>()
    // 当前正在绘制的路径
    private var currentPath = Path()
    // 绘制画笔
    private val paint = Paint().apply {
        color = Color.BLACK      // 默认颜色
        style = Paint.Style.STROKE // 描边样式
        strokeWidth = 8f         // 线条宽度
        isAntiAlias = true       // 抗锯齿
        strokeJoin = Paint.Join.ROUND // 连接处圆角
        strokeCap = Paint.Cap.ROUND   // 笔触圆角
    }

    private var isMarking = false

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制所有保存的路径
        paths.forEach { canvas.drawPath(it, paint) }
        // 绘制当前正在画的路径
        canvas.drawPath(currentPath, paint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (isMarking){
            val x = event.x
            val y = event.y
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 开始新路径
                    currentPath = Path()
                    currentPath.moveTo(x, y)
                    paths.add(currentPath)
                }
                MotionEvent.ACTION_MOVE -> {
                    // 连接路径点
                    currentPath.lineTo(x, y)
                    // 请求重绘
                    invalidate()
                }
                MotionEvent.ACTION_UP -> {
                    // 手指抬起时不需要特殊处理
                    currentPath.lineTo(x, y)
                    currentPath.close()
                    invalidate()
                }
            }
            return true
        }
        return super.onTouchEvent(event)
    }

    fun setMarking(isMarking: Boolean){
        this.isMarking = isMarking
    }

    //设置兴趣区域路径
    fun setPaths(paths:List<Path>){
        this.paths.clear()
        this.paths.addAll(paths)
        invalidate()
    }

    //获取所有绘制路径的列表
    fun getPaths():List<Path>{
        return paths
    }

    // 清空画布的方法
    fun clearCanvas() {
        paths.clear()
        currentPath.reset()
        invalidate()
    }

}