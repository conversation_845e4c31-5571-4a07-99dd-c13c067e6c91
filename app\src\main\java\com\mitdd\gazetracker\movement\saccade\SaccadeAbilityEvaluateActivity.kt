package com.mitdd.gazetracker.movement.saccade

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: SaccadeAbilityEvaluateActivity
 * Author by lilin,Date on 2024/12/11 13:59
 * PS: Not easy to write code, please indicate.
 * 扫视能力评估
 */
class SaccadeAbilityEvaluateActivity : GTBaseActivity() {

    companion object{
        private val TAG = SaccadeAbilityEvaluateActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, SaccadeAbilityEvaluateActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_saccade_ability_evaluate)

        showSaccadeAbilityEvaluateExplain()

    }

    /**
     * 显示扫视能力评估说明页面
     */
    private fun showSaccadeAbilityEvaluateExplain(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_saccade_ability, SaccadeAbilityExplainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    /**
     * 显示扫视能力评估页面
     */
    fun showSaccadeAbilityEvaluating(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.root_saccade_ability, SaccadeAbilityEvaluatingFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

}