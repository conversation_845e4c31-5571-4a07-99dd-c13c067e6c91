package com.mitdd.gazetracker.movement.saccade

import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.mitdd.gazetracker.R

/**
 * FileName: SaccadeAbilityExplainFragment
 * Author by lilin,Date on 2024/12/11 14:03
 * PS: Not easy to write code, please indicate.
 * 扫视能力评估说明页面
 */
class SaccadeAbilityExplainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = SaccadeAbilityExplainFragment::class.java.simpleName

        fun newInstance(): SaccadeAbilityExplainFragment {
            return SaccadeAbilityExplainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_saccade_ability_explain
    }

    private val ivBack by id<ImageView>(R.id.iv_back)
    private val ivDemonstration by id<ImageView>(R.id.iv_demonstration)
    private val tvStartEvaluating by id<TextView>(R.id.tv_start_evaluating)

    override fun initView() {
        super.initView()

        ivBack.setOnSingleClickListener {
            mActivity.finish()
        }
        tvStartEvaluating.setOnSingleClickListener {
            (mActivity as? SaccadeAbilityEvaluateActivity)?.showSaccadeAbilityEvaluating()
        }

        Glide.with(this)
            .asGif()
            .load(R.drawable.saccade_ability_explain)
            .listener(object : RequestListener<GifDrawable> {
                override fun onLoadFailed(
                    e: GlideException?, model: Any?,
                    target: Target<GifDrawable>?, isFirstResource: Boolean
                ): Boolean {
                    return false
                }

                override fun onResourceReady(
                    resource: GifDrawable?, model: Any?, target: Target<GifDrawable>?,
                    dataSource: DataSource?, isFirstResource: Boolean
                ): Boolean {
                    resource?.setLoopCount(GifDrawable.LOOP_FOREVER)
                    return false
                }

            })
            .into(ivDemonstration)
    }
}