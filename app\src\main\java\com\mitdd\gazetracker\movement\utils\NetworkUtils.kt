package com.mitdd.gazetracker.movement.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.network.NetworkManager
import java.net.InetAddress
import java.net.UnknownHostException

/**
 * FileName: NetworkUtils
 * Author by AI Assistant, Date on 2025/7/8
 * PS: Not easy to write code, please indicate.
 * 网络状态检查工具类
 */
object NetworkUtils {

    private const val TAG = "NetworkUtils"
    
    /**
     * 检查网络连接状态
     * @param context 上下文
     * @return 网络状态描述
     */
    fun getNetworkStatus(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            if (network == null) {
                "网络未连接"
            } else {
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                when {
                    networkCapabilities == null -> "网络状态未知"
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi已连接"
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动网络已连接"
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网已连接"
                    else -> "网络已连接(类型未知)"
                }
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            when {
                networkInfo == null -> "网络未连接"
                !networkInfo.isConnected -> "网络已断开"
                networkInfo.type == ConnectivityManager.TYPE_WIFI -> "WiFi已连接"
                networkInfo.type == ConnectivityManager.TYPE_MOBILE -> "移动网络已连接"
                networkInfo.type == ConnectivityManager.TYPE_ETHERNET -> "以太网已连接"
                else -> "网络已连接(类型未知)"
            }
        }
    }
    
    /**
     * 检查是否有网络连接
     * @param context 上下文
     * @return true表示有网络连接，false表示无网络连接
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 检查服务器连通性
     * @param host 服务器地址
     * @param timeout 超时时间(毫秒)
     * @return true表示服务器可达，false表示服务器不可达
     */
    fun isServerReachable(host: String, timeout: Int = 5000): Boolean {
        return try {
            val address = InetAddress.getByName(host)
            address.isReachable(timeout)
        } catch (e: UnknownHostException) {
            Logger.e(TAG, msg = "服务器地址解析失败: $host, 错误: ${e.message}")
            false
        } catch (e: Exception) {
            Logger.e(TAG, msg = "服务器连通性检查失败: $host, 错误: ${e.message}")
            false
        }
    }
    
    /**
     * 获取详细的网络诊断信息
     * @param context 上下文
     * @param serverHost 服务器地址(可选)
     * @return 网络诊断信息
     */
    fun getNetworkDiagnostics(context: Context, serverHost: String? = null): String {
        val networkStatus = getNetworkStatus(context)
        val isAvailable = isNetworkAvailable(context)
        
        val diagnostics = StringBuilder()
        diagnostics.append("网络状态: $networkStatus")
        diagnostics.append("\n网络可用: ${if (isAvailable) "是" else "否"}")
        
        if (serverHost != null && isAvailable) {
            val isReachable = isServerReachable(serverHost)
            diagnostics.append("\n服务器($serverHost)可达: ${if (isReachable) "是" else "否"}")
        }
        
        return diagnostics.toString()
    }
    
    /**
     * 生成网络错误的详细描述
     * @param context 上下文
     * @param exception 异常信息
     * @param serverHost 服务器地址(可选)
     * @return 详细的错误描述
     */
    fun generateNetworkErrorDescription(context: Context, exception: Exception, serverHost: String? = null): String {
        val networkDiagnostics = getNetworkDiagnostics(context, serverHost)

        val errorDescription = StringBuilder()
        errorDescription.append("网络请求失败\n")
        errorDescription.append("错误原因: ${exception.message ?: "未知错误"}\n")
        errorDescription.append("网络诊断:\n$networkDiagnostics")

        return errorDescription.toString()
    }

    /**
     * 生成网络错误的详细描述（使用NetworkManager）
     * @param exception 异常信息
     * @param serverHost 服务器地址(可选)
     * @return 详细的错误描述
     */
    fun generateNetworkErrorDescription(exception: Exception, serverHost: String? = null): String {
        return try {
            val networkStatus = getCurrentNetworkStatus()
            val isAvailable = isCurrentNetworkAvailable()

            val diagnostics = StringBuilder()
            diagnostics.append("网络状态: $networkStatus")
            diagnostics.append("\n网络可用: ${if (isAvailable) "是" else "否"}")

            if (serverHost != null && isAvailable) {
                val isReachable = isServerReachable(serverHost)
                diagnostics.append("\n服务器($serverHost)可达: ${if (isReachable) "是" else "否"}")
            }

            val errorDescription = StringBuilder()
            errorDescription.append("网络请求失败\n")
            errorDescription.append("错误原因: ${exception.message ?: "未知错误"}\n")
            errorDescription.append("网络诊断:\n$diagnostics")

            errorDescription.toString()
        } catch (e: Exception) {
            "网络请求失败\n错误原因: ${exception.message ?: "未知错误"}\n网络诊断: 无法获取网络状态"
        }
    }

    /**
     * 获取当前网络状态（使用NetworkManager）
     * @return 网络状态描述
     */
    fun getCurrentNetworkStatus(): String {
        return try {
            val networkType = NetworkManager.getNetworkType()
            val isConnected = NetworkManager.isNetworkConnected()

            when {
                !isConnected -> "网络未连接"
                networkType.name == "TYPE_WIFI" -> "WiFi已连接"
                networkType.name == "TYPE_MOBILE" -> "移动网络已连接"
                networkType.name == "TYPE_ETHERNET" -> "以太网已连接"
                else -> "网络已连接(类型未知)"
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取网络状态失败: ${e.message}")
            "无法获取网络状态"
        }
    }

    /**
     * 检查当前网络是否可用（使用NetworkManager）
     * @return true表示网络可用，false表示网络不可用
     */
    fun isCurrentNetworkAvailable(): Boolean {
        return try {
            NetworkManager.isNetworkConnected()
        } catch (e: Exception) {
            Logger.e(TAG, msg = "检查网络可用性失败: ${e.message}")
            false
        }
    }
}
