package com.mitdd.gazetracker.movement.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.BuildConfig
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.mitdd.gazetracker.movement.bean.EMPatientAdd
import com.mitdd.gazetracker.movement.bean.EMPatientList
import com.mitdd.gazetracker.movement.repository.EMPatientRepository
import com.mitdd.gazetracker.movement.utils.NetworkUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: EMPatientViewModel
 * Author by lilin,Date on 2025/6/19
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者ViewModel
 */
class EMPatientViewModel : ViewModel() {

    companion object {
        private val TAG = EMPatientViewModel::class.java.simpleName
    }

    private val emPatientRepository by lazy { EMPatientRepository() }
    private val gson by lazy { Gson() }

    // 当前患者
    val currentPatientLiveData = MutableLiveData<EMPatient?>()
    
    // 添加患者结果
    val addPatientLiveData = MutableLiveData<EMPatientAdd?>()
    
    // 患者列表
    val patientListLiveData = MutableLiveData<EMPatientList?>()
    
    // 查询患者结果
    val queryPatientLiveData = MutableLiveData<EMPatient?>()
    
    // 修改患者结果
    val modifyPatientLiveData = MutableLiveData<Any?>()
    
    // 删除患者结果
    val deletePatientLiveData = MutableLiveData<Any?>()

    /**
     * 设置当前患者
     */
    fun setCurrentPatient(patient: EMPatient?) {
        Logger.d(TAG, msg = "ViewModel设置当前患者: ${if (patient != null) "患者ID=${patient.id}, 姓名=${patient.name}" else "null"}")
        currentPatientLiveData.postValue(patient)
    }

    /**
     * 添加眼球运动评估患者
     * @param emPatient 眼球运动评估患者信息
     */
    fun addEMPatient(emPatient: EMPatient) {
        Logger.d(TAG, msg = "开始添加眼球运动评估患者")
        Logger.d(TAG, msg = "患者基本信息 - 姓名: ${emPatient.name}, 性别: ${emPatient.gender}, 年龄: ${emPatient.age}")
        Logger.d(TAG, msg = "患者医疗信息 - 住院号: ${emPatient.inpatientNum}, 病例卡号: ${emPatient.caseCardNum}, 患者类型: ${emPatient.patientType}")
        Logger.d(TAG, msg = "患者联系信息 - 电话: ${emPatient.phone}, 生日: ${emPatient.birthday}")
        Logger.d(TAG, msg = "诊断信息: ${emPatient.diagnosisInformation}")

        viewModelScope.launch {
            MutableStateFlow(emPatientRepository.addEMPatient(emPatient)).collectResponse {
                onSuccess = { result, _, _ ->
                    Logger.d(TAG, msg = "添加眼球运动评估患者成功")
                    Logger.d(TAG, msg = "新患者ID: \${result?.id}")
                    result?.let { Logger.json(TAG, json = gson.toJson(it)) }
                    addPatientLiveData.postValue(result)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "添加眼球运动评估患者失败 - 返回数据为空")
                    addPatientLiveData.postValue(null)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "添加眼球运动评估患者失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                    addPatientLiveData.postValue(null)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "添加眼球运动评估患者异常: \${throwable?.message}") // Modified: Added ?. 
                    if (BuildConfig.DEBUG) {
                        throwable?.printStackTrace() // Modified: Added ?. 
                    }
                    addPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 获取眼球运动评估患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    fun getEMPatientList(
        page: Int,
        size: Int = 10,
        sort: String? = null,
        gender: Int? = null,
        keywords: String? = null
    ) {
        Logger.d(TAG, msg = "获取眼球运动评估患者列表")

        viewModelScope.launch {
            MutableStateFlow(
                emPatientRepository.getEMPatientList(
                    page,
                    size,
                    sort ?: "createTime,desc",
                    gender,
                    keywords?.ifEmpty { null }
                )
            ).collectResponse {
                onSuccess = { result, _, _ ->
                    Logger.d(TAG, msg = "获取眼球运动评估患者列表成功")
                    Logger.d(TAG, msg = "患者列表信息 - 总数: \${result?.total}, 当前页: \${result?.current}, 每页大小: \${result?.size}")
                    Logger.d(TAG, msg = "本页患者数量: \${result?.records?.size ?: 0}")
                    patientListLiveData.postValue(result)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "获取眼球运动评估患者列表失败 - 返回数据为空")
                    patientListLiveData.postValue(null)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "获取眼球运动评估患者列表失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                    patientListLiveData.postValue(null)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "获取眼球运动评估患者列表异常: \${throwable?.message}")
                    if (BuildConfig.DEBUG) {
                        throwable?.printStackTrace()
                    }
                    patientListLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 查询眼球运动评估患者信息
     * @param id 患者ID,示例值(1)
     */
    fun queryEMPatient(id: String) {
        Logger.d(TAG, msg = "查询眼球运动评估患者信息 - 患者ID: $id")

        viewModelScope.launch {
            MutableStateFlow(emPatientRepository.queryEMPatient(id)).collectResponse {
                onSuccess = { result, _, _ ->
                    Logger.d(TAG, msg = "查询眼球运动评估患者信息成功")
                    Logger.d(TAG, msg = "患者信息 - ID: \${result?.id}, 姓名: \${result?.name}, 性别: \${result?.gender}, 年龄: \${result?.age}")
                    result?.let { Logger.json(TAG, json = gson.toJson(it)) }
                    queryPatientLiveData.postValue(result)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "查询眼球运动评估患者信息失败 - 患者ID: $id, 返回数据为空")
                    queryPatientLiveData.postValue(null)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "查询眼球运动评估患者信息失败 - 患者ID: $id, errorCode: $errorCode, errorMsg: $errorMsg")
                    queryPatientLiveData.postValue(null)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "查询眼球运动评估患者信息异常 - 患者ID: $id, 异常: \${throwable?.message}") // Modified: Added ?. 
                    if (BuildConfig.DEBUG) {
                        throwable?.printStackTrace() // Modified: Added ?. 
                    }
                    queryPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 修改眼球运动评估患者
     * @param id 患者ID,示例值(1)
     * @param emPatient 眼球运动评估患者信息
     */
    fun modifyEMPatient(id: String, emPatient: EMPatient) {
        Logger.d(TAG, msg = "修改眼球运动评估患者 - 患者ID: $id")
        Logger.d(TAG, msg = "修改后患者信息 - 姓名: \${emPatient.name}, 性别: \${emPatient.gender}, 年龄: \${emPatient.age}")
        Logger.d(TAG, msg = "修改后医疗信息 - 住院号: \${emPatient.inpatientNum}, 病例卡号: \${emPatient.caseCardNum}, 患者类型: \${emPatient.patientType}")

        viewModelScope.launch {
            MutableStateFlow(emPatientRepository.modifyEMPatient(id, emPatient)).collectResponse {
                onSuccess = { result, _, _ ->
                    Logger.d(TAG, msg = "修改眼球运动评估患者成功 - 患者ID: $id")
                    result?.let { Logger.json(TAG, json = gson.toJson(it)) }
                    modifyPatientLiveData.postValue(result)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "修改眼球运动评估患者失败 - 患者ID: $id, 返回数据为空")
                    modifyPatientLiveData.postValue(null)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "修改眼球运动评估患者失败 - 患者ID: $id, errorCode: $errorCode, errorMsg: $errorMsg")
                    modifyPatientLiveData.postValue(null)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "修改眼球运动评估患者异常 - 患者ID: $id, 异常: \${throwable?.message}") // Modified: Added ?. 
                    if (BuildConfig.DEBUG) {
                        throwable?.printStackTrace() // Modified: Added ?. 
                    }
                    modifyPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 删除眼球运动评估患者
     * @param id 患者ID,示例值(1)
     */
    fun deleteEMPatient(id: String) {
        Logger.d(TAG, msg = "删除眼球运动评估患者 - 患者ID: $id")

        viewModelScope.launch {
            MutableStateFlow(emPatientRepository.deleteEMPatient(id)).collectResponse {
                onSuccess = { result, _, _ ->
                    Logger.d(TAG, msg = "删除眼球运动评估患者成功 - 患者ID: $id")
                    result?.let { Logger.json(TAG, json = gson.toJson(it)) }
                    deletePatientLiveData.postValue(result)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "删除眼球运动评估患者失败 - 患者ID: $id, 返回数据为空")
                    deletePatientLiveData.postValue(null)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "删除眼球运动评估患者失败 - 患者ID: $id, errorCode: $errorCode, errorMsg: $errorMsg")
                    deletePatientLiveData.postValue(null)
                }
                onError = { throwable ->
                    Logger.e(TAG, msg = "删除眼球运动评估患者异常 - 患者ID: $id, 异常: \${throwable?.message}") // Modified: Added ?. 
                    if (BuildConfig.DEBUG) {
                        throwable?.printStackTrace() // Modified: Added ?. 
                    }
                    deletePatientLiveData.postValue(null)
                }
            }
        }
    }
}
