package com.mitdd.gazetracker.movement.vm

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.component.common.net.entity.ApiResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.bean.GazePoint

import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.repository.ROIDetectionRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * FileName: ROIDetectionViewModel
 * Author by AI Assistant, Date on 2025/6/24
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测ViewModel
 */
class ROIDetectionViewModel : ViewModel() {

    companion object {
        private val TAG = ROIDetectionViewModel::class.java.simpleName
    }

    private val roiDetectionRepository = ROIDetectionRepository()
    private val gson = Gson()

    /**
     * 提交结果LiveData
     */
    val submitResultLiveData = MutableLiveData<ApiResponse<Long>?>()

    /**
     * 图片上传结果LiveData
     */
    val uploadImageResultLiveData = MutableLiveData<FileUploadResponse?>()

    /**
     * 图片上传错误信息LiveData
     */
    private val _uploadImageErrorLiveData = MutableLiveData<String>()
    val uploadImageErrorLiveData: LiveData<String> = _uploadImageErrorLiveData

    /**
     * 数据提交错误信息LiveData
     */
    private val _submitErrorLiveData = MutableLiveData<String>()
    val submitErrorLiveData: LiveData<String> = _submitErrorLiveData

    /**
     * 上传图片
     * @param imageFile 图片文件
     */
    fun uploadImage(imageFile: File) {
        Logger.d(TAG, msg = "开始上传兴趣区域检测结果图片: ${imageFile.name}")

        viewModelScope.launch {
            // 创建RequestBody
            val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
            val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)

            // 调用API
            val (result, errorInfo) = roiDetectionRepository.uploadImage(body)

            if (result != null) {
                Logger.d(TAG, msg = "图片上传成功")
                Logger.d(TAG, msg = "返回URL: ${result.data?.url}")

                // 检查返回结果是否有效
                if (result.code == 200 && result.data != null) {
                    uploadImageResultLiveData.postValue(result)
                } else {
                    Logger.e(TAG, msg = "图片上传失败 - 返回码: ${result.code}, 消息: ${result.message}")
                    _uploadImageErrorLiveData.postValue("图片上传失败：${result.message ?: "未知错误"}")
                    uploadImageResultLiveData.postValue(null)
                }
            } else {
                Logger.e(TAG, msg = "图片上传失败: ${errorInfo?.message}")
                _uploadImageErrorLiveData.postValue(errorInfo?.message ?: "图片上传失败")
                uploadImageResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 提交兴趣区域检测结果
     * @param patientId 患者ID
     * @param imagePath 测试图像路径
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @param roiRegions 兴趣区域定义
     * @param gazePoints 视线轨迹点列表
     * @param duration 测试持续时间(毫秒)
     * @param notes 测试备注
     * @param imageUrl 图片URL
     */
    fun submitROIDetectionResult(
        patientId: Long,
        imagePath: String,
        imageWidth: Int,
        imageHeight: Int,
        roiRegions: String,
        gazePoints: List<GazePoint>,
        duration: Int = 30000,
        notes: String = "兴趣区域检测测试",
        imageUrl: String? = null
    ) {
        Logger.d(TAG, msg = "开始提交兴趣区域检测结果")
        Logger.d(TAG, msg = "患者ID: $patientId, 图像尺寸: ${imageWidth}x${imageHeight}, 轨迹点数量: ${gazePoints.size}")

        viewModelScope.launch {
            // 构建请求参数
            val params = buildSubmitParams(
                patientId, imagePath, imageWidth, imageHeight, roiRegions,
                gazePoints, duration, notes, imageUrl
            )
            Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")

            // 调用API
            val (result, errorInfo) = roiDetectionRepository.submitROIDetectionResult(params)

            // 在ViewModel中判断响应code
            if (result != null) {
                if (result.code == 200) {
                    Logger.d(TAG, msg = "兴趣区域检测结果提交成功 - 记录ID: ${result.data}")
                    submitResultLiveData.postValue(result)
                } else {
                    val errorMsg = "兴趣区域检测结果提交失败 - 错误码: ${result.code}, 错误信息: ${result.message}"
                    Logger.e(TAG, msg = errorMsg)
                    _submitErrorLiveData.postValue("数据提交失败：${result.message ?: "网络错误"}")
                    submitResultLiveData.postValue(null)
                }
            } else {
                Logger.e(TAG, msg = "兴趣区域检测结果提交失败: ${errorInfo?.message}")
                _submitErrorLiveData.postValue(errorInfo?.message ?: "数据提交失败")
                submitResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 构建提交参数
     */
    private fun buildSubmitParams(
        patientId: Long,
        imagePath: String,
        imageWidth: Int,
        imageHeight: Int,
        roiRegions: String,
        gazePoints: List<GazePoint>,
        duration: Int,
        notes: String,
        imageUrl: String?
    ): HashMap<String, Any> {
        val currentTime = System.currentTimeMillis()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
        val testDate = dateFormat.format(Date(currentTime))

        // 构建视线轨迹数据 - 只传递有实际数据的字段
        val gazeTrajectory = gazePoints.mapIndexed { index, point ->
            val trajectoryPoint = mutableMapOf<String, Any>(
                "index" to (index + 1),
                "x" to java.math.BigDecimal.valueOf((point.x ?: 0.0f).toDouble()),
                "y" to java.math.BigDecimal.valueOf((point.y ?: 0.0f).toDouble()),
                "distance" to java.math.BigDecimal.valueOf((point.dist ?: 0.0f).toDouble()),
                "duration" to (point.duration ?: 0),
                "isValid" to point.checkValid()
            )

            // 只有当timestamp有实际值时才添加
            point.timestamp?.let { trajectoryPoint["timestamp"] = it }

            trajectoryPoint
        }

        // 构建增强的视线轨迹JSON数据（包含ROI分析）
        val gazeTrajectoryJson = buildEnhancedGazeTrajectoryJson(gazePoints, roiRegions, imageWidth, imageHeight)

        // 计算ROI检测相关统计数据
        val roiStats = calculateROIStats(gazePoints, roiRegions, imageWidth, imageHeight, duration)

        // 构建ROI检测数据
        val roiDetectionData = hashMapOf<String, Any>(
            "imagePath" to imagePath,
            "imageWidth" to imageWidth,
            "imageHeight" to imageHeight,
            "roiRegions" to roiRegions,
            "gazeTrajectoryJson" to gazeTrajectoryJson,
            "heatmapData" to buildHeatmapData(gazePoints, imageWidth, imageHeight),
            "attentionDistribution" to (roiStats["attentionDistribution"] ?: ""),
            "scanPath" to buildScanPath(gazePoints),
            "fixationDurationAvg" to (roiStats["fixationDurationAvg"] ?: java.math.BigDecimal.ZERO),
            "fixationCount" to (roiStats["fixationCount"] ?: 0),
            "saccadeCount" to (roiStats["saccadeCount"] ?: 0),
            "totalViewingTime" to duration,
            "roiCoverageRate" to (roiStats["roiCoverageRate"] ?: java.math.BigDecimal.ZERO),
            "roiDwellTime" to (roiStats["roiDwellTime"] ?: ""),
            "firstFixationTime" to (roiStats["firstFixationTime"] ?: java.math.BigDecimal.ZERO),
            "scanPatternType" to (roiStats["scanPatternType"] ?: ""),
            // 新增：每个ROI的视线点详细信息
            "roiPointsDetails" to (roiStats["roiPointsDetails"] ?: ""),
            // 新增：每个ROI的视线点坐标信息
            "roiPointsCoordinates" to (roiStats["roiPointsCoordinates"] ?: ""),
            // 新增：ROI外视线点坐标信息
            "outsideROICoordinates" to (roiStats["outsideROICoordinates"] ?: "")
        )

        // 构建测试信息
        val testInfo = hashMapOf<String, Any>(
            "testType" to "ROI_DETECTION",
            "testSequence" to "04",
            "testDate" to testDate,
            "duration" to duration,
            "calibrationParams" to "\"标准校准\"",
            "environmentInfo" to "\"室内光线充足\"",
            "notes" to notes
        )

        // 如果有图片URL，添加到testInfo中
        imageUrl?.let {
            if (it.isNotEmpty()) {
                testInfo["imageUrl"] = it
                Logger.d(TAG, msg = "图片URL已添加到testInfo: $it")
            }
        } ?: run {
            Logger.d(TAG, msg = "图片URL为空，未添加到testInfo")
        }

        // 构建完整的提交参数
        return hashMapOf(
            "patientId" to patientId,
            "testInfo" to testInfo,
            "resultData" to hashMapOf(
                "gazeTrajectory" to gazeTrajectory,
                "roiDetectionData" to roiDetectionData
            )
        )
    }

    /**
     * 构建增强的视线轨迹JSON数据
     */
    private fun buildEnhancedGazeTrajectoryJson(
        gazePoints: List<GazePoint>,
        roiRegions: String,
        imageWidth: Int,
        imageHeight: Int
    ): String {
        val enhancedTrajectory = gazePoints.mapIndexed { index, point ->
            val x = (point.x ?: 0.0f) * imageWidth
            val y = (point.y ?: 0.0f) * imageHeight

            mapOf(
                "timestamp" to (point.timestamp ?: System.currentTimeMillis()),
                "x" to x.toDouble(),
                "y" to y.toDouble(),
                "distance" to (point.dist ?: 0.0f).toDouble(),
                "duration" to (point.duration ?: 0),
                "pointIndex" to index,
                "roiAnalysis" to analyzePointInROI(x.toDouble(), y.toDouble(), roiRegions),
                "fixationDuration" to calculateFixationDuration(gazePoints, index),
                "saccadeVelocity" to calculateSaccadeVelocity(gazePoints, index)
            )
        }

        return gson.toJson(enhancedTrajectory)
    }

    /**
     * 分析点是否在ROI区域内
     */
    private fun analyzePointInROI(x: Double, y: Double, roiRegions: String): Map<String, Any> {
        try {
            // 解析ROI区域JSON
            val roiListType = object : com.google.gson.reflect.TypeToken<Array<Map<String, Any>>>() {}.type
            val roiList = gson.fromJson<Array<Map<String, Any>>>(roiRegions, roiListType) ?: emptyArray()

            for (roi in roiList) {
                val roiId = (roi["id"] as? Number)?.toInt() ?: -1
                @Suppress("UNCHECKED_CAST")
                val coordinates = roi["coordinates"] as? List<Map<String, Any>> ?: continue

                // 检查点是否在当前ROI区域内
                if (isPointInPolygon(x, y, coordinates)) {
                    val distance = calculateDistanceToROICenter(x, y, coordinates)
                    return mapOf<String, Any>(
                        "inROI" to true,
                        "roiId" to roiId,
                        "roiName" to (roi["name"] ?: "ROI_$roiId"),
                        "distanceToROI" to distance,
                        "pointX" to x,
                        "pointY" to y,
                        "dwellTime" to calculateDwellTime(x, y, coordinates)
                    )
                }
            }

            // 如果不在任何ROI区域内，计算到最近ROI的距离
            val nearestROI = findNearestROI(x, y, roiList)
            return mapOf<String, Any>(
                "inROI" to false,
                "roiId" to -1,
                "pointX" to x,
                "pointY" to y,
                "dwellTime" to 0L
            )

        } catch (e: Exception) {
            Logger.e(TAG, msg = "分析ROI区域失败: ${e.message}")
            return mapOf<String, Any>(
                "inROI" to false,
                "roiId" to -1,
                "distanceToROI" to 0.0,
                "pointX" to x,
                "pointY" to y,
                "error" to (e.message ?: "Unknown error")
            )
        }
    }

    /**
     * 计算注视持续时间
     */
    private fun calculateFixationDuration(gazePoints: List<GazePoint>, currentIndex: Int): Long {
        // 简化实现，返回固定值
        return if (currentIndex < gazePoints.size) {
            gazePoints[currentIndex].duration?.toLong() ?: 100L
        } else {
            100L
        }
    }

    /**
     * 计算扫视速度
     */
    private fun calculateSaccadeVelocity(gazePoints: List<GazePoint>, currentIndex: Int): Double {
        if (currentIndex == 0 || currentIndex >= gazePoints.size) return 0.0

        val current = gazePoints[currentIndex]
        val previous = gazePoints[currentIndex - 1]

        val dx = (current.x ?: 0.0f) - (previous.x ?: 0.0f)
        val dy = (current.y ?: 0.0f) - (previous.y ?: 0.0f)
        val distance = sqrt(dx * dx + dy * dy).toDouble()

        val timeDiff = (current.timestamp ?: 0L) - (previous.timestamp ?: 0L)
        return if (timeDiff > 0) distance / timeDiff * 1000 else 0.0 // 像素/秒
    }

    /**
     * 计算ROI统计数据
     */
    private fun calculateROIStats(
        gazePoints: List<GazePoint>,
        roiRegions: String,
        imageWidth: Int,
        imageHeight: Int,
        duration: Int
    ): Map<String, Any> {
        Logger.d(TAG, msg = "开始计算ROI统计数据")
        Logger.d(TAG, msg = "视线点数量: ${gazePoints.size}, 图像尺寸: ${imageWidth}x${imageHeight}, 测试时长: ${duration}ms")

        // 解析ROI区域数据
        val roiList = try {
            val roiListType = object : com.google.gson.reflect.TypeToken<Array<Map<String, Any>>>() {}.type
            gson.fromJson<Array<Map<String, Any>>>(roiRegions, roiListType) ?: emptyArray()
        } catch (e: Exception) {
            Logger.e(TAG, msg = "解析ROI区域数据失败: ${e.message}")
            emptyArray<Map<String, Any>>()
        }

        // 分析每个视线点是否在ROI区域内
        val roiPointsMap = mutableMapOf<Int, MutableList<GazePoint>>()
        val roiPointsCoordinatesMap = mutableMapOf<Int, MutableList<Map<String, Any>>>() // 新增：存储每个ROI的视线点坐标信息
        val outsideROIPoints = mutableListOf<GazePoint>()
        val outsideROICoordinates = mutableListOf<Map<String, Any>>() // 新增：存储ROI外视线点坐标信息
        var totalFixationTime = 0L
        var roiDwellTimeMap = mutableMapOf<Int, Long>()

        gazePoints.forEachIndexed { pointIndex, gazePoint ->
            if (gazePoint.checkValid()) {
                val x = (gazePoint.x ?: 0.0f) * imageWidth
                val y = (gazePoint.y ?: 0.0f) * imageHeight
                val pointDuration = gazePoint.duration?.toLong() ?: 100L
                totalFixationTime += pointDuration

                var foundInROI = false

                // 检查每个ROI区域
                roiList.forEachIndexed { roiIndex, roi ->
                    @Suppress("UNCHECKED_CAST")
                    val coordinates = roi["coordinates"] as? List<Map<String, Any>> ?: emptyList()

                    if (isPointInPolygon(x.toDouble(), y.toDouble(), coordinates)) {
                        // 视点在当前ROI区域内
                        if (!roiPointsMap.containsKey(roiIndex)) {
                            roiPointsMap[roiIndex] = mutableListOf()
                            roiPointsCoordinatesMap[roiIndex] = mutableListOf()
                        }
                        roiPointsMap[roiIndex]?.add(gazePoint)

                        // 添加详细的坐标信息
                        val pointCoordinateInfo = mapOf<String, Any>(
                            "pointIndex" to pointIndex,
                            "originalX" to (gazePoint.x ?: 0.0f), // 原始比例坐标 (0-1)
                            "originalY" to (gazePoint.y ?: 0.0f), // 原始比例坐标 (0-1)
                            "pixelX" to x.toInt(), // 像素坐标
                            "pixelY" to y.toInt(), // 像素坐标
                            "duration" to pointDuration,
                            "timestamp" to (gazePoint.timestamp ?: System.currentTimeMillis()),
                            "distance" to (gazePoint.dist ?: 0.0f),
                            "roiId" to roiIndex,
                            "roiName" to (roi["name"] ?: "ROI_$roiIndex")
                        )
                        roiPointsCoordinatesMap[roiIndex]?.add(pointCoordinateInfo)

                        roiDwellTimeMap[roiIndex] = (roiDwellTimeMap[roiIndex] ?: 0L) + pointDuration
                        foundInROI = true
                        return@forEachIndexed // 找到第一个包含该点的ROI区域后退出
                    }
                }

                // 如果不在任何ROI区域内
                if (!foundInROI) {
                    outsideROIPoints.add(gazePoint)

                    // 添加ROI外视线点的坐标信息
                    val outsidePointInfo = mapOf<String, Any>(
                        "pointIndex" to pointIndex,
                        "originalX" to (gazePoint.x ?: 0.0f),
                        "originalY" to (gazePoint.y ?: 0.0f),
                        "pixelX" to x.toInt(),
                        "pixelY" to y.toInt(),
                        "duration" to pointDuration,
                        "timestamp" to (gazePoint.timestamp ?: System.currentTimeMillis()),
                        "distance" to (gazePoint.dist ?: 0.0f),
                        "roiId" to -1,
                        "roiName" to "OUTSIDE_ROI"
                    )
                    outsideROICoordinates.add(outsidePointInfo)
                }
            }
        }

        // 计算统计数据
        val totalPoints = gazePoints.size
        val fixationCount = totalPoints / 3 // 假设每3个点为一个注视
        val saccadeCount = maxOf(0, fixationCount - 1)
        val totalDuration = duration.toDouble()

        // 计算ROI覆盖率
        val roiPointsCount = roiPointsMap.values.sumOf { it.size }
        val roiCoverageRate = if (totalPoints > 0) roiPointsCount.toDouble() / totalPoints else 0.0

        // 构建注意力分布数据
        val attentionDistribution = mutableMapOf<String, Double>()
        roiPointsMap.forEach { (roiIndex, points) ->
            val coverage = if (totalPoints > 0) points.size.toDouble() / totalPoints else 0.0
            attentionDistribution["roi_$roiIndex"] = coverage
        }
        if (outsideROIPoints.isNotEmpty()) {
            attentionDistribution["outside_roi"] = if (totalPoints > 0) outsideROIPoints.size.toDouble() / totalPoints else 0.0
        }

        // 构建ROI停留时间数据
        val roiDwellTimeData = mutableMapOf<String, Long>()
        roiDwellTimeMap.forEach { (roiIndex, dwellTime) ->
            roiDwellTimeData["roi_$roiIndex"] = dwellTime
        }

        // 构建每个ROI的视线点详细信息
        val roiPointsDetails = mutableMapOf<String, Any>()
        roiPointsCoordinatesMap.forEach { (roiIndex, pointsList) ->
            roiPointsDetails["roi_${roiIndex}_points"] = pointsList
            roiPointsDetails["roi_${roiIndex}_count"] = pointsList.size
        }

        // 添加ROI外视线点详细信息
        roiPointsDetails["outside_roi_points"] = outsideROICoordinates
        roiPointsDetails["outside_roi_count"] = outsideROICoordinates.size

        // 计算首次注视时间（第一个有效视线点的时间戳）
        val firstFixationTime = gazePoints.firstOrNull { it.checkValid() }?.timestamp?.toDouble() ?: 0.0

        // 确定扫描模式类型
        val scanPatternType = when {
            roiCoverageRate > 0.7 -> "系统性扫描"
            roiCoverageRate > 0.3 -> "部分系统性扫描"
            else -> "随机扫描"
        }

        Logger.d(TAG, msg = "ROI统计计算完成:")
        Logger.d(TAG, msg = "  - ROI区域数量: ${roiList.size}")
        Logger.d(TAG, msg = "  - ROI内视线点: $roiPointsCount")
        Logger.d(TAG, msg = "  - ROI外视线点: ${outsideROIPoints.size}")
        Logger.d(TAG, msg = "  - ROI覆盖率: ${String.format("%.2f", roiCoverageRate * 100)}%")
        Logger.d(TAG, msg = "  - 扫描模式: $scanPatternType")

        // 打印每个ROI的详细统计信息
        roiPointsCoordinatesMap.forEach { (roiIndex, pointsList) ->
            Logger.d(TAG, msg = "  - ROI_$roiIndex: ${pointsList.size}个视线点")
            pointsList.forEachIndexed { index, pointInfo ->
                if (index < 3) { // 只打印前3个点的详细信息，避免日志过长
                    Logger.d(TAG, msg = "    点${index + 1}: 像素坐标(${pointInfo["pixelX"]}, ${pointInfo["pixelY"]}), 比例坐标(${pointInfo["originalX"]}, ${pointInfo["originalY"]}), 持续时间${pointInfo["duration"]}ms")
                }
            }
            if (pointsList.size > 3) {
                Logger.d(TAG, msg = "    ... 还有${pointsList.size - 3}个点")
            }
        }

        return mapOf<String, Any>(
            "attentionDistribution" to gson.toJson(attentionDistribution),
            "fixationDurationAvg" to java.math.BigDecimal.valueOf(if (fixationCount > 0) totalDuration / fixationCount else 0.0),
            "fixationCount" to fixationCount,
            "saccadeCount" to saccadeCount,
            "roiCoverageRate" to java.math.BigDecimal.valueOf(roiCoverageRate),
            "roiDwellTime" to gson.toJson(roiDwellTimeData),
            "firstFixationTime" to java.math.BigDecimal.valueOf(firstFixationTime),
            "scanPatternType" to scanPatternType,
            // 新增：每个ROI的视线点详细信息
            "roiPointsDetails" to gson.toJson(roiPointsDetails),
            // 新增：每个ROI的视线点坐标信息（JSON格式）
            "roiPointsCoordinates" to gson.toJson(roiPointsCoordinatesMap),
            // 新增：ROI外视线点坐标信息
            "outsideROICoordinates" to gson.toJson(outsideROICoordinates)
        )
    }

    /**
     * 构建热力图数据
     */
    private fun buildHeatmapData(gazePoints: List<GazePoint>, imageWidth: Int, imageHeight: Int): String {
        val heatmapPoints = gazePoints.map { point ->
            mapOf(
                "x" to ((point.x ?: 0.0f) * imageWidth).toInt(),
                "y" to ((point.y ?: 0.0f) * imageHeight).toInt(),
                "intensity" to (point.duration ?: 100) / 1000.0 // 使用duration作为强度
            )
        }
        return gson.toJson(heatmapPoints)
    }

    /**
     * 构建扫描路径数据
     */
    private fun buildScanPath(gazePoints: List<GazePoint>): String {
        val scanPath = gazePoints.mapIndexed { index, point ->
            mapOf(
                "order" to index,
                "x" to (point.x ?: 0.0f).toDouble(),
                "y" to (point.y ?: 0.0f).toDouble(),
                "timestamp" to (point.timestamp ?: System.currentTimeMillis())
            )
        }
        return gson.toJson(scanPath)
    }

    /**
     * 判断点是否在多边形内（射线法）
     */
    private fun isPointInPolygon(x: Double, y: Double, coordinates: List<Map<String, Any>>): Boolean {
        if (coordinates.size < 3) return false

        var inside = false
        var j = coordinates.size - 1

        for (i in coordinates.indices) {
            val xi = (coordinates[i]["x"] as? Number)?.toDouble() ?: 0.0
            val yi = (coordinates[i]["y"] as? Number)?.toDouble() ?: 0.0
            val xj = (coordinates[j]["x"] as? Number)?.toDouble() ?: 0.0
            val yj = (coordinates[j]["y"] as? Number)?.toDouble() ?: 0.0

            if (((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside
            }
            j = i
        }

        return inside
    }

    /**
     * 计算点到ROI中心的距离
     */
    private fun calculateDistanceToROICenter(x: Double, y: Double, coordinates: List<Map<String, Any>>): Double {
        if (coordinates.isEmpty()) return 0.0

        // 计算ROI区域的中心点
        var centerX = 0.0
        var centerY = 0.0

        for (coord in coordinates) {
            centerX += (coord["x"] as? Number)?.toDouble() ?: 0.0
            centerY += (coord["y"] as? Number)?.toDouble() ?: 0.0
        }

        centerX /= coordinates.size
        centerY /= coordinates.size

        // 计算距离
        val dx = x - centerX
        val dy = y - centerY
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 计算在ROI区域内的停留时间
     */
    private fun calculateDwellTime(x: Double, y: Double, coordinates: List<Map<String, Any>>): Long {
        // 简化实现，返回固定值
        // 实际应该根据视线轨迹的时间序列来计算
        return 100L
    }

    /**
     * 找到最近的ROI区域
     */
    private fun findNearestROI(x: Double, y: Double, roiList: Array<Map<String, Any>>): Map<String, Any> {
        var minDistance = Double.MAX_VALUE
        var nearestROIId = -1

        for (roi in roiList) {
            @Suppress("UNCHECKED_CAST")
            val coordinates = roi["coordinates"] as? List<Map<String, Any>> ?: continue
            val distance = calculateDistanceToROICenter(x, y, coordinates)

            if (distance < minDistance) {
                minDistance = distance
                nearestROIId = (roi["id"] as? Number)?.toInt() ?: -1
            }
        }

        return mapOf<String, Any>(
            "distance" to minDistance,
            "roiId" to nearestROIId
        )
    }
}
