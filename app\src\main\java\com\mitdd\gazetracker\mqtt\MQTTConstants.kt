package com.mitdd.gazetracker.mqtt

/**
 * FileName: MQTTConstants
 * Author by lilin,Date on 2024/11/7 14:53
 * PS: Not easy to write code, please indicate.
 * MQTT 常量
 */
object MQTTConstants {

    /**
     * MQTT 四元组解密KEY
     */
    const val MQTT_AES_KEY = "885d59e3ff349d7fb03903e2304556ceadfea1bf38c972575d20489e69e4b178"

    /**
     * 系统属性MQTT四元组
     */
    const val SYSTEM_PROPERTIES_KEY_MQTT_QUAD = "ro.product.airdoc.tetrad"

    /**
     * MQTT初始化完成事件，传递一个Boolean值，true表示初始化完成，false表示未完成
     */
    const val LIVE_EVENT_INIT_DONE = "isInitDone"

    /**
     * MQTT 通知 参数
     */
    const val TOPIC_NOTIFY_PARAM_EVENT = "event"
    const val TOPIC_NOTIFY_PARAM_TYPE = "type"
    const val TOPIC_NOTIFY_PARAM_BIZ_KEY = "bizKey"
    const val TOPIC_NOTIFY_PARAM_MESSAGE = "message"
    const val TOPIC_NOTIFY_PARAM_CODE = "code"

    /**
     * MQTT 通知 设备解除绑定事件
     */
    const val TOPIC_NOTIFY_EVENT_DEVICE_UNBIND = "device-unbind"

}