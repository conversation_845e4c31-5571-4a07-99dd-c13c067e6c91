package com.mitdd.gazetracker.mqtt

import android.content.Context
import com.airdoc.component.common.log.Logger
import com.aliyun.alink.dm.api.DeviceInfo
import com.aliyun.alink.dm.api.IoTApiClientConfig
import com.aliyun.alink.linkkit.api.ILinkKitConnectListener
import com.aliyun.alink.linkkit.api.IoTDMConfig
import com.aliyun.alink.linkkit.api.IoTMqttClientConfig
import com.aliyun.alink.linkkit.api.LinkKit
import com.aliyun.alink.linkkit.api.LinkKitInitParams
import com.aliyun.alink.linksdk.cmp.connect.hubapi.HubApiRequest
import com.aliyun.alink.linksdk.cmp.core.base.ConnectState
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectNotifyListener
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectSendListener
import com.aliyun.alink.linksdk.tmp.device.payload.ValueWrapper
import com.aliyun.alink.linksdk.tools.AError
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.mqtt.listener.IConnectNotifyCallBack
import com.mitdd.gazetracker.mqtt.listener.IConnectNotifyHolder

/**
 * FileName: MQTTInitManager
 * Author by lilin,Date on 2024/10/26 10:57
 * PS: Not easy to write code, please indicate.
 */
object MQTTInitManager {

    private val TAG = MQTTInitManager::class.java.simpleName

    /**
     * 判断是否初始化完成
     * 未初始化完成，所有和云端的长链通信都不通
     */
    @Volatile
    private var isInitDone = false

    /**
     * MQTT 连接状态
     */
    @Volatile
    var connectState: ConnectState = ConnectState.DISCONNECTED

    private val iConnectNotifyHolder = IConnectNotifyHolder()

    /**
     * 初始化 耗时操作，建议放到异步线程
     * @param context 上下文
     * @param deviceName 设备名称
     * @param deviceSecret 设备密钥
     * @param productKey 产品类型
     * @param productSecret 产品密钥
     * @param listener 初始化建联结果回调
     */
    fun init(context: Context, deviceName: String, deviceSecret: String, productKey: String, productSecret: String,
             mqttHost:String, listener: ILinkKitConnectListener? = null){
        if (checkInit()){
            Logger.d(TAG, msg = "It's already initialized")
            return
        }
        val params = LinkKitInitParams()
        //Step1: 构造三元组信息对象
        params.deviceInfo = DeviceInfo().apply {
            this.productKey = productKey // 产品类型
            this.deviceName = deviceName // 设备名称
            this.deviceSecret = deviceSecret // 设备密钥
            this.productSecret = productSecret // 产品密钥
        }
        //Step2: 全局默认域名
        params.connectConfig = IoTApiClientConfig()
        //Step3: 物模型缓存
        /**
         * 物模型的数据会缓存到该字段中. 不可删除或者设置为空, 否则功能会异常
         * 用户调用物模型上报接口之后，物模型会有相关数据缓存。
         */
        params.propertyValues = HashMap<String, ValueWrapper<*>>()
        //Step4: mqtt设置
        /**
         * 慎用
         * Mqtt 相关参数设置,包括接入点等信息.具体见deviceinfo文件说明
         * 域名、产品密钥、认证安全模式等；
         */
        params.mqttClientConfig = IoTMqttClientConfig(productKey, deviceName, deviceSecret).apply {
            //cleanSession=1 不接受离线消息
            receiveOfflineMsg = false
            //mqtt接入点信息. 详情请参照https://help.aliyun.com/document_detail/147356.htm
            channelHost = mqttHost
        }
        //Step5: 高阶功能功能配置,默认均为关闭状态
        params.ioTDMConfig = IoTDMConfig().apply {
            // 默认开启物模型功能，开启之后init方法会等到物模型初始化（包含请求云端物模型）完成之后才返回onInitDone
            enableThingModel = true
            // 默认不开启网关功能，开启之后，初始化的时候会初始化网关模块，获取云端网关子设备列表
            enableGateway = false
            // 默认不开启，是否开启日志推送功能
            enableLogPush = false
        }

        //Step6: 下行消息处理回调设置
        LinkKit.getInstance().registerOnPushListener(iConnectNotifyHolder)

        //Step7: 一型一密免预注册设置(可选)
        //对于一型一密免预注册的设备, 设备连云时要用上deviceToken和clientId
        //MqttConfigure.deviceToken = "deviceToken"
        //MqttConfigure.clientId = "clientId"

        //Step8: H2文件上传设置(可选)
        /**
         * 如果要用到HTTP2文件上传, 需要用户设置域名
         */

        //Step8: H2文件上传设置(可选)
        /**
         * 如果要用到HTTP2文件上传, 需要用户设置域名
         */
        //params.iotH2InitParams = IoTH2Config().apply {
        //clientId = "client-id"
        //endPoint = "https://" + productKey + this.endPoint // 线上环境
        //}

        /**
         * 设备初始化建联
         * onError 初始化建联失败，如果因网络问题导致初始化失败，需要用户重试初始化
         * onInitDone 初始化成功
         */
        LinkKit.getInstance().init(context, params, object : ILinkKitConnectListener {
            override fun onError(error: AError?) {
                Logger.e(TAG, msg = "init mqtt error msg = ${error?.msg}, code = ${error?.code}")
                isInitDone = false
                LiveEventBus.get<Boolean>(MQTTConstants.LIVE_EVENT_INIT_DONE).post(false)
                listener?.onError(error)
            }

            override fun onInitDone(data: Any?) {
                Logger.d(TAG, msg = "init mqtt onInitDone")
                isInitDone = true
                LiveEventBus.get<Boolean>(MQTTConstants.LIVE_EVENT_INIT_DONE).post(true)
                listener?.onInitDone(data)
            }
        })
    }

    fun checkInit():Boolean{
        return isInitDone
    }

    /**
     * 如果需要动态注册设备获取设备的deviceSecret，可以参考本接口实现。建议异步线程
     * 动态注册条件检测：
     * 1.云端开启该设备动态注册功能；
     * 2.首先在云端创建 pk，dn；
     * @param context 上下文
     * @param productKey 产品类型
     * @param deviceName 设备名称 需要现在云端创建
     * @param productSecret 产品密钥
     * @param listener 密钥请求回调
     */
    fun registerDevice(context: Context, productKey: String, deviceName: String,
                       productSecret: String, listener: IConnectSendListener?){
        val myDeviceInfo = DeviceInfo().apply {
            this.productKey = productKey
            this.deviceName = deviceName
            this.productSecret = productSecret
        }
        val params = LinkKitInitParams().apply {
            // 如果明确需要切换域名，可以设置 connectConfig 中 domain 的值
            connectConfig = IoTApiClientConfig()
            deviceInfo = myDeviceInfo
        }
        val hubApiRequest = HubApiRequest().apply {
            path = "/auth/register/device"
        }
        // 调用动态注册接口
        LinkKit.getInstance().deviceRegister(context, params, hubApiRequest, listener)
    }

    fun addIConnectNotifyCallBack(callBack: IConnectNotifyCallBack){
        iConnectNotifyHolder.addIConnectNotifyCallBack(callBack)
    }

    fun removeIConnectNotifyCallBack(callBack: IConnectNotifyCallBack){
        iConnectNotifyHolder.removeIConnectNotifyCallBack(callBack)
    }
}