package com.mitdd.gazetracker.mqtt

import android.content.Context
import android.text.TextUtils
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.aliyun.alink.linkkit.api.LinkKit
import com.aliyun.alink.linksdk.cmp.connect.channel.MqttPublishRequest
import com.aliyun.alink.linksdk.cmp.connect.channel.MqttSubscribeRequest
import com.aliyun.alink.linksdk.cmp.core.base.ARequest
import com.aliyun.alink.linksdk.cmp.core.base.AResponse
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectSendListener
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectSubscribeListener
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectUnscribeListener
import com.aliyun.alink.linksdk.tools.AError
import com.google.gson.Gson
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.aes.AesManager
import com.mitdd.gazetracker.device.DeviceConstants
import com.mitdd.gazetracker.device.DeviceManager
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.util.concurrent.atomic.AtomicReference

/**
 * FileName: MQTTManager
 * Author by lilin,Date on 2024/10/26 11:17
 * PS: Not easy to write code, please indicate.
 */
object MQTTManager {

    private val TAG = MQTTManager::class.java.simpleName
    private val gson = Gson()

    /**
     * MQTT 实例id
     */
    val instanceId = AtomicReference("")

    /**
     * MQTT 实例所在域
     */
    val region = AtomicReference("")

    /**
     * MQTT 设备名称
     */
    val deviceName = AtomicReference("")

    /**
     * MQTT 设备密钥
     */
    val deviceSecret = AtomicReference("")

    /**
     * MQTT 产品类型
     */
    val productKey = AtomicReference("")

    /**
     * MQTT 产品密钥
     */
    val productSecret = AtomicReference("")

    /**
     * MQTT 通知事件
     */
    val TOPIC_NOTIFY = AtomicReference("/${productKey.get()}/${deviceName.get()}/user/event/notify")

    /**
     * 解析MQTT 四元组信息
     * 会耗时，建议异步线程
     */
    fun parseMqttFourTuples(){
        //获取四元组信息
        val property = DeviceManager.getMQTTFourTuples()
        Logger.d(TAG, msg = "parseMqttFourTuples property = $property")
        if (!TextUtils.isEmpty(property)){
            val auth = AesManager.decrypt(property, AesManager.getKey(MQTTConstants.MQTT_AES_KEY))
            val split = auth.split(".")
            if (split.size == 4){
                deviceName.set(split[0])
                deviceSecret.set(split[1])
                productKey.set(split[2])
                productSecret.set(split[3])
                TOPIC_NOTIFY.set("/${productKey.get()}/${deviceName.get()}/user/event/notify")
            }
        }
    }

    /**
     * 发布，建议异步线程
     * @param data 需要发布的数据，可以为任意格式数据。如果格式为JSON String，其中id字段需要保持每次唯一，不可重复，请使用自增的方式进行设置ID字段。
     * 示例中id字段为160865432，则下次id字段应为160865433。
     * {"id":"160865432","method":"thing.event.property.post","params":{"LightSwitch":1},"version":"1.0"}
     * @param topic 拥有发布权限的Topic。设备通过该Topic向物联网平台发送消息。
     * @param qos 设置QoS（Quality of Service）等级，即物联网平台与设备之间保证交付信息的协议。仅支持：
     *        0：最多一次。
     *        1：最少一次。
     * @param isReply 是否为RPC请求，如果是，则需要等待 replyTopic消息后才Rsp。默认为false，表示不需应答。
     * @param replyTopic 设置物联网平台答复的topic，若不设置，则默认为 topic+“_reply”。
     * @param listener 发布监听
     *
     */
    fun publish(data:String,topic:String,qos:Int = 0,isReply:Boolean = false,replyTopic:String = "",listener: IConnectSendListener){
        if (!MQTTInitManager.checkInit()){
            Logger.e(TAG, msg = "publish error because mqtt is not init")
            return
        }
        val request = MqttPublishRequest()
        // 设置是否需要应答。
        request.isRPC = isReply
        // 设置qos
        request.qos = qos
        // 设置topic，设备通过该Topic向物联网平台发送消息。以下Topic为示例，需替换为用户自己设备的Topic。
        request.topic = topic
        if (isReply && !TextUtils.isEmpty(replyTopic)){
            //设置物联网平台答复的topic，若不设置，则默认为 topic+“_reply”。
            request.replyTopic = replyTopic
        }
        request.payloadObj = data
        LinkKit.getInstance().publish(request, object : IConnectSendListener {
            override fun onResponse(aRequest: ARequest?, aResponse: AResponse?) {
                // 消息成功提交给操作系统的发送缓冲区。
                // 在网络波动等异常情况下，消息可能无法到达云端。
                // 如果上行的消息有对应的下行的reply, 建议通过reply报文来确认上行消息的到达。
                Logger.d(TAG, msg = "publish $data to $topic response")
                Logger.json(TAG, json = gson.toJson(aResponse))
                listener.onResponse(aRequest,aResponse)
            }

            override fun onFailure(aRequest: ARequest?, aError: AError?) {
                // 发布失败
                Logger.e(TAG, msg = "publish $data to $topic failure")
                Logger.json(TAG, json = gson.toJson(aError))
                listener.onFailure(aRequest,aError)
            }
        })
    }

    /**
     * 订阅 建议异步线程
     * @param subTopic 要订阅的Topic
     * @param qos 设置QoS（Quality of Service）等级，即物联网平台与设备之间保证交付信息的协议。仅支持：
     *      0：最多一次。
     *      1：最少一次。
     * @param listener 订阅监听
     */
    fun subscribe(subTopic:String,qos:Int = 0,listener: IConnectSubscribeListener? = null){
        if (!MQTTInitManager.checkInit()){
            Logger.e(TAG, msg = "subscribe error because mqtt is not init")
            return
        }
        val subscribeRequest = MqttSubscribeRequest()
        subscribeRequest.topic = subTopic
        subscribeRequest.isSubscribe = true
        subscribeRequest.qos = qos
        LinkKit.getInstance().subscribe(subscribeRequest, object : IConnectSubscribeListener {
            override fun onSuccess() {
                // 订阅成功
                Logger.d(TAG, msg = "subscribe $subTopic success")
                listener?.onSuccess()
            }

            override fun onFailure(aError: AError) {
                // 订阅失败
                Logger.e(TAG, msg = "subscribe $subTopic failure")
                Logger.json(TAG, json = gson.toJson(aError))
                listener?.onFailure(aError)
            }
        })
    }

    /**
     * 取消订阅 建议异步线程
     * @param unSubTopic 要取消订阅的Topic
     * @param listener 订阅监听
     */
    fun unsubscribe(unSubTopic:String,listener: IConnectUnscribeListener?){
        if (!MQTTInitManager.checkInit()){
            Logger.e(TAG, msg = "unsubscribe error because mqtt is not init")
            return
        }
        val unSubRequest = MqttSubscribeRequest()
        unSubRequest.topic = unSubTopic
        unSubRequest.isSubscribe = false
        LinkKit.getInstance().unsubscribe(unSubRequest, object : IConnectUnscribeListener {
            override fun onSuccess() {
                // 取消订阅成功
                Logger.d(TAG, msg = "unsubscribe $unSubTopic success")
                listener?.onSuccess()
            }

            override fun onFailure(aError: AError) {
                // 取消订阅失败
                Logger.e(TAG, msg = "unsubscribe $unSubTopic failure")
                Logger.json(TAG, json = gson.toJson(aError))
                listener?.onFailure(aError)
            }
        })
    }

    /**
     * 测试AES解密
     */
    fun processRawFiles(context: Context) {
        val key = AesManager.getKey(MQTTConstants.MQTT_AES_KEY)
        val inputRawResourceId = R.raw.mqtt_input // 替换为你的输入文件的资源ID
        val outputFileName = "mqtt_output.txt" // 输出文件的名称
        context.resources.openRawResource(inputRawResourceId).use { inputStream ->
            BufferedReader(InputStreamReader(inputStream)).use { reader ->
                context.openFileOutput(outputFileName, Context.MODE_PRIVATE).use { fileOutputStream ->
                    BufferedWriter(OutputStreamWriter(fileOutputStream)).use { writer ->
                        var line: String?
                        while (reader.readLine().also { line = it } != null) {
                            val processedLine = AesManager.decrypt(line!!,key)
                            writer.write(processedLine)
                            writer.newLine()
                        }
                    }
                }
            }
        }
    }

}