package com.mitdd.gazetracker.mqtt.listener

import com.airdoc.component.common.log.Logger
import com.alibaba.fastjson.JSONObject
import com.aliyun.alink.linksdk.cmp.core.base.AMessage
import com.aliyun.alink.linksdk.cmp.core.base.ConnectState
import com.aliyun.alink.linksdk.cmp.core.listener.IConnectNotifyListener
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.mqtt.MQTTConstants
import com.mitdd.gazetracker.mqtt.MQTTInitManager
import com.mitdd.gazetracker.mqtt.MQTTManager

/**
 * FileName: IConnectNotifyHolder
 * Author by lilin,Date on 2024/10/26 11:17
 * PS: Not easy to write code, please indicate.
 * 下行监听器，云端 MQTT 下行数据都会通过这里回调
 */
open class IConnectNotifyHolder : IConnectNotifyListener {

    companion object{
        private val TAG = IConnectNotifyHolder::class.java.simpleName
    }

    private val listenerSet = HashSet<IConnectNotifyCallBack>()

    fun addIConnectNotifyCallBack(callBack: IConnectNotifyCallBack){
        listenerSet.add(callBack)
    }

    fun removeIConnectNotifyCallBack(callBack: IConnectNotifyCallBack){
        listenerSet.remove(callBack)
    }

    /**
     * onNotify 会触发的前提是 shouldHandle 没有指定不处理这个topic
     * 服务端返回数据示例  data = {"method":"thing.service.test_service","id":"123374967","params":{"vv":60},"version":"1.0.0"}
     * @param connectId 连接类型，这里判断是否长链 connectId == ConnectSDK.getInstance().getPersistentConnectId()
     * @param topic 下行的topic
     * @param aMessage 下行的数据内容
     */
    override fun onNotify(connectId: String, topic: String, aMessage: AMessage) {
        val data = String((aMessage.data as ByteArray))
        Logger.d(TAG, msg = "onNotify connectId = $connectId, topic = $topic, data = $data")
        when(topic){
            MQTTManager.TOPIC_NOTIFY.get() ->{
                handlerNotify(data)
            }
        }
    }

    /**
     * @param connectId 连接类型，这里判断是否长链 connectId == ConnectSDK.getInstance().getPersistentConnectId()
     * @param topic 下行topic
     * @return 是否要处理这个topic，如果为true，则会回调到onNotify；如果为false，onNotify不会回调这个topic相关的数据。建议默认为true。
     */
    override fun shouldHandle(connectId: String, topic: String): Boolean {
        Logger.d(TAG, msg = "shouldHandle connectId = $connectId, topic = $topic")
        return true
    }

    /**
     * SDK连云成功后，后续如果网络波动导致连接断开时，SDK会抛出ConnectState.DISCONNECTED这种状态。
     * 在这种情况下，SDK会自动尝试重连，重试的间隔是1s、2s、4s、8s...128s...128s，到了最大间隔128s后，会一直以128s为间隔重连直到连云成功。
     * @param connectId 连接类型，这里判断是否长链 connectId == ConnectSDK.getInstance().getPersistentConnectId()
     * @param connectState {@link ConnectState}
     *     CONNECTED, 连接成功
     *     DISCONNECTED, 已断链
     *     CONNECTING, 连接中
     *     CONNECTFAIL; 连接失败
     */
    override fun onConnectStateChange(connectId: String, connectState: ConnectState) {
        Logger.d(TAG, msg = "onConnectStateChange connectId = $connectId, connectState = $connectState")
        MQTTInitManager.connectState = connectState
    }

    /**
     * 处理通知消息的函数
     *
     * 该函数负责解析接收到的数据，检查其格式，并根据数据中的事件类型执行相应的处理逻辑
     * 主要关注的事件类型包括设备解绑通知通过此函数，可以通知监听器进行设备解绑后的处理
     *
     * @param data 作为通知的消息数据，应为JSON格式的字符串
     */
    private fun handlerNotify(data: String){
        // 检查数据格式是否有效，如果无效则记录错误日志并返回
        if(data.isEmpty() || !data.startsWith("{") || !data.endsWith("}")){
            Logger.e(TAG, msg = "Invalid data format: $data")
            return
        }
        try {
            // 解析数据为JSON对象
            val parseObject = JSONObject.parseObject(data)
            // 提取事件类型和业务键
            val event = parseObject[MQTTConstants.TOPIC_NOTIFY_PARAM_EVENT] as? String
            val bizKey = parseObject[MQTTConstants.TOPIC_NOTIFY_PARAM_BIZ_KEY] as? String
            // 记录解析到的事件类型和业务键
            Logger.d(TAG, msg = "handlerNotify event = $event, bizKey = $bizKey")
            // 根据事件类型处理相应的逻辑
            when(event){
                MQTTConstants.TOPIC_NOTIFY_EVENT_DEVICE_UNBIND ->{
                    // 获取设备序列号，并与业务键进行比较，如果匹配，则通知所有监听器设备已解绑
                    val deviceSn = DeviceManager.getDeviceSn()
                    if (!bizKey.isNullOrEmpty() && bizKey == deviceSn){
                        listenerSet.forEach {
                            it.onDeviceUnbind()
                        }
                    }
                }
            }
        }catch (e:Exception){
            // 捕获并记录处理过程中发生的异常
            Logger.e(TAG, msg = "handlerNotify error: $e")
        }
    }


}