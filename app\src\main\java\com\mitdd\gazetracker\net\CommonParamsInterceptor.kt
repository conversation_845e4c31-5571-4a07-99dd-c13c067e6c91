package com.mitdd.gazetracker.net

import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.device.DeviceManager
import okhttp3.Headers
import okhttp3.Interceptor
import okhttp3.Response

/**
 * FileName: CommonParamsInterceptor
 * Author by lilin,Date on 2024/10/8 15:08
 * PS: Not easy to write code, please indicate.
 */
class CommonParamsInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val oldRequest = chain.request()

        val builder = Headers.Builder()
        oldRequest.headers.forEach {
            builder.add(it.first, it.second)
        }
        builder["X-Rom-Version"] = DeviceManager.getOSVersion()
        builder["X-Device-Sn"] = DeviceManager.getDeviceSn()
        builder["X-App-Version"] = PackageUtils.getVersionName(
            BaseCommonApplication.instance,
            BaseCommonApplication.instance.packageName)
        builder["X-Device-Mode"] = DeviceManager.getProductModel()
        builder["X-Airdoc-Client"] = "d409e47e-95e6-41fc-868c-e5748957d546"
        val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)
        builder["Accept-Language"] = "${locale.language}-${locale.country}"

        val request = oldRequest.newBuilder().headers(builder.build()).build()
        return chain.proceed(request)
    }
}