package com.mitdd.gazetracker.net

import android.annotation.SuppressLint
import com.airdoc.component.common.net.base.BaseRetrofitClient
import okhttp3.OkHttpClient
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.X509TrustManager

/**
 * FileName: GTRetrofitClient
 * Author by lilin,Date on 2024/10/8 15:25
 * PS: Not easy to write code, please indicate.
 */
object MainRetrofitClient : BaseRetrofitClient() {

    private val trustAllCerts = arrayOf<X509TrustManager>(@SuppressLint("CustomX509TrustManager")
    object : X509TrustManager {
        @SuppressLint("TrustAllX509TrustManager")
        override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
        }

        @SuppressLint("TrustAllX509TrustManager")
        override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
        }

        override fun getAcceptedIssuers(): Array<X509Certificate?> = arrayOf()
    })

    private val sslContext = SSLContext.getInstance("SSL").apply {
        init(null, trustAllCerts, SecureRandom())
    }
    private val sslSocketFactory = sslContext.socketFactory

    override fun fetchBaseUrl(): String {
        return "${UrlConfig.MAIN_DOMAIN}/"
    }

    override fun handleBuilder(builder: OkHttpClient.Builder) {
        builder.connectTimeout(10L, TimeUnit.SECONDS)
        builder.readTimeout(15L, TimeUnit.SECONDS)
        builder.writeTimeout(15L, TimeUnit.SECONDS)
        builder.sslSocketFactory(sslSocketFactory, trustAllCerts[0])
        builder.hostnameVerifier(HostnameVerifier { _, _ -> true })
        builder.addInterceptor(CommonParamsInterceptor())
    }

}