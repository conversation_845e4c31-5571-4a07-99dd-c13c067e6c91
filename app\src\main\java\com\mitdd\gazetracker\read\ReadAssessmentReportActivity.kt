package com.mitdd.gazetracker.read

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: ReadAssessmentReport
 * Author by lilin,Date on 2024/12/18 14:53
 * PS: Not easy to write code, please indicate.
 * 阅读评估报告
 */
class ReadAssessmentReportActivity : GTBaseActivity() {

    companion object{
        private val TAG = ReadAssessmentReportActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ReadAssessmentReportActivity::class.java)
            return intent
        }
    }

    private val ivBack by id<ImageView>(R.id.iv_back)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_read_assessment_report)

        initParam()
        initView()
        initObserver()
        initData()
    }

    private fun initParam() {

    }

    private fun initView() {
        ivBack.setOnSingleClickListener {
            finish()
        }
    }

    private fun initObserver() {

    }

    private fun initData() {

    }

}