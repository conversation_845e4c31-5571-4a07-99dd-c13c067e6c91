package com.mitdd.gazetracker.read

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import com.airdoc.component.common.ktx.id
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.read.enumeration.ReadGrade
import com.mitdd.gazetracker.read.enumeration.ReadIdentity

/**
 * FileName: ReadInitActivity
 * Author by lilin,Date on 2024/12/4 11:26
 * PS: Not easy to write code, please indicate.
 */
class ReadInitActivity : GTBaseActivity() {

    companion object{
        private val TAG = ReadInitActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, ReadInitActivity::class.java)
        }
    }

    private val tvTabBasicInfo by id<TextView>(R.id.tv_tab_basic_info)
    private val tvTabCalibration by id<TextView>(R.id.tv_tab_calibration)
    private val tvTabStartEvaluate by id<TextView>(R.id.tv_tab_start_evaluate)

    //身份
    private var mIdentity: ReadIdentity? = null
    //年级
    private var mGrade: ReadGrade? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_read_init)

        initView()
    }

    private fun initView(){
        goToBasicInfoTab()
    }

    fun setIdentity(identity: ReadIdentity?){
        mIdentity = identity
    }

    fun getIdentity(): ReadIdentity? {
        return mIdentity
    }

    fun setGrade(grade: ReadGrade?){
        mGrade = grade
    }

    fun getGrade(): ReadGrade? {
        return mGrade
    }

    /**
     * 去基础信息Tab
     */
    fun goToBasicInfoTab(){
        tvTabBasicInfo.isSelected = true
        tvTabCalibration.isSelected = false
        tvTabStartEvaluate.isSelected = false
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_read_init_container, ReadInitBasicInfoFragment.newInstance()).commitAllowingStateLoss()
    }

    /**
     * 去眼动校准Tab
     */
    fun goToCalibrationTab(){
        tvTabBasicInfo.isSelected = false
        tvTabCalibration.isSelected = true
        tvTabStartEvaluate.isSelected = false
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_read_init_container, ReadInitCalibrationFragment.newInstance()).commitAllowingStateLoss()
    }

    /**
     * 去开始评估Tab
     */
    fun goToStartEvaluate(){
        tvTabBasicInfo.isSelected = false
        tvTabCalibration.isSelected = false
        tvTabStartEvaluate.isSelected = true
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.fl_read_init_container, ReadInitStartEvaluateFragment.newInstance()).commitAllowingStateLoss()
    }

    /**
     * 去阅读页面
     */
    fun goToRead(){
        startActivity(ReadActivity.createIntent(this,mIdentity?:ReadIdentity.PRIMARY_SCHOOL,mGrade?:ReadGrade.GRADE_FIRST))
        finish()
    }

}