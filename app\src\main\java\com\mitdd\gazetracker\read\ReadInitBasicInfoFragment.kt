package com.mitdd.gazetracker.read

import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.read.enumeration.ReadGrade
import com.mitdd.gazetracker.read.enumeration.ReadIdentity

/**
 * FileName: ReadInitBasicInfoFragment
 * Author by lilin,Date on 2024/12/4 13:46
 * PS: Not easy to write code, please indicate.
 */
class ReadInitBasicInfoFragment : BaseCommonFragment() {

    companion object{
        private val TAG = ReadInitBasicInfoFragment::class.java.simpleName

        fun newInstance(): ReadInitBasicInfoFragment {
            return ReadInitBasicInfoFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_read_init_basic_info
    }

    private val rgIdentity by id<RadioGroup>(R.id.rg_identity)
    private val tvGrade by id<TextView>(R.id.tv_grade)
    private val rgGrade by id<RadioGroup>(R.id.rg_grade)
    private val rbGradeFirst by id<RadioButton>(R.id.rb_grade_first)
    private val rbGradeSecond by id<RadioButton>(R.id.rb_grade_second)
    private val rbGradeThree by id<RadioButton>(R.id.rb_grade_three)
    private val rbGradeFour by id<RadioButton>(R.id.rb_grade_four)
    private val rbGradeFive by id<RadioButton>(R.id.rb_grade_five)
    private val rbGradeSix by id<RadioButton>(R.id.rb_grade_six)

    override fun initView() {
        super.initView()
        initListener()
    }

    private fun initListener(){
        rgIdentity.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_identity_primary_school ->{
                    (mActivity as? ReadInitActivity)?.setIdentity(ReadIdentity.PRIMARY_SCHOOL)
                    rgGrade.clearCheck()
                    tvGrade.isVisible = true
                    rgGrade.isVisible = true
                    rbGradeFour.isVisible = true
                    rbGradeFive.isVisible = true
                    rbGradeSix.isVisible = true
                }
                R.id.rb_identity_junior_high_school ->{
                    (mActivity as? ReadInitActivity)?.setIdentity(ReadIdentity.JUNIOR_HIGH_SCHOOL)
                    rgGrade.clearCheck()
                    tvGrade.isVisible = true
                    rgGrade.isVisible = true
                    rbGradeFour.isVisible = false
                    rbGradeFive.isVisible = false
                    rbGradeSix.isVisible = false
                }
                R.id.rb_identity_senior_high_school ->{
                    (mActivity as? ReadInitActivity)?.setIdentity(ReadIdentity.SENIOR_HIGH_SCHOOL)
                    rgGrade.clearCheck()
                    tvGrade.isVisible = true
                    rgGrade.isVisible = true
                    rbGradeFour.isVisible = false
                    rbGradeFive.isVisible = false
                    rbGradeSix.isVisible = false
                }
                R.id.rb_identity_adult ->{
                    (mActivity as? ReadInitActivity)?.setIdentity(ReadIdentity.ADULT)
                    rgGrade.clearCheck()
                    tvGrade.isVisible = false
                    rgGrade.isVisible = false
                    checkIdentityAndGrade()
                }
            }
        }
        rgGrade.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_grade_first ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_FIRST)
                    checkIdentityAndGrade()
                }
                R.id.rb_grade_second ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_SECOND)
                    checkIdentityAndGrade()
                }
                R.id.rb_grade_three ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_THREE)
                    checkIdentityAndGrade()
                }
                R.id.rb_grade_four ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_FOUR)
                    checkIdentityAndGrade()
                }
                R.id.rb_grade_five ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_FIVE)
                    checkIdentityAndGrade()
                }
                R.id.rb_grade_six ->{
                    (mActivity as? ReadInitActivity)?.setGrade(ReadGrade.GRADE_SIX)
                    checkIdentityAndGrade()
                }
            }
        }
    }

    /**
     * 检查身份和年级
     */
    private fun checkIdentityAndGrade(){
        val readInitActivity = mActivity as? ReadInitActivity
        val identity = readInitActivity?.getIdentity()
        val grade = readInitActivity?.getGrade()
        Logger.d(TAG, msg = "checkIdentityAndGrade identity = $identity, grade = $grade")
        when(identity){
            ReadIdentity.PRIMARY_SCHOOL,ReadIdentity.JUNIOR_HIGH_SCHOOL,ReadIdentity.SENIOR_HIGH_SCHOOL ->{
                if (grade != null){
                    readInitActivity.goToCalibrationTab()
                }
            }
            ReadIdentity.ADULT ->{
                readInitActivity.goToCalibrationTab()
            }
            else ->{
            }
        }
    }

}