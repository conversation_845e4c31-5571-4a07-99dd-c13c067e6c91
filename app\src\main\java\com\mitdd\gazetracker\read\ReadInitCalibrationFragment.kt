package com.mitdd.gazetracker.read

import android.app.Activity
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode

/**
 * FileName: ReadInitCalibrationFragment
 * Author by lilin,Date on 2024/12/4 15:08
 * PS: Not easy to write code, please indicate.
 */
class ReadInitCalibrationFragment : BaseCommonFragment() {

    companion object{
        private val TAG = ReadInitCalibrationFragment::class.java.simpleName

        fun newInstance(): ReadInitCalibrationFragment {
            return ReadInitCalibrationFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_read_init_calibration
    }

    private val tvStartCalibration by id<TextView>(R.id.tv_start_calibration)
    private val tvSkipCalibration by id<TextView>(R.id.tv_skip_calibration)

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false)?:false
            if (isSucceed){
                Toast.makeText(mActivity,getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                val calibrationFailureDialog = CalibrationFailureDialog(mActivity).apply {
                    onRecalibration = {
                        startCalibration()
                    }
                }
                calibrationFailureDialog.show()
            }
        }
    }

    override fun initView() {
        super.initView()

        tvStartCalibration.setOnSingleClickListener {
            //启动眼动校准
            startCalibration()
        }
        tvSkipCalibration.setOnSingleClickListener {
            (activity as? ReadInitActivity)?.goToStartEvaluate()
        }
    }

    private fun startCalibration() {
        calibrationLauncher.launch(CalibrationActivity.createIntent(mActivity, CalibrationMode.CALIBRATION,false))
    }
}