package com.mitdd.gazetracker.read

import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: ReadInitStartEvaluateFragment
 * Author by lilin,Date on 2024/12/4 15:30
 * PS: Not easy to write code, please indicate.
 */
class ReadInitStartEvaluateFragment : BaseCommonFragment() {

    companion object{
        private val TAG = ReadInitStartEvaluateFragment::class.java.simpleName

        fun newInstance(): ReadInitStartEvaluateFragment {
            return ReadInitStartEvaluateFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_read_init_start_evaluate
    }

    private val tvStartRead by id<TextView>(R.id.tv_start_read)

    override fun initView() {
        super.initView()

        tvStartRead.setOnSingleClickListener {
            (mActivity as? ReadInitActivity)?.goToRead()
        }
    }

}