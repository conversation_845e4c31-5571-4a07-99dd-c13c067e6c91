package com.mitdd.gazetracker.read.bean

import android.os.Parcelable
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.bean.ReadTrackData
import kotlinx.android.parcel.Parcelize

/**
 * FileName: ReadResult
 * Author by lilin,Date on 2024/12/5 15:39
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class ReadResult(
    //阅读字数
    var wordCount:Int? = null,
    //阅读时长,单位秒
    var duration:Int? = null,
    //阅读轨迹数据
    var gazeTrajectory: GazeTrajectory? = null
): Parcelable
