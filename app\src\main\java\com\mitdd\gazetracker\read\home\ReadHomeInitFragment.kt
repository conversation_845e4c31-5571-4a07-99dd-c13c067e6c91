package com.mitdd.gazetracker.read.home

import android.text.TextUtils
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager

/**
 * FileName: ReadHomeInitFragment
 * Author by lilin,Date on 2025/1/20 10:53
 * PS: Not easy to write code, please indicate.
 * 阅读家庭版初始化Fragment
 */
class ReadHomeInitFragment : BaseCommonFragment() {

    companion object{
        private val TAG = ReadHomeInitFragment::class.java.simpleName

        fun newInstance(): ReadHomeInitFragment {
            return ReadHomeInitFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_read_homo_init
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val tvGetStarted by id<TextView>(R.id.tv_get_started)

    override fun initView() {
        super.initView()
        initListener()
    }

    override fun initData() {
        super.initData()
        updateDeviceInfo()
    }

    override fun initObserver() {
        super.initObserver()
    }

    private fun initListener(){
        tvGetStarted.setOnSingleClickListener {
            //去绑定
            (mActivity as? ReadHomeMainActivity)?.toBind()
        }
    }

    private fun updateDeviceInfo(){
        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(mActivity,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }

}