package com.mitdd.gazetracker.read.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.videobox.MultiClickListener
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.common.dialog.task.DialogTaskManager
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.mqtt.MQTTInitManager
import com.mitdd.gazetracker.mqtt.listener.IConnectNotifyCallBack
import com.mitdd.gazetracker.update.UpdateDialog
import com.mitdd.gazetracker.update.vm.UpdateViewModel
import com.mitdd.gazetracker.user.BindActivity
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.vm.UserViewModel

/**
 * FileName: ReadHomeMainActivity
 * Author by lilin,Date on 2025/1/20 10:33
 * PS: Not easy to write code, please indicate.
 * 阅读家庭版首页
 */
class ReadHomeMainActivity : GTBaseActivity() {

    companion object{
        private val TAG = ReadHomeMainActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ReadHomeMainActivity::class.java)
            return intent
        }
    }

    private val viewConfig by id<View>(R.id.view_config)

    val mainDialogTaskManager = DialogTaskManager(this)

    private val updateVM by viewModels<UpdateViewModel>()
    private val userVM by viewModels<UserViewModel>()

    private val mHandler = LifecycleHandler(Looper.getMainLooper(), this)

    private var bindLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            userVM.getAccountInfo()
        }
    }

    //MQTT消息监听
    private val iConnectNotifyCallBack = object : IConnectNotifyCallBack {
        override fun onDeviceUnbind() {
            super.onDeviceUnbind()
            userVM.getAccountInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_read_homo_main)

        initParam()
        initView()
        initData()
        initObserver()
        initMQTT()
        startGazeTrackerService()
    }

    private fun initParam(){
        val intent = intent
        val hasMaskTherapyState = intent.hasExtra(GazeConstants.KEY_MASK_THERAPY_STATE)
        Logger.d(TAG, msg = "initParam hasMaskTherapyState = $hasMaskTherapyState")
        if (hasMaskTherapyState){
            val state = intent.getBooleanExtra(GazeConstants.KEY_MASK_THERAPY_STATE,false)
            Logger.d(TAG, msg = "initParam state = $state")
            mHandler.postDelayed({
                LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_CURE).post(state)
            },1000)
        }
    }

    private fun initView(){
        initListener()
    }

    private fun initListener(){
        MQTTInitManager.addIConnectNotifyCallBack(iConnectNotifyCallBack)
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                DeviceManager.startConfigActivity(this@ReadHomeMainActivity)
            }
        })
    }

    private fun initObserver(){
        userVM.accountInfoLiveData.observe(this){
            if (UserManager.isBind()){
                showHospitalMain()
            }else{
                showHospitalInit()
            }
        }
        updateVM.appUpdateInfoLiveData.observe(this){
            it?.let {
                val url = it.appVersion?.url?:""
                val version = it.appVersion?.version ?: ""
                val introduction = it.appVersion?.introduce?:""
                val appSize = it.appVersion?.appSize?:0
                if (!TextUtils.isEmpty(url)){
                    UpdateDialog(this,url,version,introduction, it.forceUpdate?:false,appSize).show()
                }
            }
        }
    }

    private fun initData(){
        userVM.getAccountInfo()
    }

    override fun onResume() {
        super.onResume()
        updateVM.getAppUpdateInfo()
    }

    fun showHospitalMain(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.read_home_main_root, ReadHomeMainFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    fun showHospitalInit(){
        val beginTransaction = supportFragmentManager.beginTransaction()
        beginTransaction.replace(R.id.read_home_main_root, ReadHomeInitFragment.newInstance())
        beginTransaction.commitAllowingStateLoss()
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        initParam()
    }

    /**
     * 去绑定
     */
    fun toBind(){
        bindLauncher.launch(BindActivity.createIntent(this))
    }

    /**
     * 开启眼动追踪服务
     */
    private fun startGazeTrackerService(){
        val intent = Intent(this, GazeTrackService::class.java)
        startForegroundService(intent)
    }

    override fun onDestroy() {
        MQTTInitManager.removeIConnectNotifyCallBack(iConnectNotifyCallBack)
        super.onDestroy()
    }

}