package com.mitdd.gazetracker.read.home

import android.app.Activity
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.ai.ChatWebActivity
import com.mitdd.gazetracker.ai.vm.AdaViewModel
import com.mitdd.gazetracker.common.widget.CommonExceptionView
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.gaze.calibration.CalibrationActivity
import com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog
import com.mitdd.gazetracker.gaze.enumeration.CalibrationMode
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.medicalhome.HomeMainActivity
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus
import com.mitdd.gazetracker.help.HelpCenterActivity
import com.mitdd.gazetracker.medicalhome.preview.ParamSettingActivity
import com.mitdd.gazetracker.medicalhome.receiver.RefreshBindUserReceiver
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentManager
import com.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleItemDecoration
import com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.RawMedia
import com.mitdd.gazetracker.read.home.adapter.ReadHomeModuleAdapter
import com.mitdd.gazetracker.read.home.bean.ReadHomeMode
import com.mitdd.gazetracker.read.home.dialog.MyopiaControlStateDialog
import com.mitdd.gazetracker.read.home.vm.MyopiaControlViewModel
import com.mitdd.gazetracker.read.home.vm.ReadHomeViewModel
import com.mitdd.gazetracker.update.UpdateDialog
import com.mitdd.gazetracker.update.vm.UpdateViewModel
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.AccountInfo
import com.mitdd.gazetracker.user.bean.Gender
import com.mitdd.gazetracker.user.vm.UserViewModel
import java.util.Calendar

/**
 * FileName: ReadHomeMainFragment
 * Author by lilin,Date on 2025/1/20 11:05
 * PS: Not easy to write code, please indicate.
 * 阅读家庭版主页Fragment
 */
class ReadHomeMainFragment : BaseCommonFragment() {

    companion object{
        private val TAG = ReadHomeMainFragment::class.java.simpleName

        fun newInstance(): ReadHomeMainFragment {
            return ReadHomeMainFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_read_homo_main
    }

    private val ivHomeLogo by id<ImageView>(R.id.iv_home_logo)
    private val ivMore by id<ImageView>(R.id.iv_more)
    private val ivHelpCenter by id<ImageView>(R.id.iv_help_center)
    private val ivCalibration by id<ImageView>(R.id.iv_calibration)
    private val ivParamSetting by id<ImageView>(R.id.iv_param_setting)
    private val clUserInfo by id<ConstraintLayout>(R.id.cl_user_info)
    private val viewUserBg by id<View>(R.id.view_user_bg)
    private val ivUserAvatar by id<ImageView>(R.id.iv_user_avatar)
    private val tvUserName by id<TextView>(R.id.tv_user_name)
    private val rvTreatmentModule by id<RecyclerView>(R.id.rv_treatment_module)
    private val clNetworkException by id<CommonExceptionView>(R.id.cl_network_exception)
    private val tvUserUnbind by id<TextView>(R.id.tv_user_unbind)
    private val ivAi by id<ImageView>(R.id.iv_ai)

    private val readHomeVM by activityViewModels<ReadHomeViewModel>()
    private val treatmentVM by activityViewModels<TreatmentViewModel>()
    private val updateVM by activityViewModels<UpdateViewModel>()
    private val userVM by activityViewModels<UserViewModel>()
    private val adaVM by activityViewModels<AdaViewModel>()
    private val myopiaControlVM by activityViewModels<MyopiaControlViewModel>()

    private val mGson = Gson()

    private var calibrationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            //获取校准状态
            val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS,false)?:false
            if (isSucceed){
                Toast.makeText(mActivity,getString(R.string.str_calibration_success), Toast.LENGTH_SHORT).show()
            }else{
                val calibrationFailureDialog = CalibrationFailureDialog(mActivity).apply {
                    onRecalibration = {
                        startCalibration()
                    }
                }
                calibrationFailureDialog.show()
            }
        }
    }

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private val mModuleAdapter = ReadHomeModuleAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        startRefreshBindUser()
    }

    override fun initView() {
        super.initView()
        initListener()

        clNetworkException.apply {
            refreshClick = {
                clNetworkException.isVisible = false
                readHomeVM.getReadHomoProfile()
            }
            getExceptionIcon().apply {
                val params = layoutParams as ConstraintLayout.LayoutParams
                params.width = 250.dp2px(mActivity)
                params.height = 223.dp2px(mActivity)
                layoutParams = params
                setImageResource(R.drawable.icon_home_network_exception)
            }
            getExceptionInfo().apply {
                val params = layoutParams as ConstraintLayout.LayoutParams
                params.setMargins(0,28.dp2px(mActivity),0,0)
                layoutParams = params
                setTextColor(ContextCompat.getColor(mActivity, R.color.white))
                text = getString(R.string.str_network_anomaly_prompt)
                textSize = 15f
            }
            getExceptionPrompt1().isVisible = false
            getExceptionPrompt2().isVisible = false
        }

        rvTreatmentModule.layoutManager = LinearLayoutManager(mActivity,RecyclerView.HORIZONTAL,false)
        rvTreatmentModule.addItemDecoration(TreatmentModuleItemDecoration(15.dp2px(mActivity)))
        rvTreatmentModule.adapter = mModuleAdapter
    }

    override fun initObserver() {
        super.initObserver()
        userVM.accountInfoLiveData.observe(this){
            val isBind = UserManager.isBind()
            updateUserAvatar(it)
            updateUserName(it)
            tvUserUnbind.isVisible = !isBind
            if (isBind){
                val dialogTaskManager = (mActivity as? ReadHomeMainActivity)?.mainDialogTaskManager
                dialogTaskManager?.let {
                    TreatmentManager.showReviewRemindDialog(mActivity,dialogTaskManager,3)
                }
            }else{
                PlayManager.playRawMedia(mActivity, RawMedia(R.raw.welcome_please_bind_the_patient_file_first))
            }
        }
        readHomeVM.readHomeProfileLiveData.observe(this){
            updateTreatmentModule(it?.modules)
        }
        treatmentVM.curTreatmentLiveData.observe(this){
            Logger.d(TAG, msg = "curTreatmentLiveData courseId = ${it?.courseId}")
            if (it != null){
                handlerTreatmentDialog(it)
            }else{
                if (UserManager.isBind()){
//                    if (LocaleManager.getLanguage() == "en"){
//                        PlayManager.playHttpUrlMedia(HttpUrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/treatment_not_open.wav"))
//                    }else{
//                        PlayManager.playHttpUrlMedia(HttpUrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/treatment_not_open.wav"))
//                    }
                }
            }
        }
        treatmentVM.activationTreatmentSuccessLiveData.observe(this){ treatmentInfo ->
            Logger.d(TAG, msg = "activationTreatmentSuccess courseId = ${treatmentInfo.courseId}")
            val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
            dialogTaskManager?.let {
                TreatmentManager.showTreatmentActivationSuccessDialog(mActivity,dialogTaskManager, treatmentInfo ,true)
            }
            treatmentVM.getCurrentTreatment()
        }
        updateVM.appUpdateInfoLiveData.observe(this){
            it?.let {
                val url = it.appVersion?.url?:""
                val version = it.appVersion?.version ?: ""
                val introduction = it.appVersion?.introduce?:""
                val appSize = it.appVersion?.appSize?:0
                if (!TextUtils.isEmpty(url)){
                    UpdateDialog(mActivity,url,version,introduction, it.forceUpdate?:false,appSize).show()
                }
            }
        }
//        LiveEventBus.get<Boolean>(GazeConstants.EVENT_MASK_THERAPY_STATE_CHANGE).observe(this){
//            Logger.d(TAG, msg = "EVENT_MASK_THERAPY_STATE_CHANGE")
//            updateUserAvatar(UserManager.getAccountInfo())
//        }
        LiveEventBus.get<Any>(RefreshBindUserReceiver.EVENT_REFRESH_BIND_USER).observe(this){
            Logger.d(TAG, msg = "EVENT_REFRESH_BIND_USER")
            readHomeVM.getReadHomoProfile()
        }
        adaVM.authCodeLiveData.observe(this){
            Logger.d(TAG, msg = "authCodeLiveData = $it")
            ivAi.isVisible = !TextUtils.isEmpty(it?.authCode) && !TextUtils.isEmpty(it?.chatUrl)
        }
    }

    override fun initData() {
        super.initData()
        updateDeviceInfo()
        readHomeVM.getReadHomoProfile()
        treatmentVM.getCurrentTreatment()
        adaVM.getAuthCode(DeviceManager.getDeviceSn())
    }

    private fun initListener() {
        ivMore.setOnSingleClickListener {
            val readHomeMenu = ReadHomeMenu(mActivity)
            readHomeMenu.showAsDropDown(ivMore,0,
                10.dp2px(mActivity), Gravity.END)
            readHomeMenu.show()
            readHomeMenu.onVersionClick = {
                updateVM.getAppUpdateInfo()
            }
        }
        ivHelpCenter.setOnSingleClickListener {
            startActivity(HelpCenterActivity.createIntent(mActivity))
        }
        ivCalibration.setOnSingleClickListener {
            val isBind = UserManager.isBind()
            Logger.d(TAG, msg = "Calibration click isBind = $isBind")
            if (isBind){
                startCalibration()
            }else{
                (mActivity as? ReadHomeMainActivity)?.toBind()
            }
        }
        clUserInfo.setOnSingleClickListener {
            val isBind = UserManager.isBind()
            Logger.d(TAG, msg = "clUserInfo click isBind = $isBind")
            if (!isBind){
                (mActivity as? ReadHomeMainActivity)?.toBind()
            }
        }
        tvUserUnbind.setOnSingleClickListener {
            (mActivity as? ReadHomeMainActivity)?.toBind()
        }
        ivParamSetting.setOnSingleClickListener {
            startActivity(ParamSettingActivity.createIntent(mActivity, ParamSettingActivity.MODE_MYOPIA))
        }
        ivAi.setOnSingleClickListener {
            startActivity(ChatWebActivity.createIntent(mActivity))
        }
    }

    private fun updateTreatmentModule(modules:List<ReadHomeMode>?){
        if (!modules.isNullOrEmpty()){
            rvTreatmentModule.isVisible = true
            clNetworkException.isVisible = false
            val filterModules = modules.filter { module ->
                module.moduleEnable == true &&
                        (module.moduleKey == ReadHomeMode.MYOPIA_P_C || module.moduleKey == ReadHomeMode.MYOPIA_R_T)
            }
            mModuleAdapter.setReadHomeModuleData(filterModules,childFragmentManager)
            mModuleAdapter.notifyDataSetChanged()
        }else{
            rvTreatmentModule.isVisible = false
            clNetworkException.isVisible = true
        }
    }

    private fun updateUserAvatar(accountInfo: AccountInfo?){
        if (UserManager.isBind()){
            viewUserBg.isSelected = true
            if (accountInfo?.gender == Gender.MALE.value){
                ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_male_normal)
            }else{
                ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_female_normal)
            }
        }else{
            viewUserBg.isSelected = false
            ivUserAvatar.setImageResource(R.drawable.icon_user_avatar_unbound)
        }
    }

    private fun updateUserName(accountInfo: AccountInfo?){
        if (UserManager.isBind()){
            tvUserName.text = accountInfo?.accountName?:""
        }else{
            tvUserName.text = getString(R.string.str_unbound)
        }
    }

    private fun updateDeviceInfo(){
        ivParamSetting.isVisible = DeviceManager.isDemoMode()
        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(mActivity,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivHomeLogo)
        }else{
            ivHomeLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }

    /**
     * 处理当前疗程弹窗
     */
    private fun handlerTreatmentDialog(treatmentInfo: TreatmentInfo){
        val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager
        dialogTaskManager?.let {
            TreatmentManager.showTreatmentExpirationRemindDialog(mActivity,dialogTaskManager,treatmentInfo,7)
        }
        when(treatmentInfo.courseStatus){
            TreatmentStatus.INACTIVE.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentActivationRemindDialog(mActivity,dialogTaskManager,treatmentInfo, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
            }
            TreatmentStatus.PENDING.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentPauseDialog(mActivity,dialogTaskManager, confirmClick = {
                        TreatmentManager.showTreatmentActivationDialog(mActivity,dialogTaskManager,treatmentInfo,true, confirmClick = {
                            val phone = UserManager.getAccountInfo()?.phone?:""
                            val courseId = treatmentInfo.courseId?:""
                            treatmentVM.activationTreatment(courseId, phone)
                        })
                    })
                }
            }
            TreatmentStatus.COMPLETED.called ->{
                dialogTaskManager?.let {
                    TreatmentManager.showTreatmentExpirationDialog(mActivity,dialogTaskManager,treatmentInfo)
                }
            }
        }
    }

    //启动眼动校准
    private fun startCalibration(){
        calibrationLauncher.launch(CalibrationActivity.createIntent(mActivity, CalibrationMode.CALIBRATION,false))
    }

    /**
     * 开启刷新绑定用户信息定时任务
     */
    private fun startRefreshBindUser(){
        cancelRefreshBindUser()
        // 获取AlarmManager实例
        val alarmManager = mActivity.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        // 创建一个Intent，指向ArchiveRefreshBroadcastReceiver
        val intent = Intent(mActivity, RefreshBindUserReceiver::class.java)
        intent.action = RefreshBindUserReceiver.ACTION_REFRESH_BIND_USER
        // 创建一个PendingIntent，用于在闹钟触发时发送广播
        val pendingIntent = PendingIntent.getBroadcast(mActivity, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        // 设置定时任务时间为每天凌晨1点
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY,1)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)

        // 如果当前时间已经过了今天的1点，设置为明天的1点
        if (calendar.timeInMillis < System.currentTimeMillis()) {
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }

        // 设置重复的定时任务，每天1点执行
        alarmManager?.setRepeating(
            AlarmManager.RTC_WAKEUP,calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY,pendingIntent)
    }

    /**
     * 取消刷新绑定用户信息定时任务
     */
    private fun cancelRefreshBindUser(){
        val alarmManager = mActivity.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        val intent = Intent(mActivity, RefreshBindUserReceiver::class.java)
        intent.action = RefreshBindUserReceiver.ACTION_REFRESH_BIND_USER
        val pendingIntent = PendingIntent.getBroadcast(mActivity, 0, intent, PendingIntent.FLAG_IMMUTABLE)
        pendingIntent?.cancel()
        alarmManager?.cancel(pendingIntent)
    }

    override fun onResume() {
        super.onResume()
        //检查升级
        updateVM.getAppUpdateInfo()
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onPause() {
        super.onPause()
        PlayManager.pause()
        mActivity.unbindService(serviceConnection)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cancelRefreshBindUser()
    }

    fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

    private fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage msg = ${msg.what}")
        when(msg.what){
            GazeConstants.MSG_GAZE_TRACKING_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_GAZE_TRACKING_STATE state = $state")
            }
            GazeConstants.MSG_APPLIED_CURE_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_CURE_STATE state = $state")
                myopiaControlVM.setMTState(state)
                showMyopiaControlStateDialog(state)
            }
            GazeConstants.MSG_UPDATE_TREATMENT_DURATION ->{
                val treatmentDuration = msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)
                Logger.d(TAG, msg = "MSG_UPDATE_TREATMENT_DURATION treatmentDuration = $treatmentDuration")
                myopiaControlVM.setMyopiaControlInfo(myopiaControlVM.myopiaControlInfo?.apply {
                    trainingDuration = treatmentDuration
                })
            }
            GazeConstants.MSG_REPORT_TREATMENT_RESULT ->{
                val resultJson = msg.data?.getString(GazeConstants.KEY_REPORT_CURE_RESULT)
                Logger.d(TAG, msg = "MSG_REPORT_TREATMENT_RESULT resultJson = $resultJson")
                val result = try {
                    mGson.fromJson(resultJson, CureInfo::class.java)
                }catch (e:Exception){
                    if (BuildConfig.DEBUG){
                        e.printStackTrace()
                    }
                    null
                }
                if (result != null){
                    myopiaControlVM.setMyopiaControlInfo(result)
                }
            }
        }
    }

    private fun showMyopiaControlStateDialog(isStart:Boolean){
        val myopiaControlStateDialog = MyopiaControlStateDialog(mActivity)
        myopiaControlStateDialog.show()
        myopiaControlStateDialog.setData(isStart)
    }

}