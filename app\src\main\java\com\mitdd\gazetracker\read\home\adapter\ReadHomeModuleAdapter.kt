package com.mitdd.gazetracker.read.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.FrameLayout
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.dp2px
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.read.home.bean.ReadHomeMode
import com.mitdd.gazetracker.read.home.myopia.MyopiaControlFragment
import com.mitdd.gazetracker.read.home.train.MyopiaTrainFragment

/**
 * FileName: ReadHomeModuleAdapter
 * Author by lilin,Date on 2025/1/20 17:50
 * PS: Not easy to write code, please indicate.
 * 阅读家庭版首页模块Adapter
 */
class ReadHomeModuleAdapter: RecyclerView.Adapter<ReadHomeModuleAdapter.ReadHomeModuleHolder>() {

    private val TAG = ReadHomeModuleAdapter::class.java.simpleName

    private var readHomeModules: MutableList<ReadHomeMode> = mutableListOf()
    private var fragmentManager: FragmentManager? = null

    fun setReadHomeModuleData(data:List<ReadHomeMode>, fragmentManager: FragmentManager){
        readHomeModules.clear()
        readHomeModules.addAll(data)
        this.fragmentManager = fragmentManager
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReadHomeModuleHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_treatment_module, parent, false)
        return ReadHomeModuleHolder(view)
    }

    override fun getItemCount(): Int {
        return readHomeModules.size
    }

    override fun onBindViewHolder(holder: ReadHomeModuleHolder, position: Int) {
        if (position in readHomeModules.indices){
            holder.bind(readHomeModules[position],position)
        }
    }

    inner class ReadHomeModuleHolder(itemView: View) : RecyclerView.ViewHolder(itemView){
        private val flModuleRoot: FrameLayout = itemView.findViewById(R.id.fl_module_root)

        fun bind(module: ReadHomeMode, position:Int){
            flModuleRoot.id += position
            val isFull = readHomeModules.size == 1
            val rootParams = flModuleRoot.layoutParams as LayoutParams
            if (isFull){
                rootParams.width = LayoutParams.MATCH_PARENT
            }else{
                rootParams.width = 452.dp2px(itemView.context)
            }
            flModuleRoot.layoutParams = rootParams
            when(module.moduleKey){
                ReadHomeMode.MYOPIA_P_C ->{
                    //近视防控模式
                    fragmentManager?.let {
                        val beginTransaction = it.beginTransaction()
                        beginTransaction.replace(flModuleRoot.id, MyopiaControlFragment.newInstance(module.moduleName?:"",isFull))
                        beginTransaction.commitAllowingStateLoss()
                    }
                }
                ReadHomeMode.MYOPIA_R_T  ->{
                    //近视和阅读训练模式
                    fragmentManager?.let {
                        val beginTransaction = it.beginTransaction()
                        beginTransaction.replace(flModuleRoot.id, MyopiaTrainFragment.newInstance(module.moduleName?:"",isFull))
                        beginTransaction.commitAllowingStateLoss()
                    }
                }
            }
        }
    }
}