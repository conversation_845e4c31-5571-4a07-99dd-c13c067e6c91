package com.mitdd.gazetracker.read.home.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import retrofit2.http.GET

/**
 * FileName: MyopiaControlApiService
 * Author by lilin,Date on 2025/1/20 16:18
 * PS: Not easy to write code, please indicate.
 */
interface MyopiaControlApiService {

    /**
     * 获取近视防控信息
     */
    @GET("dt/api/train/v1/today/myopia-p-c")
    suspend fun getMyopiaControlInfo(): ApiResponse<CureInfo>

}