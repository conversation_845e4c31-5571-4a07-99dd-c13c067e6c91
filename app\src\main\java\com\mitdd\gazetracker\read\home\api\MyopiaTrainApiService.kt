package com.mitdd.gazetracker.read.home.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.read.home.bean.MyopiaTrainInfo
import retrofit2.http.GET

/**
 * FileName: MyopiaTrainApiService
 * Author by lilin,Date on 2025/1/20 17:24
 * PS: Not easy to write code, please indicate.
 */
interface MyopiaTrainApiService {

    /**
     * 获取近视防控训练信息
     */
    @GET("dt/api/train/v1/today/myopia-r-t")
    suspend fun getMyopiaTrainInfo(): ApiResponse<MyopiaTrainInfo>

}