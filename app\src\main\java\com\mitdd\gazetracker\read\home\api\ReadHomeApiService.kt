package com.mitdd.gazetracker.read.home.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.read.home.bean.ReadHomeProfile
import retrofit2.http.GET

/**
 * FileName: ReadHomeApiService
 * Author by lilin,Date on 2025/1/20 14:49
 * PS: Not easy to write code, please indicate.
 */
interface ReadHomeApiService {

    /**
     * 获取阅读家庭版配置
     */
    @GET("dt/api/device/v1/profile/r-home")
    suspend fun getReadHomoProfile(): ApiResponse<ReadHomeProfile>

}