package com.mitdd.gazetracker.read.home.bean

import android.os.Parcelable
import com.mitdd.gazetracker.medicalhome.bean.TrainCategory
import kotlinx.parcelize.Parcelize

/**
 * FileName: MyopiaTrainInfo
 * Author by lilin,Date on 2025/1/20 17:25
 * PS: Not easy to write code, please indicate.
 * 近视防控训练信息
 */
@Parcelize
data class MyopiaTrainInfo(
    //是否允许训练
    var allowTraining:Boolean? = null,
    //是否显示为分类
    var isCategory:Boolean? = null,
    //训练列表
    var list:List<TrainCategory>? = null,
    //计划训练时长 秒
    var plannedDuration:Int? = null,
    //已训练时长 秒
    var trainingDuration:Int? = null,
) : Parcelable
