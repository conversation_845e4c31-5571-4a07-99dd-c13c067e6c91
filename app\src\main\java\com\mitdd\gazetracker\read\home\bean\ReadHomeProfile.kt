package com.mitdd.gazetracker.read.home.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: ReadHomeProfile
 * Author by lilin,Date on 2025/1/20 14:51
 * PS: Not easy to write code, please indicate.
 * 阅读家庭版配置
 */
@Parcelize
data class ReadHomeProfile(
    //模块列表
    var modules:List<ReadHomeMode>? = null,
) : Parcelable

@Parcelize
data class ReadHomeMode(
    //模块开通状态
    var moduleEnable:Boolean? = null,
    //模块代号
    var moduleKey:String? = null,
    //模块名称
    var moduleName:String? = null,
) : Parcelable{
    companion object{
        //近视防控模式
        const val MYOPIA_P_C = "myopia-p-c"
        //近视和阅读训练模式
        const val MYOPIA_R_T = "myopia-r-t"
    }
}