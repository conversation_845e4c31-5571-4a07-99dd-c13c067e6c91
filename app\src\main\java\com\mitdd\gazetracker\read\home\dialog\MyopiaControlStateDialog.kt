package com.mitdd.gazetracker.read.home.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: MyopiaControlStateDialog
 * Author by lilin,Date on 2025/1/20 16:29
 * PS: Not easy to write code, please indicate.
 * 近视防控开启或关闭通知弹窗
 */
class MyopiaControlStateDialog(context: Context) : BaseCommonDialog(context) {

    private val tvTitle by id<TextView>(R.id.tv_title)
    private val tvSubtitle by id<TextView>(R.id.tv_subtitle)
    private val ivClose by id<ImageView>(R.id.iv_close)
    private val tvOk by id<TextView>(R.id.tv_ok)
    private val ivState by id<ImageView>(R.id.iv_state)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置自定义的布局
        setContentView(R.layout.dialog_myopia_control_state)

        initView()
    }

    private fun initView() {
        ivClose.setOnSingleClickListener {
            dismiss()
        }
        tvOk.setOnSingleClickListener {
            dismiss()
        }
    }

    fun setData(isStart:Boolean){
        if (isStart){
            setTitle(context.getString(R.string.str_airdoc_myopia_control_on))
            setSubtitle(context.getString(R.string.str_please_maintain_correct_posture_watch_content))
            setStateIcon(R.drawable.icon_myopia_control_state_start)
//            PlayManager.playRawMedia(context, RawMedia(R.raw.mask_therapy_started_please_wear_glasses))
        }else{
            setTitle(context.getString(R.string.str_airdoc_myopia_control_off))
            setSubtitle(context.getString(R.string.str_recommended_enable_myopia_control_mode_watching))
            setStateIcon(R.drawable.icon_myopia_control_state_stop)
//            PlayManager.playRawMedia(context, RawMedia(R.raw.mask_therapy_closed))
        }
    }

    fun setTitle(title:String){
        tvTitle.text = title
    }

    fun setSubtitleBySpannable(subtitle:String){
        val spannableString = SpannableString(subtitle)
        spannableString.setSpan(ForegroundColorSpan(Color.parseColor("#EB4E89")),3,7, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        tvSubtitle.text = spannableString
    }

    fun setSubtitle(subtitle:String){
        tvSubtitle.text = subtitle
    }

    fun setStateIcon(res:Int){
        ivState.setImageResource(res)
    }

    override fun isFullScreen(): Boolean {
        return true
    }

}