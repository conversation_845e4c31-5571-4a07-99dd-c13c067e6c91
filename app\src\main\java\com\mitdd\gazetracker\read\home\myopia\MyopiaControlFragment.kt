package com.mitdd.gazetracker.read.home.myopia

import android.app.Activity
import android.content.pm.ApplicationInfo
import android.os.Bundle
import android.os.Message
import android.text.TextUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.CustomItemDecoration
import com.airdoc.component.common.utils.PackageUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.common.dialog.task.NotificationDialogTask
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.medicalhome.TimeProgress
import com.mitdd.gazetracker.medicalhome.bean.CommonApp
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType.ADD_APP
import com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType.APP
import com.mitdd.gazetracker.medicalhome.mask.CommonAppAdapter
import com.mitdd.gazetracker.medicalhome.mask.MaskTherapyFragment
import com.mitdd.gazetracker.medicalhome.mask.SelectCommonAppActivity
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.read.home.ReadHomeMainActivity
import com.mitdd.gazetracker.read.home.ReadHomeMainFragment
import com.mitdd.gazetracker.read.home.vm.MyopiaControlViewModel
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.UserPreference
import com.mitdd.gazetracker.user.bean.Gender

/**
 * FileName: MyopiaControlFragment
 * Author by lilin,Date on 2025/1/20 16:02
 * PS: Not easy to write code, please indicate.
 * 近视防控Fragment
 */
class MyopiaControlFragment : BaseCommonFragment() {

    companion object{
        private val TAG = MyopiaControlFragment::class.java.simpleName
        const val INPUT_PARAM_MODULE_NAME = "moduleName"
        const val INPUT_PARAM_IS_FULL = "isFull"

        fun newInstance(moduleName:String,isFull:Boolean): MyopiaControlFragment {
            val fragment = MyopiaControlFragment()
            val args = Bundle()
            args.putString(INPUT_PARAM_MODULE_NAME,moduleName)
            args.putBoolean(INPUT_PARAM_IS_FULL,isFull)
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_myopia_control
    }

    private val tvDigitalMyopiaControl by id<TextView>(R.id.tv_digital_myopia_control)
    private val switchMyopiaControl by id<SwitchCompat>(R.id.switch_myopia_control)
    private val timeProgress by id<TimeProgress>(R.id.treatment_time_progress)
    private val rvCommonApp by id<RecyclerView>(R.id.rv_common_application)
    private val ivAddCommonApp by id<ImageView>(R.id.iv_add_common_app)
    private val tvAddCommonApp by id<TextView>(R.id.tv_add_common_app)

    //选择常用应用
    private var selectCommonAppLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val applicationInfo = data?.getParcelableExtra<ApplicationInfo>(SelectCommonAppActivity.OUTPUT_PARAM_COMMON_APP)
            if (applicationInfo != null){
                addCommonApp(applicationInfo)
            }
        }
    }

    private val myopiaControlVM by activityViewModels<MyopiaControlViewModel>()

    private var commonAppList: MutableList<CommonApp> = mutableListOf()
    private val commonAppAdapter = CommonAppAdapter(commonAppList)

    private val gson = Gson()

    //模块名称
    private var mModuleName = ""
    private var isFUll = false

    override fun initParam() {
        super.initParam()
        val arguments = arguments
        if (arguments != null){
            mModuleName = arguments.getString(MaskTherapyFragment.INPUT_PARAM_MODULE_NAME,getString(R.string.str_digital_masking_therapy))
            isFUll = arguments.getBoolean(MaskTherapyFragment.INPUT_PARAM_IS_FULL)
        }
    }

    override fun initView() {
        super.initView()

        initListener()

        val accountInfo = UserManager.getAccountInfo()
        when(accountInfo?.gender){
            Gender.MALE.value ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_male)
            }
            else ->{
                timeProgress.getThumbView().setImageResource(R.drawable.icon_seekbar_thumb_female)
            }
        }
        timeProgress.getSeekBar().progressDrawable = ContextCompat.getDrawable(mActivity,R.drawable.seekbar_mask_therapy_duration_progress_drawable)

        val spanCount = if (isFUll) 8 else 4
        rvCommonApp.layoutManager = GridLayoutManager(mActivity, spanCount)
        rvCommonApp.addItemDecoration(CustomItemDecoration(20.dp2px(mActivity), 10.dp2px(mActivity), spanCount))
        rvCommonApp.adapter = commonAppAdapter

        tvDigitalMyopiaControl.text = mModuleName
    }

    override fun initObserver() {
        super.initObserver()
        myopiaControlVM.myopiaControlInfoLiveData.observe(this){
            updatePlannedDuration()
            updateTreatmentDuration()
        }
        LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_CURE).observe(this){
            Logger.d(TAG, msg = "EVENT_SWITCH_CURE")
            switchMyopiaControl(it)
        }
        myopiaControlVM.mtStateLiveData.observe(this){
            switchMyopiaControl.isChecked = it
        }
    }

    override fun initData() {
        super.initData()
        myopiaControlVM.getMyopiaControlInfo()
        loadCommonApp()
    }

    private fun initListener(){
        switchMyopiaControl.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            switchMyopiaControl(isChecked)
        }
        commonAppAdapter.setItemClickListener(object : CommonAppAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                if (position in commonAppList.indices){
                    val commonApp = commonAppList[position]
                    when(commonApp.type){
                        APP -> {
                            val applicationInfo = commonApp.applicationInfo
                            if (applicationInfo != null){
                                val packageManager = mActivity.packageManager
                                val intent = packageManager.getLaunchIntentForPackage(applicationInfo.packageName)
                                if (intent != null){
                                    startActivity(intent)
                                }
                            }
                        }
                        ADD_APP -> {
                            selectCommonAppLauncher.launch(
                                SelectCommonAppActivity.createIntent(mActivity)
                            )
                        }
                    }
                }
            }

            override fun onItemDelete(position: Int) {
                if (position in commonAppList.indices){
                    removeCommonApp(commonAppList[position])
                }
            }
        })
        ivAddCommonApp.setOnSingleClickListener {
            selectCommonAppLauncher.launch(
                SelectCommonAppActivity.createIntent(mActivity)
            )
        }
    }

    private fun showNotificationDialog(prompt:String,confirmClick:(() -> Unit)? = null){
        (mActivity as? ReadHomeMainActivity)?.mainDialogTaskManager?.addTask(NotificationDialogTask(mActivity).apply {
            setConfirmClick(confirmClick)
            setMessage(prompt)
        })
    }

    private fun loadCommonApp(){
        commonAppList.clear()
        //当前应用包名
        val curPackageName = mActivity.packageName
        val packageStr = MMKVManager.decodeString(UserPreference.COMMON_APPLICATION)?:""
        val packageNames = if (!TextUtils.isEmpty(packageStr)){
            packageStr.split(",").filter {
                !TextUtils.isEmpty(it)
            }
        }else{
            emptyList()
        }
        if (packageNames.isNotEmpty()){
            val packageManager = mActivity.packageManager
            val installedApps = packageManager.getInstalledApplications(0).filter {
                packageManager.getLaunchIntentForPackage(it.packageName) != null
            }
            for (packageName in packageNames){
                if (packageName != curPackageName){
                    for (installedApp in installedApps){
                        if (packageName == installedApp.packageName){
                            commonAppList.add(CommonApp(APP,installedApp))
                            continue
                        }
                    }
                }
            }
            commonAppList.add(CommonApp(ADD_APP))
            commonAppAdapter.notifyDataSetChanged()
            rvCommonApp.isVisible = true
            ivAddCommonApp.isVisible = false
            tvAddCommonApp.isVisible = false
        }else{
            rvCommonApp.isVisible = false
            ivAddCommonApp.isVisible = true
            tvAddCommonApp.isVisible = true
        }
        updateCommonAppRecord()
    }

    private fun addCommonApp(applicationInfo: ApplicationInfo){
        for (app in commonAppList){
            val info = app.applicationInfo
            if (info != null && info.packageName == applicationInfo.packageName){
                return
            }
        }
        rvCommonApp.isVisible = true
        ivAddCommonApp.isVisible = false
        tvAddCommonApp.isVisible = false
        commonAppList.add(0, CommonApp(APP,applicationInfo))
        val lastCommonApp = commonAppList[commonAppList.size - 1]
        if (lastCommonApp.type != ADD_APP){
            commonAppList.add(CommonApp(ADD_APP))
        }
        commonAppAdapter.notifyDataSetChanged()
        updateCommonAppRecord()
    }

    private fun removeCommonApp(commonApp: CommonApp){
        if (commonApp.type == APP){
            commonAppList.remove(commonApp)
            commonAppAdapter.notifyDataSetChanged()
            updateCommonAppRecord()
        }
    }

    private fun updateCommonAppRecord(){
        val packageNames = StringBuilder()
        for (app in commonAppList){
            val applicationInfo = app.applicationInfo
            if (applicationInfo != null){
                if (packageNames.isEmpty()){
                    packageNames.append(applicationInfo.packageName)
                }else{
                    packageNames.append(",")
                    packageNames.append(applicationInfo.packageName)
                }
            }
        }
        MMKVManager.encodeString(UserPreference.COMMON_APPLICATION, packageNames.toString())
    }

    /**
     * 更新计划治疗时长
     */
    private fun updatePlannedDuration(){
        timeProgress.getPlannedDurationView().isVisible = myopiaControlVM.myopiaControlInfo != null
        timeProgress.setPlannedDuration(myopiaControlVM.plannedDuration)
    }

    /**
     * 更新已治疗时长
     */
    private fun updateTreatmentDuration(){
        timeProgress.getTrainDurationView().isVisible = myopiaControlVM.myopiaControlInfo != null
        timeProgress.setTreatmentDuration(myopiaControlVM.treatmentDuration)
    }

    private fun switchMyopiaControl(state:Boolean){
        Logger.d(TAG, msg = "switchMyopiaControl state = $state")
        if (!state){
            switchMyopiaControl.isChecked = false
            (parentFragment as? ReadHomeMainFragment)?.sendMessageToService(
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_APPLIED_CURE
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                },
                Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                }
            )
            return
        }
        val isBind = UserManager.isBind()
        if (!isBind){
            switchMyopiaControl.isChecked = false
            showNotificationDialog(getString(R.string.str_please_bind_user_then_start_masking_therapy)){
                (mActivity as? ReadHomeMainActivity)?.toBind()
            }
            return
        }
        val isFinishUp = myopiaControlVM.isFinishUp
        val plannedDuration = myopiaControlVM.plannedDuration
        val treatmentDuration = myopiaControlVM.treatmentDuration
        if (isFinishUp || plannedDuration <= treatmentDuration){
            switchMyopiaControl.isChecked = false
            showNotificationDialog(getString(R.string.str_treatment_has_been_completed_today))
            return
        }
        (parentFragment as? ReadHomeMainFragment)?.sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_CURE
                data.putInt(GazeConstants.KEY_PLANNED_DURATION,plannedDuration)
                data.putInt(GazeConstants.KEY_TREATMENT_DURATION,treatmentDuration)
                data.putString(GazeConstants.KEY_REPORT_URL,"dt/api/train/v1/occlusion-therapy/event/report")
                data.putString(GazeConstants.KEY_BASE_URL, UrlConfig.MAIN_DOMAIN)
                data.putString(GazeConstants.KEY_REPORT_PARAM, "")
                //添加headers
                val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)
                data.putString(GazeConstants.KEY_REPORT_HEADER, gson.toJson(HashMap<String, String>().apply {
                    put("X-Rom-Version",DeviceManager.getOSVersion())
                    put("X-Device-Sn",DeviceManager.getDeviceSn())
                    put("X-App-Version",PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName))
                    put("X-Device-Mode",DeviceManager.getProductModel())
                    put("X-Airdoc-Client","d409e47e-95e6-41fc-868c-e5748957d546")
                    put("Accept-Language","${locale.language}-${locale.country}")
                }))
            }
        )
    }

    override fun onResume() {
        super.onResume()
        switchMyopiaControl.postDelayed({
            switchMyopiaControl.isChecked = DeviceManager.getMaskTherapyState()
        },500)
    }

}