package com.mitdd.gazetracker.read.home.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.read.home.api.MyopiaControlApiService

/**
 * FileName: MyopiaControlRepository
 * Author by lilin,Date on 2025/1/20 16:22
 * PS: Not easy to write code, please indicate.
 */
class MyopiaControlRepository : BaseRepository() {

    /**
     * 获取近视防控信息
     */
    suspend fun getMyopiaControlInfo(): ApiResponse<CureInfo> {
        return executeHttp {
            MainRetrofitClient.createService(MyopiaControlApiService::class.java).getMyopiaControlInfo()
        }
    }
}