package com.mitdd.gazetracker.read.home.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.read.home.api.MyopiaTrainApiService
import com.mitdd.gazetracker.read.home.bean.MyopiaTrainInfo

/**
 * FileName: MyopiaTrainRepository
 * Author by lilin,Date on 2025/1/20 17:22
 * PS: Not easy to write code, please indicate.
 */
class MyopiaTrainRepository : BaseRepository() {

    /**
     * 获取近视防控训练信息
     */
    suspend fun getMyopiaTrainInfo(): ApiResponse<MyopiaTrainInfo> {
        return executeHttp {
            MainRetrofitClient.createService(MyopiaTrainApiService::class.java).getMyopiaTrainInfo()
        }
    }

}