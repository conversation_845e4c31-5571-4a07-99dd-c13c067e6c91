package com.mitdd.gazetracker.read.home.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.read.home.api.ReadHomeApiService
import com.mitdd.gazetracker.read.home.bean.ReadHomeProfile

/**
 * FileName: ReadHomoRepository
 * Author by lilin,Date on 2025/1/20 15:07
 * PS: Not easy to write code, please indicate.
 */
class ReadHomeRepository : BaseRepository() {

    /**
     * 获取阅读家庭版配置
     */
    suspend fun getReadHomoProfile(): ApiResponse<ReadHomeProfile> {
        return executeHttp {
            MainRetrofitClient.createService(ReadHomeApiService::class.java).getReadHomoProfile()
        }
    }
}