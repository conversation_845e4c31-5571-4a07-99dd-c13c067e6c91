package com.mitdd.gazetracker.read.home.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.gaze.MaskManager
import com.mitdd.gazetracker.gaze.bean.CureInfo
import com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye
import com.mitdd.gazetracker.read.home.repository.MyopiaControlRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: MyopiaControlViewModel
 * Author by lilin,Date on 2025/1/20 16:17
 * PS: Not easy to write code, please indicate.
 * 近视防控ViewModel
 */
class MyopiaControlViewModel : ViewModel() {

    companion object{
        private val TAG = MyopiaControlViewModel::class.java.name
    }

    private val myopiaControlRepository by lazy { MyopiaControlRepository() }

    //近视防控信息
    val myopiaControlInfoLiveData = MutableLiveData<CureInfo?>()
    val myopiaControlInfo get() = myopiaControlInfoLiveData.value
    //当前治疗时长，单位秒
    val treatmentDuration get() = myopiaControlInfo?.trainingDuration?:0
    //计划治疗时长，单位秒
    val plannedDuration get() = myopiaControlInfo?.plannedDuration?:0
    //弱势眼 左left 右right
    val eyePosition get() = myopiaControlInfo?.position?: AmblyopicEye.LEFT.value
    //是否已完成今日治疗
    val isFinishUp get() = myopiaControlInfo?.isFinishUp?:false
    //遮盖疗法状态 true表示开启，false表示关闭
    val mtStateLiveData = MutableLiveData<Boolean>()

    /**
     * 获取近视防控信息
     */
    fun getMyopiaControlInfo(){
        viewModelScope.launch {
            MutableStateFlow(myopiaControlRepository.getMyopiaControlInfo()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getMyopiaControlInfo onSuccess")
                    setMyopiaControlInfo(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getMyopiaControlInfo onDataEmpty")
                    setMyopiaControlInfo(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getMyopiaControlInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    setMyopiaControlInfo(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getMyopiaControlInfo onError = $it")
                    setMyopiaControlInfo(null)
                }
            }
        }
    }

    fun setMyopiaControlInfo(cureInfo: CureInfo?){
        //保存遮盖疗法参数
        MaskManager.setCoverChannel(cureInfo?.blurChannel)
        MaskManager.setCoverMode(cureInfo?.blurMode)
        MaskManager.setCoverArea(cureInfo?.blurRadius)
        MaskManager.setCoverRange(cureInfo?.blurSigma)

        myopiaControlInfoLiveData.postValue(cureInfo)
    }

    /**
     * 设置遮盖疗法状态
     * @param state true表示开启，false表示关闭
     */
    fun setMTState(state:Boolean){
        mtStateLiveData.postValue(state)
    }

}