package com.mitdd.gazetracker.read.home.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.read.home.bean.MyopiaTrainInfo
import com.mitdd.gazetracker.read.home.repository.MyopiaTrainRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: MyopiaTrainViewModel
 * Author by lilin,Date on 2025/1/20 17:21
 * PS: Not easy to write code, please indicate.
 * 近视防控训练ViewModel
 */
class MyopiaTrainViewModel : ViewModel() {

    companion object{
        private val TAG = MyopiaTrainViewModel::class.java.name
    }

    private val myopiaTrainRepository by lazy { MyopiaTrainRepository() }

    //近视防控训练信息
    val myopiaTrainInfoLiveData = MutableLiveData<MyopiaTrainInfo?>()
    val myopiaTrainInfo get() = myopiaTrainInfoLiveData.value
    //当前治疗时长，单位秒
    val treatmentDuration get() = myopiaTrainInfo?.trainingDuration?:0
    //计划治疗时长，单位秒
    val plannedDuration get() = myopiaTrainInfo?.plannedDuration?:0
    //是否允许训练
    val allowTraining get() = myopiaTrainInfo?.allowTraining?:false

    /**
     * 获取近视防控训练信息
     */
    fun getMyopiaTrainInfo(){
        viewModelScope.launch {
            MutableStateFlow(myopiaTrainRepository.getMyopiaTrainInfo()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getMyopiaTrainInfo onSuccess")
                    myopiaTrainInfoLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getMyopiaTrainInfo onDataEmpty")
                    myopiaTrainInfoLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getMyopiaTrainInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    myopiaTrainInfoLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getMyopiaTrainInfo onError = $it")
                    myopiaTrainInfoLiveData.postValue(null)
                }
            }
        }
    }
}