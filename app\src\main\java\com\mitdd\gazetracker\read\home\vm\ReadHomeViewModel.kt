package com.mitdd.gazetracker.read.home.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.read.home.bean.ReadHomeProfile
import com.mitdd.gazetracker.read.home.repository.ReadHomeRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: ReadHomoViewModel
 * Author by lilin,Date on 2025/1/20 15:09
 * PS: Not easy to write code, please indicate.
 */
class ReadHomeViewModel : ViewModel() {

    companion object{
        private val TAG = ReadHomeViewModel::class.java.name
    }

    private val readHomeRepository by lazy { ReadHomeRepository() }

    //阅读家庭版配置信息
    val readHomeProfileLiveData = MutableLiveData<ReadHomeProfile?>()

    /**
     * 获取家庭版配置信息
     */
    fun getReadHomoProfile(){
        viewModelScope.launch {
            MutableStateFlow(readHomeRepository.getReadHomoProfile()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getReadHomoProfile onSuccess")
                    readHomeProfileLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getReadHomoProfile onDataEmpty")
                    readHomeProfileLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getReadHomoProfile onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    readHomeProfileLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getReadHomoProfile onError = $it")
                    readHomeProfileLiveData.postValue(null)
                }
            }
        }
    }

}