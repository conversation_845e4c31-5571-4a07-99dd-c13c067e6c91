package com.mitdd.gazetracker.tsc

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.view.View
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.download.DownloadUtils
import com.airdoc.component.common.download.IHttpListener
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.airdoc.videobox.MultiClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.tsc.vm.TscViewModel
import kotlinx.coroutines.launch
import okhttp3.Request
import java.io.File

/**
 * FileName: TscMainActivity
 * Author by lilin,Date on 2025/3/6 17:41
 * PS: Not easy to write code, please indicate.
 */
class TscMainActivity : GTBaseActivity() {

    companion object{
        private val TAG = TscMainActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, TscMainActivity::class.java)
            return intent
        }
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val progressUpdate by id<ProgressBar>(R.id.progress_update)
    private val tvStarted by id<TextView>(R.id.tv_started)
    private val viewConfig by id<View>(R.id.view_config)

    private val tscVM by viewModels<TscViewModel>()

    private var mTscExists = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_tsc_main)

        initView()
        initObserver()
        initData()
    }

    private fun initView(){
        initListener()

        mTscExists = PackageUtils.checkAppExistsByPackageName(this, "com.airdoc.aiproapp")
        Logger.d(TAG, msg = "initData tscExists = $mTscExists")
        if (!mTscExists){
            progressUpdate.progress = 0
            tvStarted.text = getString(R.string.str_upgrading)
            tvStarted.isEnabled = false
        }else{
            progressUpdate.progress = 100
            tvStarted.text = getString(R.string.str_get_started)
            tvStarted.isEnabled = true
        }

        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(this,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivLogo)
        }
    }

    private fun initListener(){
        tvStarted.setOnSingleClickListener {
            PackageUtils.startAppByPackageName(this,"com.airdoc.aiproapp")
        }
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                DeviceManager.startConfigActivity(this@TscMainActivity)
            }
        })
    }

    private fun initData(){
        if (!mTscExists){
            tscVM.getTscProfile()
        }
    }

    private fun initObserver(){
        tscVM.tscProfileLiveData.observe(this){
            Logger.d(TAG, msg = "initObserver tscProfileLiveData = $it")
            val apkUrl = it?.apkUrl
            val absolutePath = getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath?:""
            if (!mTscExists && !apkUrl.isNullOrEmpty() && absolutePath.isNotEmpty()){
                DownloadUtils.download(apkUrl,absolutePath,"Tsc.apk",object : IHttpListener.DownloadCallback{
                    override fun onPreDownload(request: Request, id: Int) {
                        Logger.d(TAG, msg = "Tsc onPreDownload id = $id")
                    }

                    override fun onAfterDownload(id: Int) {
                        Logger.d(TAG, msg = "Tsc onAfterDownload id = $id")
                    }

                    override fun onProgressUpdate(progress: Float, total: Long) {
                        lifecycleScope.launch {
                            progressUpdate(progress)
                        }
                    }

                    override fun onPostDownload(file: File?) {
                        lifecycleScope.launch {
                            postDownload(file)
                        }
                    }

                    override fun onErrorDownload(error: String) {
                        Logger.d(TAG, msg = "Tsc onErrorDownload error = $error")
                        lifecycleScope.launch {
                            tvStarted.isEnabled = false
                            Toast.makeText(this@TscMainActivity,getString(R.string.str_update_failed),Toast.LENGTH_SHORT).show()
                        }
                    }

                })
            }
        }
    }

    fun progressUpdate(progress: Float){
        progressUpdate.progress = (progress * 100).toInt()
    }

    fun postDownload(file: File?){
        val path = file?.absolutePath
        Logger.d(TAG, msg = "postDownload path = $path")
        if (!path.isNullOrEmpty()){
            PackageUtils.installApk(this@TscMainActivity,path)
            progressUpdate.progress = 100
            tvStarted.text = getString(R.string.str_get_started)
            tvStarted.isEnabled = true
            Toast.makeText(this@TscMainActivity,getString(R.string.str_update_successful),Toast.LENGTH_SHORT).show()
        }else{
            tvStarted.isEnabled = false
            Toast.makeText(this@TscMainActivity,getString(R.string.str_update_failed),Toast.LENGTH_SHORT).show()
        }
    }

}