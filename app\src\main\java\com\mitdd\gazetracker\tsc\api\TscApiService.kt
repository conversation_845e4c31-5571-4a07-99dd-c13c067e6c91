package com.mitdd.gazetracker.tsc.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.tsc.bean.TscProfile
import retrofit2.http.GET

/**
 * FileName: TscApiService
 * Author by lilin,Date on 2025/3/6 20:23
 * PS: Not easy to write code, please indicate.
 */
interface TscApiService {

    /**
     * 获取TSC配置信息
     */
    @GET("dt/api/device/v1/profile/tsc")
    suspend fun getTscProfile(): ApiResponse<TscProfile>

}