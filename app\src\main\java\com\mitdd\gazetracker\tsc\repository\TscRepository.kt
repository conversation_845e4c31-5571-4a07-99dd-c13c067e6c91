package com.mitdd.gazetracker.tsc.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.tsc.api.TscApiService
import com.mitdd.gazetracker.tsc.bean.TscProfile

/**
 * FileName: TscRepository
 * Author by lilin,Date on 2025/3/6 20:28
 * PS: Not easy to write code, please indicate.
 */
class TscRepository : BaseRepository() {

    /**
     * 获取TSC配置信息
     */
    suspend fun getTscProfile(): ApiResponse<TscProfile> {
        return executeHttp {
            MainRetrofitClient.createService(TscApiService::class.java).getTscProfile()
        }
    }
}