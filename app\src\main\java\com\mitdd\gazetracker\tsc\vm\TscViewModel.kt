package com.mitdd.gazetracker.tsc.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.tsc.bean.TscProfile
import com.mitdd.gazetracker.tsc.repository.TscRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: TscViewModel
 * Author by lilin,Date on 2025/3/6 20:29
 * PS: Not easy to write code, please indicate.
 */
class TscViewModel : ViewModel() {

    companion object{
        private val TAG = TscViewModel::class.java.name
    }

    private val tscRepository by lazy { TscRepository() }

    //tsc配置信息
    val tscProfileLiveData = MutableLiveData<TscProfile?>()

    /**
     * 获取TSC配置信息
     */
    fun getTscProfile(){
        viewModelScope.launch {
            MutableStateFlow(tscRepository.getTscProfile()).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getTscProfile onSuccess")
                    tscProfileLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getTscProfile onDataEmpty")
                    tscProfileLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getTscProfile onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    tscProfileLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getTscProfile onError = $it")
                    tscProfileLiveData.postValue(null)
                }
            }
        }
    }

}