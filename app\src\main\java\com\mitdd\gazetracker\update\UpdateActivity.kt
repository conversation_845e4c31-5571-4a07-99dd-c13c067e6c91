package com.mitdd.gazetracker.update

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.text.TextUtils
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import com.airdoc.component.common.download.DownloadUtils
import com.airdoc.component.common.download.IHttpListener
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.DeviceManager
import kotlinx.coroutines.launch
import okhttp3.Request
import java.io.File

/**
 * FileName: UpdateActivity
 * Author by lilin,Date on 2024/10/14 16:43
 * PS: Not easy to write code, please indicate.
 */
class UpdateActivity : GTBaseActivity() {

    companion object{
        private val TAG = UpdateActivity::class.java.simpleName

        const val INPUT_PARAM_URL = "url"

        fun createIntent(context: Context, url:String): Intent {
            val intent = Intent(context, UpdateActivity::class.java)
            intent.putExtra(INPUT_PARAM_URL,url)
            return intent
        }
    }

    private val tvTitle by id<TextView>(R.id.tv_title)
    private val progressBar by id<ProgressBar>(R.id.progressBar)

    private var mLoadUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_update)

        initParam()
        initView()
        initData()
    }

    private fun initParam() {
        mLoadUrl = intent.getStringExtra(INPUT_PARAM_URL)?:""
    }

    private fun initView() {
        tvTitle.text = createTitle()
    }

    private fun initData(){
        val absolutePath = getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath?:""
        Logger.d(TAG, msg = "initData url = $mLoadUrl, absolutePath = $absolutePath")
        if (!TextUtils.isEmpty(mLoadUrl) && !TextUtils.isEmpty(absolutePath)){
            DownloadUtils.download(mLoadUrl,absolutePath,createFileName(),downloadCallback)
        }else{
            handlerError("url or path exception")
        }
    }

    private fun createTitle():String{
        return getString(R.string.app_name)
    }

    private fun createFileName():String{
        return "${DeviceManager.getProductModel()}.apk"
    }

    fun preDownload(request: Request, id: Int){
        Logger.d(TAG, msg = "preDownload id = $id")
    }

    fun afterDownload(id: Int){
        Logger.d(TAG, msg = "afterDownload id = $id")
    }

    fun progressUpdate(progress: Float, total: Long){
        progressBar.progress = (progress * 100).toInt()
    }

    fun postDownload(file: File?){
        val absolutePath = file?.absolutePath
        Logger.d(TAG, msg = "postDownload absolutePath = $absolutePath")
        if (absolutePath != null && !TextUtils.isEmpty(absolutePath)){
            PackageUtils.installApk(this,absolutePath)
        }else{
            handlerError(getString(R.string.str_get_download_resource_exception))
        }
    }

    fun handlerError(error: String){
        Logger.e(TAG, msg = "handlerError error = $error")
        Toast.makeText(this,error, Toast.LENGTH_LONG).show()
        finish()
    }

    override fun enableBack(): Boolean {
        return false
    }

    private val downloadCallback = object : IHttpListener.DownloadCallback{
        override fun onPreDownload(request: Request, id: Int) {
            launch {
                preDownload(request, id)
            }
        }

        override fun onAfterDownload(id: Int) {
            launch {
                afterDownload(id)
            }
        }

        override fun onProgressUpdate(progress: Float, total: Long) {
            launch {
                progressUpdate(progress, total)
            }
        }

        override fun onPostDownload(file: File?) {
            launch {
                postDownload(file)
            }
        }

        override fun onErrorDownload(error: String) {
            launch {
                handlerError(error)
            }
        }

    }

}