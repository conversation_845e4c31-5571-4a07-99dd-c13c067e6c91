package com.mitdd.gazetracker.update

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import kotlin.math.round

/**
 * FileName: UpdateDialog
 * Author by lilin,Date on 2024/10/14 16:53
 * PS: Not easy to write code, please indicate.
 */
class UpdateDialog(context: Context, private var url:String, private var version:String,
                   private var introduction:String, private var isForceUpdate:Boolean,
                   private var appSize:Long
) : BaseCommonDialog(context)  {

    companion object{
        private val TAG = UpdateDialog::class.java.simpleName
    }

    private val tvAppName by id<TextView>(R.id.tv_app_name)
    private val tvVersion by id<TextView>(R.id.tv_version)
    private val tvAppSize by id<TextView>(R.id.tv_app_size)
    private val tvIntroduction by id<TextView>(R.id.tv_introduction)
    private val tvUpdate by id<TextView>(R.id.tv_update)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setCancelable(!isForceUpdate)
        setCanceledOnTouchOutside(!isForceUpdate)
        // 设置自定义的布局
        setContentView(R.layout.dialog_update)

        initView()
        initListener()
    }

    private fun initView() {
        tvUpdate.requestFocus()
        tvAppName.text = context.getString(R.string.str_app_name_s,context.getString(R.string.app_name))
        tvVersion.text = context.getString(R.string.str_version_number_s,version)
        if (appSize > 0){
            tvAppSize.isVisible = true
            tvAppSize.text = context.getString(R.string.str_app_size_d,round(appSize.toFloat() / 1024 / 1024).toInt())
        }else{
            tvAppSize.isVisible = false
        }
        tvIntroduction.text = introduction

        tvIntroduction.movementMethod = ScrollingMovementMethod()
    }

    private fun initListener() {
        tvUpdate.setOnSingleClickListener {
            context.startActivity(UpdateActivity.createIntent(context, url))
            dismiss()
        }
    }
}