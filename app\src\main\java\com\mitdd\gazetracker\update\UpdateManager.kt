package com.mitdd.gazetracker.update

import com.mitdd.gazetracker.update.bean.AppUpdateInfo
import java.util.concurrent.atomic.AtomicReference

/**
 * FileName: UpdateManager
 * Author by lilin,Date on 2025/4/23 20:29
 * PS: Not easy to write code, please indicate.
 */
object UpdateManager {

    //当前应用升级信息
    private val appUpdateInfo = AtomicReference<AppUpdateInfo?>()

    /**
     * 设置当前应用升级信息
     */
    fun setAppUpdateInfo(appUpdateInfo: AppUpdateInfo?) {
        this.appUpdateInfo.set(appUpdateInfo)
    }

    /**
     * 获取当前应用升级信息
     */
    fun getAppUpdateInfo(): AppUpdateInfo? {
        return appUpdateInfo.get()
    }

    /**
     * 是否可以升级
     */
    fun isCanUpgraded():Boolean{
        return !getAppUpdateInfo()?.appVersion?.url.isNullOrEmpty()
    }

}