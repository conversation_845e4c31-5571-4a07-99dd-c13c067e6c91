package com.mitdd.gazetracker.update.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.update.bean.AppUpdateInfo
import retrofit2.http.GET

/**
 * FileName: UpdateApiService
 * Author by lilin,Date on 2024/10/14 16:38
 * PS: Not easy to write code, please indicate.
 */
interface UpdateApiService {

    /**
     * 获取APP升级信息
     */
    @GET("dt/portal/system/v1/check-update")
    suspend fun getAppUpdateInfo(): ApiResponse<AppUpdateInfo?>

}