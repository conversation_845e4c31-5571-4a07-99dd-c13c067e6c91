package com.mitdd.gazetracker.update.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: AppVersion
 * Author by lilin,Date on 2024/10/14 16:36
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class AppVersion(
    //安装包大小
    var appSize:Long? = null,
    //apk数据签名
    var digitalSignature:String? = null,
    //版本介绍
    var introduce:String? = null,
    //安装数字版本
    var numericVersion:Int? = null,
    //安装包下载地址
    var url:String? = null,
    //安装版本
    var version:String? = null,
) : Parcelable
