package com.mitdd.gazetracker.update.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.update.api.UpdateApiService
import com.mitdd.gazetracker.update.bean.AppUpdateInfo
import com.mitdd.gazetracker.net.MainRetrofitClient

/**
 * FileName: UpdateRepository
 * Author by lilin,Date on 2024/10/14 16:39
 * PS: Not easy to write code, please indicate.
 */
class UpdateRepository : BaseRepository() {

    /**
     * 获取APP升级信息
     */
    suspend fun getAppUpdateInfo(): ApiResponse<AppUpdateInfo?> {
        return executeHttp {
            MainRetrofitClient.createService(UpdateApiService::class.java).getAppUpdateInfo()
        }
    }

}