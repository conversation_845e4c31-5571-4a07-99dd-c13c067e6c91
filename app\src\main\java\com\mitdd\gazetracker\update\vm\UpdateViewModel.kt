package com.mitdd.gazetracker.update.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.update.UpdateManager
import com.mitdd.gazetracker.update.bean.AppUpdateInfo
import com.mitdd.gazetracker.update.repository.UpdateRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: UpdateViewModel
 * Author by lilin,Date on 2024/10/14 16:40
 * PS: Not easy to write code, please indicate.
 */
class UpdateViewModel : ViewModel() {

    companion object{
        private val TAG = UpdateViewModel::class.java.name
    }

    private val updateRepository by lazy { UpdateRepository() }

    //app 升级信息
    val appUpdateInfoLiveData = MutableLiveData<AppUpdateInfo?>()

    fun getAppUpdateInfo(){
        viewModelScope.launch {
            MutableStateFlow(updateRepository.getAppUpdateInfo()).collectResponse{
                onSuccess = { it,_,_->
                    Logger.d(TAG, msg = "getAppUpdateInfo onSuccess")
                    UpdateManager.setAppUpdateInfo(it)
                    appUpdateInfoLiveData.postValue(it)
                }
                onDataEmpty = { _,_->
                    Logger.e(TAG, msg = "getAppUpdateInfo onDataEmpty")
                    UpdateManager.setAppUpdateInfo(null)
                    appUpdateInfoLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getAppUpdateInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    UpdateManager.setAppUpdateInfo(null)
                    appUpdateInfoLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getAppUpdateInfo onError = $it")
                    UpdateManager.setAppUpdateInfo(null)
                    appUpdateInfoLiveData.postValue(null)
                }
            }
        }
    }
}