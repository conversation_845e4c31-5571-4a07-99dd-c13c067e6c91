package com.mitdd.gazetracker.user

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.device.enumeration.PlacementType.M_HOME
import com.mitdd.gazetracker.device.enumeration.PlacementType.M_HOSPITAL
import com.mitdd.gazetracker.device.enumeration.PlacementType.R_HOME
import com.mitdd.gazetracker.device.enumeration.PlacementType.R_STORE
import com.mitdd.gazetracker.user.bean.VerifyInfo
import com.mitdd.gazetracker.user.vm.UserViewModel


/**
 * FileName: LoginActivity
 * Author by lilin,Date on 2024/9/26 20:21
 * PS: Not easy to write code, please indicate.
 * 登录页
 */
class BindActivity : GTBaseActivity() {

    companion object{
        private val TAG = BindActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            return Intent(context, BindActivity::class.java)
        }
    }

    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val etAccountNumber by id<EditText>(R.id.et_account_number)
    private val etPassword by id<EditText>(R.id.et_password)
    private val tvBind by id<TextView>(R.id.tv_bind)
    private val ivCheckProtocol by id<ImageView>(R.id.iv_check_protocol)
    private val tvProtocol by id<TextView>(R.id.tv_protocol)
    private val ivBack by id<ImageView>(R.id.iv_back)

    private val userVM by viewModels<UserViewModel>()

    private var mPlacementType = DeviceManager.getPlacementType()?:M_HOME
    //账号
    private var mAccountNumber = ""
    //密码
    private var mPassword = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_bind)

        initParam()
        initView()
        initObserver()
    }

    private fun initParam(){
    }

    private fun initView(){
        initListener()

        var protocolColor = ContextCompat.getColor(this,R.color.color_eb4e89)
        when(mPlacementType){
            M_HOME ->{
                tvBind.setBackgroundResource(R.drawable.common_eb4e89_round_bg)
                protocolColor = ContextCompat.getColor(this,R.color.color_eb4e89)
                ivCheckProtocol.setImageResource(R.drawable.selector_check_protocol_red)
            }
            M_HOSPITAL ->{
                tvBind.setBackgroundResource(R.drawable.common_5794ff_round_bg)
                protocolColor = ContextCompat.getColor(this,R.color.color_5794ff)
                ivCheckProtocol.setImageResource(R.drawable.selector_check_protocol_blue)
            }
            R_HOME ->{
                tvBind.setBackgroundResource(R.drawable.common_eb4e89_round_bg)
                protocolColor = ContextCompat.getColor(this,R.color.color_eb4e89)
                ivCheckProtocol.setImageResource(R.drawable.selector_check_protocol_red)
            }
            R_STORE ->{
                tvBind.setBackgroundResource(R.drawable.common_5794ff_round_bg)
                protocolColor = ContextCompat.getColor(this,R.color.color_5794ff)
                ivCheckProtocol.setImageResource(R.drawable.selector_check_protocol_blue)
            }
            else ->{

            }
        }

        val userAgreement = getString(R.string.str_user_agreement)
        val privacyPolicy = getString(R.string.str_privacy_policy)
        val text = getString(R.string.str_read_and_agree_user_agreement_privacy_policy,userAgreement,privacyPolicy)
        val spannableString = SpannableString(text)
        val userAgreementClick = object : ClickableSpan(){
            override fun onClick(widget: View) {
                startActivity(ProtocolWebActivity.createIntent(this@BindActivity,
                    "https://ada-res.airdoc.com/resources/vision-therapy/shiqi-vision-app-user-agreement.html"))
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.setColor(protocolColor)
            }

        }
        val privacyPolicyClick = object : ClickableSpan(){
            override fun onClick(widget: View) {
                startActivity(ProtocolWebActivity.createIntent(this@BindActivity,
                    "https://ada-res.airdoc.com/resources/vision-therapy/shiqi-vision-app-privacy-agreement.html"))
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.setColor(protocolColor)
            }

        }
        val startUserAgreement = text.indexOf(userAgreement)
        val endUserAgreement = startUserAgreement + userAgreement.length
        spannableString.setSpan(userAgreementClick,startUserAgreement,endUserAgreement, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        val startPrivacyPolicy = text.indexOf(privacyPolicy)
        val endPrivacyPolicy = startPrivacyPolicy + privacyPolicy.length
        spannableString.setSpan(privacyPolicyClick,startPrivacyPolicy,endPrivacyPolicy, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        tvProtocol.movementMethod = LinkMovementMethod.getInstance()
        tvProtocol.text = spannableString
        tvProtocol.highlightColor = getResources().getColor(android.R.color.transparent)

        ivCheckProtocol.isSelected = false

        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(this,logo,0,R.drawable.icon_airdoc_digital_therapy_center,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.icon_airdoc_digital_therapy_center)
        }
    }

    private fun initListener(){
        etAccountNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val inputText = s.toString()
                val trimmedText = inputText.replace(" ", "")
                mAccountNumber = trimmedText
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        etPassword.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val inputText = s.toString()
                val trimmedText = inputText.replace(" ", "")
                mPassword = trimmedText
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        tvBind.setOnSingleClickListener {
            if (!ivCheckProtocol.isSelected){
                Toast.makeText(this,getString(R.string.str_please_read_agree_agreement),Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }
            if (TextUtils.isEmpty(mAccountNumber)){
                Toast.makeText(this,getString(R.string.str_please_enter_account_number),Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }
            if (TextUtils.isEmpty(mPassword)){
                Toast.makeText(this,getString(R.string.str_please_enter_password),Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }
            //验证账号密码
            userVM.verifyAccountPassword(mAccountNumber,mPassword)
        }
        ivCheckProtocol.setOnSingleClickListener {
            ivCheckProtocol.isSelected = !ivCheckProtocol.isSelected
        }
        ivBack.setOnSingleClickListener {
            finish()
        }
    }

    private fun initObserver() {
        userVM.verifyInfoLiveData.observe(this){
            when (it) {
                is VerifyInfo -> {
                    val confirmBindDialog = ConfirmBindDialog(this,mPlacementType)
                    confirmBindDialog.show()
                    confirmBindDialog.setConfirmData(it)
                    confirmBindDialog.onConfirmClick = {
                        userVM.bindUser(it.confirmCode?:"")
                    }
                }
                is Pair<*,*> -> {
                    Toast.makeText(this,"${it.second}",Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Toast.makeText(this,getString(R.string.str_binding_failure),Toast.LENGTH_SHORT).show()
                }
            }
        }
        userVM.bindInfoLiveData.observe(this){
            when (it) {
                is String -> {
                    Toast.makeText(this,getString(R.string.str_binding_success),Toast.LENGTH_SHORT).show()
                    val returnIntent = Intent()
                    setResult(Activity.RESULT_OK, returnIntent)
                    finish()
                }
                is Pair<*,*> -> {
                    Toast.makeText(this,"${it.second}",Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Toast.makeText(this,getString(R.string.str_binding_failure),Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

}