package com.mitdd.gazetracker.user

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.device.DeviceManager
import com.mitdd.gazetracker.device.enumeration.PlacementType
import com.mitdd.gazetracker.device.enumeration.PlacementType.*
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.UrlMedia
import com.mitdd.gazetracker.net.UrlConfig
import com.mitdd.gazetracker.user.bean.Gender
import com.mitdd.gazetracker.user.bean.VerifyInfo
import com.mitdd.gazetracker.utils.LocaleManager
import androidx.core.graphics.drawable.toDrawable

/**
 * FileName: ConfirmBindDialog
 * Author by lilin,Date on 2024/9/27 11:05
 * PS: Not easy to write code, please indicate.
 * 确认绑定弹窗
 */
class ConfirmBindDialog(context: Context,private val placementType:PlacementType) : BaseCommonDialog(context) {

    private val tvSn by id<TextView>(R.id.tv_sn)
    private val ivUserAvatar by id<ImageView>(R.id.iv_user_avatar)
    private val tvUserName by id<TextView>(R.id.tv_user_name)
    private val tvGender by id<TextView>(R.id.tv_gender)
    private val tvAge by id<TextView>(R.id.tv_age)
    private val tvPhone by id<TextView>(R.id.tv_phone)
    private val tvCancel by id<TextView>(R.id.tv_cancel)
    private val tvConfirmBind by id<TextView>(R.id.tv_confirm_bind)
    private val tvPrompt by id<TextView>(R.id.tv_prompt)

    var onConfirmClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置自定义的布局
        setContentView(R.layout.dialog_confirm_bind)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initListener()

        initData()

        when(placementType){
            M_HOME ->{
                tvConfirmBind.setBackgroundResource(R.drawable.common_eb4e89_round_bg)
            }
            M_HOSPITAL ->{
                tvConfirmBind.setBackgroundResource(R.drawable.common_5794ff_round_bg)
            }
            R_HOME ->{
                tvConfirmBind.setBackgroundResource(R.drawable.common_eb4e89_round_bg)
            }
            R_STORE ->{
                tvConfirmBind.setBackgroundResource(R.drawable.common_5794ff_round_bg)
            }
            else ->{
            }
        }

        playBindConfirm()
    }

    fun setConfirmData(verifyInfo: VerifyInfo){
        with(verifyInfo){
            tvUserName.text = accountName
            tvPhone.text = phone?:context.getString(R.string.str_unknown)
            when(placementType){
                M_HOME,R_HOME -> {
                    tvGender.isVisible = true
                    tvAge.isVisible = true
                    when(gender){
                        Gender.MALE.value ->{
                            ImageLoader.loadImageWithPlaceholder(context,icon?:"",R.drawable.icon_user_avatar_male_big,R.drawable.icon_user_avatar_male_big,ivUserAvatar)
                            tvGender.text = context.getString(R.string.str_male)
                        }
                        Gender.FEMALE.value ->{
                            ImageLoader.loadImageWithPlaceholder(context,icon?:"",R.drawable.icon_user_avatar_female_big,R.drawable.icon_user_avatar_female_big,ivUserAvatar)
                            tvGender.text = context.getString(R.string.str_female)
                        }
                    }
                    if (age != null){
                        tvAge.text = context.getString(R.string.str_age_int,age)
                    }else{
                        tvAge.text = context.getString(R.string.str_unknown)
                    }
                    tvPrompt.text = context.getString(R.string.str_confirm_equipment_and_patient_information_correct)
                }
                M_HOSPITAL,R_STORE -> {
                    tvGender.isVisible = false
                    tvAge.isVisible = false
                    ImageLoader.loadImageWithPlaceholder(context,icon?:"",R.drawable.icon_bind_logo,R.drawable.icon_bind_logo,ivUserAvatar)
                    tvPrompt.text = context.getString(R.string.str_confirm_equipment_and_store_information_correct)
                }
                else ->{

                }
            }
        }
    }

    private fun initData() {
        tvSn.text = context.getString(R.string.str_device_sn_n,DeviceManager.getDeviceSn())
    }

    private fun initListener() {
        tvCancel.setOnSingleClickListener {
            dismiss()
        }
        tvConfirmBind.setOnSingleClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }

    private fun playBindConfirm(){
        when(placementType){
            M_HOME,R_HOME -> {
                if (LocaleManager.getLanguage() == "en"){
                    PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/bind_confirm_equipment_and_patient_files_correct.wav"))
                }else{
                    PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/bind_confirm_equipment_and_patient_files_correct.wav"))
                }
            }
            M_HOSPITAL,R_STORE -> {
                if (LocaleManager.getLanguage() == "en"){
                    PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/bind_confirm_equipment_and_store_information_correct.wav"))
                }else{
                    PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/bind_confirm_equipment_and_store_information_correct.wav"))
                }
            }
            else ->{

            }
        }
    }

}