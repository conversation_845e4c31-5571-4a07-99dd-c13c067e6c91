package com.mitdd.gazetracker.user

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ui.web.CustomWebView
import com.airdoc.component.common.ui.web.WebViewManager
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity

/**
 * FileName: ProtocolWebActivity
 * Author by lilin,Date on 2024/10/14 17:53
 * PS: Not easy to write code, please indicate.
 */
class ProtocolWebActivity : GTBaseActivity() {

    companion object{
        private val TAG = ProtocolWebActivity::class.java.simpleName

        const val INPUT_PROTOCOL_URL = "PROTOCOL_URL"

        fun createIntent(context: Context, protocolUrl: String): Intent {
            val intent = Intent(context, ProtocolWebActivity::class.java)
            intent.putExtra(INPUT_PROTOCOL_URL,protocolUrl)
            return intent
        }
    }

    private val wvProtocol by id<CustomWebView>(R.id.wv_protocol)

    private var mProtocolUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_protocol_web)
        initParam()
        initView()
        initData()
    }

    private fun initParam() {
        mProtocolUrl = intent.getStringExtra(INPUT_PROTOCOL_URL)?:""
    }

    private fun initView() {
    }

    private fun initData() {
        if (!TextUtils.isEmpty(mProtocolUrl)){
            wvProtocol.loadUrl(mProtocolUrl)
        }
    }

}