package com.mitdd.gazetracker.user

import com.airdoc.component.common.cache.MMKVManager
import com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo
import com.mitdd.gazetracker.user.bean.AccountInfo

/**
 * FileName: UserManager
 * Author by lilin,Date on 2024/10/14 10:19
 * PS: Not easy to write code, please indicate.
 */
object UserManager {

    /**
     * 是否绑定
     */
    fun isBind():Boolean{
        val accountInfo = getAccountInfo()
        return accountInfo?.accountId != null
    }

    /**
     * 保存账号用户信息
     */
    fun setAccountInfo(accountInfo: AccountInfo?){
        MMKVManager.encodeParcelable(UserPreference.ACCOUNT_INFO,accountInfo)
    }

    /**
     * 获取账号用户信息
     */
    fun getAccountInfo():AccountInfo?{
        return MMKVManager.decodeParcelable(UserPreference.ACCOUNT_INFO,AccountInfo::class.java)
    }

    /**
     * 保持当前用户疗程信息
     */
    fun setTreatmentInfo(treatmentInfo: TreatmentInfo?){
        MMKVManager.encodeParcelable(UserPreference.TREATMENT_INFO,treatmentInfo)
    }

    /**
     * 获取当前用户疗程信息
     */
    fun getTreatmentInfo(): TreatmentInfo?{
        return MMKVManager.decodeParcelable(UserPreference.TREATMENT_INFO, TreatmentInfo::class.java)
    }

}