package com.mitdd.gazetracker.user

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: UserPreference
 * Author by lilin,Date on 2024/9/26 16:29
 * PS: Not easy to write code, please indicate.
 */
enum class UserPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * 当前用户账号信息
     */
    ACCOUNT_INFO(null),

    /**
     * 当前用户疗程信息
     */
    TREATMENT_INFO(null),

    /**
     * 显示了疗程到期提醒弹窗的日期
     */
    SHOW_TREATMENT_EXPIRATION_REMIND_DIALOG_DATE(null),

    /**
     * 显示了复查提醒弹窗的日期
     */
    SHOW_REVIEW_REMIND_DIALOG_DATE(null),

    /**
     * 常用APP,保存常用APP的包名,逗号分隔
     */
    COMMON_APPLICATION(null);

    override fun getNameSpace(): String {
        return "UserPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}