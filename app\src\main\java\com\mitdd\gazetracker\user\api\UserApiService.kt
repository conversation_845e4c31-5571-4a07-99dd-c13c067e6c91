package com.mitdd.gazetracker.user.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.user.bean.AccountInfo
import com.mitdd.gazetracker.user.bean.VerifyInfo
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

/**
 * FileName: DeviceApiService
 * Author by lilin,Date on 2024/10/8 17:41
 * PS: Not easy to write code, please indicate.
 */
interface UserApiService {

    /**
     * 获取绑定账户信息
     */
    @GET("dt/api/device/v1/account")
    suspend fun getAccountInfo(): ApiResponse<AccountInfo?>

    /**
     * 验证账号密码
     */
    @POST("dt/api/device/v1/verify")
    suspend fun verifyAccountPassword(@Body verifyAccount: RequestBody): ApiResponse<VerifyInfo?>

    /**
     * 绑定用户
     */
    @POST("dt/api/device/v1/bind")
    suspend fun bindUser(@Body confirmCode: RequestBody): ApiResponse<Any?>
}