package com.mitdd.gazetracker.user.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: AccountInfo
 * Author by lilin,Date on 2024/11/27 10:37
 * PS: Not easy to write code, please indicate.
 * 账号信息
 */
@Parcelize
data class AccountInfo(
    //绑定的登录账号名
    var account:String? = null,
    //绑定的账号ID
    var accountId:Long? = null,
    //绑定的账号名称 视琦的档案姓名
    var accountName:String? = null,
    //年龄
    var age:Int? = null,
    //账号的过期日期
    var expirationDate:String? = null,
    //性别 男male 女female
    var gender:String? = null,
    //账号归属医院名称
    var hospitalName:String? = null,
    //账号是否已经过期
    var isExpired:Boolean? = null,
    //绑定的账号的手机号
    var phone:String? = null,
    //复查日期
    var reviewDate:String? = null,
    //复查提醒日期
    var reviewRemindDate:String? = null,
) : Parcelable
