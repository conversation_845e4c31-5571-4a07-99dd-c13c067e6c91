package com.mitdd.gazetracker.user.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: AccountInfo
 * Author by lilin,Date on 2024/10/9 16:14
 * PS: Not easy to write code, please indicate.
 * 验证账号密码返回信息
 */
@Parcelize
data class VerifyInfo(
    //绑定的账号
    var account:String? = null,
    //绑定的或者账号ID
    var accountId:Long? = null,
    //绑定的账号名称 视琦的档案名
    var accountName:String? = null,
    //年龄
    var age:Int? = null,
    //确认绑定码
    var confirmCode:String? = null,
    //确认绑定码有效期 单位秒
    var confirmCodeTtl:Int? = null,
    //性别 男male 女female
    var gender:String? = null,
    //绑定的账号的手机号
    var phone:String? = null,
    //图像
    var icon:String? = null,
): Parcelable{
    companion object{
        val EMPTY = VerifyInfo()
    }
}
