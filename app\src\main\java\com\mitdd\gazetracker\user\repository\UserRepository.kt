package com.mitdd.gazetracker.user.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.user.api.UserApiService
import com.mitdd.gazetracker.user.bean.AccountInfo
import com.mitdd.gazetracker.user.bean.VerifyInfo
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: DeviceRepository
 * Author by lilin,Date on 2024/10/8 17:43
 * PS: Not easy to write code, please indicate.
 */
class UserRepository : BaseRepository() {

    /**
     * 获取绑定账户信息
     */
    suspend fun getAccountInfo(): ApiResponse<AccountInfo?> {
        return executeHttp {
            MainRetrofitClient.createService(UserApiService::class.java).getAccountInfo()
        }
    }

    /**
     * 验证账号密码
     * @param account 账号
     * @param password 密码
     */
    suspend fun verifyAccountPassword(account:String,password:String): ApiResponse<VerifyInfo?> {
        return executeHttp {
            val hashMap = HashMap<String, String>()
            hashMap["account"] = account
            hashMap["password"] = password
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(UserApiService::class.java).verifyAccountPassword(requestBody)
        }
    }

    /**
     * 绑定用户
     * @param confirmCode 确认绑定码
     */
    suspend fun bindUser(confirmCode:String): ApiResponse<Any?> {
        return executeHttp {
            val hashMap = HashMap<String, String>()
            hashMap["confirmCode"] = confirmCode
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(UserApiService::class.java).bindUser(requestBody)
        }
    }

}