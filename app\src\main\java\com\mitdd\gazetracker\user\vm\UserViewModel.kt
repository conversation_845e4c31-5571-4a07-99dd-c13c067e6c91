package com.mitdd.gazetracker.user.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.mitdd.gazetracker.user.UserManager
import com.mitdd.gazetracker.user.bean.AccountInfo
import com.mitdd.gazetracker.user.repository.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: DeviceViewModel
 * Author by lilin,Date on 2024/10/8 17:44
 * PS: Not easy to write code, please indicate.
 */
class UserViewModel : ViewModel() {

    companion object{
        private val TAG = UserViewModel::class.java.name
    }

    private val userRepository by lazy { UserRepository() }

    //绑定账户信息
    val accountInfoLiveData = MutableLiveData<AccountInfo?>()
    //验证账号密码信息
    val verifyInfoLiveData = MutableLiveData<Any?>()
    //绑定用户
    val bindInfoLiveData = MutableLiveData<Any?>()

    /**
     * 获取绑定账户信息
     */
    fun getAccountInfo(){
        viewModelScope.launch {
            MutableStateFlow(userRepository.getAccountInfo()).collectResponse{
                onSuccess = { it, _, _ ->
                    Logger.d(TAG, msg = "getAccountInfo onSuccess")
                    UserManager.setAccountInfo(it)
                    accountInfoLiveData.postValue(it)
                }
                onDataEmpty = { code, message ->
                    Logger.e(TAG, msg = "getAccountInfo onDataEmpty code = $code, message = $message")
                    UserManager.setAccountInfo(null)
                    accountInfoLiveData.postValue(null)
                }
                onFailed = { code, message ->
                    Logger.e(TAG, msg = "getAccountInfo onFailed code = $code, message = $message")
                    UserManager.setAccountInfo(null)
                    accountInfoLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getAccountInfo onError = $it")
                    UserManager.setAccountInfo(null)
                    accountInfoLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 验证账号密码
     * @param account 账号
     * @param password 密码
     */
    fun verifyAccountPassword(account:String,password:String){
        viewModelScope.launch {
            MutableStateFlow(userRepository.verifyAccountPassword(account,password)).collectResponse{
                onSuccess = { it, _, _ ->
                    Logger.d(TAG, msg = "verifyAccountPassword onSuccess")
                    verifyInfoLiveData.postValue(it)
                }
                onDataEmpty = { code, message ->
                    Logger.e(TAG, msg = "verifyAccountPassword onDataEmpty code = $code, message = $message")
                    verifyInfoLiveData.postValue(Pair(code,message))
                }
                onFailed = { code, message ->
                    Logger.e(TAG, msg = "verifyAccountPassword onFailed code = $code, message = $message")
                    verifyInfoLiveData.postValue(Pair(code,message))
                }
                onError = {
                    Logger.e(TAG, msg = "verifyAccountPassword onError = $it")
                    verifyInfoLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 绑定用户
     * @param confirmCode 确认绑定码
     */
    fun bindUser(confirmCode:String){
        viewModelScope.launch {
            MutableStateFlow(userRepository.bindUser(confirmCode)).collectResponse{
                onSuccess = { _, _, _ ->
                    Logger.d(TAG, msg = "bindUser onSuccess")
                    bindInfoLiveData.postValue("SUCCESS")
                }
                onDataEmpty = { code, message ->
                    Logger.d(TAG, msg = "bindUser onDataEmpty code = $code, errorMsg = $message")
                    bindInfoLiveData.postValue("SUCCESS")
                }
                onFailed = { code, message ->
                    Logger.e(TAG, msg = "bindUser onFailed code = $code, errorMsg = $message")
                    bindInfoLiveData.postValue(Pair(code,message))
                }
                onError = {
                    Logger.e(TAG, msg = "bindUser onError = $it")
                    bindInfoLiveData.postValue(null)
                }
            }
        }
    }

}