package com.mitdd.gazetracker.utils

/**
 * FileName: CommonUtils
 * Author by lilin,Date on 2024/11/23 9:25
 * PS: Not easy to write code, please indicate.
 */
object CommonUtils {

    /**
     * 是否是有效的国内手机号
     */
    fun isValidChinesePhoneNumber(phoneNumber: String): Boolean {
        val pattern = "^1[3-9]\\d{9}$".toRegex()
        return pattern.matches(phoneNumber)
    }

    /**
     * 是否是有效的国际手机号
     */
    fun isValidInternationalPhoneNumber(phoneNumber: String): Boolean {
        val pattern = "^\\+?[1-9]\\d{1,14}\$".toRegex()
        return pattern.matches(phoneNumber)
    }

}