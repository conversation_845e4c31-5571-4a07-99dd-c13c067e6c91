package com.mitdd.gazetracker.utils

import android.content.Context
import android.graphics.ImageFormat
import android.media.Image
import androidx.camera.core.ImageProxy
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

object GTUtils {

    private var data: ByteArray = ByteArray(0)
    private var rowData: ByteArray = ByteArray(0)
    fun getDataFromImage(image: ImageProxy): ByteArray {
        val crop = image.cropRect
        val format = image.format
        val width = crop.width()
        val height = crop.height()
        val planes = image.planes
        val size = width * height * ImageFormat.getBitsPerPixel(format) / 8
        if (data.size != size) {
            data = ByteArray(size)
        }
        if (rowData.size != planes[0].rowStride) {
            rowData = ByteArray(planes[0].rowStride)
        }
        var channelOffset = 0 //偏移
        for (i in planes.indices) {
            when (i) {
                0 -> channelOffset = 0 // y 从0开始
                1 -> channelOffset = width * height // u 开始
                2 -> channelOffset = (width * height * 1.25).toInt() // v开始 w*h+ w*h/4（u数据长度）
            }
            val buffer = planes[i].buffer
            val rowStride = planes[i].rowStride //行跨度 每行的数据量
            val pixelStride = planes[i].pixelStride // 像素跨度 ,uv的存储间隔
            val shift = if (i == 0) 0 else 1
            val w = width shr shift // u与v只有一半
            val h = height shr shift
            buffer.position(rowStride * (crop.top shr shift) + pixelStride * (crop.left shr shift))
            var length: Int
            for (row in 0 until h) {
                if (pixelStride == 1) {
                    length = w
                    buffer[data, channelOffset, length]
                    channelOffset += length
                } else {
                    length = (w - 1) * pixelStride + 1
                    buffer[rowData, 0, length]
                    for (col in 0 until w) {
                        data[channelOffset++] = rowData[col * pixelStride]
                    }
                }
                if (row < h - 1) {
                    buffer.position(buffer.position() + rowStride - length)
                }
            }
        }
        return data
    }

    fun getDataFromImageY(image: ImageProxy): ByteArray {
        val crop = image.cropRect
        val format = image.format
        val width = crop.width()
        val height = crop.height()
        val planes = image.planes
        val size = width * height * ImageFormat.getBitsPerPixel(format) / 8
        if (data.size != size) {
            data = ByteArray(size)
        }
        if (rowData.size != planes[0].rowStride) {
            rowData = ByteArray(planes[0].rowStride)
        }
        var channelOffset = 0 //偏移
        val buffer = planes[0].buffer
        val rowStride = planes[0].rowStride //行跨度 每行的数据量
        val pixelStride = planes[0].pixelStride // 像素跨度 ,uv的存储间隔
        val shift = 0
        val w = width shr shift // u与v只有一半
        val h = height shr shift
        buffer.position(rowStride * (crop.top shr shift) + pixelStride * (crop.left shr shift))
        var length: Int
        for (row in 0 until h) {
            if (pixelStride == 1) {
                length = w
                buffer[data, channelOffset, length]
                channelOffset += length
            } else {
                length = (w - 1) * pixelStride + 1
                buffer[rowData, 0, length]
                for (col in 0 until w) {
                    data[channelOffset++] = rowData[col * pixelStride]
                }
            }
            if (row < h - 1) {
                buffer.position(buffer.position() + rowStride - length)
            }
        }
        return data
    }

    /**
     * 拷贝assets文件到特定文件
     * @param file 内容接收文件
     * @param name assets 文件名称
     * @return 返回true表示拷贝成功
     */
    fun copyAssets2Dir(context: Context, file: File, name: String):Boolean {
        if (!file.exists()) {
            var inputStream: InputStream? = null
            var fos: FileOutputStream? = null
            try {
                inputStream = context.assets.open(name)
                fos = FileOutputStream(file)
                var len: Int
                val buffer = ByteArray(2048)
                while (inputStream.read(buffer).also { len = it } != -1) {
                    fos.write(buffer, 0, len)
                }
            } catch (e: IOException) {
                e.printStackTrace()
                return false
            } finally {
                try {
                    inputStream?.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
                try {
                    fos?.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return true
    }

}
