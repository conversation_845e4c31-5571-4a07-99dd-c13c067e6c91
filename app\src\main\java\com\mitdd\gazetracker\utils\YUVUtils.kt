package com.mitdd.gazetracker.utils

import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.media.Image
import java.io.FileOutputStream
import java.io.IOException
import kotlin.experimental.and
import kotlin.math.max
import kotlin.math.min


/**
 * FileName: YUVUtils
 * Author by lilin,Date on 2024/10/29 9:35
 * PS: Not easy to write code, please indicate.
 */
object YUVUtils {

    fun saveYUV420ImageAsPNG(image: Image,filePath:String){
        if (image.format != ImageFormat.YUV_420_888) {
            return
        }
        val width = image.width
        val height = image.height
        val size = width * height

        val planes = image.planes
        val yBuffer = planes[0].buffer
        val uBuffer = planes[1].buffer
        val vBuffer = planes[2].buffer

        val yuvBytes = ByteArray(size + (width / 2) + (height / 2))
        yBuffer.get(yuvBytes, 0, size)
        uBuffer.get(yuvBytes, size, width / 2)
        vBuffer.get(yuvBytes, size + (width / 2), height / 2)

        val rgbBytes = IntArray(size)

        for (j in 0 until height) {
            for (i in 0 until width) {
                val yIndex = j * width + i
                val uIndex = (j / 2) * (width / 2) + (i / 2)
                val vIndex = (j / 2) * (width / 2) + (i / 2)

                val y = yuvBytes[yIndex] and 0xff.toByte()
                val u = yuvBytes[size + uIndex] and 0xff.toByte()
                val v = yuvBytes[size + (width / 2) + vIndex] and 0xff.toByte()

                val r: Int = clamp(y + 1.402 * (v - 128))
                val g: Int = clamp(y - 0.344136 * (u - 128) - 0.714136 * (v - 128))
                val b: Int = clamp(y + 1.772 * (u - 128))

                rgbBytes[yIndex] = -0x1000000 or (b shl 16) or (g shl 8) or r
            }
        }

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.setPixels(rgbBytes, 0, width, 0, 0, width, height)

        var output:FileOutputStream? = null
        try {
            output = FileOutputStream(filePath)
            output.use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }finally {
            try {
                output?.close()
            }catch (e: IOException){
                e.printStackTrace()
            }
        }
    }

    private fun clamp(value: Double): Int {
        return max(0.0, min(value, 255.0)).toInt()
    }

    fun yuv420ToNv21(image: Image): ByteArray {
        if (image.format != ImageFormat.YUV_420_888) {
            return byteArrayOf()
        }
        // 获取每个plane的buffer
        val yBuffer = image.planes[0].buffer
        val uBuffer = image.planes[1].buffer
        val vBuffer = image.planes[2].buffer
        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        // 初始化nv21数组，这里计算总大小可能要考虑对齐等问题
        val nv21 = ByteArray(ySize + (uSize + vSize))

        // 将Y分量直接复制到nv21数组
        yBuffer[nv21, 0, ySize]

        // 从Image的UV Plane提取数据
        val pixelStride = image.planes[1].pixelStride // U 和 V 分量的 PixelStride
        val rowStride = image.planes[1].rowStride // U 和 V 分量的 RowStride

        // 指定VU分量数据在nv21中的起始位置

        // 遍历每一行，根据RowStride和PixelStride提取U和V分量数据
        for (y in 0 until image.height / 2) {
            val vuRowStart = ySize + y * rowStride
            val uRowStart = y * rowStride
            val vRowStart = y * rowStride
            for (x in 0 until image.width / 2) {
                val vuPos = vuRowStart + x * 2
                val uPos = uRowStart + x * pixelStride
                val vPos = vRowStart + x * pixelStride
                nv21[vuPos] = vBuffer[vPos]
                nv21[vuPos + 1] = uBuffer[uPos]
            }
        }
        return nv21
    }

}