<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle" >

<!--    <padding-->
<!--        android:bottom="5dp"-->
<!--        android:left="5dp"-->
<!--        android:right="5dp"-->
<!--        android:top="5dp" />-->

    <!-- 设置圆角矩形
         radius:圆角弧度，核心代码
    -->
    <corners android:radius="100dp" />

<!--    <gradient-->
<!--        android:startColor="#66E0E4E8"-->
<!--        android:endColor="@color/white_40"-->
<!--        android:type="linear"-->
<!--        android:angle="270"/>-->

    <!-- 设置描边效果
         width:描边宽度
         color:描边颜色
     -->
<!--    <stroke-->
<!--        android:width="2dp"-->
<!--        android:color="#F9D8AF" />-->

    <!-- 设置填充效果
         color:填充颜色
     -->
    <solid android:color="#68CE67" />

</shape>