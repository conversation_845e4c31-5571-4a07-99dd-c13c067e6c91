<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!--     进度条的背景色-->
    <!--  center_vertical 将背景层的内容在垂直方向上居中对齐，不改变其高度。  -->
    <!--  如果背景层的高度（如通过 <size android:height="20dp" /> 定义）小于父容器的高度，
    背景层会垂直居中显示，顶部和底部留出等量的空白区域。  -->
    <!--  fill_horizontal 背景层的宽度会自动扩展到父容器的左右边界，确保水平方向完全覆盖。 -->
    <item android:id="@android:id/background"
        android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle">
            <corners android:radius="10dp" />
            <solid android:color="@color/white_30" />
        </shape>
    </item>
    <!--    缓冲进度条的背景色-->
<!--    <item android:id="@android:id/secondaryProgress"-->
<!--        android:gravity="center_vertical|fill_horizontal">-->
<!--        <scale android:scaleWidth="100%">-->
<!--            <shape android:shape="rectangle">-->
<!--                <size android:height="10dp" />-->
<!--                <corners android:radius="5dp" />-->
<!--                <solid android:color="@color/teal_200" />-->
<!--            </shape>-->
<!--        </scale>-->
<!--    </item>-->
    <!--  进度条的背景色-->
    <item android:id="@android:id/progress"
        android:gravity="left|fill_horizontal"
        android:clipOrientation="horizontal">
        <clip>
            <shape android:shape="rectangle">
                <corners android:radius="10dp" />
                <solid android:color="#E270A1"/>
            </shape>
        </clip>
    </item>
</layer-list>