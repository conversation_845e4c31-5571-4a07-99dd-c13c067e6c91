<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval">
    <solid android:color="@color/white" />
    <size
        android:width="28dp"
        android:height="28dp" />
    <!-- 这里的1dp边距的作用是，圆点在轨道里面的边距，这样的效果感觉更好 -->
    <stroke
        android:width="3dp"
        android:color="#00000000" />
    <corners android:radius="100dp" />
</shape>