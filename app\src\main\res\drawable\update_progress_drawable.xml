<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Background Layer 使用背景shape -->
    <item android:id="@android:id/background"
        android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle">
            <corners android:radius="100dp" />
            <solid android:color="@color/white_10" />
        </shape>
    </item>
    <!-- Progress Layer 使用进度shape -->
    <item android:id="@android:id/progress"
        android:gravity="left|fill_horizontal"
        android:clipOrientation="horizontal">
        <clip>
            <shape android:shape="rectangle">
                <corners android:radius="100dp" />
                <corners android:radius="10dp" />
                <gradient
                    android:startColor="#b94bb4"
                    android:centerColor="#7052ac"
                    android:endColor="#3266bf"
                    android:type="linear"/>
            </shape>
        </clip>
    </item>
</layer-list>