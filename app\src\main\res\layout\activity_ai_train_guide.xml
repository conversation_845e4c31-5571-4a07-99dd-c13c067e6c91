<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_main_bg">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="37dp"
        android:layout_height="50dp"
        android:src="@drawable/icon_back_white_coarse"
        android:layout_marginTop="30dp"
        android:layout_marginStart="20dp"
        android:padding="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_ai_training_guide"
        android:textColor="@color/white"
        android:textSize="22sp"
        android:textStyle="bold"
        android:layout_marginTop="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="96dp"
        android:layout_marginBottom="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/common_white_round_25_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_service_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_state_service"
            android:textColor="@color/color_333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginTop="30dp"
            android:layout_marginStart="20dp"/>

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_behavior_guidance"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:splitTrack="true"
            android:thumb="@drawable/switch_behavior_guidance_thumb"
            app:track="@drawable/switch_behavior_guidance_style"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="@+id/tv_service_state"
            app:layout_constraintBottom_toBottomOf="@+id/tv_service_state"
            app:layout_constraintLeft_toRightOf="@+id/tv_service_state"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_ai_train_guide_instructions"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="76dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>