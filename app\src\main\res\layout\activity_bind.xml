<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#EFF3F6">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        tools:src="@drawable/icon_airdoc_login_logo"
        android:layout_marginTop="108dp"
        android:scaleType="fitCenter"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <EditText
        android:id="@+id/et_account_number"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#ABADB0"
        android:hint="@string/str_please_enter_account_number"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/login_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:paddingStart="65dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="35dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_logo"/>

    <ImageView
        android:layout_width="17dp"
        android:layout_height="21dp"
        android:src="@drawable/icon_login_account_number"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toTopOf="@+id/et_account_number"
        app:layout_constraintBottom_toBottomOf="@+id/et_account_number"
        app:layout_constraintLeft_toLeftOf="@+id/et_account_number"/>

    <EditText
        android:id="@+id/et_password"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#ABADB0"
        android:hint="@string/str_please_enter_password"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/login_input_bg"
        android:imeOptions="actionDone"
        android:singleLine="true"
        android:paddingStart="65dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_account_number"/>

    <ImageView
        android:layout_width="17dp"
        android:layout_height="21dp"
        android:src="@drawable/icon_login_password"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toTopOf="@+id/et_password"
        app:layout_constraintBottom_toBottomOf="@+id/et_password"
        app:layout_constraintLeft_toLeftOf="@+id/et_password"/>

    <TextView
        android:id="@+id/tv_bind"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:text="@string/str_binding_device"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_password"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_bind">

        <ImageView
            android:id="@+id/iv_check_protocol"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/selector_check_protocol_red" />

        <TextView
            android:id="@+id/tv_protocol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_read_and_agree_user_agreement_privacy_policy"
            android:textColor="#BFBFC1"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp"/>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_coarse"
        android:layout_marginTop="30dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>