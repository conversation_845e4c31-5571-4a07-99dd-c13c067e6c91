<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/chat_root">

    <LinearLayout
        android:layout_width="240dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:background="#ECEDFE">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_airdoc_ai"
                android:textColor="#161541"
                android:textSize="9sp"
                android:textStyle="bold"
                android:layout_marginStart="9dp"
                android:includeFontPadding="false"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>

            <ImageView
                android:id="@+id/iv_close_ai"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/icon_close_airdoc_ai"
                android:layout_marginEnd="5dp"
                android:padding="5dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#66C5C1FB"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.airdoc.component.common.ui.web.CustomWebView
            android:id="@+id/chat_web"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>