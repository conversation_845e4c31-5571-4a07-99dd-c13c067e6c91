<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/app_launcher_bg">

    <com.mitdd.gazetracker.common.widget.CommonExceptionView
        android:id="@+id/cl_exception"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:scaleType="fitStart"
        android:layout_marginTop="28dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/icon_device_exception_menu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <View
        android:id="@+id/view_config"
        android:layout_width="268dp"
        android:layout_height="25dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>