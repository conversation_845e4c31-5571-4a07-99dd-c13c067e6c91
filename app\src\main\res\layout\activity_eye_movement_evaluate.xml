<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_read_main_logo"
        android:layout_marginTop="98dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_eye_movement_evaluate_system"
        android:textColor="@color/color_333333"
        android:textSize="40sp"
        android:textStyle="bold"
        android:layout_marginTop="160dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_gaze_stability"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:text="@string/str_gaze_stability"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/read_common_bg"
        android:layout_marginBottom="150dp"
        android:layout_marginStart="100dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_follow_ability"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_follow_ability"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:text="@string/str_follow_ability"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/read_common_bg"
        android:layout_marginBottom="150dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_gaze_stability"
        app:layout_constraintRight_toLeftOf="@+id/tv_saccade_ability"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_saccade_ability"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:text="@string/str_saccade_ability"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/read_common_bg"
        android:layout_marginBottom="150dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_follow_ability"
        app:layout_constraintRight_toLeftOf="@+id/tv_roi_detection"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_roi_detection"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:text="@string/str_roi_detection"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/read_common_bg"
        android:layout_marginBottom="150dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="100dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_saccade_ability"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_calibration"
        android:layout_width="wrap_content"
        android:layout_height="38dp"
        android:minWidth="115dp"
        android:text="@string/str_gaze_track_calibration"
        android:textColor="#54689A"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d7dce9_round_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_patient_information"
        android:layout_width="wrap_content"
        android:layout_height="38dp"
        android:minWidth="115dp"
        android:text="@string/str_patient_information"
        android:textColor="#54689A"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d7dce9_round_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintRight_toLeftOf="@+id/tv_calibration"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>