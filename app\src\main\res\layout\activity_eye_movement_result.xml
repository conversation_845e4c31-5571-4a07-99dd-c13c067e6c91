<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:id="@+id/result_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#EEF3F6"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_back_black_fine"
            android:layout_marginTop="34dp"
            android:layout_marginStart="19dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_saccade_ability_evaluate_result"
            android:textColor="#334067"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="30dp"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_evaluate_result"
            android:layout_width="80dp"
            android:layout_height="25dp"
            tools:text="@string/str_instability"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            android:gravity="center"
            tools:background="@drawable/common_eb4e89_round_5_bg"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_title"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_serial_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="NO.120901001"
            android:textColor="#54699A"
            android:textSize="10sp"
            android:layout_marginTop="10dp"
            app:layout_constraintLeft_toLeftOf="@+id/tv_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2024-12-09"
            android:textColor="#54699A"
            android:textSize="10sp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toRightOf="@+id/tv_serial_number"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

        <TextView
            android:id="@+id/tv_point_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="扫视个数：300"
            android:textColor="#54699A"
            android:textSize="10sp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toRightOf="@+id/tv_date"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

        <TextView
            android:id="@+id/tv_average_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="平均时长：200ms"
            android:textColor="#54699A"
            android:textSize="10sp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="15dp"
            app:layout_constraintLeft_toRightOf="@+id/tv_point_number"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

        <TextView
            android:id="@+id/tv_export_data"
            android:layout_width="115dp"
            android:layout_height="38dp"
            android:text="@string/str_export_data"
            android:textColor="#54689A"
            android:textSize="17sp"
            android:gravity="center"
            android:background="@drawable/common_d7dce9_round_bg"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="15dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="115dp"
            android:layout_height="38dp"
            android:text="@string/str_save"
            android:textColor="#54689A"
            android:textSize="17sp"
            android:gravity="center"
            android:background="@drawable/common_d7dce9_round_bg"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="15dp"
            app:layout_constraintRight_toLeftOf="@+id/tv_export_data"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>