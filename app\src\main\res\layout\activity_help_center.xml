<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_help_center_bg">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="17dp"
        android:layout_height="30dp"
        android:src="@drawable/icon_back_white_coarse"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="36dp"
        android:layout_marginStart="30dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_help"
        android:textSize="22sp"
        android:textColor="@color/white"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="40dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_material"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="95dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>