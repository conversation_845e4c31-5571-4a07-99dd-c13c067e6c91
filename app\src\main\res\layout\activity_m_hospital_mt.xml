<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#EEF3F6">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#3F96FF"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_back"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="88dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="20dp"
            android:background="@drawable/common_white_20_round_20_bg"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <ImageView
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:src="@drawable/icon_back_fine_line_arrow"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_back"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:layout_marginStart="5dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_calibrate"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="88dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ll_back"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="5dp"
            android:background="@drawable/common_white_20_round_20_bg"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <ImageView
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:src="@drawable/icon_calibration_m"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_gaze_track_calibration"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:layout_marginStart="5dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_setting"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="88dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ll_calibrate"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="5dp"
            android:background="@drawable/common_white_20_round_20_bg"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <ImageView
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:src="@drawable/icon_advanced_settings"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_advanced_settings"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:layout_marginStart="5dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_patient_library"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="88dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="20dp"
            android:background="@drawable/common_white_20_round_20_bg"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <ImageView
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:src="@drawable/icon_patient_library"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_patient_library"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:layout_marginStart="5dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_new_patient"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="88dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ll_patient_library"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="5dp"
            android:background="@drawable/common_white_20_round_20_bg"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <ImageView
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:src="@drawable/icon_new_patient"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_new_patient"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:layout_marginStart="5dp"/>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_current_patient"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="当前患者：刘博文"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ll_new_patient"
            app:layout_constraintRight_toLeftOf="@+id/ll_setting"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fl_patient_library"
        android:layout_width="270dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_content_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="20dp"
        app:layout_goneMarginStart="20dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/fl_patient_library"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>