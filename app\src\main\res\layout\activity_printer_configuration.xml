<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_coarse"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_printer_configuration"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_select_printer_driver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_select_printer_driver"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_back"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_printer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_select_printer_driver"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>