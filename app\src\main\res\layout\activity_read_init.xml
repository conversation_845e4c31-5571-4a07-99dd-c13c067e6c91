<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EEF3F6 ">

    <TextView
        android:id="@+id/tv_tab_basic_info"
        android:layout_width="287dp"
        android:layout_height="20dp"
        android:background="@drawable/selector_read_init_tab_bg"
        android:text="@string/str_basic_info"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:gravity="center_vertical"
        android:paddingStart="25dp"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_tab_calibration"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_tab_calibration"
        android:layout_width="287dp"
        android:layout_height="20dp"
        android:background="@drawable/selector_read_init_tab_bg"
        android:text="@string/str_gaze_track_calibration"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:gravity="center_vertical"
        android:paddingStart="25dp"
        android:layout_marginTop="32dp"
        android:layout_marginStart="5dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_tab_basic_info"
        app:layout_constraintRight_toLeftOf="@+id/tv_tab_start_evaluate"/>

    <TextView
        android:id="@+id/tv_tab_start_evaluate"
        android:layout_width="287dp"
        android:layout_height="20dp"
        android:background="@drawable/selector_read_init_tab_bg"
        android:text="@string/str_start_evaluating"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:gravity="center_vertical"
        android:paddingStart="25dp"
        android:layout_marginTop="32dp"
        android:layout_marginStart="5dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_tab_calibration"
        app:layout_constraintRight_toRightOf="parent"/>

    <FrameLayout
        android:id="@+id/fl_read_init_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>