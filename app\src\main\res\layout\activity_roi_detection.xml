<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_picture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerInside"/>

    <com.mitdd.gazetracker.movement.roi.ROIPathView
        android:id="@+id/roi_path_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_settings"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_settings_unselect"
        android:scaleType="centerInside"
        android:background="@drawable/common_black_20_round_bg"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_clear_roi"
        android:layout_width="wrap_content"
        android:layout_height="45dp"
        android:text="@string/str_clear_roi"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:background="@drawable/common_black_20_round_bg"
        android:layout_marginTop="30dp"
        android:layout_marginStart="30dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_save_roi"
        android:layout_width="wrap_content"
        android:layout_height="45dp"
        android:text="@string/str_save_roi"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:background="@drawable/common_black_20_round_bg"
        android:layout_marginTop="10dp"
        android:layout_marginStart="30dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_clear_roi"/>

</androidx.constraintlayout.widget.ConstraintLayout>