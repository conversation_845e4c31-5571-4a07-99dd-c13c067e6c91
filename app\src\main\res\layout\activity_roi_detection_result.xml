<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_picture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerInside"/>

    <com.mitdd.gazetracker.movement.roi.ROIPathView
        android:id="@+id/roi_path_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.mitdd.gazetracker.movement.roi.ROIDetectionResultView
        android:id="@+id/roi_detection_result"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>