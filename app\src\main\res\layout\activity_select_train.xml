<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/icon_select_train_bg">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="17dp"
        android:layout_height="28dp"
        android:src="@drawable/icon_select_train_back"
        android:layout_marginTop="30dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="240dp"
        android:layout_height="40dp"
        tools:text="刺激训练"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:gravity="center"
        android:background="@drawable/common_white_20_round_20_bg"
        android:layout_marginTop="63dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_train"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="140dp"
        android:layout_marginStart="140dp"
        android:layout_marginEnd="140dp" />

</androidx.constraintlayout.widget.ConstraintLayout>