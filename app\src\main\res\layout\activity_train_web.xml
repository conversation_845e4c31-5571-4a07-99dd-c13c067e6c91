<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.mitdd.gazetracker.medicalhome.train.TrainWebView
        android:id="@+id/wv_train"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:id="@+id/ll_train_warning"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/icon_train_alert_box_bg"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/icon_trainer_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_train_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="距离太近，请保持屏幕距离40-50cm."
            android:textColor="@color/white"
            android:textSize="11sp"
            android:textStyle="bold"
            android:layout_marginStart="8dp"/>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_keep_time"
        android:layout_width="75dp"
        android:layout_height="20dp"
        tools:text="05:23"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:textSize="14sp"
        android:includeFontPadding="false"
        android:gravity="center"
        android:background="@drawable/train_time_keep_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_close_train"
        android:layout_width="55dp"
        android:layout_height="55dp"
        android:src="@drawable/icon_close_train"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>