<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_main_bg">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="37dp"
        android:layout_height="50dp"
        android:src="@drawable/icon_back_white_coarse"
        android:layout_marginTop="30dp"
        android:layout_marginStart="20dp"
        android:padding="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_treatment_management"
        android:textColor="@color/white"
        android:textSize="22sp"
        android:textStyle="bold"
        android:layout_marginTop="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <com.mitdd.gazetracker.common.widget.CommonEmptyView
        android:id="@+id/view_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="96dp"
        android:layout_marginBottom="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:visibility="gone"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_treatments"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="96dp"
        android:layout_marginBottom="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:padding="20dp"
        android:background="@drawable/common_white_round_25_bg"/>

</androidx.constraintlayout.widget.ConstraintLayout>