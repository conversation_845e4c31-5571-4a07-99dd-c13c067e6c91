<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_tsc_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        tools:src="@drawable/icon_airdoc_digital_therapy_center"
        android:scaleType="fitStart"
        android:layout_marginTop="28dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_multimodal_psychological_assessment"
        android:textColor="@color/color_333333"
        android:textSize="35sp"
        android:includeFontPadding="false"
        android:layout_marginTop="172dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/fl_started"
        app:layout_constraintRight_toRightOf="@+id/fl_started"/>

    <TextView
        android:id="@+id/tv_intro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_multimodal_psychological_assessment_intro"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="@+id/fl_started"
        app:layout_constraintRight_toRightOf="@+id/fl_started"/>

    <FrameLayout
        android:id="@+id/fl_started"
        android:layout_width="280dp"
        android:layout_height="55dp"
        android:layout_marginEnd="98dp"
        android:layout_marginBottom="98dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <ProgressBar
            android:id="@+id/progress_update"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="?android:attr/progressBarStyleHorizontal"
            tools:progress="50"
            android:max="100"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:splitTrack="false"
            android:progressDrawable="@drawable/seekbar_tsc_update_progress_with_corners"
            android:thumb="@null" />

        <TextView
            android:id="@+id/tv_started"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:text="@string/str_get_started"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:gravity="center"
            android:textStyle="bold"/>

    </FrameLayout>

    <View
        android:id="@+id/view_config"
        android:layout_width="268dp"
        android:layout_height="25dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>