<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_tutorial_root">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_outer_frame"
        android:layout_width="500dp"
        android:layout_height="405dp"
        android:background="@drawable/common_eff3f6_round_20_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_tutorial"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:includeFontPadding="false"
            android:layout_marginTop="30dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_bring_into_use"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:text="@string/str_get_started"
            android:textColor="@color/white"
            android:textSize="17sp"
            android:background="@drawable/selector_bring_into_use_bg"
            android:gravity="center"
            android:enabled="false"
            android:layout_marginBottom="30dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_play_container"
        android:layout_width="435dp"
        android:layout_height="245dp"
        android:layout_marginTop="137dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.media3.ui.PlayerView
            android:id="@+id/player_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:use_controller="false" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_controller"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/common_333333_60_bg"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/iv_play_control_play"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/selector_play_control_play_icon"
                android:layout_marginStart="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <ImageView
                android:id="@+id/iv_play_control_full_screen"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/selector_play_control_full_screen_icon"
                android:layout_marginEnd="12dp"
                app:layout_constraintRight_toLeftOf="@+id/tv_play_control_bring_into_use"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/tv_played_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="05:34"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:includeFontPadding="false"
                android:layout_marginStart="10dp"
                app:layout_constraintLeft_toRightOf="@+id/iv_play_control_play"
                app:layout_constraintTop_toTopOf="@+id/played_progress_bar"
                app:layout_constraintBottom_toBottomOf="@+id/played_progress_bar"/>

            <TextView
                android:id="@+id/tv_total_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="23:12"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:includeFontPadding="false"
                android:layout_marginEnd="10dp"
                app:layout_constraintRight_toLeftOf="@+id/iv_play_control_full_screen"
                app:layout_constraintTop_toTopOf="@+id/played_progress_bar"
                app:layout_constraintBottom_toBottomOf="@+id/played_progress_bar"/>

            <SeekBar
                android:id="@+id/played_progress_bar"
                android:layout_width="0dp"
                android:layout_height="9dp"
                tools:progress="30"
                tools:max="90"
                android:splitTrack="false"
                android:progressDrawable="@drawable/play_progress_drawable"
                android:thumb="@drawable/play_progress_thumb"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_played_duration"
                app:layout_constraintRight_toLeftOf="@+id/tv_total_duration"/>

            <TextView
                android:id="@+id/tv_play_control_bring_into_use"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:text="@string/str_get_started"
                android:textColor="@color/white"
                android:textSize="17sp"
                android:background="@drawable/selector_bring_into_use_bg"
                android:gravity="center"
                android:enabled="false"
                android:layout_marginEnd="20dp"
                android:visibility="gone"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>