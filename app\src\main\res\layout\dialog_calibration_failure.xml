<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="360dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_eff3f6_round_20_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_calibration_failure"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/player_view"
        app:layout_constraintVertical_chainStyle="packed"/>

    <androidx.media3.ui.PlayerView
        android:id="@+id/player_view"
        android:layout_width="300dp"
        android:layout_height="168dp"
        app:use_controller="false"
        android:layout_marginTop="28dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_cancel_calibration"/>

    <ImageView
        android:id="@+id/iv_play_control_play"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@drawable/selector_play_control_play_icon"
        android:scaleType="centerInside"
        android:background="@drawable/common_333333_60_round_bg"
        app:layout_constraintLeft_toLeftOf="@+id/player_view"
        app:layout_constraintRight_toRightOf="@+id/player_view"
        app:layout_constraintTop_toTopOf="@+id/player_view"
        app:layout_constraintBottom_toBottomOf="@+id/player_view"/>

    <TextView
        android:id="@+id/tv_cancel_calibration"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel_calibration"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/login_cancel_bind_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/player_view"
        app:layout_constraintRight_toLeftOf="@+id/tv_recalibration"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_recalibration"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_recalibration"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginStart="15dp"
        app:layout_constraintTop_toTopOf="@+id/tv_cancel_calibration"
        app:layout_constraintBottom_toBottomOf="@+id/tv_cancel_calibration"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel_calibration"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>