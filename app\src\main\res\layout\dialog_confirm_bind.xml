<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="450dp"
    android:layout_height="315dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/confirm_bind_dialog_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_device"
        android:layout_width="160dp"
        android:layout_height="160dp"
        android:background="@drawable/common_white_round_30_bg"
        android:layout_marginTop="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_device_bind_links"
        app:layout_constraintHorizontal_chainStyle="packed">

        <ImageView
            android:id="@+id/iv_device"
            android:layout_width="110dp"
            android:layout_height="83dp"
            android:layout_marginTop="20dp"
            android:src="@drawable/icon_device"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_sn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="设备SN：\n882883223"
            android:textSize="13sp"
            android:textColor="@color/color_333333"
            android:includeFontPadding="false"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="13dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_device"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_device_bind_links"
        android:layout_width="70dp"
        android:layout_height="30dp"
        android:src="@drawable/icon_device_bind_links"
        app:layout_constraintTop_toTopOf="@+id/cl_device"
        app:layout_constraintBottom_toBottomOf="@+id/cl_device"
        app:layout_constraintLeft_toRightOf="@+id/cl_device"
        app:layout_constraintRight_toLeftOf="@+id/cl_user"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_user"
        android:layout_width="160dp"
        android:layout_height="160dp"
        android:background="@drawable/common_white_round_30_bg"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_device_bind_links">

        <ImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            tools:src="@drawable/icon_user_avatar_female_big"
            android:scaleType="centerCrop"
            android:layout_marginTop="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="张思思"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginTop="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_user_avatar"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_user_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_user_name">

            <TextView
                android:id="@+id/tv_gender"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="女"
                android:textColor="#999999"
                android:textSize="12sp"
                android:includeFontPadding="false"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>

            <TextView
                android:id="@+id/tv_age"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="6岁"
                android:textColor="#999999"
                android:textSize="12sp"
                android:includeFontPadding="false"
                android:layout_marginStart="5dp"
                app:layout_goneMarginStart="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_gender"/>

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="13863322400"
                android:textColor="#999999"
                android:textSize="12sp"
                android:includeFontPadding="false"
                android:layout_marginStart="5dp"
                app:layout_goneMarginStart="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_age"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_confirm_equipment_and_patient_information_correct"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_device"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="120dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/login_cancel_bind_bg"
        android:gravity="center"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_prompt"
        app:layout_constraintRight_toLeftOf="@+id/tv_confirm_bind"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_confirm_bind"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="120dp"
        android:text="@string/str_activate_use"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="15dp"
        app:layout_constraintTop_toTopOf="@+id/tv_cancel"
        app:layout_constraintBottom_toBottomOf="@+id/tv_cancel"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>