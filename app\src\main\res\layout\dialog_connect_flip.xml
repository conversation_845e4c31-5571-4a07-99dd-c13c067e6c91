<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="490dp"
    android:layout_height="340dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_25_bg">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:src="@drawable/icon_close_dialog"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>


    <TextView
        android:id="@+id/tv_available_equipment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_available_device"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="45dp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_refresh_flip"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:src="@drawable/icon_refresh_flip"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_available_equipment"
        app:layout_constraintBottom_toBottomOf="@+id/tv_available_equipment"
        app:layout_constraintLeft_toRightOf="@+id/tv_available_equipment"/>

    <View
        android:id="@+id/view_content"
        android:layout_width="match_parent"
        android:layout_height="182dp"
        android:background="@drawable/common_white_round_15_bg"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_available_equipment"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_flip"
        android:layout_width="match_parent"
        android:layout_height="182dp"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_available_equipment"/>

    <TextView
        android:id="@+id/tv_no_available_device"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_no_bluetooth_devices_available"
        android:textColor="@color/color_333333"
        android:textSize="13sp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@+id/view_content"
        app:layout_constraintRight_toRightOf="@+id/view_content"
        app:layout_constraintTop_toTopOf="@+id/view_content"
        app:layout_constraintBottom_toBottomOf="@+id/view_content"/>

    <ImageView
        android:id="@+id/iv_scan"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/icon_scan_flip"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@+id/view_content"
        app:layout_constraintRight_toRightOf="@+id/view_content"
        app:layout_constraintTop_toTopOf="@+id/view_content"
        app:layout_constraintBottom_toBottomOf="@+id/view_content"/>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginBottom="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>