<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="300dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_20_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="@string/str_airdoc_masking_therapy_on"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/icon_close_dialog"
        android:padding="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="@string/str_please_select_correct_glasses_red_s"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:textStyle="bold"
        android:gravity="center"
        android:includeFontPadding="false"
        android:layout_marginTop="8dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <ImageView
        android:id="@+id/iv_glasses"
        android:layout_width="200dp"
        android:layout_height="125dp"
        android:src="@drawable/icon_m_hospital_mt_open_glasses"
        android:layout_marginTop="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle"/>

    <TextView
        android:id="@+id/tv_red_lens"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_red_lens"
        android:textColor="#f26200"
        android:textSize="12sp"
        android:layout_marginStart="16dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_glasses"
        app:layout_constraintBottom_toBottomOf="@+id/iv_glasses"/>

    <TextView
        android:id="@+id/tv_dominant_eye"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_dominant_eye"
        android:textColor="#f26200"
        android:textSize="12sp"
        android:layout_marginEnd="16dp"
        app:layout_constraintRight_toRightOf="@+id/iv_glasses"
        app:layout_constraintBottom_toBottomOf="@+id/iv_glasses"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="115dp"
        android:layout_height="40dp"
        android:text="@string/str_well"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:layout_marginBottom="25dp"
        android:background="@drawable/reset_param_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>