<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="300dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_20_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_airdoc_myopia_control_on"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/icon_close_dialog"
        android:padding="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_please_maintain_correct_posture_watch_content"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:layout_marginTop="8dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <ImageView
        android:id="@+id/iv_state"
        android:layout_width="140dp"
        android:layout_height="115dp"
        android:src="@drawable/icon_myopia_control_state_start"
        android:layout_marginTop="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="115dp"
        android:layout_height="40dp"
        android:text="@string/str_well"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:layout_marginBottom="25dp"
        android:background="@drawable/reset_param_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>