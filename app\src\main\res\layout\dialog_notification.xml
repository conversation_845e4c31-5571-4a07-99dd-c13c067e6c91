<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="350dp"
    android:layout_height="195dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_20_bg">

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="一句话消息"
        android:textSize="20sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="70dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:layout_marginBottom="30dp"
        android:background="@drawable/reset_param_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:src="@drawable/icon_close_dialog"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>