<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="498dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_15_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_review_reminder"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/cl_content"
        app:layout_constraintVertical_chainStyle="packed"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="435dp"
        android:layout_height="175dp"
        android:background="@drawable/common_white_round_30_bg"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm">

        <TextView
            android:id="@+id/tv_review_days"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="医生建议 3 天后到院复查"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_marginTop="40dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="68dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/common_8b86f3_bottom_round_30_bg">

            <TextView
                android:id="@+id/tv_review_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="复查日期：2024-12-12"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:includeFontPadding="false"
                android:layout_marginTop="10dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/tv_hospital_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="医院名称：****************"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:includeFontPadding="false"
                android:layout_marginBottom="10dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_i_know"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>