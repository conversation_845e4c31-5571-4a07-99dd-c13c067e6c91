<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="55dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_15_bg"
    android:paddingStart="20dp"
    android:paddingEnd="20dp">

    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/icon_train_end"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_train_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="直升机捕鸟"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:layout_marginTop="10dp"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_train_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="训练时间0分9秒"
        android:textColor="@color/color_333333"
        android:textSize="12sp"
        android:includeFontPadding="false"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_train_name"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>