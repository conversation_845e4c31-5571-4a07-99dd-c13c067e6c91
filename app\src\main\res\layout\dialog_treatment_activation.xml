<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="498dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_15_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_treatment_activation"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/cl_content"
        app:layout_constraintVertical_chainStyle="packed"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="435dp"
        android:layout_height="175dp"
        android:background="@drawable/common_white_round_30_bg"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_cancel">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_please_enter_reserved_phone_number"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_marginTop="28dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:orientation="horizontal"
            android:layout_marginTop="90dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_number1"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"/>

            <TextView
                android:id="@+id/tv_number2"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <TextView
                android:id="@+id/tv_number3"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <TextView
                android:id="@+id/tv_number4"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <TextView
                android:id="@+id/tv_number5"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <TextView
                android:id="@+id/tv_number6"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <TextView
                android:id="@+id/tv_number7"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@drawable/common_eff3f6_round_8_bg"
                tools:text="1"
                android:textColor="#8D9EAC"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:gravity="center"
                android:layout_marginStart="5dp"/>

            <com.mitdd.gazetracker.common.widget.SeparatedEditText
                android:id="@+id/et_phone_tail_number"
                android:layout_width="120dp"
                android:layout_height="match_parent"
                app:password="false"
                app:showCursor="true"
                app:separateType="@integer/type_solid"
                app:maxLength="4"
                app:corner="8dp"
                app:borderColor="#EFF3F6"
                app:borderWidth="1dp"
                app:blockColor="#EFF3F6"
                app:blockSpacing="5dp"
                app:textColor="@color/color_333333"
                app:textSize="17sp"
                app:cursorColor="#EB4E89"
                app:cursorWidth="1dp"
                app:cursorDuration="1000"
                app:highLightEnable="false"
                app:highLightBefore="false"
                app:showKeyboard="false"
                android:layout_marginStart="5dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_error_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_end_number_incorrectly_entered"
            android:textColor="#EB4E89"
            android:textSize="12sp"
            android:includeFontPadding="false"
            android:layout_marginBottom="20dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/login_cancel_bind_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_confirm"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginStart="15dp"
        app:layout_constraintTop_toTopOf="@+id/tv_cancel"
        app:layout_constraintBottom_toBottomOf="@+id/tv_cancel"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>