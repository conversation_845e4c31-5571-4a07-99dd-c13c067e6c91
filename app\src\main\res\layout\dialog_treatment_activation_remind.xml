<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="498dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_15_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_treatment_activation"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/cl_content"
        app:layout_constraintVertical_chainStyle="packed"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="435dp"
        android:layout_height="175dp"
        android:background="@drawable/common_white_round_30_bg"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_cancel">

        <TextView
            android:id="@+id/tv_treatment_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_you_have_renewed_your_treatment"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_marginTop="40dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="68dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/common_8b86f3_bottom_round_30_bg">

            <TextView
                android:id="@+id/tv_card_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="卡号：89365863"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:includeFontPadding="false"
                android:layout_marginTop="10dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/tv_period"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="疗程周期：1年"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:includeFontPadding="false"
                android:layout_marginBottom="10dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/login_cancel_bind_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_confirm"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_immediate_activation"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginStart="15dp"
        app:layout_constraintTop_toTopOf="@+id/tv_cancel"
        app:layout_constraintBottom_toBottomOf="@+id/tv_cancel"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>