<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <Space
            android:id="@+id/space_median"
            android:layout_width="1px"
            android:layout_height="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier1_end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="tv_name,tv_inpatient_number,tv_birthday,tv_patient_type,tv_diagnosis_information"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier2_end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="tv_phone,tv_case_card_number,tv_gender"/>

        <EditText
            android:id="@+id/et_name"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/new_user_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/space_median"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_name"
            android:gravity="end"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="@+id/et_name"
            app:layout_constraintBottom_toBottomOf="@+id/et_name" />

        <EditText
            android:id="@+id/et_phone"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/new_user_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:inputType="phone"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="@+id/barrier2_end"
            app:layout_constraintRight_toRightOf="parent"/>

        <TextView
            android:id="@+id/tv_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_phone"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="@+id/space_median"
            app:layout_constraintRight_toRightOf="@+id/barrier2_end"
            app:layout_constraintTop_toTopOf="@+id/et_phone"
            app:layout_constraintBottom_toBottomOf="@+id/et_phone" />

        <EditText
            android:id="@+id/et_inpatient_number"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/new_user_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier1_end"
            app:layout_constraintTop_toBottomOf="@+id/et_name"
            app:layout_constraintRight_toLeftOf="@+id/space_median"/>

        <TextView
            android:id="@+id/tv_inpatient_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_inpatient_number"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="@+id/et_inpatient_number"
            app:layout_constraintBottom_toBottomOf="@+id/et_inpatient_number" />

        <EditText
            android:id="@+id/et_case_card_number"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/new_user_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier2_end"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/et_phone" />

        <TextView
            android:id="@+id/tv_case_card_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_case_card_number"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="@+id/space_median"
            app:layout_constraintRight_toRightOf="@+id/barrier2_end"
            app:layout_constraintTop_toTopOf="@+id/et_case_card_number"
            app:layout_constraintBottom_toBottomOf="@+id/et_case_card_number" />

        <TextView
            android:id="@+id/et_birthday"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:background="@drawable/new_user_input_bg"
            android:singleLine="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier1_end"
            app:layout_constraintRight_toLeftOf="@+id/space_median"
            app:layout_constraintTop_toBottomOf="@+id/et_inpatient_number"/>

        <TextView
            android:id="@+id/tv_birthday"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_birthday"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="@+id/et_birthday"
            app:layout_constraintBottom_toBottomOf="@+id/et_birthday" />

        <RadioGroup
            android:id="@+id/rg_gender"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:background="@drawable/new_user_input_bg"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier2_end"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/et_case_card_number">

            <RadioButton
                android:id="@+id/rb_male"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:checked="true"
                android:text="@string/str_male"
                android:button="@drawable/selector_input_em_patient_radio_button"
                android:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                app:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                android:paddingStart="5dp"
                android:textSize="15sp"
                android:textColor="@color/color_333333"/>

            <RadioButton
                android:id="@+id/rb_female"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_female"
                android:button="@drawable/selector_input_em_patient_radio_button"
                android:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                app:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                android:paddingStart="5dp"
                android:textSize="15sp"
                android:textColor="@color/color_333333"
                android:layout_marginStart="10dp"/>

        </RadioGroup>

        <TextView
            android:id="@+id/tv_gender"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_gender"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="@+id/space_median"
            app:layout_constraintRight_toRightOf="@+id/barrier2_end"
            app:layout_constraintTop_toTopOf="@+id/rg_gender"
            app:layout_constraintBottom_toBottomOf="@+id/rg_gender" />

        <RadioGroup
            android:id="@+id/rg_patient_type"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:background="@drawable/new_user_input_bg"
            android:layout_marginEnd="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier1_end"
            app:layout_constraintTop_toBottomOf="@+id/et_birthday"
            app:layout_constraintRight_toLeftOf="@+id/space_median">

            <RadioButton
                android:id="@+id/rb_positive"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:checked="true"
                android:text="@string/str_positive"
                android:button="@drawable/selector_input_em_patient_radio_button"
                android:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                app:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                android:paddingStart="5dp"
                android:textSize="15sp"
                android:textColor="@color/color_333333"/>

            <RadioButton
                android:id="@+id/rb_negative"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_negative"
                android:button="@drawable/selector_input_em_patient_radio_button"
                android:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                app:buttonTint="@color/selector_input_em_patient_radio_button_tint"
                android:paddingStart="5dp"
                android:textSize="15sp"
                android:textColor="@color/color_333333"
                android:layout_marginStart="10dp"/>

        </RadioGroup>

        <TextView
            android:id="@+id/tv_patient_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_patient_type"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="@+id/rg_patient_type"
            app:layout_constraintBottom_toBottomOf="@+id/rg_patient_type" />

        <EditText
            android:id="@+id/et_diagnosis_information"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:minHeight="40dp"
            android:textSize="15sp"
            android:textColor="@color/color_333333"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/common_white_round_20_bg"
            android:imeOptions="actionNext"
            android:padding="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="top|start"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintLeft_toLeftOf="@+id/barrier1_end"
            app:layout_constraintTop_toBottomOf="@+id/rg_patient_type"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tv_ok"/>

        <TextView
            android:id="@+id/tv_diagnosis_information"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="100dp"
            android:text="@string/str_diagnosis_information"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:layout_marginEnd="5dp"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/barrier1_end"
            app:layout_constraintTop_toTopOf="@+id/et_diagnosis_information"
            app:layout_constraintBottom_toBottomOf="@+id/et_diagnosis_information" />

        <TextView
            android:id="@+id/tv_ok"
            android:layout_width="150dp"
            android:layout_height="40dp"
            android:text="@string/str_ok"
            android:textColor="@color/white"
            android:textSize="17sp"
            android:background="@drawable/common_5794ff_round_bg"
            android:gravity="center"
            android:layout_marginBottom="50dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>