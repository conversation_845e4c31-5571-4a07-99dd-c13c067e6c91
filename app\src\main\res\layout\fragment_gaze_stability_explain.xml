<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="#EFF3F6">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_fine"
        android:layout_marginTop="34dp"
        android:layout_marginStart="19dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:layout_width="95dp"
        android:layout_height="20dp"
        android:src="@drawable/icon_read_main_logo"
        android:layout_marginTop="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_gaze_stability_evaluate"
        android:textColor="#334067"
        android:textSize="25sp"
        android:textStyle="bold"
        android:layout_marginTop="60dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/view_bg1"
        android:layout_width="600dp"
        android:layout_height="315dp"
        android:layout_marginTop="115dp"
        android:background="@drawable/common_white_round_25_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/view_bg2"
        android:layout_width="450dp"
        android:layout_height="200dp"
        android:background="@drawable/common_a1adc8_10_round_10_bg"
        android:layout_marginTop="35dp"
        app:layout_constraintLeft_toLeftOf="@+id/view_bg1"
        app:layout_constraintRight_toRightOf="@+id/view_bg1"
        app:layout_constraintTop_toTopOf="@+id/view_bg1"/>

    <View
        android:id="@+id/view_point"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:background="@drawable/common_black_round_bg"
        app:layout_constraintLeft_toLeftOf="@+id/view_bg2"
        app:layout_constraintRight_toRightOf="@+id/view_bg2"
        app:layout_constraintTop_toTopOf="@+id/view_bg2"
        app:layout_constraintBottom_toBottomOf="@+id/view_bg2"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_gaze_stability_evaluate_explain"
        android:textColor="@color/color_333333"
        android:textSize="17sp"
        android:layout_marginBottom="28dp"
        app:layout_constraintLeft_toLeftOf="@+id/view_bg1"
        app:layout_constraintRight_toRightOf="@+id/view_bg1"
        app:layout_constraintBottom_toBottomOf="@+id/view_bg1"/>

    <TextView
        android:id="@+id/tv_start_evaluating"
        android:layout_width="225dp"
        android:layout_height="50dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:text="@string/str_start_evaluating"
        android:textSize="18sp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>