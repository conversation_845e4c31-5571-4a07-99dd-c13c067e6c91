<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_medical_hospital_init_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        tools:src="@drawable/icon_airdoc_digital_therapy_center"
        android:scaleType="fitStart"
        android:layout_marginTop="25dp"
        android:layout_marginStart="25dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <ImageView
        android:id="@+id/iv_vision_station_smart"
        android:layout_width="245dp"
        android:layout_height="28dp"
        android:src="@drawable/icon_vision_station_smart"
        android:layout_marginTop="120dp"
        android:layout_marginEnd="115dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_visual_training_station"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_visual_training_station"
        android:textColor="@color/color_333333"
        android:textSize="40sp"
        android:textStyle="bold"
        android:layout_marginTop="162dp"
        android:layout_marginEnd="190dp"
        android:includeFontPadding="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:layout_width="110dp"
        android:layout_height="40dp"
        android:text="@string/str_intelligent_version"
        android:textColor="@color/white"
        android:textSize="28sp"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:gravity="center"
        android:background="@drawable/common_d74d80_round_8_bg"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_visual_training_station"
        app:layout_constraintBottom_toBottomOf="@+id/tv_visual_training_station"
        app:layout_constraintLeft_toRightOf="@+id/tv_visual_training_station"/>

    <TextView
        android:id="@+id/tv_get_started"
        android:layout_width="280dp"
        android:layout_height="55dp"
        android:text="@string/str_get_started"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/common_5794ff_round_bg"
        android:layout_marginBottom="113dp"
        android:layout_marginEnd="98dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>