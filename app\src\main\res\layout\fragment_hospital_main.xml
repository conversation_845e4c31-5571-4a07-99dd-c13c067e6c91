<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/icon_hospital_main_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        tools:src="@drawable/icon_airdoc_digital_therapy_center"
        android:scaleType="fitStart"
        android:layout_marginTop="38dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_more"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="25dp">

        <ImageView
            android:id="@+id/iv_more"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/icon_m_hospital_more" />

        <ImageView
            android:id="@+id/iv_more_red_dot"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/icon_red_dot"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_hospital_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="北京******眼视光中心"
        android:textColor="@color/color_333333"
        android:textSize="25sp"
        android:textStyle="bold"
        android:layout_marginTop="140dp"
        android:includeFontPadding="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_module"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="210dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_ai"
        android:layout_width="81dp"
        android:layout_height="68dp"
        android:src="@drawable/icon_airdoc_ten_thousand_language"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginStart="10dp"
        android:layout_marginBottom="55dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>