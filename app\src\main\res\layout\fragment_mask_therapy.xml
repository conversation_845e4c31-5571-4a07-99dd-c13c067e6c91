<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_mask_therapy_title"
        android:layout_width="match_parent"
        android:layout_height="123dp"
        android:background="@drawable/digital_mask_therapy_title_bg"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_mask_therapy"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/icon_mask_therapy"
            android:layout_marginTop="16dp"
            android:layout_marginStart="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_digital_mask_therapy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_digital_masking_therapy"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_marginStart="9dp"
            app:layout_constraintTop_toTopOf="@+id/iv_mask_therapy"
            app:layout_constraintBottom_toBottomOf="@+id/iv_mask_therapy"
            app:layout_constraintLeft_toRightOf="@+id/iv_mask_therapy"/>

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_mask_therapy"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:splitTrack="true"
            android:thumb="@drawable/switch_mask_therapy_thumb"
            app:track="@drawable/switch_mask_therapy_style"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="15dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <ImageView
            android:id="@+id/iv_red_blue_glasses"
            android:layout_width="34dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_left_red_right_blue_glasses_small"
            android:layout_marginEnd="10dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/switch_mask_therapy"
            app:layout_constraintBottom_toBottomOf="@+id/switch_mask_therapy"
            app:layout_constraintRight_toLeftOf="@+id/switch_mask_therapy"/>

        <com.mitdd.gazetracker.medicalhome.TimeProgress
            android:id="@+id/treatment_time_progress"
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:layout_marginTop="50dp"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.mitdd.gazetracker.common.widget.CommonAppView
        android:id="@+id/common_app_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/common_white_round_25_bg"
        android:layout_marginTop="97dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>