<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_white_round_15_bg">

    <com.mitdd.gazetracker.common.widget.CommonEmptyView
        android:id="@+id/common_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smart_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_train_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>