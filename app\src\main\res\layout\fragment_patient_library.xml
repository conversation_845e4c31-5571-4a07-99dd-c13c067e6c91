<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="270dp"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white">

    <EditText
        android:id="@+id/et_search"
        android:layout_width="228dp"
        android:layout_height="32dp"
        android:textSize="12sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#999999"
        android:hint="@string/str_enter_name_or_phone"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/search_patients_bg"
        android:imeOptions="actionSearch"
        android:singleLine="true"
        android:paddingStart="15dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="12dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/iv_search_patients"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/icon_search_patients"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/et_search"
        app:layout_constraintBottom_toBottomOf="@+id/et_search"
        app:layout_constraintRight_toRightOf="@+id/et_search"/>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smart_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/et_search"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_patient"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="12dp"
            android:paddingStart="20dp"
            android:paddingEnd="15dp"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


</androidx.constraintlayout.widget.ConstraintLayout>