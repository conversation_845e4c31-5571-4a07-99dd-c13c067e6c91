<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/app_launcher_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        tools:src="@drawable/icon_airdoc_digital_therapy_center"
        android:scaleType="fitCenter"
        android:layout_marginTop="109dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_visual_training_station"
        android:textColor="@color/color_333333"
        android:textSize="40sp"
        android:textStyle="bold"
        android:layout_marginTop="150dp"
        android:includeFontPadding="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:text="@string/str_home_training_edition"
        android:textColor="#CC2B5C"
        android:textSize="15sp"
        android:layout_marginTop="220dp"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:background="@drawable/common_cc2b5c_stroke_round_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_get_started"
        android:layout_width="280dp"
        android:layout_height="55dp"
        android:text="@string/str_get_started"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:layout_marginBottom="113dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>