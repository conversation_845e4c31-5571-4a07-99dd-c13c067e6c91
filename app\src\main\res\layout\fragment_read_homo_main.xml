<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/icon_main_bg">

    <ImageView
        android:id="@+id/iv_home_logo"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        tools:src="@drawable/icon_airdoc_digital_therapy_center"
        android:scaleType="fitStart"
        android:layout_marginTop="28dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/icon_more"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/iv_help_center"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_help_center"
        android:layout_marginEnd="15dp"
        app:layout_constraintTop_toTopOf="@+id/iv_more"
        app:layout_constraintRight_toLeftOf="@+id/iv_more"/>

    <ImageView
        android:id="@+id/iv_calibration"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_calibration"
        android:layout_marginEnd="15dp"
        app:layout_constraintTop_toTopOf="@+id/iv_help_center"
        app:layout_constraintRight_toLeftOf="@+id/iv_help_center"/>

    <ImageView
        android:id="@+id/iv_param_setting"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_param_setting"
        android:layout_marginEnd="15dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="@+id/iv_calibration"
        app:layout_constraintRight_toLeftOf="@+id/iv_calibration"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_user_info"
        android:layout_width="125dp"
        android:layout_height="45dp"
        android:layout_marginEnd="15dp"
        android:layout_marginTop="23dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_param_setting">

        <View
            android:id="@+id/view_user_bg"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/selector_user_info_bg"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:src="@drawable/icon_user_avatar_unbound"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_unbound"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_user_avatar"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_user_unbind"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:text="@string/str_unbound_account_bind_immediately"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:gravity="center"
        android:background="@drawable/user_unbind_round_bg"
        android:layout_marginTop="28dp"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_treatment_module"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="95dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.mitdd.gazetracker.common.widget.CommonExceptionView
        android:id="@+id/cl_network_exception"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/iv_ai"
        android:layout_width="81dp"
        android:layout_height="68dp"
        android:src="@drawable/icon_airdoc_ten_thousand_language"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginStart="10dp"
        android:layout_marginBottom="55dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>