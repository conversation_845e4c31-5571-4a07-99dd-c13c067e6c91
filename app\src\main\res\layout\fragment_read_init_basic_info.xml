<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="#EEF3F6">

    <TextView
        android:id="@+id/tv_identity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_you_are_now"
        android:textSize="20sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="150dp"
        android:layout_marginStart="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <RadioGroup
        android:id="@+id/rg_identity"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="50dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tv_identity"
        app:layout_constraintLeft_toLeftOf="parent">

        <RadioButton
            android:id="@+id/rb_identity_primary_school"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_primary_school_student"
            android:button="@null"
            android:gravity="center"
            tools:checked="true"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg" />

        <RadioButton
            android:id="@+id/rb_identity_junior_high_school"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_junior_high_school_student"
            android:button="@null"
            android:gravity="center"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg"
            android:layout_marginStart="20dp"/>

        <RadioButton
            android:id="@+id/rb_identity_senior_high_school"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_high_school_student"
            android:button="@null"
            android:gravity="center"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg"
            android:layout_marginStart="20dp"/>

        <RadioButton
            android:id="@+id/rb_identity_adult"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/str_adult"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_param_setting_mode_bg"
            android:layout_marginStart="20dp"/>

    </RadioGroup>

    <TextView
        android:id="@+id/tv_grade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_your_grade_is"
        android:textSize="20sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="40dp"
        android:layout_marginStart="44dp"
        app:layout_constraintTop_toBottomOf="@+id/rg_identity"
        app:layout_constraintLeft_toLeftOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <RadioGroup
        android:id="@+id/rg_grade"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="50dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tv_grade"
        app:layout_constraintLeft_toLeftOf="parent"
        android:visibility="gone"
        tools:visibility="visible">

        <RadioButton
            android:id="@+id/rb_grade_first"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_first_grade"
            android:button="@null"
            android:gravity="center"
            tools:checked="true"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg" />

        <RadioButton
            android:id="@+id/rb_grade_second"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_second_grade"
            android:button="@null"
            android:gravity="center"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg"
            android:layout_marginStart="20dp"/>

        <RadioButton
            android:id="@+id/rb_grade_three"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:text="@string/str_three_grade"
            android:button="@null"
            android:gravity="center"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_read_init_info_mode_bg"
            android:layout_marginStart="20dp"/>

        <RadioButton
            android:id="@+id/rb_grade_four"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/str_four_grade"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_param_setting_mode_bg"
            android:layout_marginStart="20dp" />

        <RadioButton
            android:id="@+id/rb_grade_five"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/str_five_grade"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_param_setting_mode_bg"
            android:layout_marginStart="20dp" />

        <RadioButton
            android:id="@+id/rb_grade_six"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/str_six_grade"
            android:textColor="@color/color_selector_read_init_info_mode"
            android:background="@drawable/selector_param_setting_mode_bg"
            android:layout_marginStart="20dp" />

    </RadioGroup>

</androidx.constraintlayout.widget.ConstraintLayout>