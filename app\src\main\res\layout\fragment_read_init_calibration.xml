<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="#EEF3F6">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_please_perform_eye_track_calibration"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginTop="170dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_start_calibration"
        android:layout_width="280dp"
        android:layout_height="80dp"
        android:text="@string/str_start_calibration"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:background="@drawable/read_common_bg"
        android:gravity="center"
        android:layout_marginTop="240dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_skip_calibration"
        android:layout_width="280dp"
        android:layout_height="80dp"
        android:text="@string/str_skip_calibration"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:background="@drawable/read_common_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_start_calibration" />

</androidx.constraintlayout.widget.ConstraintLayout>