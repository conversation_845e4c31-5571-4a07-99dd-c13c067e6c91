<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="#EEF3F6">

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="01：00"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluatingView
        android:id="@+id/evaluating_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_count_down"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#EEF3F6">

        <TextView
            android:id="@+id/tv_count_down"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:background="@drawable/read_count_down_bg"
            tools:text="3"
            android:textColor="#EB4F88"
            android:textSize="50sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="160dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_keep_posture_same_when_evaluating"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginTop="50dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_count_down"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>