<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_mask_therapy_title"
        android:layout_width="match_parent"
        android:layout_height="123dp"
        android:background="@drawable/visual_train_title_bg"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_visual_train"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/icon_visual_train"
            android:layout_marginTop="16dp"
            android:layout_marginStart="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_visual_train"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_visual_training_therapy"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_marginStart="9dp"
            app:layout_constraintTop_toTopOf="@+id/iv_visual_train"
            app:layout_constraintBottom_toBottomOf="@+id/iv_visual_train"
            app:layout_constraintLeft_toRightOf="@+id/iv_visual_train"/>

        <LinearLayout
            android:id="@+id/ll_flip_beat_state"
            android:layout_width="80dp"
            android:layout_height="28dp"
            android:orientation="horizontal"
            android:background="@drawable/selector_flip_beat_state_bg"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/icon_flip_beat"/>

            <TextView
                android:id="@+id/tv_flip_beat_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_unconnected"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:layout_marginStart="3dp"/>

        </LinearLayout>

        <com.mitdd.gazetracker.medicalhome.TimeProgress
            android:id="@+id/treatment_time_progress"
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:layout_marginTop="50dp"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fl_train_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/common_white_round_25_bg"
        android:layout_marginTop="97dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>