<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/white">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="翻转怕"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginStart="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/ll_state"
        app:layout_constraintVertical_chainStyle="packed"/>

    <LinearLayout
        android:id="@+id/ll_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="3dp"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@+id/tv_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_name"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_state"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:background="@drawable/selector_connect_flip_state_bg"/>

        <TextView
            android:id="@+id/tv_state_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="已连接"
            android:textColor="#7F7F95"
            android:textSize="13sp"
            android:layout_marginStart="7dp"
            android:includeFontPadding="false"/>

    </LinearLayout>

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#CED6E9"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>