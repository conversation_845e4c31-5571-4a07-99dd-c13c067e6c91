<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_eff3f6_round_20_bg">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_material_icon"
        android:layout_width="292dp"
        android:layout_height="0dp"
        app:shapeAppearance="@style/shape_image_round_20dp"
        app:strokeColor="@null"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="0dp"
        android:layout_height="40dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_material_icon"
        app:layout_constraintRight_toRightOf="@+id/iv_material_icon"
        app:layout_constraintBottom_toBottomOf="@+id/iv_material_icon"
        android:background="@drawable/common_black_40_bottom_round_20_bg">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="设备使用教程"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:maxLines="1"
            android:ellipsize="end"
            android:maxWidth="180dp"
            android:includeFontPadding="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginStart="20dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>