<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="250dp"
    android:layout_height="180dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_mode_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:src="@drawable/icon_inspection_center"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/shape_image_round_20dp"
        app:strokeColor="@null"/>

    <TextView
        android:id="@+id/tv_mode_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_inspection_center"
        android:textColor="@color/white"
        android:textSize="21sp"
        android:textStyle="bold"
        android:layout_marginTop="50dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:layout_width="33dp"
        android:layout_height="3dp"
        android:background="@color/white"
        android:layout_marginTop="15dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mode_name"/>

</androidx.constraintlayout.widget.ConstraintLayout>