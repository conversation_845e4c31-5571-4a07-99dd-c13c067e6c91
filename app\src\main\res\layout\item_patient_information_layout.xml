<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="105dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/selector_patient_information_bg"
    android:id="@+id/cl_patient_info">

    <TextView
        android:id="@+id/tv_patient_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="70dp"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="张思思"
        android:textSize="16sp"
        android:textColor="@color/color_patient_name"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="15dp"
        android:duplicateParentState="true"
        app:layout_constraintBottom_toTopOf="@+id/tv_patient_id"
        app:layout_constraintVertical_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_patient_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="男"
        android:textSize="10sp"
        android:textColor="@color/color_patient_name"
        android:includeFontPadding="false"
        android:layout_marginStart="10dp"
        android:duplicateParentState="true"
        app:layout_constraintBottom_toBottomOf="@+id/tv_patient_name"
        app:layout_constraintLeft_toRightOf="@+id/tv_patient_name"/>

    <View
        android:id="@+id/view_line"
        android:layout_width="1px"
        android:layout_height="9dp"
        android:background="@drawable/selector_patient_info_gender_line_bg"
        android:duplicateParentState="true"
        android:layout_marginStart="5dp"
        app:layout_constraintTop_toTopOf="@+id/tv_patient_gender"
        app:layout_constraintBottom_toBottomOf="@+id/tv_patient_gender"
        app:layout_constraintLeft_toRightOf="@+id/tv_patient_gender"/>

    <TextView
        android:id="@+id/tv_patient_age"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="11岁"
        android:textSize="10sp"
        android:textColor="@color/color_patient_name"
        android:includeFontPadding="false"
        android:layout_marginStart="5dp"
        android:duplicateParentState="true"
        app:layout_constraintBottom_toBottomOf="@+id/tv_patient_name"
        app:layout_constraintLeft_toRightOf="@+id/view_line"/>

    <TextView
        android:id="@+id/tv_patient_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="编号：暂无编号"
        android:textSize="12sp"
        android:textColor="@color/color_patient_id"
        android:includeFontPadding="false"
        android:layout_marginStart="15dp"
        android:layout_marginTop="10dp"
        android:duplicateParentState="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_patient_name"
        app:layout_constraintBottom_toTopOf="@+id/tv_patient_phone"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/tv_patient_phone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="手机号：185****6215"
        android:textSize="12sp"
        android:textColor="@color/color_patient_id"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        app:layout_goneMarginTop="15dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="10dp"
        android:duplicateParentState="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/barrier"
        app:layout_constraintTop_toBottomOf="@+id/tv_patient_id"
        app:layout_constraintBottom_toTopOf="@+id/tv_patient_file_num"/>

    <TextView
        android:id="@+id/tv_patient_file_num"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="档案号：CMYD19010211"
        android:textSize="12sp"
        android:textColor="@color/color_patient_id"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="10dp"
        android:duplicateParentState="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/barrier"
        app:layout_constraintTop_toBottomOf="@+id/tv_patient_phone"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="tv_basic_information,tv_train_data"/>

    <TextView
        android:id="@+id/tv_basic_information"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:minWidth="56dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:background="@drawable/selector_patient_basic_information_bg"
        android:text="@string/str_basic_information"
        android:textSize="10sp"
        android:textColor="@color/color_patient_basic_information"
        android:gravity="center"
        android:duplicateParentState="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="15dp"/>

    <TextView
        android:id="@+id/tv_train_data"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:minWidth="56dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:background="@drawable/selector_patient_basic_information_bg"
        android:text="@string/str_train_data"
        android:textSize="10sp"
        android:textColor="@color/color_patient_basic_information"
        android:gravity="center"
        android:duplicateParentState="true"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_basic_information"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="15dp" />

</androidx.constraintlayout.widget.ConstraintLayout>