<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="150dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_installing"
        android:textColor="@color/color_333333"
        android:textSize="11sp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:padding="5dp"
        android:background="@drawable/common_eff3f6_round_8_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="惠普"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_function"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_install"
        android:textSize="11sp"
        android:textColor="@color/color_333333"
        android:background="@drawable/common_666666_stroke_8_bg"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginBottom="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        style="?android:attr/progressBarStyleHorizontal"
        android:max="100"
        tools:progress="50"
        android:progressDrawable="@drawable/install_printer_progress_drawable"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>