<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="155dp"
    android:layout_height="129dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/white">

    <ImageView
        android:id="@+id/iv_train"
        android:layout_width="155dp"
        android:layout_height="129dp"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_train_duration"
        android:layout_width="57dp"
        android:layout_height="18dp"
        android:src="@drawable/icon_train_duration"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="14dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_train_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="05:23"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:includeFontPadding="false"
        android:layout_marginEnd="7dp"
        app:layout_constraintTop_toTopOf="@+id/iv_train_duration"
        app:layout_constraintBottom_toBottomOf="@+id/iv_train_duration"
        app:layout_constraintRight_toRightOf="@+id/iv_train_duration"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        tools:text="饼干吃吃吃"
        android:textColor="@color/color_333333"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:gravity="center"
        android:layout_marginBottom="7dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>