<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:background="#EFF3F6">

    <View
        android:id="@+id/horizontal_line1"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#CFD7E9"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/horizontal_line2"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#CFD7E9"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:id="@+id/vertical_line1"
        android:layout_width="1px"
        android:layout_height="match_parent"
        android:background="#CFD7E9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/vertical_line2"
        app:layout_constraintHorizontal_chainStyle="spread_inside"/>

    <View
        android:id="@+id/vertical_line2"
        android:layout_width="1px"
        android:layout_height="match_parent"
        android:background="#CFD7E9"
        app:layout_constraintLeft_toRightOf="@+id/vertical_line1"
        app:layout_constraintRight_toLeftOf="@+id/vertical_line3"/>

    <View
        android:id="@+id/vertical_line3"
        android:layout_width="1px"
        android:layout_height="match_parent"
        android:background="#CFD7E9"
        app:layout_constraintLeft_toRightOf="@+id/vertical_line2"
        app:layout_constraintRight_toLeftOf="@+id/vertical_line4"/>

    <View
        android:id="@+id/vertical_line4"
        android:layout_width="1px"
        android:layout_height="match_parent"
        android:background="#CFD7E9"
        app:layout_constraintLeft_toRightOf="@+id/vertical_line3"
        app:layout_constraintRight_toLeftOf="@+id/vertical_line5"/>

    <View
        android:id="@+id/vertical_line5"
        android:layout_width="1px"
        android:layout_height="match_parent"
        android:background="#CFD7E9"
        app:layout_constraintLeft_toRightOf="@+id/vertical_line4"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_card_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_card_number"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/vertical_line1"
        app:layout_constraintRight_toRightOf="@+id/vertical_line2"/>

    <TextView
        android:id="@+id/tv_treatment_period"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_treatment_period"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/vertical_line2"
        app:layout_constraintRight_toRightOf="@+id/vertical_line3"/>

    <TextView
        android:id="@+id/tv_card_open_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_card_opening_date"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/vertical_line3"
        app:layout_constraintRight_toRightOf="@+id/vertical_line4"/>

    <TextView
        android:id="@+id/tv_expiration_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_expiration_date"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/vertical_line4"
        app:layout_constraintRight_toRightOf="@+id/vertical_line5"/>

</androidx.constraintlayout.widget.ConstraintLayout>