<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/white">

    <ImageView
        android:id="@+id/iv_train_category"
        android:layout_width="83dp"
        android:layout_height="85dp"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_train_duration"
        android:layout_width="57dp"
        android:layout_height="18dp"
        android:src="@drawable/icon_train_duration"
        app:layout_constraintLeft_toLeftOf="@+id/iv_train_category"
        app:layout_constraintRight_toRightOf="@+id/iv_train_category"
        app:layout_constraintBottom_toBottomOf="@+id/iv_train_category"/>

    <TextView
        android:id="@+id/tv_train_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="05:23"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:includeFontPadding="false"
        android:layout_marginEnd="7dp"
        app:layout_constraintTop_toTopOf="@+id/iv_train_duration"
        app:layout_constraintBottom_toBottomOf="@+id/iv_train_duration"
        app:layout_constraintRight_toRightOf="@+id/iv_train_duration"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        tools:text="刺激训练"
        android:textColor="@color/color_333333"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:gravity="center"
        android:layout_marginTop="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_train_category"/>

</androidx.constraintlayout.widget.ConstraintLayout>