<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_white_round_15_bg"
    android:padding="15dp"
    android:orientation="vertical"
    android:minWidth="185dp">

    <LinearLayout
        android:id="@+id/cl_flip_beat"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_flip_beat"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_flip_beat"
            android:layout_gravity="center_vertical" />

        <TextView
            android:id="@+id/tv_flip_beat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_bluetooth_flip_beat"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp" />

        <TextView
            android:id="@+id/tv_flip_beat_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_unconnected"
            android:textColor="@color/color_selector_flip_beat_state"
            android:textSize="12sp"
            android:layout_marginStart="15dp"
            android:layout_gravity="center_vertical|end"/>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_printer_configuration"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_treatment_management">

        <ImageView
            android:id="@+id/iv_printer_configuration"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_printer_configuration"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_printer_configuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_printer_configuration"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_printer_configuration"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_help_center"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_treatment_management">

        <ImageView
            android:id="@+id/iv_help_center"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_popup_help_center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_help_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_help_center"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_help_center"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_version"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_eye_movement">

        <ImageView
            android:id="@+id/iv_version"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_version"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            tools:text="版本号：1.0.0"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:gravity="center_vertical"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_version"/>

        <ImageView
            android:id="@+id/iv_version_red_dot"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/icon_red_dot"
            app:layout_constraintTop_toTopOf="@+id/tv_version"
            app:layout_constraintRight_toRightOf="@+id/tv_version"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>