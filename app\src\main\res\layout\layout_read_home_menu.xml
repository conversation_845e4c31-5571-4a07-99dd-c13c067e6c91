<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_white_round_15_bg"
    android:padding="15dp">

    <LinearLayout
        android:id="@+id/ll_treatment_management"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_treatment_management"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_treatment_management" />

        <TextView
            android:id="@+id/tv_treatment_management"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_treatment_management"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_ai_train_guide"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_treatment_management">

        <ImageView
            android:id="@+id/iv_ai_train_guide"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_ai_train_guide" />

        <TextView
            android:id="@+id/tv_ai_train_guide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_ai_training_guide"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_read_ability"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_ai_train_guide">

        <ImageView
            android:id="@+id/iv_read_ability"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_read_ability" />

        <TextView
            android:id="@+id/tv_read_ability"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_reading_ability_test"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_eye_movement"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_read_ability">

        <ImageView
            android:id="@+id/iv_eye_movement"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_eye_movement" />

        <TextView
            android:id="@+id/tv_eye_movement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_eye_movement_evaluate"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_version"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_eye_movement">

        <ImageView
            android:id="@+id/iv_version"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/icon_version" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="版本号：1.0.0"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>