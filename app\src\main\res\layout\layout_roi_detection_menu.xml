<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_white_round_15_bg"
    android:padding="15dp">

    <TextView
        android:id="@+id/tv_select_picture"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/str_select_picture"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_mark_roi"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/str_mark_roi"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_select_picture"/>

    <TextView
        android:id="@+id/tv_start_detection"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/str_start_detection"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mark_roi"/>

</androidx.constraintlayout.widget.ConstraintLayout>