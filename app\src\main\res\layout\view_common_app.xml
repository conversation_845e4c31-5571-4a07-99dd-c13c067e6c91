<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:background="@drawable/common_white_round_25_bg">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_common_application"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="25dp"
        android:clipChildren="false"
        android:clipToPadding="false"/>

    <ImageView
        android:id="@+id/iv_add_common_app"
        android:layout_width="220dp"
        android:layout_height="195dp"
        android:layout_marginTop="40dp"
        android:src="@drawable/icon_add_common_app_prompt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_add_common_app"
        app:layout_constraintVertical_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_add_common_app"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_add_frequently_used_apps"
        android:textSize="12sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_add_common_app"
        app:layout_constraintBottom_toBottomOf="parent"/>

</merge>