<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_empty_root">

    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_prompt"
        app:layout_constraintVertical_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:textSize="12sp"
        android:textColor="@color/color_333333"
        tools:text="暂无数据"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_empty"
        app:layout_constraintBottom_toTopOf="@+id/iv_refresh"/>

    <ImageView
        android:id="@+id/iv_refresh"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:src="@drawable/icon_home_refresh"
        android:scaleType="center"
        android:layout_marginTop="20dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_prompt"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>