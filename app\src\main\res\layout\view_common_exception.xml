<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_exception_root">

    <ImageView
        android:id="@+id/iv_exception"
        android:layout_width="250dp"
        android:layout_height="222dp"
        tools:src="@drawable/icon_launcher_network_anomaly"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_exception_info"
        app:layout_constraintVertical_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_exception_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="当前网络异常，检查网络后再试试吧~"
        android:textSize="20sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="28dp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_exception"
        app:layout_constraintBottom_toTopOf="@+id/tv_exception_prompt1"/>

    <TextView
        android:id="@+id/tv_exception_prompt1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="请联系服务热线：400-100-3999"
        android:textSize="15sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="18dp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_exception_info"
        app:layout_constraintBottom_toTopOf="@+id/tv_exception_prompt2"/>

    <TextView
        android:id="@+id/tv_exception_prompt2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="设备SN：400-100-3999"
        android:textSize="15sp"
        android:textColor="@color/color_333333"
        android:layout_marginTop="5dp"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_exception_prompt1"
        app:layout_constraintBottom_toTopOf="@+id/iv_refresh"/>

    <ImageView
        android:id="@+id/iv_refresh"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:src="@drawable/icon_home_refresh"
        android:scaleType="center"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_exception_prompt2"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>