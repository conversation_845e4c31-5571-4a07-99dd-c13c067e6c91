<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/posture_correction_root">

    <ImageView
        android:id="@+id/iv_reference_position"
        android:layout_width="240dp"
        android:layout_height="271dp"
        android:src="@drawable/icon_postural_correction_avatar_grey"
        android:layout_marginTop="100dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_real_location"
        android:layout_width="240dp"
        android:layout_height="271dp"
        tools:src="@drawable/icon_postural_correction_avatar_green"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_correction_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="位置校准反馈内容"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="80dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:id="@+id/ll_close_correction"
        android:layout_width="110dp"
        android:layout_height="40dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="25dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:background="@drawable/common_eb4e89_round_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/icon_posture_correction_close"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <TextView
            android:id="@+id/tv_close_correction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_close_prompt"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:includeFontPadding="false"
            android:layout_marginStart="4dp"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_calibration"
        android:layout_width="110dp"
        android:layout_height="40dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="25dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:background="@drawable/common_eb4e89_round_bg"
        app:layout_constraintRight_toLeftOf="@+id/ll_close_correction"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/icon_posture_correction_calibration"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_gaze_track_calibration"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:includeFontPadding="false"
            android:layout_marginStart="4dp"/>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>