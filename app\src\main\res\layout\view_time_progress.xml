<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="25dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_train_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="30min"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/progress_bar"
        app:layout_constraintBottom_toBottomOf="@+id/progress_bar"/>

    <TextView
        android:id="@+id/tv_planned_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="90min"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/progress_bar"
        app:layout_constraintBottom_toBottomOf="@+id/progress_bar"/>

    <SeekBar
        android:id="@+id/progress_bar"
        android:layout_width="0dp"
        android:layout_height="20dp"
        tools:progress="30"
        tools:max="90"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:splitTrack="false"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_goneMarginStart="20dp"
        app:layout_goneMarginEnd="20dp"
        android:progressDrawable="@drawable/seekbar_mask_therapy_duration_progress_drawable"
        android:thumb="@null"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_train_duration"
        app:layout_constraintRight_toLeftOf="@+id/tv_planned_duration"/>

    <ImageView
        android:id="@+id/iv_thumb"
        android:layout_width="25dp"
        android:layout_height="25dp"
        tools:src="@drawable/icon_seekbar_thumb_female"
        app:layout_constraintTop_toTopOf="@+id/progress_bar"
        app:layout_constraintBottom_toBottomOf="@+id/progress_bar"
        app:layout_constraintLeft_toLeftOf="@+id/progress_bar"/>

</androidx.constraintlayout.widget.ConstraintLayout>