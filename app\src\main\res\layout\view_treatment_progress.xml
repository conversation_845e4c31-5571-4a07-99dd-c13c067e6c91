<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="90dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/treatment_progress_unfold_bg"
    android:id="@+id/cl_root">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_duration_of_treatment_today"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:includeFontPadding="false"
        android:layout_marginTop="15dp"
        android:layout_marginStart="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_go_home"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:src="@drawable/icon_home_page"
        android:scaleType="center"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <com.mitdd.gazetracker.medicalhome.TimeProgress
        android:id="@+id/treatment_time_progress"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:layout_marginTop="50dp"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>