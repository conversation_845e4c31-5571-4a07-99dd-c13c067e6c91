<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_visual_container">

    <ImageView
        android:id="@+id/iv_visual_point"
        android:layout_width="180dp"
        android:layout_height="180dp"
        tools:src="@drawable/icon_visual_logo"
        android:scaleType="centerInside"
        android:visibility="invisible"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_visual_calibration_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="请注视星星，直到星星爆破"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginBottom="80dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>