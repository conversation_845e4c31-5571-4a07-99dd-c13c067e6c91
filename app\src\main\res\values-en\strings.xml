<resources>
    <string name="app_name">Visual Training</string>

    <!--  视线追踪  -->
    <string name="str_cancel_calibration">Cancel Calibration</string>
    <string name="str_recalibration">Recalibrate</string>
    <string name="str_gaze_track_calibration">Eye Movement Calibration</string>
    <string name="str_calibration_failure">Oops! Let\'s Try Again</string>
    <string name="str_calibration_success">Great Job! Setup Complete</string>
    <string name="str_off_calibration">End Calibration Session</string>
    <string name="str_close_prompt">Close Message</string>
    <string name="str_please_remain_position">Stay Still</string>
    <string name="str_no_face_detected_please_sit_properly">No Face Detected, Please Sit Properly In Front of Screen</string>
    <string name="str_please_come_closer">Move a Little Closer</string>
    <string name="str_please_stay_away">Move Back a Bit</string>
    <string name="str_please_turn_to_the_right">Move Right a Little</string>
    <string name="str_please_turn_to_the_left">Move Left a Little</string>
    <string name="str_please_go_down">Lower Your Head Slightly</string>
    <string name="str_please_go_up">Raise Your Head Slightly</string>
    <string name="str_please_do_not_tilt_your_head">Keep Your Head Straight</string>
    <string name="str_watch_icon_until_bursts">Watch the Target Until it bursts</string>
    <string name="str_camera_abnormal">Camera Initialization Error</string>
    <string name="str_invalid_parameters_required_calibration">Calibration Parameters Invalid - New Calibration Needed</string>
    <!--  视线追踪  -->

    <!--  遮盖疗法  -->
    <string name="str_digital_masking_therapy">Digital Masking Therapy</string>
    <string name="str_add_frequently_used_apps">Quick Access Applications</string>
    <string name="str_duration_of_treatment_today">Today\'s Progress </string>
    <string name="str_parameter_setting">Customize Your Parameters</string>
    <string name="str_mode_selection">Pick Your Training</string>
    <string name="str_treatment_of_amblyopia">Amblyopia Treatment</string>
    <string name="str_prevention_control_of_myopia">Myopia Prevention</string>
    <string name="str_shaded_area">Occlusion Area</string>
    <string name="str_cover_amplitude">Treatment Intensity Level</string>
    <string name="str_mild">Mild</string>
    <string name="str_moderate">Moderate</string>
    <string name="str_height">High</string>
    <string name="str_completely">Complete</string>
    <string name="str_cover_channel">Occlusion Channel</string>
    <string name="str_airdoc_masking_therapy_on">Airdoc™ Therapy Active</string>
    <string name="str_airdoc_masking_therapy_off">Airdoc™ Therapy Off</string>
    <string name="str_please_wear_blue_red_glasses_watch_content">Please Wear Left Blue/Right Red Glasses to View Content</string>
    <string name="str_please_wear_red_blue_glasses_watch_content">Please Wear Left Red/Right Blue Glasses to View Content</string>
    <string name="str_please_cancel_red_blue_glasses">Please Remove Red-Blue Glasses</string>
    <string name="str_mask_therapy_illegal_arguments">The parameters of masking therapy are abnormal</string>
    <string name="str_please_wear_red_blue_glasses">Please Wear Left Red/Right Blue Glasses</string>
    <string name="str_please_bind_user_then_start_masking_therapy">Please Register First Before Starting Occlusion Therapy</string>
    <string name="str_no_treatment_available_please_contact_doctor">Treatment Course Not Activated, Please Contact Doctor</string>
    <string name="str_treatment_has_been_completed_today">Today\'s Treatment Completed</string>
    <!--  遮盖疗法  -->

    <!--  视觉训练  -->
    <string name="str_visual_training_therapy">Visual Training Therapy</string>
    <string name="str_training_data_anomalies">Training Data Error</string>
    <string name="str_flip_beat_not_connected">Flip Board Not Connected</string>
    <string name="str_abnormal_training_configuration">Training Configuration Error</string>
    <string name="str_come_back_to_practice">Back to Training</string>
    <string name="str_please_wear_red_blue_glasses_correctly">Please Wear Red-Blue Glasses Correctly</string>
    <string name="str_please_wear_blue_red_glasses_correctly">Please Wear Blue-Red Glasses Correctly</string>
    <string name="str_too_far_from_the_screen">Too Far from Screen, Maintain 40–50cm Distance</string>
    <string name="str_too_close_to_the_screen">Too Close to Screen, Maintain 40–50cm Distance</string>
    <string name="str_please_face_the_screen">Please Face the Screen Directly</string>
    <string name="str_please_keep_correct_posture">Sit Up Straight</string>
    <string name="str_please_concentrate_on_your_training">Stay Focused on Your Exercise</string>
    <string name="str_training_time_m_s">Training Time: %1$d:%2$d</string>
    <string name="str_training_suggestion">Training Recommendations</string>
    <string name="str_doctor_recommend_everyday_train_time_do_not_exceed">According to Doctor\'s Recommendations\nDaily Training Should Not Exceed %1$d Minutes~</string>
    <string name="str_doctor_recommend_type_train_time_do_not_exceed">According to Doctor\'s Recommendations\nThis Type of Training Should Not Exceed %1$d Minutes~</string>
    <string name="str_doctor_recommend_everytime_train_time_do_not_exceed">According to Doctor\'s Recommendations\nEach Training Session Should Not Exceed %1$d Minutes~</string>
    <string name="str_available_device">Available Devices</string>
    <string name="str_no_bluetooth_devices_available">No Bluetooth Devices Available</string>
    <string name="str_bind_account_get_training_plan">Link Account for Personalized Training</string>
    <!--  视觉训练  -->

    <!--  疗程  -->
    <string name="str_treatment_management">Treatment Management</string>
    <string name="str_review_reminder">Follow-up Reminder</string>
    <string name="str_review_date">Follow-up Date: %1$s</string>
    <string name="str_hospital_name">Hospital Name: %1$s</string>
    <string name="str_doctor_recommend_review_date">Schedule Follow-up Visit in %1$d Days</string>
    <string name="str_reserved_phone_number_invalid">Phone Number Invalid: %1$s</string>
    <string name="str_end_number_incorrectly_entered">Verification Failed - Try Again</string>
    <string name="str_treatment_activation">Activate Your Treatment</string>
    <string name="str_please_enter_reserved_phone_number">Verify with Phone Number</string>
    <string name="str_card_number">Member ID</string>
    <string name="str_card_number_s">ID: %1$s</string>
    <string name="str_treatment_period">Treatment Period</string>
    <string name="str_treatment_period_s">Treatment Period: %1$s</string>
    <string name="str_card_opening_date">Card Issue Date</string>
    <string name="str_you_have_renewed_your_treatment">Ready to Activate Your Renewed Treatment?</string>
    <string name="str_immediate_activation">Activate Now</string>
    <string name="str_activate_use">Activate Usage</string>
    <string name="str_expiration_date">Expiration Date</string>
    <string name="str_expiration_date_s">Expiration Date: %1$s</string>
    <string name="str_activation_success">Activation Successful</string>
    <string name="str_your_treatment_activated_success">Treatment Course Successfully Activated, Expiration Date Updated</string>
    <string name="str_treatment_expiration">Treatment Expired</string>
    <string name="str_treatment_has_expired">Treatment Course Has Expired</string>
    <string name="str_please_to_hospital_review_and_renewal">Please Visit Hospital for Follow-up and Renewal</string>
    <string name="str_treatment_due_in_days">Treatment Expires in %1$d Days</string>
    <string name="str_expiration_reminder">Expiration Reminder</string>
    <string name="str_treatment_suspended">Treatment Suspended</string>
    <string name="str_treatment_suspended_need_activate_it">Current Treatment Suspended, Activate Now?</string>
    <string name="str_stop_billing_when_treatment_suspended">Billing Paused During Treatment Suspension</string>
    <string name="str_suspended">Suspended</string>
    <string name="str_not_activated">Not Activated</string>
    <string name="str_over">Ended</string>
    <!--  疗程  -->

    <!--  阅读能力评估  -->
    <string name="str_basic_info">Basic Information</string>
    <string name="str_start_evaluating">Start Assessment</string>
    <string name="str_reading_results_analysis">Reading Result Analysis</string>
    <string name="str_your_reading_speed_assessment_result_is">Your Reading Results:</string>
    <string name="str_not_qualified">Below Standard</string>
    <string name="str_qualified">Meets Standard</string>
    <string name="str_read_word_count">Words Read</string>
    <string name="str_read_word_count_int">%1$d Words</string>
    <string name="str_read_duration">Reading Duration</string>
    <string name="str_read_duration_float">%1$f Minutes</string>
    <string name="str_read_speed">Reading Speed</string>
    <string name="str_read_speed_int">%1$d Words/Minute</string>
    <string name="str_standard_requirements">Standard Requirements</string>
    <string name="str_standard_sources">Source: Ministry of Education "Compulsory Education Curriculum Standards (2022 Version)"</string>
    <string name="str_look_read_track">View Reading Track</string>
    <string name="str_read_speed_suggestions">Speed Tips</string>
    <string name="str_keep_posture_same_when_reading">Please Maintain Posture During Reading</string>
    <string name="str_finish_reading">Complete Reading</string>
    <string name="str_you_are_now">You are:</string>
    <string name="str_primary_school_student">Elementary School Student</string>
    <string name="str_junior_high_school_student">Middle School Student</string>
    <string name="str_high_school_student">High School Student</string>
    <string name="str_adult">Adult</string>
    <string name="str_your_grade_is">Current Grade:</string>
    <string name="str_first_grade">Grade 1</string>
    <string name="str_second_grade">Grade 2</string>
    <string name="str_three_grade">Grade 3</string>
    <string name="str_four_grade">Grade 4</string>
    <string name="str_five_grade">Grade 5</string>
    <string name="str_six_grade">Grade 6</string>
    <string name="str_please_perform_eye_track_calibration">Eye Tracking Calibration Required</string>
    <string name="str_start_calibration">Begin Calibration</string>
    <string name="str_skip_calibration">Already Calibrated, Skip</string>
    <string name="str_start_read">Start Reading</string>
    <string name="str_read_assessment_report">Reading assessment report</string>
    <!--  阅读能力评估  -->

    <!--  眼球运动评估  -->
    <string name="str_eye_movement_evaluate">Eye Movement Assessment</string>
    <string name="str_eye_movement_evaluate_system">Eye Movement Assessment System</string>
    <string name="str_gaze_stability">Gaze Stability</string>
    <string name="str_follow_ability">Tracking Ability</string>
    <string name="str_saccade_ability">Saccadic Ability</string>
    <string name="str_gaze_stability_evaluate">Gaze Stability Assessment</string>
    <string name="str_gaze_stability_evaluate_explain">Start the assessment by focusing on the center marker on the screen. Remain still, and the test will automatically complete after 10 seconds.</string>
    <string name="str_keep_posture_same_when_evaluating">Please maintain the same posture during the assessment.</string>
    <string name="str_gaze_stability_evaluate_result">Gaze Stability Assessment Results</string>
    <string name="str_stability">Stable</string>
    <string name="str_instability">Unstable</string>
    <string name="str_export_data">Export Data</string>
    <string name="str_save">Save</string>
    <string name="str_follow_ability_evaluate">Tracking Ability Assessment</string>
    <string name="str_follow_ability_evaluate_explain">During the assessment, follow the moving marker with your gaze to ensure it remains centered.</string>
    <string name="str_follow_ability_evaluate_result">Tracking Ability Assessment Results</string>
    <string name="str_saccade_ability_evaluate">Saccadic Ability Assessment</string>
    <string name="str_saccade_ability_evaluate_explain">During the assessment, a marker will flash at different positions on the screen. Focus on each marker as quickly as possible.</string>
    <string name="str_saccade_ability_evaluate_result">Saccadic Ability Assessment Results</string>
    <string name="str_saccade_point_number">Number of Saccades: %1$d</string>
    <string name="str_saccade_average_duration">Average Duration: %1$dms</string>
    <!--  眼球运动评估  -->

    <string name="str_male">Male</string>
    <string name="str_female">Female</string>
    <string name="str_well">OK</string>
    <string name="str_ok">Confirm</string>
    <string name="str_ok_int">Confirm (%1$d)</string>
    <string name="str_i_know">I Understand</string>
    <string name="str_cancel">Cancel</string>
    <string name="str_age_int">%1$dy/o</string>
    <string name="str_device_sn">Device: %1$s</string>
    <string name="str_device_sn_n">Device：\n%1$s</string>
    <string name="str_status">Status</string>
    <string name="str_unconnected">Not Connected</string>
    <string name="str_connected">Connected</string>
    <string name="str_connecting">Connecting</string>
    <string name="str_disconnecting">Disconnecting</string>
    <string name="str_network_anomaly_prompt">Current Network Error, Please Check Network and Try Again~</string>
    <string name="str_information_retrieval_failure">Version Unknown</string>
    <string name="str_device_status_abnormal">Device status abnormal</string>
    <string name="str_no_data_available">No Data Found</string>
    <string name="str_please_contact_service_hotline">Help: 400–100–3999</string>
    <string name="str_help">Support Center</string>
    <string name="str_tutorial">Quick Tips</string>
    <string name="str_get_started">Let\'s Begin</string>
    <string name="str_add">Add</string>
    <string name="str_version_number_s">Version %1$s</string>
    <string name="str_app_name_s">Program: %1$s</string>
    <string name="str_app_size_d">Size：%1$dM</string>
    <string name="str_discovering_new_versions">Update Available</string>
    <string name="str_details">Learn More</string>
    <string name="str_experience_now">Try Now</string>
    <string name="str_ai_training_guide">AI Training Guidance</string>
    <string name="str_reading_ability_test">Reading Check</string>
    <string name="str_state_service">Service Status</string>
    <string name="str_display_viewpoint">Eye Tracking Display</string>
    <string name="str_environment_configuration">Setup Guide</string>
    <string name="str_behavior_guidance">Tips &amp; Tricks</string>
    <string name="str_unknown">Unknown</string>
    <string name="str_unbound">Not Registered</string>
    <string name="str_unbound_account_bind_immediately">Register Account Now</string>
    <string name="str_get_download_resource_exception">Download Error</string>
    <string name="str_upgrade_process_may_take_a_few_minutes">Upgrade may take several minutes, please be patient. Do not power off before completion.</string>
    <string name="str_user_agreement">"User Agreement"</string>
    <string name="str_privacy_policy">"Privacy Policy"</string>
    <string name="str_read_and_agree_user_agreement_privacy_policy">I have read and agree to the<b><font color="#EB4E89">%1$s</font></b>and<b><font color="#EB4E89">%2$s</font></b></string>
    <string name="str_please_read_agree_agreement">Review Terms of Service</string>
    <string name="str_please_enter_account_number">Enter Username</string>
    <string name="str_please_enter_password">Enter Password</string>
    <string name="str_binding_success">Registration Complete</string>
    <string name="str_binding_failure">Sign Up Failed</string>
    <string name="str_binding_device">Connect Device</string>
    <string name="str_confirm_equipment_and_patient_information_correct">Verify Account Details</string>
    <string name="str_confirm_equipment_and_store_information_correct">Verify Store Details</string>
    <string name="str_settings">settings</string>

    <string name="str_visual_training_station">Visual training station</string>
    <string name="str_examination_training_integrated_version">Examination and training integrated version</string>
    <string name="str_help_center">Help Center</string>
    <string name="str_bluetooth_flip_beat">Bluetooth flip beat</string>
    <string name="str_home_training_edition">Home Training Edition</string>

    <string name="str_airdoc_myopia_control_on">Airdoc™ Therapy Active</string>
    <string name="str_airdoc_myopia_control_off">Airdoc™ Therapy Off</string>
    <string name="str_please_maintain_correct_posture_watch_content">Please Maintain Correct Sitting Posture to Watch Content</string>
    <string name="str_recommended_enable_myopia_control_mode_watching">Recommended Enable Myopia Control Mode Watching</string>
    <string name="str_url_empty">url is empty</string>
    <string name="str_airdoc_ai">Airdoc AI</string>
    <string name="str_intelligent_version">Intelligent Version</string>
    <string name="str_sku_configuration">SKU Configuration</string>

    <string name="str_multimodal_psychological_assessment">Multimodal Psychological\nAssessment</string>
    <string name="str_multimodal_psychological_assessment_intro">Through AI-driven mental health support, we make\npsychological well-being and emotional optimization\nservices accessible everywhere.</string>
    <string name="str_upgrading">Upgrading</string>
    <string name="str_update_successful">Update successful</string>
    <string name="str_update_failed">Update failed</string>
    <string name="str_back">Back</string>
    <string name="str_advanced_settings">Advanced Settings</string>
    <string name="str_patient_library">Patient library</string>
    <string name="str_new_patient">New patient</string>
    <string name="str_enter_name_or_phone">Enter name or phone to search</string>
    <string name="str_id_s">ID：%1$s</string>
    <string name="str_phone_s">Phone：%1$s</string>
    <string name="str_file_num_s">File No.：%1$s</string>
    <string name="str_basic_information">Information</string>
    <string name="str_train_data">Train Data</string>
    <string name="str_no_number">No number</string>
    <string name="str_no_phone_number_yet">No phone number yet</string>
    <string name="str_no_file_number_yet">No file number yet</string>
    <string name="str_current_patient_s">Current patient：%1$s</string>
    <string name="str_new_user">New user</string>
    <string name="str_name">Name</string>
    <string name="str_gender">Gender</string>
    <string name="str_age">Age</string>
    <string name="str_birthday">Birthday</string>
    <string name="str_phone">Phone</string>
    <string name="str_doctor">Doctor</string>
    <string name="str_invalid_name_input">Invalid name input</string>
    <string name="str_invalid_age_input">Invalid age input</string>
    <string name="str_invalid_phone_number_format">Invalid phone number format</string>
    <string name="str_email">Email</string>
    <string name="str_invalid_email_format">Invalid email format</string>
    <string name="str_red_lens">Red lens</string>
    <string name="str_dominant_eye">Dominant eye</string>
    <string name="str_red">red</string>
    <string name="str_please_select_correct_glasses_red_s">Please select the correct glasses，the dominant eye should wear the %1$s lens</string>
    <string name="str_patient_added_successfully">Patient added successfully</string>
    <string name="str_patient_added_failed">Patient added failed</string>
    <string name="str_patient_info_modified_successfully">Patient info modified successfully</string>
    <string name="str_patient_info_modified_failed">Patient info modified failed</string>
    <string name="str_patient_info_queried_successfully">Patient info queried successfully</string>
    <string name="str_patient_info_queried_failed">Patient info queried failed</string>
    <string name="str_please_select_patient">Please select a patient</string>
    <string name="str_inspection_center">Inspection Center</string>
    <string name="str_training_center">Training Center</string>
    <string name="str_login_information_abnormal">Login information abnormal</string>
    <string name="str_patient_information">Patient Info</string>
    <string name="str_inpatient_number">Inpatient No.</string>
    <string name="str_case_card_number">Case Card No.</string>
    <string name="str_patient_type">Patient Type</string>
    <string name="str_diagnosis_information">Diagnosis Info</string>
    <string name="str_positive">Positive</string>
    <string name="str_negative">Negative</string>
    <string name="str_roi_detection">ROI Detection</string>
    <string name="str_select_picture">Select Picture</string>
    <string name="str_mark_roi">Mark ROI</string>
    <string name="str_save_roi">Save ROI</string>
    <string name="str_clear_roi">Clear ROI</string>
    <string name="str_start_detection">Start Detection</string>
    <string name="str_roi_detection_result">ROI Detection Result</string>

    <string name="str_train_date">Training Date</string>
    <string name="str_train_duration">Training Duration</string>
    <string name="str_duration_int_m">%1$dMinute</string>
    <string name="str_printer_configuration">Printer configuration</string>
    <string name="str_select_printer_driver">Please select the printer driver</string>
    <string name="str_installing">Installing</string>
    <string name="str_installed">Installed</string>
    <string name="str_not_installed">Not installed</string>
    <string name="str_configuration">Configuration</string>
    <string name="str_install">Install</string>
    <string name="str_install_successful">Install successful</string>
    <string name="str_install_failed">Install failed</string>

    <string name="str_hp">HP</string>
    <string name="str_fuji_xerox">Fuji Xerox</string>
    <string name="str_epson">Epson</string>

</resources>