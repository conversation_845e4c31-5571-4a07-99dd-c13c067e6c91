<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .manager-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .applied-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .native-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .mode-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .service-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .flow-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 2; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
      .native-arrow { stroke: #9b59b6; stroke-width: 3; fill: none; marker-end: url(#purplearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
    
    <marker id="purplearrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#9b59b6" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">AppliedManager与GazeApplied架构详解</text>
  
  <!-- AppliedManager -->
  <rect x="50" y="70" width="400" height="280" class="manager-box" rx="8"/>
  <text x="250" y="95" text-anchor="middle" class="subtitle">AppliedManager (单例管理器)</text>
  <text x="70" y="115" class="description-text">视线追踪应用持有者 - 274行代码</text>
  
  <!-- AppliedManager功能 -->
  <rect x="70" y="130" width="360" height="200" class="flow-box" rx="5"/>
  <text x="250" y="150" text-anchor="middle" class="step-title">核心功能</text>
  
  <text x="80" y="170" class="step-text" font-weight="bold">1. 应用模式管理:</text>
  <text x="100" y="185" class="code-text">• startAppliedReading() - 启动阅读模式</text>
  <text x="100" y="200" class="code-text">• startAppliedCure() - 启动治疗模式</text>
  <text x="100" y="215" class="code-text">• startAppliedStare() - 启动注视模式</text>
  <text x="100" y="230" class="code-text">• startAppliedFollow() - 启动追随模式</text>
  <text x="100" y="245" class="code-text">• startAppliedGlance() - 启动扫视模式</text>
  
  <text x="80" y="265" class="step-text" font-weight="bold">2. 状态管理:</text>
  <text x="100" y="280" class="code-text">• appliedMode: AtomicReference&lt;AppliedMode&gt;</text>
  <text x="100" y="295" class="code-text">• isInitialized: AtomicBoolean</text>
  <text x="100" y="310" class="code-text">• 线程安全的状态控制</text>
  
  <!-- GazeApplied -->
  <rect x="500" y="70" width="400" height="280" class="applied-box" rx="8"/>
  <text x="700" y="95" text-anchor="middle" class="subtitle">GazeApplied (JNI桥接层)</text>
  <text x="520" y="115" class="description-text">视线追踪应用 - 193行代码</text>
  
  <!-- GazeApplied功能 -->
  <rect x="520" y="130" width="360" height="200" class="flow-box" rx="5"/>
  <text x="700" y="150" text-anchor="middle" class="step-title">核心功能</text>
  
  <text x="530" y="170" class="step-text" font-weight="bold">1. Native层交互:</text>
  <text x="550" y="185" class="code-text">• nativeCreateGazeApplied() - 创建Native对象</text>
  <text x="550" y="200" class="code-text">• nativeStartApplication() - 启动应用</text>
  <text x="550" y="215" class="code-text">• nativeStopApplication() - 停止应用</text>
  <text x="550" y="230" class="code-text">• nativeCollectGaze() - 收集视线数据</text>
  <text x="550" y="245" class="code-text">• nativeGetGazeTrajectory() - 获取轨迹</text>
  
  <text x="530" y="265" class="step-text" font-weight="bold">2. 参数设置:</text>
  <text x="550" y="280" class="code-text">• setBlurParams() - 虚化参数</text>
  <text x="550" y="295" class="code-text">• setStarePoint() - 注视点</text>
  <text x="550" y="310" class="code-text">• setGlancePoint() - 扫视点</text>
  
  <!-- Native层 -->
  <rect x="950" y="70" width="400" height="280" class="native-box" rx="8"/>
  <text x="1150" y="95" text-anchor="middle" class="subtitle">Native层 (C++实现)</text>
  <text x="970" y="115" class="description-text">GazeApplication - C++核心算法</text>
  
  <!-- Native层功能 -->
  <rect x="970" y="130" width="360" height="200" class="flow-box" rx="5"/>
  <text x="1150" y="150" text-anchor="middle" class="step-title">核心功能</text>
  
  <text x="980" y="170" class="step-text" font-weight="bold">1. 应用算法:</text>
  <text x="1000" y="185" class="code-text">• APP_READING - 阅读算法</text>
  <text x="1000" y="200" class="code-text">• APP_PQBLUR - 虚化治疗算法</text>
  <text x="1000" y="215" class="code-text">• APP_STARE - 注视检测算法</text>
  <text x="1000" y="230" class="code-text">• APP_FOLLOW - 追随检测算法</text>
  <text x="1000" y="245" class="code-text">• APP_GLANCE - 扫视检测算法</text>
  
  <text x="980" y="265" class="step-text" font-weight="bold">2. 数据处理:</text>
  <text x="1000" y="280" class="code-text">• collect_gaze() - 实时数据收集</text>
  <text x="1000" y="295" class="code-text">• get_record_data_func() - 轨迹数据</text>
  <text x="1000" y="310" class="code-text">• 算法计算和结果输出</text>
  
  <!-- 应用模式详解 -->
  <rect x="50" y="380" width="1500" height="200" class="mode-box" rx="8"/>
  <text x="800" y="405" text-anchor="middle" class="subtitle">五种应用模式详解</text>
  
  <!-- 模式1: 治疗 -->
  <rect x="80" y="430" width="280" height="130" class="manager-box" rx="5"/>
  <text x="220" y="450" text-anchor="middle" class="step-title">CURE (治疗模式)</text>
  <text x="90" y="470" class="step-text" font-weight="bold">用途: 弱视/近视治疗</text>
  <text x="90" y="485" class="code-text">• 屏幕虚化处理</text>
  <text x="90" y="500" class="code-text">• 高斯模糊算法</text>
  <text x="90" y="515" class="code-text">• 遮盖疗法</text>
  <text x="90" y="530" class="code-text">• 参数: 半径、模糊度、通道</text>
  <text x="90" y="545" class="code-text">• MaskManager配置管理</text>
  
  <!-- 模式2: 阅读 -->
  <rect x="380" y="430" width="280" height="130" class="applied-box" rx="5"/>
  <text x="520" y="450" text-anchor="middle" class="step-title">READING (阅读模式)</text>
  <text x="390" y="470" class="step-text" font-weight="bold">用途: 阅读能力评估</text>
  <text x="390" y="485" class="code-text">• 阅读轨迹记录</text>
  <text x="390" y="500" class="code-text">• 视线数据收集</text>
  <text x="390" y="515" class="code-text">• 阅读速度计算</text>
  <text x="390" y="530" class="code-text">• ReadActivity中使用</text>
  <text x="390" y="545" class="code-text">• 621字固定文本</text>
  
  <!-- 模式3: 注视 -->
  <rect x="680" y="430" width="280" height="130" class="native-box" rx="5"/>
  <text x="820" y="450" text-anchor="middle" class="step-title">STARE (注视模式)</text>
  <text x="690" y="470" class="step-text" font-weight="bold">用途: 注视稳定性检查</text>
  <text x="690" y="485" class="code-text">• 固定点注视检测</text>
  <text x="690" y="500" class="code-text">• 注视稳定性分析</text>
  <text x="690" y="515" class="code-text">• 目标点设置</text>
  <text x="690" y="530" class="code-text">• setStarePoint(x, y)</text>
  <text x="690" y="545" class="code-text">• 坐标范围: [0~1]</text>
  
  <!-- 模式4: 追随 -->
  <rect x="980" y="430" width="280" height="130" class="service-box" rx="5"/>
  <text x="1120" y="450" text-anchor="middle" class="step-title">FOLLOW (追随模式)</text>
  <text x="990" y="470" class="step-text" font-weight="bold">用途: 追随能力检查</text>
  <text x="990" y="485" class="code-text">• 动态目标追踪</text>
  <text x="990" y="500" class="code-text">• 追随能力评估</text>
  <text x="990" y="515" class="code-text">• 轨迹平滑度分析</text>
  <text x="990" y="530" class="code-text">• 眼动协调性检测</text>
  <text x="990" y="545" class="code-text">• 运动轨迹算法</text>
  
  <!-- 模式5: 扫视 -->
  <rect x="1280" y="430" width="280" height="130" class="mode-box" rx="5"/>
  <text x="1420" y="450" text-anchor="middle" class="step-title">GLANCE (扫视模式)</text>
  <text x="1290" y="470" class="step-text" font-weight="bold">用途: 扫视能力检查</text>
  <text x="1290" y="485" class="code-text">• 快速扫视检测</text>
  <text x="1290" y="500" class="code-text">• 扫视精度分析</text>
  <text x="1290" y="515" class="code-text">• 目标点完成回调</text>
  <text x="1290" y="530" class="code-text">• onSaccadePointComplete()</text>
  <text x="1290" y="545" class="code-text">• setGlancePoint(x, y)</text>
  
  <!-- 调用流程 -->
  <rect x="50" y="600" width="1500" height="250" class="flow-box" rx="8"/>
  <text x="800" y="625" text-anchor="middle" class="subtitle">调用流程详解</text>
  
  <!-- 流程步骤 -->
  <rect x="80" y="650" width="200" height="80" class="service-box" rx="5"/>
  <text x="180" y="670" text-anchor="middle" class="step-title">1. GazeTrackService</text>
  <text x="90" y="690" class="step-text">接收消息:</text>
  <text x="90" y="705" class="code-text">MSG_START_APPLIED_READING</text>
  <text x="90" y="720" class="code-text">调用AppliedManager</text>
  
  <rect x="300" y="650" width="200" height="80" class="manager-box" rx="5"/>
  <text x="400" y="670" text-anchor="middle" class="step-title">2. AppliedManager</text>
  <text x="310" y="690" class="step-text">模式管理:</text>
  <text x="310" y="705" class="code-text">startAppliedReading()</text>
  <text x="310" y="720" class="code-text">状态检查和初始化</text>
  
  <rect x="520" y="650" width="200" height="80" class="applied-box" rx="5"/>
  <text x="620" y="670" text-anchor="middle" class="step-title">3. GazeApplied</text>
  <text x="530" y="690" class="step-text">JNI调用:</text>
  <text x="530" y="705" class="code-text">startApplied(READING)</text>
  <text x="530" y="720" class="code-text">nativeStartApplication()</text>
  
  <rect x="740" y="650" width="200" height="80" class="native-box" rx="5"/>
  <text x="840" y="670" text-anchor="middle" class="step-title">4. Native层</text>
  <text x="750" y="690" class="step-text">算法执行:</text>
  <text x="750" y="705" class="code-text">start_application(mode)</text>
  <text x="750" y="720" class="code-text">启动对应算法</text>
  
  <rect x="960" y="650" width="200" height="80" class="service-box" rx="5"/>
  <text x="1060" y="670" text-anchor="middle" class="step-title">5. 数据收集</text>
  <text x="970" y="690" class="step-text">实时处理:</text>
  <text x="970" y="705" class="code-text">collectGaze()</text>
  <text x="970" y="720" class="code-text">nativeCollectGaze()</text>
  
  <rect x="1180" y="650" width="200" height="80" class="manager-box" rx="5"/>
  <text x="1280" y="670" text-anchor="middle" class="step-title">6. 结果返回</text>
  <text x="1190" y="690" class="step-text">轨迹数据:</text>
  <text x="1190" y="705" class="code-text">getGazeTrajectory()</text>
  <text x="1190" y="720" class="code-text">JSON格式返回</text>
  
  <!-- 流程箭头 -->
  <line x1="280" y1="690" x2="300" y2="690" class="arrow"/>
  <line x1="500" y1="690" x2="520" y2="690" class="arrow"/>
  <line x1="720" y1="690" x2="740" y2="690" class="native-arrow"/>
  <line x1="940" y1="690" x2="960" y2="690" class="return-arrow"/>
  <line x1="1160" y1="690" x2="1180" y2="690" class="return-arrow"/>
  
  <!-- 数据流向 -->
  <rect x="80" y="750" width="1360" height="80" class="native-box" rx="5"/>
  <text x="760" y="770" text-anchor="middle" class="step-title">数据流向</text>
  
  <text x="100" y="790" class="step-text" font-weight="bold">向下调用:</text>
  <text x="120" y="805" class="code-text">Activity → Service → AppliedManager → GazeApplied → Native层</text>
  
  <text x="100" y="825" class="step-text" font-weight="bold">向上返回:</text>
  <text x="120" y="840" class="code-text">Native层 → GazeApplied → AppliedManager → Service → Activity</text>
  
  <!-- 架构优势 -->
  <rect x="50" y="870" width="1500" height="280" class="flow-box" rx="8"/>
  <text x="800" y="895" text-anchor="middle" class="subtitle">架构设计优势</text>
  
  <!-- 优势1 -->
  <rect x="80" y="920" width="350" height="120" class="manager-box" rx="5"/>
  <text x="255" y="940" text-anchor="middle" class="step-title">AppliedManager优势</text>
  <text x="90" y="960" class="step-text">• 单例模式，全局统一管理</text>
  <text x="90" y="975" class="step-text">• 线程安全的状态控制</text>
  <text x="90" y="990" class="step-text">• 多种应用模式统一接口</text>
  <text x="90" y="1005" class="step-text">• 自动初始化和资源管理</text>
  <text x="90" y="1020" class="step-text">• 外部监听器模式</text>
  
  <!-- 优势2 -->
  <rect x="450" y="920" width="350" height="120" class="applied-box" rx="5"/>
  <text x="625" y="940" text-anchor="middle" class="step-title">GazeApplied优势</text>
  <text x="460" y="960" class="step-text">• JNI桥接层，封装Native调用</text>
  <text x="460" y="975" class="step-text">• 类型安全的参数传递</text>
  <text x="460" y="990" class="step-text">• 回调机制处理异步事件</text>
  <text x="460" y="1005" class="step-text">• 资源生命周期管理</text>
  <text x="460" y="1020" class="step-text">• 错误处理和异常保护</text>
  
  <!-- 优势3 -->
  <rect x="820" y="920" width="350" height="120" class="native-box" rx="5"/>
  <text x="995" y="940" text-anchor="middle" class="step-title">整体架构优势</text>
  <text x="830" y="960" class="step-text">• 分层清晰，职责明确</text>
  <text x="830" y="975" class="step-text">• 高性能Native算法</text>
  <text x="830" y="990" class="step-text">• 可扩展的模式设计</text>
  <text x="830" y="1005" class="step-text">• 统一的数据流向</text>
  <text x="830" y="1020" class="step-text">• 良好的错误处理机制</text>
  
  <!-- 应用场景 -->
  <rect x="1190" y="920" width="350" height="120" class="service-box" rx="5"/>
  <text x="1365" y="940" text-anchor="middle" class="step-title">应用场景</text>
  <text x="1200" y="960" class="step-text">• 阅读能力评估 (ReadActivity)</text>
  <text x="1200" y="975" class="step-text">• 眼动治疗 (弱视/近视)</text>
  <text x="1200" y="990" class="step-text">• 眼动能力检查 (注视/追随/扫视)</text>
  <text x="1200" y="1005" class="step-text">• ROI兴趣区域检测</text>
  <text x="1200" y="1020" class="step-text">• 医疗诊断和康复训练</text>
  
  <!-- 连接线 -->
  <line x1="450" y1="210" x2="500" y2="210" class="arrow"/>
  <line x1="900" y1="210" x2="950" y2="210" class="native-arrow"/>
  
  <!-- 标注 -->
  <text x="475" y="205" text-anchor="middle" class="description-text">持有</text>
  <text x="925" y="205" text-anchor="middle" class="description-text">JNI调用</text>
</svg>
