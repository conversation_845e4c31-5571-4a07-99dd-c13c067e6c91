<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .mode-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .feature-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; font-weight: bold; }
      
      .cure-mode { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .reading-mode { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .stare-mode { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .follow-mode { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .glance-mode { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      .none-mode { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 3; rx: 12; }
      
      .cure-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .reading-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .stare-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .follow-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .glance-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      .none-module { fill: #ecf0f1; stroke: #95a5a6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .special-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">眼动应用模式对比分析</text>
  
  <!-- CURE治疗模式 -->
  <rect x="50" y="70" width="280" height="200" class="cure-mode"/>
  <text x="190" y="95" text-anchor="middle" class="section-title">CURE 治疗模式</text>
  
  <rect x="70" y="110" width="240" height="150" class="cure-module"/>
  <text x="190" y="130" text-anchor="middle" class="mode-title">🏥 遮盖疗法专用模式</text>
  <text x="80" y="150" class="text">• <tspan class="feature-text">核心功能：</tspan>弱视/近视治疗</text>
  <text x="80" y="165" class="text">• <tspan class="feature-text">遮盖参数：</tspan>setBlurParams()设置</text>
  <text x="80" y="180" class="text">• <tspan class="feature-text">状态保存：</tspan>saveMaskTherapyState(true)</text>
  <text x="80" y="195" class="text">• <tspan class="feature-text">治疗计时：</tspan>startTreatmentTimer()</text>
  <text x="80" y="210" class="text">• <tspan class="feature-text">数据上报：</tspan>实时治疗数据上报</text>
  <text x="80" y="225" class="text">• <tspan class="feature-text">进度监控：</tspan>治疗进度和时长管理</text>
  <text x="80" y="240" class="text">• <tspan class="feature-text">GPU渲染：</tspan>实时遮盖效果渲染</text>

  <!-- READING阅读模式 -->
  <rect x="350" y="70" width="280" height="200" class="reading-mode"/>
  <text x="490" y="95" text-anchor="middle" class="section-title">READING 阅读模式</text>
  
  <rect x="370" y="110" width="240" height="150" class="reading-module"/>
  <text x="490" y="130" text-anchor="middle" class="mode-title">📖 阅读辅助模式</text>
  <text x="380" y="150" class="text">• <tspan class="feature-text">核心功能：</tspan>阅读行为分析</text>
  <text x="380" y="165" class="text">• <tspan class="feature-text">眼动追踪：</tspan>阅读轨迹记录</text>
  <text x="380" y="180" class="text">• <tspan class="feature-text">无遮盖：</tspan>不设置遮盖参数</text>
  <text x="380" y="195" class="text">• <tspan class="feature-text">数据分析：</tspan>阅读速度和理解度</text>
  <text x="380" y="210" class="text">• <tspan class="feature-text">轨迹优化：</tspan>阅读路径分析</text>
  <text x="380" y="225" class="text">• <tspan class="feature-text">简单启动：</tspan>无额外配置</text>
  <text x="380" y="240" class="text">• <tspan class="feature-text">状态反馈：</tspan>MSG_APPLIED_READING_STATE</text>

  <!-- STARE注视模式 -->
  <rect x="650" y="70" width="280" height="200" class="stare-mode"/>
  <text x="790" y="95" text-anchor="middle" class="section-title">STARE 注视模式</text>
  
  <rect x="670" y="110" width="240" height="150" class="stare-module"/>
  <text x="790" y="130" text-anchor="middle" class="mode-title">👁️ 注视能力检查</text>
  <text x="680" y="150" class="text">• <tspan class="feature-text">核心功能：</tspan>注视稳定性测试</text>
  <text x="680" y="165" class="text">• <tspan class="feature-text">目标点：</tspan>setStarePoint(x, y)</text>
  <text x="680" y="180" class="text">• <tspan class="feature-text">坐标范围：</tspan>x, y ∈ [0, 1]</text>
  <text x="680" y="195" class="text">• <tspan class="feature-text">精度测试：</tspan>注视点偏移分析</text>
  <text x="680" y="210" class="text">• <tspan class="feature-text">时长测试：</tspan>持续注视能力</text>
  <text x="680" y="225" class="text">• <tspan class="feature-text">医疗评估：</tspan>眼动能力检查</text>
  <text x="680" y="240" class="text">• <tspan class="feature-text">状态反馈：</tspan>MSG_APPLIED_STARE_STATE</text>

  <!-- FOLLOW追随模式 -->
  <rect x="950" y="70" width="280" height="200" class="follow-mode"/>
  <text x="1090" y="95" text-anchor="middle" class="section-title">FOLLOW 追随模式</text>
  
  <rect x="970" y="110" width="240" height="150" class="follow-module"/>
  <text x="1090" y="130" text-anchor="middle" class="mode-title">🎯 追随能力检查</text>
  <text x="980" y="150" class="text">• <tspan class="feature-text">核心功能：</tspan>眼动追随测试</text>
  <text x="980" y="165" class="text">• <tspan class="feature-text">动态目标：</tspan>移动视标追踪</text>
  <text x="980" y="180" class="text">• <tspan class="feature-text">轨迹分析：</tspan>追随精度评估</text>
  <text x="980" y="195" class="text">• <tspan class="feature-text">延迟测试：</tspan>反应时间分析</text>
  <text x="980" y="210" class="text">• <tspan class="feature-text">平滑度：</tspan>追随轨迹平滑性</text>
  <text x="980" y="225" class="text">• <tspan class="feature-text">医疗诊断：</tspan>眼动协调性</text>
  <text x="980" y="240" class="text">• <tspan class="feature-text">状态反馈：</tspan>MSG_APPLIED_FOLLOW_STATE</text>

  <!-- GLANCE扫视模式 -->
  <rect x="1250" y="70" width="280" height="200" class="glance-mode"/>
  <text x="1390" y="95" text-anchor="middle" class="section-title">GLANCE 扫视模式</text>
  
  <rect x="1270" y="110" width="240" height="150" class="glance-module"/>
  <text x="1390" y="130" text-anchor="middle" class="mode-title">⚡ 扫视能力检查</text>
  <text x="1280" y="150" class="text">• <tspan class="feature-text">核心功能：</tspan>快速扫视测试</text>
  <text x="1280" y="165" class="text">• <tspan class="feature-text">目标切换：</tspan>setGlancePoint(x, y)</text>
  <text x="1280" y="180" class="text">• <tspan class="feature-text">速度测试：</tspan>扫视反应时间</text>
  <text x="1280" y="195" class="text">• <tspan class="feature-text">精度测试：</tspan>目标定位准确性</text>
  <text x="1280" y="210" class="text">• <tspan class="feature-text">频率分析：</tspan>扫视频率统计</text>
  <text x="1280" y="225" class="text">• <tspan class="feature-text">医疗评估：</tspan>扫视功能检查</text>
  <text x="1280" y="240" class="text">• <tspan class="feature-text">状态反馈：</tspan>MSG_APPLIED_GLANCE_STATE</text>

  <!-- NONE空闲模式 -->
  <rect x="600" y="290" width="280" height="120" class="none-mode"/>
  <text x="740" y="315" text-anchor="middle" class="section-title">NONE 空闲模式</text>
  
  <rect x="620" y="330" width="240" height="70" class="none-module"/>
  <text x="740" y="350" text-anchor="middle" class="mode-title">⏸️ 无应用模式</text>
  <text x="630" y="370" class="text">• <tspan class="feature-text">核心功能：</tspan>系统空闲状态</text>
  <text x="630" y="385" class="text">• <tspan class="feature-text">资源释放：</tspan>停止所有眼动应用</text>

  <!-- 详细对比分析 -->
  <rect x="50" y="430" width="1480" height="920" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="790" y="455" text-anchor="middle" class="title" style="font-size: 22px;">治疗模式与其他模式详细对比</text>

  <!-- 启动流程对比 -->
  <text x="70" y="490" class="mode-title">🚀 启动流程对比</text>
  
  <text x="90" y="515" class="flow-text" style="font-weight: bold;">CURE治疗模式启动流程：</text>
  <text x="110" y="535" class="code-text">fun startAppliedCure(): Int {</text>
  <text x="130" y="550" class="code-text">if (appliedMode.get() == CURE) return 2  // 已启动检查</text>
  <text x="130" y="565" class="code-text">init()  // 初始化</text>
  <text x="130" y="580" class="code-text">if (gazeApplied.startApplied(CURE)) {</text>
  <text x="150" y="595" class="code-text">setBlurParams(...)  // 🔥 独有：设置遮盖参数</text>
  <text x="150" y="610" class="code-text">DeviceManager.saveMaskTherapyState(true)  // 🔥 独有：保存状态</text>
  <text x="150" y="625" class="code-text">return 1</text>
  <text x="130" y="640" class="code-text">}</text>
  <text x="130" y="655" class="code-text">return 0</text>
  <text x="110" y="670" class="code-text">}</text>
  
  <text x="90" y="695" class="flow-text" style="font-weight: bold;">其他模式启动流程（以READING为例）：</text>
  <text x="110" y="715" class="code-text">fun startAppliedReading(): Int {</text>
  <text x="130" y="730" class="code-text">if (appliedMode.get() == READING) return 2</text>
  <text x="130" y="745" class="code-text">init()</text>
  <text x="130" y="760" class="code-text">return if (gazeApplied.startApplied(READING)) 1 else 0  // 简单启动</text>
  <text x="110" y="775" class="code-text">}</text>

  <!-- 功能特性对比 -->
  <text x="800" y="490" class="mode-title">⚙️ 功能特性对比</text>
  
  <text x="820" y="515" class="flow-text" style="font-weight: bold;">CURE治疗模式独有特性：</text>
  <text x="840" y="535" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">遮盖参数配置：</tspan>setBlurParams()设置4个关键参数</text>
  <text x="860" y="550" class="flow-text">- blurRadius: 遮盖区域直径(0.5~5.5mm)</text>
  <text x="860" y="565" class="flow-text">- blurSigma: 高斯模糊强度</text>
  <text x="860" y="580" class="flow-text">- blurMode: 遮盖模式(0-3)</text>
  <text x="860" y="595" class="flow-text">- channel: 遮盖通道(1-7)</text>
  
  <text x="840" y="615" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">状态持久化：</tspan>saveMaskTherapyState(true)</text>
  <text x="840" y="630" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">治疗计时器：</tspan>startTreatmentTimer()</text>
  <text x="840" y="645" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">实时数据上报：</tspan>治疗过程数据实时上传</text>
  <text x="840" y="660" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">进度监控：</tspan>治疗时长和完成度管理</text>
  <text x="840" y="675" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">GPU渲染：</tspan>实时遮盖效果渲染</text>
  
  <text x="820" y="700" class="flow-text" style="font-weight: bold;">其他模式共同特性：</text>
  <text x="840" y="720" class="flow-text">• 基础眼动追踪功能</text>
  <text x="840" y="735" class="flow-text">• 简单的启动/停止控制</text>
  <text x="840" y="750" class="flow-text">• 状态反馈消息</text>
  <text x="840" y="765" class="flow-text">• 眼动数据收集</text>

  <!-- 应用场景对比 -->
  <text x="70" y="800" class="mode-title">🎯 应用场景对比</text>
  
  <text x="90" y="825" class="flow-text" style="font-weight: bold;">CURE治疗模式应用场景：</text>
  <text x="110" y="845" class="flow-text">• <tspan style="color: #e74c3c;">医疗治疗：</tspan>弱视治疗、近视控制</text>
  <text x="110" y="860" class="flow-text">• <tspan style="color: #e74c3c;">长期疗程：</tspan>需要持续的治疗计划和进度管理</text>
  <text x="110" y="875" class="flow-text">• <tspan style="color: #e74c3c;">精确控制：</tspan>需要精确的遮盖参数和实时调整</text>
  <text x="110" y="890" class="flow-text">• <tspan style="color: #e74c3c;">数据追踪：</tspan>需要详细的治疗数据和效果分析</text>
  
  <text x="90" y="915" class="flow-text" style="font-weight: bold;">其他模式应用场景：</text>
  <text x="110" y="935" class="flow-text">• <tspan style="color: #3498db;">READING：</tspan>阅读行为分析、阅读能力评估</text>
  <text x="110" y="950" class="flow-text">• <tspan style="color: #f39c12;">STARE：</tspan>注视稳定性检查、眼动能力诊断</text>
  <text x="110" y="965" class="flow-text">• <tspan style="color: #27ae60;">FOLLOW：</tspan>追随能力测试、眼动协调性评估</text>
  <text x="110" y="980" class="flow-text">• <tspan style="color: #9b59b6;">GLANCE：</tspan>扫视功能检查、快速定位能力测试</text>

  <!-- 技术实现对比 -->
  <text x="800" y="800" class="mode-title">🔧 技术实现对比</text>
  
  <text x="820" y="825" class="flow-text" style="font-weight: bold;">CURE治疗模式技术复杂度：</text>
  <text x="840" y="845" class="flow-text">• <tspan style="color: #e74c3c;">高复杂度：</tspan>需要复杂的遮盖算法和参数管理</text>
  <text x="840" y="860" class="flow-text">• <tspan style="color: #e74c3c;">实时渲染：</tspan>GPU加速的实时遮盖效果</text>
  <text x="840" y="875" class="flow-text">• <tspan style="color: #e74c3c;">状态管理：</tspan>复杂的治疗状态和进度管理</text>
  <text x="840" y="890" class="flow-text">• <tspan style="color: #e74c3c;">数据处理：</tspan>实时数据上报和分析</text>
  
  <text x="820" y="915" class="flow-text" style="font-weight: bold;">其他模式技术复杂度：</text>
  <text x="840" y="935" class="flow-text">• <tspan style="color: #95a5a6;">中低复杂度：</tspan>主要专注于数据收集和分析</text>
  <text x="840" y="950" class="flow-text">• <tspan style="color: #95a5a6;">简单启动：</tspan>无需复杂的参数配置</text>
  <text x="840" y="965" class="flow-text">• <tspan style="color: #95a5a6;">基础功能：</tspan>标准的眼动追踪和数据记录</text>
  <text x="840" y="980" class="flow-text">• <tspan style="color: #95a5a6;">轻量级：</tspan>资源占用相对较少</text>

  <!-- Native层调用对比 -->
  <text x="70" y="1015" class="mode-title">🔗 Native层调用对比</text>
  
  <text x="90" y="1040" class="flow-text" style="font-weight: bold;">所有模式共同的Native调用：</text>
  <text x="110" y="1060" class="code-text">fun startApplied(appliedMode: AppliedMode): Boolean {</text>
  <text x="130" y="1075" class="code-text">return nativeStartApplication(nativeObj.get(), appliedMode.code)</text>
  <text x="110" y="1090" class="code-text">}</text>
  
  <text x="90" y="1115" class="flow-text" style="font-weight: bold;">CURE模式额外的Native调用：</text>
  <text x="110" y="1135" class="code-text">// 设置遮盖参数 - 只有CURE模式调用</text>
  <text x="110" y="1150" class="code-text">fun setBlurParams(blurRadius: Float, blurSigma: Float, blurMode: Int, channel: Int): Boolean {</text>
  <text x="130" y="1165" class="code-text">return nativeSetBlurParams(nativeObj.get(), blurRadius, blurSigma, blurMode, channel)</text>
  <text x="110" y="1180" class="code-text">}</text>
  
  <text x="90" y="1205" class="flow-text" style="font-weight: bold;">STARE/GLANCE模式额外的Native调用：</text>
  <text x="110" y="1225" class="code-text">// 设置目标点 - STARE和GLANCE模式调用</text>
  <text x="110" y="1240" class="code-text">nativeSetStarePoint(thiz: Long, x: Float, y: Float): Boolean</text>
  <text x="110" y="1255" class="code-text">nativeSetGlancePoint(thiz: Long, x: Float, y: Float): Boolean</text>

  <!-- 总结 -->
  <rect x="70" y="1280" width="1400" height="60" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1305" class="mode-title">🌟 核心区别总结</text>
  <text x="90" y="1325" class="flow-text"><tspan style="font-weight: bold; color: #e74c3c;">CURE治疗模式</tspan>是唯一具有<tspan style="font-weight: bold;">遮盖功能</tspan>的模式，需要复杂的参数配置、状态管理和实时渲染，专门用于医疗治疗；而其他模式主要用于<tspan style="font-weight: bold;">检查和分析</tspan>，功能相对简单，专注于数据收集和评估。</text>

</svg>
