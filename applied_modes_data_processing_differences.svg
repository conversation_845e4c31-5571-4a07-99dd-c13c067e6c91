<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .cure-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .reading-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .stare-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .follow-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .glance-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .data-flow { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">五种应用模式数据处理差异详解</text>
  
  <!-- 数据收集阶段对比 -->
  <rect x="50" y="70" width="1500" height="200" class="data-flow" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">collectGaze() 数据收集阶段差异</text>
  
  <!-- CURE模式 -->
  <rect x="80" y="120" width="280" height="130" class="cure-box" rx="5"/>
  <text x="220" y="140" text-anchor="middle" class="step-title">CURE (治疗模式)</text>
  <text x="90" y="160" class="step-text" font-weight="bold">数据处理方式:</text>
  <text x="90" y="175" class="code-text">pqblur_model.draw_gaze_result_func(x, y, dist)</text>
  <text x="90" y="190" class="step-text">• 实时屏幕虚化处理</text>
  <text x="90" y="205" class="step-text">• 不存储轨迹数据</text>
  <text x="90" y="220" class="step-text">• 直接应用视觉效果</text>
  <text x="90" y="235" class="step-text">• 无返回值</text>
  
  <!-- READING模式 -->
  <rect x="380" y="120" width="280" height="130" class="reading-box" rx="5"/>
  <text x="520" y="140" text-anchor="middle" class="step-title">READING (阅读模式)</text>
  <text x="390" y="160" class="step-text" font-weight="bold">数据处理方式:</text>
  <text x="390" y="175" class="code-text">reading_model.collect_data(x, y, duration)</text>
  <text x="390" y="190" class="step-text">• 存储原始轨迹点</text>
  <text x="390" y="205" class="step-text">• Point3f(x, y, duration)</text>
  <text x="390" y="220" class="step-text">• 简单数据收集</text>
  <text x="390" y="235" class="step-text">• 无返回值</text>
  
  <!-- STARE模式 -->
  <rect x="680" y="120" width="280" height="130" class="stare-box" rx="5"/>
  <text x="820" y="140" text-anchor="middle" class="step-title">STARE (注视模式)</text>
  <text x="690" y="160" class="step-text" font-weight="bold">数据处理方式:</text>
  <text x="690" y="175" class="code-text">stare_model.collect_data(x, y, duration)</text>
  <text x="690" y="190" class="step-text">• 存储注视轨迹点</text>
  <text x="690" y="205" class="step-text">• Point3f(x, y, duration)</text>
  <text x="690" y="220" class="step-text">• 与目标点比较分析</text>
  <text x="690" y="235" class="step-text">• 无返回值</text>
  
  <!-- FOLLOW模式 -->
  <rect x="980" y="120" width="280" height="130" class="follow-box" rx="5"/>
  <text x="1120" y="140" text-anchor="middle" class="step-title">FOLLOW (追随模式)</text>
  <text x="990" y="160" class="step-text" font-weight="bold">数据处理方式:</text>
  <text x="990" y="175" class="code-text">follow_model.collect_data(x, y, duration)</text>
  <text x="990" y="190" class="step-text">• 存储追随轨迹点</text>
  <text x="990" y="205" class="step-text">• Point3f(x, y, duration)</text>
  <text x="990" y="220" class="step-text">• 连续轨迹分析</text>
  <text x="990" y="235" class="step-text">• 无返回值</text>
  
  <!-- GLANCE模式 -->
  <rect x="1280" y="120" width="280" height="130" class="glance-box" rx="5"/>
  <text x="1420" y="140" text-anchor="middle" class="step-title">GLANCE (扫视模式)</text>
  <text x="1290" y="160" class="step-text" font-weight="bold">数据处理方式:</text>
  <text x="1290" y="175" class="code-text">finish_flag = glance_model.collect_data(x, y, duration)</text>
  <text x="1290" y="190" class="step-text">• 目标点距离检测</text>
  <text x="1290" y="205" class="step-text">• 命中判断逻辑</text>
  <text x="1290" y="220" class="step-text">• 时间戳记录</text>
  <text x="1290" y="235" class="step-text">• 返回完成状态</text>
  
  <!-- 数据返回阶段对比 -->
  <rect x="50" y="300" width="1500" height="280" class="data-flow" rx="8"/>
  <text x="800" y="325" text-anchor="middle" class="subtitle">getGazeTrajectory() 数据返回阶段差异</text>
  
  <!-- CURE模式返回 -->
  <rect x="80" y="350" width="280" height="210" class="cure-box" rx="5"/>
  <text x="220" y="370" text-anchor="middle" class="step-title">CURE 数据返回</text>
  <text x="90" y="390" class="step-text" font-weight="bold">返回特点:</text>
  <text x="90" y="405" class="code-text">• 无数据返回</text>
  <text x="90" y="420" class="step-text">• 治疗模式不需要轨迹</text>
  <text x="90" y="435" class="step-text">• 实时处理即时生效</text>
  
  <text x="90" y="455" class="step-text" font-weight="bold">应用场景:</text>
  <text x="90" y="470" class="step-text">• 弱视治疗</text>
  <text x="90" y="485" class="step-text">• 近视防控</text>
  <text x="90" y="500" class="step-text">• 实时视觉训练</text>
  
  <text x="90" y="520" class="step-text" font-weight="bold">技术特点:</text>
  <text x="90" y="535" class="step-text">• 高斯模糊算法</text>
  <text x="90" y="550" class="step-text">• 实时渲染处理</text>
  
  <!-- READING模式返回 -->
  <rect x="380" y="350" width="280" height="210" class="reading-box" rx="5"/>
  <text x="520" y="370" text-anchor="middle" class="step-title">READING 数据返回</text>
  <text x="390" y="390" class="step-text" font-weight="bold">返回格式:</text>
  <text x="390" y="405" class="code-text">{'gaze': [...]}</text>
  <text x="390" y="420" class="step-text">• 完整阅读轨迹JSON</text>
  <text x="390" y="435" class="step-text">• 时间序列数据</text>
  
  <text x="390" y="455" class="step-text" font-weight="bold">数据内容:</text>
  <text x="390" y="470" class="step-text">• x, y坐标 [0~1]</text>
  <text x="390" y="485" class="step-text">• duration持续时间</text>
  <text x="390" y="500" class="step-text">• 阅读路径分析</text>
  
  <text x="390" y="520" class="step-text" font-weight="bold">后处理:</text>
  <text x="390" y="535" class="step-text">• process_stare_points()</text>
  <text x="390" y="550" class="step-text">• 阅读模式优化</text>
  
  <!-- STARE模式返回 -->
  <rect x="680" y="350" width="280" height="210" class="stare-box" rx="5"/>
  <text x="820" y="370" text-anchor="middle" class="step-title">STARE 数据返回</text>
  <text x="690" y="390" class="step-text" font-weight="bold">返回格式:</text>
  <text x="690" y="405" class="code-text">{'gaze': [...]}</text>
  <text x="690" y="420" class="step-text">• 注视稳定性数据</text>
  <text x="690" y="435" class="step-text">• 目标点偏差分析</text>
  
  <text x="690" y="455" class="step-text" font-weight="bold">分析内容:</text>
  <text x="690" y="470" class="step-text">• 注视精度计算</text>
  <text x="690" y="485" class="step-text">• 稳定性评估</text>
  <text x="690" y="500" class="step-text">• 偏移量统计</text>
  
  <text x="690" y="520" class="step-text" font-weight="bold">后处理:</text>
  <text x="690" y="535" class="step-text">• postprocess_trajectory_data()</text>
  <text x="690" y="550" class="step-text">• 注视点聚类分析</text>
  
  <!-- FOLLOW模式返回 -->
  <rect x="980" y="350" width="280" height="210" class="follow-box" rx="5"/>
  <text x="1120" y="370" text-anchor="middle" class="step-title">FOLLOW 数据返回</text>
  <text x="990" y="390" class="step-text" font-weight="bold">返回格式:</text>
  <text x="990" y="405" class="code-text">{'gaze': [...]}</text>
  <text x="990" y="420" class="step-text">• 追随轨迹完整数据</text>
  <text x="990" y="435" class="step-text">• 路径匹配分析</text>
  
  <text x="990" y="455" class="step-text" font-weight="bold">分析内容:</text>
  <text x="990" y="470" class="step-text">• 追随精度</text>
  <text x="990" y="485" class="step-text">• 轨迹平滑度</text>
  <text x="990" y="500" class="step-text">• 反应时间</text>
  
  <text x="990" y="520" class="step-text" font-weight="bold">后处理:</text>
  <text x="990" y="535" class="step-text">• postprocess_trajectory_data()</text>
  <text x="990" y="550" class="step-text">• 追随能力评估</text>
  
  <!-- GLANCE模式返回 -->
  <rect x="1280" y="350" width="280" height="210" class="glance-box" rx="5"/>
  <text x="1420" y="370" text-anchor="middle" class="step-title">GLANCE 数据返回</text>
  <text x="1290" y="390" class="step-text" font-weight="bold">返回格式:</text>
  <text x="1290" y="405" class="code-text">{'gaze': [...]}</text>
  <text x="1290" y="420" class="step-text">• 扫视目标点数据</text>
  <text x="1290" y="435" class="step-text">• 命中时间记录</text>
  
  <text x="1290" y="455" class="step-text" font-weight="bold">特殊处理:</text>
  <text x="1290" y="470" class="step-text">• 距离阈值判断</text>
  <text x="1290" y="485" class="step-text">• 完成状态回调</text>
  <text x="1290" y="500" class="step-text">• 时间戳精确记录</text>
  
  <text x="1290" y="520" class="step-text" font-weight="bold">回调机制:</text>
  <text x="1290" y="535" class="step-text">• onSaccadePointComplete()</text>
  <text x="1290" y="550" class="step-text">• 实时完成通知</text>
  
  <!-- 核心差异对比 -->
  <rect x="50" y="610" width="1500" height="320" class="data-flow" rx="8"/>
  <text x="800" y="635" text-anchor="middle" class="subtitle">核心差异对比分析</text>
  
  <!-- 数据存储方式 -->
  <rect x="80" y="660" width="350" height="120" class="cure-box" rx="5"/>
  <text x="255" y="680" text-anchor="middle" class="step-title">数据存储方式差异</text>
  <text x="90" y="700" class="step-text">• CURE: 不存储，实时处理</text>
  <text x="90" y="715" class="step-text">• READING: vector&lt;Point3f&gt; 简单存储</text>
  <text x="90" y="730" class="step-text">• STARE: vector&lt;Point3f&gt; + 目标点比较</text>
  <text x="90" y="745" class="step-text">• FOLLOW: vector&lt;Point3f&gt; + 轨迹分析</text>
  <text x="90" y="760" class="step-text">• GLANCE: 选择性存储 + 距离判断</text>
  
  <!-- 处理算法差异 -->
  <rect x="450" y="660" width="350" height="120" class="reading-box" rx="5"/>
  <text x="625" y="680" text-anchor="middle" class="step-title">处理算法差异</text>
  <text x="460" y="700" class="step-text">• CURE: 高斯模糊 + 实时渲染</text>
  <text x="460" y="715" class="step-text">• READING: 阅读路径优化算法</text>
  <text x="460" y="730" class="step-text">• STARE: 注视点聚类 + 稳定性分析</text>
  <text x="460" y="745" class="step-text">• FOLLOW: 轨迹匹配 + 平滑度计算</text>
  <text x="460" y="760" class="step-text">• GLANCE: 目标检测 + 时间测量</text>
  
  <!-- 返回数据格式 -->
  <rect x="820" y="660" width="350" height="120" class="stare-box" rx="5"/>
  <text x="995" y="680" text-anchor="middle" class="step-title">返回数据格式差异</text>
  <text x="830" y="700" class="step-text">• CURE: 无返回数据</text>
  <text x="830" y="715" class="step-text">• READING: 阅读轨迹JSON</text>
  <text x="830" y="730" class="step-text">• STARE: 注视分析JSON</text>
  <text x="830" y="745" class="step-text">• FOLLOW: 追随评估JSON</text>
  <text x="830" y="760" class="step-text">• GLANCE: 扫视结果JSON</text>
  
  <!-- 应用场景差异 -->
  <rect x="1190" y="660" width="350" height="120" class="follow-box" rx="5"/>
  <text x="1365" y="680" text-anchor="middle" class="step-title">应用场景差异</text>
  <text x="1200" y="700" class="step-text">• CURE: 医疗治疗，实时干预</text>
  <text x="1200" y="715" class="step-text">• READING: 阅读能力评估</text>
  <text x="1200" y="730" class="step-text">• STARE: 注视稳定性检查</text>
  <text x="1200" y="745" class="step-text">• FOLLOW: 追随能力检查</text>
  <text x="1200" y="760" class="step-text">• GLANCE: 扫视能力检查</text>
  
  <!-- 技术实现细节 -->
  <rect x="80" y="800" width="1460" height="220" class="glance-box" rx="5"/>
  <text x="810" y="825" text-anchor="middle" class="step-title">技术实现细节对比</text>
  
  <!-- GLANCE特殊处理 -->
  <rect x="100" y="850" width="680" height="150" class="data-flow" rx="3"/>
  <text x="440" y="870" text-anchor="middle" class="step-text" font-weight="bold">GLANCE模式特殊处理逻辑</text>
  <text x="110" y="890" class="code-text">bool GazeGlance::collect_data(float x, float y, float duration) {</text>
  <text x="120" y="905" class="code-text">    float dist = sqrt(pow(target_point.x - x, 2) + pow(target_point.y - y, 2));</text>
  <text x="120" y="920" class="code-text">    if (dist &lt; app_hit_near_thres) {  // 距离阈值判断</text>
  <text x="130" y="935" class="code-text">        endTime = getCurrentTimeMillis();</text>
  <text x="130" y="950" class="code-text">        gaze_data_list.emplace_back(Point3f(target_point.x, target_point.y, endTime - startTime));</text>
  <text x="130" y="965" class="code-text">        return true;  // 返回完成状态</text>
  <text x="120" y="980" class="code-text">    }</text>
  <text x="120" y="995" class="code-text">    return false;</text>
  <text x="110" y="1010" class="code-text">}</text>
  
  <!-- 其他模式对比 -->
  <rect x="800" y="850" width="720" height="150" class="data-flow" rx="3"/>
  <text x="1160" y="870" text-anchor="middle" class="step-text" font-weight="bold">其他模式统一处理逻辑</text>
  <text x="810" y="890" class="code-text">void collect_data(float x, float y, float duration) {</text>
  <text x="820" y="905" class="code-text">    gaze_data_list.emplace_back(Point3f(x, y, duration));</text>
  <text x="810" y="920" class="code-text">    // 无返回值，简单存储</text>
  <text x="810" y="935" class="code-text">}</text>
  
  <text x="810" y="960" class="step-text" font-weight="bold">关键差异:</text>
  <text x="810" y="975" class="step-text">• GLANCE: 有条件存储 + 布尔返回值</text>
  <text x="810" y="990" class="step-text">• 其他模式: 无条件存储 + 无返回值</text>
  <text x="810" y="1005" class="step-text">• CURE: 不存储，直接处理</text>
  
  <!-- 数据流向箭头 -->
  <line x1="220" y1="250" x2="220" y2="350" class="data-arrow"/>
  <line x1="520" y1="250" x2="520" y2="350" class="data-arrow"/>
  <line x1="820" y1="250" x2="820" y2="350" class="data-arrow"/>
  <line x1="1120" y1="250" x2="1120" y2="350" class="data-arrow"/>
  <line x1="1420" y1="250" x2="1420" y2="350" class="data-arrow"/>
  
  <!-- 总结 -->
  <rect x="50" y="1040" width="1500" height="200" class="data-flow" rx="8"/>
  <text x="800" y="1065" text-anchor="middle" class="subtitle">数据处理差异总结</text>
  
  <text x="80" y="1090" class="step-text" font-weight="bold">1. 收集阶段差异:</text>
  <text x="100" y="1105" class="step-text">• CURE模式直接应用视觉效果，不存储数据</text>
  <text x="100" y="1120" class="step-text">• GLANCE模式有条件存储，带距离判断和完成状态返回</text>
  <text x="100" y="1135" class="step-text">• 其他模式(READING/STARE/FOLLOW)简单存储所有数据点</text>
  
  <text x="80" y="1160" class="step-text" font-weight="bold">2. 返回阶段差异:</text>
  <text x="100" y="1175" class="step-text">• CURE模式无数据返回，专注实时治疗效果</text>
  <text x="100" y="1190" class="step-text">• 其他模式都返回JSON格式轨迹数据，但后处理算法不同</text>
  <text x="100" y="1205" class="step-text">• GLANCE模式额外提供实时完成回调机制</text>
  
  <text x="80" y="1230" class="step-text" font-weight="bold">3. 应用目标差异:</text>
  <text x="100" y="1245" class="step-text">• 治疗类(CURE): 实时干预，改善视觉功能</text>
  <text x="100" y="1260" class="step-text">• 评估类(READING/STARE/FOLLOW/GLANCE): 数据收集，能力分析</text>
</svg>
