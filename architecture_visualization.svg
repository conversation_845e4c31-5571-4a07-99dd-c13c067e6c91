<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Define gradients for different modules -->
    <linearGradient id="homeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#44a08d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hospitalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8e6cf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#88d8a3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="readGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd93d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcd3c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="movementGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8c8ec;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7d8dc1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="coreGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Title -->
  <text x="800" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    MIT DD GazeTracker Architecture
  </text>

  <!-- Home Healthcare Module -->
  <g id="home-module">
    <rect x="20" y="70" width="360" height="380" rx="15" fill="url(#homeGrad)" stroke="#27ae60" stroke-width="2" />
    <text x="200" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Home Healthcare Module</text>
    <text x="200" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">HomeMainActivity, MaskTherapy, VisualTraining...</text>
  </g>

  <!-- Hospital Healthcare Module -->
  <g id="hospital-module">
    <rect x="420" y="70" width="360" height="380" rx="15" fill="url(#hospitalGrad)" stroke="#27ae60" stroke-width="2" />
    <text x="600" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Hospital Healthcare Module</text>
    <text x="600" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">HospitalMainActivity, InspectionCenter...</text>
  </g>

  <!-- Reading Assessment Module -->
  <g id="read-module">
    <rect x="820" y="70" width="360" height="380" rx="15" fill="url(#readGrad)" stroke="#f39c12" stroke-width="2" />
    <text x="1000" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Reading Assessment Module</text>
    <text x="1000" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">ReadHomeMainActivity...</text>
  </g>

  <!-- Eye Movement Evaluation Module -->
  <g id="movement-module">
    <rect x="1220" y="70" width="360" height="380" rx="15" fill="url(#movementGrad)" stroke="#3498db" stroke-width="2" />
    <text x="1400" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Eye Movement Evaluation Module</text>
    <text x="1400" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">EyeMovementEvaluateActivity...</text>
  </g>

  <!-- Core Services -->
  <g id="core-services">
    <rect x="20" y="480" width="1560" height="200" rx="15" fill="url(#coreGrad)" stroke="#8e44ad" stroke-width="2" />
    <text x="800" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">Core Technologies</text>
    <text x="800" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">GazeTrackService, CalibrationActivity...</text>
  </g>
</svg>
