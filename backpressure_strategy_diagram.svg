<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 12px; fill: #2c3e50; }
      .camera-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .queue-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .ai-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .ui-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .frame-new { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; }
      .frame-old { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; opacity: 0.6; }
      .frame-processing { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .drop-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead-red); }
      .success-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead-green); }
    </style>
    
    <!-- Arrow markers -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1200" height="800" fill="#ecf0f1"/>
  
  <!-- Title -->
  <text x="600" y="40" text-anchor="middle" class="title">背压策略：STRATEGY_KEEP_ONLY_LATEST 工作原理</text>
  
  <!-- Main Components -->
  <!-- Camera -->
  <rect x="50" y="100" width="120" height="80" rx="10" class="camera-box"/>
  <text x="110" y="130" text-anchor="middle" class="subtitle" fill="white">📷 相机</text>
  <text x="110" y="150" text-anchor="middle" class="text" fill="white">30fps</text>
  <text x="110" y="165" text-anchor="middle" class="small-text" fill="white">图像生产</text>
  
  <!-- Processing Queue -->
  <rect x="250" y="100" width="200" height="80" rx="10" class="queue-box"/>
  <text x="350" y="125" text-anchor="middle" class="subtitle" fill="white">📦 处理队列</text>
  <text x="350" y="145" text-anchor="middle" class="text" fill="white">KEEP_ONLY_LATEST</text>
  <text x="350" y="165" text-anchor="middle" class="small-text" fill="white">背压策略</text>
  
  <!-- AI Processing -->
  <rect x="520" y="100" width="150" height="80" rx="10" class="ai-box"/>
  <text x="595" y="125" text-anchor="middle" class="subtitle" fill="white">🤖 AI算法</text>
  <text x="595" y="145" text-anchor="middle" class="text" fill="white">眼动检测</text>
  <text x="595" y="165" text-anchor="middle" class="small-text" fill="white">~50ms处理</text>
  
  <!-- UI Display -->
  <rect x="750" y="100" width="120" height="80" rx="10" class="ui-box"/>
  <text x="810" y="130" text-anchor="middle" class="subtitle" fill="white">🖥️ UI界面</text>
  <text x="810" y="150" text-anchor="middle" class="text" fill="white">实时显示</text>
  <text x="810" y="165" text-anchor="middle" class="small-text" fill="white">眼动坐标</text>
  
  <!-- Frame Flow Demonstration -->
  <text x="50" y="250" class="subtitle">帧处理流程演示：</text>
  
  <!-- Time axis -->
  <line x1="50" y1="300" x2="900" y2="300" stroke="#bdc3c7" stroke-width="2"/>
  <text x="50" y="320" class="small-text">时间轴 →</text>
  
  <!-- Frame 1 (t=0ms) -->
  <circle cx="100" cy="280" r="15" class="frame-old"/>
  <text x="100" y="285" text-anchor="middle" class="small-text" fill="white">1</text>
  <text x="100" y="340" text-anchor="middle" class="small-text">帧1 (t=0ms)</text>
  
  <!-- Frame 2 (t=33ms) -->
  <circle cx="200" cy="280" r="15" class="frame-old"/>
  <text x="200" y="285" text-anchor="middle" class="small-text" fill="white">2</text>
  <text x="200" y="340" text-anchor="middle" class="small-text">帧2 (t=33ms)</text>
  
  <!-- Frame 3 (t=66ms) - Latest -->
  <circle cx="300" cy="280" r="15" class="frame-new"/>
  <text x="300" y="285" text-anchor="middle" class="small-text" fill="white">3</text>
  <text x="300" y="340" text-anchor="middle" class="small-text">帧3 (t=66ms)</text>
  <text x="300" y="355" text-anchor="middle" class="small-text" fill="#27ae60">✓ 最新帧</text>
  
  <!-- Drop arrows for old frames -->
  <path d="M 100 260 Q 150 220 200 260" class="drop-arrow"/>
  <text x="150" y="235" text-anchor="middle" class="small-text" fill="#e74c3c">丢弃旧帧</text>
  
  <!-- Processing arrow -->
  <path d="M 320 280 L 450 280" class="success-arrow"/>
  <text x="385" y="270" text-anchor="middle" class="small-text" fill="#27ae60">处理最新帧</text>
  
  <!-- Result -->
  <circle cx="500" cy="280" r="15" class="frame-processing"/>
  <text x="500" y="285" text-anchor="middle" class="small-text" fill="white">✓</text>
  <text x="500" y="340" text-anchor="middle" class="small-text">处理结果</text>
  
  <!-- Strategy Comparison -->
  <text x="50" y="420" class="subtitle">策略对比：</text>
  
  <!-- KEEP_ONLY_LATEST Strategy -->
  <rect x="50" y="450" width="500" height="120" rx="10" fill="#d5f4e6" stroke="#27ae60" stroke-width="2"/>
  <text x="70" y="475" class="subtitle" fill="#27ae60">✅ STRATEGY_KEEP_ONLY_LATEST</text>
  
  <text x="70" y="500" class="text">• 自动丢弃积压的旧帧</text>
  <text x="70" y="520" class="text">• 始终处理最新图像数据</text>
  <text x="70" y="540" class="text">• 内存使用低，实时性高</text>
  <text x="70" y="560" class="text">• 适合眼动追踪等实时应用</text>
  
  <!-- BLOCK_PRODUCER Strategy -->
  <rect x="600" y="450" width="500" height="120" rx="10" fill="#fadbd8" stroke="#e74c3c" stroke-width="2"/>
  <text x="620" y="475" class="subtitle" fill="#e74c3c">❌ STRATEGY_BLOCK_PRODUCER</text>
  
  <text x="620" y="500" class="text">• 所有帧都要处理，容易积压</text>
  <text x="620" y="520" class="text">• 内存使用高，延迟增加</text>
  <text x="620" y="540" class="text">• 可能导致内存溢出</text>
  <text x="620" y="560" class="text">• 适合批量处理场景</text>
  
  <!-- Code Example -->
  <text x="50" y="620" class="subtitle">代码实现：</text>
  
  <rect x="50" y="640" width="1100" height="120" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="70" y="665" class="code">val imageAnalysis = ImageAnalysis.Builder()</text>
  <text x="70" y="685" class="code">    .setResolutionSelector(resolutionStrategy)</text>
  <text x="70" y="705" class="code" fill="#e74c3c">    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)  // 关键配置</text>
  <text x="70" y="725" class="code">    .build().apply {</text>
  <text x="70" y="745" class="code">        setAnalyzer(ContextCompat.getMainExecutor(context)) { image -></text>
  <text x="90" y="765" class="code">            cameraListener?.onAnalyze(image)  // 处理最新帧</text>
  <text x="70" y="785" class="code">        }</text>
  <text x="70" y="805" class="code">    }</text>
  
</svg>
