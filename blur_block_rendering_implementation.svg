<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .param-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .calc-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .gpu-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .shader-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .render-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .param-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .calc-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .gpu-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .shader-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .render-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .param-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .calc-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .gpu-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .shader-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">遮盖疗法模糊块渲染实现详解</text>
  
  <!-- 第一层：参数设置 -->
  <rect x="50" y="70" width="320" height="120" class="param-layer"/>
  <text x="210" y="95" text-anchor="middle" class="section-title">参数设置</text>
  
  <rect x="70" y="110" width="280" height="70" class="param-module"/>
  <text x="210" y="130" text-anchor="middle" class="method-title">虚化参数配置</text>
  <text x="80" y="150" class="text">• 半径、模式、通道、强度</text>
  <text x="80" y="165" class="text">• 高斯核参数设置</text>

  <!-- 第二层：位置计算 -->
  <rect x="390" y="70" width="320" height="120" class="calc-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">位置计算</text>
  
  <rect x="410" y="110" width="280" height="70" class="calc-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">动态半径计算</text>
  <text x="420" y="150" class="text">• 根据距离计算虚化面积</text>
  <text x="420" y="165" class="text">• 坐标转换和边界检查</text>

  <!-- 第三层：GPU引擎 -->
  <rect x="730" y="70" width="320" height="120" class="gpu-layer"/>
  <text x="890" y="95" text-anchor="middle" class="section-title">GPU引擎</text>
  
  <rect x="750" y="110" width="280" height="70" class="gpu-module"/>
  <text x="890" y="130" text-anchor="middle" class="method-title">IPQ_BLR引擎</text>
  <text x="760" y="150" class="text">• 链式参数设置</text>
  <text x="760" y="165" class="text">• Done()触发渲染</text>

  <!-- 第四层：着色器处理 -->
  <rect x="1070" y="70" width="320" height="120" class="shader-layer"/>
  <text x="1230" y="95" text-anchor="middle" class="section-title">着色器处理</text>
  
  <rect x="1090" y="110" width="280" height="70" class="shader-module"/>
  <text x="1230" y="130" text-anchor="middle" class="method-title">OpenGL着色器</text>
  <text x="1100" y="150" class="text">• 高斯模糊算法</text>
  <text x="1100" y="165" class="text">• 颜色通道处理</text>

  <!-- 第五层：最终渲染 -->
  <rect x="1410" y="70" width="320" height="120" class="render-layer"/>
  <text x="1570" y="95" text-anchor="middle" class="section-title">最终渲染</text>
  
  <rect x="1430" y="110" width="280" height="70" class="render-module"/>
  <text x="1570" y="130" text-anchor="middle" class="method-title">Surface显示</text>
  <text x="1440" y="150" class="text">• 帧缓冲合成</text>
  <text x="1440" y="165" class="text">• 实时显示效果</text>

  <!-- 连接箭头 -->
  <line x1="370" y1="130" x2="390" y2="130" class="param-arrow"/>
  <line x1="710" y1="130" x2="730" y2="130" class="calc-arrow"/>
  <line x1="1050" y1="130" x2="1070" y2="130" class="gpu-arrow"/>
  <line x1="1390" y1="130" x2="1410" y2="130" class="shader-arrow"/>

  <!-- 详细实现分析 -->
  <rect x="50" y="220" width="1700" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="245" text-anchor="middle" class="title" style="font-size: 22px;">模糊块渲染实现详细分析</text>

  <!-- 第一部分：虚化参数设置 -->
  <text x="70" y="280" class="layer-title">⚙️ 虚化参数设置</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 四种虚化模式</text>
  <text x="110" y="325" class="code-text">#define InnerBlur 0   // 内部高斯模糊</text>
  <text x="110" y="340" class="code-text">#define OuterBlur 1   // 外部高斯模糊</text>
  <text x="110" y="355" class="code-text">#define InnerBlack 2  // 内部区域置黑</text>
  <text x="110" y="370" class="code-text">#define OuterBlack 3  // 外部区域置黑</text>
  
  <text x="90" y="395" class="flow-text" style="font-weight: bold;">2. 高斯核参数</text>
  <text x="110" y="415" class="code-text">#define InnerGKSize 35  // 内部高斯核尺寸</text>
  <text x="110" y="430" class="code-text">#define OuterGKSize 15  // 外部高斯核尺寸</text>
  <text x="110" y="445" class="code-text">float default_inner_GKSigma = 10.0F;  // 内部模糊强度</text>
  <text x="110" y="460" class="code-text">float default_outer_GKSigma = 2.0F;   // 外部模糊强度</text>
  
  <text x="90" y="485" class="flow-text" style="font-weight: bold;">3. 颜色通道设置</text>
  <text x="110" y="505" class="code-text">// channel：0=None | 1=R | 2=G | 4=B</text>
  <text x="110" y="520" class="code-text">// 设置 RG时 nBlurChannel = R | G = 3</text>
  <text x="110" y="535" class="code-text">// 设置 RB时 nBlurChannel = R | B = 5</text>
  <text x="110" y="550" class="code-text">// 设置 RGB时 nBlurChannel = R | G | B = 7</text>
  <text x="110" y="565" class="code-text">int default_channel = 1;  // 默认红色通道</text>

  <!-- 第二部分：动态半径计算 -->
  <text x="900" y="280" class="layer-title">📐 动态半径计算</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 距离相关的半径计算</text>
  <text x="940" y="325" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="960" y="340" class="code-text">if(dist >= 35 && dist <= 65) {  // 有效距离范围</text>
  <text x="980" y="355" class="code-text">// 🔥 根据距离计算虚化面积</text>
  <text x="980" y="370" class="code-text">int circle_radius = (int)(dist / 11.33F / 19.5F * 1080.0F / 3.0F * macular_blur_radius);</text>
  <text x="980" y="385" class="code-text">pq_blr->SetRadius(circle_radius)->SetPos(screen_x, screen_y)->Done();</text>
  <text x="960" y="400" class="code-text">} else {</text>
  <text x="980" y="415" class="code-text">// 使用默认半径</text>
  <text x="980" y="430" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="960" y="445" class="code-text">}</text>
  <text x="940" y="460" class="code-text">}</text>
  
  <text x="920" y="485" class="flow-text" style="font-weight: bold;">2. 半径计算公式解析</text>
  <text x="940" y="505" class="flow-text">• <tspan style="color: #e74c3c;">dist：</tspan>人眼到屏幕的距离（35-65cm）</text>
  <text x="940" y="520" class="flow-text">• <tspan style="color: #e74c3c;">11.33F：</tspan>视角转换系数</text>
  <text x="940" y="535" class="flow-text">• <tspan style="color: #e74c3c;">19.5F：</tspan>屏幕物理参数</text>
  <text x="940" y="550" class="flow-text">• <tspan style="color: #e74c3c;">1080.0F / 3.0F：</tspan>屏幕分辨率相关</text>
  <text x="940" y="565" class="flow-text">• <tspan style="color: #e74c3c;">macular_blur_radius：</tspan>黄斑区半径（0.5-5.5mm）</text>

  <!-- 第三部分：IPQ_BLR引擎接口 -->
  <text x="70" y="600" class="layer-title">🎮 IPQ_BLR引擎接口</text>
  
  <text x="90" y="625" class="flow-text" style="font-weight: bold;">1. 链式参数设置</text>
  <text x="110" y="645" class="code-text">// 虚化引擎初始化</text>
  <text x="110" y="660" class="code-text">pq_blr = newPQ_BLR();  // 创建GPU虚化引擎</text>
  <text x="110" y="675" class="code-text">pq_blr->SetRadius(pq_radius)      // 设置虚化半径</text>
  <text x="130" y="690" class="code-text">->SetChannel(channel)       // 设置颜色通道</text>
  <text x="130" y="705" class="code-text">->SetPos(width/2, height/2) // 设置初始位置</text>
  <text x="130" y="720" class="code-text">->Done();                   // 🔥 触发GPU渲染</text>
  
  <text x="90" y="745" class="flow-text" style="font-weight: bold;">2. 模式特定设置</text>
  <text x="110" y="765" class="code-text">switch (mode) {</text>
  <text x="130" y="780" class="code-text">case InnerBlur:  // 内部高斯模糊</text>
  <text x="150" y="795" class="code-text">pq_blr->SetGaussKer(InnerGKSize, InnerGKSize, GKSigma)</text>
  <text x="170" y="810" class="code-text">->SetMode(mode)->Done();</text>
  <text x="130" y="825" class="code-text">case OuterBlur:  // 外部高斯模糊</text>
  <text x="150" y="840" class="code-text">pq_blr->SetGaussKer(OuterGKSize, OuterGKSize, default_outer_GKSigma)</text>
  <text x="170" y="855" class="code-text">->SetMode(mode)->Done();</text>
  <text x="130" y="870" class="code-text">default:  // 置黑模式</text>
  <text x="150" y="885" class="code-text">pq_blr->SetMode(mode)->Done();</text>
  <text x="110" y="900" class="code-text">}</text>
  
  <text x="90" y="925" class="flow-text" style="font-weight: bold;">3. 实时参数更新</text>
  <text x="110" y="945" class="code-text">// 每帧更新位置和半径</text>
  <text x="110" y="960" class="code-text">int screen_x = (int)(x * visual_image_width);</text>
  <text x="110" y="975" class="code-text">int screen_y = (int)(y * visual_image_height);</text>
  <text x="110" y="990" class="code-text">pq_blr->SetRadius(circle_radius)->SetPos(screen_x, screen_y)->Done();</text>

  <!-- 第四部分：GPU着色器处理 -->
  <text x="900" y="600" class="layer-title">🎨 GPU着色器处理</text>
  
  <text x="920" y="625" class="flow-text" style="font-weight: bold;">1. 高斯模糊算法</text>
  <text x="940" y="645" class="flow-text">• <tspan style="color: #f39c12;">高斯核生成：</tspan>根据sigma值生成高斯权重矩阵</text>
  <text x="940" y="660" class="flow-text">• <tspan style="color: #f39c12;">卷积计算：</tspan>对指定区域进行高斯卷积</text>
  <text x="940" y="675" class="flow-text">• <tspan style="color: #f39c12;">分离卷积：</tspan>水平和垂直方向分别处理，提高效率</text>
  <text x="940" y="690" class="flow-text">• <tspan style="color: #f39c12;">边界处理：</tspan>处理图像边界的像素采样</text>
  
  <text x="920" y="715" class="flow-text" style="font-weight: bold;">2. 颜色通道处理</text>
  <text x="940" y="735" class="flow-text">• <tspan style="color: #f39c12;">通道选择：</tspan>根据channel参数选择R、G、B通道</text>
  <text x="940" y="750" class="flow-text">• <tspan style="color: #f39c12;">通道混合：</tspan>支持多通道组合（如RG、RGB等）</text>
  <text x="940" y="765" class="flow-text">• <tspan style="color: #f39c12;">颜色保持：</tspan>未选中的通道保持原始颜色</text>
  <text x="940" y="780" class="flow-text">• <tspan style="color: #f39c12;">透明度处理：</tspan>支持Alpha通道的透明度混合</text>
  
  <text x="920" y="805" class="flow-text" style="font-weight: bold;">3. 区域模式处理</text>
  <text x="940" y="825" class="flow-text">• <tspan style="color: #f39c12;">InnerBlur：</tspan>对圆形区域内部进行高斯模糊</text>
  <text x="940" y="840" class="flow-text">• <tspan style="color: #f39c12;">OuterBlur：</tspan>对圆形区域外部进行高斯模糊</text>
  <text x="940" y="855" class="flow-text">• <tspan style="color: #f39c12;">InnerBlack：</tspan>将圆形区域内部置为黑色</text>
  <text x="940" y="870" class="flow-text">• <tspan style="color: #f39c12;">OuterBlack：</tspan>将圆形区域外部置为黑色</text>
  
  <text x="920" y="895" class="flow-text" style="font-weight: bold;">4. 实时渲染优化</text>
  <text x="940" y="915" class="flow-text">• <tspan style="color: #f39c12;">GPU并行：</tspan>利用GPU的并行计算能力</text>
  <text x="940" y="930" class="flow-text">• <tspan style="color: #f39c12;">纹理缓存：</tspan>使用纹理缓存减少内存访问</text>
  <text x="940" y="945" class="flow-text">• <tspan style="color: #f39c12;">着色器优化：</tspan>优化的GLSL着色器代码</text>
  <text x="940" y="960" class="flow-text">• <tspan style="color: #f39c12;">帧率控制：</tspan>保持30fps稳定渲染</text>

  <!-- 第五部分：渲染流程总结 -->
  <text x="70" y="1015" class="layer-title">🔄 渲染流程总结</text>
  
  <text x="90" y="1040" class="flow-text" style="font-weight: bold;">1. 参数准备阶段</text>
  <text x="110" y="1060" class="flow-text">• <tspan style="color: #9b59b6;">模式设置：</tspan>选择虚化模式（模糊/置黑，内部/外部）</text>
  <text x="110" y="1075" class="flow-text">• <tspan style="color: #9b59b6;">通道配置：</tspan>设置要处理的颜色通道</text>
  <text x="110" y="1090" class="flow-text">• <tspan style="color: #9b59b6;">强度调节：</tspan>配置高斯模糊的sigma值</text>
  <text x="110" y="1105" class="flow-text">• <tspan style="color: #9b59b6;">区域大小：</tspan>设置黄斑区域的半径范围</text>
  
  <text x="90" y="1130" class="flow-text" style="font-weight: bold;">2. 实时计算阶段</text>
  <text x="110" y="1150" class="flow-text">• <tspan style="color: #9b59b6;">坐标转换：</tspan>视点坐标[0,1] → 屏幕像素坐标</text>
  <text x="110" y="1165" class="flow-text">• <tspan style="color: #9b59b6;">距离计算：</tspan>根据人眼距离动态调整虚化半径</text>
  <text x="110" y="1180" class="flow-text">• <tspan style="color: #9b59b6;">边界检查：</tspan>确保虚化区域在有效范围内</text>
  <text x="110" y="1195" class="flow-text">• <tspan style="color: #9b59b6;">参数更新：</tspan>实时更新GPU引擎参数</text>
  
  <text x="90" y="1220" class="flow-text" style="font-weight: bold;">3. GPU渲染阶段</text>
  <text x="110" y="1240" class="flow-text">• <tspan style="color: #9b59b6;">着色器执行：</tspan>OpenGL着色器处理图像数据</text>
  <text x="110" y="1255" class="flow-text">• <tspan style="color: #9b59b6;">高斯卷积：</tspan>对指定区域进行高斯模糊计算</text>
  <text x="110" y="1270" class="flow-text">• <tspan style="color: #9b59b6;">颜色混合：</tspan>处理指定颜色通道的虚化效果</text>
  <text x="110" y="1285" class="flow-text">• <tspan style="color: #9b59b6;">帧缓冲输出：</tspan>将处理结果写入帧缓冲区</text>

  <!-- 第六部分：性能特点 -->
  <text x="900" y="1015" class="layer-title">⚡ 性能特点</text>
  
  <text x="920" y="1040" class="flow-text" style="font-weight: bold;">1. 高性能特性</text>
  <text x="940" y="1060" class="flow-text">• <tspan style="color: #9b59b6;">GPU加速：</tspan>利用GPU并行计算能力</text>
  <text x="940" y="1075" class="flow-text">• <tspan style="color: #9b59b6;">实时渲染：</tspan>30fps稳定帧率</text>
  <text x="940" y="1090" class="flow-text">• <tspan style="color: #9b59b6;">动态调整：</tspan>根据距离实时调整虚化参数</text>
  <text x="940" y="1105" class="flow-text">• <tspan style="color: #9b59b6;">链式操作：</tspan>减少GPU状态切换开销</text>
  
  <text x="920" y="1130" class="flow-text" style="font-weight: bold;">2. 医疗级精度</text>
  <text x="940" y="1150" class="flow-text">• <tspan style="color: #9b59b6;">精确定位：</tspan>像素级精度的虚化位置</text>
  <text x="940" y="1165" class="flow-text">• <tspan style="color: #9b59b6;">可调参数：</tspan>支持多种虚化模式和强度</text>
  <text x="940" y="1180" class="flow-text">• <tspan style="color: #9b59b6;">颜色选择：</tspan>支持特定颜色通道的处理</text>
  <text x="940" y="1195" class="flow-text">• <tspan style="color: #9b59b6;">区域控制：</tspan>精确控制虚化区域大小</text>
  
  <text x="920" y="1220" class="flow-text" style="font-weight: bold;">3. 用户体验</text>
  <text x="940" y="1240" class="flow-text">• <tspan style="color: #9b59b6;">实时跟随：</tspan>虚化效果实时跟随视点移动</text>
  <text x="940" y="1255" class="flow-text">• <tspan style="color: #9b59b6;">平滑过渡：</tspan>虚化区域平滑变化</text>
  <text x="940" y="1270" class="flow-text">• <tspan style="color: #9b59b6;">视觉舒适：</tspan>高斯模糊提供自然的视觉效果</text>
  <text x="940" y="1285" class="flow-text">• <tspan style="color: #9b59b6;">治疗效果：</tspan>确保遮盖疗法的医疗效果</text>

  <!-- 总结 -->
  <rect x="70" y="1320" width="1600" height="200" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1345" class="layer-title">🌟 模糊块渲染实现总结</text>
  
  <text x="90" y="1370" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">参数驱动：</tspan>通过模式、通道、强度、半径等参数精确控制虚化效果</text>
  <text x="90" y="1390" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">动态计算：</tspan>根据人眼距离和视点位置实时计算虚化区域</text>
  <text x="90" y="1410" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">GPU加速：</tspan>IPQ_BLR引擎使用OpenGL着色器进行高性能渲染</text>
  <text x="90" y="1430" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">多种模式：</tspan>支持内外部高斯模糊和置黑四种虚化模式</text>
  <text x="90" y="1450" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">实时响应：</tspan>30fps实时渲染，虚化效果跟随视点移动</text>
  <text x="90" y="1470" class="flow-text">• <tspan style="font-weight: bold; color: #8e44ad;">医疗应用：</tspan>专为弱视治疗设计的高精度遮盖疗法实现</text>
  <text x="90" y="1490" class="flow-text">• <tspan style="font-weight: bold; color: #2c3e50;">核心流程：</tspan>参数设置 → 位置计算 → GPU渲染 → 着色器处理 → Surface显示</text>

</svg>
