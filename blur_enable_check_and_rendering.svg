<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .enable-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .check-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .render-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .gpu-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .display-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .enable-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .check-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .render-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .gpu-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .display-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .check-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .render-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .gpu-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">虚化开关判断与渲染位置详细流程</text>
  
  <!-- 第一层：开关设置 -->
  <rect x="50" y="70" width="300" height="120" class="enable-layer"/>
  <text x="200" y="95" text-anchor="middle" class="section-title">1. 开关设置</text>
  
  <rect x="70" y="110" width="260" height="70" class="enable-module"/>
  <text x="200" y="130" text-anchor="middle" class="method-title">set_red_blur_enable(true)</text>
  <text x="80" y="150" class="text">• blur_enable_flag = true</text>
  <text x="80" y="165" class="text">• pq_blr = newPQ_BLR()</text>

  <!-- 第二层：状态检查 -->
  <rect x="380" y="70" width="300" height="120" class="check-layer"/>
  <text x="530" y="95" text-anchor="middle" class="section-title">2. 状态检查</text>
  
  <rect x="400" y="110" width="260" height="70" class="check-module"/>
  <text x="530" y="130" text-anchor="middle" class="method-title">draw_gaze_result_func</text>
  <text x="410" y="150" class="text">• if(blur_enable_flag && pq_blr != nullptr)</text>
  <text x="410" y="165" class="text">• 每帧检查虚化状态</text>

  <!-- 第三层：渲染处理 -->
  <rect x="710" y="70" width="300" height="120" class="render-layer"/>
  <text x="860" y="95" text-anchor="middle" class="section-title">3. 渲染处理</text>
  
  <rect x="730" y="110" width="260" height="70" class="render-module"/>
  <text x="860" y="130" text-anchor="middle" class="method-title">pq_blr->SetPos()->Done()</text>
  <text x="740" y="150" class="text">• 设置虚化位置和参数</text>
  <text x="740" y="165" class="text">• 触发GPU渲染</text>

  <!-- 第四层：GPU渲染 -->
  <rect x="1040" y="70" width="300" height="120" class="gpu-layer"/>
  <text x="1190" y="95" text-anchor="middle" class="section-title">4. GPU渲染</text>
  
  <rect x="1060" y="110" width="260" height="70" class="gpu-module"/>
  <text x="1190" y="130" text-anchor="middle" class="method-title">IPQ_BLR引擎</text>
  <text x="1070" y="150" class="text">• OpenGL着色器处理</text>
  <text x="1070" y="165" class="text">• 实时虚化效果</text>

  <!-- 第五层：屏幕显示 -->
  <rect x="1370" y="70" width="300" height="120" class="display-layer"/>
  <text x="1520" y="95" text-anchor="middle" class="section-title">5. 屏幕显示</text>
  
  <rect x="1390" y="110" width="260" height="70" class="display-module"/>
  <text x="1520" y="130" text-anchor="middle" class="method-title">ANativeWindow</text>
  <text x="1400" y="150" class="text">• 帧缓冲显示</text>
  <text x="1400" y="165" class="text">• 用户看到虚化效果</text>

  <!-- 连接箭头 -->
  <line x1="350" y1="130" x2="380" y2="130" class="check-arrow"/>
  <line x1="680" y1="130" x2="710" y2="130" class="render-arrow"/>
  <line x1="1010" y1="130" x2="1040" y2="130" class="gpu-arrow"/>
  <line x1="1340" y1="130" x2="1370" y2="130" class="arrow"/>

  <!-- 详细流程说明 -->
  <rect x="50" y="220" width="1620" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="860" y="245" text-anchor="middle" class="title" style="font-size: 22px;">虚化开关判断与渲染详细实现</text>

  <!-- 第一部分：开关设置详解 -->
  <text x="70" y="280" class="layer-title">🔧 开关设置详解</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 虚化开关启用</text>
  <text x="110" y="325" class="code-text">void PqBlur::set_red_blur_enable(bool valid) {</text>
  <text x="130" y="340" class="code-text">if (valid) {</text>
  <text x="150" y="355" class="code-text">if(!blur_enable_flag) {  // 防止重复开启</text>
  <text x="170" y="370" class="code-text">blur_enable_flag = true;  // 🔥 关键标志位</text>
  <text x="170" y="385" class="code-text">pq_blr = newPQ_BLR();     // 创建GPU虚化引擎</text>
  <text x="170" y="400" class="code-text">// 初始化虚化参数</text>
  <text x="170" y="415" class="code-text">pq_blr->SetRadius(pq_radius)</text>
  <text x="190" y="430" class="code-text">->SetChannel(channel)</text>
  <text x="190" y="445" class="code-text">->SetPos(width/2, height/2)</text>
  <text x="190" y="460" class="code-text">->Done();</text>
  <text x="150" y="475" class="code-text">}</text>
  <text x="130" y="490" class="code-text">}</text>
  <text x="110" y="505" class="code-text">}</text>
  
  <text x="90" y="530" class="flow-text" style="font-weight: bold;">2. 关键状态变量</text>
  <text x="110" y="550" class="flow-text">• <tspan style="color: #e74c3c;">blur_enable_flag：</tspan>虚化功能总开关，控制是否进行虚化处理</text>
  <text x="110" y="565" class="flow-text">• <tspan style="color: #e74c3c;">pq_blr：</tspan>IPQ_BLR虚化引擎指针，nullptr表示未初始化</text>
  <text x="110" y="580" class="flow-text">• <tspan style="color: #e74c3c;">初始化参数：</tspan>半径、通道、位置、模式等GPU渲染参数</text>

  <!-- 第二部分：状态检查机制 -->
  <text x="900" y="280" class="layer-title">✅ 状态检查机制</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 每帧状态检查</text>
  <text x="940" y="325" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="960" y="340" class="code-text">// 🔥 关键检查：双重验证</text>
  <text x="960" y="355" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="980" y="370" class="code-text">// 坐标范围检查</text>
  <text x="980" y="385" class="code-text">if(x > 0 && x < 1.0 && y > 0 && y < 1.0) {</text>
  <text x="1000" y="400" class="code-text">// 执行虚化渲染</text>
  <text x="1000" y="415" class="code-text">int screen_x = (int)(x * visual_image_width);</text>
  <text x="1000" y="430" class="code-text">int screen_y = (int)(y * visual_image_height);</text>
  <text x="1000" y="445" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="980" y="460" class="code-text">}</text>
  <text x="960" y="475" class="code-text">} else {</text>
  <text x="980" y="490" class="code-text">LOGE("blur_enable_flag is false, can not blur!");</text>
  <text x="960" y="505" class="code-text">}</text>
  <text x="940" y="520" class="code-text">}</text>
  
  <text x="920" y="545" class="flow-text" style="font-weight: bold;">2. 检查逻辑说明</text>
  <text x="940" y="565" class="flow-text">• <tspan style="color: #27ae60;">blur_enable_flag：</tspan>检查虚化功能是否已启用</text>
  <text x="940" y="580" class="flow-text">• <tspan style="color: #27ae60;">pq_blr != nullptr：</tspan>检查GPU引擎是否已创建</text>
  <text x="940" y="595" class="flow-text">• <tspan style="color: #27ae60;">坐标范围：</tspan>确保视点坐标在有效范围[0,1]内</text>
  <text x="940" y="610" class="flow-text">• <tspan style="color: #27ae60;">距离检查：</tspan>根据距离动态调整虚化半径</text>

  <!-- 第三部分：调用位置分析 -->
  <text x="70" y="645" class="layer-title">📍 调用位置分析</text>
  
  <text x="90" y="670" class="flow-text" style="font-weight: bold;">1. 调用链路</text>
  <text x="110" y="690" class="code-text">// GazeApplication.cpp - collect_gaze方法</text>
  <text x="110" y="705" class="code-text">bool GazeApplication::collect_gaze(bool valid, float x, float y, float dist, float duration) {</text>
  <text x="130" y="720" class="code-text">if(app_runing) {</text>
  <text x="150" y="735" class="code-text">switch (app_mode) {</text>
  <text x="170" y="750" class="code-text">case app_typename::APP_PQBLUR:</text>
  <text x="190" y="765" class="code-text">// 🔥 这里调用虚化处理</text>
  <text x="190" y="780" class="code-text">pqblur_model.draw_gaze_result_func(x, y, dist);</text>
  <text x="190" y="795" class="code-text">break;</text>
  <text x="150" y="810" class="code-text">}</text>
  <text x="130" y="825" class="code-text">}</text>
  <text x="110" y="840" class="code-text">}</text>
  
  <text x="90" y="865" class="flow-text" style="font-weight: bold;">2. 调用频率</text>
  <text x="110" y="885" class="flow-text">• <tspan style="color: #e74c3c;">30fps调用：</tspan>每秒30次调用draw_gaze_result_func</text>
  <text x="110" y="900" class="flow-text">• <tspan style="color: #e74c3c;">实时检查：</tspan>每帧都会检查blur_enable_flag状态</text>
  <text x="110" y="915" class="flow-text">• <tspan style="color: #e74c3c;">条件执行：</tspan>只有在CURE模式下才会调用虚化处理</text>
  <text x="110" y="930" class="flow-text">• <tspan style="color: #e74c3c;">高效过滤：</tspan>通过标志位快速跳过不需要的处理</text>

  <!-- 第四部分：GPU渲染实现 -->
  <text x="900" y="645" class="layer-title">🎮 GPU渲染实现</text>
  
  <text x="920" y="670" class="flow-text" style="font-weight: bold;">1. IPQ_BLR引擎接口</text>
  <text x="940" y="690" class="code-text">class IPQ_BLR {</text>
  <text x="960" y="705" class="code-text">virtual IPQ_BLR* SetPos(int x, int y) = 0;      // 设置虚化位置</text>
  <text x="960" y="720" class="code-text">virtual IPQ_BLR* SetRadius(int r) = 0;          // 设置虚化半径</text>
  <text x="960" y="735" class="code-text">virtual IPQ_BLR* SetGaussKer(int w, int h, float sigma) = 0;  // 高斯核</text>
  <text x="960" y="750" class="code-text">virtual IPQ_BLR* SetChannel(int ch) = 0;        // 设置颜色通道</text>
  <text x="960" y="765" class="code-text">virtual IPQ_BLR* SetMode(int m) = 0;            // 设置虚化模式</text>
  <text x="960" y="780" class="code-text">virtual void Done() = 0;                        // 🔥 执行渲染</text>
  <text x="940" y="795" class="code-text">};</text>
  
  <text x="920" y="820" class="flow-text" style="font-weight: bold;">2. 渲染触发机制</text>
  <text x="940" y="840" class="flow-text">• <tspan style="color: #f39c12;">链式调用：</tspan>SetPos()->SetRadius()->Done()链式设置参数</text>
  <text x="940" y="855" class="flow-text">• <tspan style="color: #f39c12;">Done()触发：</tspan>调用Done()方法触发实际的GPU渲染</text>
  <text x="940" y="870" class="flow-text">• <tspan style="color: #f39c12;">OpenGL实现：</tspan>底层使用OpenGL着色器进行虚化处理</text>
  <text x="940" y="885" class="flow-text">• <tspan style="color: #f39c12;">实时渲染：</tspan>每帧实时计算和渲染虚化效果</text>

  <!-- 第五部分：显示机制 -->
  <text x="70" y="950" class="layer-title">📺 显示机制</text>
  
  <text x="90" y="975" class="flow-text" style="font-weight: bold;">1. 帧缓冲显示</text>
  <text x="110" y="995" class="code-text">// GazeService.cpp - draw方法</text>
  <text x="110" y="1010" class="code-text">void GazeService::draw(const Mat& img) {</text>
  <text x="130" y="1025" class="code-text">// 设置ANativeWindow格式</text>
  <text x="130" y="1040" class="code-text">ANativeWindow_setBuffersGeometry(window, img.cols, img.rows, WINDOW_FORMAT_RGBA_8888);</text>
  <text x="130" y="1055" class="code-text">// 锁定缓冲区</text>
  <text x="130" y="1070" class="code-text">ANativeWindow_Buffer buffer;</text>
  <text x="130" y="1085" class="code-text">ANativeWindow_lock(window, &buffer, nullptr);</text>
  <text x="130" y="1100" class="code-text">// 拷贝渲染结果到缓冲区</text>
  <text x="130" y="1115" class="code-text">memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize);</text>
  <text x="110" y="1130" class="code-text">}</text>
  
  <text x="90" y="1155" class="flow-text" style="font-weight: bold;">2. 显示流程</text>
  <text x="110" y="1175" class="flow-text">• <tspan style="color: #9b59b6;">GPU渲染：</tspan>IPQ_BLR在GPU中完成虚化处理</text>
  <text x="110" y="1190" class="flow-text">• <tspan style="color: #9b59b6;">帧缓冲：</tspan>渲染结果写入ANativeWindow缓冲区</text>
  <text x="110" y="1205" class="flow-text">• <tspan style="color: #9b59b6;">屏幕显示：</tspan>系统将缓冲区内容显示到屏幕</text>
  <text x="110" y="1220" class="flow-text">• <tspan style="color: #9b59b6;">用户感知：</tspan>用户看到实时跟随视点的虚化效果</text>

  <!-- 第六部分：性能优化 -->
  <text x="900" y="950" class="layer-title">⚡ 性能优化</text>
  
  <text x="920" y="975" class="flow-text" style="font-weight: bold;">1. 状态检查优化</text>
  <text x="940" y="995" class="flow-text">• <tspan style="color: #e74c3c;">快速跳过：</tspan>blur_enable_flag为false时立即返回</text>
  <text x="940" y="1010" class="flow-text">• <tspan style="color: #e74c3c;">空指针检查：</tspan>pq_blr为nullptr时避免崩溃</text>
  <text x="940" y="1025" class="flow-text">• <tspan style="color: #e74c3c;">坐标验证：</tspan>无效坐标时跳过渲染</tspan>
  <text x="940" y="1040" class="flow-text">• <tspan style="color: #e74c3c;">重复开启检查：</tspan>防止重复创建GPU资源</text>
  
  <text x="920" y="1065" class="flow-text" style="font-weight: bold;">2. GPU资源管理</text>
  <text x="940" y="1085" class="flow-text">• <tspan style="color: #e74c3c;">延迟创建：</tspan>只在需要时创建IPQ_BLR引擎</text>
  <text x="940" y="1100" class="flow-text">• <tspan style="color: #e74c3c;">及时释放：</tspan>关闭虚化时立即释放GPU资源</text>
  <text x="940" y="1115" class="flow-text">• <tspan style="color: #e74c3c;">链式调用：</tspan>减少GPU状态切换次数</text>
  <text x="940" y="1130" class="flow-text">• <tspan style="color: #e74c3c;">批量处理：</tspan>Done()方法批量提交GPU命令</text>

  <!-- 第七部分：错误处理 -->
  <text x="70" y="1265" class="layer-title">🛡️ 错误处理</text>
  
  <text x="90" y="1290" class="flow-text" style="font-weight: bold;">1. 状态异常处理</text>
  <text x="110" y="1310" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="130" y="1325" class="code-text">// 正常处理虚化</text>
  <text x="110" y="1340" class="code-text">} else {</text>
  <text x="130" y="1355" class="code-text">LOGE("blur_enable_flag is false, can not blur region!!!!");</text>
  <text x="110" y="1370" class="code-text">}</text>
  
  <text x="90" y="1395" class="flow-text" style="font-weight: bold;">2. 资源清理</text>
  <text x="110" y="1415" class="code-text">PqBlur::~PqBlur() {</text>
  <text x="130" y="1430" class="code-text">blur_enable_flag = false;</text>
  <text x="130" y="1445" class="code-text">if (pq_blr != nullptr) {</text>
  <text x="150" y="1460" class="code-text">deletePQ_BLR(pq_blr);  // 释放GPU资源</text>
  <text x="150" y="1475" class="code-text">pq_blr = nullptr;</text>
  <text x="130" y="1490" class="code-text">}</text>
  <text x="110" y="1505" class="code-text">}</text>

  <!-- 总结 -->
  <rect x="900" y="1265" width="720" height="260" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="920" y="1290" class="layer-title">🌟 虚化开关判断与渲染总结</text>
  
  <text x="920" y="1315" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">开关设置：</tspan>set_red_blur_enable(true)设置blur_enable_flag并创建GPU引擎</text>
  <text x="920" y="1335" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">状态检查：</tspan>draw_gaze_result_func每帧检查blur_enable_flag && pq_blr != nullptr</text>
  <text x="920" y="1355" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">渲染触发：</tspan>pq_blr->SetPos()->Done()触发GPU实时渲染</text>
  <text x="920" y="1375" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">GPU处理：</tspan>IPQ_BLR引擎使用OpenGL着色器进行虚化处理</text>
  <text x="920" y="1395" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">屏幕显示：</tspan>通过ANativeWindow将渲染结果显示到屏幕</text>
  <text x="920" y="1415" class="flow-text">• <tspan style="font-weight: bold; color: #16a085;">调用频率：</tspan>30fps高频调用，通过标志位快速过滤，确保性能</text>
  <text x="920" y="1435" class="flow-text">• <tspan style="font-weight: bold; color: #8e44ad;">错误处理：</tspan>完善的状态检查和资源管理，确保系统稳定</text>
  <text x="920" y="1455" class="flow-text">• <tspan style="font-weight: bold; color: #d35400;">关键位置：</tspan>GazeApplication::collect_gaze → PqBlur::draw_gaze_result_func</text>
  <text x="920" y="1475" class="flow-text">• <tspan style="font-weight: bold; color: #2c3e50;">核心机制：</tspan>双重检查(标志位+指针) + GPU渲染 + 实时显示</text>

</svg>
