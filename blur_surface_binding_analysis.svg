<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .android-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .service-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .native-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .surface-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .blur-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .android-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .service-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .native-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .surface-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .blur-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .surface-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .blur-arrow { stroke: #9b59b6; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .binding-arrow { stroke: #e74c3c; stroke-width: 4; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">遮盖疗法绑定的Surface详细分析</text>
  
  <!-- 第一层：Android UI层 -->
  <rect x="50" y="70" width="320" height="120" class="android-layer"/>
  <text x="210" y="95" text-anchor="middle" class="section-title">Android UI层</text>
  
  <rect x="70" y="110" width="280" height="70" class="android-module"/>
  <text x="210" y="130" text-anchor="middle" class="method-title">SurfaceView/TextureView</text>
  <text x="80" y="150" class="text">• 眼动追踪应用的显示组件</text>
  <text x="80" y="165" class="text">• 提供Surface给Native层</text>

  <!-- 第二层：GazeTrackService -->
  <rect x="390" y="70" width="320" height="120" class="service-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">GazeTrackService</text>
  
  <rect x="410" y="110" width="280" height="70" class="service-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">Service管理层</text>
  <text x="420" y="150" class="text">• 管理眼动追踪和应用模式</text>
  <text x="420" y="165" class="text">• 协调Surface的使用</text>

  <!-- 第三层：GazeService Native -->
  <rect x="730" y="70" width="320" height="120" class="native-layer"/>
  <text x="890" y="95" text-anchor="middle" class="section-title">GazeService Native</text>
  
  <rect x="750" y="110" width="280" height="70" class="native-module"/>
  <text x="890" y="130" text-anchor="middle" class="method-title">ANativeWindow管理</text>
  <text x="760" y="150" class="text">• setANativeWindow(window)</text>
  <text x="760" y="165" class="text">• draw(Mat& img) 渲染</text>

  <!-- 第四层：共享Surface -->
  <rect x="1070" y="70" width="320" height="120" class="surface-layer"/>
  <text x="1230" y="95" text-anchor="middle" class="section-title">共享Surface</text>
  
  <rect x="1090" y="110" width="280" height="70" class="surface-module"/>
  <text x="1230" y="130" text-anchor="middle" class="method-title">同一个ANativeWindow</text>
  <text x="1100" y="150" class="text">• GazeService和GazeApplication</text>
  <text x="1100" y="165" class="text">• 共享同一个Surface对象</text>

  <!-- 第五层：遮盖渲染 -->
  <rect x="1410" y="70" width="320" height="120" class="blur-layer"/>
  <text x="1570" y="95" text-anchor="middle" class="section-title">遮盖渲染</text>
  
  <rect x="1430" y="110" width="280" height="70" class="blur-module"/>
  <text x="1570" y="130" text-anchor="middle" class="method-title">IPQ_BLR引擎</text>
  <text x="1440" y="150" class="text">• GPU虚化处理</text>
  <text x="1440" y="165" class="text">• 渲染到同一Surface</text>

  <!-- 连接箭头 -->
  <line x1="370" y1="130" x2="390" y2="130" class="arrow"/>
  <line x1="710" y1="130" x2="730" y2="130" class="arrow"/>
  <line x1="1050" y1="130" x2="1070" y2="130" class="surface-arrow"/>
  <line x1="1390" y1="130" x2="1410" y2="130" class="blur-arrow"/>

  <!-- 绑定关系 -->
  <line x1="890" y1="190" x2="1230" y2="190" class="binding-arrow"/>
  <text x="1060" y="210" text-anchor="middle" class="highlight-text">共享绑定</text>

  <!-- 详细分析 -->
  <rect x="50" y="240" width="1700" height="1310" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="265" text-anchor="middle" class="title" style="font-size: 22px;">遮盖疗法Surface绑定详细分析</text>

  <!-- 第一部分：Surface创建和绑定 -->
  <text x="70" y="300" class="layer-title">🏗️ Surface创建和绑定流程</text>
  
  <text x="90" y="325" class="flow-text" style="font-weight: bold;">1. Android UI层Surface创建</text>
  <text x="110" y="345" class="flow-text">• <tspan style="color: #3498db;">SurfaceView/TextureView：</tspan>眼动追踪应用的显示组件</text>
  <text x="110" y="360" class="flow-text">• <tspan style="color: #3498db;">Surface对象：</tspan>由Android系统创建，用于图像显示</text>
  <text x="110" y="375" class="flow-text">• <tspan style="color: #3498db;">Native接口：</tspan>通过JNI传递给Native层</text>
  
  <text x="90" y="400" class="flow-text" style="font-weight: bold;">2. GazeService绑定Surface</text>
  <text x="110" y="420" class="code-text">// GazeService.cpp</text>
  <text x="110" y="435" class="code-text">void GazeService::setANativeWindow(ANativeWindow *win) {</text>
  <text x="130" y="450" class="code-text">pthread_mutex_lock(&mutex);</text>
  <text x="130" y="465" class="code-text">ANATIVEWINDOW_RELEASE(this->window);  // 释放旧的</text>
  <text x="130" y="480" class="code-text">this->window = win;  // 🔥 绑定新的Surface</text>
  <text x="130" y="495" class="code-text">pthread_mutex_unlock(&mutex);</text>
  <text x="110" y="510" class="code-text">}</text>
  
  <text x="90" y="535" class="flow-text" style="font-weight: bold;">3. GazeApplication共享Surface</text>
  <text x="110" y="555" class="code-text">// GazeApplication.h</text>
  <text x="110" y="570" class="code-text">class GazeApplication {</text>
  <text x="130" y="585" class="code-text">ANativeWindow *window = nullptr;  // 🔥 同样的Surface指针</text>
  <text x="110" y="600" class="code-text">};</text>
  <text x="110" y="620" class="flow-text">• <tspan style="color: #27ae60;">共享机制：</tspan>GazeService和GazeApplication使用同一个ANativeWindow指针</text>
  <text x="110" y="635" class="flow-text">• <tspan style="color: #27ae60;">内存管理：</tspan>通过引用计数管理Surface生命周期</text>

  <!-- 第二部分：Surface使用机制 -->
  <text x="900" y="300" class="layer-title">🎯 Surface使用机制</text>
  
  <text x="920" y="325" class="flow-text" style="font-weight: bold;">1. GazeService的Surface使用</text>
  <text x="940" y="345" class="code-text">void GazeService::draw(const Mat& img) {</text>
  <text x="960" y="360" class="code-text">if (!window) break;  // 检查Surface有效性</text>
  <text x="960" y="375" class="code-text">// 设置缓冲区格式</text>
  <text x="960" y="390" class="code-text">ANativeWindow_setBuffersGeometry(window, img.cols, img.rows, WINDOW_FORMAT_RGBA_8888);</text>
  <text x="960" y="405" class="code-text">// 锁定缓冲区</text>
  <text x="960" y="420" class="code-text">ANativeWindow_Buffer buffer;</text>
  <text x="960" y="435" class="code-text">ANativeWindow_lock(window, &buffer, nullptr);</text>
  <text x="960" y="450" class="code-text">// 🔥 拷贝图像数据到Surface</text>
  <text x="960" y="465" class="code-text">memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize);</text>
  <text x="940" y="480" class="code-text">}</text>
  
  <text x="920" y="505" class="flow-text" style="font-weight: bold;">2. 用途说明</text>
  <text x="940" y="525" class="flow-text">• <tspan style="color: #e74c3c;">相机预览：</tspan>显示相机采集的图像</text>
  <text x="940" y="540" class="flow-text">• <tspan style="color: #e74c3c;">检测结果：</tspan>显示人脸检测、眼动追踪结果</text>
  <text x="940" y="555" class="flow-text">• <tspan style="color: #e74c3c;">校准界面：</tspan>显示校准过程和结果</text>
  <text x="940" y="570" class="flow-text">• <tspan style="color: #e74c3c;">基础渲染：</tspan>为遮盖疗法提供基础图像</text>

  <!-- 第三部分：遮盖疗法的Surface绑定 -->
  <text x="70" y="670" class="layer-title">🎨 遮盖疗法的Surface绑定</text>
  
  <text x="90" y="695" class="flow-text" style="font-weight: bold;">1. 关键发现：共享同一个Surface</text>
  <text x="110" y="715" class="highlight-text">遮盖疗法绑定的是与GazeService相同的ANativeWindow Surface！</text>
  
  <text x="90" y="740" class="flow-text" style="font-weight: bold;">2. 证据分析</text>
  <text x="110" y="760" class="code-text">// GazeService.h 和 GazeApplication.h 都有相同的定义</text>
  <text x="110" y="775" class="code-text">ANativeWindow *window = nullptr;  // ANativeWindow 用来渲染画面的 == Surface对象</text>
  
  <text x="110" y="795" class="flow-text">• <tspan style="color: #9b59b6;">相同变量名：</tspan>两个类都使用window变量名</text>
  <text x="110" y="810" class="flow-text">• <tspan style="color: #9b59b6;">相同注释：</tspan>"ANativeWindow 用来渲染画面的 == Surface对象"</text>
  <text x="110" y="825" class="flow-text">• <tspan style="color: #9b59b6;">共享机制：</tspan>通过指针共享同一个Surface实例</text>
  
  <text x="90" y="850" class="flow-text" style="font-weight: bold;">3. 绑定过程推测</text>
  <text x="110" y="870" class="code-text">// 可能的绑定过程</text>
  <text x="110" y="885" class="code-text">1. Android UI创建SurfaceView/TextureView</text>
  <text x="110" y="900" class="code-text">2. 通过JNI传递Surface给GazeService</text>
  <text x="110" y="915" class="code-text">3. GazeService.setANativeWindow(surface)</text>
  <text x="110" y="930" class="code-text">4. GazeApplication.window = GazeService.window  // 共享指针</text>
  <text x="110" y="945" class="code-text">5. IPQ_BLR引擎使用同一个Surface进行GPU渲染</text>

  <!-- 第四部分：渲染层次和协调 -->
  <text x="900" y="670" class="layer-title">🔄 渲染层次和协调</text>
  
  <text x="920" y="695" class="flow-text" style="font-weight: bold;">1. 渲染层次结构</text>
  <text x="940" y="715" class="flow-text">• <tspan style="color: #f39c12;">底层：</tspan>GazeService绘制基础图像（相机预览、检测结果）</text>
  <text x="940" y="730" class="flow-text">• <tspan style="color: #f39c12;">上层：</tspan>IPQ_BLR引擎在同一Surface上叠加虚化效果</text>
  <text x="940" y="745" class="flow-text">• <tspan style="color: #f39c12;">合成：</tspan>GPU将基础图像和虚化效果合成</text>
  <text x="940" y="760" class="flow-text">• <tspan style="color: #f39c12;">显示：</tspan>最终合成结果显示在屏幕上</text>
  
  <text x="920" y="785" class="flow-text" style="font-weight: bold;">2. 渲染协调机制</text>
  <text x="940" y="805" class="flow-text">• <tspan style="color: #f39c12;">互斥锁：</tspan>pthread_mutex_t mutex 保护Surface访问</text>
  <text x="940" y="820" class="flow-text">• <tspan style="color: #f39c12;">帧同步：</tspan>确保基础图像和虚化效果同步</text>
  <text x="940" y="835" class="flow-text">• <tspan style="color: #f39c12;">GPU管线：</tspan>利用GPU并行处理能力</text>
  <text x="940" y="850" class="flow-text">• <tspan style="color: #f39c12;">实时性：</tspan>30fps实时渲染和显示</text>
  
  <text x="920" y="875" class="flow-text" style="font-weight: bold;">3. 虚化效果实现</text>
  <text x="940" y="895" class="code-text">// PqBlur.cpp - 虚化渲染</text>
  <text x="940" y="910" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="960" y="925" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="980" y="940" class="code-text">int screen_x = (int)(x * visual_image_width);</text>
  <text x="980" y="955" class="code-text">int screen_y = (int)(y * visual_image_height);</text>
  <text x="980" y="970" class="code-text">// 🔥 在共享Surface上渲染虚化效果</text>
  <text x="980" y="985" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="960" y="1000" class="code-text">}</text>
  <text x="940" y="1015" class="code-text">}</text>

  <!-- 第五部分：Surface特性分析 -->
  <text x="70" y="1050" class="layer-title">📊 Surface特性分析</text>
  
  <text x="90" y="1075" class="flow-text" style="font-weight: bold;">1. Surface类型和属性</text>
  <text x="110" y="1095" class="flow-text">• <tspan style="color: #3498db;">类型：</tspan>眼动追踪应用的主显示Surface</text>
  <text x="110" y="1110" class="flow-text">• <tspan style="color: #3498db;">格式：</tspan>WINDOW_FORMAT_RGBA_8888，支持透明度</text>
  <text x="110" y="1125" class="flow-text">• <tspan style="color: #3498db;">尺寸：</tspan>动态设置，根据图像尺寸调整</text>
  <text x="110" y="1140" class="flow-text">• <tspan style="color: #3498db;">缓冲：</tspan>双缓冲机制，避免撕裂</text>
  
  <text x="90" y="1165" class="flow-text" style="font-weight: bold;">2. 与其他Surface的区别</text>
  <text x="110" y="1185" class="flow-text">• <tspan style="color: #3498db;">不是系统悬浮窗：</tspan>与DotView的悬浮窗Surface完全不同</text>
  <text x="110" y="1200" class="flow-text">• <tspan style="color: #3498db;">应用内Surface：</tspan>属于眼动追踪应用内部</text>
  <text x="110" y="1215" class="flow-text">• <tspan style="color: #3498db;">专用渲染：</tspan>专门用于眼动追踪和遮盖疗法</text>
  <text x="110" y="1230" class="flow-text">• <tspan style="color: #3498db;">高性能：</tspan>支持GPU加速和实时渲染</text>
  
  <text x="90" y="1255" class="flow-text" style="font-weight: bold;">3. 使用场景</text>
  <text x="110" y="1275" class="flow-text">• <tspan style="color: #3498db;">相机预览：</tspan>显示实时相机图像</text>
  <text x="110" y="1290" class="flow-text">• <tspan style="color: #3498db;">检测可视化：</tspan>显示人脸检测、眼动追踪结果</text>
  <text x="110" y="1305" class="flow-text">• <tspan style="color: #3498db;">校准界面：</tspan>显示校准过程和引导</text>
  <text x="110" y="1320" class="flow-text">• <tspan style="color: #3498db;">遮盖疗法：</tspan>实时虚化效果渲染</text>

  <!-- 第六部分：技术优势 -->
  <text x="900" y="1050" class="layer-title">⚡ 共享Surface的技术优势</text>
  
  <text x="920" y="1075" class="flow-text" style="font-weight: bold;">1. 性能优势</text>
  <text x="940" y="1095" class="flow-text">• <tspan style="color: #27ae60;">内存效率：</tspan>避免多个Surface的内存开销</text>
  <text x="940" y="1110" class="flow-text">• <tspan style="color: #27ae60;">GPU优化：</tspan>在同一渲染上下文中处理</text>
  <text x="940" y="1125" class="flow-text">• <tspan style="color: #27ae60;">减少拷贝：</tspan>避免多个Surface间的数据拷贝</text>
  <text x="940" y="1140" class="flow-text">• <tspan style="color: #27ae60;">实时性：</tspan>确保30fps流畅渲染</text>
  
  <text x="920" y="1165" class="flow-text" style="font-weight: bold;">2. 架构优势</text>
  <text x="940" y="1185" class="flow-text">• <tspan style="color: #27ae60;">统一管理：</tspan>所有渲染内容在同一Surface上</text>
  <text x="940" y="1200" class="flow-text">• <tspan style="color: #27ae60;">层次清晰：</tspan>基础图像 + 虚化效果的清晰分层</text>
  <text x="940" y="1215" class="flow-text">• <tspan style="color: #27ae60;">易于同步：</tspan>避免多Surface同步问题</text>
  <text x="940" y="1230" class="flow-text">• <tspan style="color: #27ae60;">资源共享：</tspan>GPU资源和渲染管线共享</text>
  
  <text x="920" y="1255" class="flow-text" style="font-weight: bold;">3. 用户体验优势</text>
  <text x="940" y="1275" class="flow-text">• <tspan style="color: #27ae60;">无缝集成：</tspan>虚化效果与基础图像完美融合</text>
  <text x="940" y="1290" class="flow-text">• <tspan style="color: #27ae60;">实时响应：</tspan>虚化效果实时跟随视点移动</text>
  <text x="940" y="1305" class="flow-text">• <tspan style="color: #27ae60;">视觉连贯：</tspan>所有内容在同一显示区域</text>
  <text x="940" y="1320" class="flow-text">• <tspan style="color: #27ae60;">治疗效果：</tspan>确保遮盖疗法的医疗效果</text>

  <!-- 总结 -->
  <rect x="70" y="1350" width="1600" height="180" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1375" class="layer-title">🌟 遮盖疗法Surface绑定总结</text>
  
  <text x="90" y="1400" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">绑定对象：</tspan>遮盖疗法绑定的是眼动追踪应用主界面的ANativeWindow Surface</text>
  <text x="90" y="1420" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">共享机制：</tspan>GazeService和GazeApplication共享同一个Surface指针</text>
  <text x="90" y="1440" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">渲染层次：</tspan>基础图像(GazeService) + 虚化效果(IPQ_BLR) 在同一Surface上合成</text>
  <text x="90" y="1460" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">技术优势：</tspan>高性能、统一管理、实时响应、无缝集成</text>
  <text x="90" y="1480" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">应用范围：</tspan>仅在眼动追踪应用内有效，不跨应用显示</text>
  <text x="90" y="1500" class="flow-text">• <tspan style="font-weight: bold; color: #8e44ad;">核心特点：</tspan>这是一个应用内的专用Surface，专门用于眼动追踪和遮盖疗法的高性能渲染</text>

</svg>
