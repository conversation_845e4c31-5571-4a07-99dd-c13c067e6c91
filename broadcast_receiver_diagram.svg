<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .class-name { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .method-name { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .description { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .action-text { font-family: Arial, sans-serif; font-size: 10px; fill: #e74c3c; }
      .receiver-box { fill: #ecf0f1; stroke: #34495e; stroke-width: 2; }
      .manager-box { fill: #d5dbdb; stroke: #2c3e50; stroke-width: 2; }
      .fragment-box { fill: #aed6f1; stroke: #2980b9; stroke-width: 2; }
      .manifest-box { fill: #f8c471; stroke: #f39c12; stroke-width: 2; }
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .register-arrow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">BroadcastReceiver 在应用中的使用架构图</text>
  
  <!-- AndroidManifest.xml -->
  <rect x="50" y="60" width="200" height="120" class="manifest-box" rx="5"/>
  <text x="150" y="80" text-anchor="middle" class="class-name">AndroidManifest.xml</text>
  <text x="60" y="100" class="description">静态注册的接收器:</text>
  <text x="60" y="115" class="method-name">• BootReceiver</text>
  <text x="60" y="130" class="method-name">• GlobalBootReceiver</text>
  <text x="60" y="145" class="method-name">• RefreshBindUserReceiver</text>
  <text x="60" y="165" class="action-text">监听系统和自定义广播</text>
  
  <!-- BootReceiver -->
  <rect x="50" y="220" width="180" height="100" class="receiver-box" rx="5"/>
  <text x="140" y="240" text-anchor="middle" class="class-name">BootReceiver</text>
  <text x="60" y="260" class="method-name">onReceive()</text>
  <text x="60" y="275" class="description">监听: BOOT_COMPLETED</text>
  <text x="60" y="290" class="description">作用: 开机启动服务</text>
  <text x="60" y="305" class="action-text">启动 GTService Worker</text>
  
  <!-- GlobalBootReceiver -->
  <rect x="280" y="220" width="200" height="120" class="receiver-box" rx="5"/>
  <text x="380" y="240" text-anchor="middle" class="class-name">GlobalBootReceiver</text>
  <text x="290" y="260" class="method-name">onReceive()</text>
  <text x="290" y="275" class="description">监听视线追踪相关广播:</text>
  <text x="290" y="290" class="action-text">• START_UP_GAZE_TRACK</text>
  <text x="290" y="305" class="action-text">• SHUT_DOWN_GAZE_TRACK</text>
  <text x="290" y="320" class="action-text">• START_CALIBRATION</text>
  
  <!-- RefreshBindUserReceiver -->
  <rect x="520" y="220" width="200" height="100" class="receiver-box" rx="5"/>
  <text x="620" y="240" text-anchor="middle" class="class-name">RefreshBindUserReceiver</text>
  <text x="530" y="260" class="method-name">onReceive()</text>
  <text x="530" y="275" class="description">监听: REFRESH_BIND_USER</text>
  <text x="530" y="290" class="description">作用: 定时刷新用户信息</text>
  <text x="530" y="305" class="action-text">发送 LiveEventBus 事件</text>
  
  <!-- FlipBeatManager -->
  <rect x="750" y="220" width="200" height="120" class="manager-box" rx="5"/>
  <text x="850" y="240" text-anchor="middle" class="class-name">FlipBeatManager</text>
  <text x="760" y="260" class="method-name">mFlipBeatReceiver</text>
  <text x="760" y="275" class="description">动态注册蓝牙广播:</text>
  <text x="760" y="290" class="action-text">• BOND_STATE_CHANGED</text>
  <text x="760" y="305" class="action-text">• ACL_CONNECTED</text>
  <text x="760" y="320" class="action-text">• ACL_DISCONNECTED</text>
  
  <!-- HomeMainFragment -->
  <rect x="50" y="380" width="200" height="120" class="fragment-box" rx="5"/>
  <text x="150" y="400" text-anchor="middle" class="class-name">HomeMainFragment</text>
  <text x="60" y="420" class="method-name">startRefreshBindUser()</text>
  <text x="60" y="435" class="description">使用 AlarmManager</text>
  <text x="60" y="450" class="description">定时发送广播</text>
  <text x="60" y="465" class="action-text">每天凌晨1点触发</text>
  <text x="60" y="480" class="action-text">PendingIntent.getBroadcast()</text>
  
  <!-- ReadHomeMainFragment -->
  <rect x="280" y="380" width="200" height="120" class="fragment-box" rx="5"/>
  <text x="380" y="400" text-anchor="middle" class="class-name">ReadHomeMainFragment</text>
  <text x="290" y="420" class="method-name">startRefreshBindUser()</text>
  <text x="290" y="435" class="description">使用 AlarmManager</text>
  <text x="290" y="450" class="description">定时发送广播</text>
  <text x="290" y="465" class="action-text">每天凌晨1点触发</text>
  <text x="290" y="480" class="action-text">PendingIntent.getBroadcast()</text>
  
  <!-- GazeTrackService -->
  <rect x="520" y="380" width="200" height="100" class="manager-box" rx="5"/>
  <text x="620" y="400" text-anchor="middle" class="class-name">GazeTrackService</text>
  <text x="530" y="420" class="method-name">initObserver()</text>
  <text x="530" y="435" class="description">监听 LiveEventBus:</text>
  <text x="530" y="450" class="action-text">• APP_FOREGROUND_STATE</text>
  <text x="530" y="465" class="action-text">• SWITCH_DISPLAY_VIEWPOINT</text>
  
  <!-- GTApplication -->
  <rect x="750" y="380" width="200" height="100" class="manager-box" rx="5"/>
  <text x="850" y="400" text-anchor="middle" class="class-name">GTApplication</text>
  <text x="760" y="420" class="method-name">ActivityLifecycleCallbacks</text>
  <text x="760" y="435" class="description">发送应用前后台状态</text>
  <text x="760" y="450" class="action-text">LiveEventBus.post()</text>
  <text x="760" y="465" class="action-text">EVENT_APP_FOREGROUND_STATE</text>
  
  <!-- 数据流向箭头 -->
  <!-- AlarmManager 到 RefreshBindUserReceiver -->
  <line x1="150" y1="500" x2="620" y2="320" class="arrow"/>
  <text x="350" y="410" class="action-text">AlarmManager 定时触发</text>
  
  <!-- RefreshBindUserReceiver 到 LiveEventBus -->
  <line x1="620" y1="320" x2="620" y2="380" class="arrow"/>
  <text x="630" y="350" class="action-text">LiveEventBus.post()</text>
  
  <!-- FlipBeatManager 注册 -->
  <line x1="850" y1="340" x2="850" y2="380" class="register-arrow"/>
  <text x="860" y="360" class="description">动态注册/解注册</text>
  
  <!-- 系统广播到接收器 -->
  <line x1="150" y1="180" x2="140" y2="220" class="arrow"/>
  <line x1="150" y1="180" x2="380" y2="220" class="arrow"/>
  <line x1="150" y1="180" x2="620" y2="220" class="arrow"/>
  
  <!-- 说明文字 -->
  <text x="50" y="550" class="class-name">主要使用场景:</text>
  <text x="50" y="570" class="description">1. 系统广播监听 - BootReceiver 监听开机完成</text>
  <text x="50" y="585" class="description">2. 自定义广播通信 - GlobalBootReceiver 处理视线追踪控制</text>
  <text x="50" y="600" class="description">3. 定时任务触发 - RefreshBindUserReceiver 配合 AlarmManager</text>
  <text x="50" y="615" class="description">4. 蓝牙状态监听 - FlipBeatManager 动态注册蓝牙广播</text>
  <text x="50" y="630" class="description">5. 应用间通信 - 通过 LiveEventBus 转发广播事件</text>
  
  <text x="50" y="660" class="class-name">架构特点:</text>
  <text x="50" y="680" class="description">• 静态注册在 AndroidManifest.xml 中声明，应用未启动也能接收</text>
  <text x="50" y="695" class="description">• 动态注册在代码中注册/解注册，生命周期可控</text>
  <text x="50" y="710" class="description">• 结合 LiveEventBus 实现广播到事件总线的转换</text>
  <text x="50" y="725" class="description">• 使用 AlarmManager + PendingIntent 实现定时广播</text>
  
  <!-- 图例 -->
  <rect x="950" y="550" width="15" height="15" class="receiver-box"/>
  <text x="975" y="562" class="description">BroadcastReceiver</text>
  
  <rect x="950" y="575" width="15" height="15" class="manager-box"/>
  <text x="975" y="587" class="description">Manager/Service</text>
  
  <rect x="950" y="600" width="15" height="15" class="fragment-box"/>
  <text x="975" y="612" class="description">Fragment/Activity</text>
  
  <rect x="950" y="625" width="15" height="15" class="manifest-box"/>
  <text x="975" y="637" class="description">配置文件</text>
  
</svg>
