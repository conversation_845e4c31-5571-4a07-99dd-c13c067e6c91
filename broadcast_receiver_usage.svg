<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .receiver-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .operation-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .broadcast-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .event-arrow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#greenarrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="greenarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">BroadcastReceiver 在眼动追踪应用中的使用</text>
  
  <!-- AndroidManifest.xml 静态注册 -->
  <rect x="50" y="60" width="200" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="150" y="85" text-anchor="middle" class="subtitle">AndroidManifest.xml</text>
  <text x="60" y="105" class="operation-text">静态注册的接收器:</text>
  
  <rect x="70" y="115" width="160" height="25" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="150" y="132" text-anchor="middle" class="receiver-title">BootReceiver</text>
  
  <rect x="70" y="145" width="160" height="25" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="150" y="162" text-anchor="middle" class="receiver-title">GlobalBootReceiver</text>
  
  <rect x="70" y="175" width="160" height="25" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
  <text x="150" y="192" text-anchor="middle" class="receiver-title">RefreshBindUserReceiver</text>
  
  <!-- BootReceiver 详情 -->
  <rect x="300" y="60" width="250" height="120" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
  <text x="425" y="85" text-anchor="middle" class="subtitle">BootReceiver</text>
  
  <text x="310" y="105" class="operation-text">监听系统广播:</text>
  <text x="320" y="125" class="code-text">ACTION_BOOT_COMPLETED</text>
  
  <text x="310" y="145" class="operation-text">onReceive() 操作:</text>
  <text x="320" y="165" class="code-text">startBootStartGTServiceWorker(context)</text>
  
  <!-- GlobalBootReceiver 详情 -->
  <rect x="600" y="60" width="250" height="150" fill="#fdf2e9" stroke="#e74c3c" stroke-width="2" rx="10"/>
  <text x="725" y="85" text-anchor="middle" class="subtitle">GlobalBootReceiver</text>
  
  <text x="610" y="105" class="operation-text">监听自定义广播:</text>
  <text x="620" y="125" class="code-text">START_UP_GAZE_TRACK</text>
  <text x="620" y="140" class="code-text">SHUT_DOWN_GAZE_TRACK</text>
  <text x="620" y="155" class="code-text">START_UP_MASK_THERAPY</text>
  <text x="620" y="170" class="code-text">START_CALIBRATION</text>
  
  <text x="610" y="190" class="operation-text">处理眼动追踪控制命令</text>
  
  <!-- RefreshBindUserReceiver 详情 -->
  <rect x="900" y="60" width="250" height="120" fill="#fef9e7" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="1025" y="85" text-anchor="middle" class="subtitle">RefreshBindUserReceiver</text>
  
  <text x="910" y="105" class="operation-text">定时刷新用户信息:</text>
  <text x="920" y="125" class="code-text">ACTION_REFRESH_BIND_USER</text>
  
  <text x="910" y="145" class="operation-text">onReceive() 操作:</text>
  <text x="920" y="165" class="code-text">LiveEventBus.post(EVENT_REFRESH_BIND_USER)</text>
  
  <!-- 动态注册示例 -->
  <rect x="50" y="250" width="500" height="180" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="300" y="275" text-anchor="middle" class="subtitle">动态注册示例 - FlipBeatManager</text>
  
  <text x="70" y="300" class="operation-text">蓝牙广播监听器:</text>
  <rect x="70" y="310" width="200" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="170" y="330" text-anchor="middle" class="code-text">mFlipBeatReceiver</text>
  <text x="80" y="350" class="code-text">• ACTION_BOND_STATE_CHANGED</text>
  <text x="80" y="365" class="code-text">• ACTION_ACL_CONNECTED</text>
  <text x="80" y="380" class="code-text">• ACTION_ACL_DISCONNECTED</text>
  
  <text x="300" y="320" class="operation-text">注册和解注册:</text>
  <rect x="300" y="330" width="230" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="1" rx="5"/>
  <text x="310" y="350" class="code-text">registerReceiver(mFlipBeatReceiver, filter)</text>
  <text x="310" y="365" class="code-text">unregisterReceiver(mFlipBeatReceiver)</text>
  <text x="310" y="385" class="code-text">handlerBroadcastReceiver(intent)</text>
  <text x="310" y="400" class="code-text">→ 处理蓝牙设备状态变化</text>
  
  <!-- 广播流程图 -->
  <rect x="600" y="250" width="550" height="400" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="875" y="275" text-anchor="middle" class="subtitle">广播接收流程</text>
  
  <!-- 系统广播 -->
  <ellipse cx="700" cy="320" rx="80" ry="30" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
  <text x="700" y="315" text-anchor="middle" class="receiver-title">系统广播</text>
  <text x="700" y="330" text-anchor="middle" style="font-size: 10px; fill: white;">BOOT_COMPLETED</text>
  
  <line x1="780" y1="320" x2="850" y2="320" class="broadcast-arrow"/>
  
  <rect x="850" y="300" width="120" height="40" fill="#e8f4fd" stroke="#3498db" stroke-width="1" rx="5"/>
  <text x="910" y="325" text-anchor="middle" class="receiver-title">BootReceiver</text>
  
  <line x1="970" y1="320" x2="1040" y2="320" class="event-arrow"/>
  
  <rect x="1040" y="300" width="100" height="40" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="1090" y="315" text-anchor="middle" style="font-size: 10px; fill: white;">启动Worker</text>
  <text x="1090" y="330" text-anchor="middle" style="font-size: 10px; fill: white;">延迟启动服务</text>
  
  <!-- 自定义广播 -->
  <ellipse cx="700" cy="400" rx="80" ry="30" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
  <text x="700" y="395" text-anchor="middle" class="receiver-title">自定义广播</text>
  <text x="700" y="410" text-anchor="middle" style="font-size: 10px; fill: white;">眼动追踪控制</text>
  
  <line x1="780" y1="400" x2="850" y2="400" class="broadcast-arrow"/>
  
  <rect x="850" y="380" width="120" height="40" fill="#fdf2e9" stroke="#e74c3c" stroke-width="1" rx="5"/>
  <text x="910" y="405" text-anchor="middle" class="receiver-title">GlobalBootReceiver</text>
  
  <line x1="970" y1="400" x2="1040" y2="400" class="event-arrow"/>
  
  <rect x="1040" y="380" width="100" height="40" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="1090" y="395" text-anchor="middle" style="font-size: 10px; fill: white;">控制眼动</text>
  <text x="1090" y="410" text-anchor="middle" style="font-size: 10px; fill: white;">追踪服务</text>
  
  <!-- 定时广播 -->
  <ellipse cx="700" cy="480" rx="80" ry="30" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
  <text x="700" y="475" text-anchor="middle" class="receiver-title">定时广播</text>
  <text x="700" y="490" text-anchor="middle" style="font-size: 10px; fill: white;">AlarmManager</text>
  
  <line x1="780" y1="480" x2="850" y2="480" class="broadcast-arrow"/>
  
  <rect x="850" y="460" width="120" height="40" fill="#fef9e7" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="910" y="485" text-anchor="middle" class="receiver-title">RefreshBindUserReceiver</text>
  
  <line x1="970" y1="480" x2="1040" y2="480" class="event-arrow"/>
  
  <rect x="1040" y="460" width="100" height="40" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="1090" y="475" text-anchor="middle" style="font-size: 10px; fill: white;">LiveEventBus</text>
  <text x="1090" y="490" text-anchor="middle" style="font-size: 10px; fill: white;">刷新用户信息</text>
  
  <!-- 蓝牙广播 -->
  <ellipse cx="700" cy="560" rx="80" ry="30" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
  <text x="700" y="555" text-anchor="middle" class="receiver-title">蓝牙广播</text>
  <text x="700" y="570" text-anchor="middle" style="font-size: 10px; fill: white;">设备状态变化</text>
  
  <line x1="780" y1="560" x2="850" y2="560" class="broadcast-arrow"/>
  
  <rect x="850" y="540" width="120" height="40" fill="#f3e5f5" stroke="#9b59b6" stroke-width="1" rx="5"/>
  <text x="910" y="565" text-anchor="middle" class="receiver-title">FlipBeatManager</text>
  
  <line x1="970" y1="560" x2="1040" y2="560" class="event-arrow"/>
  
  <rect x="1040" y="540" width="100" height="40" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="1090" y="555" text-anchor="middle" style="font-size: 10px; fill: white;">处理蓝牙</text>
  <text x="1090" y="570" text-anchor="middle" style="font-size: 10px; fill: white;">连接状态</text>
  
  <!-- 说明文字 -->
  <text x="50" y="480" class="operation-text">主要特点:</text>
  <text x="70" y="500" class="operation-text">• 静态注册：在AndroidManifest.xml中声明，应用未运行时也能接收</text>
  <text x="70" y="520" class="operation-text">• 动态注册：在代码中注册，灵活控制生命周期</text>
  <text x="70" y="540" class="operation-text">• 权限控制：GlobalBootReceiver使用自定义权限保护</text>
  <text x="70" y="560" class="operation-text">• 事件转发：通过LiveEventBus将广播事件转发给其他组件</text>
  
</svg>
