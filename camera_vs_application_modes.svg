<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .camera-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .tracking-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .application-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .data-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      
      .camera-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .tracking-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .application-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .data-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .process-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">相机眼动数据 vs 应用模式的关系与区别</text>
  
  <!-- 第一层：相机层 -->
  <rect x="50" y="70" width="400" height="150" class="camera-layer"/>
  <text x="250" y="95" text-anchor="middle" class="section-title">相机层 (Camera Layer)</text>
  
  <rect x="70" y="110" width="360" height="100" class="camera-module"/>
  <text x="250" y="130" text-anchor="middle" class="method-title">MSG_TURN_ON_CAMERA</text>
  <text x="80" y="150" class="text">• 启动相机硬件，开始图像采集(30fps)</text>
  <text x="80" y="165" class="text">• 获取原始图像数据流</text>
  <text x="80" y="180" class="text">• 提供基础的图像输入</text>
  <text x="80" y="195" class="text">• <tspan class="highlight-text">仅仅是图像采集，没有眼动算法</tspan></text>

  <!-- 第二层：眼动追踪层 -->
  <rect x="500" y="70" width="400" height="150" class="tracking-layer"/>
  <text x="700" y="95" text-anchor="middle" class="section-title">眼动追踪层 (Tracking Layer)</text>
  
  <rect x="520" y="110" width="360" height="100" class="tracking-module"/>
  <text x="700" y="130" text-anchor="middle" class="method-title">MSG_START_TRACK</text>
  <text x="530" y="150" class="text">• 启动眼动追踪算法</text>
  <text x="530" y="165" class="text">• 从图像中检测和定位眼部</text>
  <text x="530" y="180" class="text">• 计算注视点坐标(x, y)</text>
  <text x="530" y="195" class="text">• <tspan class="highlight-text">产生基础眼动数据，但无特定应用逻辑</tspan></text>

  <!-- 第三层：应用模式层 -->
  <rect x="950" y="70" width="400" height="150" class="application-layer"/>
  <text x="1150" y="95" text-anchor="middle" class="section-title">应用模式层 (Application Layer)</text>
  
  <rect x="970" y="110" width="360" height="100" class="application-module"/>
  <text x="1150" y="130" text-anchor="middle" class="method-title">MSG_START_APPLIED_XXX</text>
  <text x="980" y="150" class="text">• 启动特定的眼动应用模式</text>
  <text x="980" y="165" class="text">• 对眼动数据进行特定处理</text>
  <text x="980" y="180" class="text">• 实现具体的功能逻辑</text>
  <text x="980" y="195" class="text">• <tspan class="highlight-text">这里才是真正的"应用"！</tspan></text>

  <!-- 第四层：数据输出层 -->
  <rect x="1400" y="70" width="350" height="150" class="data-layer"/>
  <text x="1575" y="95" text-anchor="middle" class="section-title">数据输出层</text>
  
  <rect x="1420" y="110" width="310" height="100" class="data-module"/>
  <text x="1575" y="130" text-anchor="middle" class="method-title">应用特定数据</text>
  <text x="1430" y="150" class="text">• CURE: 遮盖效果 + 治疗数据</text>
  <text x="1430" y="165" class="text">• READING: 阅读轨迹分析</text>
  <text x="1430" y="180" class="text">• STARE: 注视稳定性数据</text>
  <text x="1430" y="195" class="text">• FOLLOW: 追随精度数据</text>

  <!-- 连接箭头 -->
  <line x1="450" y1="145" x2="500" y2="145" class="process-arrow"/>
  <line x1="900" y1="145" x2="950" y2="145" class="process-arrow"/>
  <line x1="1350" y1="145" x2="1400" y2="145" class="data-arrow"/>

  <!-- 详细对比分析 -->
  <rect x="50" y="250" width="1700" height="1300" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="275" text-anchor="middle" class="title" style="font-size: 22px;">相机数据 vs 应用模式的核心区别</text>

  <!-- 第一部分：数据流程对比 -->
  <text x="70" y="310" class="layer-title">📊 数据流程对比</text>
  
  <text x="90" y="335" class="flow-text" style="font-weight: bold;">仅启动相机 (MSG_TURN_ON_CAMERA)：</text>
  <text x="110" y="355" class="code-text">相机硬件启动 → 图像采集(30fps) → 原始图像数据</text>
  <text x="110" y="375" class="flow-text">• <tspan style="color: #e74c3c;">结果：</tspan>只有图像流，没有眼动信息</text>
  <text x="110" y="390" class="flow-text">• <tspan style="color: #e74c3c;">用途：</tspan>为后续眼动追踪提供图像输入</text>
  <text x="110" y="405" class="flow-text">• <tspan style="color: #e74c3c;">限制：</tspan>无法获得任何眼动坐标或分析数据</text>
  
  <text x="90" y="430" class="flow-text" style="font-weight: bold;">启动眼动追踪 (MSG_START_TRACK)：</text>
  <text x="110" y="450" class="code-text">相机图像 → 眼部检测 → 瞳孔定位 → 注视点计算 → 眼动坐标(x,y)</text>
  <text x="110" y="470" class="flow-text">• <tspan style="color: #27ae60;">结果：</tspan>基础眼动坐标数据</text>
  <text x="110" y="485" class="flow-text">• <tspan style="color: #27ae60;">用途：</tspan>提供原始的注视点位置信息</text>
  <text x="110" y="500" class="flow-text">• <tspan style="color: #27ae60;">限制：</tspan>只是原始数据，没有应用层的处理逻辑</text>
  
  <text x="90" y="525" class="flow-text" style="font-weight: bold;">启动应用模式 (MSG_START_APPLIED_XXX)：</text>
  <text x="110" y="545" class="code-text">眼动坐标 → 应用特定算法 → 功能实现 → 应用数据输出</text>
  <text x="110" y="565" class="flow-text">• <tspan style="color: #e74c3c;">结果：</tspan>具有特定功能的应用数据</text>
  <text x="110" y="580" class="flow-text">• <tspan style="color: #e74c3c;">用途：</tspan>实现具体的医疗或检查功能</text>
  <text x="110" y="595" class="flow-text">• <tspan style="color: #e74c3c;">优势：</tspan>完整的功能实现，不仅仅是数据</text>

  <!-- 第二部分：具体模式的数据处理 -->
  <text x="900" y="310" class="layer-title">🔧 各模式的数据处理逻辑</text>
  
  <text x="920" y="335" class="flow-text" style="font-weight: bold;">CURE治疗模式的数据处理：</text>
  <text x="940" y="355" class="code-text">眼动坐标(x,y) → 判断弱视眼位置 → 计算遮盖区域 → GPU渲染遮盖效果</text>
  <text x="940" y="375" class="flow-text">• <tspan style="color: #e74c3c;">实时处理：</tspan>30fps实时遮盖渲染</text>
  <text x="940" y="390" class="flow-text">• <tspan style="color: #e74c3c;">参数控制：</tspan>遮盖区域、强度、模式动态调整</text>
  <text x="940" y="405" class="flow-text">• <tspan style="color: #e74c3c;">治疗逻辑：</tspan>根据注视点位置进行精确遮盖</text>
  
  <text x="920" y="430" class="flow-text" style="font-weight: bold;">READING阅读模式的数据处理：</text>
  <text x="940" y="450" class="code-text">眼动坐标(x,y) → 阅读轨迹分析 → 扫视/注视检测 → 阅读模式识别</text>
  <text x="940" y="470" class="flow-text">• <tspan style="color: #3498db;">轨迹分析：</tspan>识别阅读路径和跳跃</text>
  <text x="940" y="485" class="flow-text">• <tspan style="color: #3498db;">速度计算：</tspan>阅读速度和停留时间</text>
  <text x="940" y="500" class="flow-text">• <tspan style="color: #3498db;">理解评估：</tspan>阅读理解度分析</text>
  
  <text x="920" y="525" class="flow-text" style="font-weight: bold;">STARE注视模式的数据处理：</text>
  <text x="940" y="545" class="code-text">眼动坐标(x,y) → 目标点距离计算 → 稳定性分析 → 偏移统计</text>
  <text x="940" y="565" class="flow-text">• <tspan style="color: #f39c12;">精度测试：</tspan>注视点与目标点的偏差</text>
  <text x="940" y="580" class="flow-text">• <tspan style="color: #f39c12;">稳定性：</tspan>注视点抖动和漂移分析</text>
  <text x="940" y="595" class="flow-text">• <tspan style="color: #f39c12;">持续性：</tspan>长时间注视能力评估</text>

  <!-- 第三部分：回调数据的区别 -->
  <text x="70" y="630" class="layer-title">📡 回调数据的区别</text>
  
  <text x="90" y="655" class="flow-text" style="font-weight: bold;">仅相机+追踪的回调数据：</text>
  <text x="110" y="675" class="code-text">// 基础眼动坐标回调</text>
  <text x="110" y="690" class="code-text">onGazePoint(x: Float, y: Float, timestamp: Long) {</text>
  <text x="130" y="705" class="code-text">// 只有原始的注视点坐标</text>
  <text x="130" y="720" class="code-text">// 需要自己实现所有应用逻辑</text>
  <text x="110" y="735" class="code-text">}</text>
  
  <text x="90" y="760" class="flow-text" style="font-weight: bold;">CURE模式的回调数据：</text>
  <text x="110" y="780" class="code-text">// 治疗状态回调</text>
  <text x="110" y="795" class="code-text">MSG_APPLIED_CURE_STATE -> {</text>
  <text x="130" y="810" class="code-text">val isRunning = msg.data.getBoolean("state")</text>
  <text x="130" y="825" class="code-text">// 治疗启动/停止状态</text>
  <text x="110" y="840" class="code-text">}</text>
  <text x="110" y="855" class="code-text">MSG_UPDATE_TREATMENT_DURATION -> {</text>
  <text x="130" y="870" class="code-text">val duration = msg.data.getInt("duration")</text>
  <text x="130" y="885" class="code-text">// 实时治疗时长更新</text>
  <text x="110" y="900" class="code-text">}</text>
  <text x="110" y="915" class="code-text">MSG_REPORT_TREATMENT_RESULT -> {</text>
  <text x="130" y="930" class="code-text">val result = msg.data.getString("result")</text>
  <text x="130" y="945" class="code-text">// 治疗结果数据(JSON格式)</text>
  <text x="110" y="960" class="code-text">}</text>

  <!-- 第四部分：应用场景对比 -->
  <text x="900" y="630" class="layer-title">🎯 应用场景对比</text>
  
  <text x="920" y="655" class="flow-text" style="font-weight: bold;">仅使用相机+追踪数据的场景：</text>
  <text x="940" y="675" class="flow-text">• <tspan style="color: #95a5a6;">自定义开发：</tspan>需要自己实现所有应用逻辑</text>
  <text x="940" y="690" class="flow-text">• <tspan style="color: #95a5a6;">研究用途：</tspan>获取原始数据进行算法研究</text>
  <text x="940" y="705" class="flow-text">• <tspan style="color: #95a5a6;">调试测试：</tspan>验证眼动追踪的基础功能</text>
  <text x="940" y="720" class="flow-text">• <tspan style="color: #95a5a6;">复杂度高：</tspan>需要处理所有细节问题</text>
  
  <text x="920" y="745" class="flow-text" style="font-weight: bold;">使用应用模式的场景：</text>
  <text x="940" y="765" class="flow-text">• <tspan style="color: #27ae60;">医疗应用：</tspan>直接使用成熟的医疗功能</text>
  <text x="940" y="780" class="flow-text">• <tspan style="color: #27ae60;">标准化：</tspan>使用经过验证的算法和流程</text>
  <text x="940" y="795" class="flow-text">• <tspan style="color: #27ae60;">开箱即用：</tspan>无需重复开发基础功能</text>
  <text x="940" y="810" class="flow-text">• <tspan style="color: #27ae60;">质量保证：</tspan>经过医疗级别的测试和验证</text>
  <text x="940" y="825" class="flow-text">• <tspan style="color: #27ae60;">数据完整：</tspan>提供完整的功能数据，不仅仅是坐标</text>

  <!-- 第五部分：技术实现对比 -->
  <text x="70" y="995" class="layer-title">⚙️ 技术实现对比</text>
  
  <text x="90" y="1020" class="flow-text" style="font-weight: bold;">使用原始眼动数据的实现复杂度：</text>
  <text x="110" y="1040" class="code-text">// 需要自己实现所有逻辑</text>
  <text x="110" y="1055" class="code-text">class CustomEyeTrackingApp {</text>
  <text x="130" y="1070" class="code-text">fun onGazePoint(x: Float, y: Float) {</text>
  <text x="150" y="1085" class="code-text">// 1. 坐标系转换</text>
  <text x="150" y="1100" class="code-text">// 2. 滤波和平滑</text>
  <text x="150" y="1115" class="code-text">// 3. 应用特定逻辑</text>
  <text x="150" y="1130" class="code-text">// 4. 渲染和显示</text>
  <text x="150" y="1145" class="code-text">// 5. 数据存储和上报</text>
  <text x="150" y="1160" class="code-text">// 6. 状态管理</text>
  <text x="150" y="1175" class="code-text">// 7. 错误处理</text>
  <text x="130" y="1190" class="code-text">}</text>
  <text x="110" y="1205" class="code-text">}</text>
  
  <text x="90" y="1230" class="flow-text" style="font-weight: bold;">使用应用模式的实现复杂度：</text>
  <text x="110" y="1250" class="code-text">// 简单调用，复杂逻辑已封装</text>
  <text x="110" y="1265" class="code-text">AppliedManager.startAppliedCure()  // 一行代码启动治疗</text>
  <text x="110" y="1280" class="flow-text">• 所有复杂逻辑都在Native层实现</text>
  <text x="110" y="1295" class="flow-text">• 经过医疗级别的测试和优化</text>
  <text x="110" y="1310" class="flow-text">• 提供完整的状态管理和错误处理</text>

  <!-- 第六部分：数据质量对比 -->
  <text x="900" y="995" class="layer-title">📈 数据质量对比</text>
  
  <text x="920" y="1020" class="flow-text" style="font-weight: bold;">原始眼动数据的特点：</text>
  <text x="940" y="1040" class="flow-text">• <tspan style="color: #e67e22;">原始性：</tspan>未经处理的原始坐标</text>
  <text x="940" y="1055" class="flow-text">• <tspan style="color: #e67e22;">噪声多：</tspan>包含抖动、漂移等噪声</text>
  <text x="940" y="1070" class="flow-text">• <tspan style="color: #e67e22;">需要滤波：</tspan>需要自己实现滤波算法</text>
  <text x="940" y="1085" class="flow-text">• <tspan style="color: #e67e22;">坐标系：</tspan>需要处理坐标系转换</text>
  <text x="940" y="1100" class="flow-text">• <tspan style="color: #e67e22;">时间同步：</tspan>需要处理时间戳同步</text>
  
  <text x="920" y="1125" class="flow-text" style="font-weight: bold;">应用模式数据的特点：</text>
  <text x="940" y="1145" class="flow-text">• <tspan style="color: #27ae60;">已处理：</tspan>经过滤波和优化的数据</text>
  <text x="940" y="1160" class="flow-text">• <tspan style="color: #27ae60;">高质量：</tspan>医疗级别的数据质量</text>
  <text x="940" y="1175" class="flow-text">• <tspan style="color: #27ae60;">功能化：</tspan>直接提供功能结果，不仅仅是坐标</text>
  <text x="940" y="1190" class="flow-text">• <tspan style="color: #27ae60;">标准化：</tspan>符合医疗标准的数据格式</text>
  <text x="940" y="1205" class="flow-text">• <tspan style="color: #27ae60;">可靠性：</tspan>经过大量测试验证的可靠性</text>

  <!-- 总结 -->
  <rect x="70" y="1350" width="1600" height="180" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1375" class="layer-title">🌟 核心区别总结</text>
  
  <text x="90" y="1400" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">相机启动：</tspan>只提供图像流，无眼动信息</text>
  <text x="90" y="1420" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">眼动追踪：</tspan>提供基础的注视点坐标，但需要自己实现所有应用逻辑</text>
  <text x="90" y="1440" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">应用模式：</tspan>提供完整的功能实现，包括算法处理、状态管理、数据上报等</text>
  <text x="90" y="1460" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">关键差异：</tspan>应用模式不仅仅是数据，而是完整的功能解决方案</text>
  <text x="90" y="1480" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">医疗应用：</tspan>应用模式提供医疗级别的质量保证和标准化实现</text>
  <text x="90" y="1500" class="flow-text">• <tspan style="font-weight: bold; color: #16a085;">开发效率：</tspan>使用应用模式可以大大降低开发复杂度和提高可靠性</text>

</svg>
