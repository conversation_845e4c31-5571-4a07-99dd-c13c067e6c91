<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .read-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .movement-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .roi-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .follow-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .saccade-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .preview-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 2; }
      .common-box { fill: #f5f5f5; stroke: #9e9e9e; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">项目中Canvas绘制实现全景图</text>
  <text x="800" y="55" text-anchor="middle" class="description-text">眼动追踪系统中的所有自定义绘制组件</text>
  
  <!-- 阅读模块 -->
  <rect x="50" y="80" width="700" height="200" class="read-box" rx="8"/>
  <text x="400" y="105" text-anchor="middle" class="subtitle">阅读模块 (Read Module)</text>
  
  <!-- ReadTrackView -->
  <rect x="80" y="130" width="640" height="130" class="common-box" rx="5"/>
  <text x="400" y="150" text-anchor="middle" class="step-title">ReadTrackView - 阅读轨迹绘制</text>
  <text x="90" y="170" class="step-text" font-weight="bold">使用场景:</text>
  <text x="90" y="185" class="code-text">• ReadTrackActivity - 阅读轨迹查看页面</text>
  <text x="90" y="200" class="code-text">• 静态轨迹显示：一次性绘制完整阅读路径</text>
  <text x="90" y="215" class="code-text">• 动态轨迹显示：100ms间隔逐点动画绘制</text>
  
  <text x="400" y="170" class="step-text" font-weight="bold">Canvas绘制内容:</text>
  <text x="400" y="185" class="code-text">• canvas.drawPath(trackPath, trackPathPaint) - 轨迹路径</text>
  <text x="400" y="200" class="code-text">• canvas.drawCircle() - 视线点圆圈</text>
  <text x="400" y="215" class="code-text">• canvas.drawText() - 序号标注</text>
  
  <text x="90" y="235" class="step-text" font-weight="bold">技术特点:</text>
  <text x="90" y="250" class="code-text">• 协程动画、坐标转换、Paint复用</text>
  
  <!-- 眼动检查模块 -->
  <rect x="800" y="80" width="750" height="400" class="movement-box" rx="8"/>
  <text x="1175" y="105" text-anchor="middle" class="subtitle">眼动检查模块 (Movement Module)</text>
  
  <!-- ROI检测 -->
  <rect x="830" y="130" width="340" height="130" class="roi-box" rx="5"/>
  <text x="1000" y="150" text-anchor="middle" class="step-title">ROI检测绘制</text>
  <text x="840" y="170" class="step-text" font-weight="bold">ROIPathView:</text>
  <text x="840" y="185" class="code-text">• 手绘兴趣区域路径</text>
  <text x="840" y="200" class="code-text">• onTouchEvent触控绘制</text>
  <text x="840" y="215" class="code-text">• canvas.drawPath(paths)</text>
  
  <text x="840" y="235" class="step-text" font-weight="bold">ROIDetectionResultView:</text>
  <text x="840" y="250" class="code-text">• 视线轨迹结果显示</text>
  
  <!-- 追随能力 -->
  <rect x="1190" y="130" width="340" height="130" class="follow-box" rx="5"/>
  <text x="1360" y="150" text-anchor="middle" class="step-title">追随能力绘制</text>
  <text x="1200" y="170" class="step-text" font-weight="bold">FollowAbilityEvaluateResultView:</text>
  <text x="1200" y="185" class="code-text">• 追随路径 + 视线轨迹</text>
  <text x="1200" y="200" class="code-text">• canvas.drawPath(followPath)</text>
  <text x="1200" y="215" class="code-text">• canvas.drawPath(gazePath)</text>
  
  <text x="1200" y="235" class="step-text" font-weight="bold">FollowAbilityEvaluatingView:</text>
  <text x="1200" y="250" class="code-text">• 动态目标点显示</text>
  
  <!-- 扫视能力 -->
  <rect x="830" y="280" width="340" height="130" class="saccade-box" rx="5"/>
  <text x="1000" y="300" text-anchor="middle" class="step-title">扫视能力绘制</text>
  <text x="840" y="320" class="step-text" font-weight="bold">SaccadeAbilityEvaluateResultView:</text>
  <text x="840" y="335" class="code-text">• 扫视轨迹结果显示</text>
  <text x="840" y="350" class="code-text">• canvas.drawPath(gazePath)</text>
  <text x="840" y="365" class="code-text">• canvas.drawCircle() - 视线点</text>
  <text x="840" y="380" class="code-text">• canvas.drawText() - 序号</text>
  
  <text x="840" y="400" class="step-text" font-weight="bold">特点: 与ReadTrackView类似的绘制逻辑</text>
  
  <!-- 追随能力2 -->
  <rect x="1190" y="280" width="340" height="130" class="follow-box" rx="5"/>
  <text x="1360" y="300" text-anchor="middle" class="step-title">追随能力绘制2</text>
  <text x="1200" y="320" class="step-text" font-weight="bold">FollowAbilityEvaluateResultView2:</text>
  <text x="1200" y="335" class="code-text">• 增强版追随结果显示</text>
  <text x="1200" y="350" class="code-text">• 双路径绘制对比</text>
  <text x="1200" y="365" class="code-text">• 更详细的分析数据</text>
  
  <text x="1200" y="385" class="step-text" font-weight="bold">Canvas实现:</text>
  <text x="1200" y="400" class="code-text">• 与ResultView相同的绘制逻辑</text>
  
  <!-- 医疗预览模块 -->
  <rect x="50" y="500" width="700" height="200" class="preview-box" rx="8"/>
  <text x="400" y="525" text-anchor="middle" class="subtitle">医疗预览模块 (Medical Preview)</text>
  
  <!-- ParamPreviewView -->
  <rect x="80" y="550" width="640" height="130" class="common-box" rx="5"/>
  <text x="400" y="570" text-anchor="middle" class="step-title">ParamPreviewView - 参数预览圆圈</text>
  <text x="90" y="590" class="step-text" font-weight="bold">使用场景:</text>
  <text x="90" y="605" class="code-text">• 医疗参数预览界面</text>
  <text x="90" y="620" class="code-text">• 显示治疗区域范围</text>
  <text x="90" y="635" class="code-text">• 实时参数调整预览</text>
  
  <text x="400" y="590" class="step-text" font-weight="bold">Canvas绘制内容:</text>
  <text x="400" y="605" class="code-text">• path.addCircle() - 预览圆圈</text>
  <text x="400" y="620" class="code-text">• canvas.drawPath(path, strokePaint) - 描边</text>
  <text x="400" y="635" class="code-text">• canvas.drawPath(path, paint) - 填充</text>
  
  <text x="90" y="655" class="step-text" font-weight="bold">特点:</text>
  <text x="90" y="670" class="code-text">• updateCircle()动态更新中心坐标</text>
  
  <!-- 视标校准模块 -->
  <rect x="800" y="500" width="750" height="200" class="movement-box" rx="8"/>
  <text x="1175" y="525" text-anchor="middle" class="subtitle">视标校准模块 (Calibration Module)</text>
  
  <!-- 校准相关View -->
  <rect x="830" y="550" width="340" height="130" class="common-box" rx="5"/>
  <text x="1000" y="570" text-anchor="middle" class="step-title">校准相关View</text>
  <text x="840" y="590" class="step-text" font-weight="bold">VisualCalibrationView:</text>
  <text x="840" y="605" class="code-text">• 视标校准界面</text>
  <text x="840" y="620" class="code-text">• 继承ConstraintLayout</text>
  <text x="840" y="635" class="code-text">• 使用布局而非Canvas绘制</text>
  
  <text x="840" y="655" class="step-text" font-weight="bold">PostureCalibrationView:</text>
  <text x="840" y="670" class="code-text">• 姿势校准界面，继承FrameLayout</text>
  
  <!-- DotView -->
  <rect x="1190" y="550" width="340" height="130" class="common-box" rx="5"/>
  <text x="1360" y="570" text-anchor="middle" class="step-title">DotView - 注视点</text>
  <text x="1200" y="590" class="step-text" font-weight="bold">使用场景:</text>
  <text x="1200" y="605" class="code-text">• 悬浮注视点显示</text>
  <text x="1200" y="620" class="code-text">• WindowManager悬浮窗</text>
  <text x="1200" y="635" class="code-text">• 系统级覆盖显示</text>
  
  <text x="1200" y="655" class="step-text" font-weight="bold">实现方式:</text>
  <text x="1200" y="670" class="code-text">• 使用背景资源而非Canvas绘制</text>
  
  <!-- Canvas绘制模式分类 -->
  <rect x="50" y="730" width="1500" height="300" class="common-box" rx="8"/>
  <text x="800" y="755" text-anchor="middle" class="subtitle">Canvas绘制模式分类</text>
  
  <!-- 轨迹绘制类 -->
  <rect x="80" y="780" width="450" height="230" class="read-box" rx="5"/>
  <text x="305" y="800" text-anchor="middle" class="step-title">轨迹绘制类 (Trajectory Drawing)</text>
  <text x="90" y="820" class="step-text" font-weight="bold">共同特征:</text>
  <text x="90" y="835" class="code-text">• canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="90" y="850" class="code-text">• canvas.drawCircle(x, y, radius, pointPaint)</text>
  <text x="90" y="865" class="code-text">• canvas.drawText(index, x, y, textPaint)</text>
  
  <text x="90" y="885" class="step-text" font-weight="bold">包含组件:</text>
  <text x="90" y="900" class="code-text">• ReadTrackView (阅读轨迹)</text>
  <text x="90" y="915" class="code-text">• SaccadeAbilityEvaluateResultView (扫视结果)</text>
  <text x="90" y="930" class="code-text">• ROIDetectionResultView (ROI结果)</text>
  <text x="90" y="945" class="code-text">• FollowAbilityEvaluateResultView (追随结果)</text>
  
  <text x="90" y="965" class="step-text" font-weight="bold">技术特点:</text>
  <text x="90" y="980" class="code-text">• 坐标转换: 相对坐标[0~1] → 屏幕坐标</text>
  <text x="90" y="995" class="code-text">• 数据过滤: checkValid()过滤无效点</text>
  
  <!-- 交互绘制类 -->
  <rect x="550" y="780" width="450" height="230" class="roi-box" rx="5"/>
  <text x="775" y="800" text-anchor="middle" class="step-title">交互绘制类 (Interactive Drawing)</text>
  <text x="560" y="820" class="step-text" font-weight="bold">ROIPathView特征:</text>
  <text x="560" y="835" class="code-text">• onTouchEvent()触控事件处理</text>
  <text x="560" y="850" class="code-text">• ACTION_DOWN: currentPath.moveTo()</text>
  <text x="560" y="865" class="code-text">• ACTION_MOVE: currentPath.lineTo()</text>
  <text x="560" y="880" class="code-text">• ACTION_UP: currentPath.close()</text>
  
  <text x="560" y="900" class="step-text" font-weight="bold">实时绘制:</text>
  <text x="560" y="915" class="code-text">• 每次MOVE事件都调用invalidate()</text>
  <text x="560" y="930" class="code-text">• 实时显示用户绘制路径</text>
  
  <text x="560" y="950" class="step-text" font-weight="bold">应用场景:</text>
  <text x="560" y="965" class="code-text">• ROI兴趣区域手绘标记</text>
  <text x="560" y="980" class="code-text">• 用户自定义区域选择</text>
  
  <!-- 几何绘制类 -->
  <rect x="1020" y="780" width="450" height="230" class="preview-box" rx="5"/>
  <text x="1245" y="800" text-anchor="middle" class="step-title">几何绘制类 (Geometric Drawing)</text>
  <text x="1030" y="820" class="step-text" font-weight="bold">ParamPreviewView特征:</text>
  <text x="1030" y="835" class="code-text">• path.addCircle(centerX, centerY, radius)</text>
  <text x="1030" y="850" class="code-text">• 描边圆圈 + 填充圆圈</text>
  <text x="1030" y="865" class="code-text">• updateCircle()动态更新</text>
  
  <text x="1030" y="885" class="step-text" font-weight="bold">绘制流程:</text>
  <text x="1030" y="900" class="code-text">1. path.reset() - 重置路径</text>
  <text x="1030" y="915" class="code-text">2. path.addCircle() - 添加圆形</text>
  <text x="1030" y="930" class="code-text">3. canvas.drawPath() - 绘制路径</text>
  
  <text x="1030" y="950" class="step-text" font-weight="bold">应用场景:</text>
  <text x="1030" y="965" class="code-text">• 医疗参数预览</text>
  <text x="1030" y="980" class="code-text">• 治疗区域范围显示</text>
  
  <!-- 技术总结 -->
  <rect x="50" y="1050" width="1500" height="200" class="common-box" rx="8"/>
  <text x="800" y="1075" text-anchor="middle" class="subtitle">Canvas绘制技术总结</text>
  
  <!-- 性能优化 -->
  <rect x="80" y="1100" width="450" height="130" class="read-box" rx="5"/>
  <text x="305" y="1120" text-anchor="middle" class="step-title">性能优化技术</text>
  <text x="90" y="1140" class="code-text">• Paint对象复用，避免在onDraw()中创建</text>
  <text x="90" y="1155" class="code-text">• 使用Path.reset()而非重新创建Path</text>
  <text x="90" y="1170" class="code-text">• invalidate()精确控制重绘时机</text>
  <text x="90" y="1185" class="code-text">• 协程处理动画避免阻塞UI线程</text>
  <text x="90" y="1200" class="code-text">• 数据过滤减少无效绘制</text>
  <text x="90" y="1215" class="code-text">• 坐标转换缓存计算结果</text>
  
  <!-- 绘制模式 -->
  <rect x="550" y="1100" width="450" height="130" class="movement-box" rx="5"/>
  <text x="775" y="1120" text-anchor="middle" class="step-title">绘制模式</text>
  <text x="560" y="1140" class="code-text">• 静态绘制: 一次性绘制完整内容</text>
  <text x="560" y="1155" class="code-text">• 动态绘制: 逐步动画绘制(100ms间隔)</text>
  <text x="560" y="1170" class="code-text">• 实时绘制: 响应触控事件即时绘制</text>
  <text x="560" y="1185" class="code-text">• 预览绘制: 参数变化时动态更新</text>
  <text x="560" y="1200" class="code-text">• 结果绘制: 数据处理后的可视化</text>
  <text x="560" y="1215" class="code-text">• 轨迹绘制: 时间序列数据的路径显示</text>
  
  <!-- 应用场景 -->
  <rect x="1020" y="1100" width="450" height="130" class="saccade-box" rx="5"/>
  <text x="1245" y="1120" text-anchor="middle" class="step-title">应用场景分布</text>
  <text x="1030" y="1140" class="code-text">• 阅读轨迹分析: ReadTrackView</text>
  <text x="1030" y="1155" class="code-text">• 眼动能力评估: 扫视/追随/ROI结果显示</text>
  <text x="1030" y="1170" class="code-text">• 交互式标记: ROI区域手绘</text>
  <text x="1030" y="1185" class="code-text">• 医疗参数预览: 治疗区域显示</text>
  <text x="1030" y="1200" class="code-text">• 实时反馈: 动态目标点和注视点</text>
  <text x="1030" y="1215" class="code-text">• 数据可视化: 轨迹路径和统计图形</text>
  
  <!-- 使用统计 -->
  <rect x="50" y="1270" width="1500" height="180" class="common-box" rx="8"/>
  <text x="800" y="1295" text-anchor="middle" class="subtitle">Canvas绘制使用统计与特点</text>
  
  <text x="80" y="1320" class="step-text" font-weight="bold">📊 使用频率统计:</text>
  <text x="100" y="1335" class="step-text">• 轨迹绘制类: 5个组件 (最常用) - ReadTrackView, SaccadeResult, ROIResult, FollowResult×2</text>
  <text x="100" y="1350" class="step-text">• 交互绘制类: 1个组件 - ROIPathView (手绘交互)</text>
  <text x="100" y="1365" class="step-text">• 几何绘制类: 1个组件 - ParamPreviewView (圆形预览)</text>
  
  <text x="80" y="1385" class="step-text" font-weight="bold">🎯 核心应用领域:</text>
  <text x="100" y="1400" class="step-text">• 医疗诊断: 眼动轨迹分析和可视化是核心功能</text>
  <text x="100" y="1415" class="step-text">• 用户交互: ROI区域标记提供直观的操作方式</text>
  <text x="100" y="1430" class="step-text">• 参数预览: 治疗参数的实时可视化反馈</text>
</svg>
