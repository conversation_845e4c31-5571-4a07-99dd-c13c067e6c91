<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; fill: #666; font-weight: bold; }
      .label { font-family: Arial, sans-serif; font-size: 13px; fill: #333; font-weight: bold; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #333; }
      .comment { font-family: 'Courier New', monospace; font-size: 9px; fill: #666; font-style: italic; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .decision-arrow { stroke: #e91e63; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">setPostureCorrectionResult() 代码执行流程</text>
  
  <!-- 函数入口 -->
  <rect x="50" y="60" width="400" height="80" fill="#e8f4fd" stroke="#1976d2" stroke-width="2" rx="8"/>
  <text x="250" y="85" text-anchor="middle" class="subtitle">1. 函数入口</text>
  <text x="60" y="105" class="code">fun setPostureCorrectionResult(result: PostureCorrectionResult) {</text>
  <text x="60" y="120" class="comment">    // 接收实时检测数据，频率30-60FPS</text>
  <text x="60" y="135" class="code">}</text>
  
  <!-- 参数计算 -->
  <rect x="500" y="60" width="400" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">2. 基础参数计算</text>
  <text x="510" y="105" class="code">val scale = 0.5 / result.dist</text>
  <text x="510" y="120" class="code">val centerX = (result.leftX + result.rightX) / 2</text>
  <text x="510" y="135" class="code">val centerY = (result.leftY + result.rightY) / 2</text>
  
  <!-- 角度计算 -->
  <rect x="950" y="60" width="400" height="80" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="8"/>
  <text x="1150" y="85" text-anchor="middle" class="subtitle">3. 角度计算</text>
  <text x="960" y="105" class="code">val deltaY = result.rightY - result.leftY</text>
  <text x="960" y="120" class="code">val deltaX = result.rightX - result.leftX</text>
  <text x="960" y="135" class="code">val rotationAngle = atan2(deltaY, deltaX) * 180 / PI</text>
  
  <!-- 异常检测 -->
  <rect x="50" y="180" width="600" height="120" fill="#ffebee" stroke="#d32f2f" stroke-width="2" rx="8"/>
  <text x="350" y="205" text-anchor="middle" class="subtitle">4. 异常检测 (createPostureException)</text>
  <text x="60" y="225" class="code">val exception = createPostureException(result)</text>
  <text x="60" y="240" class="comment">// 检测距离、位置、倾斜异常</text>
  <text x="60" y="255" class="code">val distanceException = checkDistanceException(result.dist)</text>
  <text x="60" y="270" class="code">val positionException = checkPositionException(centerX, centerY)</text>
  <text x="60" y="285" class="code">val skewException = checkSkewException(rotationAngle)</text>
  
  <!-- 资源选择 -->
  <rect x="700" y="180" width="650" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="8"/>
  <text x="1025" y="205" text-anchor="middle" class="subtitle">5. 资源选择 (getResourcesByException)</text>
  <text x="710" y="225" class="code">val (colorResource, promptResource) = when (exception.degree) {</text>
  <text x="710" y="240" class="code">    0 -> Pair(R.drawable.head_green, R.string.posture_normal)</text>
  <text x="710" y="255" class="code">    1 -> Pair(R.drawable.head_yellow, getPromptByType(exception.type))</text>
  <text x="710" y="270" class="code">    2 -> Pair(R.drawable.head_red, getPromptByType(exception.type))</text>
  <text x="710" y="285" class="code">}</text>
  
  <!-- UI更新分支 -->
  <rect x="50" y="340" width="320" height="140" fill="#fff8e1" stroke="#ff8f00" stroke-width="2" rx="8"/>
  <text x="210" y="365" text-anchor="middle" class="subtitle">6A. 颜色更新</text>
  <text x="60" y="385" class="code">ivRealLocation.setImageResource(</text>
  <text x="60" y="400" class="code">    colorResource</text>
  <text x="60" y="415" class="code">)</text>
  <text x="60" y="435" class="comment">// 根据异常等级设置头像颜色</text>
  <text x="60" y="450" class="comment">// 绿色(正常)/黄色(警告)/红色(错误)</text>
  <text x="60" y="465" class="comment">// 实时视觉反馈</text>
  
  <rect x="390" y="340" width="320" height="140" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="8"/>
  <text x="550" y="365" text-anchor="middle" class="subtitle">6B. 大小更新</text>
  <text x="400" y="385" class="code">val layoutParams = ivRealLocation.layoutParams</text>
  <text x="400" y="400" class="code">layoutParams.width = (originalWidth * scale).toInt()</text>
  <text x="400" y="415" class="code">layoutParams.height = (originalHeight * scale).toInt()</text>
  <text x="400" y="430" class="code">ivRealLocation.layoutParams = layoutParams</text>
  <text x="400" y="450" class="comment">// 根据距离调整头像大小</text>
  <text x="400" y="465" class="comment">// 距离越远头像越小，距离越近头像越大</text>
  
  <rect x="730" y="340" width="320" height="140" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="8"/>
  <text x="890" y="365" text-anchor="middle" class="subtitle">6C. 位置更新</text>
  <text x="740" y="385" class="code">ivRealLocation.x = centerX - layoutParams.width / 2</text>
  <text x="740" y="400" class="code">ivRealLocation.y = centerY - layoutParams.height / 2</text>
  <text x="740" y="420" class="comment">// 头像跟随人脸中心点移动</text>
  <text x="740" y="435" class="comment">// 实时位置同步</text>
  <text x="740" y="450" class="comment">// 中心对齐方式</text>
  <text x="740" y="465" class="comment">// 坐标系转换</text>
  
  <rect x="1070" y="340" width="320" height="140" fill="#ffebee" stroke="#ff5722" stroke-width="2" rx="8"/>
  <text x="1230" y="365" text-anchor="middle" class="subtitle">6D. 角度更新</text>
  <text x="1080" y="385" class="code">ivRealLocation.rotation = rotationAngle.toFloat()</text>
  <text x="1080" y="405" class="comment">// 头像旋转角度跟随人脸倾斜</text>
  <text x="1080" y="420" class="comment">// 实时角度同步</text>
  <text x="1080" y="435" class="comment">// 弧度转角度</text>
  <text x="1080" y="450" class="comment">// Z轴旋转变换</text>
  <text x="1080" y="465" class="comment">// 视觉直观反馈</text>
  
  <!-- 文字提示更新 -->
  <rect x="50" y="520" width="420" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="8"/>
  <text x="260" y="545" text-anchor="middle" class="subtitle">7. 文字提示更新</text>
  <text x="60" y="565" class="code">tvCorrectionPrompt.text = getString(promptResource)</text>
  <text x="60" y="580" class="comment">// 根据异常类型显示对应提示文字</text>
  <text x="60" y="595" class="comment">// "请靠近一点"、"请向右转"等</text>
  <text x="60" y="610" class="comment">// 实时文字指导</text>
  
  <!-- 语音提示 -->
  <rect x="500" y="520" width="420" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="8"/>
  <text x="710" y="545" text-anchor="middle" class="subtitle">8. 语音提示播放</text>
  <text x="510" y="565" class="code">if (exception.degree > 0) {</text>
  <text x="510" y="580" class="code">    PlayManager.playRawMedia(RawMedia.CALIBRATION_RETURN_CORRECT)</text>
  <text x="510" y="595" class="code">}</text>
  <text x="510" y="610" class="comment">// 异常时播放语音指导</text>
  
  <!-- 性能监控 -->
  <rect x="950" y="520" width="400" height="100" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="8"/>
  <text x="1150" y="545" text-anchor="middle" class="subtitle">9. 性能监控</text>
  <text x="960" y="565" class="code">val endTime = System.currentTimeMillis()</text>
  <text x="960" y="580" class="code">val processingTime = endTime - startTime</text>
  <text x="960" y="595" class="comment">// 确保处理时间 < 16ms (60FPS)</text>
  <text x="960" y="610" class="comment">// 监控实时性能指标</text>
  
  <!-- 异常处理分支 -->
  <rect x="200" y="660" width="500" height="120" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="8"/>
  <text x="450" y="685" text-anchor="middle" class="subtitle">异常处理分支</text>
  <text x="210" y="705" class="code">try {</text>
  <text x="210" y="720" class="code">    // 正常更新流程</text>
  <text x="210" y="735" class="code">} catch (e: Exception) {</text>
  <text x="210" y="750" class="code">    Log.e("PostureCalibration", "Update failed", e)</text>
  <text x="210" y="765" class="code">    // 使用上一帧数据或默认状态</text>
  <text x="210" y="780" class="code">}</text>
  
  <!-- 状态缓存 -->
  <rect x="750" y="660" width="500" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="8"/>
  <text x="1000" y="685" text-anchor="middle" class="subtitle">状态缓存机制</text>
  <text x="760" y="705" class="code">// 缓存上一帧状态，避免抖动</text>
  <text x="760" y="720" class="code">private var lastValidResult: PostureCorrectionResult? = null</text>
  <text x="760" y="735" class="code">private var lastUpdateTime: Long = 0</text>
  <text x="760" y="750" class="code">// 限制更新频率，避免过度渲染</text>
  <text x="760" y="765" class="code">if (currentTime - lastUpdateTime > 16) { /* 更新 */ }</text>
  
  <!-- 优化策略 -->
  <rect x="50" y="820" width="1300" height="140" fill="#f1f8e9" stroke="#689f38" stroke-width="2" rx="8"/>
  <text x="700" y="845" text-anchor="middle" class="subtitle">性能优化策略</text>
  
  <text x="70" y="870" class="label">1. 资源复用:</text>
  <text x="70" y="885" class="code">• 预加载drawable资源，避免重复IO操作</text>
  <text x="70" y="900" class="code">• 缓存LayoutParams对象，减少对象创建</text>
  <text x="70" y="915" class="code">• 复用字符串资源，避免重复getString调用</text>
  
  <text x="450" y="870" class="label">2. 计算优化:</text>
  <text x="450" y="885" class="code">• 使用查找表替代三角函数计算</text>
  <text x="450" y="900" class="code">• 整数运算替代浮点运算</text>
  <text x="450" y="915" class="code">• 避免重复计算相同值</text>
  
  <text x="850" y="870" class="label">3. 渲染优化:</text>
  <text x="850" y="885" class="code">• 硬件加速渲染</text>
  <text x="850" y="900" class="code">• 减少不必要的重绘</text>
  <text x="850" y="915" class="code">• 批量更新UI属性</text>
  
  <text x="1150" y="870" class="label">4. 内存优化:</text>
  <text x="1150" y="885" class="code">• 及时释放临时对象</text>
  <text x="1150" y="900" class="code">• 避免内存泄漏</text>
  <text x="1150" y="915" class="code">• 监控内存使用</text>
  
  <!-- 执行时间统计 -->
  <rect x="400" y="980" width="600" height="15" fill="#4caf50" rx="7"/>
  <text x="700" y="992" text-anchor="middle" class="code" fill="#fff">总执行时间: 8-12ms (目标 &lt; 16ms，保证60FPS流畅度)</text>
  
  <!-- 连接箭头 -->
  <line x1="250" y1="140" x2="700" y2="140" class="arrow" transform="translate(0, -80)"/>
  <line x1="700" y1="140" x2="1150" y2="140" class="arrow" transform="translate(0, -80)"/>
  <line x1="350" y1="140" x2="350" y2="180" class="arrow"/>
  <line x1="1025" y1="140" x2="1025" y2="180" class="arrow"/>
  
  <!-- 并行执行箭头 -->
  <line x1="350" y1="300" x2="210" y2="340" class="arrow"/>
  <line x1="350" y1="300" x2="550" y2="340" class="arrow"/>
  <line x1="1025" y1="300" x2="890" y2="340" class="arrow"/>
  <line x1="1025" y1="300" x2="1230" y2="340" class="arrow"/>
  
  <!-- 汇聚箭头 -->
  <line x1="260" y1="480" x2="260" y2="520" class="arrow"/>
  <line x1="710" y1="480" x2="710" y2="520" class="arrow"/>
  <line x1="1150" y1="480" x2="1150" y2="520" class="arrow"/>
  
  <!-- 异常分支箭头 -->
  <line x1="450" y1="620" x2="450" y2="660" class="decision-arrow"/>
  <line x1="1000" y1="620" x2="1000" y2="660" class="decision-arrow"/>
</svg>
