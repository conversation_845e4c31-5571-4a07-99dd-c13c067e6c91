<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .comment { font-family: 'Courier New', monospace; font-size: 8px; fill: #7f8c8d; }
      .step-number { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .step-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      .highlight-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .execution-arrow { stroke: #ff9800; stroke-width: 3; fill: none; marker-end: url(#execarrow); }
    </style>
    <marker id="execarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">代码执行顺序详解 - ViewModel到UI更新完整链路</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">眼球运动评估模块实际代码执行流程</text>

  <!-- 步骤1: 用户触发 -->
  <rect x="50" y="70" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="90" text-anchor="middle" class="step-number">1</text>
  
  <rect x="100" y="70" width="500" height="60" class="code-box" rx="5"/>
  <text x="350" y="90" text-anchor="middle" class="subtitle">用户操作触发 - Activity/Fragment</text>
  <text x="110" y="110" class="code">// 用户点击上传按钮</text>
  <text x="110" y="125" class="code">btnUpload.setOnClickListener { viewModel.uploadImage(bitmap) }</text>

  <!-- 步骤2: ViewModel方法调用 -->
  <rect x="50" y="150" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="170" text-anchor="middle" class="step-number">2</text>
  
  <rect x="100" y="150" width="600" height="100" class="highlight-box" rx="5"/>
  <text x="400" y="170" text-anchor="middle" class="subtitle">ViewModel.uploadImage() - 业务逻辑入口</text>
  <text x="110" y="190" class="code">fun uploadImage(bitmap: Bitmap) {</text>
  <text x="120" y="205" class="code">  isLoadingLiveData.postValue(true) // 设置加载状态</text>
  <text x="120" y="220" class="code">  viewModelScope.launch { // 启动协程</text>
  <text x="130" y="235" class="code">    val result = saccadeAbilityRepository.uploadImage(createMultipartBody(bitmap))</text>
  <text x="120" y="250" class="code">  }</text>
  <text x="110" y="265" class="code">}</text>

  <!-- 步骤3: Repository调用 -->
  <rect x="50" y="270" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="290" text-anchor="middle" class="step-number">3</text>
  
  <rect x="100" y="270" width="600" height="80" class="code-box" rx="5"/>
  <text x="400" y="290" text-anchor="middle" class="subtitle">Repository.uploadImage() - 数据访问层</text>
  <text x="110" y="310" class="code">suspend fun uploadImage(file: MultipartBody.Part): FileUploadResponse {</text>
  <text x="120" y="325" class="code">  return MovementClient.createService(FileUploadApiService::class.java)</text>
  <text x="130" y="340" class="code">    .uploadImage(file) // Retrofit网络请求</text>
  <text x="110" y="355" class="code">}</text>

  <!-- 步骤4: 网络请求执行 -->
  <rect x="50" y="370" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="390" text-anchor="middle" class="step-number">4</text>
  
  <rect x="100" y="370" width="600" height="60" class="code-box" rx="5"/>
  <text x="400" y="390" text-anchor="middle" class="subtitle">Retrofit API调用 - 网络层</text>
  <text x="110" y="410" class="code">@Multipart @POST("/api/files/upload/image")</text>
  <text x="110" y="425" class="code">suspend fun uploadImage(@Part file: MultipartBody.Part): FileUploadResponse</text>

  <!-- 步骤5: 响应处理 -->
  <rect x="50" y="450" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="470" text-anchor="middle" class="step-number">5</text>
  
  <rect x="100" y="450" width="600" height="120" class="highlight-box" rx="5"/>
  <text x="400" y="470" text-anchor="middle" class="subtitle">ViewModel响应处理 - collectResponse机制</text>
  <text x="110" y="490" class="code">MutableStateFlow(saccadeAbilityRepository.uploadImage(file))</text>
  <text x="120" y="505" class="code">.collectResponse {</text>
  <text x="130" y="520" class="code">  onSuccess = { result, _, _ -></text>
  <text x="140" y="535" class="code">    Logger.d(TAG, "图片上传成功: ${result.data?.url}")</text>
  <text x="140" y="550" class="code">    uploadImageResultLiveData.postValue(result) // 关键步骤!</text>
  <text x="130" y="565" class="code">  }</text>
  <text x="120" y="580" class="code">}</text>

  <!-- 步骤6: LiveData.postValue()内部执行 -->
  <rect x="50" y="590" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="610" text-anchor="middle" class="step-number">6</text>
  
  <rect x="100" y="590" width="600" height="100" class="code-box" rx="5"/>
  <text x="400" y="610" text-anchor="middle" class="subtitle">LiveData.postValue() 内部机制</text>
  <text x="110" y="630" class="code">public void postValue(T value) {</text>
  <text x="120" y="645" class="code">  // 线程安全处理</text>
  <text x="120" y="660" class="code">  ArchTaskExecutor.getInstance().postToMainThread(() -> {</text>
  <text x="130" y="675" class="code">    setValue(value); // 切换到主线程执行</text>
  <text x="120" y="690" class="code">  });</text>
  <text x="110" y="705" class="code">}</text>

  <!-- 步骤7: Observer回调执行 -->
  <rect x="50" y="710" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="730" text-anchor="middle" class="step-number">7</text>
  
  <rect x="100" y="710" width="600" height="120" class="highlight-box" rx="5"/>
  <text x="400" y="730" text-anchor="middle" class="subtitle">Observer.onChanged() 回调执行</text>
  <text x="110" y="750" class="code">// Activity中注册的Observer</text>
  <text x="110" y="765" class="code">viewModel.uploadImageResultLiveData.observe(this, Observer { result -></text>
  <text x="120" y="780" class="code">  isLoadingLiveData.postValue(false) // 关闭加载状态</text>
  <text x="120" y="795" class="code">  if (result?.data != null) {</text>
  <text x="130" y="810" class="code">    submitDataToServerWithImage(result.data.url) // 继续后续流程</text>
  <text x="120" y="825" class="code">  }</text>
  <text x="110" y="840" class="code">})</text>

  <!-- 步骤8: UI更新执行 -->
  <rect x="50" y="850" width="40" height="30" class="step-box" rx="5"/>
  <text x="70" y="870" text-anchor="middle" class="step-number">8</text>
  
  <rect x="100" y="850" width="600" height="100" class="code-box" rx="5"/>
  <text x="400" y="870" text-anchor="middle" class="subtitle">UI组件更新执行</text>
  <text x="110" y="890" class="code">// 加载状态Observer同时触发</text>
  <text x="110" y="905" class="code">viewModel.isLoadingLiveData.observe(this) { isLoading -></text>
  <text x="120" y="920" class="code">  progressBar.isVisible = isLoading</text>
  <text x="120" y="935" class="code">  btnUpload.isEnabled = !isLoading</text>
  <text x="110" y="950" class="code">}</text>

  <!-- 右侧补充说明 -->
  <rect x="750" y="70" width="600" height="880" class="step-box" rx="10" fill-opacity="0.05"/>
  <text x="1050" y="100" text-anchor="middle" class="subtitle">执行环境和线程说明</text>

  <!-- 线程执行环境 -->
  <rect x="770" y="120" width="560" height="100" class="code-box" rx="5"/>
  <text x="1050" y="140" text-anchor="middle" class="subtitle">线程执行环境</text>
  <text x="780" y="160" class="text">🧵 <tspan class="code">步骤1-2</tspan>: 主线程 (UI Thread)</text>
  <text x="780" y="175" class="text">🔄 <tspan class="code">步骤3-4</tspan>: IO线程 (Dispatchers.IO)</text>
  <text x="780" y="190" class="text">📡 <tspan class="code">步骤5</tspan>: IO线程处理响应</text>
  <text x="780" y="205" class="text">⚡ <tspan class="code">步骤6-8</tspan>: 主线程 (自动切换)</text>

  <!-- 生命周期检查 -->
  <rect x="770" y="240" width="560" height="120" class="code-box" rx="5"/>
  <text x="1050" y="260" text-anchor="middle" class="subtitle">生命周期检查机制</text>
  <text x="780" y="280" class="code">// Observer回调前的生命周期检查</text>
  <text x="780" y="295" class="code">private void considerNotify(ObserverWrapper observer) {</text>
  <text x="790" y="310" class="code">  if (!observer.shouldBeActive()) {</text>
  <text x="800" y="325" class="code">    return; // PAUSED/STOPPED状态不执行</text>
  <text x="790" y="340" class="code">  }</text>
  <text x="790" y="355" class="code">  observer.mObserver.onChanged(mData);</text>
  <text x="780" y="370" class="code">}</text>

  <!-- 错误处理机制 -->
  <rect x="770" y="380" width="560" height="120" class="code-box" rx="5"/>
  <text x="1050" y="400" text-anchor="middle" class="subtitle">错误处理机制</text>
  <text x="780" y="420" class="code">collectResponse {</text>
  <text x="790" y="435" class="code">  onSuccess = { result -> liveData.postValue(result) }</text>
  <text x="790" y="450" class="code">  onFailed = { errorCode, errorMsg -></text>
  <text x="800" y="465" class="code">    Logger.e(TAG, "上传失败: $errorMsg")</text>
  <text x="800" y="480" class="code">    liveData.postValue(null) // 传递错误状态</text>
  <text x="790" y="495" class="code">  }</text>
  <text x="780" y="510" class="code">}</text>

  <!-- 配置变更处理 -->
  <rect x="770" y="520" width="560" height="100" class="code-box" rx="5"/>
  <text x="1050" y="540" text-anchor="middle" class="subtitle">配置变更处理</text>
  <text x="780" y="560" class="text">📱 <tspan class="code">Activity重建</tspan>: ViewModel实例保持</text>
  <text x="780" y="575" class="text">🔗 <tspan class="code">Observer重新注册</tspan>: 自动接收最新数据</text>
  <text x="780" y="590" class="text">💾 <tspan class="code">数据状态保持</tspan>: LiveData值不丢失</text>
  <text x="780" y="605" class="text">🎯 <tspan class="code">UI自动恢复</tspan>: 无需手动处理</text>

  <!-- 性能优化要点 -->
  <rect x="770" y="640" width="560" height="120" class="code-box" rx="5"/>
  <text x="1050" y="660" text-anchor="middle" class="subtitle">性能优化要点</text>
  <text x="780" y="680" class="text">⚡ <tspan class="code">避免频繁postValue</tspan>: 合并多次更新</text>
  <text x="780" y="695" class="text">🔄 <tspan class="code">使用MediatorLiveData</tspan>: 组合多个数据源</text>
  <text x="780" y="710" class="text">📊 <tspan class="code">合理使用observe</tspan>: 避免observeForever</text>
  <text x="780" y="725" class="text">🛡️ <tspan class="code">及时移除Observer</tspan>: 防止内存泄漏</text>
  <text x="780" y="740" class="text">🎯 <tspan class="code">数据变换</tspan>: 使用Transformations.map/switchMap</text>

  <!-- 最佳实践总结 -->
  <rect x="770" y="780" width="560" height="150" class="highlight-box" rx="5"/>
  <text x="1050" y="800" text-anchor="middle" class="subtitle">最佳实践总结</text>
  <text x="780" y="820" class="text">✅ <tspan class="code">单一职责</tspan>: ViewModel处理业务逻辑，Repository处理数据</text>
  <text x="780" y="835" class="text">✅ <tspan class="code">响应式编程</tspan>: 数据驱动UI，避免手动状态管理</text>
  <text x="780" y="850" class="text">✅ <tspan class="code">生命周期安全</tspan>: 使用正确的LifecycleOwner</text>
  <text x="780" y="865" class="text">✅ <tspan class="code">线程安全</tspan>: postValue()处理线程切换</text>
  <text x="780" y="880" class="text">✅ <tspan class="code">错误处理</tspan>: 统一的异常处理机制</text>
  <text x="780" y="895" class="text">✅ <tspan class="code">可测试性</tspan>: 清晰的依赖关系和接口设计</text>
  <text x="780" y="910" class="text">✅ <tspan class="code">内存安全</tspan>: 自动清理和生命周期管理</text>

  <!-- 执行流程箭头 -->
  <line x1="90" y1="130" x2="90" y2="150" class="execution-arrow"/>
  <line x1="90" y1="250" x2="90" y2="270" class="execution-arrow"/>
  <line x1="90" y1="350" x2="90" y2="370" class="execution-arrow"/>
  <line x1="90" y1="430" x2="90" y2="450" class="execution-arrow"/>
  <line x1="90" y1="570" x2="90" y2="590" class="execution-arrow"/>
  <line x1="90" y1="690" x2="90" y2="710" class="execution-arrow"/>
  <line x1="90" y1="830" x2="90" y2="850" class="execution-arrow"/>

</svg>
