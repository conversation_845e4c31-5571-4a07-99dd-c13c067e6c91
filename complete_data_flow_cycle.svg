<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .step-number { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .viewmodel-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .repository-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 3; }
      .livedata-box { fill: #fff8e1; stroke: #ffc107; stroke-width: 3; }
      .observer-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; }
      .ui-box { fill: #ffebee; stroke: #f44336; stroke-width: 3; }
      .step-circle { fill: #ff9800; stroke: #e65100; stroke-width: 2; }
      .data-flow { stroke: #ff9800; stroke-width: 4; fill: none; marker-end: url(#dataflow); }
      .return-flow { stroke: #4caf50; stroke-width: 4; fill: none; marker-end: url(#returnflow); }
      .observe-flow { stroke: #2196f3; stroke-width: 3; fill: none; stroke-dasharray: 8,4; marker-end: url(#observeflow); }
      .ui-update { stroke: #f44336; stroke-width: 3; fill: none; marker-end: url(#uiupdate); }
    </style>
    <marker id="dataflow" markerWidth="14" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 14 5, 0 10" fill="#ff9800" />
    </marker>
    <marker id="returnflow" markerWidth="14" markerHeight="10" refX="13" refY="5" orient="auto">
      <polygon points="0 0, 14 5, 0 10" fill="#4caf50" />
    </marker>
    <marker id="observeflow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2196f3" />
    </marker>
    <marker id="uiupdate" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#f44336" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">ViewModel → Repository → PostValue → Observer → UI更新完整流程</text>
  <text x="800" y="50" text-anchor="middle" class="subtitle">眼球运动评估模块数据流转核心机制</text>

  <!-- 步骤1: ViewModel调用Repository -->
  <circle cx="150" cy="120" r="25" class="step-circle"/>
  <text x="150" y="128" text-anchor="middle" class="step-number">1</text>
  
  <rect x="50" y="160" width="400" height="120" class="viewmodel-box" rx="10"/>
  <text x="250" y="185" text-anchor="middle" class="subtitle">ViewModel调用Repository</text>
  <text x="70" y="210" class="code">fun uploadImage(bitmap: Bitmap) {</text>
  <text x="80" y="225" class="code">  viewModelScope.launch {</text>
  <text x="90" y="240" class="code">    try {</text>
  <text x="100" y="255" class="code">      val result = saccadeAbilityRepository</text>
  <text x="110" y="270" class="code">        .uploadImage(createMultipartBody(bitmap))</text>
  <text x="90" y="285" class="code">    } catch (e: Exception) { ... }</text>
  <text x="80" y="300" class="code">  }</text>
  <text x="70" y="315" class="code">}</text>

  <!-- 步骤2: Repository执行网络请求 -->
  <circle cx="650" cy="120" r="25" class="step-circle"/>
  <text x="650" y="128" text-anchor="middle" class="step-number">2</text>
  
  <rect x="500" y="160" width="400" height="120" class="repository-box" rx="10"/>
  <text x="700" y="185" text-anchor="middle" class="subtitle">Repository执行网络请求</text>
  <text x="520" y="210" class="code">suspend fun uploadImage(</text>
  <text x="530" y="225" class="code">  file: MultipartBody.Part</text>
  <text x="520" y="240" class="code">): FileUploadResponse {</text>
  <text x="530" y="255" class="code">  return apiService.uploadImage(file)</text>
  <text x="520" y="270" class="code">}</text>
  <text x="520" y="290" class="comment">// Retrofit自动处理HTTP请求</text>
  <text x="520" y="305" class="comment">// 返回服务器响应数据</text>

  <!-- 步骤3: Repository返回数据 -->
  <circle cx="1150" cy="120" r="25" class="step-circle"/>
  <text x="1150" y="128" text-anchor="middle" class="step-number">3</text>
  
  <rect x="950" y="160" width="400" height="120" class="repository-box" rx="10"/>
  <text x="1150" y="185" text-anchor="middle" class="subtitle">Repository返回数据</text>
  <text x="970" y="210" class="code">// API响应示例</text>
  <text x="970" y="225" class="code">FileUploadResponse {</text>
  <text x="980" y="240" class="code">  code: 200,</text>
  <text x="980" y="255" class="code">  message: "上传成功",</text>
  <text x="980" y="270" class="code">  data: {</text>
  <text x="990" y="285" class="code">    url: "https://server.com/image.jpg"</text>
  <text x="980" y="300" class="code">  }</text>
  <text x="970" y="315" class="code">}</text>

  <!-- 步骤4: ViewModel处理响应并PostValue -->
  <circle cx="150" cy="400" r="25" class="step-circle"/>
  <text x="150" y="408" text-anchor="middle" class="step-number">4</text>
  
  <rect x="50" y="440" width="500" height="140" class="viewmodel-box" rx="10"/>
  <text x="300" y="465" text-anchor="middle" class="subtitle">ViewModel处理响应并PostValue</text>
  <text x="70" y="490" class="code">MutableStateFlow(repository.uploadImage(file))</text>
  <text x="80" y="505" class="code">.collectResponse {</text>
  <text x="90" y="520" class="code">  onSuccess = { result, _, _ -></text>
  <text x="100" y="535" class="code">    Logger.d("图片上传成功: ${result.data?.url}")</text>
  <text x="100" y="550" class="code">    uploadImageResultLiveData.postValue(result)</text>
  <text x="90" y="565" class="code">  }</text>
  <text x="90" y="580" class="code">  onFailed = { errorCode, errorMsg -></text>
  <text x="100" y="595" class="code">    Logger.e("上传失败: $errorMsg")</text>
  <text x="100" y="610" class="code">    uploadImageResultLiveData.postValue(null)</text>
  <text x="90" y="625" class="code">  }</text>
  <text x="80" y="640" class="code">}</text>

  <!-- 步骤5: LiveData数据发布 -->
  <circle cx="750" cy="400" r="25" class="step-circle"/>
  <text x="750" y="408" text-anchor="middle" class="step-number">5</text>
  
  <rect x="600" y="440" width="400" height="140" class="livedata-box" rx="10"/>
  <text x="800" y="465" text-anchor="middle" class="subtitle">LiveData数据发布机制</text>
  <text x="620" y="490" class="code">// postValue()内部实现</text>
  <text x="620" y="505" class="code">public void postValue(T value) {</text>
  <text x="630" y="520" class="code">  boolean postTask;</text>
  <text x="630" y="535" class="code">  synchronized (mDataLock) {</text>
  <text x="640" y="550" class="code">    postTask = mPendingData == NOT_SET;</text>
  <text x="640" y="565" class="code">    mPendingData = value;</text>
  <text x="630" y="580" class="code">  }</text>
  <text x="630" y="595" class="code">  if (!postTask) return;</text>
  <text x="630" y="610" class="code">  // 切换到主线程执行</text>
  <text x="630" y="625" class="code">  ArchTaskExecutor.getInstance()</text>
  <text x="640" y="640" class="code">    .postToMainThread(mPostValueRunnable);</text>
  <text x="620" y="655" class="code">}</text>

  <!-- 步骤6: Observer接收回调 -->
  <circle cx="1250" cy="400" r="25" class="step-circle"/>
  <text x="1250" y="408" text-anchor="middle" class="step-number">6</text>
  
  <rect x="1050" y="440" width="400" height="140" class="observer-box" rx="10"/>
  <text x="1250" y="465" text-anchor="middle" class="subtitle">Observer接收回调</text>
  <text x="1070" y="490" class="code">// Activity中的Observer注册</text>
  <text x="1070" y="505" class="code">viewModel.uploadImageResultLiveData</text>
  <text x="1080" y="520" class="code">.observe(this, Observer { result -></text>
  <text x="1090" y="535" class="code">  // 生命周期检查通过后执行</text>
  <text x="1090" y="550" class="code">  if (result?.data != null) {</text>
  <text x="1100" y="565" class="code">    Logger.d("收到上传结果: ${result.data.url}")</text>
  <text x="1100" y="580" class="code">    submitDataToServerWithImage(result.data.url)</text>
  <text x="1090" y="595" class="code">  } else {</text>
  <text x="1100" y="610" class="code">    showErrorMessage("图片上传失败")</text>
  <text x="1090" y="625" class="code">  }</text>
  <text x="1080" y="640" class="code">})</text>

  <!-- 步骤7: UI组件更新 -->
  <circle cx="400" cy="720" r="25" class="step-circle"/>
  <text x="400" y="728" text-anchor="middle" class="step-number">7</text>
  
  <rect x="50" y="760" width="1400" height="160" class="ui-box" rx="10"/>
  <text x="750" y="785" text-anchor="middle" class="subtitle">UI组件更新 - 多种更新方式</text>

  <!-- Toast更新 -->
  <rect x="70" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="170" y="820" text-anchor="middle" class="subtitle">Toast消息</text>
  <text x="80" y="840" class="code">Toast.makeText(this,</text>
  <text x="90" y="855" class="code">"图片上传成功",</text>
  <text x="90" y="870" class="code">Toast.LENGTH_SHORT)</text>
  <text x="90" y="885" class="code">.show()</text>

  <!-- ProgressBar更新 -->
  <rect x="290" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="390" y="820" text-anchor="middle" class="subtitle">ProgressBar</text>
  <text x="300" y="840" class="code">progressBar.visibility = </text>
  <text x="310" y="855" class="code">View.GONE</text>
  <text x="300" y="870" class="code">btnUpload.isEnabled = true</text>
  <text x="300" y="885" class="code">btnUpload.text = "上传图片"</text>

  <!-- TextView更新 -->
  <rect x="510" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="610" y="820" text-anchor="middle" class="subtitle">TextView</text>
  <text x="520" y="840" class="code">tvStatus.text = </text>
  <text x="530" y="855" class="code">"上传完成"</text>
  <text x="520" y="870" class="code">tvImageUrl.text = </text>
  <text x="530" y="885" class="code">result.data.url</text>

  <!-- ImageView更新 -->
  <rect x="730" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="830" y="820" text-anchor="middle" class="subtitle">ImageView</text>
  <text x="740" y="840" class="code">Glide.with(this)</text>
  <text x="750" y="855" class="code">.load(result.data.url)</text>
  <text x="750" y="870" class="code">.into(ivPreview)</text>

  <!-- RecyclerView更新 -->
  <rect x="950" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="1050" y="820" text-anchor="middle" class="subtitle">RecyclerView</text>
  <text x="960" y="840" class="code">val newItem = ImageItem(</text>
  <text x="970" y="855" class="code">url = result.data.url</text>
  <text x="960" y="870" class="code">)</text>
  <text x="960" y="885" class="code">adapter.addItem(newItem)</text>

  <!-- Canvas重绘 -->
  <rect x="1170" y="800" width="200" height="100" class="ui-box" rx="5"/>
  <text x="1270" y="820" text-anchor="middle" class="subtitle">Canvas重绘</text>
  <text x="1180" y="840" class="code">evaluateResultView</text>
  <text x="1190" y="855" class="code">.setImageUrl(url)</text>
  <text x="1180" y="870" class="code">evaluateResultView</text>
  <text x="1190" y="885" class="code">.invalidate()</text>

  <!-- 数据流箭头 -->
  <!-- ViewModel到Repository -->
  <line x1="450" y1="220" x2="500" y2="220" class="data-flow"/>
  <text x="475" y="210" text-anchor="middle" class="text" fill="#ff9800">调用</text>

  <!-- Repository到网络 -->
  <line x1="900" y1="220" x2="950" y2="220" class="data-flow"/>
  <text x="925" y="210" text-anchor="middle" class="text" fill="#ff9800">HTTP请求</text>

  <!-- 网络返回到Repository -->
  <line x1="950" y1="240" x2="900" y2="240" class="return-flow"/>
  <text x="925" y="255" text-anchor="middle" class="text" fill="#4caf50">响应数据</text>

  <!-- Repository返回到ViewModel -->
  <line x1="500" y1="240" x2="450" y2="240" class="return-flow"/>
  <text x="475" y="255" text-anchor="middle" class="text" fill="#4caf50">返回结果</text>

  <!-- ViewModel到LiveData -->
  <line x1="550" y1="540" x2="600" y2="540" class="data-flow"/>
  <text x="575" y="530" text-anchor="middle" class="text" fill="#ff9800">postValue()</text>

  <!-- LiveData到Observer -->
  <line x1="1000" y1="540" x2="1050" y2="540" class="observe-flow"/>
  <text x="1025" y="530" text-anchor="middle" class="text" fill="#2196f3">observe()</text>

  <!-- Observer到UI -->
  <line x1="1250" y1="580" x2="750" y2="760" class="ui-update"/>
  <text x="1000" y="670" text-anchor="middle" class="text" fill="#f44336">UI更新</text>

  <!-- 生命周期和错误处理说明 -->
  <rect x="50" y="940" width="1500" height="120" class="livedata-box" rx="10"/>
  <text x="800" y="965" text-anchor="middle" class="subtitle">关键机制说明</text>
  
  <text x="70" y="990" class="text">🧵 <tspan class="subtitle">线程安全</tspan>: Repository在IO线程执行，postValue()自动切换到主线程，Observer在主线程执行UI更新</text>
  <text x="70" y="1010" class="text">🔄 <tspan class="subtitle">生命周期感知</tspan>: Observer只在STARTED/RESUMED状态接收回调，DESTROYED时自动移除，避免内存泄漏</text>
  <text x="70" y="1030" class="text">⚠️ <tspan class="subtitle">错误处理</tspan>: collectResponse统一处理成功/失败/异常，通过postValue(null)传递错误状态</text>
  <text x="70" y="1050" class="text">📊 <tspan class="subtitle">数据一致性</tspan>: ViewModel在配置变更时保持数据，Observer重新注册后自动接收最新数据</text>

  <!-- 流程总结 -->
  <rect x="50" y="1080" width="1500" height="100" class="viewmodel-box" rx="10"/>
  <text x="800" y="1105" text-anchor="middle" class="subtitle">完整流程总结</text>
  
  <text x="70" y="1130" class="text">1️⃣ ViewModel.uploadImage() → 2️⃣ Repository.uploadImage() → 3️⃣ API网络请求 → 4️⃣ 响应数据返回</text>
  <text x="70" y="1150" class="text">5️⃣ collectResponse处理 → 6️⃣ LiveData.postValue() → 7️⃣ Observer.onChanged() → 8️⃣ UI组件更新</text>
  <text x="70" y="1170" class="text">🎯 <tspan class="subtitle">核心优势</tspan>: 数据驱动UI、自动生命周期管理、线程安全、响应式编程、统一错误处理</text>

</svg>
