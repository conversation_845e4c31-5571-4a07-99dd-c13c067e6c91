<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .fragment-bg { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .compose-bg { fill: #27ae60; stroke: #229954; stroke-width: 2; rx: 8; }
      .adapter-bg { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .viewmodel-bg { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .component-bg { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; rx: 8; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .refactor-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#refactor-arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="refactor-arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">家庭版首页Fragment到Compose重构架构图</text>
  
  <!-- 当前架构 (左侧) -->
  <text x="200" y="70" text-anchor="middle" class="subtitle">当前Fragment架构</text>
  
  <!-- HomeMainFragment -->
  <rect x="50" y="90" width="300" height="120" class="fragment-bg"/>
  <text x="200" y="115" text-anchor="middle" class="subtitle" fill="white">HomeMainFragment</text>
  <text x="60" y="135" class="text" fill="white">• 用户信息显示和绑定状态</text>
  <text x="60" y="150" class="text" fill="white">• 顶部导航栏 (更多、帮助、校准等)</text>
  <text x="60" y="165" class="text" fill="white">• RecyclerView展示治疗模块</text>
  <text x="60" y="180" class="text" fill="white">• 观察多个ViewModel的LiveData</text>
  <text x="60" y="195" class="text" fill="white">• 生命周期管理和事件总线监听</text>

  <!-- TreatmentModuleAdapter -->
  <rect x="50" y="230" width="300" height="100" class="adapter-bg"/>
  <text x="200" y="255" text-anchor="middle" class="subtitle" fill="white">TreatmentModuleAdapter</text>
  <text x="60" y="275" class="text" fill="white">• RecyclerView.Adapter实现</text>
  <text x="60" y="290" class="text" fill="white">• 动态Fragment替换逻辑</text>
  <text x="60" y="305" class="text" fill="white">• 根据moduleKey选择Fragment</text>
  <text x="60" y="320" class="text" fill="white">• FragmentManager事务管理</text>

  <!-- MaskTherapyFragment -->
  <rect x="50" y="350" width="140" height="120" class="fragment-bg"/>
  <text x="120" y="375" text-anchor="middle" class="subtitle" fill="white">MaskTherapy</text>
  <text x="120" y="390" text-anchor="middle" class="subtitle" fill="white">Fragment</text>
  <text x="60" y="410" class="small-text" fill="white">• 数字遮盖疗法</text>
  <text x="60" y="425" class="small-text" fill="white">• 开关控制</text>
  <text x="60" y="440" class="small-text" fill="white">• 时间进度显示</text>
  <text x="60" y="455" class="small-text" fill="white">• 服务通信</text>

  <!-- VisualTrainFragment -->
  <rect x="210" y="350" width="140" height="120" class="fragment-bg"/>
  <text x="280" y="375" text-anchor="middle" class="subtitle" fill="white">VisualTrain</text>
  <text x="280" y="390" text-anchor="middle" class="subtitle" fill="white">Fragment</text>
  <text x="220" y="410" class="small-text" fill="white">• 视觉训练疗法</text>
  <text x="220" y="425" class="small-text" fill="white">• 蓝牙设备连接</text>
  <text x="220" y="440" class="small-text" fill="white">• 训练列表/分类</text>
  <text x="220" y="455" class="small-text" fill="white">• 动态子Fragment</text>

  <!-- ViewModels -->
  <rect x="50" y="490" width="300" height="80" class="viewmodel-bg"/>
  <text x="200" y="515" text-anchor="middle" class="subtitle" fill="white">ViewModels</text>
  <text x="60" y="535" class="text" fill="white">HomeViewModel, TreatmentViewModel, MaskViewModel</text>
  <text x="60" y="550" class="text" fill="white">TrainViewModel, UserViewModel, UpdateViewModel</text>
  <text x="60" y="565" class="text" fill="white">LiveData观察者模式，数据驱动UI更新</text>

  <!-- 重构箭头 -->
  <path d="M 400 300 Q 500 250 600 300" class="refactor-arrow"/>
  <text x="500" y="240" text-anchor="middle" class="subtitle" fill="#e74c3c">重构为Compose</text>

  <!-- Compose架构 (右侧) -->
  <text x="1000" y="70" text-anchor="middle" class="subtitle">Compose重构架构</text>

  <!-- HomeMainScreen -->
  <rect x="750" y="90" width="500" height="120" class="compose-bg"/>
  <text x="1000" y="115" text-anchor="middle" class="subtitle" fill="white">HomeMainScreen (@Composable)</text>
  <text x="760" y="135" class="text" fill="white">• 状态提升：所有状态通过参数传入</text>
  <text x="760" y="150" class="text" fill="white">• 事件回调：onMoreClick, onUserClick, onModuleClick等</text>
  <text x="760" y="165" class="text" fill="white">• 声明式UI：Column + LazyColumn布局</text>
  <text x="760" y="180" class="text" fill="white">• 响应式：observeAsState()观察LiveData</text>
  <text x="760" y="195" class="text" fill="white">• 组合式：拆分为多个可复用Composable组件</text>

  <!-- Compose组件层 -->
  <rect x="750" y="230" width="500" height="140" class="component-bg"/>
  <text x="1000" y="255" text-anchor="middle" class="subtitle" fill="white">Compose组件层</text>
  
  <!-- 顶部组件 -->
  <rect x="760" y="270" width="230" height="45" class="compose-bg"/>
  <text x="875" y="285" text-anchor="middle" class="text" fill="white">TopNavigationBarCompose</text>
  <text x="875" y="300" text-anchor="middle" class="small-text" fill="white">导航栏、Logo、功能按钮</text>

  <!-- 用户信息组件 -->
  <rect x="1010" y="270" width="230" height="45" class="compose-bg"/>
  <text x="1125" y="285" text-anchor="middle" class="text" fill="white">UserInfoCompose</text>
  <text x="1125" y="300" text-anchor="middle" class="small-text" fill="white">用户头像、姓名、绑定状态</text>

  <!-- 模块列表组件 -->
  <rect x="760" y="320" width="230" height="45" class="compose-bg"/>
  <text x="875" y="335" text-anchor="middle" class="text" fill="white">TreatmentModuleListCompose</text>
  <text x="875" y="350" text-anchor="middle" class="small-text" fill="white">LazyColumn + 模块卡片</text>

  <!-- 空状态组件 -->
  <rect x="1010" y="320" width="230" height="45" class="compose-bg"/>
  <text x="1125" y="335" text-anchor="middle" class="text" fill="white">EmptyStateCompose</text>
  <text x="1125" y="350" text-anchor="middle" class="small-text" fill="white">网络异常、重试按钮</text>

  <!-- 治疗模块Compose组件 -->
  <rect x="750" y="390" width="240" height="120" class="compose-bg"/>
  <text x="870" y="415" text-anchor="middle" class="subtitle" fill="white">MaskTherapyCompose</text>
  <text x="760" y="435" class="text" fill="white">• 替代MaskTherapyFragment</text>
  <text x="760" y="450" class="text" fill="white">• Switch + 时间进度组件</text>
  <text x="760" y="465" class="text" fill="white">• 状态管理：remember + State</text>
  <text x="760" y="480" class="text" fill="white">• 副作用：LaunchedEffect</text>
  <text x="760" y="495" class="text" fill="white">• 服务通信：回调函数</text>

  <rect x="1010" y="390" width="240" height="120" class="compose-bg"/>
  <text x="1130" y="415" text-anchor="middle" class="subtitle" fill="white">VisualTrainCompose</text>
  <text x="1020" y="435" class="text" fill="white">• 替代VisualTrainFragment</text>
  <text x="1020" y="450" class="text" fill="white">• 蓝牙状态显示</text>
  <text x="1020" y="465" class="text" fill="white">• 训练列表/分类切换</text>
  <text x="1020" y="480" class="text" fill="white">• 动态内容：when表达式</text>
  <text x="1020" y="495" class="text" fill="white">• 事件处理：onClick回调</text>

  <!-- 状态管理层 -->
  <rect x="750" y="530" width="500" height="80" class="viewmodel-bg"/>
  <text x="1000" y="555" text-anchor="middle" class="subtitle" fill="white">状态管理 (保持不变)</text>
  <text x="760" y="575" class="text" fill="white">• ViewModels继续使用LiveData</text>
  <text x="760" y="590" class="text" fill="white">• Compose通过observeAsState()订阅状态变化</text>
  <text x="760" y="605" class="text" fill="white">• 业务逻辑保持在ViewModel层，UI层只负责展示</text>

  <!-- 重构优势 -->
  <rect x="50" y="630" width="1200" height="180" class="component-bg"/>
  <text x="650" y="655" text-anchor="middle" class="subtitle" fill="white">Compose重构优势</text>
  
  <text x="70" y="680" class="text" fill="white">🎯 <tspan style="font-weight: bold;">声明式UI：</tspan>用状态描述UI，而不是命令式地修改UI</text>
  <text x="70" y="700" class="text" fill="white">🔄 <tspan style="font-weight: bold;">响应式更新：</tspan>状态变化自动触发UI重组，无需手动刷新</text>
  <text x="70" y="720" class="text" fill="white">🧩 <tspan style="font-weight: bold;">组件化：</tspan>可复用的Composable函数，提高代码复用性</text>
  <text x="70" y="740" class="text" fill="white">⚡ <tspan style="font-weight: bold;">性能优化：</tspan>智能重组，只更新变化的部分</text>
  <text x="70" y="760" class="text" fill="white">🎨 <tspan style="font-weight: bold;">主题系统：</tspan>Material Design 3支持，统一的设计语言</text>
  <text x="70" y="780" class="text" fill="white">🔧 <tspan style="font-weight: bold;">简化逻辑：</tspan>消除Fragment生命周期复杂性，减少Adapter样板代码</text>
  <text x="70" y="800" class="text" fill="white">🧪 <tspan style="font-weight: bold;">易于测试：</tspan>纯函数组件，更容易进行单元测试和UI测试</text>

  <!-- 连接线 -->
  <path d="M 200 210 L 200 230" class="arrow"/>
  <path d="M 120 330 L 120 350" class="arrow"/>
  <path d="M 280 330 L 280 350" class="arrow"/>
  <path d="M 200 470 L 200 490" class="arrow"/>

  <path d="M 1000 210 L 1000 230" class="arrow"/>
  <path d="M 870 370 L 870 390" class="arrow"/>
  <path d="M 1130 370 L 1130 390" class="arrow"/>
  <path d="M 1000 510 L 1000 530" class="arrow"/>

  <!-- 实现步骤 -->
  <rect x="50" y="830" width="1200" height="160" class="viewmodel-bg"/>
  <text x="650" y="855" text-anchor="middle" class="subtitle" fill="white">重构实施步骤</text>
  
  <text x="70" y="880" class="text" fill="white">1️⃣ <tspan style="font-weight: bold;">创建基础Compose组件：</tspan>TopNavigationBarCompose, UserInfoCompose, EmptyStateCompose</text>
  <text x="70" y="900" class="text" fill="white">2️⃣ <tspan style="font-weight: bold;">重构治疗模块：</tspan>MaskTherapyCompose, VisualTrainCompose替代对应Fragment</text>
  <text x="70" y="920" class="text" fill="white">3️⃣ <tspan style="font-weight: bold;">创建主屏幕：</tspan>HomeMainScreen整合所有组件，处理状态和事件</text>
  <text x="70" y="940" class="text" fill="white">4️⃣ <tspan style="font-weight: bold;">集成Activity：</tspan>在HomeMainActivity中使用ComposeView或setContent</text>
  <text x="70" y="960" class="text" fill="white">5️⃣ <tspan style="font-weight: bold;">渐进式迁移：</tspan>保持ViewModel不变，逐步替换Fragment为Compose</text>
  <text x="70" y="980" class="text" fill="white">6️⃣ <tspan style="font-weight: bold;">测试验证：</tspan>确保功能完整性，性能优化，用户体验一致</text>

  <!-- 技术栈 -->
  <rect x="50" y="1010" width="1200" height="120" class="adapter-bg"/>
  <text x="650" y="1035" text-anchor="middle" class="subtitle" fill="white">技术栈和依赖</text>
  
  <text x="70" y="1060" class="text" fill="white">📦 <tspan style="font-weight: bold;">Compose BOM：</tspan>androidx.compose.bom (统一版本管理)</text>
  <text x="70" y="1080" class="text" fill="white">🎨 <tspan style="font-weight: bold;">Material3：</tspan>androidx.compose.material3 (现代设计组件)</text>
  <text x="70" y="1100" class="text" fill="white">🔄 <tspan style="font-weight: bold;">LiveData集成：</tspan>androidx.compose.runtime:runtime-livedata (状态观察)</text>
  <text x="70" y="1120" class="text" fill="white">🖼️ <tspan style="font-weight: bold;">图片加载：</tspan>coil-compose (异步图片加载，替代Glide)</text>

  <text x="650" y="1060" class="text" fill="white">🧭 <tspan style="font-weight: bold;">导航：</tspan>androidx.navigation:navigation-compose</text>
  <text x="650" y="1080" class="text" fill="white">🏗️ <tspan style="font-weight: bold;">架构：</tspan>androidx.lifecycle:lifecycle-viewmodel-compose</text>
  <text x="650" y="1100" class="text" fill="white">🎭 <tspan style="font-weight: bold;">动画：</tspan>androidx.compose.animation (流畅过渡效果)</text>
  <text x="650" y="1120" class="text" fill="white">🔧 <tspan style="font-weight: bold;">工具：</tspan>androidx.compose.ui:ui-tooling (预览和调试)</text>

</svg>
