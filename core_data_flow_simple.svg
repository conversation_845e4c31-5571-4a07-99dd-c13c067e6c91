<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #27ae60; }
      .step-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .viewmodel-circle { fill: #2196f3; stroke: #1976d2; stroke-width: 3; }
      .repository-circle { fill: #9c27b0; stroke: #7b1fa2; stroke-width: 3; }
      .livedata-circle { fill: #ffc107; stroke: #f57c00; stroke-width: 3; }
      .observer-circle { fill: #4caf50; stroke: #388e3c; stroke-width: 3; }
      .ui-circle { fill: #f44336; stroke: #d32f2f; stroke-width: 3; }
      .flow-arrow { stroke: #ff9800; stroke-width: 5; fill: none; marker-end: url(#flowarrow); }
      .return-arrow { stroke: #4caf50; stroke-width: 4; fill: none; marker-end: url(#returnarrow); }
      .observe-arrow { stroke: #2196f3; stroke-width: 4; fill: none; stroke-dasharray: 10,5; marker-end: url(#observearrow); }
    </style>
    <marker id="flowarrow" markerWidth="16" markerHeight="12" refX="15" refY="6" orient="auto">
      <polygon points="0 0, 16 6, 0 12" fill="#ff9800" />
    </marker>
    <marker id="returnarrow" markerWidth="16" markerHeight="12" refX="15" refY="6" orient="auto">
      <polygon points="0 0, 16 6, 0 12" fill="#4caf50" />
    </marker>
    <marker id="observearrow" markerWidth="16" markerHeight="12" refX="15" refY="6" orient="auto">
      <polygon points="0 0, 16 6, 0 12" fill="#2196f3" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="35" text-anchor="middle" class="title">ViewModel → Repository → PostValue → Observer → UI</text>
  <text x="700" y="60" text-anchor="middle" class="subtitle">核心数据流转机制</text>

  <!-- 步骤1: ViewModel -->
  <circle cx="150" cy="150" r="80" class="viewmodel-circle"/>
  <text x="150" y="140" text-anchor="middle" class="step-text">ViewModel</text>
  <text x="150" y="160" text-anchor="middle" class="step-text">调用Repository</text>

  <rect x="50" y="250" width="200" height="100" class="viewmodel-circle" rx="10" fill-opacity="0.1"/>
  <text x="150" y="275" text-anchor="middle" class="subtitle">业务逻辑处理</text>
  <text x="60" y="295" class="code">viewModelScope.launch {</text>
  <text x="70" y="310" class="code">  val result = repository</text>
  <text x="80" y="325" class="code">    .uploadImage(bitmap)</text>
  <text x="60" y="340" class="code">}</text>

  <!-- 步骤2: Repository -->
  <circle cx="450" cy="150" r="80" class="repository-circle"/>
  <text x="450" y="140" text-anchor="middle" class="step-text">Repository</text>
  <text x="450" y="160" text-anchor="middle" class="step-text">网络请求</text>

  <rect x="350" y="250" width="200" height="100" class="repository-circle" rx="10" fill-opacity="0.1"/>
  <text x="450" y="275" text-anchor="middle" class="subtitle">数据访问层</text>
  <text x="360" y="295" class="code">suspend fun uploadImage() {</text>
  <text x="370" y="310" class="code">  return apiService</text>
  <text x="380" y="325" class="code">    .uploadImage(file)</text>
  <text x="360" y="340" class="code">}</text>

  <!-- 步骤3: 数据返回 -->
  <rect x="600" y="120" width="200" height="60" class="repository-circle" rx="10" fill-opacity="0.2"/>
  <text x="700" y="140" text-anchor="middle" class="subtitle">服务器响应</text>
  <text x="610" y="160" class="code">FileUploadResponse { url: "..." }</text>

  <!-- 步骤4: PostValue -->
  <circle cx="950" cy="150" r="80" class="livedata-circle"/>
  <text x="950" y="140" text-anchor="middle" class="step-text">LiveData</text>
  <text x="950" y="160" text-anchor="middle" class="step-text">PostValue</text>

  <rect x="850" y="250" width="200" height="100" class="livedata-circle" rx="10" fill-opacity="0.1"/>
  <text x="950" y="275" text-anchor="middle" class="subtitle">数据发布</text>
  <text x="860" y="295" class="code">collectResponse {</text>
  <text x="870" y="310" class="code">  onSuccess = { result -></text>
  <text x="880" y="325" class="code">    liveData.postValue(result)</text>
  <text x="870" y="340" class="code">  }</text>
  <text x="860" y="355" class="code">}</text>

  <!-- 步骤5: Observer -->
  <circle cx="1250" cy="150" r="80" class="observer-circle"/>
  <text x="1250" y="140" text-anchor="middle" class="step-text">Observer</text>
  <text x="1250" y="160" text-anchor="middle" class="step-text">接收回调</text>

  <rect x="1150" y="250" width="200" height="100" class="observer-circle" rx="10" fill-opacity="0.1"/>
  <text x="1250" y="275" text-anchor="middle" class="subtitle">观察者回调</text>
  <text x="1160" y="295" class="code">observe(this) { result -></text>
  <text x="1170" y="310" class="code">  if (result != null) {</text>
  <text x="1180" y="325" class="code">    updateUI(result)</text>
  <text x="1170" y="340" class="code">  }</text>
  <text x="1160" y="355" class="code">}</text>

  <!-- 步骤6: UI更新 -->
  <circle cx="700" cy="450" r="80" class="ui-circle"/>
  <text x="700" y="440" text-anchor="middle" class="step-text">UI组件</text>
  <text x="700" y="460" text-anchor="middle" class="step-text">界面更新</text>

  <!-- UI更新示例 -->
  <rect x="200" y="550" width="180" height="80" class="ui-circle" rx="10" fill-opacity="0.1"/>
  <text x="290" y="575" text-anchor="middle" class="subtitle">Toast消息</text>
  <text x="210" y="595" class="code">Toast.makeText(</text>
  <text x="220" y="610" class="code">"上传成功").show()</text>

  <rect x="400" y="550" width="180" height="80" class="ui-circle" rx="10" fill-opacity="0.1"/>
  <text x="490" y="575" text-anchor="middle" class="subtitle">ProgressBar</text>
  <text x="410" y="595" class="code">progressBar.visibility</text>
  <text x="420" y="610" class="code">= View.GONE</text>

  <rect x="600" y="550" width="180" height="80" class="ui-circle" rx="10" fill-opacity="0.1"/>
  <text x="690" y="575" text-anchor="middle" class="subtitle">TextView</text>
  <text x="610" y="595" class="code">tvStatus.text = </text>
  <text x="620" y="610" class="code">"上传完成"</text>

  <rect x="800" y="550" width="180" height="80" class="ui-circle" rx="10" fill-opacity="0.1"/>
  <text x="890" y="575" text-anchor="middle" class="subtitle">Button状态</text>
  <text x="810" y="595" class="code">btnUpload.isEnabled</text>
  <text x="820" y="610" class="code">= true</text>

  <rect x="1000" y="550" width="180" height="80" class="ui-circle" rx="10" fill-opacity="0.1"/>
  <text x="1090" y="575" text-anchor="middle" class="subtitle">Canvas重绘</text>
  <text x="1010" y="595" class="code">resultView.invalidate()</text>
  <text x="1010" y="610" class="code">// 触发onDraw()</text>

  <!-- 数据流箭头 -->
  <!-- ViewModel到Repository -->
  <line x1="230" y1="150" x2="370" y2="150" class="flow-arrow"/>
  <text x="300" y="140" text-anchor="middle" class="text" fill="#ff9800">1. 调用</text>

  <!-- Repository到网络 -->
  <line x1="530" y1="150" x2="600" y2="150" class="flow-arrow"/>
  <text x="565" y="140" text-anchor="middle" class="text" fill="#ff9800">2. 请求</text>

  <!-- 网络返回 -->
  <line x1="800" y1="150" x2="870" y2="150" class="return-arrow"/>
  <text x="835" y="140" text-anchor="middle" class="text" fill="#4caf50">3. 响应</text>

  <!-- PostValue -->
  <line x1="1030" y1="150" x2="1170" y2="150" class="observe-arrow"/>
  <text x="1100" y="140" text-anchor="middle" class="text" fill="#2196f3">4. postValue()</text>

  <!-- Observer到UI -->
  <line x1="1250" y1="230" x2="700" y2="370" class="flow-arrow"/>
  <text x="975" y="300" text-anchor="middle" class="text" fill="#ff9800">5. UI更新</text>

  <!-- 生命周期管理说明 -->
  <rect x="50" y="650" width="1300" height="120" class="livedata-circle" rx="10" fill-opacity="0.1"/>
  <text x="700" y="675" text-anchor="middle" class="subtitle">关键特性说明</text>
  
  <text x="70" y="700" class="text">🧵 <tspan class="subtitle">线程管理</tspan>: Repository在IO线程执行网络请求，postValue()自动切换到主线程执行Observer回调</text>
  <text x="70" y="720" class="text">🔄 <tspan class="subtitle">生命周期感知</tspan>: Observer只在Activity/Fragment的STARTED/RESUMED状态接收回调，DESTROYED时自动清理</text>
  <text x="70" y="740" class="text">📊 <tspan class="subtitle">数据驱动</tspan>: UI完全由LiveData数据状态驱动，数据变化自动触发UI更新，实现响应式编程</text>
  <text x="70" y="760" class="text">⚠️ <tspan class="subtitle">错误处理</tspan>: collectResponse统一处理成功/失败状态，通过postValue(null)传递错误状态给Observer</text>

</svg>
