<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .comment { font-family: 'Courier New', monospace; font-size: 8px; fill: #7f8c8d; }
      .evaluating-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .result-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .canvas-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .method-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .paint-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .inheritance-arrow { stroke: #2196f3; stroke-width: 3; fill: none; marker-end: url(#inheritarrow); }
      .method-arrow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#methodarrow); }
      .canvas-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#canvasarrow); }
    </style>
    <marker id="inheritarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2196f3" />
    </marker>
    <marker id="methodarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
    <marker id="canvasarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">自定义View实现详解</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">眼球运动评估模块中的Canvas绘制和交互实现</text>

  <!-- FrameLayout基类 -->
  <rect x="550" y="70" width="300" height="60" class="evaluating-box" rx="8"/>
  <text x="700" y="90" text-anchor="middle" class="subtitle">FrameLayout (Android基类)</text>
  <text x="560" y="110" class="text">📱 Android原生容器View</text>
  <text x="560" y="125" class="text">🔧 支持子View叠加显示</text>

  <!-- 评估View继承 -->
  <rect x="50" y="180" width="400" height="200" class="evaluating-box" rx="8"/>
  <text x="250" y="205" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingView</text>
  <text x="70" y="230" class="code">class SaccadeAbilityEvaluatingView(</text>
  <text x="80" y="245" class="code">  context: Context,</text>
  <text x="80" y="260" class="code">  attrs: AttributeSet? = null</text>
  <text x="70" y="275" class="code">) : FrameLayout(context, attrs) {</text>
  <text x="80" y="295" class="comment">// 目标点ImageView</text>
  <text x="80" y="310" class="code">private lateinit var targetImageView: ImageView</text>
  <text x="80" y="330" class="comment">// 开始评估方法</text>
  <text x="80" y="345" class="code">fun startEvaluating(pointF: PointF) {</text>
  <text x="90" y="360" class="code">  targetImageView.x = pointF.x</text>
  <text x="90" y="375" class="code">  targetImageView.y = pointF.y</text>
  <text x="80" y="390" class="code">}</text>

  <!-- 结果View继承 -->
  <rect x="500" y="180" width="400" height="200" class="result-box" rx="8"/>
  <text x="700" y="205" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultView</text>
  <text x="520" y="230" class="code">class SaccadeAbilityEvaluateResultView(</text>
  <text x="530" y="245" class="code">  context: Context,</text>
  <text x="530" y="260" class="code">  attrs: AttributeSet? = null</text>
  <text x="520" y="275" class="code">) : FrameLayout(context, attrs) {</text>
  <text x="530" y="295" class="comment">// 眼动轨迹数据</text>
  <text x="530" y="310" class="code">private var gazePoints = mutableListOf&lt;GazePoint&gt;()</text>
  <text x="530" y="330" class="comment">// Canvas绘制方法</text>
  <text x="530" y="345" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="540" y="360" class="code">  super.onDraw(canvas)</text>
  <text x="540" y="375" class="code">  drawGazeTrajectory(canvas)</text>
  <text x="530" y="390" class="code">}</text>

  <!-- 其他评估View -->
  <rect x="950" y="180" width="400" height="200" class="evaluating-box" rx="8"/>
  <text x="1150" y="205" text-anchor="middle" class="subtitle">其他评估模块自定义View</text>
  <text x="970" y="230" class="text">🔍 <tspan class="code">GazeStabilityEvaluatingView</tspan></text>
  <text x="980" y="245" class="text">注视稳定性评估交互</text>
  <text x="970" y="265" class="text">👁️ <tspan class="code">FollowAbilityEvaluatingView</tspan></text>
  <text x="980" y="280" class="text">追随能力评估交互</text>
  <text x="970" y="300" class="text">📊 <tspan class="code">ROIDetectionEvaluatingView</tspan></text>
  <text x="980" y="315" class="text">ROI检测评估交互</text>
  <text x="970" y="335" class="text">🎨 <tspan class="code">对应的ResultView</tspan></text>
  <text x="980" y="350" class="text">各自的Canvas绘制实现</text>
  <text x="980" y="365" class="text">继承FrameLayout，重写onDraw()</text>

  <!-- Canvas绘制详解 -->
  <rect x="50" y="420" width="1300" height="180" class="canvas-box" rx="10"/>
  <text x="700" y="445" text-anchor="middle" class="subtitle">Canvas绘制核心实现</text>

  <!-- onDraw方法 -->
  <rect x="70" y="460" width="300" height="120" class="canvas-box" rx="5"/>
  <text x="220" y="480" text-anchor="middle" class="subtitle">onDraw()核心方法</text>
  <text x="80" y="500" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="90" y="515" class="code">  super.onDraw(canvas)</text>
  <text x="90" y="530" class="comment">  // 绘制眼动轨迹路径</text>
  <text x="90" y="545" class="code">  canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="90" y="560" class="comment">  // 绘制注视点圆圈</text>
  <text x="90" y="575" class="code">  drawGazePoints(canvas)</text>
  <text x="80" y="590" class="code">}</text>

  <!-- 轨迹绘制 -->
  <rect x="390" y="460" width="300" height="120" class="canvas-box" rx="5"/>
  <text x="540" y="480" text-anchor="middle" class="subtitle">轨迹路径绘制</text>
  <text x="400" y="500" class="code">private fun drawGazeTrajectory(canvas: Canvas) {</text>
  <text x="410" y="515" class="code">  gazePath.reset()</text>
  <text x="410" y="530" class="code">  gazePoints.forEachIndexed { index, point -></text>
  <text x="420" y="545" class="code">    val x = point.x * screenWidth</text>
  <text x="420" y="560" class="code">    val y = point.y * screenHeight</text>
  <text x="420" y="575" class="code">    if (index == 0) gazePath.moveTo(x, y)</text>
  <text x="420" y="590" class="code">    else gazePath.lineTo(x, y) }</text>

  <!-- 点圆绘制 -->
  <rect x="710" y="460" width="300" height="120" class="canvas-box" rx="5"/>
  <text x="860" y="480" text-anchor="middle" class="subtitle">注视点圆圈绘制</text>
  <text x="720" y="500" class="code">private fun drawGazePoints(canvas: Canvas) {</text>
  <text x="730" y="515" class="code">  gazePoints.forEach { point -></text>
  <text x="740" y="530" class="code">    val circleX = point.x * screenWidth</text>
  <text x="740" y="545" class="code">    val circleY = point.y * screenHeight</text>
  <text x="740" y="560" class="code">    canvas.drawCircle(circleX, circleY,</text>
  <text x="750" y="575" class="code">      gazePointRadius, gazePointPaint)</text>
  <text x="730" y="590" class="code">  }</text>

  <!-- 坐标转换 -->
  <rect x="1030" y="460" width="300" height="120" class="canvas-box" rx="5"/>
  <text x="1180" y="480" text-anchor="middle" class="subtitle">坐标转换处理</text>
  <text x="1040" y="500" class="code">// 相对坐标转屏幕坐标</text>
  <text x="1040" y="515" class="code">private fun convertToScreenCoords(</text>
  <text x="1050" y="530" class="code">  relativeX: Float, relativeY: Float</text>
  <text x="1040" y="545" class="code">): Pair&lt;Float, Float&gt; {</text>
  <text x="1050" y="560" class="code">  val screenX = relativeX * screenWidth</text>
  <text x="1050" y="575" class="code">  val screenY = relativeY * screenHeight</text>
  <text x="1050" y="590" class="code">  return Pair(screenX, screenY)</text>

  <!-- Paint样式配置 -->
  <rect x="50" y="620" width="1300" height="140" class="paint-box" rx="10"/>
  <text x="700" y="645" text-anchor="middle" class="subtitle">Paint样式配置</text>

  <!-- 轨迹Paint -->
  <rect x="70" y="660" width="240" height="80" class="paint-box" rx="5"/>
  <text x="190" y="680" text-anchor="middle" class="subtitle">轨迹路径Paint</text>
  <text x="80" y="700" class="code">gazePathPaint.apply {</text>
  <text x="90" y="715" class="code">  color = Color.RED</text>
  <text x="90" y="730" class="code">  strokeWidth = 3f</text>
  <text x="90" y="745" class="code">  style = Paint.Style.STROKE</text>

  <!-- 点圆Paint -->
  <rect x="330" y="660" width="240" height="80" class="paint-box" rx="5"/>
  <text x="450" y="680" text-anchor="middle" class="subtitle">注视点Paint</text>
  <text x="340" y="700" class="code">gazePointPaint.apply {</text>
  <text x="350" y="715" class="code">  color = Color.BLUE</text>
  <text x="350" y="730" class="code">  style = Paint.Style.FILL</text>
  <text x="350" y="745" class="code">  isAntiAlias = true</text>

  <!-- 目标点Paint -->
  <rect x="590" y="660" width="240" height="80" class="paint-box" rx="5"/>
  <text x="710" y="680" text-anchor="middle" class="subtitle">目标点Paint</text>
  <text x="600" y="700" class="code">targetPointPaint.apply {</text>
  <text x="610" y="715" class="code">  color = Color.GREEN</text>
  <text x="610" y="730" class="code">  strokeWidth = 2f</text>
  <text x="610" y="745" class="code">  style = Paint.Style.STROKE</text>

  <!-- 文字Paint -->
  <rect x="850" y="660" width="240" height="80" class="paint-box" rx="5"/>
  <text x="970" y="680" text-anchor="middle" class="subtitle">文字标注Paint</text>
  <text x="860" y="700" class="code">textPaint.apply {</text>
  <text x="870" y="715" class="code">  color = Color.BLACK</text>
  <text x="870" y="730" class="code">  textSize = 24f</text>
  <text x="870" y="745" class="code">  typeface = Typeface.DEFAULT</text>

  <!-- 背景Paint -->
  <rect x="1110" y="660" width="240" height="80" class="paint-box" rx="5"/>
  <text x="1230" y="680" text-anchor="middle" class="subtitle">背景网格Paint</text>
  <text x="1120" y="700" class="code">gridPaint.apply {</text>
  <text x="1130" y="715" class="code">  color = Color.LTGRAY</text>
  <text x="1130" y="730" class="code">  strokeWidth = 1f</text>
  <text x="1130" y="745" class="code">  pathEffect = DashPathEffect</text>

  <!-- 核心方法详解 -->
  <rect x="50" y="780" width="1300" height="160" class="method-box" rx="10"/>
  <text x="700" y="805" text-anchor="middle" class="subtitle">核心方法实现</text>

  <!-- 数据更新方法 -->
  <rect x="70" y="820" width="300" height="100" class="method-box" rx="5"/>
  <text x="220" y="840" text-anchor="middle" class="subtitle">数据更新方法</text>
  <text x="80" y="860" class="code">fun drawResult(points: List&lt;GazePoint&gt;) {</text>
  <text x="90" y="875" class="code">  gazePoints.clear()</text>
  <text x="90" y="890" class="code">  gazePoints.addAll(points.filter { it.checkValid() })</text>
  <text x="90" y="905" class="code">  invalidate() // 触发重绘</text>
  <text x="80" y="920" class="code">}</text>

  <!-- 图片生成方法 -->
  <rect x="390" y="820" width="300" height="100" class="method-box" rx="5"/>
  <text x="540" y="840" text-anchor="middle" class="subtitle">图片生成方法</text>
  <text x="400" y="860" class="code">fun generateResultBitmap(): Bitmap {</text>
  <text x="410" y="875" class="code">  val bitmap = Bitmap.createBitmap(</text>
  <text x="420" y="890" class="code">    width, height, Bitmap.Config.ARGB_8888)</text>
  <text x="410" y="905" class="code">  val canvas = Canvas(bitmap)</text>
  <text x="410" y="920" class="code">  draw(canvas) // 绘制到bitmap</text>

  <!-- 交互处理方法 -->
  <rect x="710" y="820" width="300" height="100" class="method-box" rx="5"/>
  <text x="860" y="840" text-anchor="middle" class="subtitle">交互处理方法</text>
  <text x="720" y="860" class="code">override fun onTouchEvent(event: MotionEvent): Boolean {</text>
  <text x="730" y="875" class="code">  when (event.action) {</text>
  <text x="740" y="890" class="code">    MotionEvent.ACTION_DOWN -> {</text>
  <text x="750" y="905" class="code">      // 处理触摸事件</text>
  <text x="740" y="920" class="code">    }</text>

  <!-- 生命周期方法 -->
  <rect x="1030" y="820" width="300" height="100" class="method-box" rx="5"/>
  <text x="1180" y="840" text-anchor="middle" class="subtitle">生命周期方法</text>
  <text x="1040" y="860" class="code">override fun onSizeChanged(</text>
  <text x="1050" y="875" class="code">  w: Int, h: Int, oldw: Int, oldh: Int) {</text>
  <text x="1050" y="890" class="code">  super.onSizeChanged(w, h, oldw, oldh)</text>
  <text x="1050" y="905" class="code">  screenWidth = w.toFloat()</text>
  <text x="1050" y="920" class="code">  screenHeight = h.toFloat()</text>

  <!-- 继承关系箭头 -->
  <line x1="650" y1="130" x2="250" y2="180" class="inheritance-arrow"/>
  <line x1="750" y1="130" x2="700" y2="180" class="inheritance-arrow"/>
  <line x1="750" y1="130" x2="1150" y2="180" class="inheritance-arrow"/>

  <!-- 方法调用箭头 -->
  <line x1="700" y1="380" x2="700" y2="420" class="method-arrow"/>
  <line x1="700" y1="600" x2="700" y2="620" class="canvas-arrow"/>
  <line x1="700" y1="760" x2="700" y2="780" class="method-arrow"/>

  <!-- 特点总结 -->
  <rect x="50" y="960" width="1300" height="120" class="evaluating-box" rx="10"/>
  <text x="700" y="985" text-anchor="middle" class="subtitle">自定义View实现特点总结</text>
  
  <text x="70" y="1010" class="text">🏗️ <tspan class="subtitle">继承FrameLayout</tspan>: 利用FrameLayout的容器特性，可以叠加显示多个子View，支持复杂的UI组合</text>
  <text x="70" y="1030" class="text">🎨 <tspan class="subtitle">Canvas绘制核心</tspan>: 重写onDraw()方法，使用Canvas API绘制复杂的眼动轨迹、注视点、目标点等图形元素</text>
  <text x="70" y="1050" class="text">📐 <tspan class="subtitle">坐标转换处理</tspan>: 将相对坐标(0-1)转换为屏幕坐标，确保在不同屏幕尺寸下的正确显示</text>
  <text x="70" y="1070" class="text">⚡ <tspan class="subtitle">性能优化</tspan>: 使用invalidate()触发重绘，Paint对象复用，避免在onDraw()中创建对象，确保流畅的绘制性能</text>

</svg>
