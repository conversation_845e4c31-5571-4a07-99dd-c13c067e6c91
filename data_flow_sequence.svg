<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .actor-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .message-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 1; }
      .success-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 1; }
      .error-box { fill: #ffebee; stroke: #f44336; stroke-width: 1; }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#messagearrow); }
      .return-arrow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#returnarrow); }
      .error-arrow { stroke: #f44336; stroke-width: 2; fill: none; marker-end: url(#errorarrow); }
    </style>
    <marker id="messagearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
    <marker id="returnarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
    <marker id="errorarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f44336" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">眼球运动评估数据流转时序图</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">扫视能力评估完整流程</text>

  <!-- 参与者 -->
  <rect x="50" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="110" y="95" text-anchor="middle" class="subtitle">用户</text>

  <rect x="200" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="260" y="95" text-anchor="middle" class="subtitle">Fragment</text>

  <rect x="350" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="410" y="95" text-anchor="middle" class="subtitle">Service</text>

  <rect x="500" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="560" y="95" text-anchor="middle" class="subtitle">ViewModel</text>

  <rect x="650" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="710" y="95" text-anchor="middle" class="subtitle">Repository</text>

  <rect x="800" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="860" y="95" text-anchor="middle" class="subtitle">API Service</text>

  <rect x="950" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1010" y="95" text-anchor="middle" class="subtitle">服务器</text>

  <rect x="1100" y="70" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1160" y="95" text-anchor="middle" class="subtitle">UI</text>

  <!-- 生命线 -->
  <line x1="110" y1="110" x2="110" y2="900" class="lifeline"/>
  <line x1="260" y1="110" x2="260" y2="900" class="lifeline"/>
  <line x1="410" y1="110" x2="410" y2="900" class="lifeline"/>
  <line x1="560" y1="110" x2="560" y2="900" class="lifeline"/>
  <line x1="710" y1="110" x2="710" y2="900" class="lifeline"/>
  <line x1="860" y1="110" x2="860" y2="900" class="lifeline"/>
  <line x1="1010" y1="110" x2="1010" y2="900" class="lifeline"/>
  <line x1="1160" y1="110" x2="1160" y2="900" class="lifeline"/>

  <!-- 消息序列 -->
  <!-- 1. 用户点击开始评估 -->
  <line x1="110" y1="140" x2="260" y2="140" class="message-arrow"/>
  <rect x="130" y="125" width="110" height="30" class="message-box" rx="3"/>
  <text x="185" y="135" text-anchor="middle" class="text">1. 点击开始评估</text>
  <text x="185" y="150" text-anchor="middle" class="code">onClick()</text>

  <!-- 2. Fragment启动Service -->
  <line x1="260" y1="170" x2="410" y2="170" class="message-arrow"/>
  <rect x="280" y="155" width="110" height="30" class="message-box" rx="3"/>
  <text x="335" y="165" text-anchor="middle" class="text">2. 启动眼动追踪</text>
  <text x="335" y="180" text-anchor="middle" class="code">MSG_START_TRACK</text>

  <!-- 3. Service采集数据 -->
  <rect x="420" y="200" width="140" height="30" class="message-box" rx="3"/>
  <text x="490" y="210" text-anchor="middle" class="text">3. 采集眼动数据</text>
  <text x="490" y="225" text-anchor="middle" class="code">collectGazeData()</text>

  <!-- 4. Service返回数据 -->
  <line x1="410" y1="250" x2="260" y2="250" class="return-arrow"/>
  <rect x="280" y="235" width="110" height="30" class="message-box" rx="3"/>
  <text x="335" y="245" text-anchor="middle" class="text">4. 返回轨迹数据</text>
  <text x="335" y="260" text-anchor="middle" class="code">GazeTrajectory</text>

  <!-- 5. Fragment传递数据 -->
  <line x1="260" y1="280" x2="1160" y2="280" class="message-arrow"/>
  <rect x="650" y="265" width="120" height="30" class="message-box" rx="3"/>
  <text x="710" y="275" text-anchor="middle" class="text">5. 传递评估结果</text>
  <text x="710" y="290" text-anchor="middle" class="code">LiveEventBus.post()</text>

  <!-- 6. UI接收数据并初始化ViewModel -->
  <line x1="1160" y1="310" x2="560" y2="310" class="message-arrow"/>
  <rect x="800" y="295" width="120" height="30" class="message-box" rx="3"/>
  <text x="860" y="305" text-anchor="middle" class="text">6. 初始化ViewModel</text>
  <text x="860" y="320" text-anchor="middle" class="code">ViewModelProvider</text>

  <!-- 7. ViewModel上传图片 -->
  <line x1="560" y1="340" x2="710" y2="340" class="message-arrow"/>
  <rect x="580" y="325" width="110" height="30" class="message-box" rx="3"/>
  <text x="635" y="335" text-anchor="middle" class="text">7. 上传结果图片</text>
  <text x="635" y="350" text-anchor="middle" class="code">uploadImage()</text>

  <!-- 8. Repository调用API -->
  <line x1="710" y1="370" x2="860" y2="370" class="message-arrow"/>
  <rect x="730" y="355" width="110" height="30" class="message-box" rx="3"/>
  <text x="785" y="365" text-anchor="middle" class="text">8. 调用上传API</text>
  <text x="785" y="380" text-anchor="middle" class="code">POST /upload</text>

  <!-- 9. API发送到服务器 -->
  <line x1="860" y1="400" x2="1010" y2="400" class="message-arrow"/>
  <rect x="880" y="385" width="110" height="30" class="message-box" rx="3"/>
  <text x="935" y="395" text-anchor="middle" class="text">9. HTTP请求</text>
  <text x="935" y="410" text-anchor="middle" class="code">multipart/form-data</text>

  <!-- 10. 服务器处理并返回 -->
  <line x1="1010" y1="430" x2="860" y2="430" class="return-arrow"/>
  <rect x="880" y="415" width="110" height="30" class="success-box" rx="3"/>
  <text x="935" y="425" text-anchor="middle" class="text">10. 返回图片URL</text>
  <text x="935" y="440" text-anchor="middle" class="code">HTTP 200 + URL</text>

  <!-- 11. API返回结果 -->
  <line x1="860" y1="460" x2="710" y2="460" class="return-arrow"/>
  <rect x="730" y="445" width="110" height="30" class="success-box" rx="3"/>
  <text x="785" y="455" text-anchor="middle" class="text">11. 上传成功</text>
  <text x="785" y="470" text-anchor="middle" class="code">FileUploadResponse</text>

  <!-- 12. Repository返回ViewModel -->
  <line x1="710" y1="490" x2="560" y2="490" class="return-arrow"/>
  <rect x="580" y="475" width="110" height="30" class="success-box" rx="3"/>
  <text x="635" y="485" text-anchor="middle" class="text">12. 图片上传完成</text>
  <text x="635" y="500" text-anchor="middle" class="code">collectResponse</text>

  <!-- 13. ViewModel更新LiveData -->
  <rect x="470" y="520" width="180" height="30" class="success-box" rx="3"/>
  <text x="560" y="530" text-anchor="middle" class="text">13. 更新LiveData状态</text>
  <text x="560" y="545" text-anchor="middle" class="code">uploadImageResultLiveData.postValue()</text>

  <!-- 14. ViewModel提交评估数据 -->
  <line x1="560" y1="570" x2="710" y2="570" class="message-arrow"/>
  <rect x="580" y="555" width="110" height="30" class="message-box" rx="3"/>
  <text x="635" y="565" text-anchor="middle" class="text">14. 提交评估数据</text>
  <text x="635" y="580" text-anchor="middle" class="code">submitResult()</text>

  <!-- 15. Repository调用提交API -->
  <line x1="710" y1="600" x2="860" y2="600" class="message-arrow"/>
  <rect x="730" y="585" width="110" height="30" class="message-box" rx="3"/>
  <text x="785" y="595" text-anchor="middle" class="text">15. 调用提交API</text>
  <text x="785" y="610" text-anchor="middle" class="code">POST /saccade</text>

  <!-- 16. API发送到服务器 -->
  <line x1="860" y1="630" x2="1010" y2="630" class="message-arrow"/>
  <rect x="880" y="615" width="110" height="30" class="message-box" rx="3"/>
  <text x="935" y="625" text-anchor="middle" class="text">16. 提交数据</text>
  <text x="935" y="640" text-anchor="middle" class="code">JSON数据</text>

  <!-- 17. 服务器保存并返回 -->
  <line x1="1010" y1="660" x2="860" y2="660" class="return-arrow"/>
  <rect x="880" y="645" width="110" height="30" class="success-box" rx="3"/>
  <text x="935" y="655" text-anchor="middle" class="text">17. 保存成功</text>
  <text x="935" y="670" text-anchor="middle" class="code">HTTP 200 + ID</text>

  <!-- 18. 逐层返回成功结果 -->
  <line x1="860" y1="690" x2="710" y2="690" class="return-arrow"/>
  <line x1="710" y1="720" x2="560" y2="720" class="return-arrow"/>
  <line x1="560" y1="750" x2="1160" y2="750" class="return-arrow"/>

  <rect x="730" y="675" width="110" height="30" class="success-box" rx="3"/>
  <text x="785" y="685" text-anchor="middle" class="text">18. 提交成功</text>
  <text x="785" y="700" text-anchor="middle" class="code">SaccadeAbilityAdd</text>

  <rect x="580" y="705" width="110" height="30" class="success-box" rx="3"/>
  <text x="635" y="715" text-anchor="middle" class="text">19. LiveData通知</text>
  <text x="635" y="730" text-anchor="middle" class="code">submitResultLiveData</text>

  <rect x="800" y="735" width="120" height="30" class="success-box" rx="3"/>
  <text x="860" y="745" text-anchor="middle" class="text">20. Observer回调</text>
  <text x="860" y="760" text-anchor="middle" class="code">onChanged(result)</text>

  <!-- 21. UI更新 -->
  <rect x="1070" y="780" width="180" height="30" class="success-box" rx="3"/>
  <text x="1160" y="790" text-anchor="middle" class="text">21. 显示成功提示</text>
  <text x="1160" y="805" text-anchor="middle" class="code">Toast.makeText("提交成功")</text>

  <!-- 错误处理示例 -->
  <rect x="50" y="850" width="1300" height="120" class="error-box" rx="8"/>
  <text x="700" y="875" text-anchor="middle" class="subtitle">错误处理机制</text>
  
  <text x="70" y="900" class="text">🚨 <tspan class="subtitle">网络异常</tspan>: collectResponse.onFailed → LiveData.postValue(null) → Observer显示错误Toast</text>
  <text x="70" y="920" class="text">⚠️ <tspan class="subtitle">数据异常</tspan>: collectResponse.onDataEmpty → 记录日志 → 用户友好提示</text>
  <text x="70" y="940" class="text">🔄 <tspan class="subtitle">重试机制</tspan>: Repository层实现指数退避重试 → 最大重试3次 → 最终失败则通知用户</text>
  <text x="70" y="960" class="text">🛡️ <tspan class="subtitle">生命周期保护</tspan>: viewModelScope自动取消 → LiveData生命周期感知 → 避免内存泄漏</text>

</svg>
