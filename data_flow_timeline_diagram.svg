<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .time-text { font-family: Arial, sans-serif; font-size: 11px; fill: #e74c3c; font-weight: bold; }
      .actor-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .message-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .drop-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
      .process-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead-orange); }
      .result-arrow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead-green); }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 5,5; }
      .activation-box { fill: #f39c12; fill-opacity: 0.3; stroke: #e67e22; stroke-width: 1; }
      .note-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 1; }
    </style>
    
    <!-- Arrow markers -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1400" height="900" fill="#ecf0f1"/>
  
  <!-- Title -->
  <text x="700" y="40" text-anchor="middle" class="title">眼动追踪数据流时序图 - 背压策略演示</text>
  
  <!-- Actors -->
  <!-- Camera -->
  <rect x="100" y="80" width="120" height="60" rx="10" class="actor-box"/>
  <text x="160" y="105" text-anchor="middle" class="subtitle" fill="white">📷 相机</text>
  <text x="160" y="125" text-anchor="middle" class="text" fill="white">30fps</text>
  
  <!-- Queue -->
  <rect x="350" y="80" width="150" height="60" rx="10" class="actor-box"/>
  <text x="425" y="105" text-anchor="middle" class="subtitle" fill="white">📦 处理队列</text>
  <text x="425" y="125" text-anchor="middle" class="text" fill="white">背压策略</text>
  
  <!-- AI Algorithm -->
  <rect x="650" y="80" width="150" height="60" rx="10" class="actor-box"/>
  <text x="725" y="105" text-anchor="middle" class="subtitle" fill="white">🤖 AI算法</text>
  <text x="725" y="125" text-anchor="middle" class="text" fill="white">眼动检测</text>
  
  <!-- UI Interface -->
  <rect x="950" y="80" width="120" height="60" rx="10" class="actor-box"/>
  <text x="1010" y="105" text-anchor="middle" class="subtitle" fill="white">🖥️ UI界面</text>
  <text x="1010" y="125" text-anchor="middle" class="text" fill="white">实时显示</text>
  
  <!-- Lifelines -->
  <line x1="160" y1="140" x2="160" y2="800" class="lifeline"/>
  <line x1="425" y1="140" x2="425" y2="800" class="lifeline"/>
  <line x1="725" y1="140" x2="725" y2="800" class="lifeline"/>
  <line x1="1010" y1="140" x2="1010" y2="800" class="lifeline"/>
  
  <!-- Time markers -->
  <text x="50" y="200" class="time-text">t=0ms</text>
  <text x="50" y="250" class="time-text">t=33ms</text>
  <text x="50" y="300" class="time-text">t=66ms</text>
  <text x="50" y="400" class="time-text">t=99ms</text>
  <text x="50" y="500" class="time-text">t=132ms</text>
  
  <!-- Frame 1 (t=0ms) -->
  <line x1="160" y1="190" x2="425" y2="190" class="message-arrow"/>
  <text x="290" y="185" text-anchor="middle" class="small-text">帧1 (4048x3040)</text>
  <circle cx="440" cy="190" r="8" fill="#3498db"/>
  <text x="460" y="195" class="small-text">帧1入队</text>
  
  <!-- Frame 2 (t=33ms) -->
  <line x1="160" y1="240" x2="425" y2="240" class="message-arrow"/>
  <text x="290" y="235" text-anchor="middle" class="small-text">帧2 (4048x3040)</text>
  <circle cx="440" cy="240" r="8" fill="#3498db"/>
  <text x="460" y="245" class="small-text">帧2入队</text>
  
  <!-- Frame 3 (t=66ms) -->
  <line x1="160" y1="290" x2="425" y2="290" class="message-arrow"/>
  <text x="290" y="285" text-anchor="middle" class="small-text">帧3 (4048x3040)</text>
  
  <!-- Backpressure Strategy Action -->
  <rect x="350" y="310" width="150" height="60" class="note-box"/>
  <text x="425" y="330" text-anchor="middle" class="small-text" fill="#856404">KEEP_ONLY_LATEST</text>
  <text x="425" y="345" text-anchor="middle" class="small-text" fill="#856404">策略触发:</text>
  <text x="425" y="360" text-anchor="middle" class="small-text" fill="#856404">丢弃帧1, 帧2</text>
  
  <!-- Drop old frames -->
  <line x1="410" y1="190" x2="380" y2="220" class="drop-arrow"/>
  <line x1="410" y1="240" x2="380" y2="260" class="drop-arrow"/>
  <text x="320" y="210" class="small-text" fill="#e74c3c">❌ 丢弃</text>
  <text x="320" y="260" class="small-text" fill="#e74c3c">❌ 丢弃</text>
  
  <!-- Process latest frame -->
  <circle cx="440" cy="290" r="8" fill="#27ae60"/>
  <text x="460" y="295" class="small-text" fill="#27ae60">保留最新帧</text>
  
  <!-- AI Processing -->
  <rect x="700" y="380" width="50" height="80" class="activation-box"/>
  <line x1="425" y1="390" x2="725" y2="390" class="process-arrow"/>
  <text x="575" y="385" text-anchor="middle" class="small-text">处理帧3</text>
  
  <!-- Processing note -->
  <rect x="750" y="400" width="120" height="40" class="note-box"/>
  <text x="810" y="415" text-anchor="middle" class="small-text" fill="#856404">AI算法处理</text>
  <text x="810" y="430" text-anchor="middle" class="small-text" fill="#856404">耗时: ~50ms</text>
  
  <!-- Result -->
  <line x1="725" y1="470" x2="1010" y2="470" class="result-arrow"/>
  <text x="865" y="465" text-anchor="middle" class="small-text">眼动坐标 (x, y)</text>
  
  <!-- Frame 4 (t=99ms) -->
  <line x1="160" y1="490" x2="425" y2="490" class="message-arrow"/>
  <text x="290" y="485" text-anchor="middle" class="small-text">帧4 (4048x3040)</text>
  
  <!-- Process Frame 4 -->
  <rect x="700" y="520" width="50" height="80" class="activation-box"/>
  <line x1="425" y1="530" x2="725" y2="530" class="process-arrow"/>
  <text x="575" y="525" text-anchor="middle" class="small-text">处理帧4</text>
  
  <!-- Result 2 -->
  <line x1="725" y1="610" x2="1010" y2="610" class="result-arrow"/>
  <text x="865" y="605" text-anchor="middle" class="small-text">眼动坐标 (x, y)</text>
  
  <!-- Performance Metrics -->
  <rect x="100" y="650" width="1200" height="120" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  <text x="120" y="675" class="subtitle">性能指标对比：</text>
  
  <!-- Metrics Table -->
  <rect x="120" y="690" width="280" height="70" fill="#d5f4e6" stroke="#27ae60" stroke-width="1"/>
  <text x="130" y="710" class="text" fill="#27ae60">✅ KEEP_ONLY_LATEST</text>
  <text x="130" y="730" class="small-text">• 内存使用: 低 (1-2帧)</text>
  <text x="130" y="745" class="small-text">• 处理延迟: 低 (~50ms)</text>
  <text x="130" y="760" class="small-text">• 实时性: 高</text>
  
  <rect x="420" y="690" width="280" height="70" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="430" y="710" class="text" fill="#e74c3c">❌ BLOCK_PRODUCER</text>
  <text x="430" y="730" class="small-text">• 内存使用: 高 (积压多帧)</text>
  <text x="430" y="745" class="small-text">• 处理延迟: 高 (>200ms)</text>
  <text x="430" y="760" class="small-text">• 实时性: 低</text>
  
  <rect x="720" y="690" width="280" height="70" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
  <text x="730" y="710" class="text" fill="#856404">📊 实际数据</text>
  <text x="730" y="730" class="small-text">• 相机帧率: 30fps (33ms/帧)</text>
  <text x="730" y="745" class="small-text">• AI处理时间: 50ms/帧</text>
  <text x="730" y="760" class="small-text">• 队列容量: 1帧 (最新)</text>
  
  <rect x="1020" y="690" width="260" height="70" fill="#e8f4fd" stroke="#3498db" stroke-width="1"/>
  <text x="1030" y="710" class="text" fill="#2980b9">🎯 应用场景</text>
  <text x="1030" y="730" class="small-text">• 眼动追踪</text>
  <text x="1030" y="745" class="small-text">• 实时视频分析</text>
  <text x="1030" y="760" class="small-text">• 姿态检测</text>
  
  <!-- Key Benefits -->
  <text x="120" y="800" class="subtitle">关键优势：</text>
  <text x="120" y="820" class="text">1. 防止内存溢出 - 自动丢弃积压的旧帧</text>
  <text x="120" y="840" class="text">2. 保证实时性 - 始终处理最新的图像数据</text>
  <text x="120" y="860" class="text">3. 提升用户体验 - 减少延迟，响应更及时</text>
  <text x="120" y="880" class="text">4. 系统稳定性 - 避免因积压导致的应用崩溃</text>
  
</svg>
