<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .class-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-text { font-family: "Consolas", "Monaco", monospace; font-size: 11px; fill: #2c3e50; }
      .field-text { font-family: "Consolas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .interface-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; font-style: italic; }
      
      .ui-class { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .vm-class { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .manager-class { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .native-class { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .gpu-class { fill: #fce4ec; stroke: #c2185b; stroke-width: 2; }
      .bean-class { fill: #f1f8e9; stroke: #689f38; stroke-width: 2; }
      .service-class { fill: #e0f2f1; stroke: #00796b; stroke-width: 2; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .composition-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#diamond); }
      .inheritance-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#triangle); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="diamond" markerWidth="12" markerHeight="8" refX="6" refY="4" orient="auto">
      <polygon points="0 4, 6 0, 12 4, 6 8" fill="white" stroke="#2c3e50" stroke-width="1" />
    </marker>
    <marker id="triangle" markerWidth="12" markerHeight="10" refX="12" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="white" stroke="#2c3e50" stroke-width="1" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">数字遮盖疗法系统类图</text>

  <!-- UI层 -->
  <!-- MaskTherapyFragment -->
  <rect x="50" y="60" width="320" height="200" class="ui-class" rx="5"/>
  <text x="210" y="85" text-anchor="middle" class="class-title">MaskTherapyFragment</text>
  <line x1="60" y1="95" x2="360" y2="95" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="60" y="115" class="field-text">- maskVM: MaskViewModel</text>
  <text x="60" y="130" class="field-text">- treatmentVM: TreatmentViewModel</text>
  <text x="60" y="145" class="field-text">- timeProgress: TimeProgress</text>
  <text x="60" y="160" class="field-text">- switchMaskTherapy: Switch</text>
  
  <line x1="60" y1="170" x2="360" y2="170" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="60" y="190" class="method-text">+ newInstance(moduleName, isFull): MaskTherapyFragment</text>
  <text x="60" y="205" class="method-text">+ switchMaskTherapy(state): void</text>
  <text x="60" y="220" class="method-text">- updateEyePosition(): void</text>
  <text x="60" y="235" class="method-text">- updatePlannedDuration(): void</text>
  <text x="60" y="250" class="method-text">- updateTreatmentDuration(): void</text>

  <!-- ViewModel层 -->
  <!-- MaskViewModel -->
  <rect x="420" y="60" width="300" height="200" class="vm-class" rx="5"/>
  <text x="570" y="85" text-anchor="middle" class="class-title">MaskViewModel</text>
  <line x1="430" y1="95" x2="710" y2="95" stroke="#7b1fa2" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="430" y="115" class="field-text">- occlusionTherapyLiveData: MutableLiveData</text>
  <text x="430" y="130" class="field-text">- mtStateLiveData: MutableLiveData</text>
  <text x="430" y="145" class="field-text">+ occlusionTherapy: CureInfo?</text>
  
  <line x1="430" y1="155" x2="710" y2="155" stroke="#7b1fa2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="430" y="175" class="method-text">+ getTodayOcclusionTherapy(): void</text>
  <text x="430" y="190" class="method-text">+ setOcclusionTherapy(cureInfo): void</text>
  <text x="430" y="205" class="method-text">+ setMTState(state): void</text>
  <text x="430" y="220" class="method-text">+ getPlannedDuration(): Int</text>
  <text x="430" y="235" class="method-text">+ getTreatmentDuration(): Int</text>
  <text x="430" y="250" class="method-text">+ getEyePosition(): String</text>

  <!-- 管理层 -->
  <!-- MaskManager -->
  <rect x="770" y="60" width="300" height="200" class="manager-class" rx="5"/>
  <text x="920" y="85" text-anchor="middle" class="class-title">MaskManager</text>
  <line x1="780" y1="95" x2="1060" y2="95" stroke="#388e3c" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="780" y="115" class="method-text">+ setCoverChannel(channel): void</text>
  <text x="780" y="130" class="method-text">+ getCoverChannel(): CoverChannel</text>
  <text x="780" y="145" class="method-text">+ setCoverMode(mode): void</text>
  <text x="780" y="160" class="method-text">+ getCoverMode(): CoverMode</text>
  <text x="780" y="175" class="method-text">+ setCoverArea(area): void</text>
  <text x="780" y="190" class="method-text">+ getCoverArea(): Float</text>
  <text x="780" y="205" class="method-text">+ setCoverRange(range): void</text>
  <text x="780" y="220" class="method-text">+ getCoverRange(): CoverRange</text>
  <text x="780" y="235" class="method-text">+ setAmblyopicEye(position): void</text>
  <text x="780" y="250" class="method-text">+ getAmblyopicEye(): AmblyopicEye</text>

  <!-- AppliedManager -->
  <rect x="1120" y="60" width="300" height="200" class="manager-class" rx="5"/>
  <text x="1270" y="85" text-anchor="middle" class="class-title">AppliedManager</text>
  <line x1="1130" y1="95" x2="1410" y2="95" stroke="#388e3c" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="1130" y="115" class="field-text">- gazeApplied: GazeApplied</text>
  <text x="1130" y="130" class="field-text">- appliedMode: AtomicReference</text>
  <text x="1130" y="145" class="field-text">- isInitialized: AtomicBoolean</text>
  
  <line x1="1130" y1="155" x2="1410" y2="155" stroke="#388e3c" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="1130" y="175" class="method-text">+ startAppliedCure(): Int</text>
  <text x="1130" y="190" class="method-text">+ stopApplied(): Int</text>
  <text x="1130" y="205" class="method-text">+ setBlurParams(radius, sigma, mode, channel): void</text>
  <text x="1130" y="220" class="method-text">+ init(): void</text>
  <text x="1130" y="235" class="method-text">+ destroy(): void</text>
  <text x="1130" y="250" class="method-text">+ setGazeAppliedListener(listener): void</text>

  <!-- Native层 -->
  <!-- GazeApplied -->
  <rect x="50" y="300" width="300" height="180" class="native-class" rx="5"/>
  <text x="200" y="325" text-anchor="middle" class="class-title">GazeApplied</text>
  <line x1="60" y1="335" x2="340" y2="335" stroke="#f57c00" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="60" y="355" class="field-text">- nativeObj: AtomicLong</text>
  
  <line x1="60" y1="365" x2="340" y2="365" stroke="#f57c00" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="60" y="385" class="method-text">+ init(): Boolean</text>
  <text x="60" y="400" class="method-text">+ startApplied(appliedMode): Boolean</text>
  <text x="60" y="415" class="method-text">+ stopApplied(): Boolean</text>
  <text x="60" y="430" class="method-text">+ setBlurParams(radius, sigma, mode, channel): Boolean</text>
  <text x="60" y="445" class="method-text">+ release(): void</text>
  <text x="60" y="460" class="method-text">- nativeStartApplication(obj, mode): Boolean</text>
  <text x="60" y="475" class="method-text">- nativeSetBlurParams(obj, ...): Boolean</text>

  <!-- GazeApplication -->
  <rect x="390" y="300" width="300" height="180" class="native-class" rx="5"/>
  <text x="540" y="325" text-anchor="middle" class="class-title">GazeApplication (C++)</text>
  <line x1="400" y1="335" x2="680" y2="335" stroke="#f57c00" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="400" y="355" class="field-text">- pqblur_model: PqBlur</text>
  <text x="400" y="370" class="field-text">- app_mode: int</text>
  <text x="400" y="385" class="field-text">- app_runing: bool</text>
  
  <line x1="400" y1="395" x2="680" y2="395" stroke="#f57c00" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="400" y="415" class="method-text">+ start_application(mode): bool</text>
  <text x="400" y="430" class="method-text">+ stop_application(): bool</text>
  <text x="400" y="445" class="method-text">+ set_blur_params_func(radius, sigma, mode, ch): bool</text>
  <text x="400" y="460" class="method-text">+ collect_gaze(valid, x, y, dist, duration): bool</text>
  <text x="400" y="475" class="method-text">- onGazeAppliedModeChange(mode): void</text>

  <!-- GPU渲染层 -->
  <!-- PqBlur -->
  <rect x="730" y="300" width="320" height="180" class="gpu-class" rx="5"/>
  <text x="890" y="325" text-anchor="middle" class="class-title">PqBlur (C++)</text>
  <line x1="740" y1="335" x2="1040" y2="335" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="740" y="355" class="field-text">- pq_blr: IPQ_BLR*</text>
  <text x="740" y="370" class="field-text">- blur_enable_flag: bool</text>
  <text x="740" y="385" class="field-text">- macular_blur_radius: float</text>
  
  <line x1="740" y1="395" x2="1040" y2="395" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="740" y="415" class="method-text">+ draw_gaze_result_func(x, y, dist): void</text>
  <text x="740" y="430" class="method-text">+ set_red_blur_enable(valid): void</text>
  <text x="740" y="445" class="method-text">+ set_red_blur_radius(radius): bool</text>
  <text x="740" y="460" class="method-text">+ set_red_blur_sigma(sigma): bool</text>
  <text x="740" y="475" class="method-text">+ set_red_blur_mode(mode): bool</text>

  <!-- IPQ_BLR -->
  <rect x="1100" y="300" width="300" height="180" class="gpu-class" rx="5"/>
  <text x="1250" y="325" text-anchor="middle" class="class-title">IPQ_BLR (GPU引擎)</text>
  <line x1="1110" y1="335" x2="1390" y2="335" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="1110" y="355" class="method-text">+ SetRadius(radius): IPQ_BLR*</text>
  <text x="1110" y="370" class="method-text">+ SetChannel(channel): IPQ_BLR*</text>
  <text x="1110" y="385" class="method-text">+ SetPos(x, y): IPQ_BLR*</text>
  <text x="1110" y="400" class="method-text">+ SetGaussKer(w, h, sigma): IPQ_BLR*</text>
  <text x="1110" y="415" class="method-text">+ SetMode(mode): IPQ_BLR*</text>
  <text x="1110" y="430" class="method-text">+ Done(): void</text>
  <text x="1110" y="445" class="method-text">+ newPQ_BLR(): IPQ_BLR*</text>
  <text x="1110" y="460" class="method-text">+ deletePQ_BLR(blr): void</text>

  <!-- 数据模型层 -->
  <!-- OcclusionTherapy -->
  <rect x="50" y="520" width="300" height="180" class="bean-class" rx="5"/>
  <text x="200" y="545" text-anchor="middle" class="class-title">OcclusionTherapy</text>
  <line x1="60" y1="555" x2="340" y2="555" stroke="#689f38" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="60" y="575" class="field-text">+ blurChannel: Int?</text>
  <text x="60" y="590" class="field-text">+ blurMode: Int?</text>
  <text x="60" y="605" class="field-text">+ blurRadius: Float?</text>
  <text x="60" y="620" class="field-text">+ blurSigma: Float?</text>
  <text x="60" y="635" class="field-text">+ isFinishUp: Boolean?</text>
  <text x="60" y="650" class="field-text">+ plannedDuration: Int?</text>
  <text x="60" y="665" class="field-text">+ trainingDuration: Int?</text>
  <text x="60" y="680" class="field-text">+ position: String?</text>

  <!-- 枚举类 -->
  <!-- CoverChannel -->
  <rect x="390" y="520" width="200" height="120" class="bean-class" rx="5"/>
  <text x="490" y="545" text-anchor="middle" class="class-title">CoverChannel</text>
  <line x1="400" y1="555" x2="580" y2="555" stroke="#689f38" stroke-width="1"/>
  
  <text x="400" y="575" class="field-text">CHANNEL_R(1)</text>
  <text x="400" y="590" class="field-text">CHANNEL_G(2)</text>
  <text x="400" y="605" class="field-text">CHANNEL_B(4)</text>
  <text x="400" y="620" class="field-text">CHANNEL_RG(3)</text>
  <text x="400" y="635" class="field-text">CHANNEL_RGB(7)</text>

  <!-- CoverMode -->
  <rect x="630" y="520" width="200" height="120" class="bean-class" rx="5"/>
  <text x="730" y="545" text-anchor="middle" class="class-title">CoverMode</text>
  <line x1="640" y1="555" x2="820" y2="555" stroke="#689f38" stroke-width="1"/>
  
  <text x="640" y="575" class="field-text">INTERNAL_GAUSSIAN_BLUR(0)</text>
  <text x="640" y="590" class="field-text">EXTERNAL_GAUSSIAN_BLUR(1)</text>
  <text x="640" y="605" class="field-text">INTERNAL_BLACK(2)</text>
  <text x="640" y="620" class="field-text">EXTERNAL_BLACK(3)</text>

  <!-- AmblyopicEye -->
  <rect x="870" y="520" width="200" height="120" class="bean-class" rx="5"/>
  <text x="970" y="545" text-anchor="middle" class="class-title">AmblyopicEye</text>
  <line x1="880" y1="555" x2="1060" y2="555" stroke="#689f38" stroke-width="1"/>
  
  <text x="880" y="575" class="field-text">LEFT("left")</text>
  <text x="880" y="590" class="field-text">RIGHT("right")</text>

  <!-- 服务层 -->
  <!-- GazeTrackService -->
  <rect x="1120" y="520" width="300" height="180" class="service-class" rx="5"/>
  <text x="1270" y="545" text-anchor="middle" class="class-title">GazeTrackService</text>
  <line x1="1130" y1="555" x2="1410" y2="555" stroke="#00796b" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="1130" y="575" class="field-text">- mPlannedDuration: Int</text>
  <text x="1130" y="590" class="field-text">- mTreatmentDuration: Int</text>
  
  <line x1="1130" y1="600" x2="1410" y2="600" stroke="#00796b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="1130" y="620" class="method-text">- startAppliedCure(planned, treatment): void</text>
  <text x="1130" y="635" class="method-text">- stopAppliedCure(): void</text>
  <text x="1130" y="650" class="method-text">- startTreatmentTimer(): void</text>
  <text x="1130" y="665" class="method-text">- sendMessageToClient(msg): void</text>
  <text x="1130" y="680" class="method-text">+ onGazeTracking(result): void</text>

  <!-- 关系线 -->
  <!-- UI层关系 -->
  <line x1="370" y1="160" x2="420" y2="160" class="arrow"/>
  <text x="395" y="155" class="field-text">uses</text>

  <!-- ViewModel到Manager -->
  <line x1="720" y1="160" x2="770" y2="160" class="arrow"/>
  <text x="745" y="155" class="field-text">uses</text>

  <!-- Manager到AppliedManager -->
  <line x1="1070" y1="160" x2="1120" y2="160" class="arrow"/>
  <text x="1095" y="155" class="field-text">uses</text>

  <!-- AppliedManager到GazeApplied -->
  <line x1="1270" y1="260" x2="200" y2="300" class="composition-arrow"/>
  <text x="735" y="280" class="field-text">contains</text>

  <!-- GazeApplied到GazeApplication -->
  <line x1="350" y1="390" x2="390" y2="390" class="arrow"/>
  <text x="370" y="385" class="field-text">JNI</text>

  <!-- GazeApplication到PqBlur -->
  <line x1="680" y1="390" x2="730" y2="390" class="composition-arrow"/>
  <text x="705" y="385" class="field-text">contains</text>

  <!-- PqBlur到IPQ_BLR -->
  <line x1="1050" y1="390" x2="1100" y2="390" class="composition-arrow"/>
  <text x="1075" y="385" class="field-text">uses</text>

  <!-- ViewModel到数据模型 -->
  <line x1="570" y1="260" x2="200" y2="520" class="dashed-arrow"/>
  <text x="385" y="390" class="field-text">uses</text>

  <!-- Manager到枚举 -->
  <line x1="920" y1="260" x2="490" y2="520" class="dashed-arrow"/>
  <line x1="920" y1="260" x2="730" y2="520" class="dashed-arrow"/>
  <line x1="920" y1="260" x2="970" y2="520" class="dashed-arrow"/>

  <!-- Service关系 -->
  <line x1="1270" y1="520" x2="1270" y2="260" class="arrow"/>
  <text x="1280" y="390" class="field-text">uses</text>

  <!-- 图例 -->
  <rect x="50" y="740" width="1400" height="120" fill="#f5f5f5" stroke="#cccccc" stroke-width="1" rx="5"/>
  <text x="70" y="765" class="class-title">图例说明</text>
  
  <line x1="70" y1="780" x2="120" y2="780" class="arrow"/>
  <text x="130" y="785" class="field-text">依赖关系 (uses)</text>
  
  <line x1="270" y1="780" x2="320" y2="780" class="dashed-arrow"/>
  <text x="330" y="785" class="field-text">使用关系 (uses data)</text>
  
  <line x1="500" y1="780" x2="550" y2="780" class="composition-arrow"/>
  <text x="560" y="785" class="field-text">组合关系 (contains)</text>
  
  <rect x="70" y="800" width="80" height="20" class="ui-class"/>
  <text x="160" y="815" class="field-text">UI层</text>
  
  <rect x="250" y="800" width="80" height="20" class="vm-class"/>
  <text x="340" y="815" class="field-text">ViewModel层</text>
  
  <rect x="430" y="800" width="80" height="20" class="manager-class"/>
  <text x="520" y="815" class="field-text">管理层</text>
  
  <rect x="610" y="800" width="80" height="20" class="native-class"/>
  <text x="700" y="815" class="field-text">Native层</text>
  
  <rect x="790" y="800" width="80" height="20" class="gpu-class"/>
  <text x="880" y="815" class="field-text">GPU渲染层</text>
  
  <rect x="970" y="800" width="80" height="20" class="bean-class"/>
  <text x="1060" y="815" class="field-text">数据模型</text>
  
  <rect x="1150" y="800" width="80" height="20" class="service-class"/>
  <text x="1240" y="815" class="field-text">服务层</text>

  <!-- 详细功能解释 -->
  <rect x="50" y="880" width="1700" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="70" y="905" class="class-title" style="font-size: 18px;">数字遮盖疗法系统架构详细解释</text>

  <text x="70" y="935" class="class-title" style="font-size: 14px; fill: #e74c3c;">🎯 系统核心功能</text>
  <text x="90" y="955" class="field-text">数字遮盖疗法系统通过眼动追踪实时定位视点，使用GPU虚化技术对弱视眼进行精确遮盖治疗</text>
  <text x="90" y="970" class="field-text">替代传统物理眼罩，提供更舒适、精确、可控的弱视治疗方案</text>

  <text x="70" y="995" class="class-title" style="font-size: 14px; fill: #e74c3c;">🏗️ 架构分层说明</text>

  <text x="90" y="1015" class="field-text" style="font-weight: bold;">UI层 (蓝色)：</text>
  <text x="110" y="1030" class="field-text">• MaskTherapyFragment: 用户界面，提供遮盖疗法开关、参数显示、治疗进度等功能</text>
  <text x="110" y="1045" class="field-text">• 响应用户操作，显示治疗状态，管理界面生命周期</text>

  <text x="90" y="1065" class="field-text" style="font-weight: bold;">ViewModel层 (紫色)：</text>
  <text x="110" y="1080" class="field-text">• MaskViewModel: 数据管理中心，处理遮盖疗法相关的业务逻辑和数据绑定</text>
  <text x="110" y="1095" class="field-text">• 管理LiveData，协调UI和业务层的数据流转</text>

  <text x="90" y="1115" class="field-text" style="font-weight: bold;">管理层 (绿色)：</text>
  <text x="110" y="1130" class="field-text">• MaskManager: 遮盖参数管理，存储和获取治疗配置（通道、模式、区域、弱视眼位置）</text>
  <text x="110" y="1145" class="field-text">• AppliedManager: 应用生命周期管理，控制遮盖疗法的启动、停止和状态监控</text>

  <text x="90" y="1165" class="field-text" style="font-weight: bold;">Native层 (橙色)：</text>
  <text x="110" y="1180" class="field-text">• GazeApplied: JNI桥接层，连接Java/Kotlin和C++代码</text>
  <text x="110" y="1195" class="field-text">• GazeApplication: C++应用核心，处理眼动数据，管理不同应用模式（遮盖、阅读、检查等）</text>

  <text x="90" y="1215" class="field-text" style="font-weight: bold;">GPU渲染层 (粉色)：</text>
  <text x="110" y="1230" class="field-text">• PqBlur: 虚化算法实现，管理虚化参数，处理眼动数据到虚化效果的转换</text>
  <text x="110" y="1245" class="field-text">• IPQ_BLR: GPU虚化引擎，执行OpenGL着色器渲染，实现高性能实时虚化效果</text>

  <text x="900" y="1015" class="field-text" style="font-weight: bold;">数据模型层 (浅绿色)：</text>
  <text x="920" y="1030" class="field-text">• OcclusionTherapy: 遮盖疗法数据模型，包含所有治疗参数和状态信息</text>
  <text x="920" y="1045" class="field-text">• 枚举类: CoverChannel(颜色通道)、CoverMode(遮盖模式)、AmblyopicEye(弱视眼位置)</text>

  <text x="900" y="1065" class="field-text" style="font-weight: bold;">服务层 (青色)：</text>
  <text x="920" y="1080" class="field-text">• GazeTrackService: 后台服务，协调整个系统运行，管理眼动追踪和治疗计时</text>

  <text x="70" y="1115" class="class-title" style="font-size: 14px; fill: #e74c3c;">⚡ 技术特点</text>

  <text x="900" y="1115" class="field-text" style="font-weight: bold;">实时性能：</text>
  <text x="920" y="1130" class="field-text">• 30fps眼动追踪和虚化渲染，确保流畅的用户体验</text>
  <text x="920" y="1145" class="field-text">• GPU硬件加速，低延迟响应，医疗级精度要求</text>

  <text x="900" y="1165" class="field-text" style="font-weight: bold;">医疗应用：</text>
  <text x="920" y="1180" class="field-text">• 个性化治疗参数，支持不同年龄和病情的患者</text>
  <text x="920" y="1195" class="field-text">• 非侵入性治疗，提高患者依从性和治疗效果</text>

  <text x="900" y="1215" class="field-text" style="font-weight: bold;">系统优势：</text>
  <text x="920" y="1230" class="field-text">• 模块化设计，易于维护和功能扩展</text>
  <text x="920" y="1245" class="field-text">• 跨平台架构，支持Android系统和多种硬件配置</text>
  <text x="920" y="1260" class="field-text">• 数据驱动，支持治疗效果分析和优化</text>

  <!-- 添加更多技术细节 -->
  <rect x="50" y="1300" width="1700" height="300" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="70" y="1325" class="class-title" style="font-size: 18px;">关键技术实现细节</text>

  <text x="70" y="1355" class="class-title" style="font-size: 14px; fill: #e74c3c;">🔧 核心算法实现</text>

  <text x="90" y="1375" class="field-text" style="font-weight: bold;">眼动数据处理链路：</text>
  <text x="110" y="1390" class="field-text">相机采集 → 人脸检测 → 眼部定位 → 瞳孔追踪 → 视点计算 → 坐标归一化 → 虚化渲染</text>

  <text x="90" y="1410" class="field-text" style="font-weight: bold;">GPU虚化算法：</text>
  <text x="110" y="1425" class="field-text">• 高斯模糊：使用可分离高斯核，水平和垂直两次卷积，降低计算复杂度</text>
  <text x="110" y="1440" class="field-text">• 颜色通道处理：支持RGB各通道独立或组合虚化，针对不同类型弱视</text>
  <text x="110" y="1455" class="field-text">• 动态半径计算：radius = (distance/11.33/19.5) * 1080/3 * macular_radius</text>

  <text x="90" y="1475" class="field-text" style="font-weight: bold;">实时性能优化：</text>
  <text x="110" y="1490" class="field-text">• GPU并行计算：利用OpenGL着色器并行处理像素，实现实时渲染</text>
  <text x="110" y="1505" class="field-text">• 内存管理：使用对象池和缓存机制，减少GC压力</text>
  <text x="110" y="1520" class="field-text">• 线程优化：UI线程、计算线程、渲染线程分离，确保界面流畅</text>

  <text x="900" y="1375" class="field-text" style="font-weight: bold;">医疗参数标准：</text>
  <text x="920" y="1390" class="field-text">• 虚化半径：基于黄斑区生理结构，0.5-5.5mm对应不同年龄段</text>
  <text x="920" y="1405" class="field-text">• 模糊强度：sigma值5-50，从轻微影响到完全遮盖</text>
  <text x="920" y="1420" class="field-text">• 响应时间：<16ms延迟，满足医疗设备实时性要求</text>

  <text x="900" y="1440" class="field-text" style="font-weight: bold;">安全性保障：</text>
  <text x="920" y="1455" class="field-text">• 参数范围限制：所有医疗参数都有严格的取值范围检查</text>
  <text x="920" y="1470" class="field-text">• 异常处理：网络断开、硬件故障时的安全降级机制</text>
  <text x="920" y="1485" class="field-text">• 数据保护：治疗数据加密存储，符合医疗数据安全标准</text>

  <text x="900" y="1505" class="field-text" style="font-weight: bold;">扩展性设计：</text>
  <text x="920" y="1520" class="field-text">• 插件化架构：新的虚化算法可以通过插件方式集成</text>
  <text x="920" y="1535" class="field-text">• 配置驱动：治疗参数通过配置文件管理，支持远程更新</text>
  <text x="920" y="1550" class="field-text">• API接口：提供标准API，支持第三方医疗设备集成</text>

  <text x="70" y="1575" class="class-title" style="font-size: 14px; fill: #e74c3c;">📊 系统监控指标</text>
  <text x="90" y="1595" class="field-text">• 帧率监控：实时监控渲染帧率，确保30fps稳定输出</text>
  <text x="450" y="1595" class="field-text">• 内存使用：监控GPU和系统内存使用情况</text>
  <text x="850" y="1595" class="field-text">• 治疗时长：精确记录有效治疗时间</text>

</svg>
