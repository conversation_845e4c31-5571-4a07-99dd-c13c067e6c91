<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .message-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .note-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .step-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #e74c3c; }
      
      .actor-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .lifeline { stroke: #1976d2; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #2c3e50; stroke-width: 1; fill: none; stroke-dasharray: 3,3; marker-end: url(#arrowhead); }
      .self-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .activation-box { fill: #ffecb3; stroke: #ff8f00; stroke-width: 1; }
      .note-box { fill: #fff9c4; stroke: #f57f17; stroke-width: 1; }
      .gpu-arrow { stroke: #c2185b; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">数字遮盖疗法系统时序图 - 启动到实时渲染流程</text>

  <!-- 参与者 -->
  <!-- MaskTherapyFragment -->
  <rect x="50" y="60" width="140" height="40" class="actor-box" rx="5"/>
  <text x="120" y="85" text-anchor="middle" class="actor-title">MaskTherapyFragment</text>
  <line x1="120" y1="100" x2="120" y2="1500" class="lifeline"/>

  <!-- MaskViewModel -->
  <rect x="240" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="300" y="85" text-anchor="middle" class="actor-title">MaskViewModel</text>
  <line x1="300" y1="100" x2="300" y2="1500" class="lifeline"/>

  <!-- MaskManager -->
  <rect x="410" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="470" y="85" text-anchor="middle" class="actor-title">MaskManager</text>
  <line x1="470" y1="100" x2="470" y2="1500" class="lifeline"/>

  <!-- AppliedManager -->
  <rect x="580" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="640" y="85" text-anchor="middle" class="actor-title">AppliedManager</text>
  <line x1="640" y1="100" x2="640" y2="1500" class="lifeline"/>

  <!-- GazeApplied -->
  <rect x="750" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="810" y="85" text-anchor="middle" class="actor-title">GazeApplied</text>
  <line x1="810" y1="100" x2="810" y2="1500" class="lifeline"/>

  <!-- GazeApplication -->
  <rect x="920" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="980" y="85" text-anchor="middle" class="actor-title">GazeApplication</text>
  <line x1="980" y1="100" x2="980" y2="1500" class="lifeline"/>

  <!-- PqBlur -->
  <rect x="1090" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1150" y="85" text-anchor="middle" class="actor-title">PqBlur</text>
  <line x1="1150" y1="100" x2="1150" y2="1500" class="lifeline"/>

  <!-- IPQ_BLR -->
  <rect x="1260" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1320" y="85" text-anchor="middle" class="actor-title">IPQ_BLR</text>
  <line x1="1320" y1="100" x2="1320" y2="1500" class="lifeline"/>

  <!-- GazeTrackService -->
  <rect x="1430" y="60" width="140" height="40" class="actor-box" rx="5"/>
  <text x="1500" y="85" text-anchor="middle" class="actor-title">GazeTrackService</text>
  <line x1="1500" y1="100" x2="1500" y2="1500" class="lifeline"/>

  <!-- 步骤1: 遮盖疗法启动 -->
  <text x="50" y="140" class="step-text">步骤1: 遮盖疗法启动流程</text>
  
  <!-- 1.1 用户开启遮盖疗法 -->
  <rect x="115" y="160" width="10" height="40" class="activation-box"/>
  <line x1="120" y1="170" x2="300" y2="170" class="message-arrow"/>
  <text x="210" y="165" class="message-text">switchMaskTherapy(true)</text>

  <!-- 1.2 设置遮盖参数 -->
  <rect x="295" y="180" width="10" height="60" class="activation-box"/>
  <line x1="300" y1="190" x2="470" y2="190" class="message-arrow"/>
  <text x="385" y="185" class="message-text">setOcclusionTherapy(cureInfo)</text>

  <!-- 1.3 保存参数到MaskManager -->
  <line x1="300" y1="210" x2="340" y2="210" class="self-arrow"/>
  <line x1="340" y1="210" x2="340" y2="230" stroke="#2c3e50" stroke-width="2"/>
  <line x1="340" y1="230" x2="300" y2="230" class="message-arrow"/>
  <text x="350" y="205" class="message-text">MaskManager.setCoverChannel()</text>
  <text x="350" y="220" class="message-text">MaskManager.setCoverMode()</text>
  <text x="350" y="235" class="message-text">MaskManager.setCoverArea()</text>

  <!-- 1.4 发送启动消息到Service -->
  <line x1="120" y1="260" x2="1500" y2="260" class="message-arrow"/>
  <text x="810" y="255" class="message-text">sendMessageToService(MSG_START_TRACK, MSG_TURN_ON_CAMERA, MSG_START_APPLIED_CURE)</text>

  <!-- 步骤2: Service启动遮盖应用 -->
  <text x="50" y="300" class="step-text">步骤2: Service启动遮盖应用</text>

  <!-- 2.1 启动应用治疗 -->
  <rect x="1495" y="320" width="10" height="80" class="activation-box"/>
  <line x1="1500" y1="330" x2="640" y2="330" class="message-arrow"/>
  <text x="1070" y="325" class="message-text">AppliedManager.startAppliedCure()</text>

  <!-- 2.2 初始化和启动 -->
  <rect x="635" y="350" width="10" height="100" class="activation-box"/>
  <line x1="640" y1="360" x2="680" y2="360" class="self-arrow"/>
  <line x1="680" y1="360" x2="680" y2="380" stroke="#2c3e50" stroke-width="2"/>
  <line x1="680" y1="380" x2="640" y2="380" class="message-arrow"/>
  <text x="690" y="355" class="message-text">init()</text>
  <text x="690" y="370" class="message-text">gazeApplied.startApplied(CURE)</text>

  <!-- 2.3 设置虚化参数 -->
  <line x1="640" y1="400" x2="810" y2="400" class="message-arrow"/>
  <text x="725" y="395" class="message-text">setBlurParams(radius, sigma, mode, channel)</text>

  <!-- 2.4 JNI调用Native -->
  <rect x="805" y="420" width="10" height="40" class="activation-box"/>
  <line x1="810" y1="430" x2="980" y2="430" class="message-arrow"/>
  <text x="895" y="425" class="message-text">nativeStartApplication(CURE)</text>

  <!-- 2.5 启动虚化模式 -->
  <rect x="975" y="450" width="10" height="60" class="activation-box"/>
  <line x1="980" y1="460" x2="1150" y2="460" class="message-arrow"/>
  <text x="1065" y="455" class="message-text">pqblur_model.set_red_blur_enable(true)</text>

  <!-- 2.6 创建GPU引擎 -->
  <rect x="1145" y="480" width="10" height="80" class="activation-box"/>
  <line x1="1150" y1="490" x2="1320" y2="490" class="message-arrow"/>
  <text x="1235" y="485" class="message-text">pq_blr = newPQ_BLR()</text>

  <!-- 2.7 初始化虚化参数 -->
  <line x1="1150" y1="510" x2="1190" y2="510" class="self-arrow"/>
  <line x1="1190" y1="510" x2="1190" y2="550" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1190" y1="550" x2="1150" y2="550" class="message-arrow"/>
  <text x="1200" y="505" class="message-text">pq_blr->SetRadius(radius)</text>
  <text x="1200" y="520" class="message-text">->SetChannel(channel)</text>
  <text x="1200" y="535" class="message-text">->SetPos(center_x, center_y)</text>
  <text x="1200" y="550" class="message-text">->Done()</text>

  <!-- 步骤3: 实时眼动追踪和虚化 -->
  <text x="50" y="600" class="step-text">步骤3: 实时眼动追踪和虚化渲染</text>

  <!-- 3.1 眼动数据采集 -->
  <rect x="1495" y="620" width="10" height="40" class="activation-box"/>
  <line x1="1500" y1="630" x2="980" y2="630" class="message-arrow"/>
  <text x="1240" y="625" class="message-text">collect_gaze(valid, x, y, dist, duration)</text>

  <!-- 3.2 虚化模式处理 -->
  <rect x="975" y="650" width="10" height="60" class="activation-box"/>
  <line x1="980" y1="660" x2="1020" y2="660" class="self-arrow"/>
  <line x1="1020" y1="660" x2="1020" y2="700" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1020" y1="700" x2="980" y2="700" class="message-arrow"/>
  <text x="1030" y="655" class="message-text">switch (app_mode)</text>
  <text x="1030" y="670" class="message-text">case APP_PQBLUR:</text>
  <text x="1030" y="685" class="message-text">pqblur_model.draw_gaze_result_func(x, y, dist)</text>

  <!-- 3.3 虚化渲染处理 -->
  <rect x="1145" y="720" width="10" height="120" class="activation-box"/>
  <line x1="980" y1="730" x2="1150" y2="730" class="message-arrow"/>
  <text x="1065" y="725" class="message-text">draw_gaze_result_func(x, y, dist)</text>

  <!-- 3.4 坐标转换和范围检查 -->
  <line x1="1150" y1="750" x2="1190" y2="750" class="self-arrow"/>
  <line x1="1190" y1="750" x2="1190" y2="810" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1190" y1="810" x2="1150" y2="810" class="message-arrow"/>
  <text x="1200" y="745" class="message-text">if(blur_enable_flag && pq_blr != nullptr)</text>
  <text x="1200" y="760" class="message-text">if(x > 0 && x < 1.0 && y > 0 && y < 1.0)</text>
  <text x="1200" y="775" class="message-text">screen_x = (int)(x * visual_image_width)</text>
  <text x="1200" y="790" class="message-text">screen_y = (int)(y * visual_image_height)</text>
  <text x="1200" y="805" class="message-text">计算动态半径</text>

  <!-- 3.5 GPU虚化渲染 -->
  <line x1="1150" y1="830" x2="1320" y2="830" class="gpu-arrow"/>
  <text x="1235" y="825" class="message-text">pq_blr->SetPos(screen_x, screen_y)->Done()</text>

  <!-- 3.6 GPU实时渲染 -->
  <rect x="1315" y="850" width="10" height="40" class="activation-box"/>
  <line x1="1320" y1="860" x2="1360" y2="860" class="self-arrow"/>
  <line x1="1360" y1="860" x2="1360" y2="880" stroke="#c2185b" stroke-width="3"/>
  <line x1="1360" y1="880" x2="1320" y2="880" class="gpu-arrow"/>
  <text x="1370" y="855" class="message-text">OpenGL着色器渲染</text>
  <text x="1370" y="870" class="message-text">高斯模糊处理</text>
  <text x="1370" y="885" class="message-text">实时显示到Surface</text>

  <!-- 步骤4: 参数动态调整 -->
  <text x="50" y="930" class="step-text">步骤4: 参数动态调整流程</text>

  <!-- 4.1 参数更新 -->
  <rect x="635" y="950" width="10" height="60" class="activation-box"/>
  <line x1="640" y1="960" x2="810" y2="960" class="message-arrow"/>
  <text x="725" y="955" class="message-text">setBlurParams(newRadius, newSigma, newMode, newChannel)</text>

  <!-- 4.2 Native参数设置 -->
  <rect x="805" y="980" width="10" height="40" class="activation-box"/>
  <line x1="810" y1="990" x2="980" y2="990" class="message-arrow"/>
  <text x="895" y="985" class="message-text">nativeSetBlurParams(...)</text>

  <!-- 4.3 PqBlur参数更新 -->
  <rect x="975" y="1010" width="10" height="80" class="activation-box"/>
  <line x1="980" y1="1020" x2="1150" y2="1020" class="message-arrow"/>
  <text x="1065" y="1015" class="message-text">set_blur_params_func(...)</text>

  <!-- 4.4 分别设置各参数 -->
  <line x1="1150" y1="1040" x2="1190" y2="1040" class="self-arrow"/>
  <line x1="1190" y1="1040" x2="1190" y2="1080" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1190" y1="1080" x2="1150" y2="1080" class="message-arrow"/>
  <text x="1200" y="1035" class="message-text">set_red_blur_radius(radius)</text>
  <text x="1200" y="1050" class="message-text">set_red_blur_sigma(sigma)</text>
  <text x="1200" y="1065" class="message-text">set_red_blur_mode(mode)</text>
  <text x="1200" y="1080" class="message-text">set_red_blur_channel(channel)</text>

  <!-- 步骤5: 停止遮盖疗法 -->
  <text x="50" y="1130" class="step-text">步骤5: 停止遮盖疗法流程</text>

  <!-- 5.1 用户关闭遮盖疗法 -->
  <rect x="115" y="1150" width="10" height="40" class="activation-box"/>
  <line x1="120" y1="1160" x2="300" y2="1160" class="message-arrow"/>
  <text x="210" y="1155" class="message-text">switchMaskTherapy(false)</text>

  <!-- 5.2 发送停止消息 -->
  <line x1="120" y1="1180" x2="1500" y2="1180" class="message-arrow"/>
  <text x="810" y="1175" class="message-text">sendMessageToService(MSG_STOP_APPLIED_CURE, MSG_STOP_TRACK, MSG_TURN_OFF_CAMERA)</text>

  <!-- 5.3 停止应用 -->
  <rect x="1495" y="1200" width="10" height="60" class="activation-box"/>
  <line x1="1500" y1="1210" x2="640" y2="1210" class="message-arrow"/>
  <text x="1070" y="1205" class="message-text">AppliedManager.stopApplied()</text>

  <!-- 5.4 Native停止 -->
  <rect x="635" y="1230" width="10" height="40" class="activation-box"/>
  <line x1="640" y1="1240" x2="980" y2="1240" class="message-arrow"/>
  <text x="810" y="1235" class="message-text">stop_application()</text>

  <!-- 5.5 关闭虚化 -->
  <rect x="975" y="1260" width="10" height="40" class="activation-box"/>
  <line x1="980" y1="1270" x2="1150" y2="1270" class="message-arrow"/>
  <text x="1065" y="1265" class="message-text">set_red_blur_enable(false)</text>

  <!-- 5.6 释放GPU资源 -->
  <rect x="1145" y="1290" width="10" height="40" class="activation-box"/>
  <line x1="1150" y1="1300" x2="1320" y2="1300" class="message-arrow"/>
  <text x="1235" y="1295" class="message-text">deletePQ_BLR(pq_blr)</text>

  <!-- 详细流程解释 -->
  <rect x="50" y="1350" width="1700" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="70" y="1375" class="step-text" style="font-size: 18px;">数字遮盖疗法系统时序流程详细解释</text>

  <text x="70" y="1405" class="step-text" style="font-size: 14px; fill: #e74c3c;">🚀 步骤1: 遮盖疗法启动流程</text>
  <text x="90" y="1425" class="note-text" style="font-weight: bold;">用户交互启动：</text>
  <text x="110" y="1440" class="note-text">• 用户在MaskTherapyFragment界面开启遮盖疗法开关</text>
  <text x="110" y="1455" class="note-text">• MaskViewModel接收用户操作，调用setOcclusionTherapy()设置治疗参数</text>
  <text x="110" y="1470" class="note-text">• 参数包括：虚化通道(1-7)、模式(0-3)、半径(0.5-5.5mm)、强度(5-50)、弱视眼位置</text>

  <text x="90" y="1490" class="note-text" style="font-weight: bold;">参数配置存储：</text>
  <text x="110" y="1505" class="note-text">• MaskManager负责参数的持久化存储，使用MMKV进行本地缓存</text>
  <text x="110" y="1520" class="note-text">• 确保治疗参数在应用重启后仍然有效，保持治疗连续性</text>

  <text x="90" y="1540" class="note-text" style="font-weight: bold;">服务消息传递：</text>
  <text x="110" y="1555" class="note-text">• Fragment通过Message机制向GazeTrackService发送启动指令</text>
  <text x="110" y="1570" class="note-text">• 包含MSG_TURN_ON_CAMERA、MSG_START_TRACK、MSG_START_APPLIED_CURE三个消息</text>

  <text x="70" y="1595" class="step-text" style="font-size: 14px; fill: #e74c3c;">⚙️ 步骤2: Service启动遮盖应用</text>
  <text x="90" y="1615" class="note-text" style="font-weight: bold;">应用管理器初始化：</text>
  <text x="110" y="1630" class="note-text">• AppliedManager执行init()初始化，创建GazeApplied实例</text>
  <text x="110" y="1645" class="note-text">• 设置应用模式为CURE（治疗模式），区别于阅读模式和检查模式</text>

  <text x="90" y="1665" class="note-text" style="font-weight: bold;">JNI桥接调用：</text>
  <text x="110" y="1680" class="note-text">• GazeApplied通过JNI调用nativeStartApplication()启动C++应用</text>
  <text x="110" y="1695" class="note-text">• 传递CURE模式参数，激活GazeApplication的虚化功能</text>

  <text x="90" y="1715" class="note-text" style="font-weight: bold;">GPU引擎创建：</text>
  <text x="110" y="1730" class="note-text">• PqBlur调用set_red_blur_enable(true)启用虚化功能</text>
  <text x="110" y="1745" class="note-text">• 创建IPQ_BLR GPU引擎实例，初始化OpenGL着色器和渲染管线</text>
  <text x="110" y="1760" class="note-text">• 设置初始虚化参数：半径、通道、位置、高斯核参数等</text>

  <text x="900" y="1425" class="step-text" style="font-size: 14px; fill: #e74c3c;">🎯 步骤3: 实时眼动追踪和虚化渲染</text>
  <text x="920" y="1445" class="note-text" style="font-weight: bold;">眼动数据采集：</text>
  <text x="940" y="1460" class="note-text">• GazeTrackService以30fps频率采集眼动数据</text>
  <text x="940" y="1475" class="note-text">• 数据包括：有效性标志、归一化坐标(x,y)、人眼距离、注视时长</text>

  <text x="920" y="1495" class="note-text" style="font-weight: bold;">应用模式分发：</text>
  <text x="940" y="1510" class="note-text">• GazeApplication根据app_mode进行switch分发</text>
  <text x="940" y="1525" class="note-text">• APP_PQBLUR模式调用pqblur_model.draw_gaze_result_func()</text>

  <text x="920" y="1545" class="note-text" style="font-weight: bold;">虚化渲染处理：</text>
  <text x="940" y="1560" class="note-text">• 坐标有效性检查：x,y ∈ (0,1)，确保视点在屏幕范围内</text>
  <text x="940" y="1575" class="note-text">• 坐标转换：归一化坐标 → 屏幕像素坐标</text>
  <text x="940" y="1590" class="note-text">• 动态半径计算：根据人眼距离(35-65cm)计算虚化区域大小</text>

  <text x="920" y="1610" class="note-text" style="font-weight: bold;">GPU实时渲染：</text>
  <text x="940" y="1625" class="note-text">• IPQ_BLR执行SetPos()设置虚化中心位置</text>
  <text x="940" y="1640" class="note-text">• Done()触发OpenGL着色器渲染，实现高斯模糊或置黑效果</text>
  <text x="940" y="1655" class="note-text">• 直接渲染到Surface，用户实时看到虚化效果</text>

  <text x="70" y="1780" class="step-text" style="font-size: 14px; fill: #e74c3c;">🔧 步骤4: 参数动态调整流程</text>
  <text x="90" y="1800" class="note-text" style="font-weight: bold;">实时参数调整：</text>
  <text x="110" y="1815" class="note-text">• 支持治疗过程中实时调整虚化参数，无需重启应用</text>
  <text x="110" y="1830" class="note-text">• 参数调整链路：AppliedManager → GazeApplied → GazeApplication → PqBlur</text>

  <text x="90" y="1850" class="note-text" style="font-weight: bold;">可调整参数详解：</text>
  <text x="110" y="1865" class="note-text">• 虚化半径(0.5-5.5mm)：控制遮盖区域大小，适应不同年龄患者</text>
  <text x="110" y="1880" class="note-text">• 模糊强度(5-50)：调整虚化程度，从轻微模糊到完全遮盖</text>
  <text x="110" y="1895" class="note-text">• 虚化模式：内外部高斯模糊、内外部置黑，满足不同治疗需求</text>
  <text x="110" y="1910" class="note-text">• 颜色通道：R/G/B/RG/RGB，针对不同类型的弱视进行精准治疗</text>

  <text x="900" y="1780" class="step-text" style="font-size: 14px; fill: #e74c3c;">🛑 步骤5: 停止遮盖疗法流程</text>
  <text x="920" y="1800" class="note-text" style="font-weight: bold;">用户主动停止：</text>
  <text x="940" y="1815" class="note-text">• 用户关闭遮盖疗法开关，触发switchMaskTherapy(false)</text>
  <text x="940" y="1830" class="note-text">• Fragment发送停止消息到GazeTrackService</text>

  <text x="920" y="1850" class="note-text" style="font-weight: bold;">系统安全停止：</text>
  <text x="940" y="1865" class="note-text">• Service调用AppliedManager.stopApplied()安全停止应用</text>
  <text x="940" y="1880" class="note-text">• Native层stop_application()关闭虚化功能</text>
  <text x="940" y="1895" class="note-text">• PqBlur设置blur_enable_flag=false，停止虚化渲染</text>

  <text x="920" y="1915" class="note-text" style="font-weight: bold;">资源清理：</text>
  <text x="940" y="1930" class="note-text">• 调用deletePQ_BLR()释放GPU资源</text>
  <text x="940" y="1945" class="note-text">• 清理OpenGL上下文，避免内存泄漏</text>
  <text x="940" y="1960" class="note-text">• 保存治疗数据，记录本次治疗时长和效果</text>

  <text x="70" y="1985" class="step-text" style="font-size: 14px; fill: #e74c3c;">🏥 医疗应用价值</text>
  <text x="90" y="2005" class="note-text" style="font-weight: bold;">弱视治疗原理：</text>
  <text x="110" y="2020" class="note-text">• 通过遮盖健康眼的视野，强制弱视眼参与视觉活动</text>
  <text x="110" y="2035" class="note-text">• 数字遮盖比传统眼罩更精确，只遮盖视点周围区域</text>
  <text x="110" y="2050" class="note-text">• 实时跟踪确保遮盖效果，防止患者通过转动眼球逃避治疗</text>

  <text x="90" y="2070" class="note-text" style="font-weight: bold;">技术优势：</text>
  <text x="110" y="2085" class="note-text">• 非侵入性：无需佩戴物理设备，提高患者舒适度和依从性</text>
  <text x="110" y="2100" class="note-text">• 精确控制：像素级定位，医疗级参数控制，个性化治疗方案</text>
  <text x="110" y="2115" class="note-text">• 实时监控：30fps响应速度，确保治疗效果的连续性和有效性</text>

  <text x="900" y="2005" class="note-text" style="font-weight: bold;">系统性能指标：</text>
  <text x="920" y="2020" class="note-text">• 眼动追踪精度：±0.5°视角，满足医疗应用要求</text>
  <text x="920" y="2035" class="note-text">• 渲染延迟：<16ms端到端延迟，确保实时响应</text>
  <text x="920" y="2050" class="note-text">• 系统稳定性：连续运行2小时无性能衰减</text>

  <text x="900" y="2070" class="note-text" style="font-weight: bold;">临床应用场景：</text>
  <text x="920" y="2085" class="note-text">• 儿童弱视治疗：3-12岁关键治疗期的主要治疗手段</text>
  <text x="920" y="2100" class="note-text">• 家庭治疗：支持在家进行治疗，减少医院往返</text>
  <text x="920" y="2115" class="note-text">• 治疗监控：记录治疗数据，为医生提供客观的疗效评估</text>

</svg>
