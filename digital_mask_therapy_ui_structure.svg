<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E270A1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD7E8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#68CE67;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5BC85A;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#F5F7FA"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    进院版数字遮盖疗法界面结构图
  </text>
  
  <!-- 主容器 -->
  <rect x="100" y="80" width="1000" height="680" rx="25" ry="25" fill="white" filter="url(#shadow)"/>
  
  <!-- 标题区域 (cl_mask_therapy_title) -->
  <rect x="120" y="100" width="960" height="123" rx="25" ry="25" fill="url(#titleGradient)"/>
  
  <!-- 遮盖疗法图标 -->
  <circle cx="155" cy="135" r="10" fill="white"/>
  <text x="155" y="140" text-anchor="middle" font-family="Arial" font-size="12" fill="#E270A1">🎭</text>
  
  <!-- 标题文字 -->
  <text x="180" y="140" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    数字遮盖疗法
  </text>
  
  <!-- 开关组件 -->
  <g id="switch" transform="translate(950, 120)">
    <!-- 开关轨道 -->
    <rect x="0" y="0" width="50" height="28" rx="14" ry="14" fill="#68CE67"/>
    <!-- 开关按钮 -->
    <circle cx="36" cy="14" r="11" fill="white" stroke="#68CE67" stroke-width="2"/>
    <text x="25" y="-5" font-family="Arial" font-size="10" fill="white">ON</text>
  </g>
  
  <!-- 弱视眼标识（红蓝眼镜） -->
  <g id="glasses" transform="translate(900, 125)">
    <rect x="0" y="0" width="34" height="16" rx="8" ry="8" fill="none" stroke="#666" stroke-width="1"/>
    <circle cx="8" cy="8" r="6" fill="#FF4444"/>
    <circle cx="26" cy="8" r="6" fill="#4444FF"/>
    <line x1="14" y1="8" x2="20" y2="8" stroke="#666" stroke-width="2"/>
  </g>
  
  <!-- TimeProgress 组件 -->
  <g id="timeProgress" transform="translate(140, 180)">
    <!-- 已训练时长 -->
    <text x="0" y="15" font-family="Arial" font-size="15" fill="white">30min</text>
    
    <!-- 进度条背景 -->
    <rect x="60" y="5" width="780" height="20" rx="10" ry="10" fill="rgba(255,255,255,0.3)"/>
    
    <!-- 进度条前景 -->
    <rect x="60" y="5" width="260" height="20" rx="10" ry="10" fill="url(#progressGradient)"/>
    
    <!-- 进度条Thumb -->
    <circle cx="320" cy="15" r="12" fill="white" stroke="#68CE67" stroke-width="2"/>
    <text x="320" y="19" text-anchor="middle" font-family="Arial" font-size="8" fill="#68CE67">👤</text>
    
    <!-- 计划时长 -->
    <text x="860" y="15" font-family="Arial" font-size="15" fill="white">90min</text>
  </g>
  
  <!-- CommonAppView 区域 -->
  <rect x="120" y="250" width="960" height="480" rx="25" ry="25" fill="white" stroke="#E5E5E5" stroke-width="1"/>
  
  <!-- 常用应用网格 -->
  <g id="appGrid" transform="translate(160, 290)">
    <!-- 应用图标示例 -->
    <g class="app-item">
      <rect x="0" y="0" width="80" height="80" rx="15" ry="15" fill="#4285F4"/>
      <text x="40" y="45" text-anchor="middle" font-family="Arial" font-size="24" fill="white">📱</text>
      <text x="40" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">应用1</text>
    </g>
    
    <g class="app-item" transform="translate(120, 0)">
      <rect x="0" y="0" width="80" height="80" rx="15" ry="15" fill="#34A853"/>
      <text x="40" y="45" text-anchor="middle" font-family="Arial" font-size="24" fill="white">🎮</text>
      <text x="40" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">应用2</text>
    </g>
    
    <g class="app-item" transform="translate(240, 0)">
      <rect x="0" y="0" width="80" height="80" rx="15" ry="15" fill="#EA4335"/>
      <text x="40" y="45" text-anchor="middle" font-family="Arial" font-size="24" fill="white">📚</text>
      <text x="40" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">应用3</text>
    </g>
    
    <g class="app-item" transform="translate(360, 0)">
      <rect x="0" y="0" width="80" height="80" rx="15" ry="15" fill="#FBBC04"/>
      <text x="40" y="45" text-anchor="middle" font-family="Arial" font-size="24" fill="white">🎵</text>
      <text x="40" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">应用4</text>
    </g>
    
    <!-- 添加应用按钮 -->
    <g class="app-item" transform="translate(480, 0)">
      <rect x="0" y="0" width="80" height="80" rx="15" ry="15" fill="none" stroke="#DDD" stroke-width="2" stroke-dasharray="5,5"/>
      <text x="40" y="50" text-anchor="middle" font-family="Arial" font-size="36" fill="#999">+</text>
      <text x="40" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">添加应用</text>
    </g>
  </g>
  
  <!-- 功能说明区域 -->
  <g id="annotations" transform="translate(50, 400)">
    <text x="0" y="0" font-family="Arial" font-size="16" font-weight="bold" fill="#333">主要功能组件：</text>
    
    <g transform="translate(0, 30)">
      <circle cx="5" cy="0" r="3" fill="#E270A1"/>
      <text x="15" y="5" font-family="Arial" font-size="14" fill="#666">标题区域：渐变背景 (#E270A1 → #FFD7E8)</text>
    </g>
    
    <g transform="translate(0, 55)">
      <circle cx="5" cy="0" r="3" fill="#68CE67"/>
      <text x="15" y="5" font-family="Arial" font-size="14" fill="#666">开关组件：绿色选中状态，支持动画切换</text>
    </g>
    
    <g transform="translate(0, 80)">
      <circle cx="5" cy="0" r="3" fill="#4444FF"/>
      <text x="15" y="5" font-family="Arial" font-size="14" fill="#666">弱视眼标识：红蓝眼镜图标，根据弱视眼位置切换</text>
    </g>
    
    <g transform="translate(0, 105)">
      <circle cx="5" cy="0" r="3" fill="#333"/>
      <text x="15" y="5" font-family="Arial" font-size="14" fill="#666">时间进度：显示已训练/计划时长，性别化Thumb设计</text>
    </g>
    
    <g transform="translate(0, 130)">
      <circle cx="5" cy="0" r="3" fill="#999"/>
      <text x="15" y="5" font-family="Arial" font-size="14" fill="#666">常用应用：4列/8列网格布局，支持添加删除</text>
    </g>
  </g>
  
  <!-- 样式特性说明 -->
  <g id="styleFeatures" transform="translate(650, 400)">
    <text x="0" y="0" font-family="Arial" font-size="16" font-weight="bold" fill="#333">样式特性：</text>
    
    <g transform="translate(0, 30)">
      <rect x="0" y="-8" width="20" height="16" rx="8" ry="8" fill="url(#titleGradient)"/>
      <text x="30" y="5" font-family="Arial" font-size="14" fill="#666">25dp圆角设计，现代化界面</text>
    </g>
    
    <g transform="translate(0, 55)">
      <rect x="0" y="-8" width="20" height="16" rx="8" ry="8" fill="#68CE67"/>
      <text x="30" y="5" font-family="Arial" font-size="14" fill="#666">医疗级绿色主题色</text>
    </g>
    
    <g transform="translate(0, 80)">
      <rect x="0" y="-8" width="20" height="16" rx="8" ry="8" fill="white" stroke="#E5E5E5"/>
      <text x="30" y="5" font-family="Arial" font-size="14" fill="#666">白色卡片式布局</text>
    </g>
    
    <g transform="translate(0, 105)">
      <circle cx="10" cy="0" r="8" fill="none" stroke="#DDD" stroke-width="2" stroke-dasharray="3,3"/>
      <text x="30" y="5" font-family="Arial" font-size="14" fill="#666">阴影和渐变效果</text>
    </g>
  </g>
</svg>
