<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .flow-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 12px; fill: #2c3e50; }
      
      .ui-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .ui-module { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .data-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .data-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .service-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .service-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .view-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .view-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .api-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .api-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .native-layer { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 10; }
      .native-module { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">数字遮盖疗法系统架构图</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="70" width="450" height="180" class="ui-layer"/>
  <text x="275" y="95" text-anchor="middle" class="layer-title">用户界面层</text>
  
  <!-- 遮盖疗法主界面 -->
  <rect x="70" y="110" width="130" height="60" class="ui-module"/>
  <text x="135" y="130" text-anchor="middle" class="module-title">MaskTherapyFragment</text>
  <text x="80" y="145" class="text">数字遮盖疗法主界面</text>
  <text x="80" y="160" class="text">治疗参数显示</text>
  
  <!-- 遮盖效果视图 -->
  <rect x="210" y="110" width="130" height="60" class="ui-module"/>
  <text x="275" y="130" text-anchor="middle" class="module-title">MaskEffectView</text>
  <text x="220" y="145" class="text">实时遮盖效果渲染</text>
  <text x="220" y="160" class="text">弱视眼遮盖显示</text>
  
  <!-- 治疗控制界面 -->
  <rect x="350" y="110" width="130" height="60" class="ui-module"/>
  <text x="415" y="130" text-anchor="middle" class="module-title">TreatmentControlView</text>
  <text x="360" y="145" class="text">治疗开始/暂停/停止</text>
  <text x="360" y="160" class="text">治疗进度显示</text>
  
  <!-- 参数设置界面 -->
  <rect x="70" y="180" width="130" height="60" class="ui-module"/>
  <text x="135" y="200" text-anchor="middle" class="module-title">ParamSettingView</text>
  <text x="80" y="215" class="text">遮盖参数调节</text>
  <text x="80" y="230" class="text">弱视眼选择</text>
  
  <!-- 治疗报告界面 -->
  <rect x="210" y="180" width="130" height="60" class="ui-module"/>
  <text x="275" y="200" text-anchor="middle" class="module-title">TreatmentReportView</text>
  <text x="220" y="215" class="text">治疗时长统计</text>
  <text x="220" y="230" class="text">治疗效果分析</text>
  
  <!-- 校准提示界面 -->
  <rect x="350" y="180" width="130" height="60" class="ui-module"/>
  <text x="415" y="200" text-anchor="middle" class="module-title">CalibrationHintView</text>
  <text x="360" y="215" class="text">姿势偏移提示</text>
  <text x="360" y="230" class="text">重新校准引导</text>

  <!-- 数据模型层 -->
  <rect x="550" y="70" width="450" height="180" class="data-layer"/>
  <text x="775" y="95" text-anchor="middle" class="layer-title">数据模型层</text>
  
  <!-- 遮盖疗法数据 -->
  <rect x="570" y="110" width="130" height="60" class="data-module"/>
  <text x="635" y="130" text-anchor="middle" class="module-title">OcclusionTherapy</text>
  <text x="580" y="145" class="text">遮盖模式参数</text>
  <text x="580" y="160" class="text">治疗时长数据</text>
  
  <!-- 遮盖参数数据 -->
  <rect x="710" y="110" width="130" height="60" class="data-module"/>
  <text x="775" y="130" text-anchor="middle" class="module-title">MaskParameters</text>
  <text x="720" y="145" class="text">模糊半径/程度</text>
  <text x="720" y="160" class="text">遮盖通道/模式</text>
  
  <!-- 治疗记录数据 -->
  <rect x="850" y="110" width="130" height="60" class="data-module"/>
  <text x="915" y="130" text-anchor="middle" class="module-title">TreatmentRecord</text>
  <text x="860" y="145" class="text">治疗历史记录</text>
  <text x="860" y="160" class="text">效果评估数据</text>
  
  <!-- 用户配置数据 -->
  <rect x="570" y="180" width="130" height="60" class="data-module"/>
  <text x="635" y="200" text-anchor="middle" class="module-title">UserConfig</text>
  <text x="580" y="215" class="text">弱视眼位置</text>
  <text x="580" y="230" class="text">个人偏好设置</text>
  
  <!-- 眼动数据 -->
  <rect x="710" y="180" width="130" height="60" class="data-module"/>
  <text x="775" y="200" text-anchor="middle" class="module-title">GazeData</text>
  <text x="720" y="215" class="text">实时眼动坐标</text>
  <text x="720" y="230" class="text">注视点轨迹</text>
  
  <!-- 设备状态数据 -->
  <rect x="850" y="180" width="130" height="60" class="data-module"/>
  <text x="915" y="200" text-anchor="middle" class="module-title">DeviceStatus</text>
  <text x="860" y="215" class="text">校准状态</text>
  <text x="860" y="230" class="text">姿势偏移状态</text>

  <!-- 服务层 -->
  <rect x="1050" y="70" width="450" height="180" class="service-layer"/>
  <text x="1275" y="95" text-anchor="middle" class="layer-title">服务层</text>
  
  <!-- 遮盖疗法服务 -->
  <rect x="1070" y="110" width="130" height="60" class="service-module"/>
  <text x="1135" y="130" text-anchor="middle" class="module-title">MaskTherapyService</text>
  <text x="1080" y="145" class="text">治疗流程控制</text>
  <text x="1080" y="160" class="text">状态管理</text>
  
  <!-- 眼动追踪服务 -->
  <rect x="1210" y="110" width="130" height="60" class="service-module"/>
  <text x="1275" y="130" text-anchor="middle" class="module-title">GazeTrackService</text>
  <text x="1220" y="145" class="text">实时眼动监控</text>
  <text x="1220" y="160" class="text">姿势偏移检测</text>
  
  <!-- 参数管理服务 -->
  <rect x="1350" y="110" width="130" height="60" class="service-module"/>
  <text x="1415" y="130" text-anchor="middle" class="module-title">ParamManagerService</text>
  <text x="1360" y="145" class="text">参数配置管理</text>
  <text x="1360" y="160" class="text">动态参数调整</text>
  
  <!-- 数据上报服务 -->
  <rect x="1070" y="180" width="130" height="60" class="service-module"/>
  <text x="1135" y="200" text-anchor="middle" class="module-title">ReportService</text>
  <text x="1080" y="215" class="text">治疗数据上报</text>
  <text x="1080" y="230" class="text">云端数据同步</text>
  
  <!-- 校准管理服务 -->
  <rect x="1210" y="180" width="130" height="60" class="service-module"/>
  <text x="1275" y="200" text-anchor="middle" class="module-title">CalibrationService</text>
  <text x="1220" y="215" class="text">校准状态监控</text>
  <text x="1220" y="230" class="text">自动重校准</text>
  
  <!-- 治疗计时服务 -->
  <rect x="1350" y="180" width="130" height="60" class="service-module"/>
  <text x="1415" y="200" text-anchor="middle" class="module-title">TimerService</text>
  <text x="1360" y="215" class="text">治疗时长计时</text>
  <text x="1360" y="230" class="text">定时提醒</text>

  <!-- 视图组件层 -->
  <rect x="50" y="270" width="450" height="150" class="view-layer"/>
  <text x="275" y="295" text-anchor="middle" class="layer-title">视图组件层</text>
  
  <!-- 遮盖渲染组件 -->
  <rect x="70" y="310" width="130" height="50" class="view-module"/>
  <text x="135" y="330" text-anchor="middle" class="module-title">MaskRenderer</text>
  <text x="80" y="345" class="text">实时遮盖效果绘制</text>
  <text x="80" y="355" class="text">GPU加速渲染</text>
  
  <!-- 眼动视点显示 -->
  <rect x="210" y="310" width="130" height="50" class="view-module"/>
  <text x="275" y="330" text-anchor="middle" class="module-title">GazePointView</text>
  <text x="220" y="345" class="text">注视点实时显示</text>
  <text x="220" y="355" class="text">轨迹绘制</text>
  
  <!-- 进度指示器 -->
  <rect x="350" y="310" width="130" height="50" class="view-module"/>
  <text x="415" y="330" text-anchor="middle" class="module-title">ProgressIndicator</text>
  <text x="360" y="345" class="text">治疗进度显示</text>
  <text x="360" y="355" class="text">时间倒计时</text>
  
  <!-- 参数调节控件 -->
  <rect x="140" y="370" width="200" height="40" class="view-module"/>
  <text x="240" y="390" text-anchor="middle" class="module-title">参数调节控件与动画效果</text>
  <text x="150" y="405" class="text">滑块控件、开关按钮、动画过渡效果</text>

  <!-- API层 -->
  <rect x="550" y="270" width="450" height="150" class="api-layer"/>
  <text x="775" y="295" text-anchor="middle" class="layer-title">API层</text>
  
  <!-- 遮盖疗法API -->
  <rect x="570" y="310" width="130" height="50" class="api-module"/>
  <text x="635" y="330" text-anchor="middle" class="module-title">MaskApiService</text>
  <text x="580" y="345" class="text">今日遮盖疗法API</text>
  <text x="580" y="355" class="text">参数配置API</text>
  
  <!-- 数据仓库 -->
  <rect x="710" y="310" width="130" height="50" class="api-module"/>
  <text x="775" y="330" text-anchor="middle" class="module-title">MaskRepository</text>
  <text x="720" y="345" class="text">数据访问封装</text>
  <text x="720" y="355" class="text">本地缓存管理</text>
  
  <!-- 治疗数据API -->
  <rect x="850" y="310" width="130" height="50" class="api-module"/>
  <text x="915" y="330" text-anchor="middle" class="module-title">TreatmentDataAPI</text>
  <text x="860" y="345" class="text">治疗记录上传</text>
  <text x="860" y="355" class="text">效果数据同步</text>
  
  <!-- 网络客户端 -->
  <rect x="640" y="370" width="200" height="40" class="api-module"/>
  <text x="740" y="390" text-anchor="middle" class="module-title">网络客户端与数据管理</text>
  <text x="650" y="405" class="text">Retrofit客户端、数据缓存、离线支持</text>

  <!-- Native层 -->
  <rect x="1050" y="270" width="450" height="150" class="native-layer"/>
  <text x="1275" y="295" text-anchor="middle" class="layer-title">Native层</text>
  
  <!-- 遮盖算法 -->
  <rect x="1070" y="310" width="130" height="50" class="native-module"/>
  <text x="1135" y="330" text-anchor="middle" class="module-title">MaskAlgorithm</text>
  <text x="1080" y="345" class="text">遮盖效果算法</text>
  <text x="1080" y="355" class="text">实时图像处理</text>
  
  <!-- 眼动追踪 -->
  <rect x="1210" y="310" width="130" height="50" class="native-module"/>
  <text x="1275" y="330" text-anchor="middle" class="module-title">GazeTracking</text>
  <text x="1220" y="345" class="text">nativeGazeTracking</text>
  <text x="1220" y="355" class="text">实时坐标计算</text>
  
  <!-- 图像渲染 -->
  <rect x="1350" y="310" width="130" height="50" class="native-module"/>
  <text x="1415" y="330" text-anchor="middle" class="module-title">ImageRenderer</text>
  <text x="1360" y="345" class="text">OpenGL渲染</text>
  <text x="1360" y="355" class="text">GPU加速处理</text>
  
  <!-- C++核心引擎 -->
  <rect x="1140" y="370" width="200" height="40" class="native-module"/>
  <text x="1240" y="390" text-anchor="middle" class="module-title">C++遮盖疗法引擎</text>
  <text x="1150" y="405" class="text">高性能图像处理、实时遮盖算法、内存优化</text>

  <!-- 连接箭头 -->
  <line x1="275" y1="250" x2="775" y2="250" class="arrow"/>
  <line x1="500" y1="160" x2="550" y2="160" class="arrow"/>
  <line x1="1000" y1="160" x2="1050" y2="160" class="arrow"/>
  <line x1="275" y1="250" x2="275" y2="270" class="arrow"/>
  <line x1="775" y1="250" x2="775" y2="270" class="arrow"/>
  <line x1="1275" y1="250" x2="1275" y2="270" class="arrow"/>
  
  <line x1="635" y1="360" x2="1135" y2="360" class="data-arrow"/>
  <line x1="775" y1="360" x2="775" y2="310" class="data-arrow"/>
  <line x1="1275" y1="360" x2="1275" y2="310" class="data-arrow"/>

  <!-- 数字遮盖疗法实现流程 -->
  <rect x="50" y="440" width="1450" height="720" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="775" y="465" text-anchor="middle" class="title" style="font-size: 22px;">数字遮盖疗法实现流程</text>

  <!-- 流程步骤 -->
  <text x="70" y="500" class="flow-text" style="font-weight: bold;">1. 初始化阶段：</text>
  <text x="90" y="520" class="flow-text">• 用户在MaskTherapyFragment中查看今日遮盖疗法信息</text>
  <text x="90" y="535" class="flow-text">• 检查校准状态，确保眼动追踪精度</text>
  <text x="90" y="550" class="flow-text">• 加载用户个人遮盖参数配置</text>
  <text x="90" y="565" class="flow-text">• 初始化MaskTherapyService和GazeTrackService</text>

  <text x="70" y="595" class="flow-text" style="font-weight: bold;">2. 参数配置阶段：</text>
  <text x="90" y="615" class="flow-text">• 用户选择弱视眼位置(左眼/右眼)</text>
  <text x="90" y="630" class="flow-text">• 设置遮盖模式：内部高斯模糊、外部高斯模糊、区域置黑等</text>
  <text x="90" y="645" class="flow-text">• 调节遮盖强度：模糊半径、透明度、遮盖范围</text>
  <text x="90" y="660" class="flow-text">• 配置治疗时长和休息间隔</text>

  <text x="70" y="690" class="flow-text" style="font-weight: bold;">3. 治疗启动阶段：</text>
  <text x="90" y="710" class="flow-text">• 用户点击开始治疗按钮</text>
  <text x="90" y="725" class="flow-text">• MaskTherapyService启动治疗流程</text>
  <text x="90" y="740" class="flow-text">• GazeTrackService开始实时眼动追踪</text>
  <text x="90" y="755" class="flow-text">• Native层调用nativeGazeTracking获取眼动坐标</text>
  <text x="90" y="770" class="flow-text">• MaskRenderer开始实时渲染遮盖效果</text>

  <text x="70" y="800" class="flow-text" style="font-weight: bold;">4. 实时治疗阶段：</text>
  <text x="90" y="820" class="flow-text">• 每帧(30fps)获取用户注视点坐标</text>
  <text x="90" y="835" class="flow-text">• 根据弱视眼位置和注视点计算遮盖区域</text>
  <text x="90" y="850" class="flow-text">• MaskAlgorithm实时计算遮盖效果参数</text>
  <text x="90" y="865" class="flow-text">• GPU加速渲染遮盖效果到屏幕</text>
  <text x="90" y="880" class="flow-text">• 监控姿势偏移，必要时提示重新校准</text>

  <text x="70" y="910" class="flow-text" style="font-weight: bold;">5. 状态监控阶段：</text>
  <text x="90" y="930" class="flow-text">• TimerService记录治疗时长</text>
  <text x="90" y="945" class="flow-text">• CalibrationService监控校准状态</text>
  <text x="90" y="960" class="flow-text">• 检测用户是否正常注视屏幕</text>
  <text x="90" y="975" class="flow-text">• 记录治疗过程中的眼动数据</text>

  <!-- 右侧流程 -->
  <text x="800" y="500" class="flow-text" style="font-weight: bold;">6. 数据记录阶段：</text>
  <text x="820" y="520" class="flow-text">• 实时记录治疗时长和遮盖参数</text>
  <text x="820" y="535" class="flow-text">• 保存眼动轨迹数据用于效果分析</text>
  <text x="820" y="550" class="flow-text">• 记录治疗过程中的异常事件</text>
  <text x="820" y="565" class="flow-text">• 生成治疗会话唯一标识</text>

  <text x="800" y="595" class="flow-text" style="font-weight: bold;">7. 治疗结束阶段：</text>
  <text x="820" y="615" class="flow-text">• 用户主动停止或达到预设时长自动结束</text>
  <text x="820" y="630" class="flow-text">• 停止眼动追踪和遮盖效果渲染</text>
  <text x="820" y="645" class="flow-text">• 计算本次治疗的统计数据</text>
  <text x="820" y="660" class="flow-text">• 显示治疗完成提示和效果评估</text>

  <text x="800" y="690" class="flow-text" style="font-weight: bold;">8. 数据上报阶段：</text>
  <text x="820" y="710" class="flow-text">• ReportService整理治疗数据</text>
  <text x="820" y="725" class="flow-text">• 调用TreatmentDataAPI上传治疗记录</text>
  <text x="820" y="740" class="flow-text">• 同步治疗效果数据到云端</text>
  <text x="820" y="755" class="flow-text">• 更新本地治疗历史记录</text>

  <text x="800" y="785" class="flow-text" style="font-weight: bold;">9. 效果分析阶段：</text>
  <text x="820" y="805" class="flow-text">• 分析眼动数据，评估治疗效果</text>
  <text x="820" y="820" class="flow-text">• 计算注视稳定性和双眼协调性</text>
  <text x="820" y="835" class="flow-text">• 生成治疗报告和建议</text>
  <text x="820" y="850" class="flow-text">• 为下次治疗优化参数配置</text>

  <text x="800" y="880" class="flow-text" style="font-weight: bold;">10. 用户反馈阶段：</text>
  <text x="820" y="900" class="flow-text">• 显示治疗完成Toast消息</text>
  <text x="820" y="915" class="flow-text">• 展示治疗时长和效果评分</text>
  <text x="820" y="930" class="flow-text">• 提供治疗体验反馈入口</text>
  <text x="820" y="945" class="flow-text">• 推荐下次治疗时间</text>

  <!-- 核心特性说明 -->
  <rect x="70" y="980" width="1400" height="160" style="fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1005" class="flow-text" style="font-weight: bold; font-size: 16px;">数字遮盖疗法核心特性：</text>

  <text x="90" y="1030" class="flow-text">• <tspan style="font-weight: bold;">实时眼动遮盖：</tspan>基于30fps眼动追踪，实时计算并渲染遮盖效果，精确遮盖弱视眼视野</text>
  <text x="90" y="1050" class="flow-text">• <tspan style="font-weight: bold;">多种遮盖模式：</tspan>支持内外部高斯模糊、区域置黑等多种遮盖算法，适应不同治疗需求</text>
  <text x="90" y="1070" class="flow-text">• <tspan style="font-weight: bold;">个性化参数：</tspan>根据用户弱视程度和治疗进度，动态调整遮盖强度和范围</text>
  <text x="90" y="1090" class="flow-text">• <tspan style="font-weight: bold;">智能监控系统：</tspan>实时监控姿势偏移和校准状态，确保治疗效果的准确性</text>
  <text x="90" y="1110" class="flow-text">• <tspan style="font-weight: bold;">治疗数据分析：</tspan>记录完整的治疗过程数据，提供科学的效果评估和进度跟踪</text>
  <text x="90" y="1130" class="flow-text">• <tspan style="font-weight: bold;">GPU加速渲染：</tspan>利用OpenGL和GPU加速，确保遮盖效果的实时性和流畅性</text>

</svg>
