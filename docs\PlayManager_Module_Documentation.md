# PlayManager 模块技术文档

## 概述

PlayManager 是 MIT DD GazeTracker 项目中的媒体播放管理模块，负责处理应用中的音频播放功能。该模块采用单例模式设计，基于 ExoPlayer 实现，支持多种音频源的播放。

## 模块架构

### 项目结构
```
MitDdGazeTracker/
├── app/                          # 主应用模块
├── LibMedia/                     # 媒体播放库模块
│   └── src/main/java/com/airdoc/component/media/
│       ├── PlayManager.kt        # 核心播放管理器
│       ├── bean/                 # 数据模型
│       └── player/               # 播放器实现
└── LibBehaviorGuidance/          # 行为指导库模块
```

### 依赖关系
```mermaid
graph TD
    A[app 模块] --> B[LibMedia 模块]
    B --> C[androidx.media3]
    B --> D[LibCommon]
    A --> E[使用 PlayManager]
```

## Gradle 配置

### 1. 模块声明 (settings.gradle.kts)
```kotlin
rootProject.name = "MitDdGazeTracker"
include(":app")
include(":LibBehaviorGuidance")
include(":LibMedia")
```

### 2. 主应用依赖 (app/build.gradle.kts)
```kotlin
dependencies {
    implementation(project(":LibBehaviorGuidance"))
    implementation(project(":LibMedia"))
    // 其他依赖...
}
```

### 3. LibMedia 模块配置 (LibMedia/build.gradle.kts)
```kotlin
android {
    namespace = "com.airdoc.component.media"
    compileSdk = 34
    // 其他配置...
}

dependencies {
    implementation(libs.common)
    implementation(libs.media3.exoplayer)
    implementation(libs.media3.ui)
    implementation(libs.media3.common)
}
```

### 4. 版本管理 (gradle/libs.versions.toml)
```toml
[versions]
commonVersion = "0.2.12-SNAPSHOT"
media3Version = "1.3.1"

[libraries]
common = { group = "com.airdoc.component", name = "common", version.ref = "commonVersion" }
media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3Version" }
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3Version" }
media3-common = { group = "androidx.media3", name = "media3-common", version.ref = "media3Version" }
```

## PlayManager 核心实现

### 类结构
```kotlin
package com.airdoc.component.media

object PlayManager {
    private val mediaPlayer: ExoMediaPlayer by lazy {
        ExoMediaPlayer(BaseCommonApplication.instance).apply {
            setPlayerListener(ExternalPlayerListener())
        }
    }
    
    private val playEventListeners = mutableListOf<IPlayEventListener>()
    
    // 核心方法
    fun prepare()
    fun play()
    fun pause()
    fun stop()
    fun release()
    fun isPlaying(): Boolean
    
    // 多媒体播放方法
    fun playUrlMedia(urlMedia: UrlMedia)
    fun playAssetsMedia(context: Context, assetsMedia: AssetsMedia)
    fun playRawMedia(context: Context, rawMedia: RawMedia)
    fun playStreamMedia(context: Context, streamMedia: StreamMedia)
    
    // 事件监听
    fun addPlayerEvenListener(listener: IPlayEventListener)
    fun removePlayerEvenListener(listener: IPlayEventListener)
}
```

### 支持的媒体类型

1. **UrlMedia**: 网络音频文件
2. **AssetsMedia**: Assets 目录下的音频文件
3. **RawMedia**: Raw 资源目录下的音频文件
4. **StreamMedia**: 流媒体音频

## 使用方式

### 1. 导入模块
```kotlin
import com.airdoc.component.media.PlayManager
import com.airdoc.component.media.bean.UrlMedia
import com.airdoc.component.media.bean.RawMedia
```

### 2. 基本播放操作
```kotlin
// 播放网络音频
PlayManager.playUrlMedia(UrlMedia("https://example.com/audio.wav"))

// 播放本地资源
PlayManager.playRawMedia(context, RawMedia(R.raw.audio_file))

// 控制播放
PlayManager.play()
PlayManager.pause()
PlayManager.stop()
```

### 3. 多语言音频播放示例
```kotlin
private fun playAudio(audioName: String) {
    val audioUrl = if (LocaleManager.getLanguage() == "en") {
        "${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/$audioName"
    } else {
        "${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/$audioName"
    }
    PlayManager.playUrlMedia(UrlMedia(audioUrl))
}
```

## 应用场景

### 1. 训练模块 (TrainWebActivity)
```kotlin
private fun showTrainWarning(msg: String, speechName: String) {
    if (LocaleManager.getLanguage() == "en") {
        PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/en/$speechName"))
    } else {
        PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/$speechName"))
    }
}
```

### 2. 用户绑定确认 (ConfirmBindDialog)
```kotlin
private fun playBindConfirm() {
    when(placementType) {
        M_HOME, R_HOME -> {
            val audioFile = if (LocaleManager.getLanguage() == "en") {
                "bind_confirm_equipment_and_patient_files_correct.wav"
            } else {
                "bind_confirm_equipment_and_patient_files_correct.wav"
            }
            PlayManager.playUrlMedia(UrlMedia("${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/${LocaleManager.getLanguage()}/$audioFile"))
        }
    }
}
```

### 3. 系统提醒对话框
- **治疗暂停提醒**: TreatmentPauseDialog
- **治疗过期提醒**: TreatmentExpirationDialog
- **复查提醒**: ReviewRemindDialog
- **遮盖治疗状态**: MaskTherapyStateDialog

## 主要使用文件列表

| 文件路径 | 用途 | 音频类型 |
|---------|------|----------|
| `TrainWebActivity.kt` | 训练警告提示音 | UrlMedia |
| `ConfirmBindDialog.kt` | 设备绑定确认音 | UrlMedia |
| `HomeMainFragment.kt` | 主页提示音 | RawMedia/UrlMedia |
| `TreatmentPauseDialog.kt` | 治疗暂停提示音 | UrlMedia |
| `TreatmentExpirationDialog.kt` | 治疗过期提示音 | UrlMedia |
| `ReviewRemindDialog.kt` | 复查提醒音 | UrlMedia |
| `MaskTherapyStateDialog.kt` | 遮盖治疗状态音 | RawMedia |

## 技术特性

### 1. 单例模式
- 全局唯一实例，避免资源冲突
- 延迟初始化，提高启动性能

### 2. 基于 ExoPlayer
- 支持多种音频格式
- 优秀的性能和稳定性
- 丰富的 API 接口

### 3. 事件监听机制
- 支持播放状态监听
- 可添加多个监听器
- 自动管理监听器生命周期

### 4. 多媒体源支持
- 网络音频流
- 本地资源文件
- Assets 目录文件
- Raw 资源文件

## 最佳实践

### 1. 资源管理
```kotlin
// 在 Activity/Fragment 销毁时释放资源
override fun onDestroy() {
    super.onDestroy()
    PlayManager.removePlayerEvenListener(listener)
}
```

### 2. 错误处理
```kotlin
try {
    PlayManager.playUrlMedia(UrlMedia(audioUrl))
} catch (e: Exception) {
    Logger.e(TAG, "播放音频失败", e)
}
```

### 3. 生命周期管理
```kotlin
override fun onPause() {
    super.onPause()
    if (PlayManager.isPlaying()) {
        PlayManager.pause()
    }
}

override fun onResume() {
    super.onResume()
    // 根据需要恢复播放
}
```

## 注意事项

1. **权限要求**: 确保应用具有网络访问权限（用于播放网络音频）
2. **线程安全**: PlayManager 内部已处理线程安全问题
3. **内存管理**: 及时移除不需要的事件监听器
4. **网络依赖**: 网络音频播放需要稳定的网络连接
5. **资源释放**: 在适当时机调用 release() 方法释放资源

## 扩展开发

如需扩展 PlayManager 功能，可以：

1. 添加新的媒体类型支持
2. 实现自定义播放器监听器
3. 扩展播放控制功能
4. 添加音频效果处理

## 版本信息

- **当前版本**: 基于 Media3 1.3.1
- **最低 Android 版本**: API 28
- **编译 SDK 版本**: 34
- **Kotlin 版本**: 兼容项目 Kotlin 版本

## 故障排除

### 常见问题

#### 1. 音频无法播放
**问题**: 调用 PlayManager.playUrlMedia() 后没有声音

**可能原因**:
- 网络连接问题
- 音频文件路径错误
- 设备音量设置为静音
- 音频文件格式不支持

**解决方案**:
```kotlin
// 检查网络连接
if (!NetworkUtils.isNetworkAvailable(context)) {
    Logger.w(TAG, "网络不可用，无法播放网络音频")
    return
}

// 检查音频 URL
if (audioUrl.isBlank()) {
    Logger.e(TAG, "音频 URL 为空")
    return
}

// 添加播放监听器检查状态
PlayManager.addPlayerEvenListener(object : IPlayEventListener {
    override fun onPlaybackStateChanged(state: PlaybackState) {
        when (state) {
            PlaybackState.ERROR -> Logger.e(TAG, "播放出错")
            PlaybackState.READY -> Logger.d(TAG, "准备就绪")
            PlaybackState.PLAYING -> Logger.d(TAG, "正在播放")
        }
    }
})
```

#### 2. 内存泄漏
**问题**: 长时间使用后应用内存占用过高

**解决方案**:
```kotlin
class MyActivity : AppCompatActivity() {
    private val playListener = object : IPlayEventListener {
        // 监听器实现
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PlayManager.addPlayerEvenListener(playListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        // 重要：移除监听器防止内存泄漏
        PlayManager.removePlayerEvenListener(playListener)
    }
}
```

#### 3. 多个音频同时播放
**问题**: 新音频播放时旧音频没有停止

**解决方案**:
```kotlin
fun playAudioSafely(audioUrl: String) {
    // 先停止当前播放
    if (PlayManager.isPlaying()) {
        PlayManager.stop()
    }

    // 播放新音频
    PlayManager.playUrlMedia(UrlMedia(audioUrl))
}
```

### 调试技巧

#### 1. 启用详细日志
```kotlin
// 在 Application 中启用调试模式
class MyApplication : BaseCommonApplication() {
    override fun onCreate() {
        super.onCreate()
        if (BuildConfig.DEBUG) {
            Logger.setLogLevel(Logger.VERBOSE)
        }
    }
}
```

#### 2. 监控播放状态
```kotlin
private val debugListener = object : IPlayEventListener {
    override fun onPlaybackStateChanged(state: PlaybackState) {
        Logger.d(TAG, "播放状态变化: $state")
    }

    override fun onPlayerError(error: PlaybackException) {
        Logger.e(TAG, "播放错误: ${error.message}", error)
    }
}
```

## 性能优化

### 1. 音频预加载
```kotlin
// 对于频繁使用的音频，可以预先准备
class AudioPreloader {
    fun preloadCommonAudios() {
        val commonAudios = listOf(
            "treatment_pause.wav",
            "treatment_expiration.wav",
            "bind_confirm.wav"
        )

        commonAudios.forEach { audioName ->
            val audioUrl = "${UrlConfig.RES_DOMAIN}/resources/mit-dd/audio/zh/$audioName"
            // 预加载但不播放
            PlayManager.playUrlMedia(UrlMedia(audioUrl))
            PlayManager.pause()
        }
    }
}
```

### 2. 资源缓存策略
```kotlin
// 使用本地缓存减少网络请求
class AudioCacheManager {
    private val cacheDir = File(context.cacheDir, "audio_cache")

    fun getCachedAudioPath(audioUrl: String): String? {
        val fileName = audioUrl.substringAfterLast("/")
        val cacheFile = File(cacheDir, fileName)
        return if (cacheFile.exists()) cacheFile.absolutePath else null
    }
}
```

### 3. 内存优化
```kotlin
// 及时释放不需要的资源
class AudioManager {
    private var currentPlayListener: IPlayEventListener? = null

    fun playWithAutoCleanup(audioUrl: String) {
        // 清理之前的监听器
        currentPlayListener?.let {
            PlayManager.removePlayerEvenListener(it)
        }

        // 创建新的监听器
        currentPlayListener = object : IPlayEventListener {
            override fun onPlaybackCompleted() {
                // 播放完成后自动清理
                PlayManager.removePlayerEvenListener(this)
                currentPlayListener = null
            }
        }

        PlayManager.addPlayerEvenListener(currentPlayListener!!)
        PlayManager.playUrlMedia(UrlMedia(audioUrl))
    }
}
```

## 安全考虑

### 1. URL 验证
```kotlin
fun isValidAudioUrl(url: String): Boolean {
    return try {
        val uri = Uri.parse(url)
        uri.scheme in listOf("http", "https") &&
        uri.host?.contains(UrlConfig.TRUSTED_DOMAIN) == true
    } catch (e: Exception) {
        false
    }
}
```

### 2. 权限检查
```kotlin
fun checkAudioPermissions(context: Context): Boolean {
    return ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.INTERNET
    ) == PackageManager.PERMISSION_GRANTED
}
```

## 测试指南

### 1. 单元测试示例
```kotlin
@Test
fun testPlayManagerSingleton() {
    val instance1 = PlayManager
    val instance2 = PlayManager
    assertEquals(instance1, instance2)
}

@Test
fun testAudioPlayback() {
    val testUrl = "https://example.com/test.wav"
    val urlMedia = UrlMedia(testUrl)

    // 模拟播放
    PlayManager.playUrlMedia(urlMedia)

    // 验证状态
    assertTrue(PlayManager.isPlaying())
}
```

### 2. 集成测试
```kotlin
@Test
fun testMultiLanguageAudio() {
    // 测试中文音频
    LocaleManager.setLanguage("zh")
    val zhAudioUrl = AudioUrlBuilder.buildUrl("test_audio.wav")
    assertTrue(zhAudioUrl.contains("/zh/"))

    // 测试英文音频
    LocaleManager.setLanguage("en")
    val enAudioUrl = AudioUrlBuilder.buildUrl("test_audio.wav")
    assertTrue(enAudioUrl.contains("/en/"))
}
```

## 更新日志

### v1.2.0 (2024-06-03)
- 初始版本发布
- 支持基本音频播放功能
- 集成 ExoPlayer 3.1.1

### v1.2.1 (2024-07-15)
- 修复内存泄漏问题
- 优化播放性能
- 添加错误处理机制

### v1.3.0 (2024-11-20)
- 支持多语言音频切换
- 添加音频预加载功能
- 改进事件监听机制

### v1.3.1 (当前版本)
- 修复网络音频播放问题
- 优化资源管理
- 增强错误日志记录

---

*文档最后更新时间: 2025-01-14*
*文档版本: v1.0*
*维护者: 开发团队*
