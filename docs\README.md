# 眼动追踪系统文档中心

欢迎使用MIT DD眼动追踪系统文档中心。这里包含了系统的完整技术文档，帮助开发者快速理解和使用眼动追踪功能。

## 📚 文档目录

### 🚀 快速开始
- **[快速参考手册](gaze_tracking_quick_reference.md)** - 常用API和代码模板，适合日常开发查阅
- **[系统架构图](system_architecture_diagrams.md)** - 可视化系统架构，理解组件关系

### 📖 详细文档
- **[完整技术文档](gaze_tracking_system_documentation.md)** - 系统的完整技术说明，包含所有细节

## 🎯 按需求查阅

### 我是新手开发者
1. 先阅读 [快速参考手册](gaze_tracking_quick_reference.md) 了解基本用法
2. 查看 [系统架构图](system_architecture_diagrams.md) 理解系统结构
3. 参考快速参考中的集成模板开始开发

### 我需要深入了解系统
1. 阅读 [完整技术文档](gaze_tracking_system_documentation.md) 
2. 重点关注"核心组件详解"和"初始化流程"章节
3. 查看"图像处理流程"了解算法细节

### 我遇到了问题
1. 查看 [快速参考手册](gaze_tracking_quick_reference.md) 的"故障排除速查"
2. 参考 [完整技术文档](gaze_tracking_system_documentation.md) 的"故障排除"章节
3. 检查系统日志和状态

### 我要扩展功能
1. 研究 [系统架构图](system_architecture_diagrams.md) 的扩展架构设计
2. 阅读 [完整技术文档](gaze_tracking_system_documentation.md) 的"扩展开发"章节
3. 参考现有组件的实现模式

## 🔧 核心概念速览

### 系统架构
```
应用层 (Activity/Fragment)
    ↓
服务层 (GazeTrackService)
    ↓
管理层 (TrackingManager)
    ↓
JNI层 (GazeTrack)
    ↓
Native层 (C++算法)
    ↓
硬件层 (Camera + AI Models)
```

### 关键组件
- **GazeTrackService**: 前台服务，管理眼动追踪生命周期
- **TrackingManager**: 统一管理不同追踪模式
- **GazeTrack**: JNI接口，连接Java和C++
- **GTCameraManager**: 相机管理和图像采集

### 数据流程
```
相机采集 → 图像预处理 → Native算法 → 结果封装 → 回调通知
```

## 📋 常用代码片段

### 启动眼动追踪
```kotlin
// 启动服务
startForegroundService(Intent(this, GazeTrackService::class.java))

// 开始追踪
TrackingManager.startTracking(context)

// 设置监听
TrackingManager.setGazeTrackListener(this)
```

### 处理追踪结果
```kotlin
override fun onGazeTracking(result: GazeTrackResult) {
    if (result.valid && result.checkResult()) {
        val x = result.x  // [0~1]
        val y = result.y  // [0~1]
        val distance = result.dist  // cm
        // 处理有效的视线数据
    }
}
```

### 消息通信
```kotlin
val message = Message.obtain().apply {
    what = GazeConstants.MSG_START_TRACK
}
sendMessageToService(message)
```

## 🔍 重要配置

### 图像参数
- **分辨率**: 4048x3040
- **格式**: YUV_420_888 (仅使用Y通道)
- **频率**: ~30fps

### 模型文件
- 存放位置: `app/src/main/assets/configs/`
- 文件格式: `.rknn`
- 自动拷贝到: `/data/data/包名/app_configs/`

### 校准参数
- 文件: `calib_param.txt`
- 格式: JSON (左右眼映射矩阵)
- 检查: `TrackingManager.checkCalibrationParam()`

## ⚠️ 注意事项

### 性能优化
- 及时调用 `image.close()` 释放图像资源
- 图像处理在IO线程，UI更新在主线程
- 使用 `STRATEGY_KEEP_ONLY_LATEST` 背压策略

### 错误处理
- 检查校准参数有效性
- 验证模型文件完整性
- 确认相机权限和状态

### 内存管理
- 避免在主线程进行图像处理
- 合理使用协程管理异步操作
- 注意Native层内存释放

## 🆕 版本信息

### 当前版本: v1.2.0
- 支持多种AI模型
- 增强校准精度
- 完善错误处理
- 优化性能表现

### 兼容性
- Android API 21+
- 支持ARM64架构
- 需要相机权限

## 📞 技术支持

### 开发团队
- **项目**: MIT DD GazeTracker
- **维护**: MIT DD Team
- **更新**: 2025-07-11

### 获取帮助
1. 查阅相关文档章节
2. 检查系统日志输出
3. 验证配置和权限
4. 联系技术支持团队

---

## 📖 文档使用指南

### 文档结构
- **README.md** (本文件) - 文档导航和概览
- **gaze_tracking_quick_reference.md** - 快速参考和常用代码
- **gaze_tracking_system_documentation.md** - 完整技术文档
- **system_architecture_diagrams.md** - 系统架构图表

### 阅读建议
1. **初学者**: 快速参考 → 架构图 → 实践
2. **进阶者**: 完整文档 → 深入理解 → 扩展开发
3. **维护者**: 架构图 → 技术文档 → 故障排除

### 文档更新
文档会随着系统版本更新而同步更新，请关注版本信息确保使用最新文档。

**最后更新**: 2025-07-11  
**文档版本**: v1.0
