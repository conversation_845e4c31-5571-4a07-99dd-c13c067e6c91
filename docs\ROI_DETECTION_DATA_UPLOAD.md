# 兴趣区域检测数据上报功能实现

## 概述

本文档说明兴趣区域检测数据上报功能的实现，该功能仿照注视稳定性的数据上报流程，实现了先上传检测图片获得图片URL，再提交数据的完整流程。

## 实现的组件

### 1. API服务接口
**文件**: `app/src/main/java/com/mitdd/gazetracker/movement/api/ROIDetectionApiService.kt`

```kotlin
interface ROIDetectionApiService {
    @POST("api/movement/roi-detection/submit")
    suspend fun addROIDetection(@Body roiDetectionReq: RequestBody): ApiResponse<ROIDetectionAdd>

    @Multipart
    @POST("api/files/upload/image")
    suspend fun uploadImage(@Part file: MultipartBody.Part): FileUploadResponse
}
```

### 2. 数据模型
**文件**: `app/src/main/java/com/mitdd/gazetracker/movement/bean/ROIDetectionAdd.kt`

响应数据类，包含提交成功后返回的记录ID。

### 3. 数据仓库
**文件**: `app/src/main/java/com/mitdd/gazetracker/movement/repository/ROIDetectionRepository.kt`

提供两个主要方法：
- `uploadImage()`: 上传图片到服务器
- `submitROIDetectionResult()`: 提交兴趣区域检测结果

### 4. 视图模型
**文件**: `app/src/main/java/com/mitdd/gazetracker/movement/vm/ROIDetectionViewModel.kt`

核心业务逻辑，包含：
- 图片上传功能
- 数据提交功能
- 参数构建和数据处理
- LiveData观察者模式

### 5. 结果页面更新
**文件**: `app/src/main/java/com/mitdd/gazetracker/movement/roi/ROIDetectionResultActivity.kt`

增加了数据上报功能：
- 自动截取结果页面图片
- 上传图片获取URL
- 提交完整的检测数据

## 数据流程

### 1. 测试完成流程
1. 用户完成兴趣区域检测测试
2. 系统进入结果页面 (`ROIDetectionResultActivity`)
3. 页面显示检测结果和视线轨迹

### 2. 数据上报流程
1. **数据准备**: 当图片、ROI路径和视线轨迹数据都准备完成后，自动触发上报流程
2. **图片截取**: 截取结果页面的完整截图
3. **图片上传**: 调用 `uploadImage()` 上传图片到服务器
4. **获取URL**: 服务器返回图片的访问URL
5. **数据提交**: 调用 `submitROIDetectionResult()` 提交完整的检测数据

### 3. 提交的数据结构

```json
{
  "patientId": 12345,
  "testInfo": {
    "testType": "ROI_DETECTION",
    "testSequence": "04",
    "testDate": "2025-06-24 10:30:00",
    "duration": 30000,
    "calibrationParams": {...},
    "environmentInfo": {...},
    "notes": "兴趣区域检测测试"
  },
  "resultData": {
    "gazeTrajectory": [...],
    "roiDetectionData": {
      "imagePath": "...",
      "imageWidth": 1920,
      "imageHeight": 1080,
      "roiRegions": "...",
      "gazeTrajectoryJson": "...",
      "heatmapData": "...",
      "attentionDistribution": "...",
      "scanPath": "...",
      "fixationDurationAvg": 250.0,
      "fixationCount": 15,
      "saccadeCount": 14,
      "totalViewingTime": 30000,
      "roiCoverageRate": 0.75,
      "roiDwellTime": "...",
      "firstFixationTime": 150.0,
      "scanPatternType": "systematic"
    }
  },
  "imageUrl": "https://example.com/images/roi_result_123.png"
}
```

## 关键特性

### 1. 自动化流程
- 无需用户手动操作，测试完成后自动上报数据
- 失败重试机制，图片上传失败时仍会提交数据（不包含图片URL）

### 2. 数据完整性
- 包含完整的视线轨迹数据
- 增强的轨迹分析（ROI区域分析、注视持续时间、扫视速度等）
- 热力图数据和扫描路径数据
- ROI区域统计信息

### 3. 错误处理
- 患者信息验证
- 数据完整性检查
- 网络请求异常处理
- 用户友好的错误提示

### 4. 日志记录
- 详细的调试日志
- 关键步骤的状态记录
- 便于问题排查和调试

## 后端接口

### 1. 图片上传接口
- **URL**: `POST /api/files/upload/image`
- **Content-Type**: `multipart/form-data`
- **参数**: `file` (图片文件)

### 2. 数据提交接口
- **URL**: `POST /api/movement/roi-detection/submit`
- **Content-Type**: `application/json`
- **验证**: `testType` 必须为 "ROI_DETECTION"，`testSequence` 必须为 "04"

## 使用说明

1. 确保患者信息已正确设置
2. 完成兴趣区域检测测试
3. 系统会自动进行数据上报
4. 观察Toast提示确认上报结果

## 注意事项

1. 需要网络连接才能完成数据上报
2. 图片上传可能需要较长时间，请耐心等待
3. 如果上报失败，可以查看日志了解具体原因
4. 确保后端服务正常运行且接口可访问
