# ROI视线点坐标信息统计功能增强

## 概述

本次更新为ROI检测功能添加了详细的视线点坐标信息统计，现在可以获取每个ROI区域包含的具体视线点及其详细坐标信息。

## 新增功能

### 1. 每个ROI区域的视线点统计

现在系统会详细记录每个ROI区域包含的视线点信息：

- **视线点数量**：每个ROI区域包含多少个视线点
- **详细坐标信息**：每个视线点的原始坐标、像素坐标、持续时间等
- **时间戳信息**：每个视线点的时间戳，用于时序分析
- **距离信息**：视线点到屏幕的距离

### 2. 新增数据字段

#### roiPointsDetails
包含每个ROI区域的视线点统计摘要：
```json
{
  "roi_0_points": [...],
  "roi_0_count": 15,
  "roi_1_points": [...],
  "roi_1_count": 8,
  "outside_roi_points": [...],
  "outside_roi_count": 12
}
```

#### roiPointsCoordinates
包含每个ROI区域的详细视线点坐标信息：
```json
{
  "0": [
    {
      "pointIndex": 5,
      "originalX": 0.45,
      "originalY": 0.32,
      "pixelX": 864,
      "pixelY": 346,
      "duration": 150,
      "timestamp": 1703472000000,
      "distance": 45.2,
      "roiId": 0,
      "roiName": "ROI_0"
    },
    ...
  ],
  "1": [...]
}
```

#### outsideROICoordinates
包含所有ROI区域外的视线点坐标信息：
```json
[
  {
    "pointIndex": 2,
    "originalX": 0.15,
    "originalY": 0.85,
    "pixelX": 288,
    "pixelY": 918,
    "duration": 100,
    "timestamp": 1703471950000,
    "distance": 42.8,
    "roiId": -1,
    "roiName": "OUTSIDE_ROI"
  },
  ...
]
```

### 3. 坐标信息说明

每个视线点包含以下详细信息：

| 字段 | 类型 | 说明 |
|------|------|------|
| pointIndex | Int | 视线点在整个轨迹中的索引 |
| originalX | Float | 原始X坐标（0-1比例） |
| originalY | Float | 原始Y坐标（0-1比例） |
| pixelX | Int | 像素X坐标 |
| pixelY | Int | 像素Y坐标 |
| duration | Long | 在该点的持续时间（毫秒） |
| timestamp | Long | 时间戳 |
| distance | Float | 到屏幕的距离（厘米） |
| roiId | Int | 所属ROI区域ID（-1表示不在任何ROI内） |
| roiName | String | 所属ROI区域名称 |

## 日志输出增强

系统现在会输出详细的ROI统计日志：

```
ROI统计计算完成:
  - ROI区域数量: 3
  - ROI内视线点: 45
  - ROI外视线点: 12
  - ROI覆盖率: 78.95%
  - 扫描模式: 系统性扫描
  - ROI_0: 15个视线点
    点1: 像素坐标(864, 346), 比例坐标(0.45, 0.32), 持续时间150ms
    点2: 像素坐标(892, 358), 比例坐标(0.46, 0.33), 持续时间120ms
    点3: 像素坐标(876, 342), 比例坐标(0.46, 0.32), 持续时间180ms
    ... 还有12个点
  - ROI_1: 8个视线点
    ...
```

## 后端数据结构更新

后端`RoiDetectionData`类新增了三个字段：

```java
/**
 * 每个ROI的视线点详细信息（JSON格式）
 */
private String roiPointsDetails;

/**
 * 每个ROI的视线点坐标信息（JSON格式）
 */
private String roiPointsCoordinates;

/**
 * ROI区域外的视线点坐标信息（JSON格式）
 */
private String outsideROICoordinates;
```

## 使用场景

### 1. 精确分析
- 分析用户在特定ROI区域的注视模式
- 计算视线在ROI区域内的轨迹路径
- 分析视线进入和离开ROI区域的时间点

### 2. 热力图生成
- 基于ROI区域生成更精确的热力图
- 分析ROI区域内的注视热点分布

### 3. 时序分析
- 分析用户浏览ROI区域的时间顺序
- 计算在不同ROI区域之间的切换频率

### 4. 质量评估
- 评估ROI区域设计的合理性
- 分析用户注意力分布是否符合预期

## 性能考虑

- 新增的坐标信息会增加数据量，但提供了更丰富的分析能力
- 日志输出进行了优化，避免过长的日志影响性能
- JSON序列化使用Gson进行优化处理

## 兼容性

- 新增字段为可选字段，不影响现有功能
- 旧版本的数据处理逻辑保持不变
- 新增的数据字段在后端为可选接收
