# 扫视能力评估反应时间字段移除

## 问题描述

扫视能力评估中，反应时间的计算存在以下问题：

### 原始错误代码
```kotlin
private fun calculatePointReactionTime(pointIndex: Int, gazePoints: List<GazePoint>): Int {
    if (pointIndex == 0) return 0

    val currentPoint = gazePoints[pointIndex]
    val prevPoint = gazePoints[pointIndex - 1]

    return (currentPoint.duration ?: 100) + (prevPoint.duration ?: 100)
}
```

### 问题分析
1. **概念错误**：将当前点和前一个点的duration相加，这不是真正的反应时间
2. **duration字段误用**：duration表示"视线在该点持续时间"，而不是反应时间
3. **缺乏时间基准**：没有记录目标点出现的时间戳

## 修正方案

### 1. 数据结构改进

#### GazePoint增加时间戳字段
```kotlin
@Parcelize
data class GazePoint(
    var x: Float? = null,
    var y: Float? = null,
    var dist: Float? = null,
    var duration: Int? = null,
    var index: Int? = null,
    var timestamp: Long? = null  // 新增：时间戳字段
): Parcelable
```

#### 新增TargetPoint数据结构
```kotlin
@Parcelize
data class TargetPoint(
    val x: Float,
    val y: Float,
    val appearanceTimestamp: Long,  // 目标点出现时间
    val index: Int,
    var isHit: Boolean = false,
    var firstHitTimestamp: Long? = null  // 首次命中时间
): Parcelable {
    
    fun getReactionTime(): Long? {
        return firstHitTimestamp?.let { it - appearanceTimestamp }
    }
}
```

### 2. 反应时间计算逻辑修正

#### 新的计算方法
```kotlin
private fun calculatePointReactionTime(
    pointIndex: Int, 
    gazePoints: List<GazePoint>, 
    targetPoints: List<Pair<Float, Float>>
): Int {
    // 方法1：使用时间戳计算（推荐）
    currentPoint.timestamp?.let { currentTimestamp ->
        val nearestTarget = findNearestTarget(currentX, currentY, targetPoints)
        val targetIndex = nearestTarget.first
        val distanceToTarget = nearestTarget.second
        
        if (distanceToTarget <= targetThreshold) {
            val testStartTime = gazePoints.firstOrNull()?.let { firstPoint ->
                (firstPoint.timestamp ?: 0) - (firstPoint.duration ?: 100)
            } ?: 0
            
            val estimatedTargetAppearTime = testStartTime + targetIndex * 2500L
            val reactionTime = (currentTimestamp - estimatedTargetAppearTime).toInt()
            
            if (reactionTime in 50..3000) {
                return reactionTime
            }
        }
    }
    
    // 方法2：兼容性计算（备用）
    // 使用累积时间和目标点索引的近似计算
}
```

### 3. C++层的正确实现

C++层的GazeGlance类已经正确实现了反应时间计算：

```cpp
// 设置目标点时记录开始时间
bool GazeGlance::set_target_point(float x, float y) {
    startTime = std::chrono::time_point_cast<std::chrono::milliseconds>(now).time_since_epoch().count();
}

// 收集数据时计算反应时间
bool GazeGlance::collect_data(float x, float y, float duration) {
    if(dist < app_hit_near_thres) {
        endTime = std::chrono::time_point_cast<std::chrono::milliseconds>(now).time_since_epoch().count();
        // 反应时间 = 命中时间 - 目标点出现时间
        gaze_data_list.emplace_back(Point3f(target_point.x, target_point.y, static_cast<int>(endTime - startTime)));
    }
}
```

### 4. 相关指标的改进

#### 平均扫视时间
```kotlin
private fun calculateAverageSaccadeTime(gazePoints: List<GazePoint>): Double {
    // 优先使用真实反应时间数据
    val validReactionTimes = gazePoints.mapNotNull { point ->
        val duration = point.duration ?: 0
        if (duration in 50..3000) duration else null  // 合理的反应时间范围
    }
    
    return if (validReactionTimes.isNotEmpty()) {
        validReactionTimes.average()
    } else {
        // 兼容旧数据的计算方式
    }
}
```

#### 延迟计算
```kotlin
private fun calculateLatency(gazePoints: List<GazePoint>): Double {
    // 基于真实反应时间计算延迟，而不是简单的duration累加
}
```

## 验证方法

### 1. 日志验证
添加详细的日志输出来验证计算结果：

```kotlin
Logger.d(TAG, msg = "点[$pointIndex] 反应时间计算: ${reactionTime}ms, 目标点索引: $targetIndex, 距离: $distanceToTarget")
Logger.d(TAG, msg = "平均扫视时间(反应时间): ${averageSaccadeTime}ms")
```

### 2. 合理性检查
- 反应时间应在50ms-3000ms范围内
- 平均反应时间通常在200ms-800ms之间
- 成功命中的点应有较短的反应时间

## 注意事项

1. **向后兼容**：保持对旧数据格式的兼容性
2. **数据来源**：优先使用C++层计算的准确反应时间
3. **异常处理**：对异常的反应时间值进行过滤和修正
4. **测试验证**：通过实际测试验证修正后的计算结果

## 解决方案

由于反应时间计算的复杂性和数据不一致性，决定完全移除reactionTime字段：

### 移除的内容
1. **SaccadeAnalysisResult数据类**中的reactionTime字段
2. **calculatePointReactionTime**方法
3. **buildEnhancedSaccadeTrajectory**中的reactionTime字段
4. **evaluateSaccadeQuality**方法中的reactionTime参数
5. 相关的日志输出和调试信息

### 修改后的评估逻辑
```kotlin
private fun evaluateSaccadeQuality(accuracy: Float, velocity: Float, isOnTarget: Boolean): String {
    val score = when {
        isOnTarget && accuracy >= 80f && velocity in 0.5f..1.5f -> 90f
        isOnTarget && accuracy >= 60f && velocity in 0.3f..2.0f -> 75f
        isOnTarget && accuracy >= 40f -> 60f
        accuracy >= 30f && velocity > 0.1f -> 45f
        else -> 20f
    }
    // 返回质量评级
}
```

## 总结

移除reactionTime字段后的改进：
- ✅ 消除了不准确的反应时间计算
- ✅ 简化了扫视质量评估逻辑
- ✅ 基于精度和速度进行更可靠的评估
- ✅ 避免了duration字段的误用
- ✅ 提高了数据的一致性和可靠性
