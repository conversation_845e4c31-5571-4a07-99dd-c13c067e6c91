# 眼动追踪系统快速参考

## 🚀 快速启动

### 1. 启动眼动追踪服务
```kotlin
// 在Activity中启动服务
val intent = Intent(this, GazeTrackService::class.java)
startForegroundService(intent)
```

### 2. 开始追踪
```kotlin
// 启动追踪
val result = TrackingManager.startTracking(context)
when(result) {
    0 -> // 失败
    1 -> // 成功
    2 -> // 已启动
}
```

### 3. 设置监听器
```kotlin
TrackingManager.setGazeTrackListener(object : IGazeTrackListener {
    override fun onGazeTracking(result: GazeTrackResult) {
        if (result.valid && result.checkResult()) {
            // 处理有效的视线数据
            val x = result.x  // [0~1]
            val y = result.y  // [0~1]
            val distance = result.dist  // cm [35~65]
        }
    }
})
```

## 📋 核心API速查

### TrackingManager
```kotlin
// 初始化
TrackingManager.init(context)

// 追踪控制
TrackingManager.startTracking(context): Int
TrackingManager.stopTracking(): Int

// 校准控制
TrackingManager.startVisualCalibration(context): Int
TrackingManager.startPostureCalibration(context): Int

// 状态检查
TrackingManager.checkCalibrationParam(): Boolean
TrackingManager.getServiceMode(): ServiceMode
```

### 消息通信
```kotlin
// 发送消息到服务
val message = Message.obtain().apply {
    what = GazeConstants.MSG_START_TRACK
}
sendMessageToService(message)

// 常用消息类型
MSG_TURN_ON_CAMERA     // 开启相机
MSG_TURN_OFF_CAMERA    // 关闭相机
MSG_START_TRACK        // 开始追踪
MSG_STOP_TRACK         // 停止追踪
MSG_START_CALIBRATION  // 开始校准
```

## 🔧 数据结构

### GazeTrackResult
```kotlin
data class GazeTrackResult(
    var valid: Boolean,      // 是否检测到用户
    var skew: Boolean,       // 姿势是否偏移
    var x: Float,           // X坐标 [0~1]
    var y: Float,           // Y坐标 [0~1]
    var dist: Float,        // 距离(cm) [35~65]
    var duration: Int       // 持续时间(ms)
)

// 检查结果有效性
fun checkResult(): Boolean = x in 0.0..1.0 && y in 0.0..1.0
```

### CalibrationResult
```kotlin
data class CalibrationResult(
    var calibFinish: Boolean,     // 校准完成
    var leftConsistNum: Int,      // 左眼进程 [0,1,2,3]
    var rightConsistNum: Int      // 右眼进程 [0,1,2,3]
)
```

### ServiceMode
```kotlin
enum class ServiceMode(val code: Int) {
    NONE(0),                    // 未启动
    TRACK(1),                   // 追踪模式
    VISUAL_CALIBRATION(2),      // 视标校准
    POSTURE_CALIBRATION(3)      // 姿势校准
}
```

## ⚙️ 配置常量

### 图像参数
```kotlin
const val IMAGE_WIDTH = 4048
const val IMAGE_HEIGHT = 3040
const val MODEL_DIR_NAME = "configs"
const val MODEL_FILE_EXTENSION = ".rknn"
```

### 文件路径
```
assets/configs/
├── calib_param.txt                    # 校准参数
├── *.rknn                            # AI模型文件
```

## 🐛 故障排除速查

### 初始化失败 (返回0)
```kotlin
// 检查校准参数
if (!TrackingManager.checkCalibrationParam()) {
    // 校准参数无效，需要重新校准
}

// 检查模型文件
val configDir = context.getDir("configs", Context.MODE_PRIVATE)
// 确保.rknn文件存在
```

### 追踪结果无效 (valid=false)
```kotlin
// 检查相机状态
// 检查光照条件
// 确认用户位置在检测范围内
```

### 性能问题
```kotlin
// 确保及时释放图像资源
override fun onAnalyze(image: ImageProxy) {
    try {
        // 处理图像
    } finally {
        image.close() // 重要！
    }
}
```

## 📱 集成模板

### 基础Activity模板
```kotlin
class GazeTrackingActivity : AppCompatActivity(), IGazeTrackListener {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 启动服务
        startForegroundService(Intent(this, GazeTrackService::class.java))
        
        // 设置监听
        TrackingManager.setGazeTrackListener(this)
    }
    
    private fun startTracking() {
        sendMessageToService(
            Message.obtain().apply { what = GazeConstants.MSG_TURN_ON_CAMERA },
            Message.obtain().apply { what = GazeConstants.MSG_START_TRACK }
        )
    }
    
    private fun stopTracking() {
        sendMessageToService(
            Message.obtain().apply { what = GazeConstants.MSG_STOP_TRACK },
            Message.obtain().apply { what = GazeConstants.MSG_TURN_OFF_CAMERA }
        )
    }
    
    override fun onGazeTracking(result: GazeTrackResult) {
        if (result.valid && result.checkResult()) {
            runOnUiThread {
                // 更新UI
                updateGazePoint(result.x, result.y)
            }
        }
    }
    
    override fun onDestroy() {
        TrackingManager.setGazeTrackListener(null)
        super.onDestroy()
    }
}
```

### Fragment集成模板
```kotlin
class GazeTrackingFragment : Fragment(), IGazeTrackListener {
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        TrackingManager.setGazeTrackListener(this)
    }
    
    override fun onGazeTracking(result: GazeTrackResult) {
        if (result.valid) {
            // 处理追踪结果
        }
    }
    
    override fun onDestroyView() {
        TrackingManager.setGazeTrackListener(null)
        super.onDestroyView()
    }
}
```

## 🔍 调试技巧

### 日志输出
```kotlin
Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "调试信息")
```

### 状态监控
```kotlin
// 检查当前模式
val currentMode = TrackingManager.getServiceMode()

// 检查校准状态
val isCalibrated = TrackingManager.checkCalibrationParam()
```

### 性能监控
```kotlin
// 监控图像处理频率
private var frameCount = 0
private var lastTime = System.currentTimeMillis()

override fun onGazeTracking(result: GazeTrackResult) {
    frameCount++
    val currentTime = System.currentTimeMillis()
    if (currentTime - lastTime >= 1000) {
        Log.d(TAG, "FPS: $frameCount")
        frameCount = 0
        lastTime = currentTime
    }
}
```

## 📚 相关文档

- [完整技术文档](gaze_tracking_system_documentation.md)
- [API参考手册](api_reference.md)
- [故障排除指南](troubleshooting_guide.md)

---
**快速参考版本**: v1.0  
**适用系统版本**: v1.2.0+
