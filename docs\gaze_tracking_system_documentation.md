# 眼动追踪系统完整技术文档

## 目录
1. [系统架构概览](#系统架构概览)
2. [核心组件详解](#核心组件详解)
3. [初始化流程](#初始化流程)
4. [图像处理流程](#图像处理流程)
5. [校准系统](#校准系统)
6. [API接口](#api接口)
7. [配置文件](#配置文件)
8. [故障排除](#故障排除)

## 系统架构概览

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用层 (UI)    │    │   服务层         │    │   硬件层         │
│                 │    │                 │    │                 │
│ - Activity      │◄──►│ GazeTrackService│◄──►│ Camera          │
│ - Fragment      │    │ TrackingManager │    │ Native Library  │
│ - ViewModel     │    │ GazeTrack       │    │ AI Models       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模块
- **GazeTrackService**: 前台服务，管理眼动追踪生命周期
- **TrackingManager**: 追踪管理器，协调各种追踪模式
- **GazeTrack**: JNI接口层，与Native层通信
- **GTCameraManager**: 相机管理，图像采集
- **GazeTrackingManager**: 全局管理器，模型文件管理

## 核心组件详解

### 1. GazeTrackService
**职责**: 系统核心服务，管理眼动追踪的完整生命周期

**关键方法**:
```kotlin
class GazeTrackService : Service(), ICameraListener, IGazeTrackListener {
    // 启动眼动追踪
    private fun startGazeTrack()
    
    // 处理消息队列
    private fun handleMessage(msg: Message)
    
    // 相机图像分析回调
    override fun onAnalyze(image: ImageProxy)
}
```

**服务状态管理**:
- `NONE`: 未启动
- `TRACK`: 视线追踪模式
- `VISUAL_CALIBRATION`: 视标校准模式
- `POSTURE_CALIBRATION`: 姿势校准模式

### 2. TrackingManager
**职责**: 统一管理不同的追踪模式，协调资源分配

**核心功能**:
```kotlin
object TrackingManager : IGazeTrackListener {
    // 初始化系统
    fun init(context: Context)
    
    // 启动追踪
    fun startTracking(context: Context): Int
    
    // 图像分发
    fun sendImageProxy(image: ImageProxy)
    
    // 校准参数检查
    fun checkCalibrationParam(): Boolean
}
```

### 3. GazeTrack (JNI层)
**职责**: Java与C++的桥梁，处理底层算法调用

**Native方法映射**:
```kotlin
class GazeTrack {
    // 初始化Native对象
    private external fun nativeCreateObject(configDir: String, snSerial: String): Long
    
    // 启动追踪
    private external fun nativeStartTracking(thiz: Long): Boolean
    
    // 图像处理
    private external fun nativeGazeTracking(
        thiz: Long, 
        inputImage: ByteArray, 
        width: Int, 
        height: Int
    ): HashMap<String?, Any?>?
}
```

## 初始化流程

### 1. 系统启动序列
```mermaid
sequenceDiagram
    participant App as 应用启动
    participant Service as GazeTrackService
    participant Manager as TrackingManager
    participant Native as Native层
    
    App->>Service: startForegroundService()
    Service->>Manager: init(context)
    Manager->>Manager: 检查初始化状态
    Manager->>Native: gazeTrack.init(configDir, deviceSn)
    Native->>Native: 加载AI模型
    Native->>Native: 加载校准参数
    Native-->>Manager: 初始化完成
    Manager-->>Service: 就绪状态
```

### 2. 参数生成详解

#### 配置文件路径生成
```kotlin
val absolutePath = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE).absolutePath
// 生成路径: /data/data/com.mitdd.gazetracker/app_configs
```

#### 设备序列号获取
```kotlin
fun getDeviceSn(): String {
    if (TextUtils.isEmpty(deviceSn.get())) {
        deviceSn.set(DeviceUtils.getDeviceSerial())
    }
    return deviceSn.get()
}
```

#### 模型文件拷贝
```kotlin
private suspend fun copyModel2Dir(context: Context) {
    val weightDir = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE)
    val assetManager = context.assets
    val assetsList = assetManager.list(GazeConstants.MODEL_DIR_NAME)
    
    assetsList?.forEach { assets ->
        if (assets.endsWith(GazeConstants.MODEL_FILE_EXTENSION)) {
            // 拷贝.rknn文件到本地目录
            GTUtils.copyAssets2Dir(context, weightFile, "${GazeConstants.MODEL_DIR_NAME}/$assets")
        }
    }
}
```

## 图像处理流程

### 1. 相机图像采集
```kotlin
// 相机配置
val imageAnalysis = ImageAnalysis.Builder()
    .setResolutionSelector(resolutionStrategy)
    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
    .build().apply {
        setAnalyzer(ContextCompat.getMainExecutor(context)) { image ->
            cameraListener?.onAnalyze(image)
        }
    }
```

**关键参数**:
- **分辨率**: 4048x3040 (定义在GazeConstants中)
- **背压策略**: STRATEGY_KEEP_ONLY_LATEST (保持最新帧)
- **分析频率**: ~30fps

### 2. 图像预处理
```kotlin
fun getDataFromImageY(image: ImageProxy): ByteArray {
    val crop = image.cropRect
    val width = crop.width()
    val height = crop.height()
    val planes = image.planes
    
    // 提取Y通道数据(灰度图像)
    val buffer = planes[0].buffer
    val rowStride = planes[0].rowStride
    val pixelStride = planes[0].pixelStride
    
    // 转换为ByteArray供Native层处理
    // ... 具体转换逻辑
    return data
}
```

### 3. Native层处理流程
```cpp
// C++层图像处理
extern "C" JNIEXPORT jobject JNICALL
Java_com_mitdd_gazetracker_gaze_track_GazeTrack_nativeGazeTracking(
    JNIEnv *env, jclass clazz, jlong thiz,
    jbyteArray inputImage_, jint width, jint height) {
    
    // 转换图像格式
    Mat grayimg(height, width, CV_8UC1, (unsigned char*)inputImage);
    
    // 调用追踪算法
    gazetracker_result_str result = tracker_service->process_gazetracker(grayimg);
    
    // 返回结果到Java层
    return hashMap;
}
```

### 4. 结果数据结构
```kotlin
data class GazeTrackResult(
    var valid: Boolean = false,        // 是否检测到用户
    var skew: Boolean = false,         // 姿势是否偏移
    var x: Float = -1f,               // 视标点X坐标 [0~1]
    var y: Float = -1f,               // 视标点Y坐标 [0~1]
    var dist: Float = -1f,            // 人眼距离屏幕距离(cm) [35~65]
    var duration: Int = 0             // 视线持续时间(ms)
)
```

## 校准系统

### 1. 校准参数文件结构
```json
{
  "left": [
    [-0.49058544635772705, -0.028904315084218979, 0.014056585729122162, 0.50896620750427246],
    [0.036167271435260773, -1.1914373636245728, 0.059290986508131027, 1.2132940292358398],
    // ... 6个4x1的映射矩阵
  ],
  "right": [
    [-0.51798886060714722, -0.015031713061034679, 0.014425966888666153, 0.39534613490104675],
    // ... 右眼校准参数
  ]
}
```

### 2. 校准类型

#### 视标校准 (Visual Calibration)
```kotlin
fun startVisualCalibration(context: Context): Int {
    if (serviceMode.get() == VISUAL_CALIBRATION) return 2
    init(context)
    return if (gazeTrack.startVisualCalibration()) 1 else 0
}
```

#### 姿势校准 (Posture Calibration)
```kotlin
fun startPostureCalibration(context: Context): Int {
    if (serviceMode.get() == POSTURE_CALIBRATION) return 2
    init(context)
    return if (gazeTrack.startPostureCalibration()) 1 else 0
}
```

### 3. 校准结果
```kotlin
data class CalibrationResult(
    var calibFinish: Boolean = false,      // 校准是否完成
    var leftConsistNum: Int = -1,          // 左眼校准进程 [0,1,2,3]
    var rightConsistNum: Int = -1          // 右眼校准进程 [0,1,2,3]
)
```

## API接口

### 1. 服务控制接口
```kotlin
// 启动追踪
TrackingManager.startTracking(context): Int
// 返回值: 0=失败, 1=成功, 2=已启动

// 停止追踪
TrackingManager.stopTracking(): Int

// 检查校准参数
TrackingManager.checkCalibrationParam(): Boolean
```

### 2. 消息通信接口
```kotlin
// 消息类型定义
object GazeConstants {
    const val MSG_TURN_ON_CAMERA = 0x1001
    const val MSG_TURN_OFF_CAMERA = 0x1002
    const val MSG_START_TRACK = 0x1003
    const val MSG_STOP_TRACK = 0x1004
    const val MSG_START_CALIBRATION = 0x1005
    const val MSG_STOP_CALIBRATION = 0x1006
}
```

### 3. 回调接口
```kotlin
interface IGazeTrackListener {
    fun onGazeTracking(result: GazeTrackResult)
    fun onCalibrating(result: CalibrationResult)
    fun onPostureCalibration(result: PostureCalibrationResult)
    fun onGazeServiceModeChange(mode: ServiceMode)
}
```

## 配置文件

### 1. 系统常量配置
```kotlin
object GazeConstants {
    // 图像分析参数
    const val IMAGE_WIDTH = 4048
    const val IMAGE_HEIGHT = 3040

    // 模型文件配置
    const val MODEL_DIR_NAME = "configs"
    const val MODEL_FILE_EXTENSION = ".rknn"

    // 消息类型
    const val MSG_TURN_ON_CAMERA = 0x1001
    const val MSG_TURN_OFF_CAMERA = 0x1002
    const val MSG_START_TRACK = 0x1003
    const val MSG_STOP_TRACK = 0x1004
}
```

### 2. AI模型文件清单
```
app/src/main/assets/configs/
├── calib_param.txt                                                    # 校准参数
├── ckpt_nir_eye_tracking_iris_mobileoneSeg_160x128_epoch27_iou951.rknn    # 虹膜追踪模型
├── ckpt_nir_refine_seg_iris_pupil_mobileone_128_epoch174_iou956.rknn      # 瞳孔分割模型
├── ckpt_nir_refine_seg_iris_pupil_mobileone_s4_128_epoch31_iou972.rknn    # 高精度瞳孔模型
├── face_keypoints_mobileone_seg_heatmap_224x160_epoch60_0000753.rknn      # 面部关键点模型
└── pupil_nir_2lights_keypoints_mobileoneSeg_heatmap_128_epoch170_00028.rknn # 双光源瞳孔模型
```

### 3. 设备配置
```kotlin
object DeviceManager {
    // 获取设备序列号
    fun getDeviceSn(): String

    // 获取设备信息
    fun getDeviceInfo(): DeviceInfo?

    // 检查是否演示模式
    fun isDemoMode(): Boolean

    // 获取系统版本
    fun getOSVersion(): String
}
```

## 故障排除

### 1. 常见问题诊断

#### 初始化失败
**症状**: `startTracking()` 返回 0
**原因分析**:
```kotlin
private fun startGazeTrack() {
    TrackingManager.startTracking(this).also {
        when(it) {
            0 -> { // 失败
                if (!TrackingManager.checkCalibrationParam()) {
                    Toast.makeText(this, getString(R.string.str_invalid_parameters_required_calibration), Toast.LENGTH_LONG).show()
                }
            }
            1 -> { // 成功
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_GAZE_TRACKING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE, true)
                })
            }
        }
    }
}
```

**解决方案**:
1. 检查校准参数文件是否存在
2. 验证模型文件是否完整拷贝
3. 确认设备权限是否正确

#### 相机启动失败
**症状**: 相机无法启动或图像分析异常
**检查点**:
```kotlin
try {
    cameraProvider.unbindAll()
    val imageAnalysis = ImageAnalysis.Builder()
        .setResolutionSelector(resolutionStrategy)
        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
        .build()
    cameraProvider.bindToLifecycle(lifecycleOwner, cameraSelector, imageAnalysis)
} catch (e: Exception) {
    Logger.e(TAG, msg = "startCamera Exception e = ${e.message}")
    stopCamera(context)
}
```

#### 追踪结果异常
**症状**: `GazeTrackResult.valid` 始终为 false
**检查流程**:
1. 验证图像预处理是否正确
2. 检查Native层日志输出
3. 确认校准参数加载状态

### 2. 性能优化建议

#### 内存管理
```kotlin
private fun gazeTracking(image: ImageProxy) {
    mLifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
        val result = gazeTrack.gazeTracking(image)
        withContext(Dispatchers.Main) {
            externalListener?.onGazeTracking(result)
            isSkewing.set(result.skew)
            image.close() // 重要: 及时释放图像资源
        }
    }
}
```

#### 线程优化
- 图像处理在IO线程执行
- UI更新切换到主线程
- 使用协程管理异步操作

### 3. 调试工具

#### 日志系统
```kotlin
Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startTracking serviceMode = ${serviceMode.get()}")
```

#### 状态监控
```kotlin
fun getServiceMode(): ServiceMode {
    return serviceMode.get()
}

fun checkCalibrationParam(): Boolean {
    return gazeTrack.checkCalibrationParam()
}
```

## 扩展开发

### 1. 自定义追踪模式
```kotlin
// 添加新的服务模式
enum class ServiceMode(val code: Int) {
    NONE(0),
    TRACK(1),
    VISUAL_CALIBRATION(2),
    POSTURE_CALIBRATION(3),
    CUSTOM_MODE(4) // 新增模式
}
```

### 2. 结果数据扩展
```kotlin
// 扩展追踪结果
data class ExtendedGazeTrackResult(
    val baseResult: GazeTrackResult,
    val leftPupilSize: Float,    // 左瞳孔大小
    val rightPupilSize: Float,   // 右瞳孔大小
    val blinkState: Boolean,     // 眨眼状态
    val confidence: Float        // 置信度
)
```

### 3. 集成示例
```kotlin
class CustomGazeActivity : AppCompatActivity(), IGazeTrackListener {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 启动眼动追踪服务
        startForegroundService(Intent(this, GazeTrackService::class.java))

        // 设置监听器
        TrackingManager.setGazeTrackListener(this)
    }

    override fun onGazeTracking(result: GazeTrackResult) {
        // 处理追踪结果
        if (result.valid && result.checkResult()) {
            // 有效的视线数据
            updateUI(result.x, result.y, result.dist)
        }
    }
}
```

## 版本历史

### v1.0.0
- 基础眼动追踪功能
- 视标校准系统
- 姿势校准功能

### v1.1.0
- 优化图像处理性能
- 增加错误处理机制
- 完善日志系统

### v1.2.0
- 支持多种AI模型
- 增强校准精度
- 添加设备适配

---

**文档版本**: v1.2.0
**最后更新**: 2025-07-11
**维护者**: MIT DD GazeTracker Team
