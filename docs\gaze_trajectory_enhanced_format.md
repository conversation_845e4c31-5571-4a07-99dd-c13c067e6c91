# 增强版注视稳定性轨迹数据格式

## 概述

修改后的`gazeTrajectoryJson`数据结构增加了视点相对于目标点的位置分析信息，方便用户更好地评估注视稳定性。

## 数据结构

### gazeTrajectory 数组中每个视点的数据结构：

```json
{
  "index": 1,                    // 视点序号
  "x": 0.5234,                   // 视点X坐标（0-1范围）
  "y": 0.4567,                   // 视点Y坐标（0-1范围）
  "distance": 45.2,              // 原始距离数据
  "duration": 150,               // 持续时间（毫秒）
  "relativeDistance": 0.0234,    // 相对于目标点的距离（0-1范围）
  "direction": "东北",            // 相对于目标点的方位
  "directionAngle": 45.5,        // 方位角度（0-360度）
  "ringLevel": 2,                // 环级别（0=目标区域，1-4=第几环，5+=超出范围）
  "isInTargetArea": false        // 是否在目标区域内
}
```

## 字段说明

### 新增字段详解：

1. **relativeDistance** (Float)
   - 视点距离目标点的相对距离
   - 范围：0-1（基于屏幕宽度计算）
   - 用途：量化视点偏离程度

2. **direction** (String)
   - 视点相对于目标点的方位
   - 可能值：`"东"`, `"东北"`, `"北"`, `"西北"`, `"西"`, `"西南"`, `"南"`, `"东南"`
   - 用途：直观显示偏离方向

3. **directionAngle** (Float)
   - 精确的方位角度
   - 范围：0-360度（以目标点为原点，正东为0度，逆时针为正）
   - 用途：精确的方位计算

4. **ringLevel** (Int)
   - 视点所在的环级别
   - 值说明：
     - `0`: 在目标区域内（距离 ≤ 目标点半径）
     - `1-4`: 在第1-4环内
     - `5+`: 超出所有环的范围
   - 用途：快速判断偏离程度

5. **isInTargetArea** (Boolean)
   - 是否在目标区域内
   - `true`: 在目标点半径范围内
   - `false`: 超出目标点半径范围
   - 用途：快速筛选准确的注视点

## 环级别计算规则

基于实际绘制参数：
- **目标点半径**: 25dp (约48像素)
- **环半径增长值**: 37dp (约71像素)
- **环数**: 4

环级别计算：
- 环0（目标区域）: 距离 ≤ 48像素
- 环1: 48像素 < 距离 ≤ 119像素
- 环2: 119像素 < 距离 ≤ 190像素
- 环3: 190像素 < 距离 ≤ 261像素
- 环4: 261像素 < 距离 ≤ 332像素
- 环5+: 距离 > 332像素

## 使用示例

### 评估注视稳定性：

```javascript
// 统计各环的视点分布
const ringDistribution = gazeTrajectory.reduce((acc, point) => {
  acc[point.ringLevel] = (acc[point.ringLevel] || 0) + 1;
  return acc;
}, {});

// 计算目标区域命中率
const targetHitRate = gazeTrajectory.filter(p => p.isInTargetArea).length / gazeTrajectory.length;

// 分析主要偏离方向
const directionCount = gazeTrajectory.reduce((acc, point) => {
  if (!point.isInTargetArea) {
    acc[point.direction] = (acc[point.direction] || 0) + 1;
  }
  return acc;
}, {});
```

### 生成评估报告：

```javascript
const report = {
  targetHitRate: targetHitRate * 100,  // 目标命中率百分比
  ringDistribution: ringDistribution,  // 各环分布
  mainDeviationDirection: Object.keys(directionCount).reduce((a, b) => 
    directionCount[a] > directionCount[b] ? a : b
  ),  // 主要偏离方向
  averageDeviation: gazeTrajectory.reduce((sum, p) => sum + p.relativeDistance, 0) / gazeTrajectory.length
};
```

## 完整示例数据

```json
[
  {
    "index": 1,
    "x": 0.5234,
    "y": 0.4567,
    "distance": 45.2,
    "duration": 150,
    "relativeDistance": 0.0234,
    "direction": "东北",
    "directionAngle": 45.5,
    "ringLevel": 1,
    "isInTargetArea": false
  },
  {
    "index": 2,
    "x": 0.4987,
    "y": 0.5012,
    "distance": 42.8,
    "duration": 120,
    "relativeDistance": 0.0156,
    "direction": "西",
    "directionAngle": 180.2,
    "ringLevel": 0,
    "isInTargetArea": true
  }
]
```

这种增强的数据结构使得用户可以更直观地分析注视稳定性，快速识别偏离模式和稳定性问题。
