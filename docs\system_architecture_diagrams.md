# 眼动追踪系统架构图

## 系统整体架构

```mermaid
graph TB
    subgraph "应用层"
        A[Activity/Fragment] --> B[ViewModel]
        B --> C[Repository]
    end
    
    subgraph "服务层"
        D[GazeTrackService] --> E[TrackingManager]
        E --> F[GazeTrack]
        F --> G[Native Layer]
    end
    
    subgraph "硬件层"
        H[Camera] --> I[ImageAnalysis]
        I --> J[AI Models]
        J --> K[Calibration Data]
    end
    
    A -.->|消息通信| D
    C -->|API调用| E
    I -->|图像数据| F
    G -->|算法处理| J
```

## 数据流程图

```mermaid
sequenceDiagram
    participant App as 应用
    participant Service as GazeTrackService
    participant Manager as TrackingManager
    participant Camera as 相机
    participant Native as Native层
    
    App->>Service: 启动服务
    Service->>Manager: 初始化
    Manager->>Native: 加载模型
    
    App->>Service: 开始追踪
    Service->>Manager: startTracking()
    Manager->>Native: nativeStartTracking()
    
    loop 图像处理循环
        Camera->>Service: onAnalyze(image)
        Service->>Manager: sendImageProxy(image)
        Manager->>Native: nativeGazeTracking()
        Native-->>Manager: GazeTrackResult
        Manager-->>Service: onGazeTracking()
        Service-->>App: 结果回调
    end
```

## 组件关系图

```mermaid
classDiagram
    class GazeTrackService {
        +startGazeTrack()
        +handleMessage()
        +onAnalyze(ImageProxy)
        +onGazeTracking(GazeTrackResult)
    }
    
    class TrackingManager {
        +init(Context)
        +startTracking(Context)
        +sendImageProxy(ImageProxy)
        +checkCalibrationParam()
    }
    
    class GazeTrack {
        +init(String, String)
        +gazeTracking(ImageProxy)
        +startTracking()
        +checkCalibrationParam()
    }
    
    class GTCameraManager {
        +startCamera(Context)
        +stopCamera(Context)
        +setFillLightLampState(Boolean)
    }
    
    class GazeTrackResult {
        +Boolean valid
        +Boolean skew
        +Float x, y, dist
        +Int duration
        +checkResult()
    }
    
    GazeTrackService --> TrackingManager
    TrackingManager --> GazeTrack
    GazeTrackService --> GTCameraManager
    GazeTrack --> GazeTrackResult
```

## 状态机图

```mermaid
stateDiagram-v2
    [*] --> NONE: 系统启动
    
    NONE --> TRACK: startTracking()
    NONE --> VISUAL_CALIBRATION: startVisualCalibration()
    NONE --> POSTURE_CALIBRATION: startPostureCalibration()
    
    TRACK --> NONE: stopTracking()
    VISUAL_CALIBRATION --> NONE: stopVisualCalibration()
    POSTURE_CALIBRATION --> NONE: stopPostureCalibration()
    
    TRACK --> VISUAL_CALIBRATION: 切换到校准模式
    VISUAL_CALIBRATION --> TRACK: 校准完成
    
    state TRACK {
        [*] --> 图像采集
        图像采集 --> 算法处理
        算法处理 --> 结果输出
        结果输出 --> 图像采集
    }
```

## 初始化流程图

```mermaid
flowchart TD
    A[应用启动] --> B[启动GazeTrackService]
    B --> C{是否已初始化?}
    C -->|否| D[TrackingManager.init()]
    C -->|是| I[直接启动追踪]
    
    D --> E[获取配置目录路径]
    E --> F[获取设备序列号]
    F --> G[拷贝模型文件]
    G --> H[调用Native初始化]
    H --> I[启动追踪]
    
    I --> J[开启相机]
    J --> K[设置图像分析]
    K --> L[开始图像处理循环]
```

## 图像处理流程图

```mermaid
flowchart LR
    A[相机采集] --> B[ImageProxy]
    B --> C[提取Y通道数据]
    C --> D[转换为ByteArray]
    D --> E[传递到Native层]
    
    E --> F[OpenCV Mat转换]
    F --> G[AI模型推理]
    G --> H[算法处理]
    H --> I[结果封装]
    
    I --> J[返回Java层]
    J --> K[GazeTrackResult]
    K --> L[回调通知]
```

## 校准系统架构

```mermaid
graph TB
    subgraph "校准类型"
        A[视标校准] 
        B[姿势校准]
    end
    
    subgraph "校准数据"
        C[左眼参数矩阵]
        D[右眼参数矩阵]
        E[姿势偏移参数]
    end
    
    subgraph "校准流程"
        F[显示校准点]
        G[采集注视数据]
        H[计算映射参数]
        I[保存校准结果]
    end
    
    A --> F
    B --> F
    F --> G
    G --> H
    H --> C
    H --> D
    H --> E
    H --> I
```

## 错误处理流程

```mermaid
flowchart TD
    A[启动追踪] --> B{初始化成功?}
    B -->|否| C[检查校准参数]
    C --> D{参数有效?}
    D -->|否| E[显示校准提示]
    D -->|是| F[检查模型文件]
    F --> G{文件完整?}
    G -->|否| H[重新拷贝模型]
    G -->|是| I[检查相机权限]
    
    B -->|是| J[开始图像处理]
    J --> K{追踪结果有效?}
    K -->|否| L[检查环境条件]
    K -->|是| M[正常运行]
    
    E --> N[启动校准流程]
    H --> A
    L --> O[调整光照/位置]
    N --> A
    O --> J
```

## 性能优化架构

```mermaid
graph LR
    subgraph "内存管理"
        A[图像缓存池]
        B[及时释放ImageProxy]
        C[Native内存管理]
    end
    
    subgraph "线程优化"
        D[主线程 - UI更新]
        E[IO线程 - 图像处理]
        F[计算线程 - 算法运算]
    end
    
    subgraph "算法优化"
        G[模型量化]
        H[ROI区域处理]
        I[帧率自适应]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
```

## 扩展架构设计

```mermaid
graph TB
    subgraph "当前系统"
        A[基础眼动追踪]
        B[校准系统]
        C[图像处理]
    end
    
    subgraph "扩展模块"
        D[眼动分析]
        E[行为识别]
        F[数据统计]
        G[云端同步]
    end
    
    subgraph "应用场景"
        H[医疗诊断]
        I[教育评估]
        J[游戏交互]
        K[广告分析]
    end
    
    A --> D
    B --> E
    C --> F
    D --> H
    E --> I
    F --> J
    G --> K
```

---

这些架构图展示了眼动追踪系统的各个层面：
- **整体架构**: 展示系统的分层结构
- **数据流程**: 说明数据在各组件间的流转
- **组件关系**: 描述类之间的依赖关系
- **状态机**: 展示系统的状态转换
- **流程图**: 详细说明各种操作流程
- **扩展设计**: 为未来功能扩展提供参考

使用这些图表可以帮助开发者快速理解系统架构，便于维护和扩展开发。
