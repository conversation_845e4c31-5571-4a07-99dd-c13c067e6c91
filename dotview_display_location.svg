<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .system-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .window-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .view-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .coordinate-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .display-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .system-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .window-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .view-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .coordinate-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .display-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .system-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .window-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .view-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">DotView 视点显示位置详细分析</text>
  
  <!-- 第一层：系统级悬浮窗 -->
  <rect x="50" y="70" width="320" height="120" class="system-layer"/>
  <text x="210" y="95" text-anchor="middle" class="section-title">系统级悬浮窗</text>
  
  <rect x="70" y="110" width="280" height="70" class="system-module"/>
  <text x="210" y="130" text-anchor="middle" class="method-title">TYPE_APPLICATION_OVERLAY</text>
  <text x="80" y="150" class="text">• 显示在所有应用之上</text>
  <text x="80" y="165" class="text">• 需要悬浮窗权限</text>

  <!-- 第二层：WindowManager -->
  <rect x="390" y="70" width="320" height="120" class="window-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">WindowManager</text>
  
  <rect x="410" y="110" width="280" height="70" class="window-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">addView/updateViewLayout</text>
  <text x="420" y="150" class="text">• 管理悬浮窗的添加和更新</text>
  <text x="420" y="165" class="text">• 控制窗口位置和属性</text>

  <!-- 第三层：DotView -->
  <rect x="730" y="70" width="320" height="120" class="view-layer"/>
  <text x="890" y="95" text-anchor="middle" class="section-title">DotView</text>
  
  <rect x="750" y="110" width="280" height="70" class="view-module"/>
  <text x="890" y="130" text-anchor="middle" class="method-title">20x20px 绿色圆点</text>
  <text x="760" y="150" class="text">• 半透明背景</text>
  <text x="760" y="165" class="text">• 不可点击和聚焦</text>

  <!-- 第四层：坐标系统 -->
  <rect x="1070" y="70" width="320" height="120" class="coordinate-layer"/>
  <text x="1230" y="95" text-anchor="middle" class="section-title">坐标系统</text>
  
  <rect x="1090" y="110" width="280" height="70" class="coordinate-module"/>
  <text x="1230" y="130" text-anchor="middle" class="method-title">屏幕绝对坐标</text>
  <text x="1100" y="150" class="text">• 左上角为(0,0)</text>
  <text x="1100" y="165" class="text">• 实时跟随眼动坐标</text>

  <!-- 第五层：显示效果 -->
  <rect x="1410" y="70" width="320" height="120" class="display-layer"/>
  <text x="1570" y="95" text-anchor="middle" class="section-title">显示效果</text>
  
  <rect x="1430" y="110" width="280" height="70" class="display-module"/>
  <text x="1570" y="130" text-anchor="middle" class="method-title">屏幕最顶层显示</text>
  <text x="1440" y="150" class="text">• 覆盖所有应用界面</text>
  <text x="1440" y="165" class="text">• 30fps实时更新位置</text>

  <!-- 连接箭头 -->
  <line x1="370" y1="130" x2="390" y2="130" class="system-arrow"/>
  <line x1="710" y1="130" x2="730" y2="130" class="window-arrow"/>
  <line x1="1050" y1="130" x2="1070" y2="130" class="view-arrow"/>
  <line x1="1390" y1="130" x2="1410" y2="130" class="arrow"/>

  <!-- 详细分析 -->
  <rect x="50" y="220" width="1680" height="1130" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="890" y="245" text-anchor="middle" class="title" style="font-size: 22px;">DotView 显示位置详细分析</text>

  <!-- 第一部分：DotView实现详解 -->
  <text x="70" y="280" class="layer-title">🎯 DotView实现详解</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. DotView类定义</text>
  <text x="110" y="325" class="code-text">class DotView @JvmOverloads constructor(</text>
  <text x="130" y="340" class="code-text">context: Context,</text>
  <text x="130" y="355" class="code-text">attrs: AttributeSet? = null,</text>
  <text x="130" y="370" class="code-text">defStyleAttr: Int = 0</text>
  <text x="110" y="385" class="code-text">) : View(context, attrs, defStyleAttr) {</text>
  <text x="130" y="400" class="code-text">// 🔥 继承自Android View，是一个标准的UI组件</text>
  <text x="110" y="415" class="code-text">}</text>
  
  <text x="90" y="440" class="flow-text" style="font-weight: bold;">2. WindowManager获取</text>
  <text x="110" y="460" class="code-text">private val windowManager: WindowManager by lazy {</text>
  <text x="130" y="475" class="code-text">context.getSystemService(Context.WINDOW_SERVICE) as WindowManager</text>
  <text x="110" y="490" class="code-text">}</text>
  <text x="110" y="510" class="flow-text">• <tspan style="color: #3498db;">系统服务：</tspan>获取系统级WindowManager服务</text>
  <text x="110" y="525" class="flow-text">• <tspan style="color: #3498db;">懒加载：</tspan>使用lazy确保在需要时才初始化</text>
  
  <text x="90" y="550" class="flow-text" style="font-weight: bold;">3. 悬浮窗参数配置</text>
  <text x="110" y="570" class="code-text">val dotParams = WindowManager.LayoutParams(</text>
  <text x="130" y="585" class="code-text">20,  // 宽度：20像素</text>
  <text x="130" y="600" class="code-text">20,  // 高度：20像素</text>
  <text x="130" y="615" class="code-text">WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,  // 🔥 悬浮窗类型</text>
  <text x="130" y="630" class="code-text">WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or</text>
  <text x="130" y="645" class="code-text">WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,  // 🔥 不可聚焦和触摸</text>
  <text x="130" y="660" class="code-text">PixelFormat.TRANSLUCENT  // 🔥 半透明格式</text>
  <text x="110" y="675" class="code-text">).apply {</text>
  <text x="130" y="690" class="code-text">gravity = Gravity.LEFT or Gravity.TOP  // 🔥 左上角对齐</text>
  <text x="110" y="705" class="code-text">}</text>

  <!-- 第二部分：显示位置机制 -->
  <text x="900" y="280" class="layer-title">📍 显示位置机制</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 悬浮窗类型说明</text>
  <text x="940" y="325" class="flow-text">• <tspan style="color: #e74c3c;">TYPE_APPLICATION_OVERLAY：</tspan>Android 8.0+推荐的悬浮窗类型</text>
  <text x="940" y="340" class="flow-text">• <tspan style="color: #e74c3c;">系统级显示：</tspan>显示在所有应用界面之上</text>
  <text x="940" y="355" class="flow-text">• <tspan style="color: #e74c3c;">权限要求：</tspan>需要SYSTEM_ALERT_WINDOW权限</text>
  <text x="940" y="370" class="flow-text">• <tspan style="color: #e74c3c;">全局可见：</tspan>即使切换应用也始终可见</text>
  
  <text x="920" y="395" class="flow-text" style="font-weight: bold;">2. 窗口标志说明</text>
  <text x="940" y="415" class="flow-text">• <tspan style="color: #e74c3c;">FLAG_NOT_FOCUSABLE：</tspan>不获取焦点，不影响其他应用操作</text>
  <text x="940" y="430" class="flow-text">• <tspan style="color: #e74c3c;">FLAG_NOT_TOUCH_MODAL：</tspan>不拦截触摸事件，点击穿透</text>
  <text x="940" y="445" class="flow-text">• <tspan style="color: #e74c3c;">PixelFormat.TRANSLUCENT：</tspan>支持半透明效果</text>
  <text x="940" y="460" class="flow-text">• <tspan style="color: #e74c3c;">Gravity.LEFT | TOP：</tspan>以左上角为基准定位</text>
  
  <text x="920" y="485" class="flow-text" style="font-weight: bold;">3. 视觉样式设置</text>
  <text x="940" y="505" class="code-text">init {</text>
  <text x="960" y="520" class="code-text">setBackgroundResource(R.drawable.common_green_round_bg)</text>
  <text x="940" y="535" class="code-text">}</text>
  <text x="940" y="555" class="flow-text">• <tspan style="color: #e74c3c;">绿色圆形背景：</tspan>使用drawable资源设置圆形绿色背景</text>
  <text x="940" y="570" class="flow-text">• <tspan style="color: #e74c3c;">20x20像素：</tspan>小巧的圆点，不遮挡用户视线</text>
  <text x="940" y="585" class="flow-text">• <tspan style="color: #e74c3c;">半透明效果：</tspan>既可见又不完全遮挡底层内容</text>

  <!-- 第三部分：显示控制方法 -->
  <text x="70" y="620" class="layer-title">🎮 显示控制方法</text>
  
  <text x="90" y="645" class="flow-text" style="font-weight: bold;">1. show()方法 - 首次显示</text>
  <text x="110" y="665" class="code-text">fun show(x: Int, y: Int) {</text>
  <text x="130" y="680" class="code-text">dotParams.x = x  // 设置X坐标</text>
  <text x="130" y="695" class="code-text">dotParams.y = y  // 设置Y坐标</text>
  <text x="130" y="710" class="code-text">windowManager.addView(this, dotParams)  // 🔥 添加到WindowManager</text>
  <text x="110" y="725" class="code-text">}</text>
  
  <text x="90" y="750" class="flow-text" style="font-weight: bold;">2. update()方法 - 位置更新</text>
  <text x="110" y="770" class="code-text">fun update(x: Int, y: Int) {</text>
  <text x="130" y="785" class="code-text">dotParams.x = x  // 更新X坐标</text>
  <text x="130" y="800" class="code-text">dotParams.y = y  // 更新Y坐标</text>
  <text x="130" y="815" class="code-text">windowManager.updateViewLayout(this, dotParams)  // 🔥 更新布局</text>
  <text x="110" y="830" class="code-text">}</text>
  
  <text x="90" y="855" class="flow-text" style="font-weight: bold;">3. remove()方法 - 移除显示</text>
  <text x="110" y="875" class="code-text">fun remove() {</text>
  <text x="130" y="890" class="code-text">windowManager.removeView(this)  // 🔥 从WindowManager移除</text>
  <text x="110" y="905" class="code-text">}</text>

  <!-- 第四部分：坐标转换机制 -->
  <text x="900" y="620" class="layer-title">📐 坐标转换机制</text>
  
  <text x="920" y="645" class="flow-text" style="font-weight: bold;">1. WidgetManager中的坐标转换</text>
  <text x="940" y="665" class="code-text">fun showDotView(context: Context, result: GazeTrackResult, screenWidth: Int, screenHeight: Int) {</text>
  <text x="960" y="680" class="code-text">if (result.checkResult()) {</text>
  <text x="980" y="695" class="code-text">if (dotView == null) {</text>
  <text x="1000" y="710" class="code-text">dotView = DotView(context).apply {</text>
  <text x="1020" y="725" class="code-text">// 🔥 坐标转换：[0,1] → 屏幕像素坐标</text>
  <text x="1020" y="740" class="code-text">show((result.x * screenWidth - 10).toInt(),</text>
  <text x="1040" y="755" class="code-text">(result.y * screenHeight - 10).toInt())</text>
  <text x="1000" y="770" class="code-text">}</text>
  <text x="980" y="785" class="code-text">} else {</text>
  <text x="1000" y="800" class="code-text">// 🔥 更新位置</text>
  <text x="1000" y="815" class="code-text">dotView?.update((result.x * screenWidth - 10).toInt(),</text>
  <text x="1020" y="830" class="code-text">(result.y * screenHeight - 10).toInt())</text>
  <text x="980" y="845" class="code-text">}</text>
  <text x="960" y="860" class="code-text">}</text>
  <text x="940" y="875" class="code-text">}</text>
  
  <text x="920" y="900" class="flow-text" style="font-weight: bold;">2. 坐标转换说明</text>
  <text x="940" y="920" class="flow-text">• <tspan style="color: #f39c12;">输入坐标：</tspan>GazeTrackResult.x/y 范围[0,1]，相对坐标</text>
  <text x="940" y="935" class="flow-text">• <tspan style="color: #f39c12;">转换公式：</tspan>屏幕坐标 = 相对坐标 × 屏幕尺寸 - 偏移量</text>
  <text x="940" y="950" class="flow-text">• <tspan style="color: #f39c12;">偏移量-10：</tspan>让视点圆心对准实际注视点</tspan>
  <text x="940" y="965" class="flow-text">• <tspan style="color: #f39c12;">实时更新：</tspan>30fps频率更新，跟随眼动变化</text>

  <!-- 第五部分：显示层级和权限 -->
  <text x="70" y="1000" class="layer-title">🔐 显示层级和权限</text>
  
  <text x="90" y="1025" class="flow-text" style="font-weight: bold;">1. 显示层级</text>
  <text x="110" y="1045" class="flow-text">• <tspan style="color: #9b59b6;">最顶层显示：</tspan>TYPE_APPLICATION_OVERLAY确保在所有应用之上</text>
  <text x="110" y="1060" class="flow-text">• <tspan style="color: #9b59b6;">系统级窗口：</tspan>不受应用切换影响，始终可见</text>
  <text x="110" y="1075" class="flow-text">• <tspan style="color: #9b59b6;">穿透点击：</tspan>不拦截用户操作，不影响正常使用</text>
  <text x="110" y="1090" class="flow-text">• <tspan style="color: #9b59b6;">全屏覆盖：</tspan>可以显示在状态栏、导航栏之上</text>
  
  <text x="90" y="1115" class="flow-text" style="font-weight: bold;">2. 权限要求</text>
  <text x="110" y="1135" class="flow-text">• <tspan style="color: #9b59b6;">SYSTEM_ALERT_WINDOW：</tspan>悬浮窗权限</text>
  <text x="110" y="1150" class="flow-text">• <tspan style="color: #9b59b6;">用户授权：</tspan>需要用户在设置中手动开启</text>
  <text x="110" y="1165" class="flow-text">• <tspan style="color: #9b59b6;">动态检查：</tspan>运行时检查权限状态</text>
  <text x="110" y="1180" class="flow-text">• <tspan style="color: #9b59b6;">安全限制：</tspan>Android系统对悬浮窗有严格限制</text>

  <!-- 第六部分：使用场景 -->
  <text x="900" y="1000" class="layer-title">🎯 使用场景</text>
  
  <text x="920" y="1025" class="flow-text" style="font-weight: bold;">1. 眼动追踪反馈</text>
  <text x="940" y="1045" class="flow-text">• <tspan style="color: #9b59b6;">实时视点显示：</tspan>让用户看到当前注视位置</text>
  <text x="940" y="1060" class="flow-text">• <tspan style="color: #9b59b6;">追踪精度验证：</tspan>验证眼动追踪的准确性</text>
  <text x="940" y="1075" class="flow-text">• <tspan style="color: #9b59b6;">校准辅助：</tspan>帮助用户进行眼动校准</text>
  <text x="940" y="1090" class="flow-text">• <tspan style="color: #9b59b6;">调试工具：</tspan>开发和调试时的可视化工具</text>
  
  <text x="920" y="1115" class="flow-text" style="font-weight: bold;">2. 医疗应用</text>
  <text x="940" y="1135" class="flow-text">• <tspan style="color: #9b59b6;">治疗监控：</tspan>遮盖疗法过程中的视点监控</text>
  <text x="940" y="1150" class="flow-text">• <tspan style="color: #9b59b6;">行为分析：</tspan>分析用户的注视行为模式</text>
  <text x="940" y="1165" class="flow-text">• <tspan style="color: #9b59b6;">数据收集：</tspan>收集眼动数据用于医疗分析</text>
  <text x="940" y="1180" class="flow-text">• <tspan style="color: #9b59b6;">用户引导：</tspan>引导用户正确使用设备</text>

  <!-- 总结 -->
  <rect x="70" y="1210" width="1560" height="120" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1235" class="layer-title">🌟 DotView显示位置总结</text>
  
  <text x="90" y="1260" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">显示位置：</tspan>系统级悬浮窗，显示在所有应用界面的最顶层</text>
  <text x="90" y="1280" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">技术实现：</tspan>WindowManager + TYPE_APPLICATION_OVERLAY + 20x20px绿色圆点</text>
  <text x="90" y="1300" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">坐标转换：</tspan>眼动相对坐标[0,1] → 屏幕绝对像素坐标，实时30fps更新</text>
  <text x="90" y="1320" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">用户体验：</tspan>半透明、不可点击、穿透触摸，不干扰正常操作但提供清晰的视点反馈</text>

</svg>
