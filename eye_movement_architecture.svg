<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #27ae60; }
      .activity-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .fragment-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .view-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .xml-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">眼球运动评估模块架构详解</text>

  <!-- 主Activity -->
  <rect x="450" y="60" width="300" height="80" class="activity-box" rx="8"/>
  <text x="600" y="85" text-anchor="middle" class="subtitle">EyeMovementEvaluateActivity</text>
  <text x="600" y="105" text-anchor="middle" class="text">眼球运动评估主页</text>
  <text x="460" y="125" class="code">setContentView(R.layout.activity_eye_movement_evaluate)</text>

  <!-- XML布局 -->
  <rect x="50" y="180" width="280" height="120" class="xml-box" rx="8"/>
  <text x="190" y="200" text-anchor="middle" class="subtitle">activity_eye_movement_evaluate.xml</text>
  <text x="60" y="220" class="code">&lt;ConstraintLayout&gt;</text>
  <text x="70" y="235" class="code">  &lt;TextView id="tv_gaze_stability"/&gt;</text>
  <text x="70" y="250" class="code">  &lt;TextView id="tv_follow_ability"/&gt;</text>
  <text x="70" y="265" class="code">  &lt;TextView id="tv_saccade_ability"/&gt;</text>
  <text x="70" y="280" class="code">  &lt;TextView id="tv_roi_detection"/&gt;</text>
  <text x="60" y="295" class="code">&lt;/ConstraintLayout&gt;</text>

  <!-- 扫视能力评估详细流程 -->
  <rect x="400" y="180" width="350" height="60" class="activity-box" rx="8"/>
  <text x="575" y="200" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateActivity</text>
  <text x="575" y="220" text-anchor="middle" class="text">容器Activity (FrameLayout)</text>

  <!-- Fragment切换 -->
  <rect x="380" y="270" width="180" height="60" class="fragment-box" rx="8"/>
  <text x="470" y="290" text-anchor="middle" class="subtitle">ExplainFragment</text>
  <text x="470" y="310" text-anchor="middle" class="text">说明页面</text>

  <rect x="590" y="270" width="180" height="60" class="fragment-box" rx="8"/>
  <text x="680" y="290" text-anchor="middle" class="subtitle">EvaluatingFragment</text>
  <text x="680" y="310" text-anchor="middle" class="text">评估页面</text>

  <!-- 自定义View -->
  <rect x="800" y="180" width="350" height="150" class="view-box" rx="8"/>
  <text x="975" y="200" text-anchor="middle" class="subtitle">Canvas绘制自定义View</text>
  <text x="810" y="220" class="code">SaccadeAbilityEvaluatingView:</text>
  <text x="820" y="235" class="code">- 显示目标点 (ImageView)</text>
  <text x="820" y="250" class="code">- 动态位置更新</text>
  <text x="810" y="270" class="code">SaccadeAbilityEvaluateResultView:</text>
  <text x="820" y="285" class="code">- onDraw(canvas) 绘制轨迹</text>
  <text x="820" y="300" class="code">- canvas.drawPath(gazePath, paint)</text>
  <text x="820" y="315" class="code">- canvas.drawCircle(x, y, radius, paint)</text>

  <!-- Fragment XML -->
  <rect x="50" y="360" width="280" height="100" class="xml-box" rx="8"/>
  <text x="190" y="380" text-anchor="middle" class="subtitle">fragment_saccade_ability_evaluating.xml</text>
  <text x="60" y="400" class="code">&lt;ConstraintLayout&gt;</text>
  <text x="70" y="415" class="code">  &lt;TextView id="tv_time"/&gt;</text>
  <text x="70" y="430" class="code">  &lt;SaccadeAbilityEvaluatingView/&gt;</text>
  <text x="70" y="445" class="code">  &lt;ConstraintLayout id="cl_count_down"/&gt;</text>
  <text x="60" y="460" class="code">&lt;/ConstraintLayout&gt;</text>

  <!-- 数据流程 -->
  <rect x="400" y="360" width="350" height="120" class="activity-box" rx="8"/>
  <text x="575" y="380" text-anchor="middle" class="subtitle">数据流程</text>
  <text x="410" y="400" class="code">1. Fragment与GazeTrackService通信</text>
  <text x="410" y="415" class="code">2. 接收视线轨迹数据 (GazeTrajectory)</text>
  <text x="410" y="430" class="code">3. 生成目标点序列 (targetPointsList)</text>
  <text x="410" y="445" class="code">4. 通过LiveEventBus传递结果</text>
  <text x="410" y="460" class="code">5. ResultActivity接收并绘制</text>

  <!-- Canvas绘制详解 -->
  <rect x="800" y="360" width="350" height="120" class="view-box" rx="8"/>
  <text x="975" y="380" text-anchor="middle" class="subtitle">Canvas绘制核心代码</text>
  <text x="810" y="400" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="820" y="415" class="code">  canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="820" y="430" class="code">  gazePoints.forEach { point -></text>
  <text x="830" y="445" class="code">    canvas.drawCircle(x, y, radius, paint)</text>
  <text x="820" y="460" class="code">  }</text>
  <text x="810" y="475" class="code">}</text>

  <!-- 架构特点 -->
  <rect x="50" y="520" width="1100" height="120" class="xml-box" rx="8"/>
  <text x="600" y="540" text-anchor="middle" class="subtitle">架构设计特点</text>
  <text x="60" y="560" class="text">1. <tspan class="code">主Activity + Fragment容器</tspan>: 每个评估模块使用独立Activity作为容器，内部通过Fragment切换不同页面</text>
  <text x="60" y="580" class="text">2. <tspan class="code">自定义View + Canvas绘制</tspan>: 继承FrameLayout/View，重写onDraw方法实现复杂图形绘制</text>
  <text x="60" y="600" class="text">3. <tspan class="code">Service通信</tspan>: Fragment通过Message与GazeTrackService通信，获取实时眼动数据</text>
  <text x="60" y="620" class="text">4. <tspan class="code">数据传递</tspan>: 使用LiveEventBus在Fragment和ResultActivity之间传递评估结果数据</text>

  <!-- 连接线 -->
  <line x1="600" y1="140" x2="600" y2="180" class="arrow"/>
  <line x1="330" y1="240" x2="400" y2="210" class="arrow"/>
  <line x1="575" y1="240" x2="470" y2="270" class="arrow"/>
  <line x1="575" y1="240" x2="680" y2="270" class="arrow"/>
  <line x1="750" y1="210" x2="800" y2="210" class="arrow"/>
  <line x1="330" y1="410" x2="400" y2="420" class="arrow"/>
  <line x1="750" y1="420" x2="800" y2="420" class="arrow"/>

</svg>
