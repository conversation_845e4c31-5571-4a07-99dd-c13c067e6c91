<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义样式 -->
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .message-text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .note-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .phase-text { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #e74c3c; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #3498db; }
      .detail-text { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
    </style>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadReturn" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    
    <!-- 渐变色 -->
    <linearGradient id="actorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bdc3c7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="phaseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed7d7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1400" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">眼动校准时序图 - 完整流程详解</text>
  
  <!-- 参与者 -->
  <g id="actors">
    <!-- CalibrationActivity -->
    <rect x="50" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="125" y="95" text-anchor="middle" class="actor-title">CalibrationActivity</text>
    <text x="125" y="110" text-anchor="middle" class="note-text">校准控制器</text>
    
    <!-- GazeTrackService -->
    <rect x="250" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="325" y="95" text-anchor="middle" class="actor-title">GazeTrackService</text>
    <text x="325" y="110" text-anchor="middle" class="note-text">眼动追踪服务</text>
    
    <!-- TrackingManager -->
    <rect x="450" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="525" y="95" text-anchor="middle" class="actor-title">TrackingManager</text>
    <text x="525" y="110" text-anchor="middle" class="note-text">追踪管理器</text>
    
    <!-- PostureCalibrationView -->
    <rect x="650" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="725" y="95" text-anchor="middle" class="actor-title">PostureCalibrationView</text>
    <text x="725" y="110" text-anchor="middle" class="note-text">姿势校准视图</text>
    
    <!-- VisualCalibrationView -->
    <rect x="850" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="925" y="95" text-anchor="middle" class="actor-title">VisualCalibrationView</text>
    <text x="925" y="110" text-anchor="middle" class="note-text">视标校准视图</text>
    
    <!-- GazeTrack (Native) -->
    <rect x="1050" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="1125" y="95" text-anchor="middle" class="actor-title">GazeTrack</text>
    <text x="1125" y="110" text-anchor="middle" class="note-text">C++原生引擎</text>
    
    <!-- CalibrationVM -->
    <rect x="1250" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="1325" y="95" text-anchor="middle" class="actor-title">CalibrationVM</text>
    <text x="1325" y="110" text-anchor="middle" class="note-text">校准数据模型</text>
    
    <!-- WebSocket Client -->
    <rect x="1450" y="70" width="150" height="60" rx="5" fill="url(#actorGradient)" stroke="#34495e" stroke-width="1"/>
    <text x="1525" y="95" text-anchor="middle" class="actor-title">WSClient</text>
    <text x="1525" y="110" text-anchor="middle" class="note-text">WebSocket客户端</text>
  </g>
  
  <!-- 生命线 -->
  <g id="lifelines">
    <line x1="125" y1="130" x2="125" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="325" y1="130" x2="325" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="525" y1="130" x2="525" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="725" y1="130" x2="725" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="925" y1="130" x2="925" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="1125" y1="130" x2="1125" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="1325" y1="130" x2="1325" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="1525" y1="130" x2="1525" y2="1350" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 阶段1: 初始化阶段 -->
  <rect x="30" y="160" width="1540" height="40" rx="5" fill="url(#phaseGradient)" stroke="#e74c3c" stroke-width="1"/>
  <text x="800" y="185" text-anchor="middle" class="phase-text">阶段1: 初始化阶段 (onCreate)</text>
  
  <!-- 1.1 启动服务 -->
  <line x1="125" y1="220" x2="325" y2="220" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="225" y="215" text-anchor="middle" class="message-text">startForegroundService(GazeTrackService)</text>
  
  <!-- 1.2 连接WebSocket -->
  <line x1="125" y1="250" x2="1525" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="825" y="245" text-anchor="middle" class="message-text">mCalibrationWSClient.connect() (延迟2秒)</text>
  
  <!-- 1.3 显示姿势校准界面 -->
  <line x1="125" y1="280" x2="725" y2="280" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="425" y="275" text-anchor="middle" class="message-text">showPostureCalibration()</text>
  
  <!-- 阶段2: 姿势校准阶段 -->
  <rect x="30" y="320" width="1540" height="40" rx="5" fill="url(#phaseGradient)" stroke="#e74c3c" stroke-width="1"/>
  <text x="800" y="345" text-anchor="middle" class="phase-text">阶段2: 姿势校准阶段 (Posture Calibration)</text>
  
  <!-- 2.1 开启摄像头 -->
  <line x1="125" y1="380" x2="325" y2="380" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="225" y="375" text-anchor="middle" class="message-text">turnOnCamera()</text>
  
  <!-- 2.2 启动姿势校准 -->
  <line x1="125" y1="410" x2="325" y2="410" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="225" y="405" text-anchor="middle" class="message-text">MSG_START_POSTURE_CALIBRATION</text>
  
  <line x1="325" y1="440" x2="525" y2="440" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="425" y="435" text-anchor="middle" class="message-text">startPostureCalibration(isCorrection)</text>
  
  <line x1="525" y1="470" x2="1125" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="825" y="465" text-anchor="middle" class="message-text">gazeTrack.startPostureCalibration()</text>
  
  <!-- 2.3 姿势检测循环 -->
  <rect x="1050" y="500" width="150" height="80" rx="5" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
  <text x="1125" y="520" text-anchor="middle" class="step-text">姿势检测循环</text>
  <text x="1125" y="540" text-anchor="middle" class="detail-text">• 人脸检测</text>
  <text x="1125" y="555" text-anchor="middle" class="detail-text">• 眼部位置计算</text>
  <text x="1125" y="570" text-anchor="middle" class="detail-text">• 姿势对齐判断</text>
  
  <!-- 2.4 姿势结果回调 -->
  <line x1="1125" y1="600" x2="1325" y2="600" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadReturn)"/>
  <text x="1225" y="595" text-anchor="middle" class="message-text">PostureCalibrationResult</text>
  
  <line x1="1325" y1="630" x2="125" y2="630" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadReturn)"/>
  <text x="725" y="625" text-anchor="middle" class="message-text">postureCalibrationResultLivedata.observe()</text>
  
  <line x1="125" y1="660" x2="725" y2="660" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="425" y="655" text-anchor="middle" class="message-text">setPostureCorrectionResult(result)</text>
  
  <!-- 阶段3: 视标校准阶段 -->
  <rect x="30" y="700" width="1540" height="40" rx="5" fill="url(#phaseGradient)" stroke="#e74c3c" stroke-width="1"/>
  <text x="800" y="725" text-anchor="middle" class="phase-text">阶段3: 视标校准阶段 (Visual Calibration)</text>
  
  <!-- 3.1 姿势校准成功，切换到视标校准 -->
  <line x1="125" y1="760" x2="925" y2="760" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="525" y="755" text-anchor="middle" class="message-text">showVisualCalibration()</text>
  
  <!-- 3.2 启动视标校准 -->
  <line x1="125" y1="790" x2="325" y2="790" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="225" y="785" text-anchor="middle" class="message-text">MSG_START_VISUAL_CALIBRATION</text>
  
  <line x1="325" y1="820" x2="525" y2="820" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="425" y="815" text-anchor="middle" class="message-text">startVisualCalibration()</text>
  
  <line x1="525" y1="850" x2="1125" y2="850" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="825" y="845" text-anchor="middle" class="message-text">gazeTrack.startVisualCalibration()</text>
  
  <!-- 3.3 视标校准循环 -->
  <rect x="1050" y="880" width="150" height="100" rx="5" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
  <text x="1125" y="900" text-anchor="middle" class="step-text">视标校准循环</text>
  <text x="1125" y="920" text-anchor="middle" class="detail-text">• 显示校准点</text>
  <text x="1125" y="935" text-anchor="middle" class="detail-text">• 眼动数据采集</text>
  <text x="1125" y="950" text-anchor="middle" class="detail-text">• 校准精度计算</text>
  <text x="1125" y="965" text-anchor="middle" class="detail-text">• 左右眼进度更新</text>
  
  <!-- 3.4 校准坐标更新 -->
  <line x1="1125" y1="1000" x2="1525" y2="1000" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="1325" y="995" text-anchor="middle" class="message-text">CalibrateCoordinate</text>
  
  <line x1="1525" y1="1030" x2="125" y2="1030" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadReturn)"/>
  <text x="825" y="1025" text-anchor="middle" class="message-text">calibrateCoordinateLivedata.observe()</text>
  
  <line x1="125" y1="1060" x2="925" y2="1060" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="525" y="1055" text-anchor="middle" class="message-text">setCalibrateCoordinate(coordinate)</text>
  
  <!-- 3.5 校准结果反馈 -->
  <line x1="1125" y1="1090" x2="1325" y2="1090" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadReturn)"/>
  <text x="1225" y="1085" text-anchor="middle" class="message-text">CalibrationResult</text>
  
  <line x1="1325" y1="1120" x2="925" y2="1120" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadReturn)"/>
  <text x="1125" y="1115" text-anchor="middle" class="message-text">calibrationResultLivedata</text>
  
  <!-- 阶段4: 校准完成阶段 -->
  <rect x="30" y="1160" width="1540" height="40" rx="5" fill="url(#phaseGradient)" stroke="#e74c3c" stroke-width="1"/>
  <text x="800" y="1185" text-anchor="middle" class="phase-text">阶段4: 校准完成阶段</text>
  
  <!-- 4.1 校准完成回调 -->
  <line x1="925" y1="1220" x2="125" y2="1220" stroke="#28a745" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="525" y="1215" text-anchor="middle" class="message-text">onCalibrationComplete(result)</text>
  
  <!-- 4.2 启动追踪或关闭摄像头 -->
  <line x1="125" y1="1250" x2="325" y2="1250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="225" y="1245" text-anchor="middle" class="message-text">startTrack() / turnOffCamera()</text>
  
  <!-- 4.3 上传云端 -->
  <line x1="125" y1="1280" x2="1525" y2="1280" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="825" y="1275" text-anchor="middle" class="message-text">startUploadCloud()</text>
  
  <!-- 4.4 完成校准 -->
  <line x1="125" y1="1310" x2="125" y2="1310" stroke="#28a745" stroke-width="3"/>
  <circle cx="125" cy="1310" r="5" fill="#28a745"/>
  <text x="135" y="1315" class="message-text">calibrationComplete() & finish()</text>

  <!-- 详细说明区域 -->
  <rect x="50" y="1360" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1385" text-anchor="middle" class="phase-text">眼动校准详细流程说明</text>

  <!-- 左侧：校准模式说明 -->
  <text x="70" y="1410" class="step-text">校准模式 (CalibrationMode)</text>
  <text x="90" y="1430" class="detail-text">• CALIBRATION: 完整校准流程 (姿势校准 → 视标校准)</text>
  <text x="90" y="1445" class="detail-text">• POSTURE: 仅姿势校准 (用于治疗过程中的姿势矫正)</text>

  <text x="70" y="1470" class="step-text">姿势校准核心逻辑</text>
  <text x="90" y="1490" class="detail-text">• 人脸检测: 检测用户面部是否在合适位置</text>
  <text x="90" y="1505" class="detail-text">• 眼部定位: 计算左右眼在屏幕上的坐标位置</text>
  <text x="90" y="1520" class="detail-text">• 距离计算: 判断用户与屏幕的距离是否合适</text>
  <text x="90" y="1535" class="detail-text">• 姿势对齐: aligned字段表示姿势是否正确</text>
  <text x="90" y="1550" class="detail-text">• 实时反馈: 绿色头像表示正确，红色表示需要调整</text>

  <text x="70" y="1575" class="step-text">视标校准核心逻辑</text>
  <text x="90" y="1595" class="detail-text">• 多点校准: 在屏幕不同位置显示校准点</text>
  <text x="90" y="1610" class="detail-text">• 眼动采集: 用户注视校准点时采集眼动数据</text>
  <text x="90" y="1625" class="detail-text">• 精度计算: leftConsistNum/rightConsistNum表示校准进度</text>
  <text x="90" y="1640" class="detail-text">• 动画反馈: 校准点旋转表示正在校准，爆炸表示完成</text>
  <text x="90" y="1655" class="detail-text">• 完成条件: calibFinish=true时校准完成</text>

  <!-- 右侧：数据结构说明 -->
  <text x="920" y="1410" class="step-text">关键数据结构</text>

  <rect x="940" y="1420" width="350" height="120" rx="5" fill="#f8f9fa" stroke="#6c757d" stroke-width="1"/>
  <text x="1115" y="1440" text-anchor="middle" class="step-text">PostureCalibrationResult</text>
  <text x="950" y="1460" class="detail-text">• state: Boolean - 校准是否成功</text>
  <text x="950" y="1475" class="detail-text">• leftX, leftY: Float - 左眼坐标 [0,1]</text>
  <text x="950" y="1490" class="detail-text">• rightX, rightY: Float - 右眼坐标 [0,1]</text>
  <text x="950" y="1505" class="detail-text">• dist: Float - 用户与屏幕距离</text>
  <text x="950" y="1520" class="detail-text">• aligned: Boolean - 姿势是否对齐</text>
  <text x="950" y="1535" class="detail-text">• checkPostureCorrectionResult(): 验证结果有效性</text>

  <rect x="940" y="1550" width="350" height="100" rx="5" fill="#f8f9fa" stroke="#6c757d" stroke-width="1"/>
  <text x="1115" y="1570" text-anchor="middle" class="step-text">CalibrationResult</text>
  <text x="950" y="1590" class="detail-text">• calibFinish: Boolean - 校准是否完成</text>
  <text x="950" y="1605" class="detail-text">• leftConsistNum: Int - 左眼校准进度 [0,1,2,3]</text>
  <text x="950" y="1620" class="detail-text">• rightConsistNum: Int - 右眼校准进度 [0,1,2,3]</text>
  <text x="950" y="1635" class="detail-text">• 进度≥2时开始旋转动画，表示校准中</text>

  <rect x="940" y="1660" width="350" height="80" rx="5" fill="#f8f9fa" stroke="#6c757d" stroke-width="1"/>
  <text x="1115" y="1680" text-anchor="middle" class="step-text">CalibrateCoordinate</text>
  <text x="950" y="1700" class="detail-text">• state: Boolean - 当前校准点是否完成</text>
  <text x="950" y="1715" class="detail-text">• x, y: Float - 校准点屏幕坐标 [0,1]</text>
  <text x="950" y="1730" class="detail-text">• 用于更新视标位置和播放完成动画</text>

  <!-- 底部：技术要点 -->
  <text x="70" y="1680" class="step-text">技术要点与注意事项</text>
  <text x="90" y="1700" class="detail-text">• 坐标系统: 所有坐标使用[0,1]归一化坐标，便于适配不同屏幕尺寸</text>
  <text x="90" y="1715" class="detail-text">• 异步处理: 使用lifecycleScope.launch处理校准流程，避免阻塞UI线程</text>
  <text x="1320" y="1410" class="step-text">校准流程控制</text>
  <text x="1340" y="1430" class="detail-text">• isCorrection标志区分普通校准和治疗中矫正</text>
  <text x="1340" y="1445" class="detail-text">• 校准完成后根据模式选择启动追踪或关闭摄像头</text>
  <text x="1340" y="1460" class="detail-text">• WebSocket用于与云端服务通信上传校准数据</text>
  <text x="1340" y="1475" class="detail-text">• 音频提示引导用户完成校准操作</text>
</svg>
