<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变色 -->
  <defs>
    <linearGradient id="inputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="modelGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    眼动追踪系统完整处理流程
  </text>

  <!-- 第一层：数据输入层 -->
  <rect x="50" y="60" width="1700" height="120" rx="10" fill="url(#inputGrad)" stroke="#2196f3" stroke-width="2"/>
  <text x="900" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1976d2">
    数据输入层 - 多模态传感器数据采集
  </text>

  <!-- 红外摄像头 -->
  <rect x="100" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="200" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">红外摄像头</text>
  <text x="200" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">640×480@50FPS</text>

  <!-- RGB摄像头 -->
  <rect x="320" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="420" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">RGB摄像头</text>
  <text x="420" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">1920×1080@30FPS</text>

  <!-- 数据预处理 -->
  <rect x="540" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="640" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">数据预处理</text>
  <text x="640" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">格式转换/归一化</text>

  <!-- 数据同步 -->
  <rect x="760" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="860" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">时间同步</text>
  <text x="860" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">多传感器对齐</text>

  <!-- 质量检测 -->
  <rect x="980" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="1080" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">质量检测</text>
  <text x="1080" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">光照/清晰度</text>

  <!-- 缓存管理 -->
  <rect x="1200" y="110" width="200" height="50" rx="5" fill="#fff" stroke="#2196f3" stroke-width="2"/>
  <text x="1300" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">缓存管理</text>
  <text x="1300" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">环形缓冲区</text>

  <!-- 数据流箭头 -->
  <path d="M 200 170 L 200 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 420 170 L 420 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 640 170 L 640 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 860 170 L 860 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1080 170 L 1080 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1300 170 L 1300 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第二层：AI模型推理层 -->
  <rect x="50" y="200" width="1700" height="180" rx="10" fill="url(#modelGrad)" stroke="#9c27b0" stroke-width="2"/>
  <text x="900" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#7b1fa2">
    AI模型推理层 - 深度学习模型并行处理
  </text>

  <!-- 人脸检测模型 -->
  <rect x="100" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="190" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">人脸检测</text>
  <text x="190" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">RKNN模型</text>
  <text x="190" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: 640×480</text>
  <text x="190" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 人脸区域</text>
  <text x="190" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 8ms</text>

  <!-- 眼部追踪模型 -->
  <rect x="300" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="390" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">眼部追踪</text>
  <text x="390" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">U-Net分割</text>
  <text x="390" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: 眼部ROI</text>
  <text x="390" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 虹膜掩码</text>
  <text x="390" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 6ms</text>

  <!-- 光点检测模型 -->
  <rect x="500" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="590" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">光点检测</text>
  <text x="590" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">热图回归</text>
  <text x="590" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: 眼部图像</text>
  <text x="590" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 普尔钦斑</text>
  <text x="590" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 5ms</text>

  <!-- 瞳孔分割模型 -->
  <rect x="700" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="790" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">瞳孔分割</text>
  <text x="790" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">MobileOne</text>
  <text x="790" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: 虹膜区域</text>
  <text x="790" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 瞳孔中心</text>
  <text x="790" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 4ms</text>

  <!-- 姿态估计模型 -->
  <rect x="900" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="990" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">姿态估计</text>
  <text x="990" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">SCRFD+SimplePose</text>
  <text x="990" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: RGB图像</text>
  <text x="990" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 头部姿态</text>
  <text x="990" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 12ms</text>

  <!-- 行为监测模型 -->
  <rect x="1100" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="1190" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">行为监测</text>
  <text x="1190" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">NCNN框架</text>
  <text x="1190" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输入: 面部特征</text>
  <text x="1190" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">输出: 行为状态</text>
  <text x="1190" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">推理时间: 7ms</text>

  <!-- 模型并行处理指示 -->
  <rect x="1320" y="250" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="1410" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">并行调度</text>
  <text x="1410" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">NPU加速</text>
  <text x="1410" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">6TOPS算力</text>
  <text x="1410" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">INT8量化</text>
  <text x="1410" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#999">总延迟: <20ms</text>

  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>

  <!-- 模型间数据流 -->
  <path d="M 190 360 L 190 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 390 360 L 390 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 590 360 L 590 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 790 360 L 790 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 990 360 L 990 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1190 360 L 1190 400" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第三层：C++数据处理层 -->
  <rect x="50" y="400" width="1700" height="200" rx="10" fill="url(#processGrad)" stroke="#4caf50" stroke-width="2"/>
  <text x="900" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2e7d32">
    C++数据处理层 - 算法实现与数据融合
  </text>

  <!-- 热图处理 -->
  <rect x="100" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="220" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">热图峰值检测</text>
  <text x="220" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• cv::minMaxLoc() 寻找最大值</text>
  <text x="220" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 高斯拟合亚像素定位</text>
  <text x="220" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 置信度 = max_val / threshold</text>
  <text x="220" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: cv::Point2f 坐标</text>
  <text x="220" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 1.2ms</text>

  <!-- 分割掩码处理 -->
  <rect x="360" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="480" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">分割掩码处理</text>
  <text x="480" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• threshold(mask, 0.6) 二值化</text>
  <text x="480" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• findContours() 轮廓提取</text>
  <text x="480" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• boundingRect() 边界框</text>
  <text x="480" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: cv::Rect 区域</text>
  <text x="480" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 0.8ms</text>

  <!-- 坐标系变换 -->
  <rect x="620" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="740" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">坐标系变换</text>
  <text x="740" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 模型坐标 → 图像坐标</text>
  <text x="740" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 仿射变换矩阵应用</text>
  <text x="740" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• undistortPoints() 畸变校正</text>
  <text x="740" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: 校正后坐标</text>
  <text x="740" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 0.5ms</text>

  <!-- 数据融合 -->
  <rect x="880" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1000" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">多点数据融合</text>
  <text x="1000" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 普尔钦斑 + 瞳孔中心</text>
  <text x="1000" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 向量计算 gaze_vector</text>
  <text x="1000" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 双眼数据平均/选择</text>
  <text x="1000" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: 注视方向向量</text>
  <text x="1000" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 0.3ms</text>

  <!-- 滤波算法 -->
  <rect x="1140" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1260" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">时序滤波算法</text>
  <text x="1260" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 滑动窗口平均滤波</text>
  <text x="1260" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 卡尔曼滤波预测</text>
  <text x="1260" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 异常值检测剔除</text>
  <text x="1260" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: 平滑轨迹</text>
  <text x="1260" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 0.4ms</text>

  <!-- 质量控制 -->
  <rect x="1400" y="450" width="240" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1520" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">质量控制算法</text>
  <text x="1520" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 置信度阈值过滤</text>
  <text x="1520" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 双眼一致性检查</text>
  <text x="1520" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 异常值剔除</text>
  <text x="1520" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 输出: 可靠数据</text>
  <text x="1520" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">处理时间: 0.2ms</text>

  <!-- 处理层数据流 -->
  <path d="M 220 580 L 220 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 480 580 L 480 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 740 580 L 740 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1000 580 L 1000 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1260 580 L 1260 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1520 580 L 1520 620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第四层：几何计算与校准层 -->
  <rect x="50" y="620" width="1700" height="160" rx="10" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
  <text x="900" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2e7d32">
    几何计算与校准层 - 距离估算与姿态校正
  </text>

  <!-- 距离计算 -->
  <rect x="100" y="670" width="280" height="80" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="240" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">距离计算算法</text>
  <text x="240" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 虹膜大小测量 (透明投影原理)</text>
  <text x="240" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• Sigmoid映射: distance = f(iris_size)</text>
  <text x="240" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">精度: ±2cm (30-80cm范围)</text>

  <!-- 姿态校正 -->
  <rect x="400" y="670" width="280" height="80" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="540" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">头部姿态校正</text>
  <text x="540" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 欧拉角计算 (pitch, yaw, roll)</text>
  <text x="540" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 3D旋转矩阵补偿</text>
  <text x="540" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">角度范围: ±30° (xyz轴)</text>

  <!-- 校准状态 -->
  <rect x="700" y="670" width="280" height="80" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="840" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">校准状态判断</text>
  <text x="840" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 多点校准验证</text>
  <text x="840" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 精度评估与反馈</text>
  <text x="840" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">校准精度: <1°视角</text>

  <!-- 坐标映射 -->
  <rect x="1000" y="670" width="280" height="80" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1140" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">屏幕坐标映射</text>
  <text x="1140" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视向量 → 屏幕坐标</text>
  <text x="1140" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 多分辨率适配</text>
  <text x="1140" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">支持: 1080p-4K显示</text>

  <!-- 实时反馈 -->
  <rect x="1300" y="670" width="280" height="80" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1440" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">实时反馈控制</text>
  <text x="1440" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 延迟补偿算法</text>
  <text x="1440" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 预测性跟踪</text>
  <text x="1440" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">系统延迟: <25ms</text>

  <!-- 几何层数据流 -->
  <path d="M 240 760 L 240 800" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 540 760 L 540 800" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 840 760 L 840 800" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1140 760 L 1140 800" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1440 760 L 1440 800" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第五层：应用输出层 -->
  <rect x="50" y="800" width="1700" height="180" rx="10" fill="url(#outputGrad)" stroke="#f57c00" stroke-width="2"/>
  <text x="900" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#e65100">
    应用输出层 - 眼动追踪应用与医学诊断
  </text>

  <!-- 注视稳定性测试 -->
  <rect x="100" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="200" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">注视稳定性</text>
  <text x="200" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 注视轨迹记录</text>
  <text x="200" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 稳定性指标计算</text>
  <text x="200" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 医学评估报告</text>
  <text x="200" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 眼科诊断</text>

  <!-- 扫视能力测试 -->
  <rect x="320" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="420" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">扫视能力</text>
  <text x="420" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 扫视速度测量</text>
  <text x="420" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 精确度评估</text>
  <text x="420" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 反应时间统计</text>
  <text x="420" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 神经评估</text>

  <!-- 跟踪能力测试 -->
  <rect x="540" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="640" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">跟踪能力</text>
  <text x="640" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 平滑跟踪测试</text>
  <text x="640" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 跟踪误差分析</text>
  <text x="640" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 运动预测能力</text>
  <text x="640" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 运动医学</text>

  <!-- ROI检测 -->
  <rect x="760" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="860" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">ROI检测</text>
  <text x="860" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 兴趣区域分析</text>
  <text x="860" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 注意力分布</text>
  <text x="860" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 热力图生成</text>
  <text x="860" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 认知评估</text>

  <!-- 遮盖疗法 -->
  <rect x="980" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1080" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">遮盖疗法</text>
  <text x="1080" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 实时遮盖控制</text>
  <text x="1080" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 弱视眼训练</text>
  <text x="1080" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 疗效监测</text>
  <text x="1080" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 弱视治疗</text>

  <!-- 行为监测 -->
  <rect x="1200" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1300" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">行为监测</text>
  <text x="1300" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 注意力状态</text>
  <text x="1300" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 疲劳检测</text>
  <text x="1300" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 异常行为预警</text>
  <text x="1300" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 驾驶安全</text>

  <!-- 数据记录 -->
  <rect x="1420" y="850" width="200" height="100" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1520" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">数据记录</text>
  <text x="1520" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 轨迹数据存储</text>
  <text x="1520" y="910" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 统计分析</text>
  <text x="1520" y="925" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 报告生成</text>
  <text x="1520" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#666">应用: 数据分析</text>

  <!-- 应用层数据流 -->
  <path d="M 200 960 L 200 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 420 960 L 420 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 640 960 L 640 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 860 960 L 860 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1080 960 L 1080 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1300 960 L 1300 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1520 960 L 1520 1000" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第六层：性能指标与技术参数 -->
  <rect x="50" y="1000" width="1700" height="160" rx="10" fill="#f5f5f5" stroke="#666" stroke-width="2"/>
  <text x="900" y="1030" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#333">
    性能指标与技术参数
  </text>

  <!-- 精度指标 -->
  <rect x="100" y="1050" width="280" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="240" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">精度指标</text>
  <text x="240" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视点精度: <1°视角</text>
  <text x="240" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 虹膜检测IoU: 95.1%</text>
  <text x="240" y="1125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 瞳孔分割IoU: 97.2%</text>

  <!-- 性能指标 -->
  <rect x="400" y="1050" width="280" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="540" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">性能指标</text>
  <text x="540" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 处理帧率: 50FPS</text>
  <text x="540" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 端到端延迟: <25ms</text>
  <text x="540" y="1125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• CPU占用率: <15%</text>

  <!-- 硬件规格 -->
  <rect x="700" y="1050" width="280" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="840" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">硬件规格</text>
  <text x="840" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• NPU算力: 6TOPS</text>
  <text x="840" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 内存占用: <512MB</text>
  <text x="840" y="1125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 功耗: <3W</text>

  <!-- 适用范围 -->
  <rect x="1000" y="1050" width="280" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="1140" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">适用范围</text>
  <text x="1140" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 工作距离: 30-80cm</text>
  <text x="1140" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 头部角度: ±30°</text>
  <text x="1140" y="1125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 环境光照: 50-1000lux</text>

  <!-- 系统特性 -->
  <rect x="1300" y="1050" width="280" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="1440" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">系统特性</text>
  <text x="1440" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 免校准启动</text>
  <text x="1440" y="1110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 多用户支持</text>
  <text x="1440" y="1125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 实时自适应</text>

  <!-- 数据流程总结 -->
  <rect x="50" y="1180" width="1700" height="80" rx="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
  <text x="900" y="1210" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976d2">
    完整数据流程: 图像采集 → AI模型推理 → C++算法处理 → 几何计算校准 → 应用输出
  </text>
  <text x="900" y="1235" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#333">
    总处理时间: <25ms | 系统精度: <1°视角 | 支持应用: 医学诊断、行为监测、遮盖疗法等
  </text>
  <text x="900" y="1250" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    技术栈: RKNN深度学习推理 + OpenCV图像处理 + C++高性能计算 + 实时几何算法
  </text>

</svg>
