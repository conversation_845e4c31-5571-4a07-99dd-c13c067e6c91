<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="inputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4caf50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81c784;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="rknnGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8a65;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="ncnnGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9c27b0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ba68c8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="outputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f57c00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb74d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="fusionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#607d8b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90a4ae;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 标题 -->
  <rect x="0" y="0" width="1800" height="80" fill="url(#headerGrad)"/>
  <text x="900" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#fff">
    眼动追踪与姿势检测模型架构图
  </text>
  
  <!-- 输入层 -->
  <rect x="50" y="100" width="1700" height="120" rx="10" fill="url(#inputGrad)" stroke="#4caf50" stroke-width="2"/>
  <text x="900" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">
    输入层 - 图像数据流
  </text>
  
  <!-- 相机输入 -->
  <rect x="100" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="200" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">红外相机</text>
  <text x="200" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">4048×3040 单通道</text>
  
  <!-- 预处理 -->
  <rect x="350" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="450" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">图像预处理</text>
  <text x="450" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">灰度化、降噪</text>
  
  <!-- LED光源 -->
  <rect x="600" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="700" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">红外LED光源</text>
  <text x="700" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">角膜反射增强</text>
  
  <!-- 数据流向 -->
  <rect x="850" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="950" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">数据流分发</text>
  <text x="950" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">→ 眼动追踪</text>
  
  <!-- 行为监测输入 -->
  <rect x="1100" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1200" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">RGB相机</text>
  <text x="1200" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">→ 行为监测</text>
  
  <!-- 同步机制 -->
  <rect x="1350" y="150" width="200" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="2"/>
  <text x="1450" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4caf50">时间同步</text>
  <text x="1450" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">多模态数据对齐</text>
  
  <!-- RKNN模型层 -->
  <rect x="50" y="240" width="1200" height="300" rx="10" fill="url(#rknnGrad)" stroke="#ff5722" stroke-width="2"/>
  <text x="650" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">
    RKNN推理引擎 - 眼动追踪核心模型
  </text>
  
  <!-- 人脸检测模型 -->
  <rect x="100" y="290" width="250" height="120" rx="5" fill="#fff" stroke="#ff5722" stroke-width="2"/>
  <text x="225" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff5722">人脸检测模型</text>
  <text x="225" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">face_keypoints_mobileone</text>
  <text x="225" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输入: 224×160</text>
  <text x="225" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 双眼区域边界框</text>
  <text x="225" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: MobileOne + 热图回归</text>
  <text x="225" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">精度: 检测率>95%</text>
  
  <!-- 眼部追踪模型 -->
  <rect x="370" y="290" width="250" height="120" rx="5" fill="#fff" stroke="#ff5722" stroke-width="2"/>
  <text x="495" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff5722">眼部追踪模型</text>
  <text x="495" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">nir_eye_tracking_iris</text>
  <text x="495" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输入: 160×128</text>
  <text x="495" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 虹膜分割掩码</text>
  <text x="495" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: MobileOne + U-Net分割</text>
  <text x="495" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">精度: IoU=95.1%</text>
  
  <!-- 光点检测模型 -->
  <rect x="640" y="290" width="250" height="120" rx="5" fill="#fff" stroke="#ff5722" stroke-width="2"/>
  <text x="765" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff5722">光点检测模型</text>
  <text x="765" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">pupil_nir_2lights_keypoints</text>
  <text x="765" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输入: 128×128</text>
  <text x="765" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 普尔钦斑坐标</text>
  <text x="765" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: 关键点检测 + 热图</text>
  <text x="765" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">精度: 亚像素级定位</text>
  
  <!-- 瞳孔分割模型 -->
  <rect x="910" y="290" width="250" height="120" rx="5" fill="#fff" stroke="#ff5722" stroke-width="2"/>
  <text x="1035" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff5722">瞳孔分割模型</text>
  <text x="1035" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">nir_refine_seg_iris_pupil</text>
  <text x="1035" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输入: 128×128</text>
  <text x="1035" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 瞳孔中心坐标</text>
  <text x="1035" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: MobileOne-S4 分割</text>
  <text x="1035" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">精度: IoU=97.2%</text>
  
  <!-- 模型特性说明 -->
  <rect x="100" y="430" width="1060" height="80" rx="5" fill="#fff" stroke="#ff5722" stroke-width="1"/>
  <text x="630" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff5722">RKNN模型特性</text>
  <text x="200" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 瑞芯微NPU硬件加速</text>
  <text x="200" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• INT8量化优化</text>
  <text x="200" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 推理延迟<20ms</text>
  
  <text x="450" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• MobileOne轻量化架构</text>
  <text x="450" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 专门针对红外图像优化</text>
  <text x="450" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 支持实时流式处理</text>
  
  <text x="700" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 多任务联合训练</text>
  <text x="700" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 端到端优化</text>
  <text x="700" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 鲁棒性强</text>
  
  <text x="950" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 内存占用<50MB</text>
  <text x="950" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 功耗优化</text>
  <text x="950" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 温度稳定性好</text>
  
  <!-- NCNN模型层 -->
  <rect x="1270" y="240" width="480" height="300" rx="10" fill="url(#ncnnGrad)" stroke="#9c27b0" stroke-width="2"/>
  <text x="1510" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">
    NCNN推理引擎 - 行为监测
  </text>
  
  <!-- SCRFD人脸检测 -->
  <rect x="1320" y="290" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="1410" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#9c27b0">SCRFD人脸检测</text>
  <text x="1410" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输入: 640×640</text>
  <text x="1410" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 人脸+关键点</text>
  <text x="1410" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: RetinaNet</text>
  <text x="1410" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">用途: 姿势分析</text>
  
  <!-- SimplePose姿势估计 -->
  <rect x="1520" y="290" width="180" height="100" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="2"/>
  <text x="1610" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#9c27b0">SimplePose</text>
  <text x="1610" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">姿势估计模型</text>
  <text x="1610" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">输出: 关键点坐标</text>
  <text x="1610" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">算法: HRNet</text>
  <text x="1610" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">用途: 行为监测</text>
  
  <!-- NCNN特性 -->
  <rect x="1320" y="410" width="380" height="80" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="1510" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#9c27b0">NCNN特性</text>
  <text x="1410" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 腾讯开源框架</text>
  <text x="1410" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• ARM CPU优化</text>
  <text x="1410" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• Vulkan GPU加速</text>
  
  <text x="1610" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 跨平台兼容</text>
  <text x="1610" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 内存高效</text>
  <text x="1610" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 实时性能好</text>

  <!-- C++数据处理层 -->
  <rect x="50" y="560" width="1700" height="360" rx="10" fill="url(#fusionGrad)" stroke="#607d8b" stroke-width="2"/>
  <text x="900" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">
    C++层数据处理与算法实现
  </text>

  <!-- 模型输出解析 -->
  <rect x="80" y="610" width="280" height="120" rx="5" fill="#fff" stroke="#607d8b" stroke-width="2"/>
  <text x="220" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#607d8b">模型输出解析</text>
  <text x="220" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• rknn_outputs_get()</text>
  <text x="220" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 热图数据提取</text>
  <text x="220" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 分割掩码解析</text>
  <text x="220" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 置信度阈值过滤</text>
  <text x="220" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">→ 原始检测结果</text>

  <!-- 几何计算算法 -->
  <rect x="380" y="610" width="280" height="120" rx="5" fill="#fff" stroke="#607d8b" stroke-width="2"/>
  <text x="520" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#607d8b">几何计算算法</text>
  <text x="520" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 热图峰值检测</text>
  <text x="520" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 亚像素级定位</text>
  <text x="520" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 坐标系转换</text>
  <text x="520" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 几何校正</text>
  <text x="520" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">→ 精确坐标</text>

  <!-- 眼动数据融合 -->
  <rect x="680" y="610" width="280" height="120" rx="5" fill="#fff" stroke="#607d8b" stroke-width="2"/>
  <text x="820" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#607d8b">眼动数据融合</text>
  <text x="820" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 普尔钦斑坐标</text>
  <text x="820" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 瞳孔中心坐标</text>
  <text x="820" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 双眼数据对齐</text>
  <text x="820" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视向量计算</text>
  <text x="820" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">→ 注视点坐标</text>

  <!-- 质量控制算法 -->
  <rect x="980" y="610" width="280" height="120" rx="5" fill="#fff" stroke="#607d8b" stroke-width="2"/>
  <text x="1120" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#607d8b">质量控制算法</text>
  <text x="1120" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 置信度阈值过滤</text>
  <text x="1120" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 双眼一致性检查</text>
  <text x="1120" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 异常值剔除</text>
  <text x="1120" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 滑动平均滤波</text>
  <text x="1120" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">→ 稳定数据</text>

  <!-- 距离与姿势计算 -->
  <rect x="1280" y="610" width="280" height="120" rx="5" fill="#fff" stroke="#607d8b" stroke-width="2"/>
  <text x="1420" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#607d8b">距离与姿势计算</text>
  <text x="1420" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 虹膜大小测量</text>
  <text x="1420" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 透明投影原理</text>
  <text x="1420" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• Sigmoid距离映射</text>
  <text x="1420" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 头部姿态估计</text>
  <text x="1420" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">→ 距离/角度</text>

  <!-- C++算法实现细节 -->
  <rect x="80" y="750" width="1580" height="150" rx="5" fill="#fff" stroke="#607d8b" stroke-width="1"/>
  <text x="870" y="775" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#607d8b">C++层核心算法实现流程</text>

  <!-- 热图处理 -->
  <rect x="100" y="790" width="300" height="100" rx="3" fill="#f8f9fa" stroke="#607d8b" stroke-width="1"/>
  <text x="250" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">1. 热图峰值检测</text>
  <text x="250" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• cv::minMaxLoc() 寻找最大值</text>
  <text x="250" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 高斯拟合亚像素定位</text>
  <text x="250" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 置信度 = max_val / threshold</text>
  <text x="250" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输出: cv::Point2f 坐标</text>

  <!-- 分割掩码处理 -->
  <rect x="420" y="790" width="300" height="100" rx="3" fill="#f8f9fa" stroke="#607d8b" stroke-width="1"/>
  <text x="570" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">2. 分割掩码处理</text>
  <text x="570" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• threshold(mask, 0.6) 二值化</text>
  <text x="570" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• findContours() 轮廓提取</text>
  <text x="570" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• boundingRect() 边界框</text>
  <text x="570" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输出: cv::Rect 区域</text>

  <!-- 几何变换 -->
  <rect x="740" y="790" width="300" height="100" rx="3" fill="#f8f9fa" stroke="#607d8b" stroke-width="1"/>
  <text x="890" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">3. 坐标系变换</text>
  <text x="890" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 模型坐标 → 图像坐标</text>
  <text x="890" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 仿射变换矩阵应用</text>
  <text x="890" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 畸变校正 undistortPoints()</text>
  <text x="890" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输出: 校正后坐标</text>

  <!-- 数据融合 -->
  <rect x="1060" y="790" width="300" height="100" rx="3" fill="#f8f9fa" stroke="#607d8b" stroke-width="1"/>
  <text x="1210" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">4. 多点数据融合</text>
  <text x="1210" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 普尔钦斑 + 瞳孔中心</text>
  <text x="1210" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 向量计算 gaze_vector</text>
  <text x="1210" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 双眼数据平均/选择</text>
  <text x="1210" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输出: 注视方向向量</text>

  <!-- 滤波算法 -->
  <rect x="1380" y="790" width="300" height="100" rx="3" fill="#f8f9fa" stroke="#607d8b" stroke-width="1"/>
  <text x="1530" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">5. 时序滤波算法</text>
  <text x="1530" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 滑动窗口平均滤波</text>
  <text x="1530" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 卡尔曼滤波预测</text>
  <text x="1530" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 异常值检测剔除</text>
  <text x="1530" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输出: 平滑轨迹</text>

  <!-- 输出应用层 -->
  <rect x="50" y="940" width="1700" height="200" rx="10" fill="url(#outputGrad)" stroke="#f57c00" stroke-width="2"/>
  <text x="900" y="970" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">
    输出应用层 - 眼动追踪应用
  </text>

  <!-- 注视稳定性测试 -->
  <rect x="100" y="990" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="225" y="1015" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">注视稳定性测试</text>
  <text x="225" y="1035" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视轨迹记录</text>
  <text x="225" y="1050" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 稳定性指标计算</text>
  <text x="225" y="1065" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 偏移量统计</text>
  <text x="225" y="1080" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 医学评估报告</text>
  <text x="225" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 眼科诊断</text>

  <!-- 扫视能力评估 -->
  <rect x="370" y="830" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="495" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">扫视能力评估</text>
  <text x="495" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 扫视轨迹分析</text>
  <text x="495" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 反应时间测量</text>
  <text x="495" y="905" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 精度评估</text>
  <text x="495" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 运动参数分析</text>
  <text x="495" y="935" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 神经评估</text>

  <!-- 追踪能力测试 -->
  <rect x="640" y="830" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="765" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">追踪能力测试</text>
  <text x="765" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 平滑追踪记录</text>
  <text x="765" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 跟踪误差分析</text>
  <text x="765" y="905" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 速度适应性</text>
  <text x="765" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 协调性评估</text>
  <text x="765" y="935" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 运动评估</text>

  <!-- ROI检测分析 -->
  <rect x="910" y="830" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1035" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">ROI检测分析</text>
  <text x="1035" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 兴趣区域定义</text>
  <text x="1035" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视热图生成</text>
  <text x="1035" y="905" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 覆盖率统计</text>
  <text x="1035" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 认知能力评估</text>
  <text x="1035" y="935" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 认知测试</text>

  <!-- 行为监测报告 -->
  <rect x="1180" y="830" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1305" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">行为监测报告</text>
  <text x="1305" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注意力分析</text>
  <text x="1305" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 姿势合规性</text>
  <text x="1305" y="905" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 疲劳检测</text>
  <text x="1305" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 异常行为预警</text>
  <text x="1305" y="935" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 安全监控</text>

  <!-- 数据存储与分析 -->
  <rect x="1450" y="830" width="250" height="120" rx="5" fill="#fff" stroke="#f57c00" stroke-width="2"/>
  <text x="1575" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f57c00">数据存储与分析</text>
  <text x="1575" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 轨迹数据存储</text>
  <text x="1575" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 统计分析</text>
  <text x="1575" y="905" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 趋势预测</text>
  <text x="1575" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 报告生成</text>
  <text x="1575" y="935" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">应用: 数据分析</text>

  <!-- 技术指标与性能 -->
  <rect x="50" y="1160" width="1700" height="150" rx="10" fill="#f5f5f5" stroke="#666" stroke-width="2"/>
  <text x="900" y="1190" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    技术指标与性能参数
  </text>

  <!-- 精度指标 -->
  <rect x="100" y="1210" width="300" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="250" y="1230" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">精度指标</text>
  <text x="250" y="1245" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 注视点精度: <1°视角</text>
  <text x="250" y="1260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 虹膜检测IoU: 95.1%</text>
  <text x="250" y="1275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 瞳孔分割IoU: 97.2%</text>

  <!-- 性能指标 -->
  <rect x="420" y="1020" width="300" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="570" y="1040" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">性能指标</text>
  <text x="570" y="1055" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 推理延迟: <20ms</text>
  <text x="570" y="1070" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 帧率: 50FPS</text>
  <text x="570" y="1085" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 内存占用: <50MB</text>

  <!-- 硬件要求 -->
  <rect x="740" y="1020" width="300" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="890" y="1040" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">硬件要求</text>
  <text x="890" y="1055" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 瑞芯微RK3588芯片</text>
  <text x="890" y="1070" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• NPU算力: 6TOPS</text>
  <text x="890" y="1085" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 红外相机: 4MP</text>

  <!-- 应用场景 -->
  <rect x="1060" y="1020" width="300" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="1210" y="1040" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">应用场景</text>
  <text x="1210" y="1055" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 医疗眼科诊断</text>
  <text x="1210" y="1070" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 神经功能评估</text>
  <text x="1210" y="1085" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 行为安全监控</text>

  <!-- 技术优势 -->
  <rect x="1380" y="1020" width="300" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="1530" y="1040" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">技术优势</text>
  <text x="1530" y="1055" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 端侧实时推理</text>
  <text x="1530" y="1070" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 多模态数据融合</text>
  <text x="1530" y="1085" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">• 高精度低延迟</text>

  <!-- 数据流箭头 -->
  <!-- 输入到RKNN -->
  <line x1="950" y1="220" x2="650" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="800" y="250" font-family="Arial, sans-serif" font-size="12" fill="#333">红外图像流</text>

  <!-- 输入到NCNN -->
  <line x1="1200" y1="220" x2="1510" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="1350" y="250" font-family="Arial, sans-serif" font-size="12" fill="#333">RGB图像流</text>

  <!-- RKNN到融合层 -->
  <line x1="650" y1="540" x2="400" y2="610" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="500" y="580" font-family="Arial, sans-serif" font-size="12" fill="#333">眼动数据</text>

  <!-- NCNN到融合层 -->
  <line x1="1510" y1="540" x2="1210" y2="610" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="1360" y="580" font-family="Arial, sans-serif" font-size="12" fill="#333">姿势数据</text>

  <!-- 融合层到应用层 -->
  <line x1="900" y1="760" x2="900" y2="830" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="920" y="800" font-family="Arial, sans-serif" font-size="12" fill="#333">融合结果</text>

  <!-- 应用层到指标层 -->
  <line x1="900" y1="950" x2="900" y2="1020" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="920" y="990" font-family="Arial, sans-serif" font-size="12" fill="#333">性能评估</text>

  <!-- 模型间连接箭头 -->
  <!-- 人脸检测到眼部追踪 -->
  <line x1="350" y1="350" x2="370" y2="350" stroke="#ff5722" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 眼部追踪到光点检测 -->
  <line x1="620" y1="350" x2="640" y2="350" stroke="#ff5722" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 光点检测到瞳孔分割 -->
  <line x1="890" y1="350" x2="910" y2="350" stroke="#ff5722" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 版权信息 -->
  <text x="900" y="1380" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    MIT-DD 眼动追踪系统 - 多模态深度学习架构 © 2024
  </text>
</svg>
