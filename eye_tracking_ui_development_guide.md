# 眼动追踪项目新建UI完整指南

## 📋 目录
1. [抗压能力优化](#1-抗压能力优化)
2. [Android四大组件使用总结](#2-android四大组件使用总结)
3. [相机防重机制与背压策略](#3-相机防重机制与背压策略)
4. [新建UI完整指南](#4-新建ui完整指南)

---

## 1. 抗压能力优化

### 🎯 优化目标
将相机启动消息移至服务连接回调中，避免延迟等待，提高系统抗压能力。

### 🔧 实现方案

#### 优化前问题
```kotlin
// 问题：直接发送消息，服务连接状态不确定
override fun onStart() {
    super.onStart()
    bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    // 立即发送消息可能失败
    sendMessageToService(Message.obtain().apply {
        what = GazeConstants.MSG_TURN_ON_CAMERA
    })
}
```

#### 优化后方案
```kotlin
private val serviceConnection = object : ServiceConnection {
    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        Logger.d(TAG, msg = "onServiceConnected")
        if (service != null){
            mServiceMessage = Messenger(service)
            val message = Message.obtain().apply {
                what = GazeConstants.MSG_SERVICE_CONNECTED
                replyTo = mClientMessage
            }
            mServiceMessage?.send(message)
            
            // 优化：在服务连接成功后启动相机
            sendMessageToService(Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            })
        }
    }
}
```

### ✅ 优化效果
- 确保服务连接后再启动相机
- 避免消息丢失和重复启动
- 提高系统稳定性和响应速度

---

## 2. Android四大组件使用总结

### 📱 Activity组件
| Activity | 功能描述 | 特殊配置 |
|----------|----------|----------|
| `LauncherActivity` | 启动器Activity，根据设备类型跳转 | 启动模式：standard |
| `HomeMainActivity` | 医疗家庭版主界面容器 | Fragment管理 |
| `CalibrationActivity` | 校准Activity | `singleInstance`模式 |
| `TrainWebActivity` | 训练Web界面 | WebView集成 |
| `ReadActivity` | 阅读评估Activity | 眼动数据收集 |
| `EyeMovementEvaluateActivity` | 眼动评估主界面 | 多模块入口 |

### 🔧 Service组件
```xml
<!-- GazeTrackService：眼动追踪前台服务 -->
<service android:name=".gaze.track.GazeTrackService"
    android:exported="true"
    android:enabled="true"
    android:process=":GazeTracker"
    android:foregroundServiceType="camera"/>

<!-- DesktopService：桌面服务 -->
<service android:name=".desktop.DesktopService"
    android:exported="true"
    android:enabled="true"
    android:process=":Desktop"/>
```

### 📡 BroadcastReceiver组件
| Receiver | 功能 | 广播类型 |
|----------|------|----------|
| `BootReceiver` | 开机启动监听 | 系统广播 |
| `GlobalBootReceiver` | 眼动追踪控制 | 自定义广播 |
| `RefreshBindUserReceiver` | 用户绑定刷新 | 自定义广播 |

### 💾 ContentProvider组件
项目中未直接使用ContentProvider，数据存储采用：
- MMKV键值存储
- 文件系统存储
- 网络API数据交互
- SQLite数据库（间接使用）

---

## 3. 相机防重机制与背压策略

### 🛡️ 相机防重机制

#### 原子状态管理
```kotlin
class GTCameraManager {
    // 相机是否已启动
    private val isCameraStarted = AtomicBoolean(false)
    
    fun startCamera(context: Context, lifecycleOwner: LifecycleOwner) {
        Logger.d(TAG, msg = "startCamera isCameraStarted = ${isCameraStarted.get()}")
        if (isCameraStarted.get()) return  // 防重检查
        
        // 启动相机逻辑
        isCameraStarted.set(true)  // 设置状态
    }
}
```

#### 服务状态管理
```kotlin
object TrackingManager {
    // 眼动服务模式
    private val serviceMode = AtomicReference(NONE)
    
    enum class ServiceMode {
        NONE,                    // 无状态
        TRACK,                   // 追踪模式
        POSTURE_CALIBRATION,     // 姿势校准
        VISUAL_CALIBRATION       // 视标校准
    }
}
```

### ⚡ 背压策略

#### 图像分析配置
```kotlin
val imageAnalysis = ImageAnalysis.Builder()
    .setResolutionSelector(resolutionStrategy)
    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)  // 关键配置
    .build().apply {
        setAnalyzer(ContextCompat.getMainExecutor(context)) { image ->
            cameraListener?.onAnalyze(image)
        }
    }
```

#### 协程异步处理
```kotlin
private fun gazeTracking(image: ImageProxy) {
    mLifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
        val result = gazeTrack.gazeTracking(image)
        withContext(Dispatchers.Main) {
            externalListener?.onGazeTracking(result)
            isSkewing.set(result.skew)
            image.close()  // 及时释放资源
        }
    }
}
```

---

## 4. 新建UI完整指南

### 🎨 UI开发流程

#### 第一步：需求分析与设计
1. **功能需求分析**
   - 明确UI功能目标
   - 确定用户交互流程
   - 分析数据展示需求

2. **设计规范遵循**
   - 遵循Material Design规范
   - 保持与现有UI风格一致
   - 考虑多语言和无障碍支持

#### 第二步：XML布局设计
```xml
<!-- 示例：新建眼动评估界面布局 -->
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/icon_main_bg">

    <!-- 标题栏 -->
    <include layout="@layout/layout_common_title_bar"
        android:id="@+id/title_bar"
        app:layout_constraintTop_toTopOf="parent"/>

    <!-- 内容区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/title_bar"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
```

#### 第三步：自定义View组件（如需要）
```kotlin
class CustomEyeTrackingView @JvmOverloads constructor(
    context: Context, 
    attrs: AttributeSet? = null, 
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    // 视图组件
    private val ivIndicator by id<ImageView>(R.id.iv_indicator)
    private val tvStatus by id<TextView>(R.id.tv_status)
    
    // 回调接口
    var onTrackingStateChange: ((Boolean) -> Unit)? = null
    
    init {
        val view = View.inflate(context, R.layout.view_custom_eye_tracking, null)
        addView(view)
        initView()
    }
    
    private fun initView() {
        // 初始化视图逻辑
    }
    
    fun updateTrackingState(isTracking: Boolean) {
        // 更新追踪状态显示
    }
}
```

#### 第四步：Activity/Fragment实现
```kotlin
class NewEyeTrackingActivity : BaseActivity() {
    
    // ViewBinding
    private lateinit var binding: ActivityNewEyeTrackingBinding
    
    // ViewModel
    private val viewModel by viewModels<NewEyeTrackingViewModel>()
    
    // 服务连接
    private var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null) {
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
                
                // 在服务连接后启动相机
                startEyeTracking()
            }
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNewEyeTrackingBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        initView()
        initObserver()
        initData()
    }
    
    private fun initView() {
        // 初始化视图
    }
    
    private fun initObserver() {
        // 观察ViewModel数据变化
        viewModel.trackingState.observe(this) { state ->
            updateUI(state)
        }
    }
    
    private fun initData() {
        // 初始化数据
    }
    
    private fun startEyeTracking() {
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            }
        )
    }
    
    override fun onStart() {
        super.onStart()
        bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }
    
    override fun onStop() {
        super.onStop()
        unbindService(serviceConnection)
    }
}
```

### 📝 开发检查清单

#### UI设计检查
- [ ] 布局适配不同屏幕尺寸
- [ ] 遵循Material Design规范
- [ ] 支持深色模式（如需要）
- [ ] 无障碍功能支持

#### 功能实现检查
- [ ] 服务连接状态管理
- [ ] 相机启动防重机制
- [ ] 内存泄漏检查
- [ ] 异常处理机制

#### 性能优化检查
- [ ] 图像处理异步执行
- [ ] 及时释放ImageProxy资源
- [ ] 避免主线程阻塞
- [ ] 合理使用协程

#### 测试验证检查
- [ ] 单元测试覆盖
- [ ] UI自动化测试
- [ ] 性能测试
- [ ] 兼容性测试

### 🚀 最佳实践

1. **遵循MVVM架构模式**
2. **使用ViewBinding替代findViewById**
3. **合理使用协程处理异步操作**
4. **及时释放资源避免内存泄漏**
5. **添加充分的日志记录**
6. **遵循代码规范和注释标准**

---

## 📞 技术支持

如有问题，请参考：
- [系统架构文档](system_architecture_diagrams.md)
- [完整技术文档](gaze_tracking_system_documentation.md)
- [代码规范指南](coding_standards.md)
