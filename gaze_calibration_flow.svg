<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #555; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #666; }
      .box { fill: #f8f9fa; stroke: #333; stroke-width: 2; rx: 5; }
      .new-feature { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; rx: 5; }
      .existing-feature { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; rx: 5; }
      .decision { fill: #fff9c4; stroke: #f9a825; stroke-width: 2; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .new-arrow { stroke: #0277bd; stroke-width: 2; fill: none; marker-end: url(#arrowhead-blue); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0277bd" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">眼动校准参数加载流程实现方案</text>
  
  <!-- Application Start Flow -->
  <g id="app-start">
    <text x="50" y="70" class="subtitle">应用启动流程</text>
    
    <rect x="50" y="80" width="150" height="40" class="existing-feature"/>
    <text x="125" y="105" text-anchor="middle" class="text">应用启动</text>
    
    <line x1="200" y1="100" x2="230" y2="100" class="arrow"/>
    
    <rect x="230" y="80" width="180" height="40" class="existing-feature"/>
    <text x="320" y="95" text-anchor="middle" class="small-text">GazeTrackingManager</text>
    <text x="320" y="110" text-anchor="middle" class="small-text">initGazeTracking</text>
    
    <line x1="410" y1="100" x2="440" y2="100" class="arrow"/>
    
    <rect x="440" y="60" width="160" height="80" class="new-feature"/>
    <text x="520" y="80" text-anchor="middle" class="small-text">copyModel2Dir</text>
    <text x="520" y="95" text-anchor="middle" class="small-text">拷贝.rknn模型</text>
    <text x="520" y="110" text-anchor="middle" class="small-text">+ 拷贝calib_param.txt</text>
    <text x="520" y="125" text-anchor="middle" class="small-text">→ calib_param_default.txt</text>
  </g>
  
  <!-- Tracking Start Flow -->
  <g id="tracking-start">
    <text x="50" y="200" class="subtitle">眼动追踪启动流程</text>
    
    <rect x="50" y="210" width="150" height="40" class="box"/>
    <text x="125" y="235" text-anchor="middle" class="text">启动眼动追踪</text>
    
    <line x1="200" y1="230" x2="230" y2="230" class="arrow"/>
    
    <rect x="230" y="210" width="180" height="40" class="existing-feature"/>
    <text x="320" y="225" text-anchor="middle" class="small-text">GazeService</text>
    <text x="320" y="240" text-anchor="middle" class="small-text">start_tracking</text>
    
    <line x1="410" y1="230" x2="440" y2="230" class="arrow"/>
    
    <rect x="440" y="210" width="180" height="40" class="existing-feature"/>
    <text x="530" y="225" text-anchor="middle" class="small-text">update_tracker_config_func</text>
    <text x="530" y="240" text-anchor="middle" class="small-text">检查配置文件</text>
  </g>
  
  <!-- Decision Flow -->
  <g id="decision-flow">
    <text x="50" y="320" class="subtitle">参数加载决策流程</text>
    
    <!-- Decision Diamond -->
    <polygon points="530,340 580,360 530,380 480,360" class="decision"/>
    <text x="530" y="355" text-anchor="middle" class="small-text">用户校准</text>
    <text x="530" y="370" text-anchor="middle" class="small-text">参数存在?</text>
    
    <!-- YES Path -->
    <line x1="580" y1="360" x2="650" y2="360" class="arrow"/>
    <text x="615" y="355" text-anchor="middle" class="small-text">YES</text>
    
    <rect x="650" y="340" width="160" height="40" class="existing-feature"/>
    <text x="730" y="355" text-anchor="middle" class="small-text">readConfigFromFile</text>
    <text x="730" y="370" text-anchor="middle" class="small-text">读取用户参数</text>
    
    <!-- NO Path -->
    <line x1="530" y1="380" x2="530" y2="420" class="new-arrow"/>
    <text x="545" y="400" class="small-text">NO</text>
    
    <polygon points="530,440 580,460 530,480 480,460" class="new-feature"/>
    <text x="530" y="455" text-anchor="middle" class="small-text">默认参数</text>
    <text x="530" y="470" text-anchor="middle" class="small-text">文件存在?</text>
    
    <!-- Default YES Path -->
    <line x1="580" y1="460" x2="650" y2="460" class="new-arrow"/>
    <text x="615" y="455" text-anchor="middle" class="small-text">YES</text>
    
    <rect x="650" y="440" width="160" height="40" class="new-feature"/>
    <text x="730" y="455" text-anchor="middle" class="small-text">readConfigFromFile</text>
    <text x="730" y="470" text-anchor="middle" class="small-text">读取默认参数</text>
    
    <!-- Default NO Path -->
    <line x1="530" y1="480" x2="530" y2="520" class="arrow"/>
    <text x="545" y="500" class="small-text">NO</text>
    
    <rect x="450" y="520" width="160" height="40" class="box"/>
    <text x="530" y="535" text-anchor="middle" class="small-text">提示用户校准</text>
    <text x="530" y="550" text-anchor="middle" class="small-text">calib_param_is_ready=false</text>
    
    <!-- Convergence -->
    <line x1="730" y1="380" x2="730" y2="420" class="arrow"/>
    <line x1="730" y1="480" x2="730" y2="520" class="new-arrow"/>
    <line x1="610" y1="540" x2="680" y2="540" class="arrow"/>
    
    <rect x="680" y="520" width="160" height="40" class="box"/>
    <text x="760" y="535" text-anchor="middle" class="small-text">gaze_tracker</text>
    <text x="760" y="550" text-anchor="middle" class="small-text">update_gaze_params</text>
  </g>
  
  <!-- File Structure -->
  <g id="file-structure">
    <text x="900" y="200" class="subtitle">文件结构</text>
    
    <rect x="900" y="210" width="250" height="30" class="existing-feature"/>
    <text x="1025" y="230" text-anchor="middle" class="small-text">calib_param.txt (用户校准参数 - 最高优先级)</text>
    
    <rect x="900" y="250" width="250" height="30" class="new-feature"/>
    <text x="1025" y="270" text-anchor="middle" class="small-text">calib_param_default.txt (默认参数 - 备用)</text>
    
    <rect x="900" y="290" width="250" height="30" class="box"/>
    <text x="1025" y="310" text-anchor="middle" class="small-text">assets/configs/calib_param.txt (只读模板)</text>
  </g>
  
  <!-- Implementation Points -->
  <g id="implementation">
    <text x="900" y="360" class="subtitle">实现要点</text>
    
    <rect x="900" y="370" width="250" height="120" class="box"/>
    <text x="920" y="390" class="small-text">1. 保持现有功能完全不变</text>
    <text x="920" y="410" class="small-text">2. 用户校准参数优先级最高</text>
    <text x="920" y="430" class="small-text">3. 默认参数作为备用方案</text>
    <text x="920" y="450" class="small-text">4. 向后兼容，风险可控</text>
    <text x="920" y="470" class="small-text">5. 首次使用无需强制校准</text>
  </g>
  
  <!-- Legend -->
  <g id="legend">
    <text x="50" y="620" class="subtitle">图例</text>
    
    <rect x="50" y="630" width="20" height="15" class="existing-feature"/>
    <text x="80" y="642" class="small-text">现有功能</text>
    
    <rect x="150" y="630" width="20" height="15" class="new-feature"/>
    <text x="180" y="642" class="small-text">新增功能</text>
    
    <rect x="250" y="630" width="20" height="15" class="box"/>
    <text x="280" y="642" class="small-text">通用流程</text>
    
    <polygon points="350,630 365,637.5 350,645 335,637.5" class="decision"/>
    <text x="380" y="642" class="small-text">决策点</text>
  </g>
  
  <!-- User Calibration Flow -->
  <g id="user-calibration">
    <text x="50" y="700" class="subtitle">用户校准流程（保持不变）</text>
    
    <rect x="50" y="710" width="120" height="30" class="box"/>
    <text x="110" y="730" text-anchor="middle" class="small-text">用户进行校准</text>
    
    <line x1="170" y1="725" x2="200" y2="725" class="arrow"/>
    
    <rect x="200" y="710" width="120" height="30" class="existing-feature"/>
    <text x="260" y="730" text-anchor="middle" class="small-text">校准完成</text>
    
    <line x1="320" y1="725" x2="350" y2="725" class="arrow"/>
    
    <rect x="350" y="710" width="150" height="30" class="existing-feature"/>
    <text x="425" y="730" text-anchor="middle" class="small-text">writeConfigToFile</text>
    
    <line x1="500" y1="725" x2="530" y2="725" class="arrow"/>
    
    <rect x="530" y="710" width="180" height="30" class="existing-feature"/>
    <text x="620" y="730" text-anchor="middle" class="small-text">覆盖为用户校准参数</text>
  </g>
</svg>
