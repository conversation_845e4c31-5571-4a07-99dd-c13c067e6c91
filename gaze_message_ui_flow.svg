<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="messageGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="websocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">GazeMessage作用与UI数据接收处理流程</text>
  
  <!-- 核心作用概述 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 GazeMessage作用：统一WebSocket消息格式，支持多种数据类型的结构化传输</text>
  <text x="70" y="110" class="text">• 提供action字段标识消息类型，data字段承载具体数据</text>
  <text x="70" y="125" class="text">• 支持泛型设计，可传输PostureCalibrationResult、CalibrationResult等不同类型数据</text>
  
  <!-- GazeMessage结构详解 -->
  <rect x="50" y="160" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">GazeMessage结构详解</text>
  
  <!-- 类定义 -->
  <rect x="80" y="210" width="400" height="120" rx="8" fill="url(#messageGradient)"/>
  <text x="280" y="235" text-anchor="middle" class="subtitle">GazeMessage&lt;T&gt; 类定义</text>
  
  <text x="100" y="260" class="code">class GazeMessage&lt;T&gt; {</text>
  <text x="120" y="280" class="code">var action: String = ""    // 消息类型标识</text>
  <text x="120" y="300" class="code">var data: T? = null        // 消息体数据</text>
  <text x="100" y="320" class="code">}</text>
  
  <!-- 消息类型常量 -->
  <rect x="520" y="210" width="500" height="120" rx="8" fill="url(#messageGradient)"/>
  <text x="770" y="235" text-anchor="middle" class="subtitle">支持的消息类型</text>
  
  <text x="540" y="260" class="code">ACTION_POSTURE_CALIBRATION_RESULT</text>
  <text x="540" y="280" class="text">• 姿势校准结果数据</text>
  <text x="540" y="300" class="code">ACTION_CALIBRATION_RESULT</text>
  <text x="540" y="320" class="text">• 视标校准结果数据</text>
  
  <!-- JSON格式示例 -->
  <rect x="1060" y="210" width="600" height="120" rx="8" fill="url(#messageGradient)"/>
  <text x="1360" y="235" text-anchor="middle" class="subtitle">JSON传输格式</text>
  
  <text x="1080" y="260" class="code">{</text>
  <text x="1100" y="280" class="code">"action": "ACTION_POSTURE_CALIBRATION_RESULT",</text>
  <text x="1100" y="300" class="code">"data": { PostureCalibrationResult对象 }</text>
  <text x="1080" y="320" class="code">}</text>
  
  <!-- 完整数据流程 -->
  <rect x="50" y="380" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="405" text-anchor="middle" class="subtitle">完整数据流程：从C++到UI显示</text>
  
  <!-- 步骤1: C++数据生成 -->
  <rect x="80" y="430" width="200" height="80" rx="8" fill="#fff3e0"/>
  <text x="180" y="455" text-anchor="middle" class="subtitle">1. C++数据生成</text>
  <text x="180" y="475" text-anchor="middle" class="highlight">pose_align_result</text>
  <text x="100" y="495" class="text">• 姿势检测算法输出</text>
  
  <!-- 步骤2: Java封装 -->
  <rect x="320" y="430" width="200" height="80" rx="8" fill="#e3f2fd"/>
  <text x="420" y="455" text-anchor="middle" class="subtitle">2. Java数据封装</text>
  <text x="420" y="475" text-anchor="middle" class="highlight">PostureCalibrationResult</text>
  <text x="340" y="495" class="text">• 转换为Java对象</text>
  
  <!-- 步骤3: GazeMessage包装 -->
  <rect x="560" y="430" width="200" height="80" rx="8" fill="url(#messageGradient)"/>
  <text x="660" y="455" text-anchor="middle" class="subtitle">3. GazeMessage包装</text>
  <text x="660" y="475" text-anchor="middle" class="highlight">统一消息格式</text>
  <text x="580" y="495" class="text">• 添加action标识</text>
  
  <!-- 步骤4: WebSocket传输 -->
  <rect x="800" y="430" width="200" height="80" rx="8" fill="url(#websocketGradient)"/>
  <text x="900" y="455" text-anchor="middle" class="subtitle">4. WebSocket传输</text>
  <text x="900" y="475" text-anchor="middle" class="highlight">JSON字符串</text>
  <text x="820" y="495" class="text">• 序列化并广播</text>
  
  <!-- 步骤5: UI接收解析 -->
  <rect x="1040" y="430" width="200" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="1140" y="455" text-anchor="middle" class="subtitle">5. UI接收解析</text>
  <text x="1140" y="475" text-anchor="middle" class="highlight">CalibrationActivity</text>
  <text x="1060" y="495" class="text">• 反序列化数据</text>
  
  <!-- 步骤6: UI更新 -->
  <rect x="1280" y="430" width="200" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="1380" y="455" text-anchor="middle" class="subtitle">6. UI界面更新</text>
  <text x="1380" y="475" text-anchor="middle" class="highlight">PostureCalibrationView</text>
  <text x="1300" y="495" class="text">• 调整界面显示</text>
  
  <!-- 连接箭头 -->
  <line x1="280" y1="470" x2="320" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="520" y1="470" x2="560" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="760" y1="470" x2="800" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1000" y1="470" x2="1040" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1240" y1="470" x2="1280" y2="470" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 详细代码实现 -->
  <rect x="80" y="540" width="1600" height="220" rx="8" fill="#f8f9fa"/>
  <text x="880" y="565" text-anchor="middle" class="subtitle">关键代码实现</text>
  
  <!-- 服务端发送 -->
  <text x="100" y="590" class="highlight">服务端发送 (GazeTrackService):</text>
  <text x="120" y="610" class="code">override fun onPostureCalibration(result: PostureCalibrationResult) {</text>
  <text x="140" y="630" class="code">val gazeMessage = GazeMessage&lt;PostureCalibrationResult&gt;().apply {</text>
  <text x="160" y="650" class="code">action = GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT</text>
  <text x="160" y="670" class="code">data = result</text>
  <text x="140" y="690" class="code">}</text>
  <text x="140" y="710" class="code">mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))</text>
  <text x="120" y="730" class="code">}</text>
  
  <!-- 客户端接收 -->
  <text x="900" y="590" class="highlight">客户端接收 (CalibrationActivity):</text>
  <text x="920" y="610" class="code">override fun onMessage(message: String?) {</text>
  <text x="940" y="630" class="code">val jsonObject = JSONObject(message)</text>
  <text x="940" y="650" class="code">val action = jsonObject.opt("action")</text>
  <text x="940" y="670" class="code">when(action) {</text>
  <text x="960" y="690" class="code">GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT -> {</text>
  <text x="980" y="710" class="code">val gazeMessage = gson.fromJson&lt;GazeMessage&lt;PostureCalibrationResult&gt;&gt;(message, type)</text>
  <text x="980" y="730" class="code">calibrationVM.setPostureCalibrationResult(gazeMessage.data)</text>
  
  <!-- UI数据处理流程 -->
  <rect x="50" y="800" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="825" text-anchor="middle" class="subtitle">UI层数据处理与界面调整流程</text>
  
  <!-- LiveData观察 -->
  <rect x="80" y="850" width="500" height="120" rx="8" fill="#e8f5e8"/>
  <text x="330" y="875" text-anchor="middle" class="subtitle">LiveData数据观察</text>
  
  <text x="100" y="900" class="code">calibrationVM.postureCalibrationResultLivedata.observe(this) {</text>
  <text x="120" y="920" class="code">// 姿势校准完成处理</text>
  <text x="120" y="940" class="code">if (it.state && !isCLoseCalibration) {</text>
  <text x="140" y="960" class="code">showVisualCalibration()  // 进入视标校准</text>
  
  <!-- UI更新调用 -->
  <rect x="620" y="850" width="500" height="120" rx="8" fill="#e8f5e8"/>
  <text x="870" y="875" text-anchor="middle" class="subtitle">UI界面更新</text>
  
  <text x="640" y="900" class="code">// 实时更新姿势校准界面</text>
  <text x="640" y="920" class="code">(calibrationView as? PostureCalibrationView)?</text>
  <text x="660" y="940" class="code">.setPostureCorrectionResult(it)</text>
  <text x="640" y="960" class="code">}</text>
  
  <!-- PostureCalibrationView处理 -->
  <rect x="80" y="990" width="1600" height="140" rx="8" fill="#fff3e0"/>
  <text x="880" y="1015" text-anchor="middle" class="subtitle">PostureCalibrationView.setPostureCorrectionResult() 处理逻辑</text>
  
  <text x="100" y="1040" class="highlight">1. 数据有效性检查:</text>
  <text x="120" y="1060" class="code">if (data.checkPostureCorrectionResult()) // 检查坐标是否在[0,1]范围内</text>
  
  <text x="100" y="1085" class="highlight">2. 计算眼部中心位置:</text>
  <text x="120" y="1105" class="code">val centerX = leftPoint.x + (rightPoint.x - leftPoint.x) / 2</text>
  <text x="120" y="1125" class="code">val centerY = leftPoint.y + (rightPoint.y - leftPoint.y) / 2</text>
  
  <text x="700" y="1040" class="highlight">3. 头像缩放计算:</text>
  <text x="720" y="1060" class="code">val scale = 0.5f / data.dist  // 根据距离调整大小</text>
  
  <text x="700" y="1085" class="highlight">4. 姿势状态判断:</text>
  <text x="720" y="1105" class="code">if (data.aligned) 绿色头像 else 红色头像+调整提示</text>
  
  <!-- UI调整详细逻辑 -->
  <rect x="50" y="1170" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1195" text-anchor="middle" class="subtitle">UI调整的详细逻辑</text>
  
  <!-- 头像位置调整 -->
  <rect x="80" y="1220" width="400" height="140" rx="8" fill="#e3f2fd"/>
  <text x="280" y="1245" text-anchor="middle" class="subtitle">头像位置调整</text>
  
  <text x="100" y="1270" class="code">// 头像跟随眼部位置移动</text>
  <text x="100" y="1290" class="code">ivRealLocation.translationX = centerX - avatarOriginalW / 2</text>
  <text x="100" y="1310" class="code">ivRealLocation.translationY = centerY - avatarOriginalH / 2</text>
  <text x="100" y="1330" class="code">// 根据距离调整头像大小</text>
  <text x="100" y="1350" class="code">ivRealLocation.scaleX = scale</text>
  
  <!-- 姿势异常检测 -->
  <rect x="520" y="1220" width="400" height="140" rx="8" fill="#ffebee"/>
  <text x="720" y="1245" text-anchor="middle" class="subtitle">姿势异常检测</text>
  
  <text x="540" y="1270" class="code">val postureException = createPostureException(data)</text>
  <text x="540" y="1290" class="text">• 距离异常: dist < 0.4 (太近) / > 0.6 (太远)</text>
  <text x="540" y="1310" class="text">• 位置异常: 水平偏移 > 384px / 垂直偏移 > 324px</text>
  <text x="540" y="1330" class="text">• 倾斜异常: 角度 > 15° / < -15°</text>
  <text x="540" y="1350" class="text">• 根据异常程度设置degree(1或2)</text>
  
  <!-- 用户引导反馈 -->
  <rect x="960" y="1220" width="400" height="140" rx="8" fill="#e8f5e8"/>
  <text x="1160" y="1245" text-anchor="middle" class="subtitle">用户引导反馈</text>
  
  <text x="980" y="1270" class="code">handlePostureException(postureException)</text>
  <text x="980" y="1290" class="text">• 文字提示: "请向左/右/上/下/前/后移动"</text>
  <text x="980" y="1310" class="text">• 语音播放: PlayManager.playRawMedia(resId)</text>
  <text x="980" y="1330" class="text">• 头像颜色: 绿色(正确) / 红色(需调整)</text>
  <text x="980" y="1350" class="text">• 倒计时显示: countdown字段</text>
  
  <!-- 总结 -->
  <rect x="100" y="1540" width="1600" height="40" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1565" text-anchor="middle" class="highlight">
    总结：GazeMessage提供统一的WebSocket消息格式，UI层通过LiveData观察数据变化，实时调整界面显示和用户引导
  </text>
</svg>
