<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .gaze-point-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .blur-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .canvas-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .native-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .widget-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .gaze-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .blur-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .canvas-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .native-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .widget-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .gaze-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .blur-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .canvas-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">视点和遮盖渲染位置详细分析</text>
  
  <!-- 第一层：视点渲染 -->
  <rect x="50" y="70" width="320" height="120" class="gaze-point-layer"/>
  <text x="210" y="95" text-anchor="middle" class="section-title">视点渲染</text>
  
  <rect x="70" y="110" width="280" height="70" class="gaze-module"/>
  <text x="210" y="130" text-anchor="middle" class="method-title">WidgetManager.showDotView()</text>
  <text x="80" y="150" class="text">• 悬浮窗显示视点圆圈</text>
  <text x="80" y="165" class="text">• 实时跟随眼动坐标</text>

  <!-- 第二层：遮盖渲染 -->
  <rect x="390" y="70" width="320" height="120" class="blur-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">遮盖渲染</text>
  
  <rect x="410" y="110" width="280" height="70" class="blur-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">IPQ_BLR GPU引擎</text>
  <text x="420" y="150" class="text">• OpenGL着色器虚化</text>
  <text x="420" y="165" class="text">• 实时遮盖效果渲染</text>

  <!-- 第三层：Canvas绘制 -->
  <rect x="730" y="70" width="320" height="120" class="canvas-layer"/>
  <text x="890" y="95" text-anchor="middle" class="section-title">Canvas绘制</text>
  
  <rect x="750" y="110" width="280" height="70" class="canvas-module"/>
  <text x="890" y="130" text-anchor="middle" class="method-title">Android Canvas</text>
  <text x="760" y="150" class="text">• 视点轨迹和圆圈绘制</text>
  <text x="760" y="165" class="text">• 评估结果可视化</text>

  <!-- 第四层：Native显示 -->
  <rect x="1070" y="70" width="320" height="120" class="native-layer"/>
  <text x="1230" y="95" text-anchor="middle" class="section-title">Native显示</text>
  
  <rect x="1090" y="110" width="280" height="70" class="native-module"/>
  <text x="1230" y="130" text-anchor="middle" class="method-title">ANativeWindow</text>
  <text x="1100" y="150" class="text">• 帧缓冲直接显示</text>
  <text x="1100" y="165" class="text">• 高性能图像渲染</text>

  <!-- 第五层：悬浮窗组件 -->
  <rect x="1410" y="70" width="320" height="120" class="widget-layer"/>
  <text x="1570" y="95" text-anchor="middle" class="section-title">悬浮窗组件</text>
  
  <rect x="1430" y="110" width="280" height="70" class="widget-module"/>
  <text x="1570" y="130" text-anchor="middle" class="method-title">WindowManager</text>
  <text x="1440" y="150" class="text">• 系统级悬浮显示</text>
  <text x="1440" y="165" class="text">• 视点实时跟踪</text>

  <!-- 连接箭头 -->
  <line x1="370" y1="130" x2="390" y2="130" class="gaze-arrow"/>
  <line x1="710" y1="130" x2="730" y2="130" class="blur-arrow"/>
  <line x1="1050" y1="130" x2="1070" y2="130" class="canvas-arrow"/>
  <line x1="1390" y1="130" x2="1410" y2="130" class="arrow"/>

  <!-- 详细分析 -->
  <rect x="50" y="220" width="1680" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="890" y="245" text-anchor="middle" class="title" style="font-size: 22px;">视点和遮盖渲染位置详细分析</text>

  <!-- 第一部分：视点渲染详解 -->
  <text x="70" y="280" class="layer-title">👁️ 视点渲染详解</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 悬浮窗视点显示</text>
  <text x="110" y="325" class="code-text">// GazeTrackService.kt - onGazeTracking方法</text>
  <text x="110" y="340" class="code-text">override fun onGazeTracking(gazeTrackResult: GazeTrackResult) {</text>
  <text x="130" y="355" class="code-text">if (isDisplayViewpoint) {</text>
  <text x="150" y="370" class="code-text">// 🔥 显示视点悬浮窗</text>
  <text x="150" y="385" class="code-text">WidgetManager.showDotView(this, gazeTrackResult, mScreenWidth, mScreenHeight)</text>
  <text x="130" y="400" class="code-text">} else {</text>
  <text x="150" y="415" class="code-text">WidgetManager.removeDotView()  // 隐藏视点</text>
  <text x="130" y="430" class="code-text">}</text>
  <text x="110" y="445" class="code-text">}</text>
  
  <text x="90" y="470" class="flow-text" style="font-weight: bold;">2. WidgetManager实现机制</text>
  <text x="110" y="490" class="flow-text">• <tspan style="color: #3498db;">悬浮窗权限：</tspan>使用WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY</text>
  <text x="110" y="505" class="flow-text">• <tspan style="color: #3498db;">实时更新：</tspan>30fps更新视点位置，跟随眼动坐标</text>
  <text x="110" y="520" class="flow-text">• <tspan style="color: #3498db;">坐标转换：</tspan>gazeTrackResult坐标 → 屏幕像素坐标</text>
  <text x="110" y="535" class="flow-text">• <tspan style="color: #3498db;">视觉效果：</tspan>半透明圆圈，不干扰用户操作</text>
  
  <text x="90" y="560" class="flow-text" style="font-weight: bold;">3. Canvas视点绘制</text>
  <text x="110" y="580" class="code-text">// ReadTrackView.kt - onDraw方法</text>
  <text x="110" y="595" class="code-text">override fun onDraw(canvas: Canvas) {</text>
  <text x="130" y="610" class="code-text">canvas.drawPath(trackPath, trackPathPaint)  // 绘制轨迹</text>
  <text x="130" y="625" class="code-text">readPoints.forEach { result -></text>
  <text x="150" y="640" class="code-text">val circleX = result.x!! * screenWidth</text>
  <text x="150" y="655" class="code-text">val circleY = result.y!! * screenHeight</text>
  <text x="150" y="670" class="code-text">// 🔥 绘制视点圆圈</text>
  <text x="150" y="685" class="code-text">canvas.drawCircle(circleX, circleY, circleRadius, trackPointPaint)</text>
  <text x="130" y="700" class="code-text">}</text>
  <text x="110" y="715" class="code-text">}</text>

  <!-- 第二部分：遮盖渲染详解 -->
  <text x="900" y="280" class="layer-title">🎨 遮盖渲染详解</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. GPU虚化引擎</text>
  <text x="940" y="325" class="code-text">// Blur.cpp - draw_gaze_result_func方法</text>
  <text x="940" y="340" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="960" y="355" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="980" y="370" class="code-text">int screen_x = (int)(x * visual_image_width);</text>
  <text x="980" y="385" class="code-text">int screen_y = (int)(y * visual_image_height);</text>
  <text x="980" y="400" class="code-text">// 🔥 设置虚化位置并触发GPU渲染</text>
  <text x="980" y="415" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="960" y="430" class="code-text">}</text>
  <text x="940" y="445" class="code-text">}</text>
  
  <text x="920" y="470" class="flow-text" style="font-weight: bold;">2. IPQ_BLR接口实现</text>
  <text x="940" y="490" class="code-text">class IPQ_BLR {</text>
  <text x="960" y="505" class="code-text">virtual IPQ_BLR* SetPos(int x, int y) = 0;      // 设置虚化中心</text>
  <text x="960" y="520" class="code-text">virtual IPQ_BLR* SetRadius(int r) = 0;          // 设置虚化半径</text>
  <text x="960" y="535" class="code-text">virtual IPQ_BLR* SetGaussKer(int w, int h, float sigma) = 0;  // 高斯核</text>
  <text x="960" y="550" class="code-text">virtual void Done() = 0;                        // 🔥 执行GPU渲染</text>
  <text x="940" y="565" class="code-text">};</text>
  
  <text x="920" y="590" class="flow-text" style="font-weight: bold;">3. OpenGL着色器渲染</text>
  <text x="940" y="610" class="flow-text">• <tspan style="color: #e74c3c;">顶点着色器：</tspan>处理虚化区域的几何变换</text>
  <text x="940" y="625" class="flow-text">• <tspan style="color: #e74c3c;">片段着色器：</tspan>实现高斯模糊或置黑效果</text>
  <text x="940" y="640" class="flow-text">• <tspan style="color: #e74c3c;">纹理处理：</tspan>对指定区域进行实时纹理处理</text>
  <text x="940" y="655" class="flow-text">• <tspan style="color: #e74c3c;">帧缓冲：</tspan>渲染结果写入帧缓冲区</text>

  <!-- 第三部分：Native显示机制 -->
  <text x="70" y="750" class="layer-title">🖥️ Native显示机制</text>
  
  <text x="90" y="775" class="flow-text" style="font-weight: bold;">1. ANativeWindow渲染</text>
  <text x="110" y="795" class="code-text">// GazeService.cpp - draw方法</text>
  <text x="110" y="810" class="code-text">void GazeService::draw(const Mat& img) {</text>
  <text x="130" y="825" class="code-text">// 设置缓冲区格式</text>
  <text x="130" y="840" class="code-text">ANativeWindow_setBuffersGeometry(window, img.cols, img.rows, WINDOW_FORMAT_RGBA_8888);</text>
  <text x="130" y="855" class="code-text">// 锁定缓冲区</text>
  <text x="130" y="870" class="code-text">ANativeWindow_Buffer buffer;</text>
  <text x="130" y="885" class="code-text">ANativeWindow_lock(window, &buffer, nullptr);</text>
  <text x="130" y="900" class="code-text">// 🔥 拷贝渲染结果到屏幕缓冲区</text>
  <text x="130" y="915" class="code-text">memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize);</text>
  <text x="110" y="930" class="code-text">}</text>
  
  <text x="90" y="955" class="flow-text" style="font-weight: bold;">2. 显示流程</text>
  <text x="110" y="975" class="flow-text">• <tspan style="color: #f39c12;">Surface设置：</tspan>setANativeWindow()设置渲染目标</text>
  <text x="110" y="990" class="flow-text">• <tspan style="color: #f39c12;">格式配置：</tspan>RGBA_8888格式，支持透明度</text>
  <text x="110" y="1005" class="flow-text">• <tspan style="color: #f39c12;">缓冲区管理：</tspan>双缓冲机制，避免撕裂</text>
  <text x="110" y="1020" class="flow-text">• <tspan style="color: #f39c12;">内存拷贝：</tspan>逐行拷贝，确保数据完整性</text>

  <!-- 第四部分：Canvas绘制详解 -->
  <text x="900" y="750" class="layer-title">🎨 Canvas绘制详解</text>
  
  <text x="920" y="775" class="flow-text" style="font-weight: bold;">1. 视点轨迹绘制</text>
  <text x="940" y="795" class="code-text">// 多个View类的共同模式</text>
  <text x="940" y="810" class="code-text">gazePoints.forEachIndexed { index, result -></text>
  <text x="960" y="825" class="code-text">val x = result.x!! * screenWidth</text>
  <text x="960" y="840" class="code-text">val y = result.y!! * screenHeight</text>
  <text x="960" y="855" class="code-text">if (index == 0) {</text>
  <text x="980" y="870" class="code-text">gazePath.moveTo(x, y)  // 起始点</text>
  <text x="960" y="885" class="code-text">} else {</text>
  <text x="980" y="900" class="code-text">gazePath.lineTo(x, y)  // 连接线</text>
  <text x="960" y="915" class="code-text">}</text>
  <text x="940" y="930" class="code-text">}</text>
  
  <text x="920" y="955" class="flow-text" style="font-weight: bold;">2. 视点圆圈绘制</text>
  <text x="940" y="975" class="code-text">// 绘制视点圆圈和序号</text>
  <text x="940" y="990" class="code-text">canvas.drawCircle(circleX, circleY, gazePointRadius, gazePointPaint)</text>
  <text x="940" y="1005" class="code-text">canvas.drawText(serialNumber, circleX, circleY, gazeIndexPaint)</text>
  
  <text x="920" y="1030" class="flow-text" style="font-weight: bold;">3. 应用场景</text>
  <text x="940" y="1050" class="flow-text">• <tspan style="color: #27ae60;">ReadTrackView：</tspan>阅读轨迹可视化</text>
  <text x="940" y="1065" class="flow-text">• <tspan style="color: #27ae60;">ROIDetectionResultView：</tspan>兴趣区域检测结果</text>
  <text x="940" y="1080" class="flow-text">• <tspan style="color: #27ae60;">SaccadeAbilityEvaluateResultView：</tspan>扫视能力评估</text>
  <text x="940" y="1095" class="flow-text">• <tspan style="color: #27ae60;">GazeStabilityEvaluateResultView：</tspan>注视稳定性评估</text>

  <!-- 第五部分：渲染位置总结 -->
  <text x="70" y="1130" class="layer-title">📍 渲染位置总结</text>
  
  <text x="90" y="1155" class="flow-text" style="font-weight: bold;">1. 实时视点显示</text>
  <text x="110" y="1175" class="flow-text">• <tspan style="color: #3498db;">位置：</tspan>GazeTrackService.onGazeTracking() → WidgetManager.showDotView()</text>
  <text x="110" y="1190" class="flow-text">• <tspan style="color: #3498db;">特点：</tspan>系统级悬浮窗，实时跟随眼动，半透明显示</text>
  <text x="110" y="1205" class="flow-text">• <tspan style="color: #3498db;">用途：</tspan>治疗过程中的视点反馈，调试眼动追踪精度</text>
  
  <text x="90" y="1230" class="flow-text" style="font-weight: bold;">2. 实时遮盖渲染</text>
  <text x="110" y="1250" class="flow-text">• <tspan style="color: #e74c3c;">位置：</tspan>GazeApplication.collect_gaze() → PqBlur.draw_gaze_result_func() → IPQ_BLR.Done()</text>
  <text x="110" y="1265" class="flow-text">• <tspan style="color: #e74c3c;">特点：</tspan>GPU加速，OpenGL着色器，30fps实时渲染</text>
  <text x="110" y="1280" class="flow-text">• <tspan style="color: #e74c3c;">用途：</tspan>遮盖疗法的核心功能，治疗弱视</text>
  
  <text x="90" y="1305" class="flow-text" style="font-weight: bold;">3. 评估结果可视化</text>
  <text x="110" y="1325" class="flow-text">• <tspan style="color: #27ae60;">位置：</tspan>各种ResultView.onDraw() → Canvas.drawCircle/drawPath()</text>
  <text x="110" y="1340" class="flow-text">• <tspan style="color: #27ae60;">特点：</tspan>Android Canvas绘制，支持轨迹和点的可视化</text>
  <text x="110" y="1355" class="flow-text">• <tspan style="color: #27ae60;">用途：</tspan>眼动评估结果展示，医疗数据分析</text>
  
  <text x="90" y="1380" class="flow-text" style="font-weight: bold;">4. Native图像显示</text>
  <text x="110" y="1400" class="flow-text">• <tspan style="color: #f39c12;">位置：</tspan>GazeService.draw() → ANativeWindow缓冲区</text>
  <text x="110" y="1415" class="flow-text">• <tspan style="color: #f39c12;">特点：</tspan>高性能图像渲染，直接操作帧缓冲</text>
  <text x="110" y="1430" class="flow-text">• <tspan style="color: #f39c12;">用途：</tspan>相机预览、检测结果、校准界面显示</text>

  <!-- 总结 -->
  <rect x="70" y="1460" width="1600" height="80" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1485" class="layer-title">🌟 渲染架构总结</text>
  <text x="90" y="1510" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">视点显示：</tspan>WidgetManager悬浮窗 + Canvas绘制，提供实时视点反馈和评估可视化</text>
  <text x="90" y="1530" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">遮盖渲染：</tspan>IPQ_BLR GPU引擎 + OpenGL着色器，实现高性能实时遮盖效果</text>

</svg>
