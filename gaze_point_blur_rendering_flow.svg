<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .camera-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .tracking-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .blur-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .render-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .display-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .camera-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .tracking-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .blur-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .render-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .display-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .process-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .render-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">遮盖模式下视点获取与虚化渲染完整流程</text>
  
  <!-- 第一层：相机图像采集 -->
  <rect x="50" y="70" width="300" height="120" class="camera-layer"/>
  <text x="200" y="95" text-anchor="middle" class="section-title">相机图像采集</text>
  
  <rect x="70" y="110" width="260" height="70" class="camera-module"/>
  <text x="200" y="130" text-anchor="middle" class="method-title">Camera Hardware</text>
  <text x="80" y="150" class="text">• 30fps图像采集</text>
  <text x="80" y="165" class="text">• 红外相机捕获眼部图像</text>

  <!-- 第二层：眼动追踪算法 -->
  <rect x="380" y="70" width="300" height="120" class="tracking-layer"/>
  <text x="530" y="95" text-anchor="middle" class="section-title">眼动追踪算法</text>
  
  <rect x="400" y="110" width="260" height="70" class="tracking-module"/>
  <text x="530" y="130" text-anchor="middle" class="method-title">Gaze Tracking</text>
  <text x="410" y="150" class="text">• 瞳孔检测与定位</text>
  <text x="410" y="165" class="text">• 计算注视点坐标(x, y)</text>

  <!-- 第三层：虚化算法 -->
  <rect x="710" y="70" width="300" height="120" class="blur-layer"/>
  <text x="860" y="95" text-anchor="middle" class="section-title">虚化算法处理</text>
  
  <rect x="730" y="110" width="260" height="70" class="blur-module"/>
  <text x="860" y="130" text-anchor="middle" class="method-title">PqBlur Engine</text>
  <text x="740" y="150" class="text">• 根据视点计算虚化区域</text>
  <text x="740" y="165" class="text">• 高斯模糊/置黑处理</text>

  <!-- 第四层：GPU渲染 -->
  <rect x="1040" y="70" width="300" height="120" class="render-layer"/>
  <text x="1190" y="95" text-anchor="middle" class="section-title">GPU实时渲染</text>
  
  <rect x="1060" y="110" width="260" height="70" class="render-module"/>
  <text x="1190" y="130" text-anchor="middle" class="method-title">OpenGL Rendering</text>
  <text x="1070" y="150" class="text">• 实时遮盖效果渲染</text>
  <text x="1070" y="165" class="text">• 30fps流畅显示</text>

  <!-- 第五层：前端显示 -->
  <rect x="1370" y="70" width="300" height="120" class="display-layer"/>
  <text x="1520" y="95" text-anchor="middle" class="section-title">前端显示</text>
  
  <rect x="1390" y="110" width="260" height="70" class="display-module"/>
  <text x="1520" y="130" text-anchor="middle" class="method-title">Screen Display</text>
  <text x="1400" y="150" class="text">• 用户看到遮盖效果</text>
  <text x="1400" y="165" class="text">• 实时跟随视点移动</text>

  <!-- 连接箭头 -->
  <line x1="350" y1="130" x2="380" y2="130" class="process-arrow"/>
  <line x1="680" y1="130" x2="710" y2="130" class="data-arrow"/>
  <line x1="1010" y1="130" x2="1040" y2="130" class="process-arrow"/>
  <line x1="1340" y1="130" x2="1370" y2="130" class="render-arrow"/>

  <!-- 详细流程说明 -->
  <rect x="50" y="220" width="1620" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="860" y="245" text-anchor="middle" class="title" style="font-size: 22px;">视点获取与虚化渲染详细实现</text>

  <!-- 第一部分：视点获取流程 -->
  <text x="70" y="280" class="layer-title">👁️ 视点获取流程</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 相机图像采集</text>
  <text x="110" y="325" class="code-text">// 相机硬件层</text>
  <text x="110" y="340" class="code-text">Camera → ImageCapture(30fps) → RawImageData</text>
  <text x="110" y="360" class="flow-text">• <tspan style="color: #3498db;">红外相机：</tspan>专门用于眼动追踪的红外相机</text>
  <text x="110" y="375" class="flow-text">• <tspan style="color: #3498db;">高帧率：</tspan>30fps确保实时性和流畅性</text>
  <text x="110" y="390" class="flow-text">• <tspan style="color: #3498db;">图像质量：</tspan>高分辨率确保眼部特征清晰</text>
  
  <text x="90" y="415" class="flow-text" style="font-weight: bold;">2. 眼动追踪算法</text>
  <text x="110" y="435" class="code-text">// Native C++层实现</text>
  <text x="110" y="450" class="code-text">RawImage → EyeDetection → PupilTracking → GazePoint(x, y)</text>
  <text x="110" y="470" class="flow-text">• <tspan style="color: #27ae60;">眼部检测：</tspan>从图像中定位眼部区域</text>
  <text x="110" y="485" class="flow-text">• <tspan style="color: #27ae60;">瞳孔定位：</tspan>精确定位瞳孔中心点</text>
  <text x="110" y="500" class="flow-text">• <tspan style="color: #27ae60;">坐标计算：</tspan>根据瞳孔位置计算屏幕注视点</text>
  <text x="110" y="515" class="flow-text">• <tspan style="color: #27ae60;">坐标系转换：</tspan>从相机坐标系转换到屏幕坐标系</text>
  
  <text x="90" y="540" class="flow-text" style="font-weight: bold;">3. 视点数据传递</text>
  <text x="110" y="560" class="code-text">// 实时数据流</text>
  <text x="110" y="575" class="code-text">GazePoint(x, y) → PqBlur::update_gaze_position(x, y)</text>
  <text x="110" y="595" class="flow-text">• <tspan style="color: #e74c3c;">实时更新：</tspan>30fps实时更新视点位置</text>
  <text x="110" y="610" class="flow-text">• <tspan style="color: #e74c3c;">坐标精度：</tspan>亚像素级别的精度</text>
  <text x="110" y="625" class="flow-text">• <tspan style="color: #e74c3c;">滤波处理：</tspan>对视点进行平滑滤波，减少抖动</text>

  <!-- 第二部分：虚化算法实现 -->
  <text x="900" y="280" class="layer-title">🎨 虚化算法实现</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. PqBlur初始化</text>
  <text x="940" y="325" class="code-text">void PqBlur::set_red_blur_enable(bool valid) {</text>
  <text x="960" y="340" class="code-text">if (valid) {</text>
  <text x="980" y="355" class="code-text">blur_enable_flag = true;</text>
  <text x="980" y="370" class="code-text">pq_blr = newPQ_BLR();  // 创建虚化引擎</text>
  <text x="980" y="385" class="code-text">pq_blr->SetRadius(pq_radius)  // 设置虚化半径</text>
  <text x="1000" y="400" class="code-text">->SetChannel(channel)  // 设置颜色通道</text>
  <text x="1000" y="415" class="code-text">->SetPos(width/2, height/2)  // 初始位置</text>
  <text x="1000" y="430" class="code-text">->Done();</text>
  <text x="960" y="445" class="code-text">}</text>
  <text x="940" y="460" class="code-text">}</text>
  
  <text x="920" y="485" class="flow-text" style="font-weight: bold;">2. 虚化模式设置</text>
  <text x="940" y="505" class="code-text">switch (mode) {</text>
  <text x="960" y="520" class="code-text">case InnerBlur:  // 内部高斯模糊</text>
  <text x="980" y="535" class="code-text">pq_blr->SetGaussKer(InnerGKSize, InnerGKSize, GKSigma)</text>
  <text x="1000" y="550" class="code-text">->SetMode(mode)->Done();</text>
  <text x="960" y="565" class="code-text">case OuterBlur:  // 外部高斯模糊</text>
  <text x="980" y="580" class="code-text">pq_blr->SetGaussKer(OuterGKSize, OuterGKSize, default_outer_GKSigma)</text>
  <text x="1000" y="595" class="code-text">->SetMode(mode)->Done();</text>
  <text x="960" y="610" class="code-text">default:  // 置黑模式</text>
  <text x="980" y="625" class="code-text">pq_blr->SetMode(mode)->Done();</text>
  <text x="940" y="640" class="code-text">}</text>

  <!-- 第三部分：实时渲染流程 -->
  <text x="70" y="675" class="layer-title">🖥️ 实时渲染流程</text>
  
  <text x="90" y="700" class="flow-text" style="font-weight: bold;">1. 视点位置更新</text>
  <text x="110" y="720" class="code-text">// 每帧更新视点位置</text>
  <text x="110" y="735" class="code-text">void update_gaze_position(float x, float y) {</text>
  <text x="130" y="750" class="code-text">if (pq_blr != nullptr) {</text>
  <text x="150" y="765" class="code-text">// 根据弱视眼位置调整遮盖位置</text>
  <text x="150" y="780" class="code-text">float blur_x = calculate_blur_position_x(x, amblyopic_eye);</text>
  <text x="150" y="795" class="code-text">float blur_y = calculate_blur_position_y(y, amblyopic_eye);</text>
  <text x="150" y="810" class="code-text">pq_blr->SetPos((int)blur_x, (int)blur_y);</text>
  <text x="130" y="825" class="code-text">}</text>
  <text x="110" y="840" class="code-text">}</text>
  
  <text x="90" y="865" class="flow-text" style="font-weight: bold;">2. 虚化区域计算</text>
  <text x="110" y="885" class="flow-text">• <tspan style="color: #e74c3c;">弱视眼判断：</tspan>根据左眼/右眼弱视确定遮盖位置</text>
  <text x="110" y="900" class="flow-text">• <tspan style="color: #e74c3c;">区域计算：</tspan>以视点为中心，计算虚化区域范围</text>
  <text x="110" y="915" class="flow-text">• <tspan style="color: #e74c3c;">半径控制：</tspan>根据pq_radius参数控制虚化区域大小</text>
  <text x="110" y="930" class="flow-text">• <tspan style="color: #e74c3c;">边界处理：</tspan>确保虚化区域不超出屏幕边界</text>
  
  <text x="90" y="955" class="flow-text" style="font-weight: bold;">3. GPU加速渲染</text>
  <text x="110" y="975" class="code-text">// OpenGL渲染管线</text>
  <text x="110" y="990" class="code-text">FrameBuffer → ShaderProgram → GaussianBlur/BlackMask → OutputTexture</text>
  <text x="110" y="1010" class="flow-text">• <tspan style="color: #f39c12;">着色器：</tspan>使用GPU着色器实现高斯模糊算法</text>
  <text x="110" y="1025" class="flow-text">• <tspan style="color: #f39c12;">纹理处理：</tspan>对指定区域进行纹理处理</text>
  <text x="110" y="1040" class="flow-text">• <tspan style="color: #f39c12;">实时性：</tspan>GPU并行处理确保30fps流畅渲染</text>
  <text x="110" y="1055" class="flow-text">• <tspan style="color: #f39c12;">混合模式：</tspan>将虚化效果与原始画面混合</text>

  <!-- 第四部分：前端显示机制 -->
  <text x="900" y="675" class="layer-title">📱 前端显示机制</text>
  
  <text x="920" y="700" class="flow-text" style="font-weight: bold;">1. 渲染结果输出</text>
  <text x="940" y="720" class="code-text">// 渲染管线输出</text>
  <text x="940" y="735" class="code-text">ProcessedFrame → SurfaceView → UserScreen</text>
  <text x="940" y="755" class="flow-text">• <tspan style="color: #9b59b6;">帧缓冲：</tspan>渲染结果存储在帧缓冲区</text>
  <text x="940" y="770" class="flow-text">• <tspan style="color: #9b59b6;">表面视图：</tspan>通过SurfaceView显示到屏幕</text>
  <text x="940" y="785" class="flow-text">• <tspan style="color: #9b59b6;">硬件加速：</tspan>利用硬件加速确保流畅显示</text>
  
  <text x="920" y="810" class="flow-text" style="font-weight: bold;">2. 用户视觉效果</text>
  <text x="940" y="830" class="flow-text">• <tspan style="color: #9b59b6;">实时跟随：</tspan>虚化区域实时跟随用户视点移动</text>
  <text x="940" y="845" class="flow-text">• <tspan style="color: #9b59b6;">流畅性：</tspan>30fps确保视觉效果流畅自然</text>
  <text x="940" y="860" class="flow-text">• <tspan style="color: #9b59b6;">治疗效果：</tspan>通过遮盖弱视眼视野实现治疗目的</text>
  <text x="940" y="875" class="flow-text">• <tspan style="color: #9b59b6;">参数可调：</tspan>虚化强度、范围、模式可实时调整</text>
  
  <text x="920" y="900" class="flow-text" style="font-weight: bold;">3. 性能优化</text>
  <text x="940" y="920" class="flow-text">• <tspan style="color: #9b59b6;">GPU加速：</tspan>充分利用GPU并行计算能力</text>
  <text x="940" y="935" class="flow-text">• <tspan style="color: #9b59b6;">内存管理：</tspan>高效的纹理和缓冲区管理</text>
  <text x="940" y="950" class="flow-text">• <tspan style="color: #9b59b6;">算法优化：</tspan>优化的高斯模糊算法实现</text>
  <text x="940" y="965" class="flow-text">• <tspan style="color: #9b59b6;">资源回收：</tspan>及时释放不需要的渲染资源</text>

  <!-- 第五部分：关键参数说明 -->
  <text x="70" y="1000" class="layer-title">⚙️ 关键参数说明</text>
  
  <text x="90" y="1025" class="flow-text" style="font-weight: bold;">1. 虚化参数配置</text>
  <text x="110" y="1045" class="code-text">• pq_radius: 虚化区域半径 (0.5~5.5mm黄斑区直径)</text>
  <text x="110" y="1060" class="code-text">• GKSigma: 高斯模糊强度参数</text>
  <text x="110" y="1075" class="code-text">• channel: 颜色通道选择 (1-7)</text>
  <text x="110" y="1090" class="code-text">• mode: 虚化模式 (InnerBlur/OuterBlur/InnerBlack/OuterBlack)</text>
  
  <text x="90" y="1115" class="flow-text" style="font-weight: bold;">2. 位置计算参数</text>
  <text x="110" y="1135" class="code-text">• visual_image_width/height: 显示区域尺寸</text>
  <text x="110" y="1150" class="code-text">• amblyopic_eye: 弱视眼位置 (LEFT/RIGHT)</text>
  <text x="110" y="1165" class="code-text">• gaze_x/y: 实时视点坐标</text>
  <text x="110" y="1180" class="code-text">• blur_x/y: 计算后的虚化中心位置</text>
  
  <text x="90" y="1205" class="flow-text" style="font-weight: bold;">3. 渲染性能参数</text>
  <text x="110" y="1225" class="code-text">• frame_rate: 渲染帧率 (30fps)</text>
  <text x="110" y="1240" class="code-text">• texture_size: 纹理尺寸</text>
  <text x="110" y="1255" class="code-text">• buffer_count: 缓冲区数量</text>
  <text x="110" y="1270" class="code-text">• shader_precision: 着色器精度</text>

  <!-- 第六部分：资源管理 -->
  <text x="900" y="1000" class="layer-title">🔧 资源管理</text>
  
  <text x="920" y="1025" class="flow-text" style="font-weight: bold;">1. 虚化引擎生命周期</text>
  <text x="940" y="1045" class="code-text">// 创建虚化引擎</text>
  <text x="940" y="1060" class="code-text">pq_blr = newPQ_BLR();  // 分配内存和GPU资源</text>
  <text x="940" y="1075" class="code-text">// 配置参数</text>
  <text x="940" y="1090" class="code-text">pq_blr->SetRadius()->SetChannel()->SetPos()->Done();</text>
  <text x="940" y="1105" class="code-text">// 销毁虚化引擎</text>
  <text x="940" y="1120" class="code-text">deletePQ_BLR(pq_blr);  // 释放内存和GPU资源</text>
  <text x="940" y="1135" class="code-text">pq_blr = nullptr;</text>
  
  <text x="920" y="1160" class="flow-text" style="font-weight: bold;">2. 内存和GPU资源管理</text>
  <text x="940" y="1180" class="flow-text">• <tspan style="color: #e74c3c;">内存分配：</tspan>动态分配虚化引擎所需内存</text>
  <text x="940" y="1195" class="flow-text">• <tspan style="color: #e74c3c;">GPU资源：</tspan>分配纹理、缓冲区、着色器等GPU资源</text>
  <text x="940" y="1210" class="flow-text">• <tspan style="color: #e74c3c;">资源释放：</tspan>及时释放不再使用的资源</text>
  <text x="940" y="1225" class="flow-text">• <tspan style="color: #e74c3c;">异常处理：</tspan>确保资源分配失败时的正确处理</text>
  
  <text x="920" y="1250" class="flow-text" style="font-weight: bold;">3. 状态管理</text>
  <text x="940" y="1270" class="flow-text">• <tspan style="color: #e74c3c;">blur_enable_flag：</tspan>虚化功能启用状态</text>
  <text x="940" y="1285" class="flow-text">• <tspan style="color: #e74c3c;">重复检查：</tspan>防止重复开启虚化功能</text>
  <text x="940" y="1300" class="flow-text">• <tspan style="color: #e74c3c;">空指针检查：</tspan>确保pq_blr指针有效性</text>
  <text x="940" y="1315" class="flow-text">• <tspan style="color: #e74c3c;">日志记录：</tspan>记录关键操作和状态变化</text>

  <!-- 总结 -->
  <rect x="70" y="1350" width="1550" height="180" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1375" class="layer-title">🌟 视点获取与虚化渲染总结</text>
  
  <text x="90" y="1400" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">视点获取：</tspan>通过红外相机30fps采集 → 眼动追踪算法 → 实时计算注视点坐标</text>
  <text x="90" y="1420" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">虚化处理：</tspan>PqBlur引擎根据视点位置和弱视眼信息 → 计算虚化区域 → 应用高斯模糊或置黑效果</text>
  <text x="90" y="1440" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">实时渲染：</tspan>GPU加速的OpenGL渲染管线 → 30fps流畅虚化效果 → 实时跟随视点移动</text>
  <text x="90" y="1460" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">前端显示：</tspan>通过SurfaceView将渲染结果显示到屏幕 → 用户看到实时跟随的遮盖效果</text>
  <text x="90" y="1480" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">资源管理：</tspan>完善的内存和GPU资源管理 → 确保系统稳定性和性能优化</text>
  <text x="90" y="1500" class="flow-text">• <tspan style="font-weight: bold; color: #16a085;">治疗效果：</tspan>通过精确的视点追踪和实时虚化，实现对弱视眼的有效遮盖治疗</text>

</svg>
