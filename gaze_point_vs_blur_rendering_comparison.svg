<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .gaze-point-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .blur-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .window-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .gpu-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .surface-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .gaze-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .blur-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .window-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .gpu-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .surface-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .gaze-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .blur-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .vs-arrow { stroke: #e74c3c; stroke-width: 4; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 10,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">视点显示 vs 遮盖疗法渲染方式对比</text>
  
  <!-- 左侧：视点显示 -->
  <rect x="50" y="80" width="700" height="400" style="fill: #f0f8ff; stroke: #3498db; stroke-width: 3; rx: 15;"/>
  <text x="400" y="110" text-anchor="middle" class="section-title" style="fill: #3498db;">视点显示 (DotView)</text>
  
  <!-- 视点显示层级 -->
  <rect x="80" y="130" width="280" height="80" class="gaze-point-layer"/>
  <text x="220" y="155" text-anchor="middle" class="section-title">系统级悬浮窗</text>
  <rect x="100" y="170" width="240" height="30" class="gaze-module"/>
  <text x="220" y="190" text-anchor="middle" class="method-title">WindowManager + TYPE_APPLICATION_OVERLAY</text>
  
  <rect x="80" y="230" width="280" height="80" class="window-layer"/>
  <text x="220" y="255" text-anchor="middle" class="section-title">20x20px 绿色圆点</text>
  <rect x="100" y="270" width="240" height="30" class="window-module"/>
  <text x="220" y="290" text-anchor="middle" class="method-title">半透明 + 不可点击 + 穿透触摸</text>
  
  <rect x="80" y="330" width="280" height="80" class="surface-layer"/>
  <text x="220" y="355" text-anchor="middle" class="section-title">显示在所有应用之上</text>
  <rect x="100" y="370" width="240" height="30" class="surface-module"/>
  <text x="220" y="390" text-anchor="middle" class="method-title">全局可见 + 实时跟随</text>
  
  <!-- 视点特点 -->
  <rect x="400" y="130" width="320" height="280" class="gaze-module"/>
  <text x="560" y="155" text-anchor="middle" class="layer-title">视点显示特点</text>
  <text x="420" y="180" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">渲染方式：</tspan>Android View + WindowManager</text>
  <text x="420" y="200" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">显示位置：</tspan>系统级悬浮窗，最顶层</text>
  <text x="420" y="220" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">权限要求：</tspan>SYSTEM_ALERT_WINDOW</text>
  <text x="420" y="240" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">视觉效果：</tspan>小圆点，不遮挡内容</text>
  <text x="420" y="260" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">交互性：</tspan>不可点击，穿透触摸</text>
  <text x="420" y="280" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">应用范围：</tspan>全局显示，跨应用可见</text>
  <text x="420" y="300" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">性能影响：</tspan>极小，只是简单View</text>
  <text x="420" y="320" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">用途：</tspan>视点反馈，调试辅助</text>
  <text x="420" y="340" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">技术层级：</tspan>Android UI层</text>
  <text x="420" y="360" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">实现复杂度：</tspan>简单</text>
  <text x="420" y="380" class="flow-text">• <tspan style="color: #3498db; font-weight: bold;">资源占用：</tspan>很少</text>

  <!-- VS 分隔符 -->
  <text x="900" y="320" text-anchor="middle" class="title" style="font-size: 48px; fill: #e74c3c;">VS</text>
  <line x1="780" y1="280" x2="1020" y2="280" class="vs-arrow"/>
  <line x1="780" y1="360" x2="1020" y2="360" class="vs-arrow"/>

  <!-- 右侧：遮盖疗法 -->
  <rect x="1050" y="80" width="700" height="400" style="fill: #fff5f5; stroke: #e74c3c; stroke-width: 3; rx: 15;"/>
  <text x="1400" y="110" text-anchor="middle" class="section-title" style="fill: #e74c3c;">遮盖疗法 (PqBlur)</text>
  
  <!-- 遮盖疗法层级 -->
  <rect x="1080" y="130" width="280" height="80" class="blur-layer"/>
  <text x="1220" y="155" text-anchor="middle" class="section-title">GPU渲染引擎</text>
  <rect x="1100" y="170" width="240" height="30" class="blur-module"/>
  <text x="1220" y="190" text-anchor="middle" class="method-title">IPQ_BLR + OpenGL着色器</text>
  
  <rect x="1080" y="230" width="280" height="80" class="gpu-layer"/>
  <text x="1220" y="255" text-anchor="middle" class="section-title">实时虚化效果</text>
  <rect x="1100" y="270" width="240" height="30" class="gpu-module"/>
  <text x="1220" y="290" text-anchor="middle" class="method-title">高斯模糊 + 置黑 + 动态遮盖</text>
  
  <rect x="1080" y="330" width="280" height="80" class="surface-layer"/>
  <text x="1220" y="355" text-anchor="middle" class="section-title">应用内Surface渲染</text>
  <rect x="1100" y="370" width="240" height="30" class="surface-module"/>
  <text x="1220" y="390" text-anchor="middle" class="method-title">ANativeWindow + 帧缓冲</text>
  
  <!-- 遮盖特点 -->
  <rect x="1400" y="130" width="320" height="280" class="blur-module"/>
  <text x="1560" y="155" text-anchor="middle" class="layer-title">遮盖疗法特点</text>
  <text x="1420" y="180" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">渲染方式：</tspan>GPU + OpenGL着色器</text>
  <text x="1420" y="200" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">显示位置：</tspan>应用内Surface，不跨应用</text>
  <text x="1420" y="220" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">权限要求：</tspan>无特殊权限</text>
  <text x="1420" y="240" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">视觉效果：</tspan>大面积虚化，遮盖内容</text>
  <text x="1420" y="260" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">交互性：</tspan>完全遮盖，阻挡视线</text>
  <text x="1420" y="280" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">应用范围：</tspan>仅当前应用内有效</text>
  <text x="1420" y="300" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">性能影响：</tspan>较大，GPU密集计算</text>
  <text x="1420" y="320" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">用途：</tspan>医疗治疗，弱视矫正</text>
  <text x="1420" y="340" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">技术层级：</tspan>Native C++ + GPU</text>
  <text x="1420" y="360" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">实现复杂度：</tspan>复杂</text>
  <text x="1420" y="380" class="flow-text">• <tspan style="color: #e74c3c; font-weight: bold;">资源占用：</tspan>大量GPU资源</text>

  <!-- 详细技术对比 -->
  <rect x="50" y="520" width="1700" height="1030" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="545" text-anchor="middle" class="title" style="font-size: 22px;">技术实现详细对比</text>

  <!-- 第一部分：渲染层级对比 -->
  <text x="70" y="580" class="layer-title">🏗️ 渲染层级对比</text>
  
  <text x="90" y="605" class="flow-text" style="font-weight: bold;">视点显示 (DotView) - 系统UI层</text>
  <text x="110" y="625" class="code-text">Android应用层 → WindowManager → 系统悬浮窗 → 屏幕最顶层</text>
  <text x="110" y="645" class="flow-text">• <tspan style="color: #3498db;">显示层级：</tspan>系统级，覆盖所有应用</text>
  <text x="110" y="660" class="flow-text">• <tspan style="color: #3498db;">渲染引擎：</tspan>Android View系统</text>
  <text x="110" y="675" class="flow-text">• <tspan style="color: #3498db;">硬件加速：</tspan>使用系统默认的硬件加速</text>
  <text x="110" y="690" class="flow-text">• <tspan style="color: #3498db;">跨应用：</tspan>可以在任何应用上显示</text>
  
  <text x="90" y="715" class="flow-text" style="font-weight: bold;">遮盖疗法 (PqBlur) - GPU渲染层</text>
  <text x="110" y="735" class="code-text">Native C++ → IPQ_BLR引擎 → OpenGL着色器 → ANativeWindow → 应用Surface</text>
  <text x="110" y="755" class="flow-text">• <tspan style="color: #e74c3c;">显示层级：</tspan>应用内，仅在当前应用有效</text>
  <text x="110" y="770" class="flow-text">• <tspan style="color: #e74c3c;">渲染引擎：</tspan>自定义GPU引擎</text>
  <text x="110" y="785" class="flow-text">• <tspan style="color: #e74c3c;">硬件加速：</tspan>专用GPU着色器和OpenGL</text>
  <text x="110" y="800" class="flow-text">• <tspan style="color: #e74c3c;">应用限制：</tspan>只能在眼动追踪应用内显示</text>

  <!-- 第二部分：技术实现对比 -->
  <text x="900" y="580" class="layer-title">⚙️ 技术实现对比</text>
  
  <text x="920" y="605" class="flow-text" style="font-weight: bold;">视点显示技术栈</text>
  <text x="940" y="625" class="code-text">// Kotlin/Java层</text>
  <text x="940" y="640" class="code-text">class DotView : View {</text>
  <text x="960" y="655" class="code-text">fun show(x: Int, y: Int) {</text>
  <text x="980" y="670" class="code-text">windowManager.addView(this, dotParams)</text>
  <text x="960" y="685" class="code-text">}</text>
  <text x="940" y="700" class="code-text">}</text>
  
  <text x="920" y="725" class="flow-text" style="font-weight: bold;">遮盖疗法技术栈</text>
  <text x="940" y="745" class="code-text">// C++层</text>
  <text x="940" y="760" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y) {</text>
  <text x="960" y="775" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="940" y="790" class="code-text">}</text>
  <text x="940" y="805" class="code-text">// GPU着色器</text>
  <text x="940" y="820" class="code-text">OpenGL Fragment Shader → 高斯模糊算法 → 帧缓冲</text>

  <!-- 第三部分：显示范围对比 -->
  <text x="70" y="855" class="layer-title">📱 显示范围对比</text>
  
  <text x="90" y="880" class="flow-text" style="font-weight: bold;">视点显示范围</text>
  <text x="110" y="900" class="flow-text">• <tspan style="color: #3498db;">全局显示：</tspan>在任何应用界面上都可见</text>
  <text x="110" y="915" class="flow-text">• <tspan style="color: #3498db;">系统级：</tspan>甚至可以显示在锁屏界面上</text>
  <text x="110" y="930" class="flow-text">• <tspan style="color: #3498db;">跨应用：</tspan>切换应用时视点依然显示</text>
  <text x="110" y="945" class="flow-text">• <tspan style="color: #3498db;">权限控制：</tspan>需要用户授权悬浮窗权限</text>
  
  <text x="90" y="970" class="flow-text" style="font-weight: bold;">遮盖疗法显示范围</text>
  <text x="110" y="990" class="flow-text">• <tspan style="color: #e74c3c;">应用内限制：</tspan>只在眼动追踪应用内有效</text>
  <text x="110" y="1005" class="flow-text">• <tspan style="color: #e74c3c;">Surface绑定：</tspan>绑定到特定的ANativeWindow</text>
  <text x="110" y="1020" class="flow-text">• <tspan style="color: #e74c3c;">应用切换：</tspan>切换到其他应用时遮盖效果消失</text>
  <text x="110" y="1035" class="flow-text">• <tspan style="color: #e74c3c;">无需权限：</tspan>在自己应用内渲染，无需特殊权限</text>

  <!-- 第四部分：性能和资源对比 -->
  <text x="900" y="855" class="layer-title">⚡ 性能和资源对比</text>
  
  <text x="920" y="880" class="flow-text" style="font-weight: bold;">视点显示性能</text>
  <text x="940" y="900" class="flow-text">• <tspan style="color: #3498db;">CPU占用：</tspan>极低，只是简单的View绘制</text>
  <text x="940" y="915" class="flow-text">• <tspan style="color: #3498db;">GPU占用：</tspan>几乎为0，系统默认渲染</text>
  <text x="940" y="930" class="flow-text">• <tspan style="color: #3498db;">内存占用：</tspan>很少，只有一个小View</text>
  <text x="940" y="945" class="flow-text">• <tspan style="color: #3498db;">电池影响：</tspan>可忽略不计</text>
  
  <text x="920" y="970" class="flow-text" style="font-weight: bold;">遮盖疗法性能</text>
  <text x="940" y="990" class="flow-text">• <tspan style="color: #e74c3c;">CPU占用：</tspan>中等，坐标计算和参数管理</text>
  <text x="940" y="1005" class="flow-text">• <tspan style="color: #e74c3c;">GPU占用：</tspan>高，实时着色器计算</text>
  <text x="940" y="1020" class="flow-text">• <tspan style="color: #e74c3c;">内存占用：</tspan>较大，纹理缓冲和帧缓冲</text>
  <text x="940" y="1035" class="flow-text">• <tspan style="color: #e74c3c;">电池影响：</tspan>明显，GPU密集计算</text>

  <!-- 第五部分：用途和目标对比 -->
  <text x="70" y="1070" class="layer-title">🎯 用途和目标对比</text>
  
  <text x="90" y="1095" class="flow-text" style="font-weight: bold;">视点显示用途</text>
  <text x="110" y="1115" class="flow-text">• <tspan style="color: #3498db;">调试辅助：</tspan>开发时验证眼动追踪精度</text>
  <text x="110" y="1130" class="flow-text">• <tspan style="color: #3498db;">用户反馈：</tspan>让用户看到当前注视位置</text>
  <text x="110" y="1145" class="flow-text">• <tspan style="color: #3498db;">校准辅助：</tspan>帮助用户进行眼动校准</text>
  <text x="110" y="1160" class="flow-text">• <tspan style="color: #3498db;">演示工具：</tspan>向他人展示眼动追踪效果</text>
  
  <text x="90" y="1185" class="flow-text" style="font-weight: bold;">遮盖疗法用途</text>
  <text x="110" y="1205" class="flow-text">• <tspan style="color: #e74c3c;">医疗治疗：</tspan>弱视治疗的核心功能</text>
  <text x="110" y="1220" class="flow-text">• <tspan style="color: #e74c3c;">视觉遮盖：</tspan>阻挡弱视眼的视觉输入</text>
  <text x="110" y="1235" class="flow-text">• <tspan style="color: #e74c3c;">治疗效果：</tspan>强制使用健康眼，改善弱视</text>
  <text x="110" y="1250" class="flow-text">• <tspan style="color: #e74c3c;">精确控制：</tspan>根据视点位置精确遮盖</text>

  <!-- 第六部分：技术架构对比 -->
  <text x="900" y="1070" class="layer-title">🏛️ 技术架构对比</text>
  
  <text x="920" y="1095" class="flow-text" style="font-weight: bold;">视点显示架构</text>
  <text x="940" y="1115" class="code-text">GazeTrackService → WidgetManager → DotView → WindowManager → 系统显示</text>
  <text x="940" y="1135" class="flow-text">• <tspan style="color: #3498db;">简单架构：</tspan>标准Android UI组件</text>
  <text x="940" y="1150" class="flow-text">• <tspan style="color: #3498db;">系统依赖：</tspan>依赖Android窗口系统</text>
  <text x="940" y="1165" class="flow-text">• <tspan style="color: #3498db;">易于维护：</tspan>使用标准API，稳定可靠</text>
  
  <text x="920" y="1190" class="flow-text" style="font-weight: bold;">遮盖疗法架构</text>
  <text x="940" y="1210" class="code-text">GazeApplication → PqBlur → IPQ_BLR → OpenGL → ANativeWindow → Surface</text>
  <text x="940" y="1230" class="flow-text">• <tspan style="color: #e74c3c;">复杂架构：</tspan>自定义GPU渲染引擎</text>
  <text x="940" y="1245" class="flow-text">• <tspan style="color: #e74c3c;">底层控制：</tspan>直接操作GPU和帧缓冲</text>
  <text x="940" y="1260" class="flow-text">• <tspan style="color: #e74c3c;">高度定制：</tspan>专门为医疗应用优化</text>

  <!-- 总结 -->
  <rect x="70" y="1290" width="1600" height="240" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1315" class="layer-title">🌟 核心区别总结</text>
  
  <text x="90" y="1340" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">视点显示：</tspan>系统级悬浮窗，全局可见，轻量级，用于反馈和调试</text>
  <text x="90" y="1360" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">遮盖疗法：</tspan>应用内GPU渲染，重量级，用于医疗治疗</text>
  <text x="90" y="1380" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">显示层级：</tspan>视点在系统最顶层，遮盖在应用Surface层</text>
  <text x="90" y="1400" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">技术复杂度：</tspan>视点使用标准Android API，遮盖使用自定义GPU引擎</text>
  <text x="90" y="1420" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">应用范围：</tspan>视点跨应用全局显示，遮盖仅在当前应用内有效</text>
  <text x="90" y="1440" class="flow-text">• <tspan style="font-weight: bold; color: #e67e22;">性能影响：</tspan>视点几乎无影响，遮盖需要大量GPU资源</text>
  <text x="90" y="1460" class="flow-text">• <tspan style="font-weight: bold; color: #8e44ad;">权限要求：</tspan>视点需要悬浮窗权限，遮盖无需特殊权限</text>
  <text x="90" y="1480" class="flow-text">• <tspan style="font-weight: bold; color: #d35400;">功能目标：</tspan>视点用于辅助和反馈，遮盖用于实际医疗治疗</text>
  <text x="90" y="1500" class="flow-text">• <tspan style="font-weight: bold; color: #2c3e50;">实现方式：</tspan>完全不同的技术路径，服务于不同的需求</text>

</svg>
