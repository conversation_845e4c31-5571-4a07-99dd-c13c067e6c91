<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <linearGradient id="cameraGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976d2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="jniGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f57c00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="uiGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4caf50;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9c27b0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    MSG_GAZE_TRAJECTORY_RESULT 消息处理流程
  </text>

  <!-- 相机实时数据采集层 -->
  <rect x="50" y="60" width="300" height="100" rx="10" fill="url(#cameraGrad)" stroke="#1976d2" stroke-width="2"/>
  <text x="200" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#fff">相机实时数据采集</text>
  <text x="200" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">30fps图像流</text>
  <text x="200" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">每帧调用onAnalyze回调</text>
  <text x="200" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">实时视线追踪</text>

  <!-- JNI数据收集层 -->
  <rect x="400" y="60" width="300" height="100" rx="10" fill="url(#jniGrad)" stroke="#f57c00" stroke-width="2"/>
  <text x="550" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#fff">JNI数据收集层</text>
  <text x="550" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">nativeCollectGaze调用</text>
  <text x="550" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">传递: valid, x, y, dist, duration</text>
  <text x="550" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#fff">C++层数据累积</text>

  <!-- C++数据处理层 -->
  <rect x="750" y="60" width="300" height="100" rx="10" fill="#ffebee" stroke="#d32f2f" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#d32f2f">C++数据处理层</text>
  <text x="900" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#d32f2f">GazeApplication::collect_gaze</text>
  <text x="900" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#d32f2f">按模式分发数据</text>
  <text x="900" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#d32f2f">vector&lt;Point3f&gt;存储</text>

  <!-- 数据参数详细说明 -->
  <rect x="100" y="200" width="1200" height="120" rx="10" fill="#f9f9f9" stroke="#666" stroke-width="2"/>
  <text x="700" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#333">JNI方法参数详细说明</text>

  <!-- nativeCollectGaze参数 -->
  <rect x="150" y="250" width="200" height="60" rx="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="250" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">nativeCollectGaze</text>
  <text x="250" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">valid: Boolean</text>
  <text x="250" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">x, y: Float (0-1)</text>

  <rect x="370" y="250" width="200" height="60" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="470" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f57c00">距离和时间参数</text>
  <text x="470" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">dist: Float (35-65cm)</text>
  <text x="470" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">duration: Float (ms)</text>

  <rect x="590" y="250" width="200" height="60" rx="5" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
  <text x="690" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#388e3c">C++存储结构</text>
  <text x="690" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Point3f(x, y, duration)</text>
  <text x="690" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">vector累积存储</text>

  <rect x="810" y="250" width="200" height="60" rx="5" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
  <text x="910" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7b1fa2">模式分发</text>
  <text x="910" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">STARE/FOLLOW/GLANCE</text>
  <text x="910" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">不同算法处理</text>

  <rect x="1030" y="250" width="200" height="60" rx="5" fill="#ffebee" stroke="#f44336" stroke-width="1"/>
  <text x="1130" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">数据后处理</text>
  <text x="1130" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">postprocess_trajectory</text>
  <text x="1130" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">聚类分析</text>

  <!-- 轨迹数据获取触发 -->
  <rect x="200" y="360" width="1000" height="80" rx="10" fill="#fff8e1" stroke="#ffc107" stroke-width="2"/>
  <text x="700" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#f57c00">轨迹数据获取触发</text>
  <text x="700" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">用户操作完成 → 发送MSG_GET_GAZE_TRAJECTORY消息 → 调用nativeGetGazeTrajectory</text>
  <text x="700" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">C++层: get_record_data_func(mode) → transfer_trajectory_to_jsondata → 返回JSON字符串</text>

  <!-- JSON数据结构 -->
  <rect x="50" y="480" width="600" height="180" rx="10" fill="url(#dataGrad)" stroke="#9c27b0" stroke-width="2"/>
  <text x="350" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">返回的JSON数据结构</text>

  <rect x="80" y="520" width="540" height="130" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="100" y="540" font-family="Courier New, monospace" font-size="11" fill="#333">{</text>
  <text x="120" y="555" font-family="Courier New, monospace" font-size="11" fill="#333">"gaze": [</text>
  <text x="140" y="570" font-family="Courier New, monospace" font-size="11" fill="#333">{</text>
  <text x="160" y="585" font-family="Courier New, monospace" font-size="11" fill="#333">"index": 1,</text>
  <text x="160" y="600" font-family="Courier New, monospace" font-size="11" fill="#333">"x": 0.5234,        // 视点X坐标(0-1)</text>
  <text x="160" y="615" font-family="Courier New, monospace" font-size="11" fill="#333">"y": 0.4567,        // 视点Y坐标(0-1)</text>
  <text x="160" y="630" font-family="Courier New, monospace" font-size="11" fill="#333">"duration": 150     // 持续时间(ms)</text>
  <text x="140" y="645" font-family="Courier New, monospace" font-size="11" fill="#333">}, ...</text>

  <!-- UI层处理 -->
  <rect x="700" y="480" width="650" height="180" rx="10" fill="url(#uiGrad)" stroke="#4caf50" stroke-width="2"/>
  <text x="1025" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">UI层数据处理和显示</text>

  <!-- 消息接收 -->
  <rect x="720" y="520" width="280" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="860" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">消息接收处理</text>
  <text x="860" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">MSG_GAZE_TRAJECTORY_RESULT</text>
  <text x="860" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">JSON解析为GazeTrajectory</text>

  <!-- 数据转换 -->
  <rect x="1020" y="520" width="280" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="1160" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">数据转换</text>
  <text x="1160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Gson.fromJson()</text>
  <text x="1160" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">List&lt;GazePoint&gt;</text>

  <!-- UI更新 -->
  <rect x="720" y="590" width="280" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="860" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">UI显示更新</text>
  <text x="860" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">轨迹路径绘制</text>
  <text x="860" y="640" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">热力图生成</text>

  <!-- 结果分析 -->
  <rect x="1020" y="590" width="280" height="60" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="1160" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">结果分析</text>
  <text x="1160" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">精度计算</text>
  <text x="1160" y="640" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">报告生成</text>

  <!-- UI具体变化示例 -->
  <rect x="50" y="700" width="1300" height="200" rx="10" fill="#f5f5f5" stroke="#999" stroke-width="2"/>
  <text x="700" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#333">UI层根据轨迹参数的具体变化</text>

  <!-- 轨迹绘制 -->
  <rect x="100" y="750" width="200" height="120" rx="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="200" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">轨迹路径绘制</text>
  <text x="200" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">x, y → 屏幕坐标</text>
  <text x="200" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">screenX = x * width</text>
  <text x="200" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">screenY = y * height</text>
  <text x="200" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Path.lineTo(screenX, screenY)</text>
  <text x="200" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Canvas绘制连线</text>
  <text x="200" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">实时/静态模式</text>

  <!-- 注视点分析 -->
  <rect x="320" y="750" width="200" height="120" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="420" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f57c00">注视点分析</text>
  <text x="420" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">duration → 停留时间</text>
  <text x="420" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">&gt;100ms = 有效注视</text>
  <text x="420" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">聚类相近点</text>
  <text x="420" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">计算注视中心</text>
  <text x="420" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">圆圈大小 ∝ duration</text>
  <text x="420" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">颜色深度表示强度</text>

  <!-- 精度评估 -->
  <rect x="540" y="750" width="200" height="120" rx="5" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
  <text x="640" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#388e3c">精度评估</text>
  <text x="640" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">目标点 vs 实际点</text>
  <text x="640" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">距离偏差计算</text>
  <text x="640" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">√[(x1-x2)²+(y1-y2)²]</text>
  <text x="640" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">精度百分比</text>
  <text x="640" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">颜色编码反馈</text>
  <text x="640" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">绿色=准确 红色=偏差</text>

  <!-- 动态播放 -->
  <rect x="760" y="750" width="200" height="120" rx="5" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
  <text x="860" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7b1fa2">动态播放</text>
  <text x="860" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">按时间序列播放</text>
  <text x="860" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">delay(100ms)间隔</text>
  <text x="860" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">逐点添加到Path</text>
  <text x="860" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">实时invalidate()</text>
  <text x="860" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">轨迹逐渐显现</text>
  <text x="860" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">用户体验优化</text>

  <!-- 热力图生成 -->
  <rect x="980" y="750" width="200" height="120" rx="5" fill="#ffebee" stroke="#f44336" stroke-width="1"/>
  <text x="1080" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">热力图生成</text>
  <text x="1080" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">密度统计</text>
  <text x="1080" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">网格划分</text>
  <text x="1080" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">权重 = duration</text>
  <text x="1080" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">高斯模糊</text>
  <text x="1080" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">颜色映射</text>
  <text x="1080" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">蓝→绿→黄→红</text>

  <!-- 数据统计 -->
  <rect x="1200" y="750" width="150" height="120" rx="5" fill="#fff8e1" stroke="#ffc107" stroke-width="1"/>
  <text x="1275" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f57c00">数据统计</text>
  <text x="1275" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">总点数</text>
  <text x="1275" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">有效点数</text>
  <text x="1275" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">平均精度</text>
  <text x="1275" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">总时长</text>
  <text x="1275" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">反应时间</text>
  <text x="1275" y="860" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">评估等级</text>

  <!-- 箭头连接 -->
  <line x1="350" y1="110" x2="400" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="110" x2="750" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="900" y1="160" x2="900" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="320" x2="700" y2="360" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="350" y1="440" x2="350" y2="480" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1025" y1="440" x2="1025" y2="480" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="660" x2="700" y2="700" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 循环数据流 -->
  <path d="M 200 180 Q 100 220 100 300 Q 100 380 200 420 Q 300 460 400 420 Q 500 380 500 300 Q 500 220 400 180"
        stroke="#ff5722" stroke-width="3" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  <text x="300" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ff5722">实时数据流</text>

  <!-- 性能指标 -->
  <rect x="1200" y="920" width="180" height="60" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="1290" y="940" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ff9800">性能指标</text>
  <text x="1290" y="955" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">数据点: 1000+/次</text>
  <text x="1290" y="970" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">处理延迟: &lt;50ms</text>
</svg>