<?xml version="1.0" encoding="UTF-8"?>
<svg width="2000" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #e74c3c; }
      .actor-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #ffffff; text-anchor: middle; }
      .message-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .condition-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #e74c3c; font-style: italic; }
      .note-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      
      .actor-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 6; }
      .lifeline { stroke: #bdc3c7; stroke-width: 1; stroke-dasharray: 3,3; }
      .activation { fill: #ecf0f1; stroke: #3498db; stroke-width: 1; opacity: 0.8; }
      .message-arrow { stroke: #2c3e50; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 6,3; }
      .self-call { stroke: #e74c3c; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .async-arrow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
      .condition-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 1; rx: 4; }
      .alt-box { fill: #f8d7da; stroke: #dc3545; stroke-width: 1; rx: 4; }
    </style>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="1000" y="30" text-anchor="middle" class="title">HomeMainFragment 详细时序图</text>
  
  <!-- 参与者头部 -->
  <rect x="50" y="50" width="80" height="30" class="actor-box"/>
  <text x="90" y="70" class="actor-title">User</text>
  
  <rect x="150" y="50" width="100" height="30" class="actor-box"/>
  <text x="200" y="70" class="actor-title">HomeMainActivity</text>
  
  <rect x="270" y="50" width="100" height="30" class="actor-box"/>
  <text x="320" y="70" class="actor-title">HomeMainFragment</text>
  
  <rect x="390" y="50" width="90" height="30" class="actor-box"/>
  <text x="435" y="70" class="actor-title">UserViewModel</text>
  
  <rect x="500" y="50" width="90" height="30" class="actor-box"/>
  <text x="545" y="70" class="actor-title">HomeViewModel</text>
  
  <rect x="610" y="50" width="100" height="30" class="actor-box"/>
  <text x="660" y="70" class="actor-title">TreatmentViewModel</text>
  
  <rect x="730" y="50" width="90" height="30" class="actor-box"/>
  <text x="775" y="70" class="actor-title">MaskViewModel</text>
  
  <rect x="840" y="50" width="110" height="30" class="actor-box"/>
  <text x="895" y="70" class="actor-title">TreatmentAdapter</text>
  
  <rect x="970" y="50" width="90" height="30" class="actor-box"/>
  <text x="1015" y="70" class="actor-title">UserManager</text>
  
  <rect x="1080" y="50" width="90" height="30" class="actor-box"/>
  <text x="1125" y="70" class="actor-title">DeviceManager</text>
  
  <rect x="1190" y="50" width="100" height="30" class="actor-box"/>
  <text x="1240" y="70" class="actor-title">CalibrationActivity</text>
  
  <rect x="1310" y="50" width="80" height="30" class="actor-box"/>
  <text x="1350" y="70" class="actor-title">Repository</text>
  
  <rect x="1410" y="50" width="80" height="30" class="actor-box"/>
  <text x="1450" y="70" class="actor-title">LiveEventBus</text>
  
  <rect x="1510" y="50" width="100" height="30" class="actor-box"/>
  <text x="1560" y="70" class="actor-title">MaskTherapyFragment</text>
  
  <rect x="1630" y="50" width="100" height="30" class="actor-box"/>
  <text x="1680" y="70" class="actor-title">VisualTrainFragment</text>
  
  <!-- 生命线 -->
  <line x1="90" y1="80" x2="90" y2="1750" class="lifeline"/>
  <line x1="200" y1="80" x2="200" y2="1750" class="lifeline"/>
  <line x1="320" y1="80" x2="320" y2="1750" class="lifeline"/>
  <line x1="435" y1="80" x2="435" y2="1750" class="lifeline"/>
  <line x1="545" y1="80" x2="545" y2="1750" class="lifeline"/>
  <line x1="660" y1="80" x2="660" y2="1750" class="lifeline"/>
  <line x1="775" y1="80" x2="775" y2="1750" class="lifeline"/>
  <line x1="895" y1="80" x2="895" y2="1750" class="lifeline"/>
  <line x1="1015" y1="80" x2="1015" y2="1750" class="lifeline"/>
  <line x1="1125" y1="80" x2="1125" y2="1750" class="lifeline"/>
  <line x1="1240" y1="80" x2="1240" y2="1750" class="lifeline"/>
  <line x1="1350" y1="80" x2="1350" y2="1750" class="lifeline"/>
  <line x1="1450" y1="80" x2="1450" y2="1750" class="lifeline"/>
  <line x1="1560" y1="80" x2="1560" y2="1750" class="lifeline"/>
  <line x1="1680" y1="80" x2="1680" y2="1750" class="lifeline"/>

  <!-- 第一阶段：应用启动 -->
  <text x="50" y="110" class="section-title">阶段1: 应用启动与初始化</text>
  
  <!-- 用户启动应用 -->
  <line x1="90" y1="130" x2="200" y2="130" class="message-arrow"/>
  <text x="145" y="125" class="message-text">启动应用</text>
  
  <!-- Activity创建Fragment -->
  <rect x="195" y="140" width="10" height="40" class="activation"/>
  <line x1="200" y1="150" x2="320" y2="150" class="message-arrow"/>
  <text x="260" y="145" class="message-text">initView() → newInstance()</text>
  
  <!-- Fragment生命周期开始 -->
  <rect x="315" y="160" width="10" height="600" class="activation"/>
  <line x1="320" y1="170" x2="360" y2="170" class="self-call"/>
  <line x1="360" y1="170" x2="360" y2="180" class="self-call"/>
  <line x1="360" y1="180" x2="320" y2="180" class="self-call"/>
  <text x="365" y="175" class="message-text">onCreate()</text>
  
  <!-- 启动定时刷新 -->
  <line x1="320" y1="190" x2="360" y2="190" class="self-call"/>
  <line x1="360" y1="190" x2="360" y2="200" class="self-call"/>
  <line x1="360" y1="200" x2="320" y2="200" class="self-call"/>
  <text x="365" y="195" class="message-text">startRefreshBindUser()</text>
  
  <!-- 初始化BehaviorGuidanceManager -->
  <line x1="320" y1="210" x2="360" y2="210" class="async-arrow"/>
  <line x1="360" y1="210" x2="360" y2="220" class="async-arrow"/>
  <line x1="360" y1="220" x2="320" y2="220" class="async-arrow"/>
  <text x="365" y="215" class="message-text">BehaviorGuidanceManager.init()</text>
  
  <!-- Activity获取用户信息 -->
  <line x1="200" y1="240" x2="435" y2="240" class="message-arrow"/>
  <text x="317" y="235" class="message-text">initData() → getAccountInfo()</text>
  
  <!-- 第二阶段：用户信息处理 -->
  <text x="50" y="270" class="section-title">阶段2: 用户信息获取与处理</text>
  
  <!-- UserViewModel处理 -->
  <rect x="430" y="290" width="10" height="80" class="activation"/>
  <line x1="435" y1="300" x2="1350" y2="300" class="message-arrow"/>
  <text x="892" y="295" class="message-text">Repository.getAccountInfo()</text>
  
  <!-- Repository返回数据 -->
  <line x1="1350" y1="320" x2="435" y2="320" class="return-arrow"/>
  <text x="892" y="315" class="message-text">AccountInfo</text>
  
  <!-- LiveData通知Fragment -->
  <line x1="435" y1="340" x2="320" y2="340" class="async-arrow"/>
  <text x="377" y="335" class="message-text">accountInfoLiveData.postValue()</text>
  
  <!-- Fragment观察者响应 -->
  <line x1="320" y1="360" x2="360" y2="360" class="self-call"/>
  <line x1="360" y1="360" x2="360" y2="370" class="self-call"/>
  <line x1="360" y1="370" x2="320" y2="370" class="self-call"/>
  <text x="365" y="365" class="message-text">observe() 回调</text>
  
  <!-- 条件判断框 -->
  <rect x="50" y="380" width="300" height="40" class="condition-box"/>
  <text x="60" y="395" class="condition-text">if (UserManager.isBind())</text>
  <text x="60" y="410" class="condition-text">已绑定: 获取疗程信息 | 未绑定: 播放提示音</text>
  
  <!-- 检查绑定状态 -->
  <line x1="320" y1="430" x2="1015" y2="430" class="message-arrow"/>
  <text x="667" y="425" class="message-text">UserManager.isBind()</text>
  
  <line x1="1015" y1="450" x2="320" y2="450" class="return-arrow"/>
  <text x="667" y="445" class="message-text">boolean</text>
  
  <!-- 分支处理 -->
  <rect x="370" y="470" width="400" height="60" class="alt-box"/>
  <text x="380" y="485" class="condition-text">ALT [已绑定]</text>
  
  <!-- 已绑定分支 -->
  <line x1="320" y1="490" x2="545" y2="490" class="message-arrow"/>
  <text x="432" y="485" class="message-text">getMedicalHomeProfile()</text>
  
  <line x1="320" y1="510" x2="660" y2="510" class="message-arrow"/>
  <text x="490" y="505" class="message-text">getCurrentTreatment()</text>
  
  <!-- 未绑定分支 -->
  <text x="380" y="525" class="condition-text">[未绑定] PlayManager.playRawMedia()</text>
  
  <!-- 第三阶段：UI更新 -->
  <text x="50" y="560" class="section-title">阶段3: UI更新与状态同步</text>
  
  <!-- 更新用户头像 -->
  <line x1="320" y1="580" x2="360" y2="580" class="self-call"/>
  <line x1="360" y1="580" x2="360" y2="590" class="self-call"/>
  <line x1="360" y1="590" x2="320" y2="590" class="self-call"/>
  <text x="365" y="585" class="message-text">updateUserAvatar()</text>
  
  <!-- 获取遮盖疗法状态 -->
  <line x1="320" y1="600" x2="1125" y2="600" class="message-arrow"/>
  <text x="722" y="595" class="message-text">DeviceManager.getMaskTherapyState()</text>
  
  <!-- 获取弱视眼位置 -->
  <line x1="320" y1="620" x2="775" y2="620" class="message-arrow"/>
  <text x="547" y="615" class="message-text">maskVM.eyePosition</text>
  
  <!-- 更新用户名 -->
  <line x1="320" y1="640" x2="360" y2="640" class="self-call"/>
  <line x1="360" y1="640" x2="360" y2="650" class="self-call"/>
  <line x1="360" y1="650" x2="320" y2="650" class="self-call"/>
  <text x="365" y="645" class="message-text">updateUserName()</text>
  
  <!-- 更新设备信息 -->
  <line x1="320" y1="660" x2="1125" y2="660" class="message-arrow"/>
  <text x="722" y="655" class="message-text">updateDeviceInfo() → getDeviceInfo()</text>
  
  <!-- 获取AI授权 -->
  <line x1="320" y1="680" x2="360" y2="680" class="self-call"/>
  <line x1="360" y1="680" x2="360" y2="690" class="self-call"/>
  <line x1="360" y1="690" x2="320" y2="690" class="self-call"/>
  <text x="365" y="685" class="message-text">adaVM.getAuthCode()</text>
  
  <!-- 第四阶段：疗程模块配置 -->
  <text x="50" y="720" class="section-title">阶段4: 疗程模块配置与Fragment管理</text>
  
  <!-- HomeViewModel获取配置 -->
  <rect x="540" y="740" width="10" height="60" class="activation"/>
  <line x1="545" y1="750" x2="1350" y2="750" class="message-arrow"/>
  <text x="947" y="745" class="message-text">API: /api/device/v1/profile/home</text>
  
  <line x1="1350" y1="770" x2="545" y2="770" class="return-arrow"/>
  <text x="947" y="765" class="message-text">MedicalHomeProfile</text>
  
  <!-- LiveData通知 -->
  <line x1="545" y1="790" x2="320" y2="790" class="async-arrow"/>
  <text x="432" y="785" class="message-text">medicalHomeProfileLiveData</text>
  
  <!-- Fragment处理模块数据 -->
  <line x1="320" y1="810" x2="360" y2="810" class="self-call"/>
  <line x1="360" y1="810" x2="360" y2="820" class="self-call"/>
  <line x1="360" y1="820" x2="320" y2="820" class="self-call"/>
  <text x="365" y="815" class="message-text">updateTreatmentModule()</text>
  
  <!-- 过滤模块 -->
  <rect x="370" y="830" width="350" height="40" class="condition-box"/>
  <text x="380" y="845" class="condition-text">过滤启用模块: OCCLUSION_THERAPY, VISION_THERAPY</text>
  
  <!-- 设置Adapter数据 -->
  <line x1="320" y1="880" x2="895" y2="880" class="message-arrow"/>
  <text x="607" y="875" class="message-text">setTreatmentModuleData(modules, childFragmentManager)</text>
  
  <!-- Adapter处理 -->
  <rect x="890" y="890" width="10" height="120" class="activation"/>
  <line x1="895" y1="900" x2="935" y2="900" class="self-call"/>
  <line x1="935" y1="900" x2="935" y2="910" class="self-call"/>
  <line x1="935" y1="910" x2="895" y2="910" class="self-call"/>
  <text x="940" y="905" class="message-text">onBindViewHolder()</text>
  
  <!-- 条件分支：模块类型 -->
  <rect x="950" y="920" width="400" height="80" class="alt-box"/>
  <text x="960" y="935" class="condition-text">switch(moduleKey)</text>
  
  <!-- 遮盖疗法Fragment -->
  <line x1="895" y1="945" x2="1560" y2="945" class="message-arrow"/>
  <text x="1227" y="940" class="message-text">OCCLUSION_THERAPY → MaskTherapyFragment</text>
  
  <!-- 视觉训练Fragment -->
  <line x1="895" y1="965" x2="1680" y2="965" class="message-arrow"/>
  <text x="1287" y="960" class="message-text">VISION_THERAPY → VisualTrainFragment</text>
  
  <!-- Fragment替换 -->
  <text x="960" y="985" class="condition-text">FragmentManager.replace(flModuleRoot.id, fragment)</text>
  
  <!-- 通知数据变化 -->
  <line x1="320" y1="1020" x2="895" y2="1020" class="message-arrow"/>
  <text x="607" y="1015" class="message-text">notifyDataSetChanged()</text>
  
  <!-- 第五阶段：用户交互 -->
  <text x="50" y="1060" class="section-title">阶段5: 用户交互处理</text>
  
  <!-- 用户点击校准 -->
  <line x1="90" y1="1080" x2="320" y2="1080" class="message-arrow"/>
  <text x="205" y="1075" class="message-text">点击校准按钮</text>
  
  <!-- 检查绑定状态 -->
  <rect x="315" y="1090" width="10" height="120" class="activation"/>
  <line x1="320" y1="1100" x2="1015" y2="1100" class="message-arrow"/>
  <text x="667" y="1095" class="message-text">UserManager.isBind()</text>
  
  <line x1="1015" y1="1120" x2="320" y2="1120" class="return-arrow"/>
  <text x="667" y="1115" class="message-text">true</text>
  
  <!-- 启动校准Activity -->
  <line x1="320" y1="1140" x2="1240" y2="1140" class="message-arrow"/>
  <text x="780" y="1135" class="message-text">calibrationLauncher.launch()</text>
  
  <!-- 校准Activity处理 -->
  <rect x="1235" y="1150" width="10" height="40" class="activation"/>
  <line x1="1240" y1="1160" x2="1280" y2="1160" class="self-call"/>
  <line x1="1280" y1="1160" x2="1280" y2="1170" class="self-call"/>
  <line x1="1280" y1="1170" x2="1240" y2="1170" class="self-call"/>
  <text x="1285" y="1165" class="message-text">校准处理</text>
  
  <!-- 校准结果返回 -->
  <line x1="1240" y1="1190" x2="320" y2="1190" class="return-arrow"/>
  <text x="780" y="1185" class="message-text">校准结果(成功/失败)</text>
  
  <!-- 结果处理 -->
  <line x1="320" y1="1200" x2="360" y2="1200" class="self-call"/>
  <line x1="360" y1="1200" x2="360" y2="1210" class="self-call"/>
  <line x1="360" y1="1210" x2="320" y2="1210" class="self-call"/>
  <text x="365" y="1205" class="message-text">处理校准结果</text>
  
  <!-- 第六阶段：事件总线 -->
  <text x="50" y="1240" class="section-title">阶段6: 事件总线通信</text>
  
  <!-- LiveEventBus事件 -->
  <line x1="1450" y1="1260" x2="320" y2="1260" class="async-arrow"/>
  <text x="885" y="1255" class="message-text">EVENT_REFRESH_BIND_USER</text>
  
  <!-- 刷新配置 -->
  <line x1="320" y1="1280" x2="545" y2="1280" class="message-arrow"/>
  <text x="432" y="1275" class="message-text">getMedicalHomeProfile()</text>
  
  <!-- 第七阶段：生命周期结束 -->
  <text x="50" y="1320" class="section-title">阶段7: 生命周期结束</text>
  
  <!-- Fragment销毁 -->
  <line x1="320" y1="1340" x2="360" y2="1340" class="self-call"/>
  <line x1="360" y1="1340" x2="360" y2="1350" class="self-call"/>
  <line x1="360" y1="1350" x2="320" y2="1350" class="self-call"/>
  <text x="365" y="1345" class="message-text">onDestroyView()</text>
  
  <!-- 取消定时刷新 -->
  <line x1="320" y1="1360" x2="360" y2="1360" class="self-call"/>
  <line x1="360" y1="1360" x2="360" y2="1370" class="self-call"/>
  <line x1="360" y1="1370" x2="320" y2="1370" class="self-call"/>
  <text x="365" y="1365" class="message-text">cancelRefreshBindUser()</text>
  
  <!-- 释放资源 -->
  <line x1="320" y1="1380" x2="360" y2="1380" class="self-call"/>
  <line x1="360" y1="1380" x2="360" y2="1390" class="self-call"/>
  <line x1="360" y1="1390" x2="320" y2="1390" class="self-call"/>
  <text x="365" y="1385" class="message-text">BehaviorGuidanceManager.release()</text>
  
  <!-- 时序图说明 -->
  <rect x="50" y="1420" width="1650" height="320" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="70" y="1445" class="section-title">时序图关键特征说明</text>
  
  <text x="70" y="1470" class="message-text"><tspan style="font-weight: bold;">1. 异步响应式架构：</tspan>ViewModel通过LiveData异步通知Fragment，避免阻塞UI线程</text>
  <text x="70" y="1490" class="message-text"><tspan style="font-weight: bold;">2. 条件分支处理：</tspan>根据用户绑定状态、模块类型等条件执行不同的业务逻辑</text>
  <text x="70" y="1510" class="message-text"><tspan style="font-weight: bold;">3. 生命周期管理：</tspan>从onCreate到onDestroyView完整管理资源，包括定时任务和第三方库</text>
  <text x="70" y="1530" class="message-text"><tspan style="font-weight: bold;">4. 动态Fragment管理：</tspan>Adapter根据模块配置动态替换Fragment，实现模块化界面</text>
  <text x="70" y="1550" class="message-text"><tspan style="font-weight: bold;">5. 权限控制流程：</tspan>校准等功能需要检查用户绑定状态，体现了权限控制的时序</text>
  <text x="70" y="1570" class="message-text"><tspan style="font-weight: bold;">6. 事件总线通信：</tspan>使用LiveEventBus实现跨组件异步通信，解耦组件依赖</text>
  <text x="70" y="1590" class="message-text"><tspan style="font-weight: bold;">7. 多数据源整合：</tspan>整合UserManager、DeviceManager等多个数据源，统一更新UI状态</text>
  <text x="70" y="1610" class="message-text"><tspan style="font-weight: bold;">8. 错误处理机制：</tspan>校准失败、网络异常等情况都有相应的处理流程</text>
  <text x="70" y="1630" class="message-text"><tspan style="font-weight: bold;">9. 用户体验优化：</tspan>未绑定用户播放提示音，已绑定用户显示个性化头像和疗程信息</text>
  <text x="70" y="1650" class="message-text"><tspan style="font-weight: bold;">10. 模块化设计：</tspan>疗程模块可配置启用/禁用，支持动态加载不同的治疗Fragment</text>
  
  <!-- 图例 -->
  <rect x="1450" y="1680" width="250" height="60" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 1; rx: 4;"/>
  <text x="1460" y="1700" class="note-text">图例说明:</text>
  <line x1="1460" y1="1710" x2="1490" y2="1710" class="message-arrow"/>
  <text x="1495" y="1715" class="note-text">同步调用</text>
  <line x1="1460" y1="1720" x2="1490" y2="1720" class="return-arrow"/>
  <text x="1495" y="1725" class="note-text">异步返回</text>
  <line x1="1460" y1="1730" x2="1490" y2="1730" class="async-arrow"/>
  <text x="1495" y="1735" class="note-text">异步调用</text>
