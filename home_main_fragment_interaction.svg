<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .interaction-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 12px; fill: #2c3e50; }
      
      .fragment-bg { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .method-bg { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .viewmodel-bg { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .vm-method-bg { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .activity-bg { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .activity-method-bg { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .adapter-bg { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .adapter-method-bg { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .view-bg { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      .view-method-bg { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .manager-bg { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 12; }
      .manager-method-bg { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .event-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">HomeMainFragment 方法作用与交互关系图</text>
  
  <!-- HomeMainFragment 核心区域 -->
  <rect x="50" y="70" width="500" height="450" class="fragment-bg"/>
  <text x="300" y="95" text-anchor="middle" class="section-title">HomeMainFragment</text>
  
  <!-- 生命周期方法 -->
  <rect x="70" y="110" width="220" height="120" class="method-bg"/>
  <text x="180" y="130" text-anchor="middle" class="method-title">生命周期方法</text>
  <text x="80" y="150" class="text">onCreate() - 启动刷新绑定用户</text>
  <text x="80" y="165" class="text">onDestroyView() - 取消刷新</text>
  <text x="80" y="180" class="text">initView() - 初始化RecyclerView</text>
  <text x="80" y="195" class="text">initObserver() - 观察ViewModel</text>
  <text x="80" y="210" class="text">initData() - 获取设备信息和授权</text>
  
  <!-- 事件处理方法 -->
  <rect x="300" y="110" width="220" height="120" class="method-bg"/>
  <text x="410" y="130" text-anchor="middle" class="method-title">事件处理方法</text>
  <text x="310" y="150" class="text">initListener() - 设置点击监听</text>
  <text x="310" y="165" class="text">ivMore.click - 显示菜单弹窗</text>
  <text x="310" y="180" class="text">ivCalibration.click - 启动校准</text>
  <text x="310" y="195" class="text">clUserInfo.click - 用户绑定</text>
  <text x="310" y="210" class="text">ivAi.click - 启动AI助手</text>
  
  <!-- UI更新方法 -->
  <rect x="70" y="240" width="220" height="120" class="method-bg"/>
  <text x="180" y="260" text-anchor="middle" class="method-title">UI更新方法</text>
  <text x="80" y="280" class="text">updateUserAvatar() - 更新头像</text>
  <text x="80" y="295" class="text">updateUserName() - 更新用户名</text>
  <text x="80" y="310" class="text">updateDeviceInfo() - 更新设备信息</text>
  <text x="80" y="325" class="text">updateTreatmentModule() - 更新疗程</text>
  <text x="80" y="340" class="text">handlerTreatmentDialog() - 处理弹窗</text>
  
  <!-- 业务逻辑方法 -->
  <rect x="300" y="240" width="220" height="120" class="method-bg"/>
  <text x="410" y="260" text-anchor="middle" class="method-title">业务逻辑方法</text>
  <text x="310" y="280" class="text">startCalibration() - 启动校准Activity</text>
  <text x="310" y="295" class="text">calibrationLauncher - 校准结果处理</text>
  <text x="310" y="310" class="text">startRefreshBindUser() - 定时刷新</text>
  <text x="310" y="325" class="text">cancelRefreshBindUser() - 取消定时</text>
  <text x="310" y="340" class="text">BehaviorGuidanceManager - 行为指导</text>
  
  <!-- 数据绑定区域 -->
  <rect x="70" y="370" width="450" height="130" class="method-bg"/>
  <text x="295" y="390" text-anchor="middle" class="method-title">数据绑定与观察者</text>
  <text x="80" y="410" class="text">userVM.accountInfoLiveData.observe() - 监听用户信息变化，触发UI更新和疗程获取</text>
  <text x="80" y="425" class="text">homeVM.medicalHomeProfileLiveData.observe() - 监听家庭版配置，更新治疗模块</text>
  <text x="80" y="440" class="text">treatmentVM.currentTreatmentLiveData.observe() - 监听当前疗程，处理疗程弹窗</text>
  <text x="80" y="455" class="text">maskVM.occlusionTherapyLiveData.observe() - 监听遮盖疗法状态</text>
  <text x="80" y="470" class="text">updateVM.appUpdateInfoLiveData.observe() - 监听版本更新信息</text>
  <text x="80" y="485" class="text">LiveEventBus.EVENT_REFRESH_BIND_USER - 监听绑定用户刷新事件</text>

  <!-- ViewModel层 -->
  <rect x="600" y="70" width="450" height="300" class="viewmodel-bg"/>
  <text x="825" y="95" text-anchor="middle" class="section-title">ViewModel层</text>
  
  <!-- HomeViewModel -->
  <rect x="620" y="110" width="200" height="80" class="vm-method-bg"/>
  <text x="720" y="130" text-anchor="middle" class="method-title">HomeViewModel</text>
  <text x="630" y="150" class="text">getMedicalHomeProfile()</text>
  <text x="630" y="165" class="text">medicalHomeProfileLiveData</text>
  <text x="630" y="180" class="text">maskTherapyStateLivedata</text>
  
  <!-- TreatmentViewModel -->
  <rect x="830" y="110" width="200" height="80" class="vm-method-bg"/>
  <text x="930" y="130" text-anchor="middle" class="method-title">TreatmentViewModel</text>
  <text x="840" y="150" class="text">getCurrentTreatment()</text>
  <text x="840" y="165" class="text">activationTreatment()</text>
  <text x="840" y="180" class="text">currentTreatmentLiveData</text>
  
  <!-- UserViewModel -->
  <rect x="620" y="200" width="200" height="80" class="vm-method-bg"/>
  <text x="720" y="220" text-anchor="middle" class="method-title">UserViewModel</text>
  <text x="630" y="240" class="text">getAccountInfo()</text>
  <text x="630" y="255" class="text">accountInfoLiveData</text>
  <text x="630" y="270" class="text">用户绑定状态管理</text>
  
  <!-- MaskViewModel -->
  <rect x="830" y="200" width="200" height="80" class="vm-method-bg"/>
  <text x="930" y="220" text-anchor="middle" class="method-title">MaskViewModel</text>
  <text x="840" y="240" class="text">getTodayOcclusionTherapy()</text>
  <text x="840" y="255" class="text">setMTState()</text>
  <text x="840" y="270" class="text">eyePosition属性</text>
  
  <!-- UpdateViewModel -->
  <rect x="620" y="290" width="200" height="60" class="vm-method-bg"/>
  <text x="720" y="310" text-anchor="middle" class="method-title">UpdateViewModel</text>
  <text x="630" y="330" class="text">getAppUpdateInfo()</text>
  <text x="630" y="345" class="text">appUpdateInfoLiveData</text>
  
  <!-- AdaViewModel -->
  <rect x="830" y="290" width="200" height="60" class="vm-method-bg"/>
  <text x="930" y="310" text-anchor="middle" class="method-title">AdaViewModel</text>
  <text x="840" y="330" class="text">getAuthCode()</text>
  <text x="840" y="345" class="text">authCodeLiveData</text>

  <!-- Activity层 -->
  <rect x="1100" y="70" width="350" height="200" class="activity-bg"/>
  <text x="1275" y="95" text-anchor="middle" class="section-title">Activity层</text>
  
  <!-- HomeMainActivity -->
  <rect x="1120" y="110" width="150" height="80" class="activity-method-bg"/>
  <text x="1195" y="130" text-anchor="middle" class="method-title">HomeMainActivity</text>
  <text x="1130" y="150" class="text">toBind() - 跳转绑定</text>
  <text x="1130" y="165" class="text">mainDialogTaskManager</text>
  <text x="1130" y="180" class="text">Fragment容器管理</text>
  
  <!-- CalibrationActivity -->
  <rect x="1280" y="110" width="150" height="80" class="activity-method-bg"/>
  <text x="1355" y="130" text-anchor="middle" class="method-title">CalibrationActivity</text>
  <text x="1290" y="150" class="text">createIntent()</text>
  <text x="1290" y="165" class="text">校准模式设置</text>
  <text x="1290" y="180" class="text">返回校准结果</text>
  
  <!-- 其他Activity -->
  <rect x="1120" y="200" width="310" height="50" class="activity-method-bg"/>
  <text x="1275" y="220" text-anchor="middle" class="method-title">其他Activity</text>
  <text x="1130" y="240" class="text">HelpCenterActivity, ParamSettingActivity, ChatWebActivity</text>

  <!-- Adapter层 -->
  <rect x="1500" y="70" width="250" height="200" class="adapter-bg"/>
  <text x="1625" y="95" text-anchor="middle" class="section-title">Adapter层</text>

  <!-- TreatmentModuleAdapter -->
  <rect x="1520" y="110" width="210" height="140" class="adapter-method-bg"/>
  <text x="1625" y="130" text-anchor="middle" class="method-title">TreatmentModuleAdapter</text>
  <text x="1530" y="150" class="text">setTreatmentModuleData()</text>
  <text x="1530" y="165" class="text">onBindViewHolder()</text>
  <text x="1530" y="180" class="text">Fragment动态替换:</text>
  <text x="1530" y="195" class="text">• OCCLUSION_THERAPY →</text>
  <text x="1530" y="210" class="text">  MaskTherapyFragment</text>
  <text x="1530" y="225" class="text">• VISION_THERAPY →</text>
  <text x="1530" y="240" class="text">  VisualTrainFragment</text>

  <!-- View组件层 -->
  <rect x="50" y="540" width="700" height="200" class="view-bg"/>
  <text x="400" y="565" text-anchor="middle" class="section-title">View组件层</text>

  <!-- UI组件 -->
  <rect x="70" y="580" width="200" height="140" class="view-method-bg"/>
  <text x="170" y="600" text-anchor="middle" class="method-title">UI组件</text>
  <text x="80" y="620" class="text">ivHomeLogo - 设备Logo</text>
  <text x="80" y="635" class="text">ivMore - 更多菜单</text>
  <text x="80" y="650" class="text">ivHelpCenter - 帮助中心</text>
  <text x="80" y="665" class="text">ivCalibration - 校准按钮</text>
  <text x="80" y="680" class="text">ivParamSetting - 参数设置</text>
  <text x="80" y="695" class="text">ivAi - AI助手</text>
  <text x="80" y="710" class="text">clUserInfo - 用户信息区域</text>

  <!-- 用户信息组件 -->
  <rect x="280" y="580" width="200" height="140" class="view-method-bg"/>
  <text x="380" y="600" text-anchor="middle" class="method-title">用户信息组件</text>
  <text x="290" y="620" class="text">viewUserBg - 用户背景</text>
  <text x="290" y="635" class="text">ivUserAvatar - 用户头像</text>
  <text x="290" y="650" class="text">tvUserName - 用户名</text>
  <text x="290" y="665" class="text">tvUserUnbind - 未绑定提示</text>
  <text x="290" y="680" class="text">头像状态:</text>
  <text x="290" y="695" class="text">• 性别区分(男/女)</text>
  <text x="290" y="710" class="text">• 遮盖疗法状态(眼镜)</text>

  <!-- RecyclerView组件 -->
  <rect x="490" y="580" width="240" height="140" class="view-method-bg"/>
  <text x="610" y="600" text-anchor="middle" class="method-title">RecyclerView组件</text>
  <text x="500" y="620" class="text">rvTreatmentModule - 疗程模块</text>
  <text x="500" y="635" class="text">LinearLayoutManager(HORIZONTAL)</text>
  <text x="500" y="650" class="text">TreatmentModuleItemDecoration</text>
  <text x="500" y="665" class="text">clNetworkException - 网络异常</text>
  <text x="500" y="680" class="text">动态显示/隐藏:</text>
  <text x="500" y="695" class="text">• 有模块数据 → 显示RecyclerView</text>
  <text x="500" y="710" class="text">• 无模块数据 → 显示异常页面</text>

  <!-- Manager层 -->
  <rect x="800" y="540" width="650" height="200" class="manager-bg"/>
  <text x="1125" y="565" text-anchor="middle" class="section-title">Manager层</text>

  <!-- UserManager -->
  <rect x="820" y="580" width="150" height="80" class="manager-method-bg"/>
  <text x="895" y="600" text-anchor="middle" class="method-title">UserManager</text>
  <text x="830" y="620" class="text">isBind() - 绑定检查</text>
  <text x="830" y="635" class="text">getAccountInfo()</text>
  <text x="830" y="650" class="text">getTreatmentInfo()</text>

  <!-- DeviceManager -->
  <rect x="980" y="580" width="150" height="80" class="manager-method-bg"/>
  <text x="1055" y="600" text-anchor="middle" class="method-title">DeviceManager</text>
  <text x="990" y="620" class="text">isDemoMode()</text>
  <text x="990" y="635" class="text">getDeviceInfo()</text>
  <text x="990" y="650" class="text">getMaskTherapyState()</text>

  <!-- TreatmentManager -->
  <rect x="1140" y="580" width="150" height="80" class="manager-method-bg"/>
  <text x="1215" y="600" text-anchor="middle" class="method-title">TreatmentManager</text>
  <text x="1150" y="620" class="text">showReviewRemindDialog()</text>
  <text x="1150" y="635" class="text">showTreatmentDialog()</text>
  <text x="1150" y="650" class="text">疗程状态处理</text>

  <!-- 其他Manager -->
  <rect x="820" y="670" width="470" height="50" class="manager-method-bg"/>
  <text x="1055" y="690" text-anchor="middle" class="method-title">其他Manager</text>
  <text x="830" y="710" class="text">BehaviorGuidanceManager, PlayManager, ImageLoader, MQTTInitManager</text>

  <!-- 交互流程图 -->
  <rect x="50" y="760" width="1700" height="400" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="785" text-anchor="middle" class="title" style="font-size: 22px;">核心交互流程</text>

  <!-- 用户绑定流程 -->
  <rect x="70" y="810" width="300" height="120" style="fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 8;"/>
  <text x="220" y="830" text-anchor="middle" class="method-title">1. 用户绑定流程</text>
  <text x="80" y="850" class="interaction-text">① HomeMainActivity.initData()</text>
  <text x="80" y="865" class="interaction-text">② userVM.getAccountInfo()</text>
  <text x="80" y="880" class="interaction-text">③ accountInfoLiveData.observe()</text>
  <text x="80" y="895" class="interaction-text">④ updateUserAvatar() + updateUserName()</text>
  <text x="80" y="910" class="interaction-text">⑤ 未绑定 → toBind() / 已绑定 → 获取疗程</text>

  <!-- 疗程模块加载流程 -->
  <rect x="390" y="810" width="300" height="120" style="fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 8;"/>
  <text x="540" y="830" text-anchor="middle" class="method-title">2. 疗程模块加载流程</text>
  <text x="400" y="850" class="interaction-text">① homeVM.getMedicalHomeProfile()</text>
  <text x="400" y="865" class="interaction-text">② medicalHomeProfileLiveData.observe()</text>
  <text x="400" y="880" class="interaction-text">③ updateTreatmentModule(modules)</text>
  <text x="400" y="895" class="interaction-text">④ 过滤启用模块 → Adapter设置数据</text>
  <text x="400" y="910" class="interaction-text">⑤ Fragment动态替换到RecyclerView</text>

  <!-- 校准流程 -->
  <rect x="710" y="810" width="300" height="120" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="860" y="830" text-anchor="middle" class="method-title">3. 校准流程</text>
  <text x="720" y="850" class="interaction-text">① ivCalibration.click</text>
  <text x="720" y="865" class="interaction-text">② 检查绑定状态 UserManager.isBind()</text>
  <text x="720" y="880" class="interaction-text">③ 已绑定 → startCalibration()</text>
  <text x="720" y="895" class="interaction-text">④ calibrationLauncher.launch()</text>
  <text x="720" y="910" class="interaction-text">⑤ 校准结果处理 → Toast/Dialog</text>

  <!-- 事件总线流程 -->
  <rect x="1030" y="810" width="300" height="120" style="fill: #fce4ec; stroke: #e91e63; stroke-width: 2; rx: 8;"/>
  <text x="1180" y="830" text-anchor="middle" class="method-title">4. 事件总线流程</text>
  <text x="1040" y="850" class="interaction-text">① LiveEventBus事件监听</text>
  <text x="1040" y="865" class="interaction-text">② EVENT_REFRESH_BIND_USER</text>
  <text x="1040" y="880" class="interaction-text">③ 刷新家庭版配置</text>
  <text x="1040" y="895" class="interaction-text">④ RefreshBindUserReceiver广播</text>
  <text x="1040" y="910" class="interaction-text">⑤ 定时刷新机制</text>

  <!-- Fragment生命周期流程 -->
  <rect x="1350" y="810" width="300" height="120" style="fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 8;"/>
  <text x="1500" y="830" text-anchor="middle" class="method-title">5. Fragment生命周期</text>
  <text x="1360" y="850" class="interaction-text">① onCreate() → 启动定时刷新</text>
  <text x="1360" y="865" class="interaction-text">② initView() → 设置RecyclerView</text>
  <text x="1360" y="880" class="interaction-text">③ initObserver() → 绑定ViewModel</text>
  <text x="1360" y="895" class="interaction-text">④ initData() → 获取设备信息</text>
  <text x="1360" y="910" class="interaction-text">⑤ onDestroyView() → 清理资源</text>

  <!-- 连接箭头 -->
  <!-- Fragment到ViewModel -->
  <line x1="550" y1="300" x2="600" y2="200" class="arrow"/>
  <line x1="550" y1="320" x2="600" y2="240" class="arrow"/>

  <!-- Fragment到Activity -->
  <line x1="550" y1="200" x2="1100" y2="150" class="arrow"/>

  <!-- Fragment到Adapter -->
  <line x1="550" y1="350" x2="1500" y2="180" class="arrow"/>

  <!-- Fragment到View -->
  <line x1="300" y1="520" x2="400" y2="540" class="arrow"/>

  <!-- Fragment到Manager -->
  <line x1="500" y1="450" x2="800" y2="620" class="arrow"/>

  <!-- 数据流箭头 -->
  <line x1="720" y1="190" x2="410" y2="280" class="data-arrow"/>
  <line x1="930" y1="190" x2="410" y2="320" class="data-arrow"/>

  <!-- 事件流箭头 -->
  <line x1="1195" y1="190" x2="410" y2="200" class="event-arrow"/>
  <line x1="1625" y1="270" x2="610" y2="650" class="event-arrow"/>

  <!-- 详细方法说明 -->
  <rect x="70" y="950" width="1600" height="600" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="870" y="975" text-anchor="middle" class="title" style="font-size: 20px;">HomeMainFragment 核心方法详细说明</text>

  <!-- 第一列方法说明 -->
  <text x="90" y="1010" class="method-title" style="font-size: 16px;">🔄 生命周期方法</text>
  <text x="90" y="1030" class="interaction-text"><tspan style="font-weight: bold;">onCreate():</tspan></text>
  <text x="110" y="1045" class="interaction-text">• 调用startRefreshBindUser()启动定时刷新绑定用户状态</text>
  <text x="110" y="1060" class="interaction-text">• 异步初始化BehaviorGuidanceManager行为指导管理器</text>

  <text x="90" y="1080" class="interaction-text"><tspan style="font-weight: bold;">initView():</tspan></text>
  <text x="110" y="1095" class="interaction-text">• 设置RecyclerView为水平布局LinearLayoutManager</text>
  <text x="110" y="1110" class="interaction-text">• 添加TreatmentModuleItemDecoration装饰器</text>
  <text x="110" y="1125" class="interaction-text">• 绑定TreatmentModuleAdapter适配器</text>

  <text x="90" y="1145" class="interaction-text"><tspan style="font-weight: bold;">initObserver():</tspan></text>
  <text x="110" y="1160" class="interaction-text">• 观察userVM.accountInfoLiveData触发用户信息更新</text>
  <text x="110" y="1175" class="interaction-text">• 观察homeVM.medicalHomeProfileLiveData更新疗程模块</text>
  <text x="110" y="1190" class="interaction-text">• 观察treatmentVM各种疗程状态变化</text>
  <text x="110" y="1205" class="interaction-text">• 监听LiveEventBus的EVENT_REFRESH_BIND_USER事件</text>

  <!-- 第二列方法说明 -->
  <text x="450" y="1010" class="method-title" style="font-size: 16px;">🎯 事件处理方法</text>
  <text x="450" y="1030" class="interaction-text"><tspan style="font-weight: bold;">initListener():</tspan></text>
  <text x="470" y="1045" class="interaction-text">• ivMore.click → 显示MenuPopupWindow菜单弹窗</text>
  <text x="470" y="1060" class="interaction-text">• ivCalibration.click → 检查绑定状态后启动校准</text>
  <text x="470" y="1075" class="interaction-text">• clUserInfo.click → 未绑定时跳转到绑定页面</text>
  <text x="470" y="1090" class="interaction-text">• ivAi.click → 启动ChatWebActivity AI助手</text>

  <text x="450" y="1110" class="interaction-text"><tspan style="font-weight: bold;">startCalibration():</tspan></text>
  <text x="470" y="1125" class="interaction-text">• 使用calibrationLauncher启动CalibrationActivity</text>
  <text x="470" y="1140" class="interaction-text">• 传递CalibrationMode.CALIBRATION校准模式</text>
  <text x="470" y="1155" class="interaction-text">• 处理校准结果：成功显示Toast，失败显示重新校准Dialog</text>

  <text x="450" y="1175" class="interaction-text"><tspan style="font-weight: bold;">calibrationLauncher:</tspan></text>
  <text x="470" y="1190" class="interaction-text">• ActivityResultContracts.StartActivityForResult()</text>
  <text x="470" y="1205" class="interaction-text">• 获取校准结果并显示相应的用户反馈</text>

  <!-- 第三列方法说明 -->
  <text x="810" y="1010" class="method-title" style="font-size: 16px;">🎨 UI更新方法</text>
  <text x="810" y="1030" class="interaction-text"><tspan style="font-weight: bold;">updateUserAvatar():</tspan></text>
  <text x="830" y="1045" class="interaction-text">• 根据用户绑定状态、性别、遮盖疗法状态设置头像</text>
  <text x="830" y="1060" class="interaction-text">• 男性/女性不同头像，遮盖疗法时显示眼镜效果</text>
  <text x="830" y="1075" class="interaction-text">• 弱视眼位置(左/右)决定眼镜颜色分布</text>

  <text x="810" y="1095" class="interaction-text"><tspan style="font-weight: bold;">updateTreatmentModule():</tspan></text>
  <text x="830" y="1110" class="interaction-text">• 过滤启用的疗程模块(遮盖疗法、视觉训练)</text>
  <text x="830" y="1125" class="interaction-text">• 调用mModuleAdapter.setTreatmentModuleData()</text>
  <text x="830" y="1140" class="interaction-text">• 传递childFragmentManager用于Fragment替换</text>
  <text x="830" y="1155" class="interaction-text">• 无数据时显示网络异常页面</text>

  <text x="810" y="1175" class="interaction-text"><tspan style="font-weight: bold;">handlerTreatmentDialog():</tspan></text>
  <text x="830" y="1190" class="interaction-text">• 根据疗程状态显示不同Dialog</text>
  <text x="830" y="1205" class="interaction-text">• PENDING状态显示激活Dialog，COMPLETED显示过期Dialog</text>

  <!-- 第四列方法说明 -->
  <text x="1170" y="1010" class="method-title" style="font-size: 16px;">🔗 数据交互方法</text>
  <text x="1170" y="1030" class="interaction-text"><tspan style="font-weight: bold;">TreatmentModuleAdapter交互:</tspan></text>
  <text x="1190" y="1045" class="interaction-text">• setTreatmentModuleData()传递模块数据和FragmentManager</text>
  <text x="1190" y="1060" class="interaction-text">• onBindViewHolder()中根据moduleKey动态替换Fragment</text>
  <text x="1190" y="1075" class="interaction-text">• OCCLUSION_THERAPY → MaskTherapyFragment</text>
  <text x="1190" y="1090" class="interaction-text">• VISION_THERAPY → VisualTrainFragment</text>

  <text x="1170" y="1110" class="interaction-text"><tspan style="font-weight: bold;">ViewModel数据绑定:</tspan></text>
  <text x="1190" y="1125" class="interaction-text">• 使用activityViewModels()共享ViewModel实例</text>
  <text x="1190" y="1140" class="interaction-text">• LiveData观察者模式实现数据驱动UI更新</text>
  <text x="1190" y="1155" class="interaction-text">• 自动处理生命周期，避免内存泄漏</text>

  <text x="1170" y="1175" class="interaction-text"><tspan style="font-weight: bold;">Manager层调用:</tspan></text>
  <text x="1190" y="1190" class="interaction-text">• UserManager.isBind()检查用户绑定状态</text>
  <text x="1190" y="1205" class="interaction-text">• DeviceManager获取设备信息和遮盖疗法状态</text>

  <!-- 底部特性说明 -->
  <rect x="90" y="1230" width="1560" height="300" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="870" y="1255" text-anchor="middle" class="method-title" style="font-size: 18px;">🌟 HomeMainFragment 核心特性</text>

  <text x="110" y="1280" class="interaction-text"><tspan style="font-weight: bold;">• MVVM架构模式：</tspan>Fragment作为View层，通过ViewModel进行数据绑定，实现UI与业务逻辑分离</text>
  <text x="110" y="1300" class="interaction-text"><tspan style="font-weight: bold;">• 响应式编程：</tspan>使用LiveData观察者模式，数据变化自动驱动UI更新，减少手动UI刷新代码</text>
  <text x="110" y="1320" class="interaction-text"><tspan style="font-weight: bold;">• 动态Fragment管理：</tspan>通过TreatmentModuleAdapter动态替换Fragment，实现模块化的疗程界面</text>
  <text x="110" y="1340" class="interaction-text"><tspan style="font-weight: bold;">• 事件总线通信：</tspan>使用LiveEventBus实现跨组件通信，如绑定用户状态刷新事件</text>
  <text x="110" y="1360" class="interaction-text"><tspan style="font-weight: bold;">• 生命周期管理：</tspan>合理处理Fragment生命周期，启动和停止定时任务，初始化和释放资源</text>
  <text x="110" y="1380" class="interaction-text"><tspan style="font-weight: bold;">• 用户状态感知：</tspan>根据用户绑定状态、性别、疗法状态动态调整UI显示，提供个性化体验</text>
  <text x="110" y="1400" class="interaction-text"><tspan style="font-weight: bold;">• 异常处理机制：</tspan>网络异常时显示友好的异常页面，校准失败时提供重试选项</text>
  <text x="110" y="1420" class="interaction-text"><tspan style="font-weight: bold;">• 模块化设计：</tspan>疗程模块可配置启用/禁用，支持水平滚动显示，适应不同屏幕尺寸</text>
  <text x="110" y="1440" class="interaction-text"><tspan style="font-weight: bold;">• 多媒体集成：</tspan>集成音频播放、图像加载、AI助手等功能，提供丰富的用户交互体验</text>
  <text x="110" y="1460" class="interaction-text"><tspan style="font-weight: bold;">• 国际化支持：</tspan>支持多语言环境，根据系统语言自动调整UI显示</text>
  <text x="110" y="1480" class="interaction-text"><tspan style="font-weight: bold;">• 设备适配：</tspan>根据设备类型(Demo模式/正式模式)显示不同功能，支持设备Logo自定义</text>
  <text x="110" y="1500" class="interaction-text"><tspan style="font-weight: bold;">• 权限管理：</tspan>根据用户绑定状态控制功能访问权限，未绑定用户引导到绑定页面</text>

</svg>
