<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .message-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .step-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      .note-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      
      .actor-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 5,5; }
      .activation { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
      .message-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
      .self-call { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .note-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 1; rx: 4; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">HomeMainFragment 时序图</text>
  
  <!-- 参与者 -->
  <!-- HomeMainActivity -->
  <rect x="50" y="60" width="120" height="40" class="actor-box"/>
  <text x="110" y="85" class="actor-title">HomeMainActivity</text>
  
  <!-- HomeMainFragment -->
  <rect x="200" y="60" width="120" height="40" class="actor-box"/>
  <text x="260" y="85" class="actor-title">HomeMainFragment</text>
  
  <!-- UserViewModel -->
  <rect x="350" y="60" width="120" height="40" class="actor-box"/>
  <text x="410" y="85" class="actor-title">UserViewModel</text>
  
  <!-- HomeViewModel -->
  <rect x="500" y="60" width="120" height="40" class="actor-box"/>
  <text x="560" y="85" class="actor-title">HomeViewModel</text>
  
  <!-- TreatmentViewModel -->
  <rect x="650" y="60" width="120" height="40" class="actor-box"/>
  <text x="710" y="85" class="actor-title">TreatmentViewModel</text>
  
  <!-- TreatmentModuleAdapter -->
  <rect x="800" y="60" width="140" height="40" class="actor-box"/>
  <text x="870" y="85" class="actor-title">TreatmentModuleAdapter</text>
  
  <!-- UserManager -->
  <rect x="970" y="60" width="120" height="40" class="actor-box"/>
  <text x="1030" y="85" class="actor-title">UserManager</text>
  
  <!-- DeviceManager -->
  <rect x="1120" y="60" width="120" height="40" class="actor-box"/>
  <text x="1180" y="85" class="actor-title">DeviceManager</text>
  
  <!-- CalibrationActivity -->
  <rect x="1270" y="60" width="120" height="40" class="actor-box"/>
  <text x="1330" y="85" class="actor-title">CalibrationActivity</text>
  
  <!-- Repository -->
  <rect x="1420" y="60" width="120" height="40" class="actor-box"/>
  <text x="1480" y="85" class="actor-title">Repository</text>
  
  <!-- 生命线 -->
  <line x1="110" y1="100" x2="110" y2="1350" class="lifeline"/>
  <line x1="260" y1="100" x2="260" y2="1350" class="lifeline"/>
  <line x1="410" y1="100" x2="410" y2="1350" class="lifeline"/>
  <line x1="560" y1="100" x2="560" y2="1350" class="lifeline"/>
  <line x1="710" y1="100" x2="710" y2="1350" class="lifeline"/>
  <line x1="870" y1="100" x2="870" y2="1350" class="lifeline"/>
  <line x1="1030" y1="100" x2="1030" y2="1350" class="lifeline"/>
  <line x1="1180" y1="100" x2="1180" y2="1350" class="lifeline"/>
  <line x1="1330" y1="100" x2="1330" y2="1350" class="lifeline"/>
  <line x1="1480" y1="100" x2="1480" y2="1350" class="lifeline"/>

  <!-- 时序步骤 -->
  
  <!-- 步骤1: Activity启动 -->
  <text x="50" y="140" class="step-text">1. Activity启动流程</text>
  
  <!-- Activity创建Fragment -->
  <line x1="110" y1="160" x2="260" y2="160" class="message-arrow"/>
  <text x="185" y="155" class="message-text">newInstance()</text>
  
  <!-- Fragment onCreate -->
  <rect x="255" y="170" width="10" height="30" class="activation"/>
  <line x1="260" y1="180" x2="300" y2="180" class="self-call"/>
  <line x1="300" y1="180" x2="300" y2="190" class="self-call"/>
  <line x1="300" y1="190" x2="260" y2="190" class="self-call"/>
  <text x="305" y="185" class="message-text">onCreate()</text>
  
  <!-- 启动定时刷新 -->
  <line x1="260" y1="210" x2="300" y2="210" class="self-call"/>
  <line x1="300" y1="210" x2="300" y2="220" class="self-call"/>
  <line x1="300" y1="220" x2="260" y2="220" class="self-call"/>
  <text x="305" y="215" class="message-text">startRefreshBindUser()</text>
  
  <!-- Activity initData -->
  <rect x="105" y="240" width="10" height="20" class="activation"/>
  <line x1="110" y1="250" x2="410" y2="250" class="message-arrow"/>
  <text x="260" y="245" class="message-text">initData() → getAccountInfo()</text>
  
  <!-- 步骤2: 用户信息获取 -->
  <text x="50" y="290" class="step-text">2. 用户信息获取流程</text>
  
  <!-- UserViewModel获取账户信息 -->
  <rect x="405" y="310" width="10" height="40" class="activation"/>
  <line x1="410" y1="320" x2="1480" y2="320" class="message-arrow"/>
  <text x="945" y="315" class="message-text">getAccountInfo() → Repository</text>
  
  <!-- Repository返回数据 -->
  <line x1="1480" y1="340" x2="410" y2="340" class="return-arrow"/>
  <text x="945" y="335" class="message-text">AccountInfo</text>
  
  <!-- LiveData通知Fragment -->
  <line x1="410" y1="360" x2="260" y2="360" class="message-arrow"/>
  <text x="335" y="355" class="message-text">accountInfoLiveData.observe()</text>
  
  <!-- 步骤3: Fragment响应用户信息 -->
  <text x="50" y="400" class="step-text">3. Fragment响应用户信息变化</text>
  
  <rect x="255" y="420" width="10" height="120" class="activation"/>
  
  <!-- 获取医疗家庭版配置 -->
  <line x1="260" y1="430" x2="560" y2="430" class="message-arrow"/>
  <text x="410" y="425" class="message-text">getMedicalHomeProfile()</text>
  
  <!-- 获取当前疗程 -->
  <line x1="260" y1="450" x2="710" y2="450" class="message-arrow"/>
  <text x="485" y="445" class="message-text">getCurrentTreatment()</text>
  
  <!-- 检查绑定状态 -->
  <line x1="260" y1="470" x2="1030" y2="470" class="message-arrow"/>
  <text x="645" y="465" class="message-text">UserManager.isBind()</text>
  
  <!-- 更新用户头像 -->
  <line x1="260" y1="490" x2="300" y2="490" class="self-call"/>
  <line x1="300" y1="490" x2="300" y2="500" class="self-call"/>
  <line x1="300" y1="500" x2="260" y2="500" class="self-call"/>
  <text x="305" y="495" class="message-text">updateUserAvatar()</text>
  
  <!-- 更新用户名 -->
  <line x1="260" y1="510" x2="300" y2="510" class="self-call"/>
  <line x1="300" y1="510" x2="300" y2="520" class="self-call"/>
  <line x1="300" y1="520" x2="260" y2="520" class="self-call"/>
  <text x="305" y="515" class="message-text">updateUserName()</text>
  
  <!-- 步骤4: 疗程模块更新 -->
  <text x="50" y="570" class="step-text">4. 疗程模块更新流程</text>
  
  <!-- HomeViewModel返回配置 -->
  <rect x="555" y="590" width="10" height="40" class="activation"/>
  <line x1="560" y1="600" x2="1480" y2="600" class="message-arrow"/>
  <text x="1020" y="595" class="message-text">API调用获取配置</text>
  
  <line x1="1480" y1="620" x2="560" y2="620" class="return-arrow"/>
  <text x="1020" y="615" class="message-text">MedicalHomeProfile</text>
  
  <!-- LiveData通知Fragment -->
  <line x1="560" y1="640" x2="260" y2="640" class="message-arrow"/>
  <text x="410" y="635" class="message-text">medicalHomeProfileLiveData.observe()</text>
  
  <!-- Fragment更新疗程模块 -->
  <rect x="255" y="660" width="10" height="80" class="activation"/>
  <line x1="260" y1="670" x2="300" y2="670" class="self-call"/>
  <line x1="300" y1="670" x2="300" y2="680" class="self-call"/>
  <line x1="300" y1="680" x2="260" y2="680" class="self-call"/>
  <text x="305" y="675" class="message-text">updateTreatmentModule()</text>
  
  <!-- 设置Adapter数据 -->
  <line x1="260" y1="700" x2="870" y2="700" class="message-arrow"/>
  <text x="565" y="695" class="message-text">setTreatmentModuleData()</text>
  
  <!-- Adapter通知数据变化 -->
  <line x1="260" y1="720" x2="870" y2="720" class="message-arrow"/>
  <text x="565" y="715" class="message-text">notifyDataSetChanged()</text>
  
  <!-- 步骤5: 校准流程 -->
  <text x="50" y="770" class="step-text">5. 校准流程</text>
  
  <!-- 用户点击校准按钮 -->
  <rect x="255" y="790" width="10" height="100" class="activation"/>
  <line x1="50" y1="800" x2="260" y2="800" class="message-arrow"/>
  <text x="155" y="795" class="message-text">用户点击校准</text>
  
  <!-- 检查绑定状态 -->
  <line x1="260" y1="820" x2="1030" y2="820" class="message-arrow"/>
  <text x="645" y="815" class="message-text">isBind()</text>
  
  <line x1="1030" y1="840" x2="260" y2="840" class="return-arrow"/>
  <text x="645" y="835" class="message-text">true/false</text>
  
  <!-- 启动校准Activity -->
  <line x1="260" y1="860" x2="1330" y2="860" class="message-arrow"/>
  <text x="795" y="855" class="message-text">startCalibration() → CalibrationActivity</text>
  
  <!-- 校准结果返回 -->
  <line x1="1330" y1="880" x2="260" y2="880" class="return-arrow"/>
  <text x="795" y="875" class="message-text">校准结果</text>
  
  <!-- 步骤6: Adapter Fragment管理 -->
  <text x="50" y="920" class="step-text">6. Adapter Fragment管理流程</text>
  
  <!-- Adapter绑定数据 -->
  <rect x="865" y="940" width="10" height="80" class="activation"/>
  <line x1="870" y1="950" x2="910" y2="950" class="self-call"/>
  <line x1="910" y1="950" x2="910" y2="960" class="self-call"/>
  <line x1="910" y1="960" x2="870" y2="960" class="self-call"/>
  <text x="915" y="955" class="message-text">onBindViewHolder()</text>
  
  <!-- 根据模块类型替换Fragment -->
  <line x1="870" y1="980" x2="910" y2="980" class="self-call"/>
  <line x1="910" y1="980" x2="910" y2="1000" class="self-call"/>
  <line x1="910" y1="1000" x2="870" y2="1000" class="self-call"/>
  <text x="915" y="985" class="message-text">Fragment替换</text>
  <text x="915" y="995" class="note-text">MaskTherapyFragment/</text>
  <text x="915" y="1005" class="note-text">VisualTrainFragment</text>
  
  <!-- 步骤7: 事件总线通信 -->
  <text x="50" y="1050" class="step-text">7. 事件总线通信流程</text>
  
  <!-- LiveEventBus事件 -->
  <rect x="255" y="1070" width="10" height="60" class="activation"/>
  <line x1="50" y1="1080" x2="260" y2="1080" class="message-arrow"/>
  <text x="155" y="1075" class="message-text">EVENT_REFRESH_BIND_USER</text>
  
  <!-- 刷新家庭版配置 -->
  <line x1="260" y1="1100" x2="560" y2="1100" class="message-arrow"/>
  <text x="410" y="1095" class="message-text">getMedicalHomeProfile()</text>
  
  <!-- 定时刷新机制 -->
  <line x1="260" y1="1120" x2="300" y2="1120" class="self-call"/>
  <line x1="300" y1="1120" x2="300" y2="1130" class="self-call"/>
  <line x1="300" y1="1130" x2="260" y2="1130" class="self-call"/>
  <text x="305" y="1125" class="message-text">定时刷新</text>
  
  <!-- 步骤8: 生命周期结束 -->
  <text x="50" y="1170" class="step-text">8. 生命周期结束</text>
  
  <!-- Fragment销毁 -->
  <rect x="255" y="1190" width="10" height="40" class="activation"/>
  <line x1="260" y1="1200" x2="300" y2="1200" class="self-call"/>
  <line x1="300" y1="1200" x2="300" y2="1210" class="self-call"/>
  <line x1="300" y1="1210" x2="260" y2="1210" class="self-call"/>
  <text x="305" y="1205" class="message-text">onDestroyView()</text>
  
  <!-- 取消定时刷新 -->
  <line x1="260" y1="1220" x2="300" y2="1220" class="self-call"/>
  <line x1="300" y1="1220" x2="300" y2="1230" class="self-call"/>
  <line x1="300" y1="1230" x2="260" y2="1230" class="self-call"/>
  <text x="305" y="1225" class="message-text">cancelRefreshBindUser()</text>
  
  <!-- 注释框 -->
  <rect x="1450" y="140" width="300" height="100" class="note-box"/>
  <text x="1460" y="160" class="note-text">注释说明:</text>
  <text x="1460" y="180" class="note-text">• 实线箭头: 同步调用</text>
  <text x="1460" y="195" class="note-text">• 虚线箭头: 异步返回</text>
  <text x="1460" y="210" class="note-text">• 自调用: 内部方法调用</text>
  <text x="1460" y="225" class="note-text">• 激活框: 对象生命周期</text>
  
  <!-- 关键时序说明 -->
  <rect x="50" y="1270" width="1700" height="70" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="70" y="1290" class="step-text">关键时序特点:</text>
  <text x="70" y="1310" class="message-text">1. Fragment生命周期驱动整个流程，从onCreate到onDestroyView完整管理资源</text>
  <text x="70" y="1325" class="message-text">2. 响应式编程模式，ViewModel通过LiveData异步通知Fragment更新UI，避免阻塞主线程</text>
  <text x="70" y="1340" class="message-text">3. 用户交互触发业务逻辑，如校准流程需要检查权限后才能执行，体现了权限控制的时序</text>
