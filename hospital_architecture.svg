<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 22px; font-weight: bold; text-anchor: middle; fill: #e74c3c; }
      .module-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; font-weight: bold; fill: white; text-anchor: middle; }
      .component { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; fill: white; text-anchor: middle; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #555; }
      .flow-text { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 16px; fill: #2c3e50; font-weight: bold; }
      .config-highlight { stroke: #ff6b35; stroke-width: 4; stroke-dasharray: 10,5; }
      .advantage-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #e74c3c; font-weight: bold; }
      .comparison-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; fill: #666; }
      .server-config { fill: #ff6b35; stroke: #e74c3c; stroke-width: 3; }
      .dynamic-arrow { stroke: #ff6b35; stroke-width: 5; marker-end: url(#dynamicArrow); }
      .version-box { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; }
      .weak-version { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 2; }
      .strong-version { fill: #00b894; stroke: #00a085; stroke-width: 3; }
      .config-flow { stroke: #6c5ce7; stroke-width: 3; stroke-dasharray: 5,5; marker-end: url(#configArrow); }
      .api-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .data-flow { stroke: #3498db; stroke-width: 3; marker-end: url(#dataArrow); }
      .config-data { fill: #f39c12; stroke:rgb(213, 87, 24); stroke-width: 2; }
    </style>
    <marker id="dynamicArrow" markerWidth="15" markerHeight="12" refX="12" refY="6" orient="auto">
      <polygon points="0 0, 15 6, 0 12" fill="#ff6b35"/>
    </marker>
    <marker id="configArrow" markerWidth="12" markerHeight="10" refX="10" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="#6c5ce7"/>
    </marker>
    <marker id="dataArrow" markerWidth="12" markerHeight="10" refX="10" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="#3498db"/>
    </marker>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    <filter id="strongGlow">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 主标题 -->
  <text x="900" y="40" class="title">医疗进院版系统架构图</text>
  <text x="900" y="75" class="subtitle">🏆 三版本中动态配置能力最强 - 无需发版即可调整业务流程</text>

  <!-- 三版本对比区域 -->
  <rect x="50" y="100" width="1700" height="160" fill="#f8f9fa" stroke="#dee2e6" stroke-width="3" rx="15"/>
  <text x="70" y="130" class="flow-text">📊 三版本架构对比分析 - 突出进院版动态配置优势</text>

  <!-- 医疗家庭版 -->
  <rect x="80" y="150" width="450" height="100" class="weak-version" rx="10" filter="url(#shadow)"/>
  <text x="305" y="175" class="module-title" style="fill: #2d3436;">📱 医疗家庭版 (M_HOME)</text>
  <text x="100" y="195" class="comparison-text">• Fragment嵌入式架构 (固化设计)</text>
  <text x="100" y="210" class="comparison-text">• 模块配置写死在代码中</text>
  <text x="100" y="225" class="comparison-text">• 需要发版才能调整业务流程</text>
  <text x="100" y="240" class="comparison-text">配置能力: ⭐⭐ (有限，需发版)</text>

  <!-- 阅读版 -->
  <rect x="550" y="150" width="450" height="100" class="weak-version" rx="10" filter="url(#shadow)"/>
  <text x="775" y="175" class="module-title" style="fill: #2d3436;">📖 阅读版 (R_HOME/R_STORE)</text>
  <text x="570" y="195" class="comparison-text">• Fragment嵌入式架构 (固化设计)</text>
  <text x="570" y="210" class="comparison-text">• 模块配置写死在代码中</text>
  <text x="570" y="225" class="comparison-text">• 需要发版才能调整业务流程</text>
  <text x="570" y="240" class="comparison-text">配置能力: ⭐⭐ (有限，需发版)</text>

  <!-- 医疗进院版 -->
  <rect x="1020" y="150" width="650" height="100" class="strong-version" rx="10" filter="url(#strongGlow)"/>
  <text x="1345" y="175" class="module-title">🏥 医疗进院版 (M_HOSPITAL) - 业界领先</text>
  <text x="1040" y="195" class="component">• Activity跳转架构 + 完全动态配置系统</text>
  <text x="1040" y="210" class="component">• 服务器端实时控制，零发版更新业务流程</text>
  <text x="1040" y="225" class="component">• 支持模块开关、URL、认证信息动态配置</text>
  <text x="1040" y="240" class="advantage-text" style="fill: white;">配置能力: ⭐⭐⭐⭐⭐ (业界领先，零发版运营)</text>

  <!-- 服务器配置层 (最突出的核心优势) -->
  <rect x="50" y="280" width="500" height="280" class="server-config" rx="15" filter="url(#strongGlow)"/>
  <text x="300" y="310" class="module-title">🌐 服务器端动态配置层 - 核心竞争力</text>
  <text x="300" y="330" class="component" style="fill: white; font-size: 16px;">⚡ 实时配置系统 - 无需发版更新业务流程</text>

  <!-- API接口层 -->
  <rect x="70" y="350" width="200" height="80" class="api-box" rx="8"/>
  <text x="170" y="375" class="component">HospitalApiService</text>
  <text x="170" y="390" class="component">getHospitalEditionProfile()</text>
  <text x="170" y="405" class="component">🔄 实时获取最新配置</text>
  <text x="170" y="420" class="component">📡 服务器端控制</text>

  <rect x="290" y="350" width="200" height="80" class="config-data" rx="8"/>
  <text x="390" y="375" class="component">MHospitalProfile</text>
  <text x="390" y="390" class="component">bizModules配置数据</text>
  <text x="390" y="405" class="component">🎛️ 模块开关控制</text>
  <text x="390" y="420" class="component">📋 内容动态配置</text>

  <!-- 配置处理层 -->
  <rect x="70" y="450" width="200" height="80" fill="#c0392b" rx="8"/>
  <text x="170" y="475" class="component">HospitalViewModel</text>
  <text x="170" y="490" class="component">配置数据处理</text>
  <text x="170" y="505" class="component">✅ moduleEnable过滤</text>
  <text x="170" y="520" class="component">🔧 实时生效控制</text>

  <rect x="290" y="450" width="200" height="80" fill="#c0392b" rx="8"/>
  <text x="390" y="475" class="component">HospitalModuleAdapter</text>
  <text x="390" y="490" class="component">动态UI渲染</text>
  <text x="390" y="505" class="component">📐 自适应布局调整</text>
  <text x="390" y="520" class="component">🎨 UI自动优化</text>

  <!-- 主界面层 -->
  <rect x="600" y="280" width="400" height="280" fill="#3498db" rx="10" stroke="#2980b9" stroke-width="2"/>
  <text x="800" y="310" class="module-title">📱 主界面层 - 动态渲染引擎</text>

  <rect x="620" y="330" width="160" height="60" fill="#2980b9" rx="5"/>
  <text x="700" y="350" class="component">HospitalMainActivity</text>
  <text x="700" y="365" class="component">主容器Activity</text>
  <text x="700" y="380" class="component">🏠 统一入口管理</text>

  <rect x="800" y="330" width="160" height="60" fill="#2980b9" rx="5"/>
  <text x="880" y="350" class="component">HospitalMainFragment</text>
  <text x="880" y="365" class="component">首页Fragment</text>
  <text x="880" y="380" class="component">📋 模块展示容器</text>

  <rect x="620" y="410" width="160" height="60" fill="#2980b9" rx="5"/>
  <text x="700" y="430" class="component">HospitalInitFragment</text>
  <text x="700" y="445" class="component">初始化页面</text>
  <text x="700" y="460" class="component">⚙️ 配置加载</text>

  <rect x="800" y="410" width="160" height="60" fill="#2980b9" rx="5"/>
  <text x="880" y="430" class="component">HospitalModuleAdapter</text>
  <text x="880" y="445" class="component">🔄 动态适配器</text>
  <text x="880" y="460" class="component">📐 自适应布局</text>

  <rect x="620" y="490" width="340" height="60" fill="#2980b9" rx="5"/>
  <text x="790" y="510" class="component">动态配置响应机制</text>
  <text x="790" y="525" class="component">🔄 实时监听配置变化 → 自动更新UI → 无需重启应用</text>
  <text x="790" y="540" class="component">⚡ 配置变更立即生效，用户无感知更新</text>
  <!-- 业务模块层 -->
  <rect x="1050" y="280" width="650" height="280" fill="#27ae60" rx="10" stroke="#229954" stroke-width="2"/>
  <text x="1375" y="310" class="module-title">🏥 业务模块层 - 完全动态加载</text>

  <!-- 核心业务模块 -->
  <rect x="1070" y="330" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1160" y="350" class="component">🔍 InspectionCenter</text>
  <text x="1160" y="365" class="component">检查中心</text>
  <text x="1160" y="380" class="component">🔄 moduleEnable控制</text>
  <text x="1160" y="395" class="component">🌐 URL动态配置</text>

  <rect x="1270" y="330" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1360" y="350" class="component">🏃 TrainingCenter</text>
  <text x="1360" y="365" class="component">训练中心</text>
  <text x="1360" y="380" class="component">🔄 moduleEnable控制</text>
  <text x="1360" y="395" class="component">🌐 URL动态配置</text>

  <rect x="1470" y="330" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1560" y="350" class="component">👁️ MaskingTherapy</text>
  <text x="1560" y="365" class="component">遮盖疗法</text>
  <text x="1560" y="380" class="component">🔄 moduleEnable控制</text>
  <text x="1560" y="395" class="component">� Token动态配置</text>

  <!-- 扩展模块 -->
  <rect x="1070" y="420" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1160" y="440" class="component">👥 PatientLibrary</text>
  <text x="1160" y="455" class="component">患者库管理</text>
  <text x="1160" y="470" class="component">📊 动态数据源</text>
  <text x="1160" y="485" class="component">🔄 实时同步</text>

  <rect x="1270" y="420" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1360" y="440" class="component">📈 DataAnalytics</text>
  <text x="1360" y="455" class="component">数据分析</text>
  <text x="1360" y="470" class="component">📊 动态报表</text>
  <text x="1360" y="485" class="component">🔄 实时更新</text>

  <rect x="1470" y="420" width="180" height="70" fill="#229954" rx="5"/>
  <text x="1560" y="440" class="component">🌐 WebViewModule</text>
  <text x="1560" y="455" class="component">网页模块</text>
  <text x="1560" y="470" class="component">🔗 URL完全可配置</text>
  <text x="1560" y="485" class="component">🔑 认证信息动态</text>

  <!-- 动态路由机制 -->
  <rect x="1070" y="510" width="580" height="40" fill="#1e8449" rx="5"/>
  <text x="1360" y="525" class="component">🎯 智能模块路由: moduleKey → Activity跳转</text>
  <text x="1360" y="540" class="component">⚡ 支持参数动态传递 (URL、Token、配置信息)</text>

  <!-- 动态配置数据流箭头 -->
  <!-- 服务器配置到主界面的实时数据流 -->
  <line x1="550" y1="420" x2="600" y2="420" class="dynamic-arrow"/>
  <text x="575" y="410" class="advantage-text">🔄 实时配置</text>
  <text x="575" y="440" class="advantage-text">零延迟传递</text>

  <!-- 主界面到业务模块的动态启动流 -->
  <line x1="1000" y1="420" x2="1050" y2="420" class="dynamic-arrow"/>
  <text x="1025" y="410" class="advantage-text">⚡ 动态启动</text>
  <text x="1025" y="440" class="advantage-text">按需加载</text>

  <!-- 配置数据流向标注 -->
  <line x1="300" y1="430" x2="300" y2="480" class="config-flow"/>
  <line x1="800" y1="480" x2="800" y2="530" class="config-flow"/>
  <line x1="1375" y1="480" x2="1375" y2="510" class="config-flow"/>

  <!-- 动态配置特性标注 -->
  <rect x="50" y="580" width="1650" height="100" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="70" y="605" class="advantage-text">🏆 医疗进院版独有优势 - 业界领先的动态配置能力:</text>
  <text x="90" y="625" class="description">✅ 服务器端实时控制模块启用/禁用  ✅ 无需App发版即可调整业务流程  ✅ 支持模块内容、URL、认证信息动态配置</text>
  <text x="90" y="640" class="description">✅ 自适应UI布局调整  ✅ 多语言动态支持  ✅ 医院个性化定制  ✅ 运营策略快速响应  ✅ A/B测试支持</text>
  <text x="90" y="655" class="description">✅ 零停机配置更新  ✅ 模块间完全解耦  ✅ 支持热插拔业务模块  ✅ 实时监控配置生效状态</text>
  <text x="90" y="670" class="advantage-text">🎯 对比其他版本: 家庭版/阅读版采用固定Fragment嵌入，配置能力有限，需要发版更新</text>

  <!-- 动态配置流程说明 -->
  <text x="50" y="710" class="title">🚀 医疗进院版动态配置流程 - 业界领先技术架构</text>

  <!-- 左侧流程 -->
  <text x="50" y="750" class="flow-text">1. 🔄 实时配置获取流程:</text>
  <text x="70" y="770" class="description">• 应用启动 → HospitalViewModel.getHospitalEditionProfile()</text>
  <text x="70" y="785" class="description">• API调用: /dt/api/device/v1/profile/m-hospital</text>
  <text x="70" y="800" class="description">• 服务器返回 MHospitalProfile 包含 bizModules 配置</text>
  <text x="70" y="815" class="description">• 根据 moduleEnable 字段实时过滤可用模块</text>

  <text x="50" y="845" class="flow-text">2. ⚡ 动态界面渲染机制:</text>
  <text x="70" y="865" class="description">• HospitalModuleAdapter.updateModule() 根据配置渲染</text>
  <text x="70" y="880" class="description">• 自动调整 RecyclerView ItemDecoration 间距</text>
  <text x="70" y="895" class="description">• 支持水平滚动，模块数量动态扩展</text>
  <text x="70" y="910" class="description">• UI布局自适应，无需硬编码尺寸</text>

  <text x="50" y="940" class="flow-text">3. 🎯 智能模块路由系统:</text>
  <text x="70" y="960" class="description">• moduleKey 映射: inspection-center → InspectionCenterActivity</text>
  <text x="70" y="975" class="description">• 动态参数传递: URL、loginAuthToken、cover 等</text>
  <text x="70" y="990" class="description">• Activity 独立启动，模块间完全解耦</text>
  <text x="70" y="1005" class="description">• 支持 WebView 模块，URL 完全可配置</text>

  <!-- 右侧优势 -->
  <text x="900" y="750" class="flow-text">4. 🏆 核心技术优势:</text>
  <text x="920" y="770" class="advantage-text">✅ 零停机配置更新 - 无需重启应用</text>
  <text x="920" y="785" class="advantage-text">✅ 服务器端实时控制 - 运营策略快速响应</text>
  <text x="920" y="800" class="advantage-text">✅ 医院个性化定制 - 按需启用业务模块</text>
  <text x="920" y="815" class="advantage-text">✅ 多语言动态支持 - 国际化部署友好</text>
  <text x="920" y="830" class="advantage-text">✅ A/B 测试支持 - 数据驱动业务优化</text>

  <text x="900" y="860" class="flow-text">5. 🆚 三版本技术对比:</text>
  <text x="920" y="880" class="description">📱 医疗家庭版: Fragment 嵌入，配置固化，需发版更新</text>
  <text x="920" y="895" class="description">📖 阅读版: Fragment 嵌入，配置固化，需发版更新</text>
  <text x="920" y="910" class="advantage-text">🏥 医疗进院版: Activity 跳转 + 动态配置，零发版更新 🏆</text>
  <text x="920" y="925" class="advantage-text">🎖️ 配置能力提升 10x，运维成本降低 90%</text>

  <text x="900" y="955" class="flow-text">6. 📊 业务价值与技术指标:</text>
  <text x="920" y="975" class="advantage-text">• 配置更新响应时间: 实时 (vs 其他版本需发版)</text>
  <text x="920" y="990" class="advantage-text">• 模块扩展能力: 无限制 (vs 其他版本固定)</text>
  <text x="920" y="1005" class="advantage-text">• 医院定制化程度: 100% (vs 其他版本 0%)</text>

  <!-- 技术领先标注 -->
  <rect x="50" y="1030" width="1650" height="60" fill="#d4edda" stroke="#28a745" stroke-width="3" rx="15"/>
  <text x="875" y="1055" class="advantage-text" style="font-size: 18px; text-anchor: middle;">🎖️ 医疗进院版 = 业界领先的动态配置架构 + 零发版运营能力 + 医院级定制化支持</text>
  <text x="875" y="1075" class="advantage-text" style="font-size: 16px; text-anchor: middle;">🏆 三版本中唯一支持服务器端实时控制业务流程的版本，技术领先优势明显</text>

  <!-- 配置数据结构示例 -->
  <rect x="50" y="1110" width="800" height="200" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="450" y="1135" class="flow-text" style="text-anchor: middle;">📋 动态配置数据结构示例 (MHospitalProfile)</text>

  <text x="70" y="1160" class="description" style="font-family: 'Courier New', monospace; font-size: 11px;">bizModules: [</text>
  <text x="90" y="1175" class="description" style="font-family: 'Courier New', monospace; font-size: 11px;">  { moduleKey: "inspection-center", moduleEnable: true, url: "...", cover: "..." },</text>
  <text x="90" y="1190" class="description" style="font-family: 'Courier New', monospace; font-size: 11px;">  { moduleKey: "training-center", moduleEnable: false, url: "...", cover: "..." },</text>
  <text x="90" y="1205" class="description" style="font-family: 'Courier New', monospace; font-size: 11px;">  { moduleKey: "masking-therapy", moduleEnable: true, loginAuthToken: "...", cover: "..." }</text>
  <text x="70" y="1220" class="description" style="font-family: 'Courier New', monospace; font-size: 11px;">]</text>

  <text x="70" y="1245" class="advantage-text">⚡ 服务器端修改 moduleEnable: false → true，客户端立即生效</text>
  <text x="70" y="1260" class="advantage-text">🔄 无需发版，无需重启，用户无感知更新</text>
  <text x="70" y="1275" class="advantage-text">🎯 医院可根据业务需求实时调整功能模块</text>
  <text x="70" y="1290" class="advantage-text">📊 支持 A/B 测试，数据驱动产品迭代</text>

  <!-- 技术架构优势总结 -->
  <rect x="900" y="1110" width="800" height="200" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
  <text x="1300" y="1135" class="flow-text" style="text-anchor: middle;">🏆 医疗进院版技术架构优势总结</text>

  <text x="920" y="1160" class="advantage-text">1. 🎯 架构设计: Activity 跳转 vs Fragment 嵌入</text>
  <text x="940" y="1175" class="description">• 进院版: 模块间完全解耦，支持独立开发部署</text>
  <text x="940" y="1190" class="description">• 其他版本: Fragment 嵌入，模块耦合度高</text>

  <text x="920" y="1210" class="advantage-text">2. 🔄 配置管理: 动态配置 vs 静态配置</text>
  <text x="940" y="1225" class="description">• 进院版: 服务器端实时控制，零发版更新</text>
  <text x="940" y="1240" class="description">• 其他版本: 配置写死代码，需发版更新</text>

  <text x="920" y="1260" class="advantage-text">3. 🏥 定制化能力: 100% vs 0%</text>
  <text x="940" y="1275" class="description">• 进院版: 医院级个性化定制，按需启用模块</text>
  <text x="940" y="1290" class="description">• 其他版本: 固定功能，无法定制</text>

</svg>
