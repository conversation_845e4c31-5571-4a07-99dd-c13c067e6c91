<?xml version="1.0" encoding="UTF-8"?>
<svg width="1900" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 22px; font-weight: bold; text-anchor: middle; fill: #e74c3c; }
      .section-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #2c3e50; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #555; }
      .problem-text { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 13px; fill: #e74c3c; font-weight: bold; }
      .advantage-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; fill: #27ae60; font-weight: bold; }
      .weak-box { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 3; }
      .strong-box { fill: #00b894; stroke: #00a085; stroke-width: 3; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      .problem-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .solution-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .flow-arrow { stroke: #e74c3c; stroke-width: 4; marker-end: url(#problemArrow); }
      .success-arrow { stroke: #27ae60; stroke-width: 4; marker-end: url(#successArrow); }
      .vs-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 48px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
    </style>
    <marker id="problemArrow" markerWidth="12" markerHeight="10" refX="10" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="#e74c3c"/>
    </marker>
    <marker id="successArrow" markerWidth="12" markerHeight="10" refX="10" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="#27ae60"/>
    </marker>
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 主标题 -->
  <text x="950" y="40" class="title">为什么进院版不需要发版？与家庭版深度对比</text>
  <text x="950" y="75" class="subtitle">架构决定命运：Fragment嵌入 vs Activity跳转 + 动态配置</text>

  <!-- VS 标识 -->
  <text x="950" y="130" class="vs-text">VS</text>

  <!-- 医疗家庭版 - 左侧 -->
  <rect x="50" y="160" width="800" height="600" class="weak-box" rx="15" filter="url(#shadow)"/>
  <text x="450" y="190" class="section-title" style="fill: #2d3436;">📱 医疗家庭版 - 需要发版的架构</text>

  <!-- 家庭版问题1: 枚举固化 -->
  <rect x="70" y="210" width="760" height="100" class="problem-box" rx="8"/>
  <text x="450" y="235" class="problem-text" style="text-anchor: middle;">❌ 问题1: 模块枚举固化在代码中</text>
  <text x="90" y="255" class="code-text">enum class TreatmentModule(val moduleKey:String) {</text>
  <text x="110" y="270" class="code-text">OCCLUSION_THERAPY("occlusion-therapy"),  // 写死，无法动态修改</text>
  <text x="110" y="285" class="code-text">VISION_THERAPY("vision-therapy")         // 新增模块需修改此处</text>
  <text x="90" y="300" class="code-text">}</text>

  <!-- 家庭版问题2: Fragment嵌入 -->
  <rect x="70" y="320" width="760" height="120" class="problem-box" rx="8"/>
  <text x="450" y="345" class="problem-text" style="text-anchor: middle;">❌ 问题2: Fragment嵌入式架构 - 高耦合</text>
  <text x="90" y="365" class="code-text">when(module.moduleKey){</text>
  <text x="110" y="380" class="code-text">TreatmentModule.OCCLUSION_THERAPY.moduleKey -> {</text>
  <text x="130" y="395" class="code-text">// Fragment直接嵌入，无法动态替换</text>
  <text x="130" y="410" class="code-text">beginTransaction.replace(id, MaskTherapyFragment.newInstance())</text>
  <text x="110" y="425" class="code-text">}</text>
  <text x="90" y="440" class="code-text">}</text>

  <!-- 家庭版问题3: 过滤逻辑固化 -->
  <rect x="70" y="450" width="760" height="100" class="problem-box" rx="8"/>
  <text x="450" y="475" class="problem-text" style="text-anchor: middle;">❌ 问题3: 业务逻辑硬编码</text>
  <text x="90" y="495" class="code-text">val filterModules = modules.filter { module -></text>
  <text x="110" y="510" class="code-text">module.moduleEnable == true &&</text>
  <text x="110" y="525" class="code-text">(module.moduleKey == TreatmentModule.OCCLUSION_THERAPY.moduleKey ||</text>
  <text x="110" y="540" class="code-text"> module.moduleKey == TreatmentModule.VISION_THERAPY.moduleKey) // 硬编码</text>

  <!-- 家庭版发版流程 -->
  <rect x="70" y="560" width="760" height="180" class="problem-box" rx="8"/>
  <text x="450" y="585" class="problem-text" style="text-anchor: middle;">🔄 家庭版业务调整流程 - 必须发版</text>
  
  <text x="90" y="610" class="description" style="font-weight: bold;">新增模块步骤 (耗时数天到数周):</text>
  <text x="110" y="630" class="description">1. 修改 TreatmentModule 枚举 → 添加新模块常量</text>
  <text x="110" y="645" class="description">2. 修改 TreatmentModuleAdapter → 添加 Fragment 映射</text>
  <text x="110" y="660" class="description">3. 修改 HomeMainFragment → 更新过滤逻辑</text>
  <text x="110" y="675" class="description">4. 开发新的 Fragment → 实现业务逻辑</text>
  <text x="110" y="690" class="description">5. 编译 → 测试 → 发版 → 用户更新</text>
  <text x="110" y="705" class="problem-text">6. ❌ 用户必须更新App才能看到新功能</text>
  <text x="110" y="720" class="problem-text">7. ❌ 无法回滚，出问题需要再次发版</text>

  <!-- 医疗进院版 - 右侧 -->
  <rect x="1050" y="160" width="800" height="600" class="strong-box" rx="15" filter="url(#glow)"/>
  <text x="1450" y="190" class="section-title">🏥 医疗进院版 - 无需发版的架构</text>

  <!-- 进院版优势1: 动态配置 -->
  <rect x="1070" y="210" width="760" height="100" class="solution-box" rx="8"/>
  <text x="1450" y="235" class="advantage-text" style="text-anchor: middle;">✅ 优势1: 服务器端动态配置</text>
  <text x="1090" y="255" class="code-text">data class MHospitalMode(</text>
  <text x="1110" y="270" class="code-text">var moduleEnable: Boolean?,    // 服务器端实时控制</text>
  <text x="1110" y="285" class="code-text">var moduleKey: String?,        // 动态模块标识</text>
  <text x="1110" y="300" class="code-text">var url: String?               // 动态URL配置</text>

  <!-- 进院版优势2: Activity跳转 -->
  <rect x="1070" y="320" width="760" height="120" class="solution-box" rx="8"/>
  <text x="1450" y="345" class="advantage-text" style="text-anchor: middle;">✅ 优势2: Activity跳转架构 - 完全解耦</text>
  <text x="1090" y="365" class="code-text">when(it.moduleKey){  // it 来自服务器配置</text>
  <text x="1110" y="380" class="code-text">HospitalModuleKey.INSPECTION_CENTER.moduleKey -> {</text>
  <text x="1130" y="395" class="code-text">// Activity跳转，模块完全独立</text>
  <text x="1130" y="410" class="code-text">startActivity(InspectionCenterActivity.createIntent(url))</text>
  <text x="1110" y="425" class="code-text">}</text>
  <text x="1090" y="440" class="code-text">}</text>

  <!-- 进院版优势3: 实时配置 -->
  <rect x="1070" y="450" width="760" height="100" class="solution-box" rx="8"/>
  <text x="1450" y="475" class="advantage-text" style="text-anchor: middle;">✅ 优势3: 实时配置获取</text>
  <text x="1090" y="495" class="code-text">// 启动时调用API获取最新配置</text>
  <text x="1090" y="510" class="code-text">hospitalVM.getHospitalEditionProfile()  // 实时获取</text>
  <text x="1090" y="525" class="code-text">// 服务器返回配置 → 客户端自动渲染</text>
  <text x="1090" y="540" class="code-text">updateModule(it?.bizModules)  // 动态更新UI</text>

  <!-- 进院版零发版流程 -->
  <rect x="1070" y="560" width="760" height="180" class="solution-box" rx="8"/>
  <text x="1450" y="585" class="advantage-text" style="text-anchor: middle;">⚡ 进院版业务调整流程 - 零发版更新</text>
  
  <text x="1090" y="610" class="description" style="font-weight: bold;">新增模块步骤 (耗时数分钟):</text>
  <text x="1110" y="630" class="advantage-text">1. 服务器端添加模块配置 → 设置 moduleKey、url 等</text>
  <text x="1110" y="645" class="advantage-text">2. 客户端自动获取配置 → 无需修改代码</text>
  <text x="1110" y="660" class="advantage-text">3. UI自动渲染新模块 → 动态适配布局</text>
  <text x="1110" y="675" class="advantage-text">4. 用户立即看到新功能 → 无需更新App</text>
  <text x="1110" y="690" class="advantage-text">5. ✅ 配置错误可立即回滚</text>
  <text x="1110" y="705" class="advantage-text">6. ✅ 支持A/B测试和灰度发布</text>
  <text x="1110" y="720" class="advantage-text">7. ✅ 医院可根据需求个性化定制</text>

  <!-- 数据流对比箭头 -->
  <line x1="450" y1="770" x2="450" y2="820" class="flow-arrow"/>
  <text x="450" y="810" class="problem-text" style="text-anchor: middle;">需要发版</text>

  <line x1="1450" y1="770" x2="1450" y2="820" class="success-arrow"/>
  <text x="1450" y="810" class="advantage-text" style="text-anchor: middle;">零发版更新</text>

  <!-- 核心差异对比 -->
  <rect x="50" y="840" width="1800" height="200" class="code-box" rx="15" filter="url(#shadow)"/>
  <text x="950" y="870" class="title" style="font-size: 24px;">🎯 核心技术差异对比 - 为什么进院版不需要发版？</text>

  <!-- 配置来源对比 -->
  <text x="70" y="900" class="description" style="font-weight: bold; font-size: 16px;">1. 📋 配置数据来源:</text>
  <text x="100" y="920" class="problem-text">❌ 家庭版: 枚举常量 → 编译时固化 → 运行时无法修改</text>
  <text x="100" y="940" class="advantage-text">✅ 进院版: API接口 → 运行时获取 → 服务器端实时控制</text>

  <!-- 模块加载方式对比 -->
  <text x="70" y="970" class="description" style="font-weight: bold; font-size: 16px;">2. 🔧 模块加载方式:</text>
  <text x="100" y="990" class="problem-text">❌ 家庭版: Fragment嵌入 → 编译时绑定 → 无法动态替换</text>
  <text x="100" y="1010" class="advantage-text">✅ 进院版: Activity跳转 → 运行时路由 → 完全动态加载</text>

  <!-- 业务逻辑控制对比 -->
  <text x="1000" y="900" class="description" style="font-weight: bold; font-size: 16px;">3. 🎛️ 业务逻辑控制:</text>
  <text x="1030" y="920" class="problem-text">❌ 家庭版: 代码硬编码 → 逻辑固化 → 调整需发版</text>
  <text x="1030" y="940" class="advantage-text">✅ 进院版: 配置驱动 → 逻辑动态 → 实时调整生效</text>

  <!-- 扩展能力对比 -->
  <text x="1000" y="970" class="description" style="font-weight: bold; font-size: 16px;">4. 🚀 扩展能力:</text>
  <text x="1030" y="990" class="problem-text">❌ 家庭版: 修改源码 → 重新编译 → 发版更新</text>
  <text x="1030" y="1010" class="advantage-text">✅ 进院版: 服务器配置 → 客户端自适应 → 无缝扩展</text>

  <!-- 实际场景对比 -->
  <rect x="50" y="1060" width="1800" height="280" class="solution-box" rx="15"/>
  <text x="950" y="1090" class="title" style="font-size: 24px;">📊 实际业务场景对比 - 新增模块流程</text>

  <!-- 家庭版场景 -->
  <rect x="70" y="1110" width="850" height="200" class="weak-box" rx="10"/>
  <text x="495" y="1135" class="section-title" style="fill: #2d3436;">📱 家庭版新增"眼动训练"模块</text>

  <text x="90" y="1160" class="description" style="font-weight: bold;">开发团队需要做的工作:</text>
  <text x="110" y="1180" class="problem-text">1. 修改 TreatmentModule.kt → 添加 EYE_MOVEMENT_THERAPY</text>
  <text x="110" y="1195" class="problem-text">2. 修改 TreatmentModuleAdapter.kt → 添加 Fragment 映射</text>
  <text x="110" y="1210" class="problem-text">3. 修改 HomeMainFragment.kt → 更新过滤逻辑</text>
  <text x="110" y="1225" class="problem-text">4. 开发 EyeMovementFragment → 实现业务逻辑</text>
  <text x="110" y="1240" class="problem-text">5. 编译、测试、发版 → 耗时 1-2 周</text>
  <text x="110" y="1255" class="problem-text">6. 用户更新App → 才能使用新功能</text>
  <text x="110" y="1270" class="problem-text">7. ❌ 如果有Bug，需要再次发版修复</text>
  <text x="110" y="1285" class="problem-text">8. ❌ 无法针对特定医院定制</text>

  <!-- 进院版场景 -->
  <rect x="980" y="1110" width="850" height="200" class="strong-box" rx="10"/>
  <text x="1405" y="1135" class="section-title">🏥 进院版新增"眼动训练"模块</text>

  <text x="1000" y="1160" class="description" style="font-weight: bold;">运营团队只需要做的工作:</text>
  <text x="1020" y="1180" class="advantage-text">1. 服务器端添加配置 → moduleKey: "eye-movement-therapy"</text>
  <text x="1020" y="1195" class="advantage-text">2. 设置模块信息 → moduleName, cover, url 等</text>
  <text x="1020" y="1210" class="advantage-text">3. 设置启用状态 → moduleEnable: true</text>
  <text x="1020" y="1225" class="advantage-text">4. 配置立即生效 → 耗时 5 分钟</text>
  <text x="1020" y="1240" class="advantage-text">5. 用户立即看到 → 无需更新App</text>
  <text x="1020" y="1255" class="advantage-text">6. ✅ 配置错误可立即回滚</text>
  <text x="1020" y="1270" class="advantage-text">7. ✅ 可针对不同医院个性化配置</text>
  <text x="1020" y="1285" class="advantage-text">8. ✅ 支持A/B测试和灰度发布</text>

  <!-- 总结 -->
  <rect x="50" y="1360" width="1800" height="60" class="solution-box" rx="15"/>
  <text x="950" y="1385" class="title" style="font-size: 20px;">🏆 结论: 进院版通过架构创新实现零发版运营</text>
  <text x="950" y="1405" class="advantage-text" style="font-size: 16px; text-anchor: middle;">服务器端配置 + Activity跳转架构 = 业界领先的动态配置能力</text>

</svg>
