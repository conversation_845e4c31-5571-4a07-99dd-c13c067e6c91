<svg width="1800" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="homeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.3"/>
    </linearGradient>
    <linearGradient id="hospitalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:0.3"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <style>
    .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
    .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; font-weight: bold; fill: #34495e; text-anchor: middle; }
    .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; font-weight: bold; fill: #7f8c8d; text-anchor: middle; }
    .description { font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #2c3e50; }
    .advantage { font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #27ae60; font-weight: bold; }
    .disadvantage { font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #e74c3c; font-weight: bold; }
    .code-text { font-family: 'Consolas', monospace; font-size: 12px; fill: #2c3e50; }
    .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; fill: #e67e22; font-weight: bold; }
  </style>

  <!-- 标题 -->
  <text x="900" y="40" class="title">🏥 进院版 vs 🏠 家庭版：为什么发版需求不同？</text>

  <!-- 家庭版 - 左侧 -->
  <rect x="50" y="80" width="800" height="1050" fill="url(#homeGradient)" stroke="#ff6b6b" stroke-width="2" rx="15" filter="url(#shadow)"/>
  <text x="450" y="110" class="section-title">🏠 医疗家庭版 - 需要发版的架构</text>

  <!-- 家庭版架构图 -->
  <rect x="80" y="130" width="740" height="200" fill="#fff" stroke="#ff6b6b" stroke-width="1" rx="8"/>
  <text x="450" y="155" class="subtitle">Fragment 嵌入式架构</text>
  
  <!-- Fragment 嵌入示意图 -->
  <rect x="100" y="170" width="700" height="140" fill="#ffeaa7" stroke="#fdcb6e" stroke-width="1" rx="5"/>
  <text x="450" y="190" class="description" text-anchor="middle">HomeMainActivity (主容器)</text>
  
  <rect x="120" y="200" width="200" height="80" fill="#fab1a0" stroke="#e17055" stroke-width="1" rx="3"/>
  <text x="220" y="225" class="description" text-anchor="middle">MaskTherapyFragment</text>
  <text x="220" y="245" class="code-text" text-anchor="middle">(数字遮盖疗法)</text>
  <text x="220" y="260" class="code-text" text-anchor="middle">代码固化</text>
  
  <rect x="340" y="200" width="200" height="80" fill="#fab1a0" stroke="#e17055" stroke-width="1" rx="3"/>
  <text x="440" y="225" class="description" text-anchor="middle">VisualTrainFragment</text>
  <text x="440" y="245" class="code-text" text-anchor="middle">(视觉训练疗法)</text>
  <text x="440" y="260" class="code-text" text-anchor="middle">代码固化</text>
  
  <rect x="560" y="200" width="200" height="80" fill="#fab1a0" stroke="#e17055" stroke-width="1" rx="3"/>
  <text x="660" y="225" class="description" text-anchor="middle">其他Fragment</text>
  <text x="660" y="245" class="code-text" text-anchor="middle">(功能模块)</text>
  <text x="660" y="260" class="code-text" text-anchor="middle">代码固化</text>

  <!-- 家庭版配置方式 -->
  <rect x="80" y="350" width="740" height="150" fill="#fff" stroke="#ff6b6b" stroke-width="1" rx="8"/>
  <text x="450" y="375" class="subtitle">静态配置管理</text>
  
  <text x="100" y="400" class="code-text">// 家庭版配置 - 代码中硬编码</text>
  <text x="100" y="420" class="code-text">when(module.moduleKey){</text>
  <text x="120" y="435" class="code-text">TreatmentModule.OCCLUSION_THERAPY.moduleKey -> {</text>
  <text x="140" y="450" class="code-text">// 数字遮盖疗法 - 固定Fragment</text>
  <text x="140" y="465" class="code-text">beginTransaction.replace(flModuleRoot.id, MaskTherapyFragment.newInstance())</text>
  <text x="120" y="480" class="code-text">}</text>
  <text x="100" y="495" class="code-text">}</text>

  <!-- 家庭版问题分析 -->
  <rect x="80" y="520" width="740" height="200" fill="#fff" stroke="#ff6b6b" stroke-width="1" rx="8"/>
  <text x="450" y="545" class="subtitle">❌ 为什么需要发版？</text>
  
  <text x="100" y="570" class="disadvantage">1. 🔒 架构限制：Fragment 嵌入导致高耦合</text>
  <text x="120" y="590" class="description">• 所有功能模块都嵌入在同一个 Activity 中</text>
  <text x="120" y="605" class="description">• 模块间相互依赖，无法独立部署</text>
  
  <text x="100" y="630" class="disadvantage">2. 📝 配置固化：功能写死在代码中</text>
  <text x="120" y="650" class="description">• moduleKey 和对应的 Fragment 硬编码</text>
  <text x="120" y="665" class="description">• 新增功能必须修改源代码</text>
  
  <text x="100" y="690" class="disadvantage">3. 🚀 发版依赖：任何变更都需要重新打包</text>
  <text x="120" y="710" class="description">• 功能开关需要修改代码 → 重新编译 → 发版</text>

  <!-- 家庭版发版流程 -->
  <rect x="80" y="740" width="740" height="180" fill="#fff" stroke="#ff6b6b" stroke-width="1" rx="8"/>
  <text x="450" y="765" class="subtitle">📦 家庭版发版流程</text>
  
  <rect x="100" y="780" width="120" height="40" fill="#ff7675" stroke="#d63031" stroke-width="1" rx="5"/>
  <text x="160" y="805" class="description" text-anchor="middle">修改代码</text>
  
  <text x="230" y="805" class="description">→</text>
  
  <rect x="250" y="780" width="120" height="40" fill="#ff7675" stroke="#d63031" stroke-width="1" rx="5"/>
  <text x="310" y="805" class="description" text-anchor="middle">重新编译</text>
  
  <text x="380" y="805" class="description">→</text>
  
  <rect x="400" y="780" width="120" height="40" fill="#ff7675" stroke="#d63031" stroke-width="1" rx="5"/>
  <text x="460" y="805" class="description" text-anchor="middle">打包APK</text>
  
  <text x="530" y="805" class="description">→</text>
  
  <rect x="550" y="780" width="120" height="40" fill="#ff7675" stroke="#d63031" stroke-width="1" rx="5"/>
  <text x="610" y="805" class="description" text-anchor="middle">发布部署</text>
  
  <text x="680" y="805" class="description">→</text>
  
  <rect x="700" y="780" width="120" height="40" fill="#ff7675" stroke="#d63031" stroke-width="1" rx="5"/>
  <text x="760" y="805" class="description" text-anchor="middle">用户更新</text>

  <text x="100" y="850" class="highlight">⏱️ 发版周期：2-3周</text>
  <text x="100" y="870" class="highlight">💰 成本：高（开发+测试+发布）</text>
  <text x="100" y="890" class="highlight">🎯 灵活性：0%（无法动态调整）</text>

  <!-- 家庭版总结 -->
  <rect x="80" y="940" width="740" height="170" fill="#fff" stroke="#ff6b6b" stroke-width="1" rx="8"/>
  <text x="450" y="965" class="subtitle">📊 家庭版特点总结</text>
  
  <text x="100" y="990" class="disadvantage">❌ 架构缺陷：</text>
  <text x="120" y="1010" class="description">• Fragment 嵌入式架构，模块高度耦合</text>
  <text x="120" y="1025" class="description">• 配置硬编码，无法动态调整</text>
  
  <text x="100" y="1050" class="disadvantage">❌ 运维成本：</text>
  <text x="120" y="1070" class="description">• 任何功能变更都需要完整发版流程</text>
  <text x="120" y="1085" class="description">• 响应速度慢，无法快速适应医院需求</text>

  <!-- 进院版 - 右侧 -->
  <rect x="950" y="80" width="800" height="1050" fill="url(#hospitalGradient)" stroke="#4ecdc4" stroke-width="2" rx="15" filter="url(#shadow)"/>
  <text x="1350" y="110" class="section-title">🏥 医疗进院版 - 无需发版的架构</text>

  <!-- 进院版架构图 -->
  <rect x="980" y="130" width="740" height="200" fill="#fff" stroke="#4ecdc4" stroke-width="1" rx="8"/>
  <text x="1350" y="155" class="subtitle">Activity 跳转式架构</text>
  
  <!-- Activity 跳转示意图 -->
  <rect x="1000" y="170" width="700" height="140" fill="#a8e6cf" stroke="#81c784" stroke-width="1" rx="5"/>
  <text x="1350" y="190" class="description" text-anchor="middle">HospitalMainActivity (主入口)</text>
  
  <rect x="1020" y="200" width="200" height="80" fill="#81c784" stroke="#66bb6a" stroke-width="1" rx="3"/>
  <text x="1120" y="225" class="description" text-anchor="middle">InspectionCenterActivity</text>
  <text x="1120" y="245" class="code-text" text-anchor="middle">(检查中心)</text>
  <text x="1120" y="260" class="code-text" text-anchor="middle">独立Activity</text>
  
  <rect x="1240" y="200" width="200" height="80" fill="#81c784" stroke="#66bb6a" stroke-width="1" rx="3"/>
  <text x="1340" y="225" class="description" text-anchor="middle">TrainCenterActivity</text>
  <text x="1340" y="245" class="code-text" text-anchor="middle">(训练中心)</text>
  <text x="1340" y="260" class="code-text" text-anchor="middle">独立Activity</text>
  
  <rect x="1460" y="200" width="200" height="80" fill="#81c784" stroke="#66bb6a" stroke-width="1" rx="3"/>
  <text x="1560" y="225" class="description" text-anchor="middle">MHospitalMTActivity</text>
  <text x="1560" y="245" class="code-text" text-anchor="middle">(遮盖疗法)</text>
  <text x="1560" y="260" class="code-text" text-anchor="middle">独立Activity</text>

  <!-- 进院版配置方式 -->
  <rect x="980" y="350" width="740" height="150" fill="#fff" stroke="#4ecdc4" stroke-width="1" rx="8"/>
  <text x="1350" y="375" class="subtitle">动态配置管理</text>
  
  <text x="1000" y="400" class="code-text">// 进院版配置 - 服务器端动态控制</text>
  <text x="1000" y="420" class="code-text">when(it.moduleKey){</text>
  <text x="1020" y="435" class="code-text">HospitalModuleKey.INSPECTION_CENTER.moduleKey -> {</text>
  <text x="1040" y="450" class="code-text">// 动态URL，服务器端配置</text>
  <text x="1040" y="465" class="code-text">startActivity(InspectionCenterActivity.createIntent(mActivity, it.url))</text>
  <text x="1020" y="480" class="code-text">}</text>
  <text x="1000" y="495" class="code-text">}</text>

  <!-- 进院版优势分析 -->
  <rect x="980" y="520" width="740" height="200" fill="#fff" stroke="#4ecdc4" stroke-width="1" rx="8"/>
  <text x="1350" y="545" class="subtitle">✅ 为什么无需发版？</text>
  
  <text x="1000" y="570" class="advantage">1. 🔓 架构解耦：Activity 跳转实现完全解耦</text>
  <text x="1020" y="590" class="description">• 每个功能模块都是独立的 Activity</text>
  <text x="1020" y="605" class="description">• 模块间零依赖，支持独立开发部署</text>
  
  <text x="1000" y="630" class="advantage">2. 🌐 动态配置：服务器端实时控制</text>
  <text x="1020" y="650" class="description">• moduleKey、URL、开关状态都由服务器配置</text>
  <text x="1020" y="665" class="description">• 新增功能无需修改客户端代码</text>
  
  <text x="1000" y="690" class="advantage">3. ⚡ 实时生效：配置变更立即生效</text>
  <text x="1020" y="710" class="description">• 功能开关 → 服务器配置 → 5分钟生效</text>

  <!-- 进院版配置流程 -->
  <rect x="980" y="740" width="740" height="180" fill="#fff" stroke="#4ecdc4" stroke-width="1" rx="8"/>
  <text x="1350" y="765" class="subtitle">⚡ 进院版配置流程</text>
  
  <rect x="1000" y="780" width="120" height="40" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
  <text x="1060" y="805" class="description" text-anchor="middle">服务器配置</text>
  
  <text x="1130" y="805" class="description">→</text>
  
  <rect x="1150" y="780" width="120" height="40" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
  <text x="1210" y="805" class="description" text-anchor="middle">API推送</text>
  
  <text x="1280" y="805" class="description">→</text>
  
  <rect x="1300" y="780" width="120" height="40" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
  <text x="1360" y="805" class="description" text-anchor="middle">客户端接收</text>
  
  <text x="1430" y="805" class="description">→</text>
  
  <rect x="1450" y="780" width="120" height="40" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
  <text x="1510" y="805" class="description" text-anchor="middle">动态加载</text>
  
  <text x="1580" y="805" class="description">→</text>
  
  <rect x="1600" y="780" width="120" height="40" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
  <text x="1660" y="805" class="description" text-anchor="middle">立即生效</text>

  <text x="1000" y="850" class="highlight">⏱️ 配置周期：5分钟</text>
  <text x="1000" y="870" class="highlight">💰 成本：极低（仅配置变更）</text>
  <text x="1000" y="890" class="highlight">🎯 灵活性：100%（完全动态）</text>

  <!-- 进院版总结 -->
  <rect x="980" y="940" width="740" height="170" fill="#fff" stroke="#4ecdc4" stroke-width="1" rx="8"/>
  <text x="1350" y="965" class="subtitle">🏆 进院版特点总结</text>
  
  <text x="1000" y="990" class="advantage">✅ 架构优势：</text>
  <text x="1020" y="1010" class="description">• Activity 跳转式架构，模块完全解耦</text>
  <text x="1020" y="1025" class="description">• 动态配置，服务器端实时控制</text>
  
  <text x="1000" y="1050" class="advantage">✅ 运维优势：</text>
  <text x="1020" y="1070" class="description">• 零发版更新，配置变更立即生效</text>
  <text x="1020" y="1085" class="description">• 响应速度极快，完美适应医院个性化需求</text>

  <!-- 进院版实时生效机制详解 -->
  <rect x="50" y="1150" width="1700" height="350" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="900" y="1180" class="title">⚡ 进院版配置实时生效机制详解</text>

  <!-- 机制1: onResume生命周期触发 -->
  <rect x="80" y="1200" width="800" height="120" fill="#fff" stroke="#28a745" stroke-width="1" rx="8"/>
  <text x="480" y="1225" class="subtitle">🔄 机制1: Activity生命周期触发配置刷新</text>

  <text x="100" y="1250" class="code-text">// HospitalMainActivity.kt</text>
  <text x="100" y="1265" class="code-text">override fun onResume() {</text>
  <text x="120" y="1280" class="code-text">super.onResume()</text>
  <text x="120" y="1295" class="code-text">updateVM.getAppUpdateInfo()  // 每次回到前台都检查更新</text>
  <text x="100" y="1310" class="code-text">}</text>

  <!-- 机制2: MQTT实时推送 -->
  <rect x="920" y="1200" width="800" height="120" fill="#fff" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="1320" y="1225" class="subtitle">📡 机制2: MQTT实时推送机制</text>

  <text x="940" y="1250" class="code-text">// GTBaseActivity.kt - MQTT初始化</text>
  <text x="940" y="1265" class="code-text">fun initMQTT() {</text>
  <text x="960" y="1280" class="code-text">MQTTInitManager.init(...)  // 建立长连接</text>
  <text x="960" y="1295" class="code-text">// 服务器可实时推送配置变更</text>
  <text x="940" y="1310" class="code-text">}</text>

  <!-- 机制3: LiveEventBus事件监听 -->
  <rect x="80" y="1340" width="800" height="120" fill="#fff" stroke="#ffc107" stroke-width="1" rx="8"/>
  <text x="480" y="1365" class="subtitle">🎯 机制3: LiveEventBus事件监听</text>

  <text x="100" y="1390" class="code-text">// 配置变更事件监听</text>
  <text x="100" y="1405" class="code-text">LiveEventBus.get&lt;Any&gt;(RefreshBindUserReceiver.EVENT_REFRESH_BIND_USER)</text>
  <text x="120" y="1420" class="code-text">.observe(this) {</text>
  <text x="140" y="1435" class="code-text">hospitalVM.getHospitalEditionProfile()  // 重新获取配置</text>
  <text x="120" y="1450" class="code-text">}</text>

  <!-- 机制4: API轮询机制 -->
  <rect x="920" y="1340" width="800" height="120" fill="#fff" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="1320" y="1365" class="subtitle">🔁 机制4: API主动获取最新配置</text>

  <text x="940" y="1390" class="code-text">// HospitalMainFragment.kt</text>
  <text x="940" y="1405" class="code-text">override fun initData() {</text>
  <text x="960" y="1420" class="code-text">hospitalVM.getHospitalEditionProfile()  // 启动时获取</text>
  <text x="960" y="1435" class="code-text">// 每次都调用API获取最新服务器配置</text>
  <text x="940" y="1450" class="code-text">}</text>

  <!-- 实时生效流程图 -->
  <rect x="80" y="1480" width="1640" height="60" fill="#e9ecef" stroke="#6c757d" stroke-width="1" rx="5"/>
  <text x="900" y="1505" class="highlight" text-anchor="middle">⚡ 实时生效流程</text>

  <rect x="100" y="1520" width="120" height="30" fill="#28a745" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="160" y="1540" class="description" text-anchor="middle" style="fill: white;">服务器配置变更</text>

  <text x="230" y="1540" class="description">→</text>

  <rect x="250" y="1520" width="120" height="30" fill="#17a2b8" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="310" y="1540" class="description" text-anchor="middle" style="fill: white;">MQTT推送/API调用</text>

  <text x="380" y="1540" class="description">→</text>

  <rect x="400" y="1520" width="120" height="30" fill="#ffc107" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="460" y="1540" class="description" text-anchor="middle">LiveEventBus通知</text>

  <text x="530" y="1540" class="description">→</text>

  <rect x="550" y="1520" width="120" height="30" fill="#6f42c1" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="610" y="1540" class="description" text-anchor="middle" style="fill: white;">ViewModel更新</text>

  <text x="680" y="1540" class="description">→</text>

  <rect x="700" y="1520" width="120" height="30" fill="#fd7e14" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="760" y="1540" class="description" text-anchor="middle" style="fill: white;">UI自动刷新</text>

  <text x="830" y="1540" class="description">→</text>

  <rect x="850" y="1520" width="120" height="30" fill="#e83e8c" stroke="#20c997" stroke-width="1" rx="3"/>
  <text x="910" y="1540" class="description" text-anchor="middle" style="fill: white;">配置立即生效</text>

  <!-- 底部对比总结 -->
  <rect x="50" y="1560" width="1700" height="40" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="900" y="1585" class="title">🎯 核心差异：架构设计决定发版需求 - Fragment嵌入 vs Activity跳转 | 静态配置 vs 动态配置</text>
</svg>
