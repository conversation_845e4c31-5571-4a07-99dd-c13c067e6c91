#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 257949696 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=19208, tid=12724
#
# JRE version:  (21.0.6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://**********': 

Host: AMD Ryzen 5 4500U with Radeon Graphics         , 6 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3155)
Time: Thu Jul  3 10:29:28 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3155) elapsed time: 0.031111 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000138bc731f20):  JavaThread "Unknown thread" [_thread_in_vm, id=12724, stack(0x00000009dfe00000,0x00000009dff00000) (1024K)]

Stack: [0x00000009dfe00000,0x00000009dff00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aef86]
V  [jvm.dll+0x3af258]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x32871b]
V  [jvm.dll+0x826549]
V  [jvm.dll+0x3bc158]
V  [jvm.dll+0x80f7f8]
V  [jvm.dll+0x45035e]
V  [jvm.dll+0x451ac1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffcac9b1848, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000138bc795840 WorkerThread "GC Thread#0"                     [id=8816, stack(0x00000009dff00000,0x00000009e0000000) (1024K)]
  0x00000138bc7a7740 ConcurrentGCThread "G1 Main Marker"            [id=10816, stack(0x00000009e0000000,0x00000009e0100000) (1024K)]
  0x00000138bc7a83b0 WorkerThread "G1 Conc#0"                       [id=4760, stack(0x00000009e0100000,0x00000009e0200000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffcac1aaa87]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffcaca1e4a0] Heap_lock - owner thread: 0x00000138bc731f20

Heap address: 0x000000070a000000, size: 3936 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x000000070a000000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x00000138cf160000,0x00000138cf910000] _byte_map_base: 0x00000138cb910000

Marking Bits: (CMBitMap*) 0x00000138bc795f40
 Bits: [0x00000138cf910000, 0x00000138d3690000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.016 Loaded shared library D:\AndroidStudio\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff681a00000 - 0x00007ff681a0a000 	D:\AndroidStudio\jbr\bin\java.exe
0x00007ffd44410000 - 0x00007ffd44608000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffd43330000 - 0x00007ffd433ef000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd41cb0000 - 0x00007ffd41fa6000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd41bb0000 - 0x00007ffd41cb0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd2a290000 - 0x00007ffd2a2ab000 	D:\AndroidStudio\jbr\bin\VCRUNTIME140.dll
0x00007ffce5230000 - 0x00007ffce5248000 	D:\AndroidStudio\jbr\bin\jli.dll
0x00007ffd441a0000 - 0x00007ffd4433d000 	C:\Windows\System32\USER32.dll
0x00007ffd41b80000 - 0x00007ffd41ba2000 	C:\Windows\System32\win32u.dll
0x00007ffd27e80000 - 0x00007ffd2811a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ffd43870000 - 0x00007ffd4389c000 	C:\Windows\System32\GDI32.dll
0x00007ffd437d0000 - 0x00007ffd4386e000 	C:\Windows\System32\msvcrt.dll
0x00007ffd42140000 - 0x00007ffd42255000 	C:\Windows\System32\gdi32full.dll
0x00007ffd423d0000 - 0x00007ffd4246d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd42470000 - 0x00007ffd424a0000 	C:\Windows\System32\IMM32.DLL
0x00007ffd3ac10000 - 0x00007ffd3ac1c000 	D:\AndroidStudio\jbr\bin\vcruntime140_1.dll
0x00007ffccb250000 - 0x00007ffccb2dd000 	D:\AndroidStudio\jbr\bin\msvcp140.dll
0x00007ffcabe70000 - 0x00007ffcacafb000 	D:\AndroidStudio\jbr\bin\server\jvm.dll
0x00007ffd42c70000 - 0x00007ffd42d1f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffd42b20000 - 0x00007ffd42bbc000 	C:\Windows\System32\sechost.dll
0x00007ffd42990000 - 0x00007ffd42ab6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd44340000 - 0x00007ffd443ab000 	C:\Windows\System32\WS2_32.dll
0x00007ffd41670000 - 0x00007ffd416bb000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd374c0000 - 0x00007ffd374e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd3a160000 - 0x00007ffd3a16a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd41560000 - 0x00007ffd41572000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd401a0000 - 0x00007ffd401b2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd2ac10000 - 0x00007ffd2ac1a000 	D:\AndroidStudio\jbr\bin\jimage.dll
0x00007ffd3f990000 - 0x00007ffd3fb74000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffd26260000 - 0x00007ffd26294000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd41af0000 - 0x00007ffd41b72000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd21490000 - 0x00007ffd214b0000 	D:\AndroidStudio\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\AndroidStudio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;D:\AndroidStudio\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://**********': 
java_class_path (initial): D:/AndroidStudio/plugins/vcs-git/lib/git4idea-rt.jar;D:/AndroidStudio/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4127195136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4127195136                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=D:/Git/mingw64/libexec/git-core;D:/Git/mingw64/libexec/git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;D:\Git\cmd;D:\jdk1.8.0_451\bin;D:\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;D:\cpolar\;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=hanzhen
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 10736K (0% of 16117304K total physical memory with 421988K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3155)
OS uptime: 10 days 0:35 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, f16c
Processor Information for processor 0
  Max Mhz: 2375, Current Mhz: 1400, Mhz Limit: 2375
Processor Information for processor 1
  Max Mhz: 2375, Current Mhz: 1400, Mhz Limit: 2375
Processor Information for processor 2
  Max Mhz: 2375, Current Mhz: 1400, Mhz Limit: 2375
Processor Information for processor 3
  Max Mhz: 2375, Current Mhz: 1700, Mhz Limit: 2375
Processor Information for processor 4
  Max Mhz: 2375, Current Mhz: 1400, Mhz Limit: 2375
Processor Information for processor 5
  Max Mhz: 2375, Current Mhz: 1700, Mhz Limit: 2375

Memory: 4k page, system-wide physical 15739M (412M free)
TotalPageFile size 41339M (AvailPageFile size 196M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 56M, peak: 302M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
