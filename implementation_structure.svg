<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
      .section-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #555; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #666; }
      .java-layer { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8; }
      .native-layer { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 8; }
      .file-system { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 8; }
      .modification { fill: #e1f5fe; stroke: #2196f3; stroke-width: 2; rx: 5; }
      .existing { fill: #f5f5f5; stroke: #757575; stroke-width: 1; rx: 5; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">眼动校准参数默认值实现架构</text>
  
  <!-- Java Layer -->
  <g id="java-layer">
    <rect x="50" y="60" width="600" height="200" class="java-layer"/>
    <text x="70" y="85" class="section-title">Java层 (Android)</text>
    
    <!-- GazeTrackingManager -->
    <rect x="70" y="100" width="260" height="140" class="modification"/>
    <text x="200" y="120" text-anchor="middle" class="text">GazeTrackingManager.kt</text>
    <text x="90" y="140" class="code">private suspend fun copyModel2Dir(context: Context) {</text>
    <text x="90" y="155" class="code">  val assetsList = assetManager.list("configs")</text>
    <text x="90" y="170" class="code">  for (assets in assetsList) {</text>
    <text x="90" y="185" class="code">    if (assets.endsWith(".rknn")) {</text>
    <text x="90" y="200" class="code">      // 拷贝模型文件 (现有逻辑)</text>
    <text x="90" y="215" class="code">    } else if (assets == "calib_param.txt") {</text>
    <text x="90" y="230" class="code">      // 新增: 拷贝默认校准参数</text>
    
    <!-- GazeTrack -->
    <rect x="360" y="100" width="260" height="140" class="existing"/>
    <text x="490" y="120" text-anchor="middle" class="text">GazeTrack.kt (无需修改)</text>
    <text x="380" y="140" class="code">fun init(configDir: String, sn: String) {</text>
    <text x="380" y="155" class="code">  nativeObj.set(nativeCreateObject(configDir, sn))</text>
    <text x="380" y="170" class="code">  // 继续传递configDir路径</text>
    <text x="380" y="185" class="code">}</text>
    <text x="380" y="210" class="small-text">保持现有接口不变</text>
    <text x="380" y="225" class="small-text">向后兼容</text>
  </g>
  
  <!-- Native Layer -->
  <g id="native-layer">
    <rect x="50" y="280" width="600" height="280" class="native-layer"/>
    <text x="70" y="305" class="section-title">Native层 (C++)</text>
    
    <!-- GazeService.cpp -->
    <rect x="70" y="320" width="520" height="220" class="modification"/>
    <text x="330" y="340" text-anchor="middle" class="text">GazeService.cpp - update_tracker_config_func() 修改</text>
    
    <text x="90" y="360" class="code">void GazeService::update_tracker_config_func() {</text>
    <text x="90" y="375" class="code">  // 1. 检查用户校准参数文件 (现有逻辑)</text>
    <text x="90" y="390" class="code">  if (fileExists(config_filepath)) {</text>
    <text x="90" y="405" class="code">    if (readConfigFromFile(config_filepath, gaze_params)) {</text>
    <text x="90" y="420" class="code">      gaze_tracker.update_gaze_params(gaze_params);</text>
    <text x="90" y="435" class="code">      return; // 用户参数优先</text>
    <text x="90" y="450" class="code">    }</text>
    <text x="90" y="465" class="code">  }</text>
    <text x="90" y="485" class="code">  // 2. 新增: 检查默认参数文件</text>
    <text x="90" y="500" class="code">  string default_config = config_dir + "/calib_param_default.txt";</text>
    <text x="90" y="515" class="code">  if (fileExists(default_config)) {</text>
    <text x="90" y="530" class="code">    if (readConfigFromFile(default_config, gaze_params)) {</text>
  </g>
  
  <!-- File System -->
  <g id="file-system">
    <rect x="700" y="60" width="650" height="500" class="file-system"/>
    <text x="720" y="85" class="section-title">文件系统结构</text>
    
    <!-- Assets (Read-only) -->
    <rect x="720" y="100" width="280" height="80" class="existing"/>
    <text x="860" y="120" text-anchor="middle" class="text">assets/configs/ (只读)</text>
    <text x="740" y="140" class="code">calib_param.txt</text>
    <text x="740" y="155" class="code">├── 默认校准参数模板</text>
    <text x="740" y="170" class="code">└── 应用打包时包含</text>
    
    <!-- App Private Directory -->
    <rect x="720" y="200" width="280" height="120" class="modification"/>
    <text x="860" y="220" text-anchor="middle" class="text">/data/data/app/app_configs/</text>
    <text x="740" y="240" class="code">calib_param.txt</text>
    <text x="740" y="255" class="code">├── 用户校准后的参数</text>
    <text x="740" y="270" class="code">└── 最高优先级</text>
    <text x="740" y="290" class="code">calib_param_default.txt</text>
    <text x="740" y="305" class="code">├── 从assets拷贝的默认参数</text>
    <text x="740" y="320" class="code">└── 备用参数</text>
    
    <!-- Priority Flow -->
    <rect x="1020" y="100" width="300" height="220" class="existing"/>
    <text x="1170" y="120" text-anchor="middle" class="text">参数加载优先级</text>
    
    <rect x="1040" y="140" width="260" height="30" style="fill:#4caf50;stroke:#2e7d32;stroke-width:2;rx:5;"/>
    <text x="1170" y="160" text-anchor="middle" class="text">1. calib_param.txt (用户校准)</text>
    
    <line x1="1170" y1="170" x2="1170" y2="185" class="arrow"/>
    <text x="1180" y="180" class="small-text">不存在</text>
    
    <rect x="1040" y="185" width="260" height="30" style="fill:#ff9800;stroke:#f57c00;stroke-width:2;rx:5;"/>
    <text x="1170" y="205" text-anchor="middle" class="text">2. calib_param_default.txt (默认)</text>
    
    <line x1="1170" y1="215" x2="1170" y2="230" class="arrow"/>
    <text x="1180" y="225" class="small-text">不存在</text>
    
    <rect x="1040" y="230" width="260" height="30" style="fill:#f44336;stroke:#d32f2f;stroke-width:2;rx:5;"/>
    <text x="1170" y="250" text-anchor="middle" class="text">3. 提示用户校准</text>
    
    <!-- Copy Process -->
    <rect x="1020" y="340" width="300" height="120" class="modification"/>
    <text x="1170" y="360" text-anchor="middle" class="text">初始化拷贝过程</text>
    <text x="1040" y="380" class="code">应用启动时:</text>
    <text x="1040" y="395" class="code">assets/configs/calib_param.txt</text>
    <text x="1040" y="410" class="code">↓ 拷贝到</text>
    <text x="1040" y="425" class="code">/data/data/app/app_configs/</text>
    <text x="1040" y="440" class="code">calib_param_default.txt</text>
  </g>
  
  <!-- Flow Arrows -->
  <line x1="350" y1="260" x2="350" y2="280" class="arrow"/>
  <line x1="650" y1="420" x2="700" y2="420" class="arrow"/>
  <line x1="1000" y1="260" x2="1020" y2="260" class="arrow"/>
  
  <!-- Benefits -->
  <g id="benefits">
    <rect x="50" y="580" width="600" height="280" class="java-layer"/>
    <text x="70" y="605" class="section-title">实现优势</text>
    
    <rect x="70" y="620" width="180" height="100" class="modification"/>
    <text x="160" y="640" text-anchor="middle" class="text">向后兼容</text>
    <text x="90" y="660" class="small-text">• 现有功能完全不变</text>
    <text x="90" y="675" class="small-text">• 用户校准流程保持</text>
    <text x="90" y="690" class="small-text">• API接口不变</text>
    <text x="90" y="705" class="small-text">• 风险最小化</text>
    
    <rect x="270" y="620" width="180" height="100" class="modification"/>
    <text x="360" y="640" text-anchor="middle" class="text">用户体验</text>
    <text x="290" y="660" class="small-text">• 开箱即用</text>
    <text x="290" y="675" class="small-text">• 无需强制校准</text>
    <text x="290" y="690" class="small-text">• 渐进式降级</text>
    <text x="290" y="705" class="small-text">• 智能参数选择</text>
    
    <rect x="470" y="620" width="180" height="100" class="modification"/>
    <text x="560" y="640" text-anchor="middle" class="text">维护性</text>
    <text x="490" y="660" class="small-text">• 代码修改最小</text>
    <text x="490" y="675" class="small-text">• 逻辑清晰简单</text>
    <text x="490" y="690" class="small-text">• 易于测试验证</text>
    <text x="490" y="705" class="small-text">• 便于后续扩展</text>
    
    <!-- Implementation Steps -->
    <rect x="70" y="740" width="560" height="100" class="existing"/>
    <text x="350" y="760" text-anchor="middle" class="text">实现步骤</text>
    <text x="90" y="780" class="code">1. 修改 GazeTrackingManager.copyModel2Dir() - 添加 calib_param.txt 拷贝逻辑</text>
    <text x="90" y="795" class="code">2. 修改 GazeService.update_tracker_config_func() - 添加默认参数检查</text>
    <text x="90" y="810" class="code">3. 测试验证 - 确保三种场景都正常工作</text>
    <text x="90" y="825" class="code">4. 部署上线 - 渐进式发布，监控运行状态</text>
  </g>
  
  <!-- Test Scenarios -->
  <g id="test-scenarios">
    <rect x="700" y="580" width="650" height="280" class="native-layer"/>
    <text x="720" y="605" class="section-title">测试场景</text>
    
    <rect x="720" y="620" width="200" height="80" style="fill:#4caf50;opacity:0.3;stroke:#4caf50;stroke-width:2;rx:5;"/>
    <text x="820" y="640" text-anchor="middle" class="text">场景1: 有用户校准参数</text>
    <text x="740" y="660" class="small-text">• 使用用户参数</text>
    <text x="740" y="675" class="small-text">• 忽略默认参数</text>
    <text x="740" y="690" class="small-text">• 现有逻辑不变</text>
    
    <rect x="940" y="620" width="200" height="80" style="fill:#ff9800;opacity:0.3;stroke:#ff9800;stroke-width:2;rx:5;"/>
    <text x="1040" y="640" text-anchor="middle" class="text">场景2: 仅有默认参数</text>
    <text x="960" y="660" class="small-text">• 使用默认参数</text>
    <text x="960" y="675" class="small-text">• 正常启动追踪</text>
    <text x="960" y="690" class="small-text">• 新增功能生效</text>
    
    <rect x="1160" y="620" width="200" height="80" style="fill:#f44336;opacity:0.3;stroke:#f44336;stroke-width:2;rx:5;"/>
    <text x="1260" y="640" text-anchor="middle" class="text">场景3: 无任何参数</text>
    <text x="1180" y="660" class="small-text">• 提示用户校准</text>
    <text x="1180" y="675" class="small-text">• 保持现有行为</text>
    <text x="1180" y="690" class="small-text">• 降级处理</text>
    
    <rect x="720" y="720" width="610" height="120" class="modification"/>
    <text x="1025" y="740" text-anchor="middle" class="text">验证要点</text>
    <text x="740" y="760" class="code">✓ 用户校准后能正确覆盖默认参数</text>
    <text x="740" y="775" class="code">✓ 删除用户参数后能自动使用默认参数</text>
    <text x="740" y="790" class="code">✓ 首次安装应用能使用默认参数正常工作</text>
    <text x="740" y="805" class="code">✓ 校准精度和稳定性与原有功能一致</text>
    <text x="740" y="820" class="code">✓ 不同设备和环境下的兼容性测试</text>
  </g>
</svg>
