<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="activityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="webviewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3A9B5C;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="flipbeatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55555;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="maskGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9B59B6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#F8F9FA"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#2C3E50">
    InspectionCenterActivity 系统架构图
  </text>
  
  <!-- 主Activity层 -->
  <rect x="50" y="80" width="1300" height="150" rx="15" ry="15" fill="url(#activityGradient)" filter="url(#shadow)"/>
  <text x="700" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    InspectionCenterActivity (进院版检查中心)
  </text>
  
  <!-- Activity组件 -->
  <rect x="80" y="130" width="200" height="80" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="180" y="155" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">生命周期管理</text>
  <text x="180" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onCreate()</text>
  <text x="180" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onDestroy()</text>
  
  <rect x="320" y="130" width="200" height="80" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="420" y="155" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">参数处理</text>
  <text x="420" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="white">initParam()</text>
  <text x="420" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="white">URL参数解析</text>
  
  <rect x="560" y="130" width="200" height="80" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="660" y="155" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">按键事件</text>
  <text x="660" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="white">dispatchKeyEvent()</text>
  <text x="660" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="white">sendKeyCode()</text>
  
  <rect x="800" y="130" width="200" height="80" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="900" y="155" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">打印功能</text>
  <text x="900" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onPrintPage()</text>
  <text x="900" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="white">createWebPrintJob()</text>
  
  <rect x="1040" y="130" width="200" height="80" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1140" y="155" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">返回处理</text>
  <text x="1140" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="white">OnBackPressedCallback</text>
  <text x="1140" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="white">handleOnBackPressed()</text>
  
  <!-- WebView层 -->
  <rect x="50" y="280" width="600" height="200" rx="15" ry="15" fill="url(#webviewGradient)" filter="url(#shadow)"/>
  <text x="350" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    InspectionCenterWebView (Web容器)
  </text>
  
  <!-- WebView组件 -->
  <rect x="80" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="160" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">Web加载</text>
  <text x="160" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">loadUrl()</text>
  <text x="160" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">WebViewManager</text>
  <text x="160" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">hookWebView()</text>
  <text x="160" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">destroy()</text>
  
  <rect x="260" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="340" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">JS接口</text>
  <text x="340" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">addJavascriptInterface</text>
  <text x="340" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">InspectionCenterAction</text>
  <text x="340" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">postMessage()</text>
  <text x="340" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">finish()</text>
  
  <rect x="440" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="520" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">数据通信</text>
  <text x="520" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">sendDataToWebView()</text>
  <text x="520" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">backPressed()</text>
  <text x="520" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">home()</text>
  <text x="520" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">printPage()</text>
  
  <!-- FlipBeat设备层 -->
  <rect x="750" y="280" width="600" height="200" rx="15" ry="15" fill="url(#flipbeatGradient)" filter="url(#shadow)"/>
  <text x="1050" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    FlipBeat 翻转拍设备管理
  </text>
  
  <!-- FlipBeat组件 -->
  <rect x="780" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="860" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">设备连接</text>
  <text x="860" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">FlipBeatManager</text>
  <text x="860" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">connectFlipBeat()</text>
  <text x="860" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">蓝牙GATT</text>
  <text x="860" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">UUID配置</text>
  
  <rect x="960" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1040" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">命令控制</text>
  <text x="1040" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onFlipClap()</text>
  <text x="1040" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onFlipRecover()</text>
  <text x="1040" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">writeDataToFlipBeat()</text>
  <text x="1040" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">十六进制命令</text>
  
  <rect x="1160" y="330" width="160" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1240" y="355" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">状态监听</text>
  <text x="1240" y="375" text-anchor="middle" font-family="Arial" font-size="12" fill="white">FlipBeatListener</text>
  <text x="1240" y="390" text-anchor="middle" font-family="Arial" font-size="12" fill="white">onConnectionStateChange</text>
  <text x="1240" y="405" text-anchor="middle" font-family="Arial" font-size="12" fill="white">registerListener</text>
  <text x="1240" y="420" text-anchor="middle" font-family="Arial" font-size="12" fill="white">unRegisterListener</text>
  
  <!-- 数字遮盖系统层 -->
  <rect x="50" y="530" width="1300" height="200" rx="15" ry="15" fill="url(#maskGradient)" filter="url(#shadow)"/>
  <text x="700" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    数字遮盖疗法系统 (Digital Mask Therapy)
  </text>
  
  <!-- 遮盖系统组件 -->
  <rect x="80" y="580" width="200" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="180" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">参数管理</text>
  <text x="180" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">MaskManager</text>
  <text x="180" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">setCoverChannel()</text>
  <text x="180" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">setCoverMode()</text>
  <text x="180" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">setCoverArea()</text>
  <text x="180" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">setCoverRange()</text>
  
  <rect x="300" y="580" width="200" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="400" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">遮盖模式</text>
  <text x="400" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">CoverMode枚举</text>
  <text x="400" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">内部高斯模糊(0)</text>
  <text x="400" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">外部高斯模糊(1)</text>
  <text x="400" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">内部区域置黑(2)</text>
  <text x="400" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">外部区域置黑(3)</text>
  
  <rect x="520" y="580" width="200" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="620" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">颜色通道</text>
  <text x="620" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">CoverChannel枚举</text>
  <text x="620" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">R(1) G(2) GR(3)</text>
  <text x="620" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">B(4) BR(5) BG(6)</text>
  <text x="620" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">BGR(7)</text>
  <text x="620" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">针对不同弱视类型</text>
  
  <rect x="740" y="580" width="200" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="840" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">遮盖幅度</text>
  <text x="840" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">CoverRange枚举</text>
  <text x="840" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">轻度(20f)</text>
  <text x="840" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">中度(50f)</text>
  <text x="840" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">高度(100f)</text>
  <text x="840" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">完全(-1f)</text>
  
  <rect x="960" y="580" width="200" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1060" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">GPU渲染</text>
  <text x="1060" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">AppliedManager</text>
  <text x="1060" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">setBlurParams()</text>
  <text x="1060" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">PqBlur算法</text>
  <text x="1060" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">IPQ_BLR引擎</text>
  <text x="1060" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">OpenGL着色器</text>
  
  <rect x="1180" y="580" width="140" height="120" rx="10" ry="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
  <text x="1250" y="605" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">数据存储</text>
  <text x="1250" y="625" text-anchor="middle" font-family="Arial" font-size="12" fill="white">MaskPreference</text>
  <text x="1250" y="640" text-anchor="middle" font-family="Arial" font-size="12" fill="white">MMKV缓存</text>
  <text x="1250" y="655" text-anchor="middle" font-family="Arial" font-size="12" fill="white">参数持久化</text>
  <text x="1250" y="670" text-anchor="middle" font-family="Arial" font-size="12" fill="white">配置恢复</text>
  <text x="1250" y="685" text-anchor="middle" font-family="Arial" font-size="12" fill="white">默认值管理</text>
  
  <!-- 连接线 -->
  <!-- Activity到WebView -->
  <line x1="350" y1="230" x2="350" y2="280" stroke="#2C3E50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Activity到FlipBeat -->
  <line x1="1050" y1="230" x2="1050" y2="280" stroke="#2C3E50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- WebView到数字遮盖 -->
  <line x1="350" y1="480" x2="350" y2="530" stroke="#2C3E50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- FlipBeat到数字遮盖 -->
  <line x1="1050" y1="480" x2="1050" y2="530" stroke="#2C3E50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2C3E50"/>
    </marker>
  </defs>
  
  <!-- 说明文字 -->
  <rect x="50" y="780" width="1300" height="180" rx="15" ry="15" fill="#ECF0F1" stroke="#BDC3C7" stroke-width="2"/>
  <text x="700" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2C3E50">
    进院版数字遮盖系统架构说明
  </text>
  
  <text x="70" y="840" font-family="Arial" font-size="14" fill="#2C3E50">
    <tspan x="70" dy="0">🏥 <tspan font-weight="bold">InspectionCenterActivity</tspan>: 进院版检查中心主界面，负责整体流程控制和用户交互</tspan>
    <tspan x="70" dy="20">🌐 <tspan font-weight="bold">InspectionCenterWebView</tspan>: Web容器，加载检查页面，提供JS接口与原生代码通信</tspan>
    <tspan x="70" dy="20">🎮 <tspan font-weight="bold">FlipBeat翻转拍</tspan>: 蓝牙设备管理，控制物理翻转拍进行数字遮盖切换</tspan>
    <tspan x="70" dy="20">👁️ <tspan font-weight="bold">数字遮盖疗法</tspan>: 核心治疗功能，通过GPU实时渲染实现精准的视觉遮盖效果</tspan>
    <tspan x="70" dy="20">⚙️ <tspan font-weight="bold">参数配置</tspan>: 支持多种遮盖模式、颜色通道、强度调节，适应不同患者需求</tspan>
  </text>
</svg>
