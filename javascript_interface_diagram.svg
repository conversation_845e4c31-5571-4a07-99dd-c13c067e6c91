<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="webGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="androidGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="interfaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e1f5fe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81d4fa;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#00000030"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
    
    <marker id="arrowheadBlue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196F3"/>
    </marker>
    
    <marker id="arrowheadGreen" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    JavaScript接口桥梁架构图 - Web与Android原生交互
  </text>
  
  <!-- Web端 (左侧) -->
  <g>
    <rect x="50" y="100" width="300" height="800" rx="15" fill="url(#webGradient)" filter="url(#shadow)"/>
    <text x="200" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#E65100">
      Web端 (HTML/JavaScript)
    </text>
    
    <!-- Web功能模块 -->
    <rect x="70" y="160" width="260" height="60" rx="8" fill="#ffb74d"/>
    <text x="200" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#BF360C">
      检查中心Web页面
    </text>
    <text x="200" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#BF360C">
      眼动检查、视力测试、报告生成
    </text>
    
    <rect x="70" y="240" width="260" height="60" rx="8" fill="#ffb74d"/>
    <text x="200" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#BF360C">
      训练中心Web页面
    </text>
    <text x="200" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#BF360C">
      眼动训练游戏、康复训练
    </text>
    
    <!-- JavaScript调用 -->
    <text x="200" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#E65100">
      JavaScript调用原生功能：
    </text>
    
    <rect x="80" y="350" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="367" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Inspection.postMessage(JSON)
    </text>
    
    <rect x="80" y="385" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="402" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Train.calibration()
    </text>
    
    <rect x="80" y="420" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="437" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Train.startGazeTracker()
    </text>
    
    <rect x="80" y="455" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="472" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Train.getDeviceSn()
    </text>
    
    <rect x="80" y="490" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="507" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Inspection.finish()
    </text>
    
    <rect x="80" y="525" width="240" height="25" rx="5" fill="#ff8a65"/>
    <text x="200" y="542" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.Inspection.printPage()
    </text>
    
    <!-- 接收原生数据 -->
    <text x="200" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#E65100">
      接收原生数据：
    </text>
    
    <rect x="80" y="600" width="240" height="25" rx="5" fill="#ffab91"/>
    <text x="200" y="617" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      window.sendDataToWebView(data)
    </text>
    
    <rect x="80" y="635" width="240" height="25" rx="5" fill="#ffab91"/>
    <text x="200" y="652" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      翻转拍状态: {"type":"fliperStatus"}
    </text>
    
    <rect x="80" y="670" width="240" height="25" rx="5" fill="#ffab91"/>
    <text x="200" y="687" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#BF360C">
      返回事件: {"type":"goBack"}
    </text>
  </g>
  
  <!-- JavaScript接口桥梁 (中间) -->
  <g>
    <rect x="400" y="200" width="300" height="600" rx="15" fill="url(#interfaceGradient)" filter="url(#shadow)"/>
    <text x="550" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#0277BD">
      JavaScript接口桥梁
    </text>
    
    <!-- Inspection接口 -->
    <rect x="420" y="260" width="260" height="150" rx="8" fill="#4fc3f7"/>
    <text x="550" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#01579B">
      Inspection接口
    </text>
    <text x="550" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#01579B">
      @JavascriptInterface 注解方法
    </text>
    
    <text x="430" y="325" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • postMessage(msg) - 处理翻转拍消息
    </text>
    <text x="430" y="340" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • finish() - 关闭页面
    </text>
    <text x="430" y="355" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • home(isHome) - 设置首页状态
    </text>
    <text x="430" y="370" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • printPage() - 打印页面
    </text>
    <text x="430" y="385" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • sendDataToWebView() - 发送数据到Web
    </text>
    <text x="430" y="400" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • backPressed() - 返回按键处理
    </text>
    
    <!-- Train接口 -->
    <rect x="420" y="430" width="260" height="180" rx="8" fill="#4fc3f7"/>
    <text x="550" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#01579B">
      Train接口
    </text>
    <text x="550" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#01579B">
      @JavascriptInterface 注解方法
    </text>
    
    <text x="430" y="495" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • postMessage(msg) - 处理训练消息
    </text>
    <text x="430" y="510" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • calibration() - 启动眼动校准
    </text>
    <text x="430" y="525" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • startGazeTracker() - 启动眼动追踪
    </text>
    <text x="430" y="540" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • stopGazeTracker() - 停止眼动追踪
    </text>
    <text x="430" y="555" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • getDeviceSn() - 获取设备序列号
    </text>
    <text x="430" y="570" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • finish() - 关闭页面
    </text>
    <text x="430" y="585" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      • home(isHome) - 设置首页状态
    </text>
    
    <!-- 接口注册 -->
    <rect x="420" y="630" width="260" height="60" rx="8" fill="#29b6f6"/>
    <text x="550" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#01579B">
      接口注册到WebView
    </text>
    <text x="550" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      addJavascriptInterface(object, "Inspection")
    </text>
    <text x="550" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      addJavascriptInterface(object, "Train")
    </text>
  </g>
  
  <!-- Android原生端 (右侧) -->
  <g>
    <rect x="750" y="100" width="300" height="800" rx="15" fill="url(#androidGradient)" filter="url(#shadow)"/>
    <text x="900" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2E7D32">
      Android原生端
    </text>
    
    <!-- Activity -->
    <rect x="770" y="160" width="260" height="60" rx="8" fill="#81c784"/>
    <text x="900" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1B5E20">
      InspectionCenterActivity
    </text>
    <text x="900" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1B5E20">
      检查中心Activity
    </text>
    
    <rect x="770" y="240" width="260" height="60" rx="8" fill="#81c784"/>
    <text x="900" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1B5E20">
      TrainCenterActivity
    </text>
    <text x="900" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1B5E20">
      训练中心Activity
    </text>
    
    <!-- 硬件控制 -->
    <text x="900" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      硬件设备控制：
    </text>
    
    <rect x="780" y="350" width="240" height="25" rx="5" fill="#a5d6a7"/>
    <text x="900" y="367" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      FlipBeatManager - 翻转拍控制
    </text>
    
    <rect x="780" y="385" width="240" height="25" rx="5" fill="#a5d6a7"/>
    <text x="900" y="402" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      GazeTracker - 眼动追踪服务
    </text>
    
    <rect x="780" y="420" width="240" height="25" rx="5" fill="#a5d6a7"/>
    <text x="900" y="437" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      DeviceManager - 设备信息管理
    </text>
    
    <rect x="780" y="455" width="240" height="25" rx="5" fill="#a5d6a7"/>
    <text x="900" y="472" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      PrintManager - 打印服务
    </text>
    
    <!-- 系统功能 -->
    <text x="900" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      系统功能调用：
    </text>
    
    <rect x="780" y="530" width="240" height="25" rx="5" fill="#c8e6c9"/>
    <text x="900" y="547" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      Camera API - 摄像头控制
    </text>
    
    <rect x="780" y="565" width="240" height="25" rx="5" fill="#c8e6c9"/>
    <text x="900" y="582" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      Bluetooth - 蓝牙设备连接
    </text>
    
    <rect x="780" y="600" width="240" height="25" rx="5" fill="#c8e6c9"/>
    <text x="900" y="617" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      File System - 文件存储
    </text>
    
    <rect x="780" y="635" width="240" height="25" rx="5" fill="#c8e6c9"/>
    <text x="900" y="652" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      Network - 网络请求
    </text>
    
    <!-- 数据回调 -->
    <text x="900" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      数据回调到Web：
    </text>
    
    <rect x="780" y="710" width="240" height="25" rx="5" fill="#dcedc8"/>
    <text x="900" y="727" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      设备连接状态变化
    </text>
    
    <rect x="780" y="745" width="240" height="25" rx="5" fill="#dcedc8"/>
    <text x="900" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      眼动追踪数据实时更新
    </text>
    
    <rect x="780" y="780" width="240" height="25" rx="5" fill="#dcedc8"/>
    <text x="900" y="797" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1B5E20">
      校准结果反馈
    </text>
  </g>
  
  <!-- 连接箭头 -->
  <!-- Web到接口 -->
  <line x1="350" y1="400" x2="400" y2="400" stroke="#2196F3" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
  <text x="375" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">
    调用
  </text>
  
  <!-- 接口到Android -->
  <line x1="700" y1="400" x2="750" y2="400" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowheadGreen)"/>
  <text x="725" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#4CAF50">
    执行
  </text>
  
  <!-- Android到接口 (回调) -->
  <line x1="750" y1="450" x2="700" y2="450" stroke="#FF9800" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="725" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FF9800">
    回调
  </text>
  
  <!-- 接口到Web (回调) -->
  <line x1="400" y1="450" x2="350" y2="450" stroke="#FF9800" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="375" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FF9800">
    通知
  </text>
  
  <!-- 底部说明 -->
  <rect x="100" y="920" width="1200" height="60" rx="10" fill="#ffffff" stroke="#ddd" stroke-width="1"/>
  <text x="700" y="945" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">
    JavaScript接口作用总结
  </text>
  <text x="700" y="965" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    通过@JavascriptInterface注解，Web页面可以直接调用Android原生功能，实现硬件控制、系统服务访问和数据交互
  </text>
</svg>
