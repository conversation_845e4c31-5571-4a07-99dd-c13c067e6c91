<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变和样式 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="kotlinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7F52FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C711E1;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="androidGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3DDC84;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#07C160;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="architectureGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F7931E;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#00000030"/>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#34495e"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>

  <!-- 主标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
    MIT DD GazeTracker Architecture
  </text>
  <text x="700" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#7f8c8d">
    Kotlin Features & Android Components Integration
  </text>

  <!-- Kotlin特性区域 -->
  <g id="kotlin-features">
    <!-- 背景框 -->
    <rect x="50" y="90" width="650" height="380" fill="url(#kotlinGradient)" stroke="#7F52FF" stroke-width="3" rx="15" filter="url(#shadow)"/>
    <text x="375" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="white">
      🔷 Kotlin Language Features
    </text>

    <!-- Data Classes -->
    <rect x="80" y="150" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="150" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">📦 Data Classes</text>
    <text x="150" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">@Parcelize</text>
    <text x="150" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Patient, Train</text>
    <text x="150" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">EMPatient</text>

    <!-- Coroutines -->
    <rect x="240" y="150" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="310" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">⚡ Coroutines</text>
    <text x="310" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">viewModelScope</text>
    <text x="310" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">launch, async</text>
    <text x="310" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">suspend functions</text>

    <!-- Enum Classes -->
    <rect x="400" y="150" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="470" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🔢 Enum Classes</text>
    <text x="470" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Preferences</text>
    <text x="470" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">INameSpace</text>
    <text x="470" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Type safety</text>

    <!-- Extension Functions -->
    <rect x="560" y="150" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="630" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🔧 Extension</text>
    <text x="630" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">Functions</text>
    <text x="630" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">deepCopy()</text>
    <text x="630" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Custom methods</text>

    <!-- Null Safety -->
    <rect x="80" y="240" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="150" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🛡️ Null Safety</text>
    <text x="150" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">?., ?:, !!</text>
    <text x="150" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Safe calls</text>
    <text x="150" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">NPE prevention</text>

    <!-- Lambda & HOF -->
    <rect x="240" y="240" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="310" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🔗 Lambda &</text>
    <text x="310" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">HOF</text>
    <text x="310" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">onItemClick</text>
    <text x="310" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Functional style</text>

    <!-- Companion Objects -->
    <rect x="400" y="240" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="470" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">👥 Companion</text>
    <text x="470" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">Objects</text>
    <text x="470" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Constants</text>
    <text x="470" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Static members</text>

    <!-- When Expressions -->
    <rect x="560" y="240" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="630" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🔀 When</text>
    <text x="630" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">Expressions</text>
    <text x="630" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Pattern match</text>
    <text x="630" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Smart casting</text>

    <!-- Sealed Classes -->
    <rect x="80" y="330" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="150" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🔒 Sealed Classes</text>
    <text x="150" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">State management</text>
    <text x="150" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Type hierarchy</text>
    <text x="150" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Exhaustive when</text>

    <!-- Property Delegation -->
    <rect x="240" y="330" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="310" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🎯 Property</text>
    <text x="310" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">Delegation</text>
    <text x="310" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">by viewModels</text>
    <text x="310" y="396" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">by lazy</text>

    <!-- Scope Functions -->
    <rect x="400" y="330" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="470" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">🎪 Scope Functions</text>
    <text x="470" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">apply, let, run</text>
    <text x="470" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">also, with</text>
    <text x="470" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Builder pattern</text>

    <!-- Inline Functions -->
    <rect x="560" y="330" width="140" height="70" fill="#ffffff" stroke="#7F52FF" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="630" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#7F52FF">⚡ Inline Functions</text>
    <text x="630" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Performance</text>
    <text x="630" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">No overhead</text>
    <text x="630" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Reified generics</text>
  </g>

  <!-- Android组件区域 -->
  <g id="android-components">
    <!-- 背景框 -->
    <rect x="720" y="90" width="650" height="380" fill="url(#androidGradient)" stroke="#3DDC84" stroke-width="3" rx="15" filter="url(#shadow)"/>
    <text x="1045" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="white">
      🤖 Android Framework Components
    </text>

    <!-- Activity -->
    <rect x="750" y="150" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="820" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">📱 Activity</text>
    <text x="820" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">LauncherActivity</text>
    <text x="820" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">TrainWebActivity</text>
    <text x="820" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">UI Entry Point</text>

    <!-- Fragment -->
    <rect x="910" y="150" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="980" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">🧩 Fragment</text>
    <text x="980" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">HomeMainFragment</text>
    <text x="980" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">MyopiaTrainFragment</text>
    <text x="980" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Modular UI</text>

    <!-- Service -->
    <rect x="1070" y="150" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1140" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">⚙️ Service</text>
    <text x="1140" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">GazeTrackService</text>
    <text x="1140" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">DesktopService</text>
    <text x="1140" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Background Tasks</text>

    <!-- ViewModel -->
    <rect x="1230" y="150" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1300" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">🏗️ ViewModel</text>
    <text x="1300" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">ROIDetectionVM</text>
    <text x="1300" y="203" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">TreatmentVM</text>
    <text x="1300" y="216" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Business Logic</text>

    <!-- BroadcastReceiver -->
    <rect x="750" y="240" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="820" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">📡 Broadcast</text>
    <text x="820" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">Receiver</text>
    <text x="820" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">BootReceiver</text>
    <text x="820" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">System Events</text>

    <!-- RecyclerView -->
    <rect x="910" y="240" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="980" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">📋 RecyclerView</text>
    <text x="980" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">Adapter</text>
    <text x="980" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">PrinterAdapter</text>
    <text x="980" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">List Management</text>

    <!-- LiveData -->
    <rect x="1070" y="240" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1140" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">📊 LiveData</text>
    <text x="1140" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">accountInfoLiveData</text>
    <text x="1140" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Observer pattern</text>
    <text x="1140" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Reactive UI</text>

    <!-- WorkManager -->
    <rect x="1230" y="240" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1300" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">⏰ WorkManager</text>
    <text x="1300" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Background tasks</text>
    <text x="1300" y="293" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">BootStartWorker</text>
    <text x="1300" y="306" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Scheduled Jobs</text>

    <!-- CameraX -->
    <rect x="750" y="330" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="820" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">📷 CameraX</text>
    <text x="820" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Camera operations</text>
    <text x="820" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Image capture</text>
    <text x="820" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Gaze tracking</text>

    <!-- Material Design -->
    <rect x="910" y="330" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="980" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">🎨 Material</text>
    <text x="980" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">Design</text>
    <text x="980" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">UI Components</text>
    <text x="980" y="396" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Modern UI</text>

    <!-- Retrofit -->
    <rect x="1070" y="330" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1140" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">🌐 Retrofit</text>
    <text x="1140" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">HTTP Client</text>
    <text x="1140" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">API Services</text>
    <text x="1140" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Network Layer</text>

    <!-- Navigation -->
    <rect x="1230" y="330" width="140" height="70" fill="#ffffff" stroke="#3DDC84" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="1300" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#3DDC84">🧭 Navigation</text>
    <text x="1300" y="373" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Intent navigation</text>
    <text x="1300" y="386" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Activity flow</text>
    <text x="1300" y="399" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">Deep linking</text>
  </g>

  <!-- 架构层次 -->
  <g id="architecture-layers">
    <!-- 背景框 -->
    <rect x="50" y="500" width="1320" height="450" fill="url(#architectureGradient)" stroke="#FF6B35" stroke-width="3" rx="15" filter="url(#shadow)"/>
    <text x="710" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="white">
      🏗️ Application Architecture Layers
    </text>

    <!-- UI Layer -->
    <rect x="80" y="560" width="1260" height="100" fill="#ffffff" stroke="#FF6B35" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="110" y="585" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#FF6B35">📱 Presentation Layer (UI)</text>
    <text x="110" y="605" font-family="Arial, sans-serif" font-size="12" fill="#666">Activities: LauncherActivity, TrainWebActivity, EyeMovementEvaluateActivity</text>
    <text x="110" y="620" font-family="Arial, sans-serif" font-size="12" fill="#666">Fragments: HomeMainFragment, MyopiaTrainFragment, ROIDetectionFragment</text>
    <text x="110" y="635" font-family="Arial, sans-serif" font-size="12" fill="#666">Adapters: PrinterAdapter, TrainListAdapter, HospitalModuleAdapter</text>
    <text x="110" y="650" font-family="Arial, sans-serif" font-size="12" fill="#666">Material Design Components, Custom Views, CameraX Preview</text>

    <!-- Business Layer -->
    <rect x="80" y="680" width="1260" height="100" fill="#ffffff" stroke="#FF6B35" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="110" y="705" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#FF6B35">⚙️ Domain Layer (Business Logic)</text>
    <text x="110" y="725" font-family="Arial, sans-serif" font-size="12" fill="#666">ViewModels: ROIDetectionViewModel, TreatmentViewModel, UserViewModel</text>
    <text x="110" y="740" font-family="Arial, sans-serif" font-size="12" fill="#666">Repositories: ROIDetectionRepository, GazeStabilityRepository</text>
    <text x="110" y="755" font-family="Arial, sans-serif" font-size="12" fill="#666">Use Cases: Gaze Tracking, Eye Movement Analysis, Medical Training</text>
    <text x="110" y="770" font-family="Arial, sans-serif" font-size="12" fill="#666">Coroutines, LiveData, StateFlow, Business Rules</text>

    <!-- Data Layer -->
    <rect x="80" y="800" width="1260" height="100" fill="#ffffff" stroke="#FF6B35" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="110" y="825" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#FF6B35">💾 Data Layer</text>
    <text x="110" y="845" font-family="Arial, sans-serif" font-size="12" fill="#666">Network: Retrofit, OkHttp, API Services (EMPatientApiService, HomeApiService)</text>
    <text x="110" y="860" font-family="Arial, sans-serif" font-size="12" fill="#666">Local Storage: MMKV, Preferences, File System</text>
    <text x="110" y="875" font-family="Arial, sans-serif" font-size="12" fill="#666">Services: GazeTrackService, DesktopService, Background Processing</text>
    <text x="110" y="890" font-family="Arial, sans-serif" font-size="12" fill="#666">External Libraries: OpenCV, NCNN, Behavior Guidance, Media Processing</text>
  </g>

  <!-- 连接线和数据流 -->
  <g id="connections" stroke="#34495e" stroke-width="3" fill="none">
    <!-- Kotlin to Android 连接 -->
    <path d="M 700 285 L 720 285" marker-end="url(#arrowhead)"/>
    <text x="710" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#34495e">Integration</text>

    <!-- 层次间连接 -->
    <path d="M 710 660 L 710 680" marker-end="url(#arrowhead)"/>
    <path d="M 710 780 L 710 800" marker-end="url(#arrowhead)"/>

    <!-- 数据流箭头 -->
    <path d="M 200 470 Q 350 450 500 470" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="350" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#3498db">Data Flow</text>
  </g>

  <!-- 特性说明框 -->
  <g id="feature-highlights">
    <!-- Kotlin特色 -->
    <rect x="50" y="970" width="300" height="25" fill="#7F52FF" stroke="#7F52FF" stroke-width="1" rx="5"/>
    <text x="60" y="987" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      🔷 Kotlin: Modern, Concise, Interoperable
    </text>

    <!-- Android特色 -->
    <rect x="370" y="970" width="300" height="25" fill="#3DDC84" stroke="#3DDC84" stroke-width="1" rx="5"/>
    <text x="380" y="987" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      🤖 Android: Rich Framework, Lifecycle-aware
    </text>

    <!-- 架构特色 -->
    <rect x="690" y="970" width="300" height="25" fill="#FF6B35" stroke="#FF6B35" stroke-width="1" rx="5"/>
    <text x="700" y="987" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      🏗️ Architecture: Clean, Scalable, Testable
    </text>

    <!-- 项目特色 -->
    <rect x="1010" y="970" width="340" height="25" fill="#9b59b6" stroke="#9b59b6" stroke-width="1" rx="5"/>
    <text x="1020" y="987" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      👁️ MIT DD GazeTracker: Medical Eye Tracking Solution
    </text>
  </g>
</svg>
