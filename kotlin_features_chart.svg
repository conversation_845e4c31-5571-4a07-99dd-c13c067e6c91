<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    MIT DD GazeTracker 项目中的 Kotlin 特性使用情况
  </text>
  
  <!-- 图例 -->
  <g id="legend">
    <rect x="50" y="70" width="20" height="15" fill="#3498db"/>
    <text x="80" y="82" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">高频使用</text>
    
    <rect x="180" y="70" width="20" height="15" fill="#2ecc71"/>
    <text x="210" y="82" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">中频使用</text>
    
    <rect x="310" y="70" width="20" height="15" fill="#f39c12"/>
    <text x="340" y="82" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">低频使用</text>
  </g>
  
  <!-- 主要特性区域 -->
  <g id="main-features">
    <!-- Data Classes -->
    <rect x="100" y="120" width="200" height="80" rx="10" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
    <text x="200" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Data Classes</text>
    <text x="200" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">@Parcelize</text>
    <text x="200" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Bean类定义</text>
    
    <!-- Null Safety -->
    <rect x="320" y="120" width="200" height="80" rx="10" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
    <text x="420" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Null Safety</text>
    <text x="420" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">?. ?: !!</text>
    <text x="420" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">空安全操作</text>
    
    <!-- Coroutines -->
    <rect x="540" y="120" width="200" height="80" rx="10" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
    <text x="640" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Coroutines</text>
    <text x="640" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">suspend fun</text>
    <text x="640" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">viewModelScope</text>
    
    <!-- Scope Functions -->
    <rect x="760" y="120" width="200" height="80" rx="10" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
    <text x="860" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Scope Functions</text>
    <text x="860" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">.let .also .apply</text>
    <text x="860" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">作用域函数</text>
  </g>
  
  <!-- 中频特性区域 -->
  <g id="medium-features">
    <!-- When Expressions -->
    <rect x="100" y="230" width="180" height="70" rx="8" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
    <text x="190" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white">When表达式</text>
    <text x="190" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">模式匹配</text>
    <text x="190" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">替代switch</text>
    
    <!-- Property Delegates -->
    <rect x="300" y="230" width="180" height="70" rx="8" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
    <text x="390" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white">Property Delegates</text>
    <text x="390" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">by lazy</text>
    <text x="390" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">延迟初始化</text>
    
    <!-- Companion Objects -->
    <rect x="500" y="230" width="180" height="70" rx="8" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
    <text x="590" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white">Companion Objects</text>
    <text x="590" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">静态成员</text>
    <text x="590" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">常量定义</text>
    
    <!-- Enum Classes -->
    <rect x="700" y="230" width="180" height="70" rx="8" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
    <text x="790" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white">Enum Classes</text>
    <text x="790" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">枚举类</text>
    <text x="790" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">类型安全</text>
  </g>
  
  <!-- 低频特性区域 -->
  <g id="low-features">
    <!-- Annotations -->
    <rect x="150" y="330" width="150" height="60" rx="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
    <text x="225" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Annotations</text>
    <text x="225" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">@OptIn</text>
    
    <!-- Extension Functions -->
    <rect x="320" y="330" width="150" height="60" rx="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
    <text x="395" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Extension Func</text>
    <text x="395" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">扩展函数</text>
    
    <!-- Higher-Order Functions -->
    <rect x="490" y="330" width="150" height="60" rx="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
    <text x="565" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Higher-Order</text>
    <text x="565" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">高阶函数</text>
    
    <!-- Inline Functions -->
    <rect x="660" y="330" width="150" height="60" rx="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
    <text x="735" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Inline Functions</text>
    <text x="735" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">内联函数</text>
  </g>
  
  <!-- 使用频率统计图 -->
  <g id="usage-chart">
    <text x="100" y="440" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">使用频率统计</text>
    
    <!-- 柱状图 -->
    <rect x="100" y="460" width="60" height="150" fill="#3498db"/>
    <text x="130" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Data Classes</text>
    <text x="130" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">95%</text>
    
    <rect x="180" y="480" width="60" height="130" fill="#3498db"/>
    <text x="210" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Null Safety</text>
    <text x="210" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">90%</text>
    
    <rect x="260" y="500" width="60" height="110" fill="#3498db"/>
    <text x="290" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Coroutines</text>
    <text x="290" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">85%</text>
    
    <rect x="340" y="520" width="60" height="90" fill="#2ecc71"/>
    <text x="370" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Scope Func</text>
    <text x="370" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">75%</text>
    
    <rect x="420" y="540" width="60" height="70" fill="#2ecc71"/>
    <text x="450" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">When</text>
    <text x="450" y="530" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">65%</text>
    
    <rect x="500" y="560" width="60" height="50" fill="#2ecc71"/>
    <text x="530" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Delegates</text>
    <text x="530" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">55%</text>
    
    <rect x="580" y="580" width="60" height="30" fill="#f39c12"/>
    <text x="610" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">Extensions</text>
    <text x="610" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">35%</text>
  </g>
  
  <!-- 项目特点说明 -->
  <g id="project-info">
    <rect x="700" y="460" width="450" height="200" rx="10" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
    <text x="925" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">项目Kotlin特性亮点</text>
    
    <text x="720" y="510" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 大量使用数据类配合@Parcelize进行数据传递</text>
    <text x="720" y="530" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 全面采用空安全操作符，提高代码健壮性</text>
    <text x="720" y="550" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 协程广泛应用于网络请求和异步处理</text>
    <text x="720" y="570" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 作用域函数简化代码逻辑，提高可读性</text>
    <text x="720" y="590" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• When表达式替代传统switch，更加灵活</text>
    <text x="720" y="610" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 属性委托实现延迟初始化，优化性能</text>
    <text x="720" y="630" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 枚举类提供类型安全的常量定义</text>
    <text x="720" y="650" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 现代化Android开发最佳实践</text>
  </g>
  
  <!-- 底部信息 -->
  <text x="600" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#7f8c8d">
    基于MIT DD GazeTracker项目代码分析 - Kotlin语言特性使用情况统计
  </text>
</svg>
