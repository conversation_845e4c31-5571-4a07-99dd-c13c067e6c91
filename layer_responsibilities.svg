<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .responsibility { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .ui-layer { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; }
      .view-layer { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .viewmodel-layer { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .repository-layer { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 3; }
      .network-layer { fill: #ffebee; stroke: #f44336; stroke-width: 3; }
      .service-layer { fill: #fce4ec; stroke: #e91e63; stroke-width: 3; }
      .collaboration-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#collab); }
      .data-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#data); }
    </style>
    <marker id="collab" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="data" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">眼球运动评估模块各层职责与协作关系</text>
  <text x="700" y="50" text-anchor="middle" class="subtitle">分层架构设计原则与实现细节</text>

  <!-- 第一层：UI展示层 -->
  <rect x="50" y="80" width="1300" height="120" class="ui-layer" rx="10"/>
  <rect x="60" y="90" width="120" height="35" fill="#4caf50" rx="5"/>
  <text x="120" y="112" text-anchor="middle" class="layer-title">UI展示层</text>
  
  <!-- Activity职责 -->
  <rect x="200" y="100" width="250" height="80" class="ui-layer" rx="5"/>
  <text x="325" y="120" text-anchor="middle" class="subtitle">Activity - 容器管理者</text>
  <text x="210" y="140" class="responsibility">🎭 Fragment生命周期管理</text>
  <text x="210" y="155" class="responsibility">🔄 页面导航和路由控制</text>
  <text x="210" y="170" class="responsibility">📱 系统级事件处理</text>

  <!-- Fragment职责 -->
  <rect x="470" y="100" width="250" height="80" class="ui-layer" rx="5"/>
  <text x="595" y="120" text-anchor="middle" class="subtitle">Fragment - 业务实现者</text>
  <text x="480" y="140" class="responsibility">🎮 用户交互响应</text>
  <text x="480" y="155" class="responsibility">📊 数据收集和验证</text>
  <text x="480" y="170" class="responsibility">🔗 跨组件数据传递</text>

  <!-- ResultActivity职责 -->
  <rect x="740" y="100" width="250" height="80" class="ui-layer" rx="5"/>
  <text x="865" y="120" text-anchor="middle" class="subtitle">ResultActivity - 结果展示者</text>
  <text x="750" y="140" class="responsibility">👁️ 评估结果可视化</text>
  <text x="750" y="155" class="responsibility">📡 LiveData状态观察</text>
  <text x="750" y="170" class="responsibility">💾 结果保存和分享</text>

  <!-- 协作说明 -->
  <rect x="1010" y="100" width="320" height="80" class="ui-layer" rx="5"/>
  <text x="1170" y="120" text-anchor="middle" class="subtitle">UI层协作机制</text>
  <text x="1020" y="140" class="responsibility">• Activity通过FragmentManager管理Fragment切换</text>
  <text x="1020" y="155" class="responsibility">• Fragment通过LiveEventBus传递数据到ResultActivity</text>
  <text x="1020" y="170" class="responsibility">• 统一的生命周期管理和内存优化</text>

  <!-- 第二层：自定义View层 -->
  <rect x="50" y="220" width="1300" height="100" class="view-layer" rx="10"/>
  <rect x="60" y="230" width="140" height="35" fill="#ff9800" rx="5"/>
  <text x="130" y="252" text-anchor="middle" class="layer-title">自定义View层</text>

  <!-- Canvas绘制职责 -->
  <rect x="220" y="240" width="280" height="60" class="view-layer" rx="5"/>
  <text x="360" y="260" text-anchor="middle" class="subtitle">Canvas绘制组件</text>
  <text x="230" y="280" class="responsibility">🎨 复杂图形绘制 (轨迹、圆点、文字)</text>
  <text x="230" y="295" class="responsibility">📐 坐标系转换 (相对坐标 → 屏幕坐标)</text>

  <!-- 交互处理职责 -->
  <rect x="520" y="240" width="280" height="60" class="view-layer" rx="5"/>
  <text x="660" y="260" text-anchor="middle" class="subtitle">交互处理组件</text>
  <text x="530" y="280" class="responsibility">🎯 目标点动态显示和定位</text>
  <text x="530" y="295" class="responsibility">🔄 实时数据更新和重绘触发</text>

  <!-- 图片生成职责 -->
  <rect x="820" y="240" width="280" height="60" class="view-layer" rx="5"/>
  <text x="960" y="260" text-anchor="middle" class="subtitle">图片生成组件</text>
  <text x="830" y="280" class="responsibility">💾 结果图片生成 (drawToBitmap)</text>
  <text x="830" y="295" class="responsibility">📤 图片压缩和格式转换</text>

  <!-- 第三层：ViewModel层 -->
  <rect x="50" y="340" width="1300" height="120" class="viewmodel-layer" rx="10"/>
  <rect x="60" y="350" width="120" height="35" fill="#2196f3" rx="5"/>
  <text x="120" y="372" text-anchor="middle" class="layer-title">ViewModel层</text>

  <!-- 数据管理职责 -->
  <rect x="200" y="360" width="250" height="80" class="viewmodel-layer" rx="5"/>
  <text x="325" y="380" text-anchor="middle" class="subtitle">数据状态管理</text>
  <text x="210" y="400" class="responsibility">📊 UI相关数据状态维护</text>
  <text x="210" y="415" class="responsibility">🔄 配置变更时数据保持</text>
  <text x="210" y="430" class="responsibility">📡 LiveData数据发布</text>

  <!-- 业务逻辑职责 -->
  <rect x="470" y="360" width="250" height="80" class="viewmodel-layer" rx="5"/>
  <text x="595" y="380" text-anchor="middle" class="subtitle">业务逻辑处理</text>
  <text x="480" y="400" class="responsibility">🧠 复杂业务规则实现</text>
  <text x="480" y="415" class="responsibility">🔄 数据格式转换和验证</text>
  <text x="480" y="430" class="responsibility">📈 评估结果计算和分析</text>

  <!-- 协程管理职责 -->
  <rect x="740" y="360" width="250" height="80" class="viewmodel-layer" rx="5"/>
  <text x="865" y="380" text-anchor="middle" class="subtitle">协程生命周期管理</text>
  <text x="750" y="400" class="responsibility">🧵 viewModelScope自动管理</text>
  <text x="750" y="415" class="responsibility">⚡ 异步操作线程安全</text>
  <text x="750" y="430" class="responsibility">🛡️ 内存泄漏防护</text>

  <!-- Repository调用职责 -->
  <rect x="1010" y="360" width="320" height="80" class="viewmodel-layer" rx="5"/>
  <text x="1170" y="380" text-anchor="middle" class="subtitle">Repository调用协调</text>
  <text x="1020" y="400" class="responsibility">🔗 Repository层接口调用</text>
  <text x="1020" y="415" class="responsibility">📊 多数据源结果聚合</text>
  <text x="1020" y="430" class="responsibility">⚠️ 统一错误处理和状态管理</text>

  <!-- 第四层：Repository层 -->
  <rect x="50" y="480" width="1300" height="100" class="repository-layer" rx="10"/>
  <rect x="60" y="490" width="120" height="35" fill="#9c27b0" rx="5"/>
  <text x="120" y="512" text-anchor="middle" class="layer-title">Repository层</text>

  <!-- 数据访问职责 -->
  <rect x="200" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="325" y="520" text-anchor="middle" class="subtitle">数据访问抽象</text>
  <text x="210" y="540" class="responsibility">🌐 网络、本地、缓存数据源统一</text>
  <text x="210" y="555" class="responsibility">🔄 数据源切换和降级策略</text>

  <!-- API封装职责 -->
  <rect x="470" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="595" y="520" text-anchor="middle" class="subtitle">API调用封装</text>
  <text x="480" y="540" class="responsibility">📝 请求参数构建和验证</text>
  <text x="480" y="555" class="responsibility">🔄 响应数据解析和转换</text>

  <!-- 缓存管理职责 -->
  <rect x="740" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="865" y="520" text-anchor="middle" class="subtitle">缓存策略管理</text>
  <text x="750" y="540" class="responsibility">💾 本地数据缓存和同步</text>
  <text x="750" y="555" class="responsibility">⏰ 缓存过期和更新策略</text>

  <!-- 异常处理职责 -->
  <rect x="1010" y="500" width="320" height="60" class="repository-layer" rx="5"/>
  <text x="1170" y="520" text-anchor="middle" class="subtitle">异常处理和重试</text>
  <text x="1020" y="540" class="responsibility">🚨 网络异常捕获和分类处理</text>
  <text x="1020" y="555" class="responsibility">🔄 指数退避重试机制</text>

  <!-- 第五层：Network层 -->
  <rect x="50" y="600" width="1300" height="100" class="network-layer" rx="10"/>
  <rect x="60" y="610" width="120" height="35" fill="#f44336" rx="5"/>
  <text x="120" y="632" text-anchor="middle" class="layer-title">Network层</text>

  <!-- API定义职责 -->
  <rect x="200" y="620" width="250" height="60" class="network-layer" rx="5"/>
  <text x="325" y="640" text-anchor="middle" class="subtitle">API接口定义</text>
  <text x="210" y="660" class="responsibility">🔌 RESTful API接口声明</text>
  <text x="210" y="675" class="responsibility">📋 请求响应数据模型定义</text>

  <!-- 网络配置职责 -->
  <rect x="470" y="620" width="250" height="60" class="network-layer" rx="5"/>
  <text x="595" y="640" text-anchor="middle" class="subtitle">网络配置管理</text>
  <text x="480" y="660" class="responsibility">⚙️ Retrofit、OkHttp配置</text>
  <text x="480" y="675" class="responsibility">🔐 SSL证书和安全配置</text>

  <!-- 拦截器职责 -->
  <rect x="740" y="620" width="250" height="60" class="network-layer" rx="5"/>
  <text x="865" y="640" text-anchor="middle" class="subtitle">网络拦截器</text>
  <text x="750" y="660" class="responsibility">🔑 认证token自动添加</text>
  <text x="750" y="675" class="responsibility">📝 请求响应日志记录</text>

  <!-- 数据传输职责 -->
  <rect x="1010" y="620" width="320" height="60" class="network-layer" rx="5"/>
  <text x="1170" y="640" text-anchor="middle" class="subtitle">数据传输处理</text>
  <text x="1020" y="660" class="responsibility">📡 HTTP请求发送和响应接收</text>
  <text x="1020" y="675" class="responsibility">🗜️ 数据压缩和编码处理</text>

  <!-- 第六层：Service层 -->
  <rect x="50" y="720" width="1300" height="100" class="service-layer" rx="10"/>
  <rect x="60" y="730" width="120" height="35" fill="#e91e63" rx="5"/>
  <text x="120" y="752" text-anchor="middle" class="layer-title">Service层</text>

  <!-- 后台服务职责 -->
  <rect x="200" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="325" y="760" text-anchor="middle" class="subtitle">后台服务管理</text>
  <text x="210" y="780" class="responsibility">👁️ 眼动数据实时采集</text>
  <text x="210" y="795" class="responsibility">🔄 持续运行不受Activity影响</text>

  <!-- 硬件交互职责 -->
  <rect x="470" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="595" y="760" text-anchor="middle" class="subtitle">硬件设备交互</text>
  <text x="480" y="780" class="responsibility">📷 摄像头控制和图像处理</text>
  <text x="480" y="795" class="responsibility">🔍 眼动追踪算法执行</text>

  <!-- 通信管理职责 -->
  <rect x="740" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="865" y="760" text-anchor="middle" class="subtitle">跨组件通信</text>
  <text x="750" y="780" class="responsibility">📡 Message机制实现</text>
  <text x="750" y="795" class="responsibility">🔗 Binder接口提供</text>

  <!-- 数据缓存职责 -->
  <rect x="1010" y="740" width="320" height="60" class="service-layer" rx="5"/>
  <text x="1170" y="760" text-anchor="middle" class="subtitle">数据缓存和传输</text>
  <text x="1020" y="780" class="responsibility">💾 轨迹数据临时缓存</text>
  <text x="1020" y="795" class="responsibility">📊 数据格式化和传输</text>

  <!-- 协作关系箭头 -->
  <line x1="700" y1="200" x2="700" y2="220" class="collaboration-arrow"/>
  <line x1="700" y1="320" x2="700" y2="340" class="collaboration-arrow"/>
  <line x1="700" y1="460" x2="700" y2="480" class="collaboration-arrow"/>
  <line x1="700" y1="580" x2="700" y2="600" class="collaboration-arrow"/>
  <line x1="700" y1="700" x2="700" y2="720" class="collaboration-arrow"/>

  <!-- Service到Fragment的通信 -->
  <path d="M 200 780 Q 100 650 200 140" class="data-arrow"/>
  <text x="120" y="460" class="text" fill="#ff9800" transform="rotate(-90 120 460)">Message通信</text>

  <!-- 架构优势说明 -->
  <rect x="50" y="840" width="1300" height="120" class="viewmodel-layer" rx="10"/>
  <text x="700" y="865" text-anchor="middle" class="subtitle">分层架构协作优势</text>
  
  <text x="70" y="890" class="text">🎯 <tspan class="subtitle">单一职责原则</tspan>: 每层专注特定功能，UI层处理交互，ViewModel管理状态，Repository处理数据，Network负责通信</text>
  <text x="70" y="910" class="text">🔄 <tspan class="subtitle">依赖倒置原则</tspan>: 上层依赖下层抽象，Repository接口解耦具体实现，便于测试和替换</text>
  <text x="70" y="930" class="text">📡 <tspan class="subtitle">数据流控制</tspan>: 单向数据流，响应式编程，LiveData实现数据驱动UI，确保状态一致性</text>
  <text x="70" y="950" class="text">🛡️ <tspan class="subtitle">生命周期管理</tspan>: viewModelScope协程管理，LiveData生命周期感知，Service独立运行，全面的内存泄漏防护</text>

</svg>
