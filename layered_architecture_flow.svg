<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .ui-layer { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .view-layer { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .viewmodel-layer { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .repository-layer { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .network-layer { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .service-layer { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .upward-flow { stroke: #4caf50; stroke-width: 3; fill: none; marker-end: url(#upward); }
      .downward-flow { stroke: #2196f3; stroke-width: 3; fill: none; marker-end: url(#downward); }
      .data-flow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#dataflow); }
      .lifecycle-flow { stroke: #9c27b0; stroke-width: 2; fill: none; stroke-dasharray: 5,3; marker-end: url(#lifecycle); }
    </style>
    <marker id="upward" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4caf50" />
    </marker>
    <marker id="downward" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2196f3" />
    </marker>
    <marker id="dataflow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
    <marker id="lifecycle" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">眼球运动评估模块分层架构协作流程图</text>
  <text x="800" y="50" text-anchor="middle" class="subtitle">从用户操作到数据存储的完整数据流</text>

  <!-- 第一层：UI展示层 -->
  <rect x="50" y="80" width="1500" height="120" class="ui-layer" rx="8"/>
  <rect x="60" y="90" width="100" height="30" fill="#4caf50" rx="5"/>
  <text x="110" y="110" text-anchor="middle" class="layer-title">UI展示层</text>
  
  <!-- Activity -->
  <rect x="200" y="100" width="200" height="80" class="ui-layer" rx="5"/>
  <text x="300" y="120" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateActivity</text>
  <text x="210" y="140" class="text">🎭 Fragment容器管理</text>
  <text x="210" y="155" class="text">🔄 页面导航控制</text>
  <text x="210" y="170" class="code">supportFragmentManager.replace()</text>

  <!-- Fragment -->
  <rect x="420" y="100" width="250" height="80" class="ui-layer" rx="5"/>
  <text x="545" y="120" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingFragment</text>
  <text x="430" y="140" class="text">🎮 用户交互处理</text>
  <text x="430" y="155" class="text">📊 数据收集</text>
  <text x="430" y="170" class="code">sendMessageToService(MSG_START_TRACK)</text>

  <!-- ResultActivity -->
  <rect x="690" y="100" width="250" height="80" class="ui-layer" rx="5"/>
  <text x="815" y="120" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultActivity</text>
  <text x="700" y="140" class="text">👁️ 结果展示</text>
  <text x="700" y="155" class="text">📱 LiveData观察</text>
  <text x="700" y="170" class="code">viewModel.observe(this, Observer{})</text>

  <!-- 第二层：自定义View层 -->
  <rect x="50" y="220" width="1500" height="100" class="view-layer" rx="8"/>
  <rect x="60" y="230" width="120" height="30" fill="#ff9800" rx="5"/>
  <text x="120" y="250" text-anchor="middle" class="layer-title">自定义View层</text>

  <!-- EvaluatingView -->
  <rect x="200" y="240" width="220" height="60" class="view-layer" rx="5"/>
  <text x="310" y="260" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingView</text>
  <text x="210" y="280" class="text">🎯 目标点显示</text>
  <text x="210" y="295" class="code">startEvaluating(pointF)</text>

  <!-- ResultView -->
  <rect x="440" y="240" width="220" height="60" class="view-layer" rx="5"/>
  <text x="550" y="260" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultView</text>
  <text x="450" y="280" class="text">🎨 Canvas轨迹绘制</text>
  <text x="450" y="295" class="code">onDraw(canvas)</text>

  <!-- Canvas绘制详情 -->
  <rect x="680" y="240" width="300" height="60" class="view-layer" rx="5"/>
  <text x="830" y="260" text-anchor="middle" class="subtitle">Canvas绘制机制</text>
  <text x="690" y="280" class="code">canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="690" y="295" class="code">canvas.drawCircle(x, y, radius, pointPaint)</text>

  <!-- 第三层：ViewModel层 -->
  <rect x="50" y="340" width="1500" height="120" class="viewmodel-layer" rx="8"/>
  <rect x="60" y="350" width="100" height="30" fill="#2196f3" rx="5"/>
  <text x="110" y="370" text-anchor="middle" class="layer-title">ViewModel层</text>

  <!-- SaccadeAbilityViewModel -->
  <rect x="200" y="360" width="280" height="80" class="viewmodel-layer" rx="5"/>
  <text x="340" y="380" text-anchor="middle" class="subtitle">SaccadeAbilityViewModel</text>
  <text x="210" y="400" class="text">📊 数据状态管理</text>
  <text x="210" y="415" class="text">🧵 协程管理 (viewModelScope)</text>
  <text x="210" y="430" class="code">val submitResultLiveData = MutableLiveData&lt;&gt;()</text>

  <!-- EMPatientViewModel -->
  <rect x="500" y="360" width="280" height="80" class="viewmodel-layer" rx="5"/>
  <text x="640" y="380" text-anchor="middle" class="subtitle">EMPatientViewModel</text>
  <text x="510" y="400" class="text">👤 患者信息管理</text>
  <text x="510" y="415" class="text">📋 患者列表数据</text>
  <text x="510" y="430" class="code">val currentPatientLiveData = MutableLiveData&lt;&gt;()</text>

  <!-- LiveData机制 -->
  <rect x="800" y="360" width="280" height="80" class="viewmodel-layer" rx="5"/>
  <text x="940" y="380" text-anchor="middle" class="subtitle">LiveData数据发布机制</text>
  <text x="810" y="400" class="text">🔄 生命周期感知</text>
  <text x="810" y="415" class="text">📡 自动UI更新</text>
  <text x="810" y="430" class="code">liveData.postValue(result)</text>

  <!-- 第四层：Repository层 -->
  <rect x="50" y="480" width="1500" height="100" class="repository-layer" rx="8"/>
  <rect x="60" y="490" width="100" height="30" fill="#9c27b0" rx="5"/>
  <text x="110" y="510" text-anchor="middle" class="layer-title">Repository层</text>

  <!-- SaccadeAbilityRepository -->
  <rect x="200" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="325" y="520" text-anchor="middle" class="subtitle">SaccadeAbilityRepository</text>
  <text x="210" y="540" class="text">🌐 API调用封装</text>
  <text x="210" y="555" class="code">suspend fun submitResult()</text>

  <!-- EMPatientRepository -->
  <rect x="470" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="595" y="520" text-anchor="middle" class="subtitle">EMPatientRepository</text>
  <text x="480" y="540" class="text">👤 患者数据CRUD</text>
  <text x="480" y="555" class="code">suspend fun addPatient()</text>

  <!-- 数据转换 -->
  <rect x="740" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="865" y="520" text-anchor="middle" class="subtitle">数据格式转换</text>
  <text x="750" y="540" class="text">📝 业务数据 → API格式</text>
  <text x="750" y="555" class="code">buildSubmitParams()</text>

  <!-- 错误处理 -->
  <rect x="1010" y="500" width="250" height="60" class="repository-layer" rx="5"/>
  <text x="1135" y="520" text-anchor="middle" class="subtitle">统一错误处理</text>
  <text x="1020" y="540" class="text">⚠️ 异常处理和重试</text>
  <text x="1020" y="555" class="code">collectResponse { }</text>

  <!-- 第五层：Network层 -->
  <rect x="50" y="600" width="1500" height="100" class="network-layer" rx="8"/>
  <rect x="60" y="610" width="100" height="30" fill="#f44336" rx="5"/>
  <text x="110" y="630" text-anchor="middle" class="layer-title">Network层</text>

  <!-- API Services -->
  <rect x="200" y="620" width="200" height="60" class="network-layer" rx="5"/>
  <text x="300" y="640" text-anchor="middle" class="subtitle">SaccadeAbilityApiService</text>
  <text x="210" y="660" class="code">@POST("/api/movement/saccade")</text>
  <text x="210" y="675" class="code">suspend fun addSaccadeAbility()</text>

  <rect x="420" y="620" width="200" height="60" class="network-layer" rx="5"/>
  <text x="520" y="640" text-anchor="middle" class="subtitle">EMPatientApiService</text>
  <text x="430" y="660" class="code">@POST("/api/movement/patient")</text>
  <text x="430" y="675" class="code">suspend fun addPatient()</text>

  <rect x="640" y="620" width="200" height="60" class="network-layer" rx="5"/>
  <text x="740" y="640" text-anchor="middle" class="subtitle">FileUploadApiService</text>
  <text x="650" y="660" class="code">@POST("/api/files/upload")</text>
  <text x="650" y="675" class="code">suspend fun uploadImage()</text>

  <!-- Retrofit配置 -->
  <rect x="860" y="620" width="200" height="60" class="network-layer" rx="5"/>
  <text x="960" y="640" text-anchor="middle" class="subtitle">MovementClient</text>
  <text x="870" y="660" class="text">🔌 Retrofit配置</text>
  <text x="870" y="675" class="text">🌐 10.100.2.254:8080</text>

  <!-- 网络拦截器 -->
  <rect x="1080" y="620" width="200" height="60" class="network-layer" rx="5"/>
  <text x="1180" y="640" text-anchor="middle" class="subtitle">网络拦截器</text>
  <text x="1090" y="660" class="text">🔐 认证处理</text>
  <text x="1090" y="675" class="text">📝 日志记录</text>

  <!-- 第六层：Service层 -->
  <rect x="50" y="720" width="1500" height="100" class="service-layer" rx="8"/>
  <rect x="60" y="730" width="100" height="30" fill="#e91e63" rx="5"/>
  <text x="110" y="750" text-anchor="middle" class="layer-title">Service层</text>

  <!-- GazeTrackService -->
  <rect x="200" y="740" width="300" height="60" class="service-layer" rx="5"/>
  <text x="350" y="760" text-anchor="middle" class="subtitle">GazeTrackService</text>
  <text x="210" y="780" class="text">👁️ 眼动数据采集</text>
  <text x="210" y="795" class="code">handleMessage(MSG_START_TRACK)</text>

  <!-- 硬件交互 -->
  <rect x="520" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="645" y="760" text-anchor="middle" class="subtitle">硬件设备交互</text>
  <text x="530" y="780" class="text">📷 摄像头控制</text>
  <text x="530" y="795" class="text">🔍 眼动追踪算法</text>

  <!-- 数据缓存 -->
  <rect x="790" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="915" y="760" text-anchor="middle" class="subtitle">数据缓存管理</text>
  <text x="800" y="780" class="text">💾 轨迹数据缓存</text>
  <text x="800" y="795" class="text">🔄 实时数据传输</text>

  <!-- 跨组件通信 -->
  <rect x="1060" y="740" width="250" height="60" class="service-layer" rx="5"/>
  <text x="1185" y="760" text-anchor="middle" class="subtitle">跨组件通信</text>
  <text x="1070" y="780" class="text">📡 Message通信</text>
  <text x="1070" y="795" class="text">🔗 LiveEventBus</text>

  <!-- 数据流箭头 - 上行流程 -->
  <text x="1350" y="100" class="subtitle" fill="#4caf50">数据上行流程</text>
  <line x1="1320" y1="180" x2="1320" y2="220" class="upward-flow"/>
  <line x1="1320" y1="320" x2="1320" y2="340" class="upward-flow"/>
  <line x1="1320" y1="460" x2="1320" y2="480" class="upward-flow"/>
  <line x1="1320" y1="580" x2="1320" y2="600" class="upward-flow"/>
  <line x1="1320" y1="700" x2="1320" y2="720" class="upward-flow"/>

  <text x="1330" y="200" class="text" fill="#4caf50">用户操作</text>
  <text x="1330" y="330" class="text" fill="#4caf50">数据收集</text>
  <text x="1330" y="470" class="text" fill="#4caf50">业务处理</text>
  <text x="1330" y="590" class="text" fill="#4caf50">数据转换</text>
  <text x="1330" y="710" class="text" fill="#4caf50">API调用</text>

  <!-- 数据流箭头 - 下行流程 -->
  <text x="1450" y="100" class="subtitle" fill="#2196f3">数据下行流程</text>
  <line x1="1420" y1="720" x2="1420" y2="700" class="downward-flow"/>
  <line x1="1420" y1="600" x2="1420" y2="580" class="downward-flow"/>
  <line x1="1420" y1="480" x2="1420" y2="460" class="downward-flow"/>
  <line x1="1420" y1="340" x2="1420" y2="320" class="downward-flow"/>
  <line x1="1420" y1="220" x2="1420" y2="180" class="downward-flow"/>

  <text x="1430" y="710" class="text" fill="#2196f3">服务器响应</text>
  <text x="1430" y="590" class="text" fill="#2196f3">数据解析</text>
  <text x="1430" y="470" class="text" fill="#2196f3">异常处理</text>
  <text x="1430" y="330" class="text" fill="#2196f3">状态管理</text>
  <text x="1430" y="200" class="text" fill="#2196f3">UI更新</text>

  <!-- 横向数据流 -->
  <line x1="400" y1="140" x2="420" y2="140" class="data-flow"/>
  <line x1="670" y1="140" x2="690" y2="140" class="data-flow"/>
  <line x1="420" y1="270" x2="440" y2="270" class="data-flow"/>
  <line x1="480" y1="400" x2="500" y2="400" class="data-flow"/>
  <line x1="780" y1="400" x2="800" y2="400" class="data-flow"/>

  <!-- Service与Fragment的通信 -->
  <path d="M 350 740 Q 400 650 545 180" class="lifecycle-flow"/>
  <text x="450" y="450" class="text" fill="#9c27b0">Message通信</text>

  <!-- LiveEventBus跨组件通信 -->
  <path d="M 545 180 Q 600 250 815 180" class="data-flow"/>
  <text x="650" y="200" class="text" fill="#ff9800">LiveEventBus</text>

  <!-- 详细流程说明 -->
  <rect x="50" y="850" width="1500" height="200" class="ui-layer" rx="8"/>
  <text x="800" y="875" text-anchor="middle" class="subtitle">完整数据流转详细步骤</text>

  <!-- 步骤1-3 -->
  <rect x="70" y="890" width="280" height="140" class="ui-layer" rx="5"/>
  <text x="210" y="910" text-anchor="middle" class="subtitle">步骤1-3: 用户交互与数据采集</text>
  <text x="80" y="930" class="text">1️⃣ 用户点击"开始评估"按钮</text>
  <text x="80" y="945" class="code">Fragment.tvStartEvaluating.onClick</text>
  <text x="80" y="965" class="text">2️⃣ Fragment与Service建立通信</text>
  <text x="80" y="980" class="code">sendMessageToService(MSG_START_TRACK)</text>
  <text x="80" y="1000" class="text">3️⃣ Service采集眼动数据</text>
  <text x="80" y="1015" class="code">GazeTrackService.handleMessage()</text>

  <!-- 步骤4-6 -->
  <rect x="370" y="890" width="280" height="140" class="viewmodel-layer" rx="5"/>
  <text x="510" y="910" text-anchor="middle" class="subtitle">步骤4-6: 数据处理与业务逻辑</text>
  <text x="380" y="930" class="text">4️⃣ 数据传递到ResultActivity</text>
  <text x="380" y="945" class="code">LiveEventBus.post(evaluateResult)</text>
  <text x="380" y="965" class="text">5️⃣ ViewModel处理业务逻辑</text>
  <text x="380" y="980" class="code">viewModel.uploadImage(bitmap)</text>
  <text x="380" y="1000" class="text">6️⃣ 协程执行异步操作</text>
  <text x="380" y="1015" class="code">viewModelScope.launch { }</text>

  <!-- 步骤7-9 -->
  <rect x="670" y="890" width="280" height="140" class="repository-layer" rx="5"/>
  <text x="810" y="910" text-anchor="middle" class="subtitle">步骤7-9: 数据提交与网络请求</text>
  <text x="680" y="930" class="text">7️⃣ Repository封装API调用</text>
  <text x="680" y="945" class="code">repository.submitResult(params)</text>
  <text x="680" y="965" class="text">8️⃣ 网络请求发送到服务器</text>
  <text x="680" y="980" class="code">apiService.addSaccadeAbility()</text>
  <text x="680" y="1000" class="text">9️⃣ 服务器处理并返回响应</text>
  <text x="680" y="1015" class="code">HTTP 200 OK + 数据ID</text>

  <!-- 步骤10-12 -->
  <rect x="970" y="890" width="280" height="140" class="network-layer" rx="5"/>
  <text x="1110" y="910" text-anchor="middle" class="subtitle">步骤10-12: 响应处理与UI更新</text>
  <text x="980" y="930" class="text">🔟 collectResponse处理响应</text>
  <text x="980" y="945" class="code">onSuccess = { liveData.postValue() }</text>
  <text x="980" y="965" class="text">1️⃣1️⃣ LiveData通知观察者</text>
  <text x="980" y="980" class="code">Observer.onChanged(result)</text>
  <text x="980" y="1000" class="text">1️⃣2️⃣ UI更新显示结果</text>
  <text x="980" y="1015" class="code">Toast.makeText("提交成功")</text>

  <!-- 生命周期管理说明 -->
  <rect x="1270" y="890" width="260" height="140" class="service-layer" rx="5"/>
  <text x="1400" y="910" text-anchor="middle" class="subtitle">生命周期管理</text>
  <text x="1280" y="930" class="text">🔄 viewModelScope</text>
  <text x="1290" y="945" class="text">自动管理协程生命周期</text>
  <text x="1280" y="965" class="text">📱 LiveData</text>
  <text x="1290" y="980" class="text">生命周期感知观察者</text>
  <text x="1280" y="1000" class="text">🛡️ 内存泄漏防护</text>
  <text x="1290" y="1015" class="text">Activity销毁时自动清理</text>

  <!-- 架构优势总结 -->
  <rect x="50" y="1070" width="1500" height="100" class="viewmodel-layer" rx="8"/>
  <text x="800" y="1095" text-anchor="middle" class="subtitle">分层架构优势总结</text>

  <text x="70" y="1120" class="text">🎯 <tspan class="subtitle">职责分离</tspan>: 每层专注于特定功能，UI层处理交互，ViewModel管理状态，Repository处理数据</text>
  <text x="70" y="1140" class="text">🔄 <tspan class="subtitle">数据流控制</tspan>: 单向数据流，响应式编程，通过LiveData实现数据驱动UI更新</text>
  <text x="70" y="1160" class="text">⚡ <tspan class="subtitle">异步处理</tspan>: 协程处理耗时操作，collectResponse统一错误处理，主线程安全</text>

</svg>
