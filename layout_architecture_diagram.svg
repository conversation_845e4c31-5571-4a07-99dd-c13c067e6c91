<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .xml-box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
      .kotlin-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .theme-box { fill: #fdf2e9; stroke: #f39c12; stroke-width: 2; }
      .custom-view-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; }
      .performance-box { fill: #fdedec; stroke: #e74c3c; stroke-width: 2; }
      .arrow { stroke: #7f8c8d; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">眼动追踪项目布局样式架构图</text>
  <text x="700" y="55" text-anchor="middle" class="subtitle">Layout Style Implementation Architecture</text>

  <!-- 1. XML布局层 -->
  <rect x="50" y="80" width="300" height="180" class="xml-box" rx="8"/>
  <text x="200" y="105" text-anchor="middle" class="subtitle">📄 XML布局层</text>
  <text x="60" y="125" class="text">• ConstraintLayout根布局</text>
  <text x="60" y="145" class="text">• 多View叠加显示</text>
  <text x="60" y="165" class="text">• 自定义View集成</text>
  <text x="60" y="185" class="code">activity_roi_detection.xml</text>
  <text x="60" y="200" class="code">view_posture_calibration.xml</text>
  <text x="60" y="215" class="code">activity_roi_detection_result.xml</text>
  <text x="60" y="235" class="text">🎯 提供基础布局结构</text>

  <!-- 2. 主题样式层 -->
  <rect x="380" y="80" width="300" height="180" class="theme-box" rx="8"/>
  <text x="530" y="105" text-anchor="middle" class="subtitle">🎨 主题样式层</text>
  <text x="390" y="125" class="text">• Material Design主题</text>
  <text x="390" y="145" class="text">• 日夜模式支持</text>
  <text x="390" y="165" class="text">• 圆角样式定义</text>
  <text x="390" y="185" class="code">themes.xml / themes-night.xml</text>
  <text x="390" y="200" class="code">styles.xml</text>
  <text x="390" y="215" class="code">colors.xml</text>
  <text x="390" y="235" class="text">🎨 统一视觉风格</text>

  <!-- 3. 屏幕适配层 -->
  <rect x="710" y="80" width="300" height="180" class="kotlin-box" rx="8"/>
  <text x="860" y="105" text-anchor="middle" class="subtitle">📱 屏幕适配层</text>
  <text x="720" y="125" class="text">• AutoSize库集成</text>
  <text x="720" y="145" class="text">• 多进程适配支持</text>
  <text x="720" y="165" class="text">• 设备实际尺寸适配</text>
  <text x="720" y="185" class="code">GTApplication.initAutoSize()</text>
  <text x="720" y="200" class="code">AutoSizeConfig.getInstance()</text>
  <text x="720" y="215" class="code">.setUseDeviceSize(true)</text>
  <text x="720" y="235" class="text">📐 确保多屏幕兼容</text>

  <!-- 4. 自定义View层 -->
  <rect x="1040" y="80" width="320" height="180" class="custom-view-box" rx="8"/>
  <text x="1200" y="105" text-anchor="middle" class="subtitle">🔧 自定义View层</text>
  <text x="1050" y="125" class="text">• Canvas绘制引擎</text>
  <text x="1050" y="145" class="text">• 触摸事件处理</text>
  <text x="1050" y="165" class="text">• 实时图形渲染</text>
  <text x="1050" y="185" class="code">ROIPathView</text>
  <text x="1050" y="200" class="code">ROIDetectionResultView</text>
  <text x="1050" y="215" class="code">PostureCalibrationView</text>
  <text x="1050" y="235" class="text">🎯 实现复杂交互功能</text>

  <!-- 连接箭头 -->
  <line x1="350" y1="170" x2="380" y2="170" class="arrow"/>
  <line x1="680" y1="170" x2="710" y2="170" class="arrow"/>
  <line x1="1010" y1="170" x2="1040" y2="170" class="arrow"/>

  <!-- 5. 动态布局控制 -->
  <rect x="50" y="300" width="400" height="200" class="kotlin-box" rx="8"/>
  <text x="250" y="325" text-anchor="middle" class="subtitle">⚡ 动态布局控制</text>
  <text x="60" y="350" class="text">• ConstraintSet动态约束</text>
  <text x="60" y="370" class="text">• 运行时布局调整</text>
  <text x="60" y="390" class="text">• 坐标转换系统</text>
  <text x="60" y="415" class="code">val constraintSet = ConstraintSet()</text>
  <text x="60" y="430" class="code">constraintSet.clone(postureCorrectionRoot)</text>
  <text x="60" y="445" class="code">constraintSet.setMargin(viewId, margin)</text>
  <text x="60" y="460" class="code">constraintSet.applyTo(rootLayout)</text>
  <text x="60" y="480" class="text">🔄 实现响应式布局变化</text>

  <!-- 6. Canvas绘制系统 -->
  <rect x="480" y="300" width="400" height="200" class="custom-view-box" rx="8"/>
  <text x="680" y="325" text-anchor="middle" class="subtitle">🎨 Canvas绘制系统</text>
  <text x="490" y="350" class="text">• Path路径绘制</text>
  <text x="490" y="370" class="text">• Paint画笔配置</text>
  <text x="490" y="390" class="text">• 抗锯齿处理</text>
  <text x="490" y="415" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="490" y="430" class="code">  canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="490" y="445" class="code">  canvas.drawCircle(x, y, radius, paint)</text>
  <text x="490" y="460" class="code">}</text>
  <text x="490" y="480" class="text">🖌️ 精确图形渲染</text>

  <!-- 7. 性能优化策略 -->
  <rect x="910" y="300" width="450" height="200" class="performance-box" rx="8"/>
  <text x="1135" y="325" text-anchor="middle" class="subtitle">🚀 性能优化策略</text>
  <text x="920" y="350" class="text">• Paint对象复用避免GC</text>
  <text x="920" y="370" class="text">• invalidate()精确重绘</text>
  <text x="920" y="390" class="text">• 坐标预计算缓存</text>
  <text x="920" y="415" class="code">private val gazePointPaint = Paint().apply {</text>
  <text x="920" y="430" class="code">  isAntiAlias = true</text>
  <text x="920" y="445" class="code">}</text>
  <text x="920" y="460" class="code">// 避免在onDraw中创建对象</text>
  <text x="920" y="480" class="text">⚡ 确保60fps流畅体验</text>

  <!-- 垂直连接箭头 -->
  <line x1="200" y1="260" x2="200" y2="300" class="arrow"/>
  <line x1="680" y1="260" x2="680" y2="300" class="arrow"/>
  <line x1="1200" y1="260" x2="1200" y2="300" class="arrow"/>

  <!-- 8. 坐标转换系统 -->
  <rect x="50" y="540" width="600" height="150" class="kotlin-box" rx="8"/>
  <text x="350" y="565" text-anchor="middle" class="subtitle">📐 坐标转换系统</text>
  <text x="60" y="590" class="text">• 相对坐标(0-1) → 屏幕坐标(px)</text>
  <text x="60" y="610" class="text">• 多屏幕尺寸适配</text>
  <text x="60" y="635" class="code">val circleX = result.x!! * screenWidth</text>
  <text x="60" y="650" class="code">val circleY = result.y!! * screenHeight</text>
  <text x="60" y="670" class="text">🎯 确保精确定位</text>

  <!-- 9. 触摸事件处理 -->
  <rect x="680" y="540" width="680" height="150" class="custom-view-box" rx="8"/>
  <text x="1020" y="565" text-anchor="middle" class="subtitle">👆 触摸事件处理</text>
  <text x="690" y="590" class="text">• ACTION_DOWN/MOVE/UP事件</text>
  <text x="690" y="610" class="text">• Path路径实时绘制</text>
  <text x="690" y="635" class="code">override fun onTouchEvent(event: MotionEvent): Boolean {</text>
  <text x="690" y="650" class="code">  when (event.action) { ACTION_MOVE -> currentPath.lineTo(x, y) }</text>
  <text x="690" y="670" class="text">✋ 支持手势交互</text>

  <!-- 10. 数据流向图 -->
  <rect x="50" y="720" width="1310" height="120" class="xml-box" rx="8"/>
  <text x="705" y="745" text-anchor="middle" class="subtitle">🔄 数据流向与渲染流程</text>
  
  <!-- 数据流程步骤 -->
  <rect x="70" y="760" width="120" height="40" fill="#3498db" rx="5"/>
  <text x="130" y="785" text-anchor="middle" class="text" fill="white">1. XML解析</text>
  
  <rect x="220" y="760" width="120" height="40" fill="#27ae60" rx="5"/>
  <text x="280" y="785" text-anchor="middle" class="text" fill="white">2. 主题应用</text>
  
  <rect x="370" y="760" width="120" height="40" fill="#f39c12" rx="5"/>
  <text x="430" y="785" text-anchor="middle" class="text" fill="white">3. 屏幕适配</text>
  
  <rect x="520" y="760" width="120" height="40" fill="#9b59b6" rx="5"/>
  <text x="580" y="785" text-anchor="middle" class="text" fill="white">4. View创建</text>
  
  <rect x="670" y="760" width="120" height="40" fill="#e74c3c" rx="5"/>
  <text x="730" y="785" text-anchor="middle" class="text" fill="white">5. 布局测量</text>
  
  <rect x="820" y="760" width="120" height="40" fill="#34495e" rx="5"/>
  <text x="880" y="785" text-anchor="middle" class="text" fill="white">6. Canvas绘制</text>
  
  <rect x="970" y="760" width="120" height="40" fill="#16a085" rx="5"/>
  <text x="1030" y="785" text-anchor="middle" class="text" fill="white">7. 事件处理</text>
  
  <rect x="1120" y="760" width="120" height="40" fill="#8e44ad" rx="5"/>
  <text x="1180" y="785" text-anchor="middle" class="text" fill="white">8. 动态更新</text>

  <!-- 流程箭头 -->
  <line x1="190" y1="780" x2="220" y2="780" class="arrow"/>
  <line x1="340" y1="780" x2="370" y2="780" class="arrow"/>
  <line x1="490" y1="780" x2="520" y2="780" class="arrow"/>
  <line x1="640" y1="780" x2="670" y2="780" class="arrow"/>
  <line x1="790" y1="780" x2="820" y2="780" class="arrow"/>
  <line x1="940" y1="780" x2="970" y2="780" class="arrow"/>
  <line x1="1090" y1="780" x2="1120" y2="780" class="arrow"/>

  <!-- 底部总结 -->
  <text x="700" y="860" text-anchor="middle" class="subtitle">🎯 核心特点总结</text>
  <text x="100" y="890" class="text">• 多层架构设计：XML布局 + 自定义View + 动态约束 + Canvas绘制</text>
  <text x="100" y="910" class="text">• 精确坐标系统：相对坐标与屏幕坐标灵活转换，支持多屏幕适配</text>
  <text x="100" y="930" class="text">• 高性能渲染：Paint复用、精确重绘、坐标缓存等优化策略</text>
  <text x="100" y="950" class="text">• 实时交互支持：触摸绘制、动态布局调整、Canvas实时渲染</text>
  <text x="100" y="970" class="text">• 适用场景：眼动追踪、精确定位、实时绘制等需要高精度坐标处理的应用</text>

</svg>
