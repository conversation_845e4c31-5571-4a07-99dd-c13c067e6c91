<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .state-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .created-state { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .started-state { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .resumed-state { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .paused-state { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .stopped-state { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .destroyed-state { fill: #fafafa; stroke: #757575; stroke-width: 2; }
      .lifecycle-arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#lifecycle); }
      .active-flow { stroke: #4caf50; stroke-width: 3; fill: none; marker-end: url(#active); }
      .inactive-flow { stroke: #f44336; stroke-width: 3; fill: none; marker-end: url(#inactive); }
    </style>
    <marker id="lifecycle" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#34495e" />
    </marker>
    <marker id="active" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4caf50" />
    </marker>
    <marker id="inactive" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#f44336" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">LiveData生命周期感知机制详解</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">Activity/Fragment生命周期与Observer状态管理</text>

  <!-- 生命周期状态流程 -->
  <text x="700" y="75" text-anchor="middle" class="subtitle">Activity/Fragment生命周期状态变化</text>

  <!-- CREATED状态 -->
  <rect x="50" y="90" width="180" height="100" class="created-state" rx="8"/>
  <text x="140" y="115" text-anchor="middle" class="subtitle">CREATED</text>
  <text x="60" y="135" class="state-text">📱 Activity/Fragment已创建</text>
  <text x="60" y="150" class="state-text">🔧 ViewModel初始化</text>
  <text x="60" y="165" class="state-text">📡 Observer注册</text>
  <text x="60" y="180" class="code">observe(this, Observer { })</text>

  <!-- STARTED状态 -->
  <rect x="260" y="90" width="180" height="100" class="started-state" rx="8"/>
  <text x="350" y="115" text-anchor="middle" class="subtitle">STARTED</text>
  <text x="270" y="135" class="state-text">👁️ 用户可见但不可交互</text>
  <text x="270" y="150" class="state-text">✅ Observer开始接收回调</text>
  <text x="270" y="165" class="state-text">🔄 UI开始更新</text>
  <text x="270" y="180" class="code">onChanged(data) 执行</text>

  <!-- RESUMED状态 -->
  <rect x="470" y="90" width="180" height="100" class="started-state" rx="8"/>
  <text x="560" y="115" text-anchor="middle" class="subtitle">RESUMED</text>
  <text x="480" y="135" class="state-text">🎮 用户可完全交互</text>
  <text x="480" y="150" class="state-text">✅ Observer正常工作</text>
  <text x="480" y="165" class="state-text">📊 数据实时更新UI</text>
  <text x="480" y="180" class="code">完整功能可用</text>

  <!-- PAUSED状态 -->
  <rect x="680" y="90" width="180" height="100" class="paused-state" rx="8"/>
  <text x="770" y="115" text-anchor="middle" class="subtitle">PAUSED</text>
  <text x="690" y="135" class="state-text">⏸️ 部分被其他Activity覆盖</text>
  <text x="690" y="150" class="state-text">⏸️ Observer暂停回调</text>
  <text x="690" y="165" class="state-text">💾 数据状态保留</text>
  <text x="690" y="180" class="code">onChanged() 不执行</text>

  <!-- STOPPED状态 -->
  <rect x="890" y="90" width="180" height="100" class="stopped-state" rx="8"/>
  <text x="980" y="115" text-anchor="middle" class="subtitle">STOPPED</text>
  <text x="900" y="135" class="state-text">🙈 完全不可见</text>
  <text x="900" y="150" class="state-text">⏹️ Observer完全暂停</text>
  <text x="900" y="165" class="state-text">💾 ViewModel数据保持</text>
  <text x="900" y="180" class="code">UI更新暂停</text>

  <!-- DESTROYED状态 -->
  <rect x="1100" y="90" width="180" height="100" class="destroyed-state" rx="8"/>
  <text x="1190" y="115" text-anchor="middle" class="subtitle">DESTROYED</text>
  <text x="1110" y="135" class="state-text">💀 Activity/Fragment销毁</text>
  <text x="1110" y="150" class="state-text">🗑️ Observer自动移除</text>
  <text x="1110" y="165" class="state-text">🧹 内存清理</text>
  <text x="1110" y="180" class="code">避免内存泄漏</text>

  <!-- 生命周期箭头 -->
  <line x1="230" y1="140" x2="260" y2="140" class="lifecycle-arrow"/>
  <line x1="440" y1="140" x2="470" y2="140" class="lifecycle-arrow"/>
  <line x1="650" y1="140" x2="680" y2="140" class="lifecycle-arrow"/>
  <line x1="860" y1="140" x2="890" y2="140" class="lifecycle-arrow"/>
  <line x1="1070" y1="140" x2="1100" y2="140" class="lifecycle-arrow"/>

  <!-- Observer活跃状态指示 -->
  <rect x="50" y="210" width="600" height="40" class="started-state" rx="5"/>
  <text x="350" y="235" text-anchor="middle" class="subtitle">Observer活跃状态 (接收LiveData回调)</text>

  <rect x="680" y="210" width="600" height="40" class="paused-state" rx="5"/>
  <text x="980" y="235" text-anchor="middle" class="subtitle">Observer非活跃状态 (暂停接收回调)</text>

  <!-- LiveData工作机制详解 -->
  <rect x="50" y="270" width="1300" height="200" class="started-state" rx="10"/>
  <text x="700" y="295" text-anchor="middle" class="subtitle">LiveData生命周期感知工作机制</text>

  <!-- 数据发布检查 -->
  <rect x="70" y="310" width="250" height="140" class="started-state" rx="5"/>
  <text x="195" y="330" text-anchor="middle" class="subtitle">数据发布时的生命周期检查</text>
  <text x="80" y="350" class="code">override fun postValue(value: T?) {</text>
  <text x="90" y="365" class="code">  // 切换到主线程</text>
  <text x="90" y="380" class="code">  mainHandler.post {</text>
  <text x="100" y="395" class="code">    setValue(value)</text>
  <text x="90" y="410" class="code">  }</text>
  <text x="80" y="425" class="code">}</text>
  <text x="80" y="445" class="code">override fun setValue(value: T?) {</text>
  <text x="90" y="460" class="code">  // 通知活跃观察者</text>
  <text x="90" y="475" class="code">  dispatchingValue(null)</text>
  <text x="80" y="490" class="code">}</text>

  <!-- Observer状态检查 -->
  <rect x="340" y="310" width="250" height="140" class="started-state" rx="5"/>
  <text x="465" y="330" text-anchor="middle" class="subtitle">Observer活跃状态检查</text>
  <text x="350" y="350" class="code">private fun considerNotify(observer) {</text>
  <text x="360" y="365" class="code">  if (!observer.shouldBeActive()) {</text>
  <text x="370" y="380" class="code">    return // 非活跃状态，不通知</text>
  <text x="360" y="395" class="code">  }</text>
  <text x="360" y="410" class="code">  if (observer.lastVersion >= version) {</text>
  <text x="370" y="425" class="code">    return // 已是最新数据</text>
  <text x="360" y="440" class="code">  }</text>
  <text x="360" y="455" class="code">  observer.onChanged(value)</text>
  <text x="350" y="470" class="code">}</text>

  <!-- 生命周期状态判断 -->
  <rect x="610" y="310" width="250" height="140" class="started-state" rx="5"/>
  <text x="735" y="330" text-anchor="middle" class="subtitle">生命周期状态判断</text>
  <text x="620" y="350" class="code">boolean shouldBeActive() {</text>
  <text x="630" y="365" class="code">  return owner.getLifecycle()</text>
  <text x="640" y="380" class="code">    .getCurrentState()</text>
  <text x="640" y="395" class="code">    .isAtLeast(STARTED)</text>
  <text x="620" y="410" class="code">}</text>
  <text x="620" y="430" class="comment">// STARTED 和 RESUMED 状态</text>
  <text x="620" y="445" class="comment">// 被认为是活跃状态</text>
  <text x="620" y="460" class="comment">// 可以接收LiveData回调</text>

  <!-- 自动清理机制 -->
  <rect x="880" y="310" width="250" height="140" class="started-state" rx="5"/>
  <text x="1005" y="330" text-anchor="middle" class="subtitle">自动清理机制</text>
  <text x="890" y="350" class="code">@OnLifecycleEvent(ON_DESTROY)</text>
  <text x="890" y="365" class="code">void onDestroy() {</text>
  <text x="900" y="380" class="code">  // 自动移除Observer</text>
  <text x="900" y="395" class="code">  removeObserver(this)</text>
  <text x="890" y="410" class="code">}</text>
  <text x="890" y="430" class="comment">// LifecycleOwner销毁时</text>
  <text x="890" y="445" class="comment">// 自动清理所有Observer</text>
  <text x="890" y="460" class="comment">// 防止内存泄漏</text>

  <!-- 配置变更处理 -->
  <rect x="50" y="490" width="1300" height="120" class="resumed-state" rx="10"/>
  <text x="700" y="515" text-anchor="middle" class="subtitle">配置变更 (横竖屏切换) 处理机制</text>

  <!-- 配置变更前 -->
  <rect x="70" y="530" width="200" height="60" class="resumed-state" rx="5"/>
  <text x="170" y="550" text-anchor="middle" class="subtitle">配置变更前</text>
  <text x="80" y="570" class="text">📱 Activity即将销毁</text>
  <text x="80" y="585" class="text">💾 ViewModel数据保持</text>

  <!-- 配置变更中 -->
  <rect x="290" y="530" width="200" height="60" class="resumed-state" rx="5"/>
  <text x="390" y="550" text-anchor="middle" class="subtitle">配置变更中</text>
  <text x="300" y="570" class="text">🔄 Activity重新创建</text>
  <text x="300" y="585" class="text">🔗 ViewModel实例保持</text>

  <!-- 配置变更后 -->
  <rect x="510" y="530" width="200" height="60" class="resumed-state" rx="5"/>
  <text x="610" y="550" text-anchor="middle" class="subtitle">配置变更后</text>
  <text x="520" y="570" class="text">📡 Observer重新注册</text>
  <text x="520" y="585" class="text">📊 数据自动恢复UI</text>

  <!-- viewModelScope管理 -->
  <rect x="730" y="530" width="200" height="60" class="resumed-state" rx="5"/>
  <text x="830" y="550" text-anchor="middle" class="subtitle">viewModelScope</text>
  <text x="740" y="570" class="text">🧵 协程继续运行</text>
  <text x="740" y="585" class="text">⚡ 无需重新启动</text>

  <!-- 数据一致性 -->
  <rect x="950" y="530" width="200" height="60" class="resumed-state" rx="5"/>
  <text x="1050" y="550" text-anchor="middle" class="subtitle">数据一致性</text>
  <text x="960" y="570" class="text">📊 UI状态完全恢复</text>
  <text x="960" y="585" class="text">🎯 用户体验无缝</text>

  <!-- 配置变更箭头 -->
  <line x1="270" y1="560" x2="290" y2="560" class="active-flow"/>
  <line x1="490" y1="560" x2="510" y2="560" class="active-flow"/>
  <line x1="710" y1="560" x2="730" y2="560" class="active-flow"/>
  <line x1="930" y1="560" x2="950" y2="560" class="active-flow"/>

  <!-- 最佳实践总结 -->
  <rect x="50" y="630" width="1300" height="120" class="created-state" rx="10"/>
  <text x="700" y="655" text-anchor="middle" class="subtitle">LiveData生命周期管理最佳实践</text>
  
  <text x="70" y="680" class="text">🎯 <tspan class="subtitle">正确的LifecycleOwner</tspan>: 使用Activity或Fragment作为LifecycleOwner，确保Observer在正确的生命周期中注册</text>
  <text x="70" y="700" class="text">⚡ <tspan class="subtitle">避免内存泄漏</tspan>: 不要使用Application Context作为LifecycleOwner，避免使用observeForever而不手动移除</text>
  <text x="70" y="720" class="text">🔄 <tspan class="subtitle">数据一致性</tspan>: 利用ViewModel在配置变更时保持数据，确保UI状态的一致性和连续性</text>
  <text x="70" y="740" class="text">🛡️ <tspan class="subtitle">线程安全</tspan>: 使用postValue()在后台线程发布数据，LiveData自动切换到主线程执行Observer回调</text>

</svg>
