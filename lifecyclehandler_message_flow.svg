<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .activity-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .service-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .handler-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .messenger-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .lifecycle-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .message-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 2; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
      .lifecycle-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#purplearrowhead); }
      .message-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#orangearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
    
    <marker id="purplearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
    
    <marker id="orangearrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#f39c12" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">LifecycleHandler 消息处理流程详解</text>
  
  <!-- ReadActivity组件 -->
  <rect x="50" y="70" width="300" height="200" class="activity-box" rx="8"/>
  <text x="200" y="95" text-anchor="middle" class="subtitle">ReadActivity</text>
  
  <!-- LifecycleHandler -->
  <rect x="70" y="110" width="260" height="60" class="handler-box" rx="5"/>
  <text x="200" y="130" text-anchor="middle" class="step-title">LifecycleHandler</text>
  <text x="80" y="145" class="code-text">object : LifecycleHandler(Looper.getMainLooper(), this)</text>
  <text x="80" y="160" class="description-text">• 生命周期感知 • 主线程处理 • 自动清理</text>
  
  <!-- mClientMessage -->
  <rect x="70" y="180" width="260" height="40" class="messenger-box" rx="5"/>
  <text x="200" y="200" text-anchor="middle" class="step-title">mClientMessage: Messenger</text>
  <text x="80" y="215" class="code-text">Messenger(handler) - 包装handler用于接收消息</text>
  
  <!-- parseMessage方法 -->
  <rect x="70" y="230" width="260" height="30" class="message-box" rx="5"/>
  <text x="200" y="250" text-anchor="middle" class="step-title">parseMessage(msg: Message)</text>
  
  <!-- GazeTrackService组件 -->
  <rect x="1250" y="70" width="300" height="200" class="service-box" rx="8"/>
  <text x="1400" y="95" text-anchor="middle" class="subtitle">GazeTrackService</text>
  
  <!-- Service Handler -->
  <rect x="1270" y="110" width="260" height="60" class="handler-box" rx="5"/>
  <text x="1400" y="130" text-anchor="middle" class="step-title">Service LifecycleHandler</text>
  <text x="1280" y="145" class="code-text">处理来自Activity的消息</text>
  <text x="1280" y="160" class="description-text">• 执行具体操作 • 返回状态和数据</text>
  
  <!-- mServiceMessage -->
  <rect x="1270" y="180" width="260" height="40" class="messenger-box" rx="5"/>
  <text x="1400" y="200" text-anchor="middle" class="step-title">mServiceMessage: Messenger</text>
  <text x="1280" y="215" class="code-text">用于向Activity发送回复消息</text>
  
  <!-- 生命周期管理 -->
  <rect x="400" y="70" width="800" height="80" class="lifecycle-box" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">生命周期管理机制</text>
  <text x="420" y="115" class="step-text">• Activity创建时: LifecycleHandler绑定生命周期</text>
  <text x="420" y="130" class="step-text">• Activity活跃时: 正常处理消息</text>
  <text x="420" y="145" class="step-text">• Activity销毁时: 自动停止处理消息，避免内存泄漏</text>
  
  <!-- 消息流程步骤 -->
  <rect x="50" y="300" width="1500" height="750" class="message-box" rx="5"/>
  <text x="800" y="325" text-anchor="middle" class="subtitle">消息处理流程步骤</text>
  
  <!-- 步骤1: 初始化 -->
  <rect x="80" y="350" width="1440" height="80" class="handler-box" rx="5"/>
  <text x="100" y="370" class="step-title">步骤1: LifecycleHandler初始化</text>
  <text x="100" y="390" class="step-text">创建时机: Activity类初始化时创建handler对象</text>
  <text x="100" y="405" class="code-text">private val handler = object :LifecycleHandler(Looper.getMainLooper(), this){ override fun handleMessage(msg: Message) { parseMessage(msg) } }</text>
  <text x="100" y="420" class="description-text">• 绑定主线程Looper • 绑定Activity生命周期 • 重写handleMessage方法</text>
  
  <!-- 步骤2: Messenger包装 -->
  <rect x="80" y="450" width="1440" height="60" class="messenger-box" rx="5"/>
  <text x="100" y="470" class="step-title">步骤2: Messenger包装handler</text>
  <text x="100" y="485" class="code-text">private var mClientMessage:Messenger = Messenger(handler)</text>
  <text x="100" y="500" class="description-text">• 将handler包装成Messenger • 用于Service回复消息 • 建立双向通信通道</text>
  
  <!-- 步骤3: 传递给Service -->
  <rect x="80" y="530" width="1440" height="60" class="activity-box" rx="5"/>
  <text x="100" y="550" class="step-title">步骤3: 传递Messenger给Service</text>
  <text x="100" y="565" class="code-text">sendMessageToService(Message.obtain().apply { what = MSG_SERVICE_CONNECTED; replyTo = mClientMessage })</text>
  <text x="100" y="580" class="description-text">• Service连接时传递mClientMessage • Service获得回复消息的能力 • 建立完整通信链路</text>
  
  <!-- 箭头1: Activity -> Service -->
  <line x1="200" y1="600" x2="1400" y2="600" class="arrow"/>
  <text x="800" y="595" text-anchor="middle" class="description-text">传递mClientMessage</text>
  
  <!-- 步骤4: Service处理并回复 -->
  <rect x="80" y="620" width="1440" height="80" class="service-box" rx="5"/>
  <text x="100" y="640" class="step-title">步骤4: Service处理消息并回复</text>
  <text x="100" y="660" class="step-text">Service执行操作后，通过mClientMessage发送回复消息</text>
  <text x="100" y="675" class="code-text">clientMessenger.send(Message.obtain().apply { what = MSG_GAZE_TRACKING_STATE; data = Bundle().apply { putBoolean(KEY_STATE, true) } })</text>
  <text x="100" y="690" class="description-text">• 执行摄像头、追踪等操作 • 封装结果到Message • 通过Messenger发送回Activity</text>
  
  <!-- 箭头2: Service -> Activity -->
  <line x1="1400" y1="720" x2="200" y2="720" class="message-arrow"/>
  <text x="800" y="715" text-anchor="middle" class="description-text">发送回复消息</text>
  
  <!-- 步骤5: LifecycleHandler接收消息 -->
  <rect x="80" y="740" width="1440" height="100" class="handler-box" rx="5"/>
  <text x="100" y="760" class="step-title">步骤5: LifecycleHandler接收并处理消息</text>
  <text x="100" y="780" class="step-text">handler.handleMessage()被自动调用，检查生命周期后执行parseMessage()</text>
  <text x="100" y="795" class="code-text">override fun handleMessage(msg: Message) { super.handleMessage(msg); parseMessage(msg) }</text>
  <text x="100" y="810" class="description-text">• 检查Activity是否还活跃 • 如果活跃则处理消息 • 如果已销毁则忽略消息</text>
  <text x="100" y="825" class="description-text">• 在主线程执行，可安全更新UI • 避免Activity销毁后的崩溃</text>
  
  <!-- 步骤6: 具体消息处理 -->
  <rect x="80" y="860" width="1440" height="170" class="message-box" rx="5"/>
  <text x="100" y="880" class="step-title">步骤6: parseMessage()具体消息处理</text>
  
  <text x="100" y="905" class="step-text" font-weight="bold">MSG_GAZE_TRACKING_STATE:</text>
  <text x="120" y="920" class="code-text">val state = msg.data.getBoolean(KEY_STATE); Logger.d("追踪状态: $state")</text>
  
  <text x="100" y="940" class="step-text" font-weight="bold">MSG_APPLIED_READING_STATE:</text>
  <text x="120" y="955" class="code-text">if (state) { startTime = System.currentTimeMillis() } // 记录开始时间</text>
  
  <text x="100" y="975" class="step-text" font-weight="bold">MSG_GAZE_TRAJECTORY_RESULT:</text>
  <text x="120" y="990" class="code-text">val json = msg.data.getString(KEY_GAZE_TRAJECTORY)</text>
  <text x="120" y="1005" class="code-text">mGazeTrajectory = mGson.fromJson(json, GazeTrajectory::class.java)</text>
  <text x="120" y="1020" class="code-text">// 创建ReadResult并跳转到结果页面</text>
  
  <!-- 生命周期保护机制 -->
  <rect x="400" y="180" width="800" height="100" class="lifecycle-box" rx="8"/>
  <text x="800" y="205" text-anchor="middle" class="subtitle">生命周期保护机制</text>
  
  <text x="420" y="230" class="step-text" font-weight="bold">普通Handler问题:</text>
  <text x="440" y="245" class="step-text">• Activity销毁后仍可能处理消息导致崩溃</text>
  <text x="440" y="260" class="step-text">• 持有Activity引用可能导致内存泄漏</text>
  
  <text x="420" y="275" class="step-text" font-weight="bold">LifecycleHandler优势:</text>
  <text x="440" y="290" class="step-text">• 自动检查生命周期状态</text>
  <text x="440" y="305" class="step-text">• Activity销毁时自动停止处理消息</text>
  <text x="440" y="320" class="step-text">• 避免内存泄漏和崩溃</text>
  
  <!-- 连接线 -->
  <line x1="350" y1="180" x2="200" y2="150" class="lifecycle-arrow"/>
  <line x1="1250" y1="180" x2="1400" y2="150" class="lifecycle-arrow"/>
</svg>
