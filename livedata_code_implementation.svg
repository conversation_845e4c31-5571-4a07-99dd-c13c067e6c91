<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 8px; fill: #27ae60; }
      .comment { font-family: 'Courier New', monospace; font-size: 8px; fill: #7f8c8d; }
      .viewmodel-section { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .activity-section { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .ui-section { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .flow-section { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .code-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#codearrow); }
    </style>
    <marker id="codearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">LiveData + ViewModel UI更新代码实现详解</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">眼球运动评估模块中的具体代码示例</text>

  <!-- ViewModel实现部分 -->
  <rect x="50" y="70" width="650" height="280" class="viewmodel-section" rx="8"/>
  <text x="375" y="95" text-anchor="middle" class="subtitle">ViewModel层 - 数据状态管理与业务逻辑</text>

  <!-- ViewModel类定义 -->
  <rect x="70" y="110" width="300" height="230" class="viewmodel-section" rx="5"/>
  <text x="220" y="130" text-anchor="middle" class="subtitle">SaccadeAbilityViewModel</text>
  <text x="80" y="150" class="code">class SaccadeAbilityViewModel : ViewModel() {</text>
  <text x="90" y="165" class="comment">// LiveData声明 - 管理UI状态</text>
  <text x="90" y="180" class="code">val uploadImageResultLiveData = </text>
  <text x="100" y="195" class="code">  MutableLiveData&lt;FileUploadResponse?&gt;()</text>
  <text x="90" y="215" class="code">val submitResultLiveData = </text>
  <text x="100" y="230" class="code">  MutableLiveData&lt;SaccadeAbilityAdd?&gt;()</text>
  <text x="90" y="250" class="code">val isLoadingLiveData = </text>
  <text x="100" y="265" class="code">  MutableLiveData&lt;Boolean&gt;(false)</text>
  <text x="90" y="285" class="comment">// Repository依赖注入</text>
  <text x="90" y="300" class="code">private val repository = SaccadeAbilityRepository()</text>
  <text x="80" y="320" class="code">}</text>

  <!-- 业务方法实现 -->
  <rect x="390" y="110" width="290" height="230" class="viewmodel-section" rx="5"/>
  <text x="535" y="130" text-anchor="middle" class="subtitle">业务方法实现</text>
  <text x="400" y="150" class="code">fun uploadImage(bitmap: Bitmap) {</text>
  <text x="410" y="165" class="comment">// 设置加载状态</text>
  <text x="410" y="180" class="code">isLoadingLiveData.postValue(true)</text>
  <text x="410" y="200" class="comment">// 启动协程处理异步操作</text>
  <text x="410" y="215" class="code">viewModelScope.launch {</text>
  <text x="420" y="230" class="code">try {</text>
  <text x="430" y="245" class="comment">// 调用Repository上传图片</text>
  <text x="430" y="260" class="code">val result = repository.uploadImage(bitmap)</text>
  <text x="430" y="275" class="comment">// 更新LiveData状态</text>
  <text x="430" y="290" class="code">uploadImageResultLiveData.postValue(result)</text>
  <text x="420" y="305" class="code">} catch (e: Exception) {</text>
  <text x="430" y="320" class="code">uploadImageResultLiveData.postValue(null)</text>
  <text x="420" y="335" class="code">} finally { isLoadingLiveData.postValue(false) }</text>

  <!-- Activity实现部分 -->
  <rect x="720" y="70" width="630" height="280" class="activity-section" rx="8"/>
  <text x="1035" y="95" text-anchor="middle" class="subtitle">Activity层 - Observer注册与UI更新逻辑</text>

  <!-- Observer注册 -->
  <rect x="740" y="110" width="290" height="230" class="activity-section" rx="5"/>
  <text x="885" y="130" text-anchor="middle" class="subtitle">Observer注册</text>
  <text x="750" y="150" class="code">private fun initViewModelObserver() {</text>
  <text x="760" y="165" class="comment">// 观察图片上传结果</text>
  <text x="760" y="180" class="code">viewModel.uploadImageResultLiveData</text>
  <text x="770" y="195" class="code">.observe(this, Observer { result -></text>
  <text x="780" y="210" class="code">if (result?.data != null) {</text>
  <text x="790" y="225" class="comment">// 上传成功，继续提交数据</text>
  <text x="790" y="240" class="code">submitDataToServer(result.data.url)</text>
  <text x="780" y="255" class="code">} else {</text>
  <text x="790" y="270" class="comment">// 上传失败，显示错误</text>
  <text x="790" y="285" class="code">showErrorMessage("图片上传失败")</text>
  <text x="780" y="300" class="code">}</text>
  <text x="770" y="315" class="code">})</text>
  <text x="750" y="330" class="code">}</text>

  <!-- UI更新方法 -->
  <rect x="1050" y="110" width="280" height="230" class="activity-section" rx="5"/>
  <text x="1190" y="130" text-anchor="middle" class="subtitle">UI更新方法</text>
  <text x="1060" y="150" class="code">// 加载状态观察</text>
  <text x="1060" y="165" class="code">viewModel.isLoadingLiveData</text>
  <text x="1070" y="180" class="code">.observe(this) { isLoading -></text>
  <text x="1080" y="195" class="code">progressBar.isVisible = isLoading</text>
  <text x="1080" y="210" class="code">btnSubmit.isEnabled = !isLoading</text>
  <text x="1080" y="225" class="code">btnSubmit.text = if (isLoading) {</text>
  <text x="1090" y="240" class="code">"上传中..." } else { "上传图片" }</text>
  <text x="1070" y="255" class="code">}</text>
  <text x="1060" y="275" class="comment">// 提交结果观察</text>
  <text x="1060" y="290" class="code">viewModel.submitResultLiveData</text>
  <text x="1070" y="305" class="code">.observe(this) { result -></text>
  <text x="1080" y="320" class="code">showResultToast(result)</text>
  <text x="1070" y="335" class="code">}</text>

  <!-- UI组件更新示例 -->
  <rect x="50" y="370" width="1300" height="200" class="ui-section" rx="8"/>
  <text x="700" y="395" text-anchor="middle" class="subtitle">具体UI组件更新实现</text>

  <!-- Toast更新 -->
  <rect x="70" y="410" width="200" height="140" class="ui-section" rx="5"/>
  <text x="170" y="430" text-anchor="middle" class="subtitle">Toast消息更新</text>
  <text x="80" y="450" class="code">private fun showResultToast(</text>
  <text x="90" y="465" class="code">result: SaccadeAbilityAdd?</text>
  <text x="80" y="480" class="code">) {</text>
  <text x="90" y="495" class="code">val message = if (result != null) {</text>
  <text x="100" y="510" class="code">"数据提交成功"</text>
  <text x="90" y="525" class="code">} else {</text>
  <text x="100" y="540" class="code">"数据提交失败，请重试"</text>
  <text x="90" y="555" class="code">}</text>
  <text x="90" y="570" class="code">Toast.makeText(this, message,</text>
  <text x="100" y="585" class="code">Toast.LENGTH_SHORT).show()</text>
  <text x="80" y="600" class="code">}</text>

  <!-- ProgressBar更新 -->
  <rect x="290" y="410" width="200" height="140" class="ui-section" rx="5"/>
  <text x="390" y="430" text-anchor="middle" class="subtitle">ProgressBar状态</text>
  <text x="300" y="450" class="code">// 在Observer中直接更新</text>
  <text x="300" y="465" class="code">viewModel.isLoadingLiveData</text>
  <text x="310" y="480" class="code">.observe(this) { isLoading -></text>
  <text x="320" y="495" class="code">if (isLoading) {</text>
  <text x="330" y="510" class="code">progressBar.visibility = </text>
  <text x="340" y="525" class="code">View.VISIBLE</text>
  <text x="330" y="540" class="code">progressBar.show()</text>
  <text x="320" y="555" class="code">} else {</text>
  <text x="330" y="570" class="code">progressBar.visibility = </text>
  <text x="340" y="585" class="code">View.GONE</text>
  <text x="320" y="600" class="code">}</text>
  <text x="310" y="615" class="code">}</text>

  <!-- Canvas重绘 -->
  <rect x="510" y="410" width="200" height="140" class="ui-section" rx="5"/>
  <text x="610" y="430" text-anchor="middle" class="subtitle">Canvas重绘更新</text>
  <text x="520" y="450" class="code">// 评估结果数据观察</text>
  <text x="520" y="465" class="code">viewModel.gazePointsLiveData</text>
  <text x="530" y="480" class="code">.observe(this) { gazePoints -></text>
  <text x="540" y="495" class="code">evaluateResultView</text>
  <text x="550" y="510" class="code">.updateGazePoints(gazePoints)</text>
  <text x="540" y="525" class="comment">// 触发onDraw()重绘</text>
  <text x="540" y="540" class="code">evaluateResultView.invalidate()</text>
  <text x="540" y="555" class="comment">// 生成结果图片</text>
  <text x="540" y="570" class="code">val bitmap = evaluateResultView</text>
  <text x="550" y="585" class="code">.drawToBitmap()</text>
  <text x="540" y="600" class="code">viewModel.uploadImage(bitmap)</text>
  <text x="530" y="615" class="code">}</text>

  <!-- RecyclerView更新 -->
  <rect x="730" y="410" width="200" height="140" class="ui-section" rx="5"/>
  <text x="830" y="430" text-anchor="middle" class="subtitle">RecyclerView列表</text>
  <text x="740" y="450" class="code">// 患者列表数据观察</text>
  <text x="740" y="465" class="code">patientViewModel.patientListLiveData</text>
  <text x="750" y="480" class="code">.observe(this) { patientList -></text>
  <text x="760" y="495" class="code">patientAdapter.submitList(patientList)</text>
  <text x="760" y="510" class="code">if (patientList.isEmpty()) {</text>
  <text x="770" y="525" class="code">tvEmptyView.visibility = </text>
  <text x="780" y="540" class="code">View.VISIBLE</text>
  <text x="770" y="555" class="code">recyclerView.visibility = </text>
  <text x="780" y="570" class="code">View.GONE</text>
  <text x="760" y="585" class="code">} else {</text>
  <text x="770" y="600" class="code">// 显示列表，隐藏空视图</text>
  <text x="760" y="615" class="code">}</text>
  <text x="750" y="630" class="code">}</text>

  <!-- Button状态更新 -->
  <rect x="950" y="410" width="200" height="140" class="ui-section" rx="5"/>
  <text x="1050" y="430" text-anchor="middle" class="subtitle">Button状态控制</text>
  <text x="960" y="450" class="code">// 多个LiveData组合观察</text>
  <text x="960" y="465" class="code">MediatorLiveData&lt;Boolean&gt;().apply {</text>
  <text x="970" y="480" class="code">addSource(viewModel.isLoadingLiveData)</text>
  <text x="980" y="495" class="code">{ updateButtonState() }</text>
  <text x="970" y="510" class="code">addSource(viewModel.currentPatientLiveData)</text>
  <text x="980" y="525" class="code">{ updateButtonState() }</text>
  <text x="960" y="540" class="code">}.observe(this) { canSubmit -></text>
  <text x="970" y="555" class="code">btnSubmit.isEnabled = canSubmit</text>
  <text x="970" y="570" class="code">btnSubmit.alpha = if (canSubmit) </text>
  <text x="980" y="585" class="code">1.0f else 0.5f</text>
  <text x="960" y="600" class="code">}</text>

  <!-- 数据流箭头 -->
  <line x1="375" y1="350" x2="1035" y2="370" class="code-arrow"/>
  <text x="700" y="365" text-anchor="middle" class="text" fill="#ff9800">LiveData.postValue() → Observer.onChanged()</text>

  <!-- 错误处理和最佳实践 -->
  <rect x="50" y="590" width="1300" height="120" class="flow-section" rx="8"/>
  <text x="700" y="615" text-anchor="middle" class="subtitle">错误处理和最佳实践</text>
  
  <text x="70" y="640" class="text">🚨 <tspan class="subtitle">异常处理</tspan>: try-catch包装Repository调用，确保异常不会导致应用崩溃，通过LiveData传递错误状态</text>
  <text x="70" y="660" class="text">🔄 <tspan class="subtitle">状态管理</tspan>: 使用isLoadingLiveData管理加载状态，避免重复请求，提供良好的用户体验</text>
  <text x="70" y="680" class="text">⚡ <tspan class="subtitle">性能优化</tspan>: 使用MediatorLiveData组合多个数据源，避免不必要的UI更新，合理使用observe vs observeForever</text>
  <text x="70" y="700" class="text">🛡️ <tspan class="subtitle">内存安全</tspan>: 确保Observer在正确的LifecycleOwner中注册，避免在onDestroy后继续接收回调</text>

  <!-- 架构优势总结 -->
  <rect x="50" y="730" width="1300" height="100" class="viewmodel-section" rx="8"/>
  <text x="700" y="755" text-anchor="middle" class="subtitle">LiveData + ViewModel UI更新架构优势</text>
  
  <text x="70" y="780" class="text">📊 <tspan class="subtitle">数据驱动</tspan>: UI完全由数据状态驱动，消除手动状态管理的复杂性和错误</text>
  <text x="70" y="800" class="text">🔄 <tspan class="subtitle">响应式</tspan>: 数据变化自动触发UI更新，实现真正的响应式编程模式</text>
  <text x="70" y="820" class="text">🎯 <tspan class="subtitle">可测试</tspan>: ViewModel业务逻辑与UI分离，便于单元测试和集成测试</text>

</svg>
