<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #27ae60; }
      .step-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .livedata-circle { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .viewmodel-rect { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .activity-rect { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .flow-arrow { stroke: #e91e63; stroke-width: 3; fill: none; marker-end: url(#flowarrow); }
      .observe-arrow { stroke: #2196f3; stroke-width: 2; fill: none; stroke-dasharray: 8,4; marker-end: url(#observearrow); }
      .data-arrow { stroke: #ff9800; stroke-width: 2; fill: none; marker-end: url(#dataarrow); }
    </style>
    <marker id="flowarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e91e63" />
    </marker>
    <marker id="observearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
    </marker>
    <marker id="dataarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">LiveData &amp; ViewModel 交互流程详解</text>
  <text x="800" y="55" text-anchor="middle" class="subtitle">以扫视能力评估为例</text>

  <!-- 时间轴 -->
  <line x1="100" y1="100" x2="100" y2="1100" stroke="#bdc3c7" stroke-width="3"/>
  <text x="50" y="110" class="subtitle">时间轴</text>

  <!-- 步骤1: 用户触发评估 -->
  <circle cx="100" cy="150" r="8" fill="#e91e63"/>
  <rect x="150" y="120" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="140" text-anchor="middle" class="subtitle">步骤1: 用户触发扫视能力评估</text>
  <text x="160" y="160" class="code">SaccadeAbilityEvaluatingFragment.startEvaluating()</text>
  <text x="160" y="175" class="text">• 用户点击开始评估按钮</text>

  <!-- Fragment详细信息 -->
  <rect x="600" y="120" width="350" height="60" class="activity-rect" rx="5"/>
  <text x="775" y="140" text-anchor="middle" class="subtitle">Fragment 处理逻辑</text>
  <text x="610" y="160" class="code">sendMessageToService(MSG_START_TRACK)</text>
  <text x="610" y="175" class="text">• 与GazeTrackService建立通信</text>

  <!-- 步骤2: 数据收集 -->
  <circle cx="100" cy="250" r="8" fill="#e91e63"/>
  <rect x="150" y="220" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="240" text-anchor="middle" class="subtitle">步骤2: 实时数据收集</text>
  <text x="160" y="260" class="code">parseMessage(MSG_GAZE_TRAJECTORY_RESULT)</text>
  <text x="160" y="275" class="text">• 接收眼动轨迹数据</text>

  <!-- 数据结构 -->
  <rect x="600" y="220" width="350" height="60" class="livedata-circle" rx="5"/>
  <text x="775" y="240" text-anchor="middle" class="subtitle">GazeTrajectory 数据结构</text>
  <text x="610" y="260" class="code">List&lt;GazePoint&gt; gazePoints</text>
  <text x="610" y="275" class="text">• x, y, duration, timestamp</text>

  <!-- 步骤3: ViewModel初始化 -->
  <circle cx="100" cy="350" r="8" fill="#e91e63"/>
  <rect x="150" y="320" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="340" text-anchor="middle" class="subtitle">步骤3: ViewModel 初始化</text>
  <text x="160" y="360" class="code">ViewModelProvider(this)[SaccadeAbilityViewModel::class.java]</text>
  <text x="160" y="375" class="text">• Activity创建ViewModel实例</text>

  <!-- ViewModel详细信息 -->
  <rect x="600" y="320" width="450" height="60" class="viewmodel-rect" rx="5"/>
  <text x="825" y="340" text-anchor="middle" class="subtitle">SaccadeAbilityViewModel 初始化</text>
  <text x="610" y="360" class="code">val submitResultLiveData = MutableLiveData&lt;SaccadeAbilityAdd?&gt;()</text>
  <text x="610" y="375" class="code">val uploadImageResultLiveData = MutableLiveData&lt;FileUploadResponse?&gt;()</text>

  <!-- 步骤4: LiveData观察者注册 -->
  <circle cx="100" cy="450" r="8" fill="#e91e63"/>
  <rect x="150" y="420" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="440" text-anchor="middle" class="subtitle">步骤4: 注册LiveData观察者</text>
  <text x="160" y="460" class="code">viewModel.uploadImageResultLiveData.observe(this, Observer { })</text>
  <text x="160" y="475" class="text">• Activity订阅数据变化</text>

  <!-- Observer详细代码 -->
  <rect x="600" y="420" width="500" height="60" class="activity-rect" rx="5"/>
  <text x="850" y="440" text-anchor="middle" class="subtitle">Observer 回调实现</text>
  <text x="610" y="460" class="code">Observer { result -> if (result?.data != null) submitDataToServer(result.data.url) }</text>
  <text x="610" y="475" class="text">• 图片上传成功后触发数据提交</text>

  <!-- 步骤5: 图片上传触发 -->
  <circle cx="100" cy="550" r="8" fill="#e91e63"/>
  <rect x="150" y="520" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="540" text-anchor="middle" class="subtitle">步骤5: 触发图片上传</text>
  <text x="160" y="560" class="code">viewModel.uploadImage(bitmap)</text>
  <text x="160" y="575" class="text">• 评估完成后上传结果图片</text>

  <!-- ViewModel处理逻辑 -->
  <rect x="600" y="520" width="500" height="60" class="viewmodel-rect" rx="5"/>
  <text x="850" y="540" text-anchor="middle" class="subtitle">ViewModel 协程处理</text>
  <text x="610" y="560" class="code">viewModelScope.launch { val result = repository.uploadImage(file) }</text>
  <text x="610" y="575" class="text">• 在IO线程执行网络请求</text>

  <!-- 步骤6: 网络请求执行 -->
  <circle cx="100" cy="650" r="8" fill="#e91e63"/>
  <rect x="150" y="620" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="640" text-anchor="middle" class="subtitle">步骤6: Repository 网络请求</text>
  <text x="160" y="660" class="code">saccadeAbilityRepository.uploadImage(multipartBody)</text>
  <text x="160" y="675" class="text">• Retrofit API调用</text>

  <!-- Repository实现 -->
  <rect x="600" y="620" width="500" height="60" class="activity-rect" rx="5"/>
  <text x="850" y="640" text-anchor="middle" class="subtitle">Repository 实现细节</text>
  <text x="610" y="660" class="code">@POST("/api/files/upload/image") suspend fun uploadImage(@Part file: MultipartBody.Part)</text>
  <text x="610" y="675" class="text">• 文件上传到服务器</text>

  <!-- 步骤7: LiveData数据更新 -->
  <circle cx="100" cy="750" r="8" fill="#e91e63"/>
  <rect x="150" y="720" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="740" text-anchor="middle" class="subtitle">步骤7: LiveData 数据发布</text>
  <text x="160" y="760" class="code">uploadImageResultLiveData.postValue(result)</text>
  <text x="160" y="775" class="text">• ViewModel更新LiveData</text>

  <!-- LiveData机制 -->
  <ellipse cx="850" cy="750" rx="200" ry="30" class="livedata-circle"/>
  <text x="850" y="745" text-anchor="middle" class="subtitle">LiveData 通知机制</text>
  <text x="850" y="760" text-anchor="middle" class="text">自动通知所有活跃观察者</text>

  <!-- 步骤8: UI更新 -->
  <circle cx="100" cy="850" r="8" fill="#e91e63"/>
  <rect x="150" y="820" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="840" text-anchor="middle" class="subtitle">步骤8: UI 自动更新</text>
  <text x="160" y="860" class="code">Observer回调执行 -> submitDataToServerWithImage(url)</text>
  <text x="160" y="875" class="text">• 主线程安全的UI更新</text>

  <!-- UI更新详情 -->
  <rect x="600" y="820" width="500" height="60" class="activity-rect" rx="5"/>
  <text x="850" y="840" text-anchor="middle" class="subtitle">UI 更新逻辑</text>
  <text x="610" y="860" class="code">if (result?.data != null) { submitDataToServerWithImage(result.data.url) }</text>
  <text x="610" y="875" class="text">• 根据上传结果决定后续操作</text>

  <!-- 步骤9: 链式数据提交 -->
  <circle cx="100" cy="950" r="8" fill="#e91e63"/>
  <rect x="150" y="920" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="940" text-anchor="middle" class="subtitle">步骤9: 链式数据提交</text>
  <text x="160" y="960" class="code">viewModel.submitSaccadeAbilityResult(..., imageUrl)</text>
  <text x="160" y="975" class="text">• 图片URL作为参数提交评估数据</text>

  <!-- 最终结果 -->
  <rect x="600" y="920" width="500" height="60" class="viewmodel-rect" rx="5"/>
  <text x="850" y="940" text-anchor="middle" class="subtitle">最终数据提交</text>
  <text x="610" y="960" class="code">submitResultLiveData.postValue(result) -> Toast.makeText("提交成功")</text>
  <text x="610" y="975" class="text">• 用户看到成功提示</text>

  <!-- 步骤10: 完成 -->
  <circle cx="100" cy="1050" r="8" fill="#4caf50"/>
  <rect x="150" y="1020" width="400" height="60" class="step-box" rx="8"/>
  <text x="350" y="1040" text-anchor="middle" class="subtitle">步骤10: 流程完成</text>
  <text x="160" y="1060" class="code">Toast.makeText(this, "数据提交成功", Toast.LENGTH_SHORT).show()</text>
  <text x="160" y="1075" class="text">• 用户收到成功反馈</text>

  <!-- 生命周期管理说明 -->
  <rect x="1200" y="120" width="350" height="200" class="viewmodel-rect" rx="8"/>
  <text x="1375" y="145" text-anchor="middle" class="subtitle">生命周期管理优势</text>
  <text x="1210" y="170" class="text">🔄 <tspan class="code">viewModelScope</tspan> 自动管理协程</text>
  <text x="1210" y="190" class="text">📱 <tspan class="code">LifecycleOwner</tspan> 感知Activity状态</text>
  <text x="1210" y="210" class="text">🛡️ Activity销毁时自动取消观察</text>
  <text x="1210" y="230" class="text">⚡ 避免内存泄漏和空指针异常</text>
  <text x="1210" y="250" class="text">🎯 只在STARTED/RESUMED状态更新UI</text>
  <text x="1210" y="270" class="text">🔗 配置变更时自动重新订阅</text>
  <text x="1210" y="290" class="text">📊 数据持久化，横竖屏切换不丢失</text>
  <text x="1210" y="310" class="text">🚀 主线程安全，无需手动切换线程</text>

  <!-- 错误处理机制 -->
  <rect x="1200" y="350" width="350" height="180" class="activity-rect" rx="8"/>
  <text x="1375" y="375" text-anchor="middle" class="subtitle">collectResponse 错误处理</text>
  <text x="1210" y="400" class="code">MutableStateFlow(repository.submit()).collectResponse {</text>
  <text x="1220" y="420" class="code">onSuccess = { result, _, _ -></text>
  <text x="1230" y="435" class="code">liveData.postValue(result)</text>
  <text x="1220" y="450" class="code">}</text>
  <text x="1220" y="470" class="code">onFailed = { errorCode, errorMsg -></text>
  <text x="1230" y="485" class="code">Logger.e(TAG, "提交失败: $errorMsg")</text>
  <text x="1230" y="500" class="code">liveData.postValue(null)</text>
  <text x="1220" y="515" class="code">}</text>
  <text x="1210" y="530" class="code">}</text>

  <!-- 数据流箭头 -->
  <path d="M 550 150 Q 700 100 850 150" class="flow-arrow"/>
  <path d="M 550 250 Q 700 200 850 250" class="flow-arrow"/>
  <path d="M 550 350 Q 700 300 850 350" class="flow-arrow"/>
  <path d="M 550 450 Q 700 400 850 450" class="observe-arrow"/>
  <path d="M 550 550 Q 700 500 850 550" class="flow-arrow"/>
  <path d="M 550 650 Q 700 600 850 650" class="flow-arrow"/>
  <path d="M 850 720 Q 700 700 550 750" class="data-arrow"/>
  <path d="M 550 850 Q 700 800 850 850" class="flow-arrow"/>

  <!-- 时间轴连接线 -->
  <line x1="100" y1="150" x2="100" y2="250" class="flow-arrow"/>
  <line x1="100" y1="250" x2="100" y2="350" class="flow-arrow"/>
  <line x1="100" y1="350" x2="100" y2="450" class="flow-arrow"/>
  <line x1="100" y1="450" x2="100" y2="550" class="flow-arrow"/>
  <line x1="100" y1="550" x2="100" y2="650" class="flow-arrow"/>
  <line x1="100" y1="650" x2="100" y2="750" class="flow-arrow"/>
  <line x1="100" y1="750" x2="100" y2="850" class="data-arrow"/>
  <line x1="100" y1="850" x2="100" y2="950" class="flow-arrow"/>
  <line x1="100" y1="950" x2="100" y2="1050" class="flow-arrow"/>

</svg>
