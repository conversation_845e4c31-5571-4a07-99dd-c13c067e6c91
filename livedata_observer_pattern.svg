<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .livedata-box { fill: #fff8e1; stroke: #ff8f00; stroke-width: 3; }
      .observer-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .lifecycle-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .data-flow { stroke: #ff5722; stroke-width: 3; fill: none; marker-end: url(#dataflow); }
      .observe-flow { stroke: #4caf50; stroke-width: 2; fill: none; stroke-dasharray: 6,3; marker-end: url(#observeflow); }
      .lifecycle-flow { stroke: #2196f3; stroke-width: 2; fill: none; stroke-dasharray: 4,2; marker-end: url(#lifecycleflow); }
    </style>
    <marker id="dataflow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ff5722" />
    </marker>
    <marker id="observeflow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
    <marker id="lifecycleflow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">LiveData 观察者模式 &amp; 生命周期管理</text>
  <text x="700" y="50" text-anchor="middle" class="subtitle">眼球运动评估模块中的数据绑定机制</text>

  <!-- LiveData核心 -->
  <ellipse cx="700" cy="150" rx="150" ry="80" class="livedata-box"/>
  <text x="700" y="130" text-anchor="middle" class="subtitle">LiveData&lt;T&gt;</text>
  <text x="700" y="150" text-anchor="middle" class="code">MutableLiveData&lt;SaccadeAbilityAdd?&gt;</text>
  <text x="700" y="170" text-anchor="middle" class="text">• 数据持有者</text>
  <text x="700" y="185" text-anchor="middle" class="text">• 生命周期感知</text>
  <text x="700" y="200" text-anchor="middle" class="text">• 线程安全</text>

  <!-- Observer 1: SaccadeAbilityEvaluateResultActivity -->
  <rect x="50" y="300" width="300" height="120" class="observer-box" rx="8"/>
  <text x="200" y="325" text-anchor="middle" class="subtitle">Observer 1: ResultActivity</text>
  <text x="60" y="345" class="code">viewModel.uploadImageResultLiveData</text>
  <text x="60" y="360" class="code">  .observe(this, Observer { result -></text>
  <text x="70" y="375" class="code">    if (result?.data != null) {</text>
  <text x="80" y="390" class="code">      submitDataToServer(result.data.url)</text>
  <text x="70" y="405" class="code">    }</text>
  <text x="60" y="420" class="code">  })</text>

  <!-- Observer 2: Fragment -->
  <rect x="400" y="300" width="300" height="120" class="observer-box" rx="8"/>
  <text x="550" y="325" text-anchor="middle" class="subtitle">Observer 2: Fragment</text>
  <text x="410" y="345" class="code">viewModel.submitResultLiveData</text>
  <text x="410" y="360" class="code">  .observe(this, Observer { result -></text>
  <text x="420" y="375" class="code">    if (result != null) {</text>
  <text x="430" y="390" class="code">      Toast.makeText("提交成功")</text>
  <text x="420" y="405" class="code">    }</text>
  <text x="410" y="420" class="code">  })</text>

  <!-- Observer 3: 其他组件 -->
  <rect x="750" y="300" width="300" height="120" class="observer-box" rx="8"/>
  <text x="900" y="325" text-anchor="middle" class="subtitle">Observer 3: 其他组件</text>
  <text x="760" y="345" class="code">patientViewModel.currentPatientLiveData</text>
  <text x="760" y="360" class="code">  .observe(this, Observer { patient -></text>
  <text x="770" y="375" class="code">    updatePatientInfo(patient)</text>
  <text x="770" y="390" class="code">    refreshUI()</text>
  <text x="760" y="405" class="code">  })</text>

  <!-- LifecycleOwner -->
  <rect x="1100" y="300" width="250" height="120" class="lifecycle-box" rx="8"/>
  <text x="1225" y="325" text-anchor="middle" class="subtitle">LifecycleOwner</text>
  <text x="1110" y="345" class="text">🔄 CREATED</text>
  <text x="1110" y="360" class="text">▶️ STARTED</text>
  <text x="1110" y="375" class="text">⏸️ RESUMED</text>
  <text x="1110" y="390" class="text">⏹️ PAUSED</text>
  <text x="1110" y="405" class="text">❌ DESTROYED</text>

  <!-- ViewModel数据源 -->
  <rect x="50" y="500" width="600" height="150" class="livedata-box" rx="8"/>
  <text x="350" y="525" text-anchor="middle" class="subtitle">ViewModel 数据发布机制</text>
  
  <!-- postValue示例 -->
  <rect x="70" y="540" width="250" height="90" class="observer-box" rx="5"/>
  <text x="195" y="560" text-anchor="middle" class="subtitle">postValue() - 后台线程</text>
  <text x="80" y="580" class="code">viewModelScope.launch {</text>
  <text x="90" y="595" class="code">  val result = repository.upload()</text>
  <text x="90" y="610" class="code">  liveData.postValue(result)</text>
  <text x="80" y="625" class="code">}</text>

  <!-- setValue示例 -->
  <rect x="340" y="540" width="250" height="90" class="observer-box" rx="5"/>
  <text x="465" y="560" text-anchor="middle" class="subtitle">setValue() - 主线程</text>
  <text x="350" y="580" class="code">// 主线程中直接设置</text>
  <text x="350" y="595" class="code">currentPatientLiveData</text>
  <text x="360" y="610" class="code">  .setValue(newPatient)</text>
  <text x="350" y="625" class="code">// 立即通知观察者</text>

  <!-- 生命周期管理详解 -->
  <rect x="700" y="500" width="650" height="150" class="lifecycle-box" rx="8"/>
  <text x="1025" y="525" text-anchor="middle" class="subtitle">生命周期感知机制</text>
  
  <text x="720" y="550" class="text">📱 <tspan class="code">STARTED/RESUMED</tspan> 状态时:</text>
  <text x="730" y="565" class="text">✅ Observer回调正常执行</text>
  <text x="730" y="580" class="text">🔄 UI更新生效</text>
  
  <text x="720" y="600" class="text">⏸️ <tspan class="code">PAUSED/STOPPED</tspan> 状态时:</text>
  <text x="730" y="615" class="text">⏸️ Observer回调暂停</text>
  <text x="730" y="630" class="text">💾 数据缓存保留</text>
  
  <text x="1000" y="550" class="text">❌ <tspan class="code">DESTROYED</tspan> 状态时:</text>
  <text x="1010" y="565" class="text">🗑️ 自动移除Observer</text>
  <text x="1010" y="580" class="text">🛡️ 避免内存泄漏</text>
  
  <text x="1000" y="600" class="text">🔄 <tspan class="code">配置变更</tspan> 时:</text>
  <text x="1010" y="615" class="text">💾 ViewModel数据保持</text>
  <text x="1010" y="630" class="text">🔗 自动重新订阅</text>

  <!-- 数据流转示例 -->
  <rect x="50" y="700" width="1300" height="150" class="livedata-box" rx="8"/>
  <text x="700" y="725" text-anchor="middle" class="subtitle">完整数据流转示例</text>
  
  <!-- 步骤流程 -->
  <rect x="70" y="740" width="200" height="80" class="observer-box" rx="5"/>
  <text x="170" y="760" text-anchor="middle" class="subtitle">1. 数据产生</text>
  <text x="80" y="780" class="code">repository.uploadImage()</text>
  <text x="80" y="795" class="text">• 网络请求完成</text>
  <text x="80" y="810" class="text">• 返回结果数据</text>

  <rect x="290" y="740" width="200" height="80" class="observer-box" rx="5"/>
  <text x="390" y="760" text-anchor="middle" class="subtitle">2. ViewModel处理</text>
  <text x="300" y="780" class="code">liveData.postValue(result)</text>
  <text x="300" y="795" class="text">• 协程中安全发布</text>
  <text x="300" y="810" class="text">• 切换到主线程</text>

  <rect x="510" y="740" width="200" height="80" class="observer-box" rx="5"/>
  <text x="610" y="760" text-anchor="middle" class="subtitle">3. LiveData分发</text>
  <text x="520" y="780" class="code">notifyObservers()</text>
  <text x="520" y="795" class="text">• 检查生命周期状态</text>
  <text x="520" y="810" class="text">• 通知活跃观察者</text>

  <rect x="730" y="740" width="200" height="80" class="observer-box" rx="5"/>
  <text x="830" y="760" text-anchor="middle" class="subtitle">4. Observer回调</text>
  <text x="740" y="780" class="code">observer.onChanged()</text>
  <text x="740" y="795" class="text">• 主线程执行</text>
  <text x="740" y="810" class="text">• 更新UI组件</text>

  <rect x="950" y="740" width="200" height="80" class="observer-box" rx="5"/>
  <text x="1050" y="760" text-anchor="middle" class="subtitle">5. UI更新</text>
  <text x="960" y="780" class="code">updateViews(data)</text>
  <text x="960" y="795" class="text">• 界面刷新</text>
  <text x="960" y="810" class="text">• 用户看到变化</text>

  <!-- 连接线 -->
  <!-- LiveData到Observers -->
  <line x1="600" y1="200" x2="200" y2="300" class="data-flow"/>
  <line x1="650" y1="220" x2="550" y2="300" class="data-flow"/>
  <line x1="750" y1="220" x2="900" y2="300" class="data-flow"/>

  <!-- LifecycleOwner到Observers -->
  <line x1="1100" y1="360" x2="350" y2="360" class="lifecycle-flow"/>
  <line x1="1100" y1="360" x2="550" y2="360" class="lifecycle-flow"/>
  <line x1="1100" y1="360" x2="900" y2="360" class="lifecycle-flow"/>

  <!-- ViewModel到LiveData -->
  <line x1="350" y1="500" x2="650" y2="230" class="data-flow"/>

  <!-- 数据流转步骤连接 -->
  <line x1="270" y1="780" x2="290" y2="780" class="data-flow"/>
  <line x1="490" y1="780" x2="510" y2="780" class="data-flow"/>
  <line x1="710" y1="780" x2="730" y2="780" class="data-flow"/>
  <line x1="930" y1="780" x2="950" y2="780" class="data-flow"/>

  <!-- 观察者模式说明 -->
  <text x="200" y="280" class="text" fill="#4caf50">observe()</text>
  <text x="550" y="280" class="text" fill="#4caf50">observe()</text>
  <text x="900" y="280" class="text" fill="#4caf50">observe()</text>

  <text x="500" y="250" class="text" fill="#ff5722">postValue()</text>
  <text x="400" y="480" class="text" fill="#ff5722">setValue()</text>

  <text x="750" y="340" class="text" fill="#2196f3">lifecycle-aware</text>

</svg>
