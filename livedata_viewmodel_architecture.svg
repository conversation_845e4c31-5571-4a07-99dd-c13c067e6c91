<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .viewmodel-box { fill: #e8f4fd; stroke: #2196f3; stroke-width: 2; }
      .livedata-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .activity-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .repository-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .network-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #ff9800; stroke-width: 3; fill: none; marker-end: url(#orangearrow); }
      .observe-flow { stroke: #2196f3; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#bluearrow); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="orangearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff9800" />
    </marker>
    <marker id="bluearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">LiveData &amp; ViewModel 在眼球运动评估中的架构图</text>

  <!-- ViewModel层 -->
  <rect x="50" y="80" width="600" height="200" class="viewmodel-box" rx="8"/>
  <text x="350" y="105" text-anchor="middle" class="subtitle">ViewModel Layer (数据管理层)</text>
  
  <!-- SaccadeAbilityViewModel -->
  <rect x="70" y="120" width="170" height="140" class="viewmodel-box" rx="5"/>
  <text x="155" y="140" text-anchor="middle" class="subtitle">SaccadeAbilityViewModel</text>
  <text x="80" y="160" class="code">val submitResultLiveData</text>
  <text x="80" y="175" class="code">val uploadImageResultLiveData</text>
  <text x="80" y="195" class="text">• 管理扫视能力评估数据</text>
  <text x="80" y="210" class="text">• 处理图片上传逻辑</text>
  <text x="80" y="225" class="text">• 协程生命周期管理</text>
  <text x="80" y="240" class="code">viewModelScope.launch { }</text>

  <!-- EMPatientViewModel -->
  <rect x="260" y="120" width="170" height="140" class="viewmodel-box" rx="5"/>
  <text x="345" y="140" text-anchor="middle" class="subtitle">EMPatientViewModel</text>
  <text x="270" y="160" class="code">val currentPatientLiveData</text>
  <text x="270" y="175" class="code">val patientListLiveData</text>
  <text x="270" y="195" class="text">• 患者信息状态管理</text>
  <text x="270" y="210" class="text">• 患者列表数据处理</text>
  <text x="270" y="225" class="text">• 数据持久化管理</text>
  <text x="270" y="240" class="code">setCurrentPatient(patient)</text>

  <!-- GazeStabilityViewModel -->
  <rect x="450" y="120" width="170" height="140" class="viewmodel-box" rx="5"/>
  <text x="535" y="140" text-anchor="middle" class="subtitle">GazeStabilityViewModel</text>
  <text x="460" y="160" class="code">val submitResultLiveData</text>
  <text x="460" y="175" class="code">val uploadImageResultLiveData</text>
  <text x="460" y="195" class="text">• 注视稳定性数据管理</text>
  <text x="460" y="210" class="text">• 轨迹分析处理</text>
  <text x="460" y="225" class="text">• 结果图片上传</text>
  <text x="460" y="240" class="code">submitGazeStabilityResult()</text>

  <!-- LiveData数据流 -->
  <rect x="750" y="80" width="600" height="200" class="livedata-box" rx="8"/>
  <text x="1050" y="105" text-anchor="middle" class="subtitle">LiveData 数据流转机制</text>

  <!-- LiveData特性 -->
  <rect x="770" y="120" width="260" height="140" class="livedata-box" rx="5"/>
  <text x="900" y="140" text-anchor="middle" class="subtitle">LiveData 核心特性</text>
  <text x="780" y="160" class="text">🔄 <tspan class="code">observe(lifecycleOwner, observer)</tspan></text>
  <text x="780" y="175" class="text">📱 生命周期感知 (Lifecycle-aware)</text>
  <text x="780" y="190" class="text">🛡️ 内存泄漏防护</text>
  <text x="780" y="205" class="text">🎯 自动UI更新</text>
  <text x="780" y="220" class="text">📊 <tspan class="code">postValue() / setValue()</tspan></text>
  <text x="780" y="235" class="text">🔗 数据绑定 (Data Binding)</text>
  <text x="780" y="250" class="text">⚡ 主线程安全</text>

  <!-- collectResponse机制 -->
  <rect x="1050" y="120" width="280" height="140" class="livedata-box" rx="5"/>
  <text x="1190" y="140" text-anchor="middle" class="subtitle">collectResponse 错误处理</text>
  <text x="1060" y="160" class="code">MutableStateFlow(repository.submit())</text>
  <text x="1060" y="175" class="code">.collectResponse {</text>
  <text x="1070" y="190" class="code">  onSuccess = { result, _, _ -></text>
  <text x="1080" y="205" class="code">    liveData.postValue(result)</text>
  <text x="1070" y="220" class="code">  }</text>
  <text x="1070" y="235" class="code">  onFailed = { code, msg -></text>
  <text x="1080" y="250" class="code">    liveData.postValue(null)</text>
  <text x="1070" y="265" class="code">  }</text>

  <!-- Activity/Fragment层 -->
  <rect x="50" y="320" width="600" height="180" class="activity-box" rx="8"/>
  <text x="350" y="345" text-anchor="middle" class="subtitle">Activity/Fragment Layer (UI层)</text>

  <!-- SaccadeAbilityEvaluateResultActivity -->
  <rect x="70" y="360" width="250" height="120" class="activity-box" rx="5"/>
  <text x="195" y="380" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultActivity</text>
  <text x="80" y="400" class="code">private fun initViewModelObserver() {</text>
  <text x="90" y="415" class="code">  viewModel.uploadImageResultLiveData</text>
  <text x="100" y="430" class="code">    .observe(this) { result -></text>
  <text x="110" y="445" class="code">      updateUI(result)</text>
  <text x="100" y="460" class="code">    }</text>
  <text x="80" y="475" class="code">}</text>

  <!-- Fragment -->
  <rect x="340" y="360" width="280" height="120" class="activity-box" rx="5"/>
  <text x="480" y="380" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingFragment</text>
  <text x="350" y="400" class="text">• 与GazeTrackService通信</text>
  <text x="350" y="415" class="text">• 收集评估数据</text>
  <text x="350" y="430" class="text">• 通过LiveEventBus传递结果</text>
  <text x="350" y="445" class="code">LiveEventBus.get&lt;Result&gt;().post(data)</text>
  <text x="350" y="460" class="text">• 触发Canvas绘制更新</text>

  <!-- Repository层 -->
  <rect x="750" y="320" width="600" height="180" class="repository-box" rx="8"/>
  <text x="1050" y="345" text-anchor="middle" class="subtitle">Repository Layer (数据源层)</text>

  <!-- SaccadeAbilityRepository -->
  <rect x="770" y="360" width="180" height="120" class="repository-box" rx="5"/>
  <text x="860" y="380" text-anchor="middle" class="subtitle">SaccadeAbilityRepository</text>
  <text x="780" y="400" class="code">suspend fun submitResult()</text>
  <text x="780" y="415" class="code">suspend fun uploadImage()</text>
  <text x="780" y="430" class="text">• API接口调用</text>
  <text x="780" y="445" class="text">• 数据格式转换</text>
  <text x="780" y="460" class="text">• 网络异常处理</text>

  <!-- EMPatientRepository -->
  <rect x="970" y="360" width="180" height="120" class="repository-box" rx="5"/>
  <text x="1060" y="380" text-anchor="middle" class="subtitle">EMPatientRepository</text>
  <text x="980" y="400" class="code">suspend fun addPatient()</text>
  <text x="980" y="415" class="code">suspend fun getPatientList()</text>
  <text x="980" y="430" class="text">• 患者数据CRUD</text>
  <text x="980" y="445" class="text">• 本地缓存管理</text>
  <text x="980" y="460" class="text">• 数据同步逻辑</text>

  <!-- 网络层 -->
  <rect x="50" y="540" width="1300" height="120" class="network-box" rx="8"/>
  <text x="700" y="565" text-anchor="middle" class="subtitle">Network Layer (网络层)</text>

  <!-- API Services -->
  <rect x="70" y="580" width="200" height="60" class="network-box" rx="5"/>
  <text x="170" y="600" text-anchor="middle" class="subtitle">EMPatientApiService</text>
  <text x="80" y="620" class="code">@POST("/api/movement/patient")</text>
  <text x="80" y="635" class="code">suspend fun addPatient()</text>

  <rect x="290" y="580" width="200" height="60" class="network-box" rx="5"/>
  <text x="390" y="600" text-anchor="middle" class="subtitle">SaccadeAbilityApiService</text>
  <text x="300" y="620" class="code">@POST("/api/movement/saccade")</text>
  <text x="300" y="635" class="code">suspend fun submitResult()</text>

  <rect x="510" y="580" width="200" height="60" class="network-box" rx="5"/>
  <text x="610" y="600" text-anchor="middle" class="subtitle">FileUploadApiService</text>
  <text x="520" y="620" class="code">@POST("/api/files/upload")</text>
  <text x="520" y="635" class="code">suspend fun uploadImage()</text>

  <rect x="730" y="580" width="200" height="60" class="network-box" rx="5"/>
  <text x="830" y="600" text-anchor="middle" class="subtitle">MovementClient</text>
  <text x="740" y="620" class="text">• Retrofit配置</text>
  <text x="740" y="635" class="text">• 基础URL: 10.100.2.254:8080</text>

  <rect x="950" y="580" width="200" height="60" class="network-box" rx="5"/>
  <text x="1050" y="600" text-anchor="middle" class="subtitle">MainRetrofitClient</text>
  <text x="960" y="620" class="text">• 通用网络配置</text>
  <text x="960" y="635" class="text">• SSL证书处理</text>

  <!-- 数据流转示例 -->
  <rect x="50" y="700" width="1300" height="250" class="livedata-box" rx="8"/>
  <text x="700" y="725" text-anchor="middle" class="subtitle">数据流转完整示例 - 扫视能力评估</text>

  <!-- 步骤流程 -->
  <text x="70" y="750" class="subtitle">1. 用户交互触发</text>
  <text x="80" y="770" class="code">Fragment收集评估数据 → LiveEventBus.post(result)</text>

  <text x="70" y="800" class="subtitle">2. ViewModel处理</text>
  <text x="80" y="820" class="code">viewModel.uploadImage(bitmap) → viewModelScope.launch { repository.uploadImage() }</text>

  <text x="70" y="850" class="subtitle">3. LiveData通知</text>
  <text x="80" y="870" class="code">uploadImageResultLiveData.postValue(result) → Activity.observe() → UI更新</text>

  <text x="70" y="900" class="subtitle">4. 链式处理</text>
  <text x="80" y="920" class="code">图片上传成功 → 提交评估数据 → submitResultLiveData.postValue() → Toast提示</text>

  <!-- 连接线和数据流 -->
  <!-- ViewModel到LiveData数据发布 -->
  <line x1="350" y1="280" x2="900" y2="320" class="data-flow"/>
  <text x="600" y="305" class="text" fill="#ff9800">postValue()</text>

  <!-- LiveData到Activity观察更新 -->
  <line x1="900" y1="320" x2="350" y2="360" class="observe-flow"/>
  <text x="600" y="345" class="text" fill="#2196f3">observe()</text>

  <!-- ViewModel到Repository调用 -->
  <line x1="350" y1="280" x2="860" y2="360" class="arrow"/>
  <text x="580" y="325" class="text">suspend fun</text>

  <!-- Repository到Network请求 -->
  <line x1="860" y1="480" x2="390" y2="580" class="arrow"/>
  <text x="600" y="535" class="text">Retrofit API</text>

  <!-- 生命周期管理标注 -->
  <rect x="1200" y="700" width="180" height="100" class="viewmodel-box" rx="5"/>
  <text x="1290" y="720" text-anchor="middle" class="subtitle">生命周期管理</text>
  <text x="1210" y="740" class="text">🔄 viewModelScope</text>
  <text x="1210" y="755" class="text">📱 LifecycleOwner</text>
  <text x="1210" y="770" class="text">🛡️ 自动取消协程</text>
  <text x="1210" y="785" class="text">⚡ 内存泄漏防护</text>

  <!-- 错误处理流程 -->
  <rect x="1200" y="820" width="180" height="100" class="network-box" rx="5"/>
  <text x="1290" y="840" text-anchor="middle" class="subtitle">错误处理机制</text>
  <text x="1210" y="860" class="text">❌ onFailed</text>
  <text x="1210" y="875" class="text">⚠️ onDataEmpty</text>
  <text x="1210" y="890" class="text">🔄 onError</text>
  <text x="1210" y="905" class="text">✅ onSuccess</text>

</svg>
