<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .step-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .viewmodel-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .livedata-box { fill: #fff8e1; stroke: #ffc107; stroke-width: 3; }
      .observer-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .ui-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .lifecycle-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .data-flow { stroke: #ff9800; stroke-width: 4; fill: none; marker-end: url(#dataflow); }
      .observe-flow { stroke: #4caf50; stroke-width: 3; fill: none; stroke-dasharray: 8,4; marker-end: url(#observeflow); }
      .ui-update { stroke: #9c27b0; stroke-width: 3; fill: none; marker-end: url(#uiupdate); }
      .lifecycle-flow { stroke: #f44336; stroke-width: 2; fill: none; stroke-dasharray: 6,3; marker-end: url(#lifecycle); }
    </style>
    <marker id="dataflow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ff9800" />
    </marker>
    <marker id="observeflow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4caf50" />
    </marker>
    <marker id="uiupdate" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#9c27b0" />
    </marker>
    <marker id="lifecycle" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f44336" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">LiveData + ViewModel 实现UI更新机制详解</text>
  <text x="800" y="50" text-anchor="middle" class="subtitle">眼球运动评估模块中的响应式UI更新流程</text>

  <!-- ViewModel核心区域 -->
  <rect x="50" y="80" width="500" height="200" class="viewmodel-box" rx="10"/>
  <text x="300" y="105" text-anchor="middle" class="subtitle">ViewModel - 数据状态管理中心</text>

  <!-- SaccadeAbilityViewModel -->
  <rect x="70" y="120" width="220" height="140" class="viewmodel-box" rx="5"/>
  <text x="180" y="140" text-anchor="middle" class="subtitle">SaccadeAbilityViewModel</text>
  <text x="80" y="160" class="code">class SaccadeAbilityViewModel : ViewModel() {</text>
  <text x="90" y="175" class="code">  val uploadImageResultLiveData = </text>
  <text x="100" y="190" class="code">    MutableLiveData&lt;FileUploadResponse?&gt;()</text>
  <text x="90" y="210" class="code">  val submitResultLiveData = </text>
  <text x="100" y="225" class="code">    MutableLiveData&lt;SaccadeAbilityAdd?&gt;()</text>
  <text x="90" y="245" class="code">  // 业务逻辑处理...</text>
  <text x="80" y="260" class="code">}</text>

  <!-- 协程处理 -->
  <rect x="310" y="120" width="220" height="140" class="viewmodel-box" rx="5"/>
  <text x="420" y="140" text-anchor="middle" class="subtitle">协程异步处理</text>
  <text x="320" y="160" class="code">fun uploadImage(bitmap: Bitmap) {</text>
  <text x="330" y="175" class="code">  viewModelScope.launch {</text>
  <text x="340" y="190" class="code">    try {</text>
  <text x="350" y="205" class="code">      val result = repository.upload()</text>
  <text x="350" y="220" class="code">      uploadImageResultLiveData</text>
  <text x="360" y="235" class="code">        .postValue(result)</text>
  <text x="340" y="250" class="code">    } catch (e: Exception) { ... }</text>
  <text x="330" y="265" class="code">  }</text>
  <text x="320" y="280" class="code">}</text>

  <!-- LiveData核心区域 -->
  <rect x="600" y="80" width="450" height="200" class="livedata-box" rx="10"/>
  <text x="825" y="105" text-anchor="middle" class="subtitle">LiveData - 响应式数据发布中心</text>

  <!-- LiveData特性 -->
  <rect x="620" y="120" width="200" height="140" class="livedata-box" rx="5"/>
  <text x="720" y="140" text-anchor="middle" class="subtitle">LiveData核心特性</text>
  <text x="630" y="160" class="text">🔄 <tspan class="code">postValue()</tspan> 线程安全</text>
  <text x="630" y="175" class="text">📱 生命周期感知</text>
  <text x="630" y="190" class="text">🎯 自动UI更新通知</text>
  <text x="630" y="205" class="text">🛡️ 内存泄漏防护</text>
  <text x="630" y="220" class="text">📊 数据状态保持</text>
  <text x="630" y="235" class="text">⚡ 主线程切换</text>
  <text x="630" y="250" class="text">🔗 观察者模式实现</text>

  <!-- 数据发布机制 -->
  <rect x="840" y="120" width="190" height="140" class="livedata-box" rx="5"/>
  <text x="935" y="140" text-anchor="middle" class="subtitle">数据发布机制</text>
  <text x="850" y="160" class="code">// 后台线程安全发布</text>
  <text x="850" y="175" class="code">liveData.postValue(newData)</text>
  <text x="850" y="195" class="code">// 主线程直接设置</text>
  <text x="850" y="210" class="code">liveData.setValue(newData)</text>
  <text x="850" y="230" class="code">// 自动通知所有观察者</text>
  <text x="850" y="245" class="code">notifyActiveObservers()</text>

  <!-- Observer注册区域 -->
  <rect x="1100" y="80" width="450" height="200" class="observer-box" rx="10"/>
  <text x="1325" y="105" text-anchor="middle" class="subtitle">Observer - 观察者注册与回调</text>

  <!-- Activity Observer -->
  <rect x="1120" y="120" width="200" height="140" class="observer-box" rx="5"/>
  <text x="1220" y="140" text-anchor="middle" class="subtitle">Activity Observer注册</text>
  <text x="1130" y="160" class="code">private fun initViewModelObserver() {</text>
  <text x="1140" y="175" class="code">  viewModel.uploadImageResultLiveData</text>
  <text x="1150" y="190" class="code">    .observe(this, Observer { result -></text>
  <text x="1160" y="205" class="code">      if (result?.data != null) {</text>
  <text x="1170" y="220" class="code">        submitDataToServer(result.data.url)</text>
  <text x="1160" y="235" class="code">      } else {</text>
  <text x="1170" y="250" class="code">        showErrorMessage()</text>
  <text x="1160" y="265" class="code">      }</text>
  <text x="1150" y="280" class="code">    })</text>

  <!-- Fragment Observer -->
  <rect x="1340" y="120" width="190" height="140" class="observer-box" rx="5"/>
  <text x="1435" y="140" text-anchor="middle" class="subtitle">Fragment Observer</text>
  <text x="1350" y="160" class="code">viewModel.submitResultLiveData</text>
  <text x="1360" y="175" class="code">  .observe(this) { result -></text>
  <text x="1370" y="190" class="code">    when {</text>
  <text x="1380" y="205" class="code">      result != null -> {</text>
  <text x="1390" y="220" class="code">        showSuccessToast()</text>
  <text x="1380" y="235" class="code">      }</text>
  <text x="1380" y="250" class="code">      else -> showErrorToast()</text>
  <text x="1370" y="265" class="code">    }</text>
  <text x="1360" y="280" class="code">  }</text>

  <!-- UI更新区域 -->
  <rect x="50" y="320" width="1500" height="180" class="ui-box" rx="10"/>
  <text x="800" y="345" text-anchor="middle" class="subtitle">UI组件更新机制</text>

  <!-- Toast更新 -->
  <rect x="70" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="170" y="380" text-anchor="middle" class="subtitle">Toast消息提示</text>
  <text x="80" y="400" class="code">Observer { result -></text>
  <text x="90" y="415" class="code">  if (result != null) {</text>
  <text x="100" y="430" class="code">    Toast.makeText(this,</text>
  <text x="110" y="445" class="code">      "数据提交成功",</text>
  <text x="110" y="460" class="code">      Toast.LENGTH_SHORT).show()</text>
  <text x="90" y="475" class="code">  }</text>
  <text x="80" y="490" class="code">}</text>

  <!-- ProgressBar更新 -->
  <rect x="290" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="390" y="380" text-anchor="middle" class="subtitle">ProgressBar状态</text>
  <text x="300" y="400" class="code">Observer { isLoading -></text>
  <text x="310" y="415" class="code">  progressBar.isVisible = isLoading</text>
  <text x="310" y="430" class="code">  if (isLoading) {</text>
  <text x="320" y="445" class="code">    progressBar.show()</text>
  <text x="310" y="460" class="code">  } else {</text>
  <text x="320" y="475" class="code">    progressBar.hide()</text>
  <text x="310" y="490" class="code">  }</text>
  <text x="300" y="505" class="code">}</text>

  <!-- TextView更新 -->
  <rect x="510" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="610" y="380" text-anchor="middle" class="subtitle">TextView内容更新</text>
  <text x="520" y="400" class="code">Observer { patient -></text>
  <text x="530" y="415" class="code">  tvPatientName.text = </text>
  <text x="540" y="430" class="code">    patient?.name ?: "未选择患者"</text>
  <text x="530" y="445" class="code">  tvPatientAge.text = </text>
  <text x="540" y="460" class="code">    "年龄: ${patient?.age ?: "--"}"</text>
  <text x="530" y="475" class="code">  updatePatientInfo(patient)</text>
  <text x="520" y="490" class="code">}</text>

  <!-- Canvas重绘 -->
  <rect x="730" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="830" y="380" text-anchor="middle" class="subtitle">Canvas重绘更新</text>
  <text x="740" y="400" class="code">Observer { gazePoints -></text>
  <text x="750" y="415" class="code">  evaluateResultView</text>
  <text x="760" y="430" class="code">    .drawResult(gazePoints)</text>
  <text x="750" y="445" class="code">  // 触发onDraw()重绘</text>
  <text x="750" y="460" class="code">  invalidate()</text>
  <text x="740" y="475" class="code">}</text>

  <!-- RecyclerView更新 -->
  <rect x="950" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="1050" y="380" text-anchor="middle" class="subtitle">RecyclerView列表</text>
  <text x="960" y="400" class="code">Observer { patientList -></text>
  <text x="970" y="415" class="code">  adapter.submitList(patientList)</text>
  <text x="970" y="430" class="code">  if (patientList.isEmpty()) {</text>
  <text x="980" y="445" class="code">    showEmptyView()</text>
  <text x="970" y="460" class="code">  } else {</text>
  <text x="980" y="475" class="code">    hideEmptyView()</text>
  <text x="970" y="490" class="code">  }</text>
  <text x="960" y="505" class="code">}</text>

  <!-- Button状态更新 -->
  <rect x="1170" y="360" width="200" height="120" class="ui-box" rx="5"/>
  <text x="1270" y="380" text-anchor="middle" class="subtitle">Button状态控制</text>
  <text x="1180" y="400" class="code">Observer { isSubmitting -></text>
  <text x="1190" y="415" class="code">  btnSubmit.isEnabled = !isSubmitting</text>
  <text x="1190" y="430" class="code">  btnSubmit.text = if (isSubmitting) {</text>
  <text x="1200" y="445" class="code">    "提交中..."</text>
  <text x="1190" y="460" class="code">  } else {</text>
  <text x="1200" y="475" class="code">    "提交数据"</text>
  <text x="1190" y="490" class="code">  }</text>
  <text x="1180" y="505" class="code">}</text>

  <!-- 生命周期管理区域 -->
  <rect x="50" y="520" width="1500" height="120" class="lifecycle-box" rx="10"/>
  <text x="800" y="545" text-anchor="middle" class="subtitle">生命周期感知机制</text>

  <!-- STARTED/RESUMED状态 -->
  <rect x="70" y="560" width="280" height="60" class="lifecycle-box" rx="5"/>
  <text x="210" y="580" text-anchor="middle" class="subtitle">STARTED/RESUMED 状态</text>
  <text x="80" y="600" class="text">✅ Observer回调正常执行</text>
  <text x="80" y="615" class="text">🔄 UI更新立即生效</text>

  <!-- PAUSED/STOPPED状态 -->
  <rect x="370" y="560" width="280" height="60" class="lifecycle-box" rx="5"/>
  <text x="510" y="580" text-anchor="middle" class="subtitle">PAUSED/STOPPED 状态</text>
  <text x="380" y="600" class="text">⏸️ Observer回调暂停</text>
  <text x="380" y="615" class="text">💾 数据状态保留</text>

  <!-- DESTROYED状态 -->
  <rect x="670" y="560" width="280" height="60" class="lifecycle-box" rx="5"/>
  <text x="810" y="580" text-anchor="middle" class="subtitle">DESTROYED 状态</text>
  <text x="680" y="600" class="text">🗑️ 自动移除Observer</text>
  <text x="680" y="615" class="text">🛡️ 避免内存泄漏</text>

  <!-- 配置变更 -->
  <rect x="970" y="560" width="280" height="60" class="lifecycle-box" rx="5"/>
  <text x="1110" y="580" text-anchor="middle" class="subtitle">配置变更 (横竖屏)</text>
  <text x="980" y="600" class="text">💾 ViewModel数据保持</text>
  <text x="980" y="615" class="text">🔗 自动重新订阅Observer</text>

  <!-- viewModelScope -->
  <rect x="1270" y="560" width="260" height="60" class="lifecycle-box" rx="5"/>
  <text x="1400" y="580" text-anchor="middle" class="subtitle">viewModelScope管理</text>
  <text x="1280" y="600" class="text">🧵 协程自动取消</text>
  <text x="1280" y="615" class="text">⚡ 防止内存泄漏</text>

  <!-- 数据流箭头 -->
  <!-- ViewModel到LiveData -->
  <line x1="550" y1="180" x2="600" y2="180" class="data-flow"/>
  <text x="575" y="170" text-anchor="middle" class="text" fill="#ff9800">postValue()</text>

  <!-- LiveData到Observer -->
  <line x1="1050" y1="180" x2="1100" y2="180" class="observe-flow"/>
  <text x="1075" y="170" text-anchor="middle" class="text" fill="#4caf50">observe()</text>

  <!-- Observer到UI -->
  <line x1="1325" y1="280" x2="800" y2="360" class="ui-update"/>
  <text x="1050" y="325" text-anchor="middle" class="text" fill="#9c27b0">UI更新</text>

  <!-- 生命周期管理箭头 -->
  <line x1="800" y1="520" x2="1220" y2="280" class="lifecycle-flow"/>
  <text x="1000" y="400" text-anchor="middle" class="text" fill="#f44336">生命周期控制</text>

  <!-- 详细步骤说明 -->
  <rect x="50" y="660" width="1500" height="200" class="viewmodel-box" rx="10"/>
  <text x="800" y="685" text-anchor="middle" class="subtitle">LiveData + ViewModel UI更新完整流程</text>

  <!-- 步骤1-3 -->
  <rect x="70" y="700" width="350" height="140" class="viewmodel-box" rx="5"/>
  <text x="245" y="720" text-anchor="middle" class="subtitle">步骤1-3: 数据产生与处理</text>
  <text x="80" y="740" class="step-text">1️⃣ <tspan class="code">用户操作触发</tspan></text>
  <text x="90" y="755" class="step-text">• Fragment收集评估数据</text>
  <text x="90" y="770" class="step-text">• 通过LiveEventBus传递到ResultActivity</text>
  <text x="80" y="790" class="step-text">2️⃣ <tspan class="code">ViewModel业务处理</tspan></text>
  <text x="90" y="805" class="step-text">• viewModelScope.launch启动协程</text>
  <text x="90" y="820" class="step-text">• Repository执行网络请求</text>
  <text x="80" y="840" class="step-text">3️⃣ <tspan class="code">数据状态更新</tspan></text>
  <text x="90" y="855" class="step-text">• collectResponse处理响应结果</text>

  <!-- 步骤4-6 -->
  <rect x="440" y="700" width="350" height="140" class="livedata-box" rx="5"/>
  <text x="615" y="720" text-anchor="middle" class="subtitle">步骤4-6: LiveData数据发布</text>
  <text x="450" y="740" class="step-text">4️⃣ <tspan class="code">LiveData.postValue()</tspan></text>
  <text x="460" y="755" class="step-text">• 后台线程安全发布数据</text>
  <text x="460" y="770" class="step-text">• 自动切换到主线程</text>
  <text x="450" y="790" class="step-text">5️⃣ <tspan class="code">生命周期检查</tspan></text>
  <text x="460" y="805" class="step-text">• 检查Observer的LifecycleOwner状态</text>
  <text x="460" y="820" class="step-text">• 只通知STARTED/RESUMED状态的观察者</text>
  <text x="450" y="840" class="step-text">6️⃣ <tspan class="code">通知所有活跃观察者</tspan></text>
  <text x="460" y="855" class="step-text">• 遍历观察者列表并执行回调</text>

  <!-- 步骤7-9 -->
  <rect x="810" y="700" width="350" height="140" class="observer-box" rx="5"/>
  <text x="985" y="720" text-anchor="middle" class="subtitle">步骤7-9: Observer回调执行</text>
  <text x="820" y="740" class="step-text">7️⃣ <tspan class="code">Observer.onChanged()</tspan></text>
  <text x="830" y="755" class="step-text">• 主线程中执行回调函数</text>
  <text x="830" y="770" class="step-text">• 接收最新的数据状态</text>
  <text x="820" y="790" class="step-text">8️⃣ <tspan class="code">业务逻辑判断</tspan></text>
  <text x="830" y="805" class="step-text">• 根据数据状态执行不同逻辑</text>
  <text x="830" y="820" class="step-text">• 成功/失败/加载中状态处理</text>
  <text x="820" y="840" class="step-text">9️⃣ <tspan class="code">触发UI组件更新</tspan></text>
  <text x="830" y="855" class="step-text">• 调用具体的UI更新方法</text>

  <!-- 步骤10-12 -->
  <rect x="1180" y="700" width="350" height="140" class="ui-box" rx="5"/>
  <text x="1355" y="720" text-anchor="middle" class="subtitle">步骤10-12: UI界面更新</text>
  <text x="1190" y="740" class="step-text">🔟 <tspan class="code">View属性更新</tspan></text>
  <text x="1200" y="755" class="step-text">• TextView.text、Button.isEnabled等</text>
  <text x="1200" y="770" class="step-text">• ProgressBar.visibility状态切换</text>
  <text x="1190" y="790" class="step-text">1️⃣1️⃣ <tspan class="code">复杂组件刷新</tspan></text>
  <text x="1200" y="805" class="step-text">• RecyclerView.adapter.notifyDataSetChanged()</text>
  <text x="1200" y="820" class="step-text">• Canvas.invalidate()触发重绘</text>
  <text x="1190" y="840" class="step-text">1️⃣2️⃣ <tspan class="code">用户感知更新</tspan></text>
  <text x="1200" y="855" class="step-text">• Toast消息显示、界面状态变化</text>

  <!-- 错误处理和优势说明 -->
  <rect x="50" y="880" width="1500" height="120" class="lifecycle-box" rx="10"/>
  <text x="800" y="905" text-anchor="middle" class="subtitle">LiveData + ViewModel UI更新机制优势</text>

  <text x="70" y="930" class="text">🎯 <tspan class="subtitle">数据驱动UI</tspan>: UI状态完全由数据状态决定，避免手动管理UI状态导致的不一致问题</text>
  <text x="70" y="950" class="text">🔄 <tspan class="subtitle">自动生命周期管理</tspan>: LiveData自动处理Activity/Fragment生命周期，避免在不合适的时机更新UI</text>
  <text x="70" y="970" class="text">⚡ <tspan class="subtitle">线程安全</tspan>: postValue()确保后台线程安全，自动切换到主线程执行UI更新，避免ANR</text>
  <text x="70" y="990" class="text">🛡️ <tspan class="subtitle">内存泄漏防护</tspan>: Observer自动在DESTROYED状态移除，viewModelScope自动取消协程</text>

</svg>
