<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .source-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .sender-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .bus-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .receiver-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .data-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .flow-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">LiveEventBus数据传递链完整流程</text>
  <text x="800" y="55" text-anchor="middle" class="description-text">INPUT_PARAM_TRACK_RESULT数据的来源与接收</text>
  
  <!-- 数据源头 -->
  <rect x="50" y="80" width="1500" height="200" class="source-box" rx="8"/>
  <text x="800" y="105" text-anchor="middle" class="subtitle">数据源头：ReadActivity阅读测试</text>
  
  <!-- ReadActivity数据生成 -->
  <rect x="80" y="130" width="450" height="130" class="data-box" rx="5"/>
  <text x="305" y="150" text-anchor="middle" class="step-title">1. ReadActivity - 数据生成</text>
  <text x="90" y="170" class="code-text">// 阅读测试完成后</text>
  <text x="90" y="185" class="code-text">GazeConstants.MSG_GAZE_TRAJECTORY_RESULT -> {</text>
  <text x="100" y="200" class="code-text">    val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)</text>
  <text x="100" y="215" class="code-text">    mGazeTrajectory = mGson.fromJson(json, GazeTrajectory::class.java)</text>
  <text x="100" y="230" class="code-text">    </text>
  <text x="100" y="245" class="code-text">    val readResult = ReadResult().apply {</text>
  <text x="110" y="260" class="code-text">        this.gazeTrajectory = mGazeTrajectory  // 轨迹数据</text>
  
  <!-- Native层数据来源 -->
  <rect x="550" y="130" width="450" height="130" class="source-box" rx="5"/>
  <text x="775" y="150" text-anchor="middle" class="step-title">数据来源：Native层算法</text>
  <text x="560" y="170" class="code-text">GazeTrackService收到MSG_GET_GAZE_TRAJECTORY:</text>
  <text x="560" y="185" class="code-text">↓</text>
  <text x="560" y="200" class="code-text">AppliedManager.getGazeTrajectory()</text>
  <text x="560" y="215" class="code-text">↓</text>
  <text x="560" y="230" class="code-text">GazeApplied.getGazeTrajectory(READING)</text>
  <text x="560" y="245" class="code-text">↓</text>
  <text x="560" y="260" class="code-text">Native层reading_model.get_record_jsondata()</text>
  
  <!-- 数据格式 -->
  <rect x="1020" y="130" width="450" height="130" class="data-box" rx="5"/>
  <text x="1245" y="150" text-anchor="middle" class="step-title">GazeTrajectory数据格式</text>
  <text x="1030" y="170" class="code-text">data class GazeTrajectory(</text>
  <text x="1040" y="185" class="code-text">    val gaze: List&lt;GazePoint&gt;? = null</text>
  <text x="1030" y="200" class="code-text">)</text>
  <text x="1030" y="220" class="code-text">data class GazePoint(</text>
  <text x="1040" y="235" class="code-text">    val x: Float?,      // 视线点X坐标[0~1]</text>
  <text x="1040" y="250" class="code-text">    val y: Float?,      // 视线点Y坐标[0~1]</text>
  <text x="1040" y="265" class="code-text">    val duration: Long?, // 持续时间(ms)</text>
  
  <!-- 数据传递链 -->
  <rect x="50" y="300" width="1500" height="250" class="sender-box" rx="8"/>
  <text x="800" y="325" text-anchor="middle" class="subtitle">数据传递链：ReadActivity → ReadResultAnalysisActivity → ReadTrackActivity</text>
  
  <!-- 步骤1: ReadActivity发送 -->
  <rect x="80" y="350" width="280" height="180" class="sender-box" rx="5"/>
  <text x="220" y="370" text-anchor="middle" class="step-title">1. ReadActivity发送</text>
  <text x="90" y="390" class="code-text">// 阅读测试完成，跳转到结果页</text>
  <text x="90" y="405" class="code-text">LiveEventBus.get&lt;ReadResult&gt;(</text>
  <text x="100" y="420" class="code-text">    INPUT_PARAM_READ_RESULT</text>
  <text x="90" y="435" class="code-text">).post(readResult)</text>
  <text x="90" y="450" class="code-text">startActivity(</text>
  <text x="100" y="465" class="code-text">    ReadResultAnalysisActivity</text>
  <text x="100" y="480" class="code-text">        .createIntent(this)</text>
  <text x="90" y="495" class="code-text">)</text>
  <text x="90" y="510" class="code-text">finish()</text>
  
  <!-- 步骤2: ReadResultAnalysisActivity接收和转发 -->
  <rect x="380" y="350" width="280" height="180" class="bus-box" rx="5"/>
  <text x="520" y="370" text-anchor="middle" class="step-title">2. 结果分析页接收转发</text>
  <text x="390" y="390" class="code-text">// 接收ReadResult</text>
  <text x="390" y="405" class="code-text">LiveEventBus.get&lt;ReadResult&gt;(</text>
  <text x="400" y="420" class="code-text">    INPUT_PARAM_READ_RESULT</text>
  <text x="390" y="435" class="code-text">).observeSticky(this) {</text>
  <text x="400" y="450" class="code-text">    mReadResult = it</text>
  <text x="390" y="465" class="code-text">}</text>
  <text x="390" y="485" class="code-text">// 用户点击"查看轨迹"</text>
  <text x="390" y="500" class="code-text">tvLookReadTrack.setOnSingleClickListener {</text>
  <text x="400" y="515" class="code-text">    // 转发轨迹数据</text>
  
  <!-- 步骤3: ReadTrackActivity接收 -->
  <rect x="680" y="350" width="280" height="180" class="receiver-box" rx="5"/>
  <text x="820" y="370" text-anchor="middle" class="step-title">3. ReadTrackActivity接收</text>
  <text x="690" y="390" class="code-text">// 监听轨迹数据</text>
  <text x="690" y="405" class="code-text">LiveEventBus.get&lt;GazeTrajectory&gt;(</text>
  <text x="700" y="420" class="code-text">    INPUT_PARAM_TRACK_RESULT</text>
  <text x="690" y="435" class="code-text">).observeSticky(this) { trajectory -></text>
  <text x="700" y="450" class="code-text">    mGazeTrajectory = trajectory</text>
  <text x="700" y="465" class="code-text">    // 默认显示静态轨迹</text>
  <text x="700" y="480" class="code-text">    rgTools.check(R.id.rb_track_figure)</text>
  <text x="690" y="495" class="code-text">}</text>
  
  <!-- 关键代码 -->
  <rect x="980" y="350" width="500" height="180" class="data-box" rx="5"/>
  <text x="1230" y="370" text-anchor="middle" class="step-title">关键代码：数据转发</text>
  <text x="990" y="390" class="code-text">// ReadResultAnalysisActivity.kt 第113行</text>
  <text x="990" y="405" class="code-text">tvLookReadTrack.setOnSingleClickListener {</text>
  <text x="1000" y="420" class="code-text">    LiveEventBus.get&lt;GazeTrajectory&gt;(INPUT_PARAM_TRACK_RESULT)</text>
  <text x="1010" y="435" class="code-text">        .post(mReadResult?.gazeTrajectory)  // 🎯 关键转发</text>
  <text x="1000" y="450" class="code-text">    startActivity(ReadTrackActivity.createIntent(this))</text>
  <text x="990" y="465" class="code-text">}</text>
  <text x="990" y="485" class="step-text" font-weight="bold">说明:</text>
  <text x="990" y="500" class="step-text">• mReadResult来自ReadActivity的完整测试结果</text>
  <text x="990" y="515" class="step-text">• gazeTrajectory是其中的轨迹数据部分</text>
  
  <!-- 其他使用INPUT_PARAM_TRACK_RESULT的地方 -->
  <rect x="50" y="570" width="1500" height="300" class="flow-box" rx="8"/>
  <text x="800" y="595" text-anchor="middle" class="subtitle">其他使用INPUT_PARAM_TRACK_RESULT的场景</text>
  
  <!-- 眼动检查模块 -->
  <rect x="80" y="620" width="450" height="230" class="receiver-box" rx="5"/>
  <text x="305" y="640" text-anchor="middle" class="step-title">眼动检查模块 (不同的Key)</text>
  <text x="90" y="660" class="step-text" font-weight="bold">注意：眼动检查使用不同的Key</text>
  <text x="90" y="680" class="code-text">// ROI检测</text>
  <text x="90" y="695" class="code-text">LiveEventBus.get&lt;List&lt;GazePoint&gt;&gt;(</text>
  <text x="100" y="710" class="code-text">    INPUT_PARAM_EVALUATE_RESULT  // 不同的Key</text>
  <text x="90" y="725" class="code-text">).post(mGazeTrajectory?.gaze ?: emptyList())</text>
  
  <text x="90" y="745" class="code-text">// 追随能力评估</text>
  <text x="90" y="760" class="code-text">LiveEventBus.get&lt;FollowAbilityEvaluateResult&gt;(</text>
  <text x="100" y="775" class="code-text">    INPUT_PARAM_EVALUATE_RESULT  // 不同的Key</text>
  <text x="90" y="790" class="code-text">).post(followAbilityEvaluateResult)</text>
  
  <text x="90" y="810" class="code-text">// 扫视能力评估</text>
  <text x="90" y="825" class="code-text">LiveEventBus.get&lt;SaccadeAbilityEvaluateResult&gt;(</text>
  <text x="100" y="840" class="code-text">    INPUT_PARAM_EVALUATE_RESULT  // 不同的Key</text>
  
  <!-- Key常量定义 -->
  <rect x="550" y="620" width="450" height="230" class="data-box" rx="5"/>
  <text x="775" y="640" text-anchor="middle" class="step-title">Key常量定义位置</text>
  <text x="560" y="660" class="code-text">// ReadTrackActivity.kt 第29行</text>
  <text x="560" y="675" class="code-text">companion object {</text>
  <text x="570" y="690" class="code-text">    const val INPUT_PARAM_TRACK_RESULT = "trackResult"</text>
  <text x="560" y="705" class="code-text">}</text>
  
  <text x="560" y="725" class="code-text">// ReadResultAnalysisActivity.kt 第13行</text>
  <text x="560" y="740" class="code-text">import com.mitdd.gazetracker.read.ReadTrackActivity.Companion.INPUT_PARAM_TRACK_RESULT</text>
  
  <text x="560" y="760" class="step-text" font-weight="bold">专用于阅读轨迹查看:</text>
  <text x="560" y="775" class="step-text">• INPUT_PARAM_TRACK_RESULT 专门用于阅读轨迹</text>
  <text x="560" y="790" class="step-text">• INPUT_PARAM_EVALUATE_RESULT 用于眼动检查</text>
  <text x="560" y="805" class="step-text">• 不同模块使用不同的Key避免冲突</text>
  
  <!-- 数据类型对比 -->
  <rect x="1020" y="620" width="450" height="230" class="source-box" rx="5"/>
  <text x="1245" y="640" text-anchor="middle" class="step-title">数据类型对比</text>
  <text x="1030" y="660" class="step-text" font-weight="bold">阅读模块:</text>
  <text x="1030" y="675" class="code-text">LiveEventBus.get&lt;GazeTrajectory&gt;(INPUT_PARAM_TRACK_RESULT)</text>
  <text x="1030" y="690" class="step-text">→ 完整的GazeTrajectory对象</text>
  
  <text x="1030" y="710" class="step-text" font-weight="bold">ROI检测:</text>
  <text x="1030" y="725" class="code-text">LiveEventBus.get&lt;List&lt;GazePoint&gt;&gt;(INPUT_PARAM_EVALUATE_RESULT)</text>
  <text x="1030" y="740" class="step-text">→ 只有GazePoint列表</text>
  
  <text x="1030" y="760" class="step-text" font-weight="bold">追随/扫视:</text>
  <text x="1030" y="775" class="code-text">LiveEventBus.get&lt;EvaluateResult&gt;(INPUT_PARAM_EVALUATE_RESULT)</text>
  <text x="1030" y="790" class="step-text">→ 包含额外分析数据的结果对象</text>
  
  <text x="1030" y="810" class="step-text" font-weight="bold">设计原因:</text>
  <text x="1030" y="825" class="step-text">• 不同模块需要不同格式的数据</text>
  <text x="1030" y="840" class="step-text">• 避免数据类型冲突和混淆</text>
  
  <!-- 完整数据流向图 -->
  <rect x="50" y="890" width="1500" height="200" class="flow-box" rx="8"/>
  <text x="800" y="915" text-anchor="middle" class="subtitle">完整数据流向图</text>
  
  <!-- 流程步骤 -->
  <rect x="80" y="940" width="120" height="60" class="source-box" rx="3"/>
  <text x="140" y="960" text-anchor="middle" class="step-text">Native层</text>
  <text x="140" y="975" text-anchor="middle" class="step-text">算法处理</text>
  <text x="140" y="990" text-anchor="middle" class="code-text">JSON数据</text>
  
  <rect x="220" y="940" width="120" height="60" class="data-box" rx="3"/>
  <text x="280" y="960" text-anchor="middle" class="step-text">ReadActivity</text>
  <text x="280" y="975" text-anchor="middle" class="step-text">数据接收</text>
  <text x="280" y="990" text-anchor="middle" class="code-text">GazeTrajectory</text>
  
  <rect x="360" y="940" width="120" height="60" class="sender-box" rx="3"/>
  <text x="420" y="960" text-anchor="middle" class="step-text">ReadResult</text>
  <text x="420" y="975" text-anchor="middle" class="step-text">封装发送</text>
  <text x="420" y="990" text-anchor="middle" class="code-text">LiveEventBus</text>
  
  <rect x="500" y="940" width="120" height="60" class="bus-box" rx="3"/>
  <text x="560" y="960" text-anchor="middle" class="step-text">Analysis</text>
  <text x="560" y="975" text-anchor="middle" class="step-text">Activity</text>
  <text x="560" y="990" text-anchor="middle" class="code-text">中转处理</text>
  
  <rect x="640" y="940" width="120" height="60" class="sender-box" rx="3"/>
  <text x="700" y="960" text-anchor="middle" class="step-text">用户点击</text>
  <text x="700" y="975" text-anchor="middle" class="step-text">查看轨迹</text>
  <text x="700" y="990" text-anchor="middle" class="code-text">转发数据</text>
  
  <rect x="780" y="940" width="120" height="60" class="receiver-box" rx="3"/>
  <text x="840" y="960" text-anchor="middle" class="step-text">ReadTrack</text>
  <text x="840" y="975" text-anchor="middle" class="step-text">Activity</text>
  <text x="840" y="990" text-anchor="middle" class="code-text">接收显示</text>
  
  <rect x="920" y="940" width="120" height="60" class="receiver-box" rx="3"/>
  <text x="980" y="960" text-anchor="middle" class="step-text">ReadTrackView</text>
  <text x="980" y="975" text-anchor="middle" class="step-text">Canvas绘制</text>
  <text x="980" y="990" text-anchor="middle" class="code-text">轨迹显示</text>
  
  <!-- 流程箭头 -->
  <line x1="200" y1="970" x2="220" y2="970" class="data-arrow"/>
  <line x1="340" y1="970" x2="360" y2="970" class="data-arrow"/>
  <line x1="480" y1="970" x2="500" y2="970" class="data-arrow"/>
  <line x1="620" y1="970" x2="640" y2="970" class="data-arrow"/>
  <line x1="760" y1="970" x2="780" y2="970" class="data-arrow"/>
  <line x1="900" y1="970" x2="920" y2="970" class="data-arrow"/>
  
  <!-- 主要传递箭头 -->
  <line x1="360" y1="530" x2="380" y2="530" class="data-arrow"/>
  <line x1="660" y1="530" x2="680" y2="530" class="data-arrow"/>
  
  <!-- 总结 -->
  <rect x="50" y="1110" width="1500" height="250" class="flow-box" rx="8"/>
  <text x="800" y="1135" text-anchor="middle" class="subtitle">数据传递链总结</text>
  
  <text x="80" y="1160" class="step-text" font-weight="bold">🎯 数据来源:</text>
  <text x="100" y="1175" class="step-text">• Native层reading_model算法处理后的JSON数据</text>
  <text x="100" y="1190" class="step-text">• 通过GazeTrackService → AppliedManager → GazeApplied传递到ReadActivity</text>
  
  <text x="80" y="1210" class="step-text" font-weight="bold">🔄 传递路径:</text>
  <text x="100" y="1225" class="step-text">ReadActivity → ReadResultAnalysisActivity → ReadTrackActivity</text>
  <text x="100" y="1240" class="step-text">使用LiveEventBus的INPUT_PARAM_TRACK_RESULT作为Key</text>
  
  <text x="80" y="1260" class="step-text" font-weight="bold">📱 接收页面:</text>
  <text x="100" y="1275" class="step-text">• ReadTrackActivity.initObserver()监听INPUT_PARAM_TRACK_RESULT</text>
  <text x="100" y="1290" class="step-text">• 使用observeSticky确保数据不丢失</text>
  <text x="100" y="1305" class="step-text">• 接收到数据后自动显示静态轨迹</text>
  
  <text x="80" y="1325" class="step-text" font-weight="bold">🔑 关键特点:</text>
  <text x="100" y="1340" class="step-text">• 粘性事件确保Activity启动后能接收到之前发送的数据</text>
  <text x="100" y="1355" class="step-text">• 专用Key避免与其他模块的数据冲突</text>
</svg>
