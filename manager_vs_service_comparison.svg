<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="managerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="utilityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1700" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">GazeTrackingManager vs GazeTrackService 详细对比</text>
  
  <!-- 核心区别概述 -->
  <rect x="50" y="60" width="1700" height="100" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心区别：三个不同层次的组件</text>
  <text x="70" y="110" class="text">• GazeTrackingManager：工具类，负责模型文件管理和全局配置</text>
  <text x="70" y="130" class="text">• TrackingManager：算法管理器，直接控制C++算法和图像处理</text>
  <text x="70" y="150" class="text">• GazeTrackService：Android服务，提供生命周期管理和进程间通信</text>
  
  <!-- 三个组件对比 -->
  <rect x="50" y="180" width="1700" height="450" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="205" text-anchor="middle" class="subtitle">三个组件详细对比</text>
  
  <!-- GazeTrackingManager -->
  <rect x="80" y="230" width="500" height="380" rx="8" fill="url(#utilityGradient)" stroke="#ff9800" stroke-width="2"/>
  <text x="330" y="255" text-anchor="middle" class="subtitle">GazeTrackingManager</text>
  <text x="330" y="275" text-anchor="middle" class="highlight">工具类 (Utility)</text>
  
  <text x="100" y="300" class="highlight">主要职责：</text>
  <text x="120" y="320" class="text">• 模型文件管理</text>
  <text x="120" y="340" class="text">• 全局配置管理</text>
  <text x="120" y="360" class="text">• 初始化工具方法</text>
  
  <text x="100" y="385" class="highlight">核心方法：</text>
  <text x="120" y="405" class="code">initGazeTracking(context)</text>
  <text x="120" y="425" class="code">copyModel2Dir(context)</text>
  <text x="120" y="445" class="code">getDisplayViewpoint()</text>
  <text x="120" y="465" class="code">setDisplayViewpoint(isShow)</text>
  
  <text x="100" y="490" class="highlight">特点：</text>
  <text x="120" y="510" class="text">• object 单例</text>
  <text x="120" y="530" class="text">• 无状态管理</text>
  <text x="120" y="550" class="text">• 纯工具性质</text>
  <text x="120" y="570" class="text">• 应用级别配置</text>
  <text x="120" y="590" class="text">• 不直接参与算法</text>
  
  <!-- TrackingManager -->
  <rect x="620" y="230" width="500" height="380" rx="8" fill="url(#managerGradient)" stroke="#2196f3" stroke-width="2"/>
  <text x="870" y="255" text-anchor="middle" class="subtitle">TrackingManager</text>
  <text x="870" y="275" text-anchor="middle" class="highlight">算法管理器 (Algorithm Manager)</text>
  
  <text x="640" y="300" class="highlight">主要职责：</text>
  <text x="660" y="320" class="text">• 直接控制C++算法</text>
  <text x="660" y="340" class="text">• 图像数据分发</text>
  <text x="660" y="360" class="text">• 模式状态管理</text>
  <text x="660" y="380" class="text">• 算法结果回调</text>
  
  <text x="640" y="405" class="highlight">核心方法：</text>
  <text x="660" y="425" class="code">startTracking(context)</text>
  <text x="660" y="445" class="code">sendImageProxy(image)</text>
  <text x="660" y="465" class="code">startVisualCalibration()</text>
  <text x="660" y="485" class="code">getServiceMode()</text>
  
  <text x="640" y="510" class="highlight">特点：</text>
  <text x="660" y="530" class="text">• object 单例</text>
  <text x="660" y="550" class="text">• 有状态管理</text>
  <text x="660" y="570" class="text">• 直接操作算法</text>
  <text x="660" y="590" class="text">• 线程安全设计</text>
  
  <!-- GazeTrackService -->
  <rect x="1160" y="230" width="500" height="380" rx="8" fill="url(#serviceGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="1410" y="255" text-anchor="middle" class="subtitle">GazeTrackService</text>
  <text x="1410" y="275" text-anchor="middle" class="highlight">Android服务 (Service)</text>
  
  <text x="1180" y="300" class="highlight">主要职责：</text>
  <text x="1200" y="320" class="text">• 生命周期管理</text>
  <text x="1200" y="340" class="text">• 进程间通信</text>
  <text x="1200" y="360" class="text">• WebSocket服务</text>
  <text x="1200" y="380" class="text">• 相机管理协调</text>
  <text x="1200" y="400" class="text">• 治疗应用管理</text>
  
  <text x="1180" y="425" class="highlight">核心方法：</text>
  <text x="1200" y="445" class="code">parseMessage(msg)</text>
  <text x="1200" y="465" class="code">startGazeTrack()</text>
  <text x="1200" y="485" class="code">onGazeTracking(result)</text>
  <text x="1200" y="505" class="code">broadcast(message)</text>
  
  <text x="1180" y="530" class="highlight">特点：</text>
  <text x="1200" y="550" class="text">• 前台服务</text>
  <text x="1200" y="570" class="text">• 生命周期感知</text>
  <text x="1200" y="590" class="text">• 消息处理中心</text>
  
  <!-- 架构层次图 -->
  <rect x="50" y="650" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="675" text-anchor="middle" class="subtitle">系统架构层次关系</text>
  
  <!-- 应用层 -->
  <rect x="100" y="700" width="1600" height="60" rx="8" fill="#f8f9fa"/>
  <text x="900" y="735" text-anchor="middle" class="subtitle">应用层 (Activity/Fragment)</text>
  
  <!-- 服务层 -->
  <rect x="100" y="780" width="1600" height="60" rx="8" fill="url(#serviceGradient)"/>
  <text x="900" y="815" text-anchor="middle" class="subtitle">服务层 - GazeTrackService (生命周期管理 + 进程通信)</text>
  
  <!-- 管理层 -->
  <rect x="100" y="860" width="1600" height="60" rx="8" fill="url(#managerGradient)"/>
  <text x="900" y="895" text-anchor="middle" class="subtitle">管理层 - TrackingManager (算法控制 + 状态管理)</text>
  
  <!-- JNI层 -->
  <rect x="100" y="940" width="1600" height="60" rx="8" fill="#fff3e0"/>
  <text x="900" y="975" text-anchor="middle" class="subtitle">JNI层 - GazeTrack (Java-C++桥接)</text>
  
  <!-- 工具层 -->
  <rect x="1400" y="700" width="280" height="60" rx="8" fill="url(#utilityGradient)"/>
  <text x="1540" y="735" text-anchor="middle" class="subtitle">工具层 - GazeTrackingManager</text>
  
  <!-- 连接箭头 -->
  <line x1="900" y1="760" x2="900" y2="780" stroke="#4caf50" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="900" y1="840" x2="900" y2="860" stroke="#2196f3" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="900" y1="920" x2="900" y2="940" stroke="#ff9800" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 工具层连接 -->
  <line x1="1400" y1="730" x2="1100" y2="730" stroke="#ff9800" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="1250" y="720" text-anchor="middle" class="note">配置支持</text>
  
  <!-- 详细职责对比 -->
  <rect x="50" y="1020" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1045" text-anchor="middle" class="subtitle">详细职责对比表</text>
  
  <!-- 表头 -->
  <rect x="80" y="1070" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1095" text-anchor="middle" class="subtitle">职责类别</text>
  
  <rect x="300" y="1070" width="400" height="40" rx="5" fill="url(#utilityGradient)"/>
  <text x="500" y="1095" text-anchor="middle" class="subtitle">GazeTrackingManager</text>
  
  <rect x="720" y="1070" width="400" height="40" rx="5" fill="url(#managerGradient)"/>
  <text x="920" y="1095" text-anchor="middle" class="subtitle">TrackingManager</text>
  
  <rect x="1140" y="1070" width="400" height="40" rx="5" fill="url(#serviceGradient)"/>
  <text x="1340" y="1095" text-anchor="middle" class="subtitle">GazeTrackService</text>
  
  <!-- 生命周期 -->
  <rect x="80" y="1120" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1145" text-anchor="middle" class="text">生命周期</text>
  
  <rect x="300" y="1120" width="400" height="40" rx="5" fill="#fff"/>
  <text x="500" y="1145" text-anchor="middle" class="text">应用级别，长期存在</text>
  
  <rect x="720" y="1120" width="400" height="40" rx="5" fill="#fff"/>
  <text x="920" y="1145" text-anchor="middle" class="text">算法级别，按需创建</text>
  
  <rect x="1140" y="1120" width="400" height="40" rx="5" fill="#fff"/>
  <text x="1340" y="1145" text-anchor="middle" class="text">服务级别，前台服务</text>
  
  <!-- 主要功能 -->
  <rect x="80" y="1170" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1195" text-anchor="middle" class="text">主要功能</text>
  
  <rect x="300" y="1170" width="400" height="40" rx="5" fill="#fff"/>
  <text x="500" y="1195" text-anchor="middle" class="text">配置管理、文件操作</text>
  
  <rect x="720" y="1170" width="400" height="40" rx="5" fill="#fff"/>
  <text x="920" y="1195" text-anchor="middle" class="text">算法控制、数据处理</text>
  
  <rect x="1140" y="1170" width="400" height="40" rx="5" fill="#fff"/>
  <text x="1340" y="1195" text-anchor="middle" class="text">通信协调、生命周期</text>
  
  <!-- 状态管理 -->
  <rect x="80" y="1220" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1245" text-anchor="middle" class="text">状态管理</text>
  
  <rect x="300" y="1220" width="400" height="40" rx="5" fill="#fff"/>
  <text x="500" y="1245" text-anchor="middle" class="text">无状态</text>
  
  <rect x="720" y="1220" width="400" height="40" rx="5" fill="#fff"/>
  <text x="920" y="1245" text-anchor="middle" class="text">ServiceMode状态</text>
  
  <rect x="1140" y="1220" width="400" height="40" rx="5" fill="#fff"/>
  <text x="1340" y="1245" text-anchor="middle" class="text">Service生命周期状态</text>
  
  <!-- 通信方式 -->
  <rect x="80" y="1270" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1295" text-anchor="middle" class="text">通信方式</text>
  
  <rect x="300" y="1270" width="400" height="40" rx="5" fill="#fff"/>
  <text x="500" y="1295" text-anchor="middle" class="text">直接方法调用</text>
  
  <rect x="720" y="1270" width="400" height="40" rx="5" fill="#fff"/>
  <text x="920" y="1295" text-anchor="middle" class="text">回调接口</text>
  
  <rect x="1140" y="1270" width="400" height="40" rx="5" fill="#fff"/>
  <text x="1340" y="1295" text-anchor="middle" class="text">Message + WebSocket</text>
  
  <!-- 依赖关系 -->
  <rect x="80" y="1320" width="200" height="40" rx="5" fill="#f8f9fa"/>
  <text x="180" y="1345" text-anchor="middle" class="text">依赖关系</text>
  
  <rect x="300" y="1320" width="400" height="40" rx="5" fill="#fff"/>
  <text x="500" y="1345" text-anchor="middle" class="text">独立工具类</text>
  
  <rect x="720" y="1320" width="400" height="40" rx="5" fill="#fff"/>
  <text x="920" y="1345" text-anchor="middle" class="text">依赖GazeTrack</text>
  
  <rect x="1140" y="1320" width="400" height="40" rx="5" fill="#fff"/>
  <text x="1340" y="1345" text-anchor="middle" class="text">依赖TrackingManager</text>
  
  <!-- 使用场景 -->
  <rect x="50" y="1440" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1465" text-anchor="middle" class="subtitle">典型使用场景</text>
  
  <!-- GazeTrackingManager使用 -->
  <rect x="80" y="1490" width="500" height="120" rx="8" fill="#fff3e0"/>
  <text x="330" y="1515" text-anchor="middle" class="subtitle">GazeTrackingManager 使用</text>
  
  <text x="100" y="1540" class="code">// 应用启动时初始化</text>
  <text x="100" y="1560" class="code">GazeTrackingManager.initGazeTracking(context)</text>
  <text x="100" y="1580" class="code">// 设置全局配置</text>
  <text x="100" y="1600" class="code">GazeTrackingManager.setDisplayViewpoint(true)</text>
  
  <!-- TrackingManager使用 -->
  <rect x="620" y="1490" width="500" height="120" rx="8" fill="#e3f2fd"/>
  <text x="870" y="1515" text-anchor="middle" class="subtitle">TrackingManager 使用</text>
  
  <text x="640" y="1540" class="code">// 启动算法</text>
  <text x="640" y="1560" class="code">TrackingManager.startTracking(context)</text>
  <text x="640" y="1580" class="code">// 处理图像</text>
  <text x="640" y="1600" class="code">TrackingManager.sendImageProxy(image)</text>
  
  <!-- GazeTrackService使用 -->
  <rect x="1160" y="1490" width="500" height="120" rx="8" fill="#e8f5e8"/>
  <text x="1410" y="1515" text-anchor="middle" class="subtitle">GazeTrackService 使用</text>
  
  <text x="1180" y="1540" class="code">// 启动服务</text>
  <text x="1180" y="1560" class="code">startForegroundService(intent)</text>
  <text x="1180" y="1580" class="code">// 发送消息</text>
  <text x="1180" y="1600" class="code">sendMessageToService(message)</text>
  
  <!-- 总结 -->
  <rect x="100" y="1660" width="1600" height="30" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1680" text-anchor="middle" class="highlight">
    总结：三个组件分工明确 - Manager负责工具配置，TrackingManager负责算法控制，Service负责生命周期和通信
  </text>
</svg>
