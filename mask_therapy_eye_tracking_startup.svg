<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .step-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .layer-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; font-weight: bold; }
      
      .ui-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .service-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .manager-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .native-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .hardware-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      
      .step-module { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 8; }
      .ui-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .service-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .manager-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .native-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .hardware-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .message-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .call-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .native-arrow { stroke: #9b59b6; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">MaskTherapyFragment 启动遮盖模式眼动完整流程</text>
  
  <!-- UI层 -->
  <rect x="50" y="70" width="300" height="120" class="ui-layer"/>
  <text x="200" y="95" text-anchor="middle" class="section-title">UI层</text>
  
  <rect x="70" y="110" width="260" height="70" class="ui-module"/>
  <text x="200" y="130" text-anchor="middle" class="method-title">MaskTherapyFragment</text>
  <text x="80" y="150" class="text">• switchMaskTherapy.setOnCheckedChangeListener</text>
  <text x="80" y="165" class="text">• switchMaskTherapy(isChecked) 调用</text>

  <!-- Service层 -->
  <rect x="400" y="70" width="300" height="120" class="service-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">Service层</text>
  
  <rect x="420" y="110" width="260" height="70" class="service-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">GazeTrackService</text>
  <text x="430" y="150" class="text">• MSG_START_APPLIED_CURE 消息处理</text>
  <text x="430" y="165" class="text">• startAppliedCure(plannedDuration, treatmentDuration)</text>

  <!-- Manager层 -->
  <rect x="750" y="70" width="300" height="120" class="manager-layer"/>
  <text x="900" y="95" text-anchor="middle" class="section-title">Manager层</text>
  
  <rect x="770" y="110" width="260" height="70" class="manager-module"/>
  <text x="900" y="130" text-anchor="middle" class="method-title">AppliedManager</text>
  <text x="780" y="150" class="text">• startAppliedCure() 调用</text>
  <text x="780" y="165" class="text">• 设置遮盖参数和状态管理</text>

  <!-- Native层 -->
  <rect x="1100" y="70" width="300" height="120" class="native-layer"/>
  <text x="1250" y="95" text-anchor="middle" class="section-title">Native层</text>
  
  <rect x="1120" y="110" width="260" height="70" class="native-module"/>
  <text x="1250" y="130" text-anchor="middle" class="method-title">GazeApplied</text>
  <text x="1130" y="150" class="text">• nativeStartApplication(CURE) 调用</text>
  <text x="1130" y="165" class="text">• C++ GazeApplication.start_application()</text>

  <!-- Hardware层 -->
  <rect x="1450" y="70" width="300" height="120" class="hardware-layer"/>
  <text x="1600" y="95" text-anchor="middle" class="section-title">Hardware层</text>
  
  <rect x="1470" y="110" width="260" height="70" class="hardware-module"/>
  <text x="1600" y="130" text-anchor="middle" class="method-title">眼动追踪硬件</text>
  <text x="1480" y="150" class="text">• 相机启动和图像采集</text>
  <text x="1480" y="165" class="text">• 实时眼动追踪和遮盖渲染</text>

  <!-- 连接箭头 -->
  <line x1="350" y1="130" x2="400" y2="130" class="message-arrow"/>
  <line x1="700" y1="130" x2="750" y2="130" class="call-arrow"/>
  <line x1="1050" y1="130" x2="1100" y2="130" class="call-arrow"/>
  <line x1="1400" y1="130" x2="1450" y2="130" class="native-arrow"/>

  <!-- 详细流程说明 -->
  <rect x="50" y="220" width="1700" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="245" text-anchor="middle" class="title" style="font-size: 22px;">遮盖模式眼动启动详细流程</text>

  <!-- 第一步：用户交互触发 -->
  <text x="70" y="280" class="step-title">🎯 第一步：用户交互触发</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 用户操作开关</text>
  <text x="110" y="325" class="code-text">switchMaskTherapy.setOnCheckedChangeListener { buttonView, isChecked -></text>
  <text x="130" y="340" class="code-text">if (!buttonView.isPressed) return@setOnCheckedChangeListener</text>
  <text x="130" y="355" class="code-text">switchMaskTherapy(isChecked)</text>
  <text x="110" y="370" class="code-text">}</text>
  
  <text x="110" y="390" class="flow-text">• <tspan style="font-weight: bold;">触发条件：</tspan>用户手动点击遮盖疗法开关</text>
  <text x="110" y="405" class="flow-text">• <tspan style="font-weight: bold;">状态检查：</tspan>只处理用户主动按压操作(buttonView.isPressed)</text>
  <text x="110" y="420" class="flow-text">• <tspan style="font-weight: bold;">方法调用：</tspan>调用switchMaskTherapy(isChecked)进行状态切换</text>

  <!-- 第二步：权限和状态验证 -->
  <text x="900" y="280" class="step-title">🔐 第二步：权限和状态验证</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 多层级验证</text>
  <text x="940" y="325" class="flow-text">• 用户绑定验证：UserManager.isBind()</text>
  <text x="940" y="340" class="flow-text">• 疗程数据验证：maskVM.occlusionTherapy != null</text>
  <text x="940" y="355" class="flow-text">• 疗程状态验证：TreatmentStatus检查</text>
  <text x="940" y="370" class="flow-text">• 治疗完成验证：isFinishUp || plannedDuration <= treatmentDuration</text>
  
  <text x="920" y="395" class="flow-text" style="font-weight: bold;">2. 验证通过后的消息发送</text>
  <text x="940" y="415" class="code-text">sendMessageToService(</text>
  <text x="960" y="430" class="code-text">MSG_TURN_ON_CAMERA,</text>
  <text x="960" y="445" class="code-text">MSG_START_TRACK,</text>
  <text x="960" y="460" class="code-text">MSG_START_APPLIED_CURE</text>
  <text x="940" y="475" class="code-text">)</text>

  <!-- 第三步：消息传递和处理 -->
  <text x="70" y="510" class="step-title">📡 第三步：消息传递和处理</text>
  
  <text x="90" y="535" class="flow-text" style="font-weight: bold;">1. Messenger消息发送</text>
  <text x="110" y="555" class="code-text">fun sendMessageToService(vararg messages: Message) {</text>
  <text x="130" y="570" class="code-text">messages.forEach { mServiceMessage?.send(it) }</text>
  <text x="110" y="585" class="code-text">}</text>
  
  <text x="110" y="605" class="flow-text">• <tspan style="font-weight: bold;">通信机制：</tspan>通过Messenger与GazeTrackService进行IPC通信</text>
  <text x="110" y="620" class="flow-text">• <tspan style="font-weight: bold;">消息顺序：</tspan>相机启动 → 眼动追踪 → 遮盖疗法</tspan>
  <text x="110" y="635" class="flow-text">• <tspan style="font-weight: bold;">参数携带：</tspan>MSG_START_APPLIED_CURE携带时长、URL、请求头等配置</text>
  
  <text x="90" y="660" class="flow-text" style="font-weight: bold;">2. GazeTrackService消息处理</text>
  <text x="110" y="680" class="code-text">GazeConstants.MSG_START_APPLIED_CURE -> {</text>
  <text x="130" y="695" class="code-text">// 解析报告参数、请求头、URL配置</text>
  <text x="130" y="710" class="code-text">// 获取计划时长和已治疗时长</text>
  <text x="130" y="725" class="code-text">startAppliedCure(plannedDuration, treatmentDuration)</text>
  <text x="110" y="740" class="code-text">}</text>

  <!-- 第四步：AppliedManager调用 -->
  <text x="900" y="510" class="step-title">⚙️ 第四步：AppliedManager调用</text>
  
  <text x="920" y="535" class="flow-text" style="font-weight: bold;">1. GazeTrackService.startAppliedCure()</text>
  <text x="940" y="555" class="code-text">private fun startAppliedCure(plannedDuration: Int, treatmentDuration: Int) {</text>
  <text x="960" y="570" class="code-text">mTreatmentDuration = maxOf(0, treatmentDuration)</text>
  <text x="960" y="585" class="code-text">mPlannedDuration = if (plannedDuration <= mTreatmentDuration)</text>
  <text x="980" y="600" class="code-text">mTreatmentDuration + 5400 else plannedDuration</text>
  <text x="960" y="615" class="code-text">AppliedManager.startAppliedCure()</text>
  <text x="940" y="630" class="code-text">}</text>
  
  <text x="920" y="655" class="flow-text" style="font-weight: bold;">2. AppliedManager.startAppliedCure()</text>
  <text x="940" y="675" class="code-text">fun startAppliedCure(): Int {</text>
  <text x="960" y="690" class="code-text">if (appliedMode.get() == CURE) return 2</text>
  <text x="960" y="705" class="code-text">init()</text>
  <text x="960" y="720" class="code-text">if (gazeApplied.startApplied(CURE)) {</text>
  <text x="980" y="735" class="code-text">setBlurParams(...) // 设置遮盖参数</text>
  <text x="980" y="750" class="code-text">DeviceManager.saveMaskTherapyState(true)</text>
  <text x="980" y="765" class="code-text">return 1</text>
  <text x="960" y="780" class="code-text">}</text>
  <text x="940" y="795" class="code-text">}</text>

  <!-- 第五步：Native层启动 -->
  <text x="70" y="830" class="step-title">🔧 第五步：Native层启动</text>
  
  <text x="90" y="855" class="flow-text" style="font-weight: bold;">1. GazeApplied.startApplied(CURE)</text>
  <text x="110" y="875" class="code-text">fun startApplied(appliedMode: AppliedMode): Boolean {</text>
  <text x="130" y="890" class="code-text">return nativeStartApplication(nativeObj.get(), appliedMode.code)</text>
  <text x="110" y="905" class="code-text">}</text>
  
  <text x="110" y="925" class="flow-text">• <tspan style="font-weight: bold;">JNI调用：</tspan>调用Native层的nativeStartApplication方法</text>
  <text x="110" y="940" class="flow-text">• <tspan style="font-weight: bold;">模式参数：</tspan>传递CURE模式(治疗模式)给Native层</text>
  <text x="110" y="955" class="flow-text">• <tspan style="font-weight: bold;">对象引用：</tspan>使用nativeObj.get()获取Native对象指针</text>
  
  <text x="90" y="980" class="flow-text" style="font-weight: bold;">2. Native层C++实现</text>
  <text x="110" y="1000" class="code-text">extern "C" JNIEXPORT jboolean JNICALL</text>
  <text x="110" y="1015" class="code-text">Java_com_mitdd_gazetracker_gaze_application_GazeApplied_nativeStartApplication(</text>
  <text x="130" y="1030" class="code-text">JNIEnv *env, jclass clazz, jlong thiz, jint mode) {</text>
  <text x="150" y="1045" class="code-text">auto *application = reinterpret_cast&lt;GazeApplication *&gt;(thiz);</text>
  <text x="150" y="1060" class="code-text">return application->start_application(mode);</text>
  <text x="110" y="1075" class="code-text">}</text>

  <!-- 第六步：遮盖参数设置 -->
  <text x="900" y="830" class="step-title">🎨 第六步：遮盖参数设置</text>
  
  <text x="920" y="855" class="flow-text" style="font-weight: bold;">1. 遮盖参数配置</text>
  <text x="940" y="875" class="code-text">setBlurParams(</text>
  <text x="960" y="890" class="code-text">MaskManager.getCoverArea() ?: 4.0f,     // 遮盖区域</text>
  <text x="960" y="905" class="code-text">MaskManager.getCoverRange().ranger,      // 遮盖幅度</text>
  <text x="960" y="920" class="code-text">MaskManager.getCoverMode().mode,         // 遮盖模式</text>
  <text x="960" y="935" class="code-text">MaskManager.getCoverChannel().channel    // 遮盖通道</text>
  <text x="940" y="950" class="code-text">)</text>
  
  <text x="920" y="975" class="flow-text" style="font-weight: bold;">2. 参数说明</text>
  <text x="940" y="995" class="flow-text">• <tspan style="font-weight: bold;">遮盖区域：</tspan>0.5~5.5mm黄斑区直径值</text>
  <text x="940" y="1010" class="flow-text">• <tspan style="font-weight: bold;">遮盖幅度：</tspan>高斯模糊的sigma值</text>
  <text x="940" y="1025" class="flow-text">• <tspan style="font-weight: bold;">遮盖模式：</tspan>0-内部高斯模糊，1-外部高斯模糊，2-内部置黑，3-外部置黑</text>
  <text x="940" y="1040" class="flow-text">• <tspan style="font-weight: bold;">遮盖通道：</tspan>1-7之间，默认为1(红色通道)</text>
  
  <text x="920" y="1065" class="flow-text" style="font-weight: bold;">3. 状态保存</text>
  <text x="940" y="1085" class="code-text">DeviceManager.saveMaskTherapyState(true)</text>
  <text x="940" y="1100" class="flow-text">• 保存遮盖疗法启动状态到本地存储</text>

  <!-- 第七步：启动成功反馈 -->
  <text x="70" y="1135" class="step-title">✅ 第七步：启动成功反馈</text>
  
  <text x="90" y="1160" class="flow-text" style="font-weight: bold;">1. 启动治疗计时器</text>
  <text x="110" y="1180" class="code-text">if (it == 1) {</text>
  <text x="130" y="1195" class="code-text">startTreatmentTimer()  // 开始治疗计时</text>
  <text x="130" y="1210" class="code-text">sendMessageToClient(Message.obtain().apply {</text>
  <text x="150" y="1225" class="code-text">what = GazeConstants.MSG_APPLIED_CURE_STATE</text>
  <text x="150" y="1240" class="code-text">data.putBoolean(GazeConstants.KEY_STATE, true)</text>
  <text x="130" y="1255" class="code-text">})</text>
  <text x="110" y="1270" class="code-text">}</text>
  
  <text x="90" y="1295" class="flow-text" style="font-weight: bold;">2. UI状态更新</text>
  <text x="110" y="1315" class="flow-text">• MaskTherapyFragment接收MSG_APPLIED_CURE_STATE消息</text>
  <text x="110" y="1330" class="flow-text">• 更新maskVM.setMTState(true)状态</text>
  <text x="110" y="1345" class="flow-text">• 显示MaskTherapyStateDialog状态对话框</text>
  <text x="110" y="1360" class="flow-text">• 开关保持选中状态，红蓝眼镜图标显示</text>

  <!-- 第八步：实时眼动追踪 -->
  <text x="900" y="1135" class="step-title">👁️ 第八步：实时眼动追踪</text>
  
  <text x="920" y="1160" class="flow-text" style="font-weight: bold;">1. 眼动追踪启动</text>
  <text x="940" y="1180" class="flow-text">• 相机开始图像采集(30fps)</text>
  <text x="940" y="1195" class="flow-text">• Native层开始眼动算法处理</text>
  <text x="940" y="1210" class="flow-text">• 实时计算用户注视点坐标</text>
  <text x="940" y="1225" class="flow-text">• 根据弱视眼位置进行遮盖渲染</text>
  
  <text x="920" y="1250" class="flow-text" style="font-weight: bold;">2. 遮盖效果实现</text>
  <text x="940" y="1270" class="flow-text">• GPU加速的实时遮盖效果渲染</text>
  <text x="940" y="1285" class="flow-text">• 根据注视点动态调整遮盖位置</text>
  <text x="940" y="1300" class="flow-text">• 治疗数据实时上报到服务器</text>
  <text x="940" y="1315" class="flow-text">• 治疗时长实时更新和进度监控</text>

  <!-- 底部总结 -->
  <rect x="70" y="1380" width="1600" height="150" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1405" class="step-title">🌟 遮盖模式眼动启动流程总结</text>
  
  <text x="90" y="1430" class="flow-text">• <tspan style="font-weight: bold;">用户交互触发：</tspan>用户点击开关 → 权限验证 → 消息发送</text>
  <text x="90" y="1450" class="flow-text">• <tspan style="font-weight: bold;">服务层处理：</tspan>GazeTrackService接收消息 → 解析配置 → 调用AppliedManager</text>
  <text x="90" y="1470" class="flow-text">• <tspan style="font-weight: bold;">管理层控制：</tspan>AppliedManager设置参数 → 调用Native层 → 状态管理</text>
  <text x="90" y="1490" class="flow-text">• <tspan style="font-weight: bold;">Native层执行：</tspan>C++启动眼动应用 → 硬件控制 → 实时遮盖渲染</text>
  <text x="90" y="1510" class="flow-text">• <tspan style="font-weight: bold;">反馈机制：</tspan>启动成功反馈 → UI状态更新 → 实时治疗监控</text>

</svg>
