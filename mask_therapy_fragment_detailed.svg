<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .flow-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 12px; fill: #2c3e50; }
      .method-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; font-weight: bold; }
      
      .ui-component { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .ui-module { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .data-component { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .data-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .service-component { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .service-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .lifecycle-component { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .lifecycle-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .logic-component { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .logic-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .message-component { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 10; }
      .message-module { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .event-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">MaskTherapyFragment 详细功能模块图</text>
  
  <!-- UI组件层 -->
  <rect x="50" y="70" width="500" height="200" class="ui-component"/>
  <text x="300" y="95" text-anchor="middle" class="section-title">UI组件层</text>
  
  <!-- 标题区域 -->
  <rect x="70" y="110" width="220" height="70" class="ui-module"/>
  <text x="180" y="130" text-anchor="middle" class="module-title">标题区域 (cl_mask_therapy_title)</text>
  <text x="80" y="150" class="text">• tvDigitalMaskTherapy - 模块名称显示</text>
  <text x="80" y="165" class="text">• ivMaskTherapy - 遮盖疗法图标</text>
  <text x="80" y="180" class="text">• 背景: digital_mask_therapy_title_bg</text>
  
  <!-- 控制区域 -->
  <rect x="300" y="110" width="220" height="70" class="ui-module"/>
  <text x="410" y="130" text-anchor="middle" class="module-title">控制区域</text>
  <text x="310" y="150" class="text">• switchMaskTherapy - 遮盖疗法开关</text>
  <text x="310" y="165" class="text">• ivRedBlueGlasses - 弱视眼标识</text>
  <text x="310" y="180" class="text">• 根据性别显示不同Thumb样式</text>
  
  <!-- 进度显示区域 -->
  <rect x="70" y="190" width="220" height="70" class="ui-module"/>
  <text x="180" y="210" text-anchor="middle" class="module-title">TimeProgress 进度组件</text>
  <text x="80" y="230" class="text">• 计划治疗时长显示 (tvPlannedDuration)</text>
  <text x="80" y="245" class="text">• 已治疗时长显示 (tvTrainDuration)</text>
  <text x="80" y="260" class="text">• 进度条和Thumb位置计算</text>
  
  <!-- 应用区域 -->
  <rect x="300" y="190" width="220" height="70" class="ui-module"/>
  <text x="410" y="210" text-anchor="middle" class="module-title">CommonAppView 应用区域</text>
  <text x="310" y="230" class="text">• 常用应用网格显示</text>
  <text x="310" y="245" class="text">• 添加应用功能</text>
  <text x="310" y="260" class="text">• 根据isFull调整SpanCount(4/8)</text>

  <!-- 数据管理层 -->
  <rect x="600" y="70" width="500" height="200" class="data-component"/>
  <text x="850" y="95" text-anchor="middle" class="section-title">数据管理层</text>
  
  <!-- ViewModel -->
  <rect x="620" y="110" width="220" height="70" class="data-module"/>
  <text x="730" y="130" text-anchor="middle" class="module-title">MaskViewModel</text>
  <text x="630" y="150" class="text">• occlusionTherapyLiveData - 遮盖疗法数据</text>
  <text x="630" y="165" class="text">• mtStateLiveData - 遮盖疗法状态</text>
  <text x="630" y="180" class="text">• 计划时长、已治疗时长、弱视眼位置</text>
  
  <!-- TreatmentViewModel -->
  <rect x="850" y="110" width="220" height="70" class="data-module"/>
  <text x="960" y="130" text-anchor="middle" class="module-title">TreatmentViewModel</text>
  <text x="860" y="150" class="text">• curTreatmentLiveData - 当前疗程</text>
  <text x="860" y="165" class="text">• activationTreatment() - 激活疗程</text>
  <text x="860" y="180" class="text">• 疗程状态管理</text>
  
  <!-- 数据模型 -->
  <rect x="620" y="190" width="220" height="70" class="data-module"/>
  <text x="730" y="210" text-anchor="middle" class="module-title">数据模型</text>
  <text x="630" y="230" class="text">• CureInfo - 遮盖疗法信息</text>
  <text x="630" y="245" class="text">• TreatmentInfo - 疗程信息</text>
  <text x="630" y="260" class="text">• AmblyopicEye - 弱视眼枚举</text>
  
  <!-- 参数管理 -->
  <rect x="850" y="190" width="220" height="70" class="data-module"/>
  <text x="960" y="210" text-anchor="middle" class="module-title">MaskManager 参数管理</text>
  <text x="860" y="230" class="text">• setCoverChannel/Mode/Area/Range</text>
  <text x="860" y="245" class="text">• setAmblyopicEye - 弱视眼设置</text>
  <text x="860" y="260" class="text">• 遮盖参数配置</text>

  <!-- 服务交互层 -->
  <rect x="1150" y="70" width="500" height="200" class="service-component"/>
  <text x="1400" y="95" text-anchor="middle" class="section-title">服务交互层</text>
  
  <!-- GazeTrackService -->
  <rect x="1170" y="110" width="220" height="70" class="service-module"/>
  <text x="1280" y="130" text-anchor="middle" class="module-title">GazeTrackService 交互</text>
  <text x="1180" y="150" class="text">• ServiceConnection - 服务连接</text>
  <text x="1180" y="165" class="text">• Messenger - 消息通信</text>
  <text x="1180" y="180" class="text">• onResume/onPause 绑定/解绑</text>
  
  <!-- 消息处理 -->
  <rect x="1400" y="110" width="220" height="70" class="service-module"/>
  <text x="1510" y="130" text-anchor="middle" class="module-title">消息处理机制</text>
  <text x="1410" y="150" class="text">• MSG_TURN_ON/OFF_CAMERA</text>
  <text x="1410" y="165" class="text">• MSG_START/STOP_TRACK</text>
  <text x="1410" y="180" class="text">• MSG_START/STOP_APPLIED_CURE</text>
  
  <!-- UserManager -->
  <rect x="1170" y="190" width="220" height="70" class="service-module"/>
  <text x="1280" y="210" text-anchor="middle" class="module-title">UserManager</text>
  <text x="1180" y="230" class="text">• isBind() - 绑定状态检查</text>
  <text x="1180" y="245" class="text">• getAccountInfo() - 账户信息</text>
  <text x="1180" y="260" class="text">• getTreatmentInfo() - 疗程信息</text>
  
  <!-- DeviceManager -->
  <rect x="1400" y="190" width="220" height="70" class="service-module"/>
  <text x="1510" y="210" text-anchor="middle" class="module-title">DeviceManager</text>
  <text x="1410" y="230" class="text">• getMaskTherapyState() - 状态获取</text>
  <text x="1410" y="245" class="text">• 设备状态管理</text>
  <text x="1410" y="260" class="text">• 产品模型信息</text>

  <!-- 生命周期管理层 -->
  <rect x="50" y="290" width="500" height="180" class="lifecycle-component"/>
  <text x="300" y="315" text-anchor="middle" class="section-title">生命周期管理层</text>
  
  <!-- 初始化方法 -->
  <rect x="70" y="330" width="220" height="60" class="lifecycle-module"/>
  <text x="180" y="350" text-anchor="middle" class="module-title">初始化方法</text>
  <text x="80" y="370" class="text">• initParam() - 参数初始化</text>
  <text x="80" y="385" class="text">• initView() - 视图初始化</text>
  
  <!-- 观察者设置 -->
  <rect x="300" y="330" width="220" height="60" class="lifecycle-module"/>
  <text x="410" y="350" text-anchor="middle" class="module-title">观察者设置</text>
  <text x="310" y="370" class="text">• initObserver() - LiveData观察</text>
  <text x="310" y="385" class="text">• LiveEventBus事件监听</text>
  
  <!-- 生命周期回调 -->
  <rect x="70" y="400" width="220" height="60" class="lifecycle-module"/>
  <text x="180" y="420" text-anchor="middle" class="module-title">生命周期回调</text>
  <text x="80" y="440" class="text">• onResume() - 服务绑定</text>
  <text x="80" y="455" class="text">• onPause() - 服务解绑</text>
  
  <!-- 监听器设置 -->
  <rect x="300" y="400" width="220" height="60" class="lifecycle-module"/>
  <text x="410" y="420" text-anchor="middle" class="module-title">监听器设置</text>
  <text x="310" y="440" class="text">• initListener() - 事件监听</text>
  <text x="310" y="455" class="text">• Switch状态变化监听</text>

  <!-- 业务逻辑层 -->
  <rect x="600" y="290" width="500" height="180" class="logic-component"/>
  <text x="850" y="315" text-anchor="middle" class="section-title">业务逻辑层</text>
  
  <!-- 遮盖疗法控制 -->
  <rect x="620" y="330" width="220" height="60" class="logic-module"/>
  <text x="730" y="350" text-anchor="middle" class="module-title">遮盖疗法控制</text>
  <text x="630" y="370" class="text">• switchMaskTherapy() - 开关控制</text>
  <text x="630" y="385" class="text">• 状态验证和权限检查</text>
  
  <!-- UI更新逻辑 -->
  <rect x="850" y="330" width="220" height="60" class="logic-module"/>
  <text x="960" y="350" text-anchor="middle" class="module-title">UI更新逻辑</text>
  <text x="860" y="370" class="text">• updateEyePosition() - 弱视眼显示</text>
  <text x="860" y="385" class="text">• updatePlanned/TreatmentDuration()</text>
  
  <!-- 对话框管理 -->
  <rect x="620" y="400" width="220" height="60" class="logic-module"/>
  <text x="730" y="420" text-anchor="middle" class="module-title">对话框管理</text>
  <text x="630" y="440" class="text">• showNotificationDialog() - 通知</text>
  <text x="630" y="455" class="text">• showMaskTherapyStateDialog()</text>
  
  <!-- 治疗状态管理 -->
  <rect x="850" y="400" width="220" height="60" class="logic-module"/>
  <text x="960" y="420" text-anchor="middle" class="module-title">治疗状态管理</text>
  <text x="860" y="440" class="text">• TreatmentManager对话框</text>
  <text x="860" y="455" class="text">• 疗程激活/暂停/过期处理</text>

  <!-- 消息通信层 -->
  <rect x="1150" y="290" width="500" height="180" class="message-component"/>
  <text x="1400" y="315" text-anchor="middle" class="section-title">消息通信层</text>
  
  <!-- 消息发送 -->
  <rect x="1170" y="330" width="220" height="60" class="message-module"/>
  <text x="1280" y="350" text-anchor="middle" class="module-title">消息发送</text>
  <text x="1180" y="370" class="text">• sendMessageToService() - 批量发送</text>
  <text x="1180" y="385" class="text">• 相机/追踪/遮盖控制消息</text>
  
  <!-- 消息接收处理 -->
  <rect x="1400" y="330" width="220" height="60" class="message-module"/>
  <text x="1510" y="350" text-anchor="middle" class="module-title">消息接收处理</text>
  <text x="1410" y="370" class="text">• parseMessage() - 消息解析</text>
  <text x="1410" y="385" class="text">• Handler消息处理机制</text>
  
  <!-- 状态同步 -->
  <rect x="1170" y="400" width="220" height="60" class="message-module"/>
  <text x="1280" y="420" text-anchor="middle" class="module-title">状态同步</text>
  <text x="1180" y="440" class="text">• MSG_APPLIED_CURE_STATE</text>
  <text x="1180" y="455" class="text">• MSG_UPDATE_TREATMENT_DURATION</text>
  
  <!-- 结果上报 -->
  <rect x="1400" y="400" width="220" height="60" class="message-module"/>
  <text x="1510" y="420" text-anchor="middle" class="module-title">结果上报</text>
  <text x="1410" y="440" class="text">• MSG_REPORT_TREATMENT_RESULT</text>
  <text x="1410" y="455" class="text">• JSON数据解析和处理</text>

  <!-- 连接箭头 -->
  <line x1="300" y1="270" x2="850" y2="270" class="arrow"/>
  <line x1="550" y1="170" x2="600" y2="170" class="arrow"/>
  <line x1="1100" y1="170" x2="1150" y2="170" class="arrow"/>
  <line x1="300" y1="270" x2="300" y2="290" class="arrow"/>
  <line x1="850" y1="270" x2="850" y2="290" class="arrow"/>
  <line x1="1400" y1="270" x2="1400" y2="290" class="arrow"/>
  
  <line x1="730" y1="400" x2="1280" y2="400" class="data-arrow"/>
  <line x1="960" y1="400" x2="960" y2="330" class="data-arrow"/>
  <line x1="1280" y1="400" x2="1280" y2="330" class="data-arrow"/>

  <!-- 详细功能流程 -->
  <rect x="50" y="490" width="1600" height="1060" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="850" y="515" text-anchor="middle" class="title" style="font-size: 22px;">MaskTherapyFragment 详细功能流程</text>

  <!-- 第一部分：初始化流程 -->
  <text x="70" y="550" class="flow-text" style="font-weight: bold; font-size: 16px;">🔧 初始化流程</text>

  <text x="70" y="575" class="method-text">1. newInstance(moduleName, isFull)</text>
  <text x="90" y="590" class="flow-text">• 创建Fragment实例，传递模块名称和布局模式参数</text>
  <text x="90" y="605" class="flow-text">• isFull决定CommonAppView的SpanCount(4列/8列)</text>

  <text x="70" y="630" class="method-text">2. initParam()</text>
  <text x="90" y="645" class="flow-text">• 从arguments中获取INPUT_PARAM_MODULE_NAME和INPUT_PARAM_IS_FULL</text>
  <text x="90" y="660" class="flow-text">• 设置mModuleName默认值为"数字遮盖疗法"</text>

  <text x="70" y="685" class="method-text">3. initView()</text>
  <text x="90" y="700" class="flow-text">• 根据用户性别设置TimeProgress的Thumb图标(男性/女性)</text>
  <text x="90" y="715" class="flow-text">• 设置进度条样式：seekbar_mask_therapy_duration_progress_drawable</text>
  <text x="90" y="730" class="flow-text">• 显示模块名称到tvDigitalMaskTherapy</text>
  <text x="90" y="745" class="flow-text">• 调用commonAppView.setSpanCount(isFull ? 8 : 4)</text>

  <text x="70" y="770" class="method-text">4. initObserver()</text>
  <text x="90" y="785" class="flow-text">• 观察treatmentVM.curTreatmentLiveData → 触发maskVM.getTodayOcclusionTherapy()</text>
  <text x="90" y="800" class="flow-text">• 观察maskVM.occlusionTherapyLiveData → 更新UI(弱视眼、时长)</text>
  <text x="90" y="815" class="flow-text">• 监听LiveEventBus.EVENT_SWITCH_CURE → 切换遮盖疗法状态</text>
  <text x="90" y="830" class="flow-text">• 观察maskVM.mtStateLiveData → 同步开关状态和眼镜图标显示</text>

  <text x="70" y="855" class="method-text">5. initListener()</text>
  <text x="90" y="870" class="flow-text">• switchMaskTherapy.setOnCheckedChangeListener → 处理用户手动切换</text>
  <text x="90" y="885" class="flow-text">• commonAppView.onAddCommonApp → 启动SelectCommonAppActivity</text>

  <!-- 第二部分：UI更新机制 -->
  <text x="900" y="550" class="flow-text" style="font-weight: bold; font-size: 16px;">🎨 UI更新机制</text>

  <text x="900" y="575" class="method-text">1. updateEyePosition()</text>
  <text x="920" y="590" class="flow-text">• 根据maskVM.eyePosition更新ivRedBlueGlasses图标</text>
  <text x="920" y="605" class="flow-text">• LEFT: icon_left_blue_right_red_glasses_small</text>
  <text x="920" y="620" class="flow-text">• RIGHT: icon_left_red_right_blue_glasses_small</text>

  <text x="900" y="645" class="method-text">2. updatePlannedDuration()</text>
  <text x="920" y="660" class="flow-text">• 显示/隐藏计划时长视图：maskVM.occlusionTherapy != null</text>
  <text x="920" y="675" class="flow-text">• 调用timeProgress.setPlannedDuration(maskVM.plannedDuration)</text>
  <text x="920" y="690" class="flow-text">• 时长转换：秒 → 分钟(向上取整)，设置进度条最大值</text>

  <text x="900" y="715" class="method-text">3. updateTreatmentDuration()</text>
  <text x="920" y="730" class="flow-text">• 显示/隐藏已治疗时长视图</text>
  <text x="920" y="745" class="flow-text">• 调用timeProgress.setTreatmentDuration(maskVM.treatmentDuration)</text>
  <text x="920" y="760" class="flow-text">• 时长转换：秒 → 分钟(向下取整)，更新进度条进度</text>

  <text x="900" y="785" class="method-text">4. TimeProgress组件功能</text>
  <text x="920" y="800" class="flow-text">• updateThumbPosition() - 根据进度计算Thumb位置</text>
  <text x="920" y="815" class="flow-text">• 考虑进度条宽度、Padding、Thumb宽度进行精确定位</text>
  <text x="920" y="830" class="flow-text">• 动态调整ConstraintLayout.LayoutParams的Margin</text>

  <text x="900" y="855" class="method-text">5. CommonAppView功能</text>
  <text x="920" y="870" class="flow-text">• 加载用户常用应用列表，支持添加/删除应用</text>
  <text x="920" y="885" class="flow-text">• GridLayoutManager布局，CustomItemDecoration装饰</text>

  <!-- 第三部分：遮盖疗法控制逻辑 -->
  <text x="70" y="920" class="flow-text" style="font-weight: bold; font-size: 16px;">⚙️ 遮盖疗法控制逻辑</text>

  <text x="70" y="945" class="method-text">1. switchMaskTherapy(state: Boolean)</text>
  <text x="90" y="960" class="flow-text">• 关闭状态：发送MSG_STOP_APPLIED_CURE、MSG_STOP_TRACK、MSG_TURN_OFF_CAMERA</text>
  <text x="90" y="975" class="flow-text">• 开启前检查：用户绑定状态、疗程可用性、治疗状态</text>

  <text x="70" y="1000" class="method-text">2. 权限和状态验证</text>
  <text x="90" y="1015" class="flow-text">• UserManager.isBind() - 未绑定显示绑定提示对话框</text>
  <text x="90" y="1030" class="flow-text">• maskVM.occlusionTherapy == null - 显示"无可用疗程"提示</text>
  <text x="90" y="1045" class="flow-text">• 疗程状态检查：INACTIVE/PENDING/COMPLETED分别处理</text>
  <text x="90" y="1060" class="flow-text">• 治疗完成检查：isFinishUp || plannedDuration <= treatmentDuration</text>

  <text x="70" y="1085" class="method-text">3. 疗程状态处理</text>
  <text x="90" y="1100" class="flow-text">• INACTIVE: 显示激活提醒 → 激活对话框 → 调用activationTreatment()</text>
  <text x="90" y="1115" class="flow-text">• PENDING: 显示暂停对话框 → 激活对话框 → 激活疗程</text>
  <text x="90" y="1130" class="flow-text">• COMPLETED: 显示疗程过期对话框</text>

  <text x="70" y="1155" class="method-text">4. 遮盖疗法启动</text>
  <text x="90" y="1170" class="flow-text">• 发送启动消息：MSG_TURN_ON_CAMERA → MSG_START_TRACK → MSG_START_APPLIED_CURE</text>
  <text x="90" y="1185" class="flow-text">• 设置HTTP请求头：版本号、设备模型、客户端ID、语言</text>

  <!-- 第四部分：服务通信机制 -->
  <text x="900" y="920" class="flow-text" style="font-weight: bold; font-size: 16px;">📡 服务通信机制</text>

  <text x="900" y="945" class="method-text">1. ServiceConnection管理</text>
  <text x="920" y="960" class="flow-text">• onServiceConnected: 获取Messenger，发送MSG_SERVICE_CONNECTED</text>
  <text x="920" y="975" class="flow-text">• onServiceDisconnected: 清空mServiceMessage引用</text>
  <text x="920" y="990" class="flow-text">• onResume绑定服务，onPause解绑服务</text>

  <text x="900" y="1015" class="method-text">2. 消息发送机制</text>
  <text x="920" y="1030" class="flow-text">• sendMessageToService(vararg messages) - 批量发送消息</text>
  <text x="920" y="1045" class="flow-text">• 支持同时发送多个Message，确保操作原子性</text>

  <text x="900" y="1070" class="method-text">3. 消息接收处理</text>
  <text x="920" y="1085" class="flow-text">• parseMessage(msg) - Handler消息解析</text>
  <text x="920" y="1100" class="flow-text">• MSG_GAZE_TRACKING_STATE: 眼动追踪状态</text>
  <text x="920" y="1115" class="flow-text">• MSG_APPLIED_CURE_STATE: 遮盖疗法状态 → 更新UI和显示状态对话框</text>
  <text x="920" y="1130" class="flow-text">• MSG_UPDATE_TREATMENT_DURATION: 实时更新治疗时长</text>
  <text x="920" y="1145" class="flow-text">• MSG_REPORT_TREATMENT_RESULT: JSON结果解析和数据更新</text>

  <text x="900" y="1170" class="method-text">4. 状态对话框显示</text>
  <text x="920" y="1185" class="flow-text">• showMaskTherapyStateDialog(isStart, eyePosition) - 显示遮盖状态</text>

  <!-- 第五部分：生命周期管理 -->
  <text x="70" y="1220" class="flow-text" style="font-weight: bold; font-size: 16px;">🔄 生命周期管理</text>

  <text x="70" y="1245" class="method-text">1. onResume()</text>
  <text x="90" y="1260" class="flow-text">• 延迟500ms同步遮盖疗法开关状态：DeviceManager.getMaskTherapyState()</text>
  <text x="90" y="1275" class="flow-text">• 绑定GazeTrackService：BIND_AUTO_CREATE模式</text>

  <text x="70" y="1300" class="method-text">2. onPause()</text>
  <text x="90" y="1315" class="flow-text">• 解绑GazeTrackService，释放服务连接</text>

  <text x="70" y="1340" class="method-text">3. 对话框管理</text>
  <text x="90" y="1355" class="flow-text">• showNotificationDialog() - 使用DialogTaskManager管理对话框队列</text>
  <text x="90" y="1370" class="flow-text">• 支持确认回调，处理用户交互</text>

  <!-- 第六部分：数据流转 -->
  <text x="900" y="1220" class="flow-text" style="font-weight: bold; font-size: 16px;">📊 数据流转</text>

  <text x="900" y="1245" class="method-text">1. ViewModel数据绑定</text>
  <text x="920" y="1260" class="flow-text">• MaskViewModel: 遮盖疗法数据管理，参数配置到MaskManager</text>
  <text x="920" y="1275" class="flow-text">• TreatmentViewModel: 疗程管理，激活/暂停操作</text>

  <text x="900" y="1300" class="method-text">2. LiveData响应式更新</text>
  <text x="920" y="1315" class="flow-text">• 疗程数据变化 → 自动获取今日遮盖疗法信息</text>
  <text x="920" y="1330" class="flow-text">• 遮盖疗法数据变化 → 更新UI显示(时长、弱视眼)</text>
  <text x="920" y="1345" class="flow-text">• 状态变化 → 同步开关和图标显示</text>

  <text x="900" y="1370" class="method-text">3. 事件总线通信</text>
  <text x="920" y="1385" class="flow-text">• EVENT_SWITCH_CURE: 跨组件遮盖疗法状态切换</text>

  <!-- 核心特性总结 -->
  <rect x="70" y="1410" width="1560" height="120" style="fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1435" class="flow-text" style="font-weight: bold; font-size: 16px;">🌟 MaskTherapyFragment 核心特性总结</text>

  <text x="90" y="1460" class="flow-text">• <tspan style="font-weight: bold;">模块化设计：</tspan>支持全屏/半屏模式，动态调整应用网格布局，适应不同显示需求</text>
  <text x="90" y="1480" class="flow-text">• <tspan style="font-weight: bold;">智能状态管理：</tspan>多层级权限验证，疗程状态检查，确保遮盖疗法安全启动</text>
  <text x="90" y="1500" class="flow-text">• <tspan style="font-weight: bold;">实时数据同步：</tspan>通过Messenger与GazeTrackService通信，实时更新治疗进度和状态</tspan>
  <text x="90" y="1520" class="flow-text">• <tspan style="font-weight: bold;">用户体验优化：</tspan>个性化UI显示(性别、弱视眼)，进度可视化，友好的错误提示和引导</text>

</svg>
