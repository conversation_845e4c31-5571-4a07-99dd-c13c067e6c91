<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .component-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .relationship-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      
      .home-fragment { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .home-module { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .adapter { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .adapter-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .mask-fragment { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .mask-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .shared-component { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .shared-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .data-flow { stroke: #9b59b6; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .creation-flow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
      .communication-flow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 12,6; }
      .lifecycle-flow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 6,3; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">MaskTherapyFragment 与相关组件关系图</text>
  
  <!-- HomeMainFragment -->
  <rect x="50" y="70" width="500" height="300" class="home-fragment"/>
  <text x="300" y="95" text-anchor="middle" class="section-title">HomeMainFragment</text>
  
  <!-- HomeMainFragment 核心功能 -->
  <rect x="70" y="110" width="220" height="80" class="home-module"/>
  <text x="180" y="130" text-anchor="middle" class="method-title">核心功能</text>
  <text x="80" y="150" class="text">• 医疗家庭版主界面</text>
  <text x="80" y="165" class="text">• 用户信息管理</text>
  <text x="80" y="180" class="text">• 疗程模块容器</text>
  
  <!-- RecyclerView管理 -->
  <rect x="300" y="110" width="220" height="80" class="home-module"/>
  <text x="410" y="130" text-anchor="middle" class="method-title">RecyclerView管理</text>
  <text x="310" y="150" class="text">• rvTreatmentModule</text>
  <text x="310" y="165" class="text">• LinearLayoutManager(HORIZONTAL)</text>
  <text x="310" y="180" class="text">• TreatmentModuleItemDecoration</text>
  
  <!-- ViewModel交互 -->
  <rect x="70" y="200" width="220" height="80" class="home-module"/>
  <text x="180" y="220" text-anchor="middle" class="method-title">ViewModel交互</text>
  <text x="80" y="240" class="text">• homeVM.getMedicalHomeProfile()</text>
  <text x="80" y="255" class="text">• 观察medicalHomeProfileLiveData</text>
  <text x="80" y="270" class="text">• updateTreatmentModule(modules)</text>
  
  <!-- Adapter数据设置 -->
  <rect x="300" y="200" width="220" height="80" class="home-module"/>
  <text x="410" y="220" text-anchor="middle" class="method-title">Adapter数据设置</text>
  <text x="310" y="240" class="text">• mModuleAdapter.setTreatmentModuleData()</text>
  <text x="310" y="255" class="text">• 传递childFragmentManager</text>
  <text x="310" y="270" class="text">• notifyDataSetChanged()</text>
  
  <!-- Fragment生命周期 -->
  <rect x="70" y="290" width="450" height="70" class="home-module"/>
  <text x="295" y="310" text-anchor="middle" class="method-title">Fragment生命周期管理</text>
  <text x="80" y="330" class="text">• initView() → 设置RecyclerView和Adapter</text>
  <text x="80" y="345" class="text">• initObserver() → 监听ViewModel数据变化，自动更新治疗模块</text>

  <!-- TreatmentModuleAdapter -->
  <rect x="650" y="70" width="500" height="300" class="adapter"/>
  <text x="900" y="95" text-anchor="middle" class="section-title">TreatmentModuleAdapter</text>
  
  <!-- Adapter核心功能 -->
  <rect x="670" y="110" width="220" height="80" class="adapter-module"/>
  <text x="780" y="130" text-anchor="middle" class="method-title">Adapter核心功能</text>
  <text x="680" y="150" class="text">• RecyclerView.Adapter实现</text>
  <text x="680" y="165" class="text">• 治疗模块数据绑定</text>
  <text x="680" y="180" class="text">• Fragment动态管理</text>
  
  <!-- 数据设置方法 -->
  <rect x="900" y="110" width="220" height="80" class="adapter-module"/>
  <text x="1010" y="130" text-anchor="middle" class="method-title">数据设置方法</text>
  <text x="910" y="150" class="text">• setTreatmentModuleData()</text>
  <text x="910" y="165" class="text">• 接收List&lt;Module&gt;数据</text>
  <text x="910" y="180" class="text">• 保存FragmentManager引用</text>
  
  <!-- ViewHolder绑定 -->
  <rect x="670" y="200" width="220" height="80" class="adapter-module"/>
  <text x="780" y="220" text-anchor="middle" class="method-title">ViewHolder绑定</text>
  <text x="680" y="240" class="text">• onBindViewHolder(holder, position)</text>
  <text x="680" y="255" class="text">• 动态设置FrameLayout宽度</text>
  <text x="680" y="270" class="text">• 根据moduleKey替换Fragment</text>
  
  <!-- Fragment替换逻辑 -->
  <rect x="900" y="200" width="220" height="80" class="adapter-module"/>
  <text x="1010" y="220" text-anchor="middle" class="method-title">Fragment替换逻辑</text>
  <text x="910" y="240" class="text">• OCCLUSION_THERAPY →</text>
  <text x="910" y="255" class="text">  MaskTherapyFragment.newInstance()</text>
  <text x="910" y="270" class="text">• VISION_THERAPY → VisualTrainFragment</text>
  
  <!-- 布局管理 -->
  <rect x="670" y="290" width="460" height="70" class="adapter-module"/>
  <text x="900" y="310" text-anchor="middle" class="method-title">布局管理</text>
  <text x="680" y="330" class="text">• isFull判断：单模块时MATCH_PARENT，多模块时452.dp2px宽度</text>
  <text x="680" y="345" class="text">• FragmentTransaction.replace(flModuleRoot.id, fragment)</text>

  <!-- MaskTherapyFragment -->
  <rect x="1200" y="70" width="500" height="300" class="mask-fragment"/>
  <text x="1450" y="95" text-anchor="middle" class="section-title">MaskTherapyFragment</text>
  
  <!-- Fragment创建 -->
  <rect x="1220" y="110" width="220" height="80" class="mask-module"/>
  <text x="1330" y="130" text-anchor="middle" class="method-title">Fragment创建</text>
  <text x="1230" y="150" class="text">• newInstance(moduleName, isFull)</text>
  <text x="1230" y="165" class="text">• 接收Adapter传递的参数</text>
  <text x="1230" y="180" class="text">• 设置Bundle参数</text>
  
  <!-- 参数初始化 -->
  <rect x="1450" y="110" width="220" height="80" class="mask-module"/>
  <text x="1560" y="130" text-anchor="middle" class="method-title">参数初始化</text>
  <text x="1460" y="150" class="text">• initParam() 获取参数</text>
  <text x="1460" y="165" class="text">• mModuleName设置</text>
  <text x="1460" y="180" class="text">• isFull布局模式</text>
  
  <!-- UI组件管理 -->
  <rect x="1220" y="200" width="220" height="80" class="mask-module"/>
  <text x="1330" y="220" text-anchor="middle" class="method-title">UI组件管理</text>
  <text x="1230" y="240" class="text">• TimeProgress进度显示</text>
  <text x="1230" y="255" class="text">• 遮盖疗法开关控制</text>
  <text x="1230" y="270" class="text">• CommonAppView应用管理</text>
  
  <!-- 业务逻辑 -->
  <rect x="1450" y="200" width="220" height="80" class="mask-module"/>
  <text x="1560" y="220" text-anchor="middle" class="method-title">业务逻辑</text>
  <text x="1460" y="240" class="text">• 遮盖疗法控制逻辑</text>
  <text x="1460" y="255" class="text">• GazeTrackService通信</text>
  <text x="1460" y="270" class="text">• 治疗状态管理</text>
  
  <!-- 生命周期 -->
  <rect x="1220" y="290" width="460" height="70" class="mask-module"/>
  <text x="1450" y="310" text-anchor="middle" class="method-title">生命周期</text>
  <text x="1230" y="330" class="text">• 作为子Fragment被Adapter管理，继承父Fragment的生命周期</text>
  <text x="1230" y="345" class="text">• 通过childFragmentManager进行Fragment事务操作</text>

  <!-- 共享组件 -->
  <rect x="50" y="400" width="1650" height="150" class="shared-component"/>
  <text x="875" y="425" text-anchor="middle" class="section-title">共享组件与数据流</text>
  
  <!-- ViewModel共享 -->
  <rect x="70" y="450" width="250" height="80" class="shared-module"/>
  <text x="195" y="470" text-anchor="middle" class="method-title">ViewModel共享</text>
  <text x="80" y="490" class="text">• HomeViewModel - 家庭版配置管理</text>
  <text x="80" y="505" class="text">• MaskViewModel - 遮盖疗法数据</text>
  <text x="80" y="520" class="text">• TreatmentViewModel - 疗程管理</text>
  
  <!-- FragmentManager -->
  <rect x="340" y="450" width="250" height="80" class="shared-module"/>
  <text x="465" y="470" text-anchor="middle" class="method-title">FragmentManager</text>
  <text x="350" y="490" class="text">• childFragmentManager传递</text>
  <text x="350" y="505" class="text">• Fragment事务管理</text>
  <text x="350" y="520" class="text">• 生命周期同步</text>
  
  <!-- 事件总线 -->
  <rect x="610" y="450" width="250" height="80" class="shared-module"/>
  <text x="735" y="470" text-anchor="middle" class="method-title">事件总线</text>
  <text x="620" y="490" class="text">• LiveEventBus跨组件通信</text>
  <text x="620" y="505" class="text">• EVENT_REFRESH_BIND_USER</text>
  <text x="620" y="520" class="text">• EVENT_SWITCH_CURE</text>
  
  <!-- 配置管理 -->
  <rect x="880" y="450" width="250" height="80" class="shared-module"/>
  <text x="1005" y="470" text-anchor="middle" class="method-title">配置管理</text>
  <text x="890" y="490" class="text">• UserManager用户管理</text>
  <text x="890" y="505" class="text">• DeviceManager设备管理</text>
  <text x="890" y="520" class="text">• TreatmentManager疗程管理</text>
  
  <!-- 服务通信 -->
  <rect x="1150" y="450" width="250" height="80" class="shared-module"/>
  <text x="1275" y="470" text-anchor="middle" class="method-title">服务通信</text>
  <text x="1160" y="490" class="text">• GazeTrackService眼动服务</text>
  <text x="1160" y="505" class="text">• Messenger消息通信</text>
  <text x="1160" y="520" class="text">• 状态同步机制</text>
  
  <!-- 对话框管理 -->
  <rect x="1420" y="450" width="250" height="80" class="shared-module"/>
  <text x="1545" y="470" text-anchor="middle" class="method-title">对话框管理</text>
  <text x="1430" y="490" class="text">• DialogTaskManager队列管理</text>
  <text x="1430" y="505" class="text">• 治疗状态对话框</text>
  <text x="1430" y="520" class="text">• 通知对话框</text>

  <!-- 连接箭头和关系说明 -->
  
  <!-- HomeMainFragment 到 TreatmentModuleAdapter -->
  <line x1="550" y1="220" x2="650" y2="220" class="creation-flow"/>
  <text x="600" y="210" text-anchor="middle" class="relationship-text" style="font-weight: bold;">创建和数据传递</text>
  
  <!-- TreatmentModuleAdapter 到 MaskTherapyFragment -->
  <line x1="1150" y1="220" x2="1200" y2="220" class="creation-flow"/>
  <text x="1175" y="210" text-anchor="middle" class="relationship-text" style="font-weight: bold;">Fragment实例化</text>
  
  <!-- 双向数据流 -->
  <line x1="300" y1="380" x2="900" y2="380" class="data-flow"/>
  <line x1="900" y1="390" x2="300" y2="390" class="data-flow"/>
  <text x="600" y="375" text-anchor="middle" class="relationship-text" style="font-weight: bold;">数据流和状态同步</text>
  
  <!-- 共享组件连接 -->
  <line x1="300" y1="370" x2="465" y2="450" class="lifecycle-flow"/>
  <line x1="900" y1="370" x2="735" y2="450" class="lifecycle-flow"/>
  <line x1="1450" y1="370" x2="1275" y2="450" class="lifecycle-flow"/>

  <!-- 详细关系说明 -->
  <rect x="50" y="570" width="1650" height="780" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="875" y="595" text-anchor="middle" class="title" style="font-size: 22px;">组件关系详细说明</text>

  <!-- 第一部分：创建关系 -->
  <text x="70" y="630" class="flow-text" style="font-weight: bold; font-size: 16px;">🏗️ 创建关系链</text>

  <text x="70" y="655" class="relationship-text" style="font-weight: bold;">1. HomeMainFragment → TreatmentModuleAdapter</text>
  <text x="90" y="675" class="flow-text">• <tspan style="font-weight: bold;">initView()阶段：</tspan>HomeMainFragment创建TreatmentModuleAdapter实例</text>
  <text x="90" y="690" class="flow-text">• <tspan style="font-weight: bold;">设置到RecyclerView：</tspan>rvTreatmentModule.adapter = mModuleAdapter</text>
  <text x="90" y="705" class="flow-text">• <tspan style="font-weight: bold;">布局管理器：</tspan>LinearLayoutManager(HORIZONTAL) + TreatmentModuleItemDecoration</text>

  <text x="70" y="730" class="relationship-text" style="font-weight: bold;">2. TreatmentModuleAdapter → MaskTherapyFragment</text>
  <text x="90" y="750" class="flow-text">• <tspan style="font-weight: bold;">onBindViewHolder()触发：</tspan>当moduleKey == "OCCLUSION_THERAPY"时</text>
  <text x="90" y="765" class="flow-text">• <tspan style="font-weight: bold;">Fragment实例化：</tspan>MaskTherapyFragment.newInstance(moduleName, isFull)</text>
  <text x="90" y="780" class="flow-text">• <tspan style="font-weight: bold;">Fragment替换：</tspan>fragmentManager.beginTransaction().replace(flModuleRoot.id, fragment).commit()</text>

  <text x="70" y="805" class="relationship-text" style="font-weight: bold;">3. 参数传递机制</text>
  <text x="90" y="825" class="flow-text">• <tspan style="font-weight: bold;">moduleName：</tspan>从HomeMainFragment传递到Adapter，再传递给MaskTherapyFragment</text>
  <text x="90" y="840" class="flow-text">• <tspan style="font-weight: bold;">isFull：</tspan>布局模式参数，决定CommonAppView的SpanCount(4列/8列)</text>
  <text x="90" y="855" class="flow-text">• <tspan style="font-weight: bold;">childFragmentManager：</tspan>从HomeMainFragment传递，用于子Fragment管理</text>

  <!-- 第二部分：数据流关系 -->
  <text x="900" y="630" class="flow-text" style="font-weight: bold; font-size: 16px;">📊 数据流关系</text>

  <text x="900" y="655" class="relationship-text" style="font-weight: bold;">1. ViewModel数据流向</text>
  <text x="920" y="675" class="flow-text">• <tspan style="font-weight: bold;">HomeViewModel：</tspan>HomeMainFragment获取医疗家庭版配置</text>
  <text x="920" y="690" class="flow-text">• <tspan style="font-weight: bold;">配置过滤：</tspan>updateTreatmentModule()过滤启用的模块</text>
  <text x="920" y="705" class="flow-text">• <tspan style="font-weight: bold;">数据传递：</tspan>mModuleAdapter.setTreatmentModuleData(modules, childFragmentManager)</text>

  <text x="900" y="730" class="relationship-text" style="font-weight: bold;">2. MaskViewModel共享</text>
  <text x="920" y="750" class="flow-text">• <tspan style="font-weight: bold;">Activity级别共享：</tspan>activityViewModels()确保数据一致性</text>
  <text x="920" y="765" class="flow-text">• <tspan style="font-weight: bold;">LiveData观察：</tspan>MaskTherapyFragment观察occlusionTherapyLiveData</text>
  <text x="920" y="780" class="flow-text">• <tspan style="font-weight: bold;">状态同步：</tspan>mtStateLiveData同步遮盖疗法开关状态</text>

  <text x="900" y="805" class="relationship-text" style="font-weight: bold;">3. 事件总线通信</text>
  <text x="920" y="825" class="flow-text">• <tspan style="font-weight: bold;">EVENT_REFRESH_BIND_USER：</tspan>HomeMainFragment刷新配置</text>
  <text x="920" y="840" class="flow-text">• <tspan style="font-weight: bold;">EVENT_SWITCH_CURE：</tspan>MaskTherapyFragment状态变化通知</text>
  <text x="920" y="855" class="flow-text">• <tspan style="font-weight: bold;">跨组件通信：</tspan>解耦组件间的直接依赖</text>

  <!-- 第三部分：生命周期关系 -->
  <text x="70" y="890" class="flow-text" style="font-weight: bold; font-size: 16px;">🔄 生命周期关系</text>

  <text x="70" y="915" class="relationship-text" style="font-weight: bold;">1. Fragment生命周期层次</text>
  <text x="90" y="935" class="flow-text">• <tspan style="font-weight: bold;">父Fragment：</tspan>HomeMainFragment作为容器Fragment</text>
  <text x="90" y="950" class="flow-text">• <tspan style="font-weight: bold;">子Fragment：</tspan>MaskTherapyFragment通过childFragmentManager管理</text>
  <text x="90" y="965" class="flow-text">• <tspan style="font-weight: bold;">生命周期继承：</tspan>子Fragment跟随父Fragment的生命周期</text>

  <text x="70" y="990" class="relationship-text" style="font-weight: bold;">2. Adapter生命周期</text>
  <text x="90" y="1010" class="flow-text">• <tspan style="font-weight: bold;">创建时机：</tspan>HomeMainFragment.initView()阶段创建</text>
  <text x="90" y="1025" class="flow-text">• <tspan style="font-weight: bold;">数据更新：</tspan>观察medicalHomeProfileLiveData触发setTreatmentModuleData()</text>
  <text x="90" y="1040" class="flow-text">• <tspan style="font-weight: bold;">Fragment管理：</tspan>负责MaskTherapyFragment的创建和替换</text>

  <text x="70" y="1065" class="relationship-text" style="font-weight: bold;">3. 服务绑定关系</text>
  <text x="90" y="1085" class="flow-text">• <tspan style="font-weight: bold;">MaskTherapyFragment：</tspan>onResume()绑定GazeTrackService</text>
  <text x="90" y="1100" class="flow-text">• <tspan style="font-weight: bold;">服务通信：</tspan>通过Messenger与Native层通信</text>
  <text x="90" y="1115" class="flow-text">• <tspan style="font-weight: bold;">状态同步：</tspan>实时更新治疗进度和状态</text>

  <!-- 第四部分：交互关系 -->
  <text x="900" y="890" class="flow-text" style="font-weight: bold; font-size: 16px;">🔗 交互关系</text>

  <text x="900" y="915" class="relationship-text" style="font-weight: bold;">1. 用户交互流程</text>
  <text x="920" y="935" class="flow-text">• <tspan style="font-weight: bold;">HomeMainFragment：</tspan>用户查看疗程模块，选择遮盖疗法</text>
  <text x="920" y="950" class="flow-text">• <tspan style="font-weight: bold;">TreatmentModuleAdapter：</tspan>响应用户选择，动态加载对应Fragment</text>
  <text x="920" y="965" class="flow-text">• <tspan style="font-weight: bold;">MaskTherapyFragment：</tspan>用户操作遮盖疗法开关，执行治疗</text>

  <text x="900" y="990" class="relationship-text" style="font-weight: bold;">2. 状态管理协作</text>
  <text x="920" y="1010" class="flow-text">• <tspan style="font-weight: bold;">权限检查：</tspan>MaskTherapyFragment检查用户绑定状态</text>
  <text x="920" y="1025" class="flow-text">• <tspan style="font-weight: bold;">疗程验证：</tspan>通过TreatmentViewModel验证疗程状态</text>
  <text x="920" y="1040" class="flow-text">• <tspan style="font-weight: bold;">状态反馈：</tspan>通过对话框和Toast向用户反馈操作结果</text>

  <text x="900" y="1065" class="relationship-text" style="font-weight: bold;">3. 错误处理协作</text>
  <text x="920" y="1085" class="flow-text">• <tspan style="font-weight: bold;">网络异常：</tspan>HomeMainFragment显示网络异常页面</text>
  <text x="920" y="1100" class="flow-text">• <tspan style="font-weight: bold;">服务异常：</tspan>MaskTherapyFragment处理GazeTrackService连接失败</text>
  <text x="920" y="1115" class="flow-text">• <tspan style="font-weight: bold;">统一管理：</tspan>通过DialogTaskManager统一管理错误对话框</text>

  <!-- 第五部分：架构优势 -->
  <text x="70" y="1150" class="flow-text" style="font-weight: bold; font-size: 16px;">✨ 架构优势</text>

  <text x="70" y="1175" class="relationship-text" style="font-weight: bold;">1. 模块化设计</text>
  <text x="90" y="1195" class="flow-text">• <tspan style="font-weight: bold;">职责分离：</tspan>HomeMainFragment负责容器管理，MaskTherapyFragment专注业务逻辑</text>
  <text x="90" y="1210" class="flow-text">• <tspan style="font-weight: bold;">可扩展性：</tspan>TreatmentModuleAdapter支持动态添加新的治疗模块</text>
  <text x="90" y="1225" class="flow-text">• <tspan style="font-weight: bold;">代码复用：</tspan>Adapter模式实现Fragment的统一管理</text>

  <text x="70" y="1250" class="relationship-text" style="font-weight: bold;">2. 数据驱动</text>
  <text x="90" y="1270" class="flow-text">• <tspan style="font-weight: bold;">响应式更新：</tspan>LiveData自动驱动UI更新，减少手动刷新</text>
  <text x="90" y="1285" class="flow-text">• <tspan style="font-weight: bold;">状态一致性：</tspan>ViewModel共享确保数据状态的一致性</text>
  <text x="90" y="1300" class="flow-text">• <tspan style="font-weight: bold;">事件解耦：</tspan>LiveEventBus实现组件间的松耦合通信</text>

  <!-- 第六部分：关键特性 -->
  <text x="900" y="1150" class="flow-text" style="font-weight: bold; font-size: 16px;">🎯 关键特性</text>

  <text x="900" y="1175" class="relationship-text" style="font-weight: bold;">1. 动态Fragment管理</text>
  <text x="920" y="1195" class="flow-text">• <tspan style="font-weight: bold;">运行时替换：</tspan>根据配置动态替换不同的治疗Fragment</text>
  <text x="920" y="1210" class="flow-text">• <tspan style="font-weight: bold;">内存优化：</tspan>只加载当前需要的Fragment，节省内存</text>
  <text x="920" y="1225" class="flow-text">• <tspan style="font-weight: bold;">状态保持：</tspan>Fragment切换时保持用户操作状态</text>

  <text x="900" y="1250" class="relationship-text" style="font-weight: bold;">2. 用户体验优化</text>
  <text x="920" y="1270" class="flow-text">• <tspan style="font-weight: bold;">无缝切换：</tspan>模块间切换流畅，无明显加载延迟</text>
  <text x="920" y="1285" class="flow-text">• <tspan style="font-weight: bold;">状态同步：</tspan>实时同步治疗状态，提供即时反馈</text>
  <text x="920" y="1300" class="flow-text">• <tspan style="font-weight: bold;">错误恢复：</tspan>完善的错误处理和恢复机制</text>

  <!-- 底部总结 -->
  <rect x="70" y="1320" width="1560" height="60" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1345" class="flow-text" style="font-weight: bold; font-size: 16px;">🏆 架构总结</text>
  <text x="90" y="1365" class="flow-text">这种三层架构设计实现了<tspan style="font-weight: bold;">容器管理</tspan>、<tspan style="font-weight: bold;">适配器模式</tspan>和<tspan style="font-weight: bold;">业务逻辑</tspan>的完美分离，通过ViewModel和事件总线实现数据驱动和组件解耦，为医疗家庭版提供了可扩展、可维护的模块化架构。</text>

</svg>
