<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="webviewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e1f5fe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81d4fa;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="trainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="nativeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#00000030"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f5f5f5"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    医疗应用架构图 - Activity结构分析
  </text>
  
  <!-- 主应用入口 -->
  <rect x="500" y="80" width="200" height="60" rx="10" fill="#4CAF50" filter="url(#shadow)"/>
  <text x="600" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    医疗应用主界面
  </text>
  
  <!-- 连接线 -->
  <line x1="500" y1="140" x2="200" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="140" x2="600" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="140" x2="1000" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- InspectionCenterActivity (检查中心) -->
  <g>
    <rect x="50" y="200" width="300" height="80" rx="10" fill="url(#webviewGradient)" filter="url(#shadow)"/>
    <text x="200" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#0277BD">
      InspectionCenterActivity
    </text>
    <text x="200" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#0277BD">
      检查中心
    </text>
    <text x="200" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#01579B">
      ✅ 完全使用WebView
    </text>
    
    <!-- WebView容器 -->
    <rect x="70" y="300" width="260" height="50" rx="5" fill="#81d4fa" filter="url(#shadow)"/>
    <text x="200" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#01579B">
      InspectionCenterWebView
    </text>
    
    <!-- JavaScript接口 -->
    <rect x="80" y="370" width="100" height="30" rx="5" fill="#4fc3f7"/>
    <text x="130" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      JS接口: Inspection
    </text>
    
    <!-- 功能模块 -->
    <rect x="200" y="370" width="80" height="30" rx="5" fill="#4fc3f7"/>
    <text x="240" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      翻转拍控制
    </text>
    
    <rect x="80" y="410" width="80" height="30" rx="5" fill="#4fc3f7"/>
    <text x="120" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      页面打印
    </text>
    
    <rect x="200" y="410" width="80" height="30" rx="5" fill="#4fc3f7"/>
    <text x="240" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#01579B">
      URL加载
    </text>
  </g>
  
  <!-- TrainCenterActivity (训练中心) -->
  <g>
    <rect x="450" y="200" width="300" height="80" rx="10" fill="url(#trainGradient)" filter="url(#shadow)"/>
    <text x="600" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      TrainCenterActivity
    </text>
    <text x="600" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#2E7D32">
      训练中心
    </text>
    <text x="600" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1B5E20">
      ✅ 完全使用WebView
    </text>
    
    <!-- WebView容器 -->
    <rect x="470" y="300" width="260" height="50" rx="5" fill="#a5d6a7" filter="url(#shadow)"/>
    <text x="600" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1B5E20">
      TrainCenterWebView
    </text>
    
    <!-- JavaScript接口 -->
    <rect x="480" y="370" width="80" height="30" rx="5" fill="#81c784"/>
    <text x="520" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1B5E20">
      JS接口: Train
    </text>
    
    <!-- 功能模块 -->
    <rect x="580" y="370" width="80" height="30" rx="5" fill="#81c784"/>
    <text x="620" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1B5E20">
      眼动追踪
    </text>
    
    <rect x="480" y="410" width="80" height="30" rx="5" fill="#81c784"/>
    <text x="520" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1B5E20">
      校准功能
    </text>
    
    <rect x="580" y="410" width="80" height="30" rx="5" fill="#81c784"/>
    <text x="620" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1B5E20">
      设备信息
    </text>
    
    <rect x="680" y="370" width="60" height="30" rx="5" fill="#81c784"/>
    <text x="710" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1B5E20">
      翻转拍
    </text>
  </g>
  
  <!-- MHospitalMTActivity (遮盖疗法) -->
  <g>
    <rect x="850" y="200" width="300" height="80" rx="10" fill="url(#nativeGradient)" filter="url(#shadow)"/>
    <text x="1000" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#E65100">
      MHospitalMTActivity
    </text>
    <text x="1000" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#E65100">
      遮盖疗法
    </text>
    <text x="1000" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#BF360C">
      ❌ 原生Android界面
    </text>
    
    <!-- Fragment容器 -->
    <rect x="870" y="300" width="120" height="50" rx="5" fill="#ffcc80" filter="url(#shadow)"/>
    <text x="930" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#BF360C">
      MHospitalMT
    </text>
    <text x="930" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#BF360C">
      Fragment
    </text>
    
    <rect x="1010" y="300" width="120" height="50" rx="5" fill="#ffcc80" filter="url(#shadow)"/>
    <text x="1070" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#BF360C">
      PatientLibrary
    </text>
    <text x="1070" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#BF360C">
      Fragment
    </text>
    
    <!-- 原生组件 -->
    <rect x="880" y="370" width="80" height="30" rx="5" fill="#ffb74d"/>
    <text x="920" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#BF360C">
      遮盖开关
    </text>
    
    <rect x="880" y="410" width="80" height="30" rx="5" fill="#ffb74d"/>
    <text x="920" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#BF360C">
      治疗进度
    </text>
    
    <rect x="1020" y="370" width="80" height="30" rx="5" fill="#ffb74d"/>
    <text x="1060" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#BF360C">
      患者列表
    </text>
    
    <rect x="1020" y="410" width="80" height="30" rx="5" fill="#ffb74d"/>
    <text x="1060" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#BF360C">
      新建患者
    </text>
  </g>
  
  <!-- 连接线 -->
  <line x1="200" y1="280" x2="200" y2="300" stroke="#0277BD" stroke-width="2"/>
  <line x1="600" y1="280" x2="600" y2="300" stroke="#2E7D32" stroke-width="2"/>
  <line x1="1000" y1="280" x2="930" y2="300" stroke="#E65100" stroke-width="2"/>
  <line x1="1000" y1="280" x2="1070" y2="300" stroke="#E65100" stroke-width="2"/>
  
  <!-- 图例 -->
  <g>
    <text x="50" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#333">
      架构特点说明：
    </text>
    
    <rect x="50" y="540" width="20" height="15" fill="url(#webviewGradient)"/>
    <text x="80" y="552" font-family="Arial, sans-serif" font-size="12" fill="#333">
      WebView架构 - 检查中心和训练中心都使用WebView加载Web内容
    </text>
    
    <rect x="50" y="570" width="20" height="15" fill="url(#nativeGradient)"/>
    <text x="80" y="582" font-family="Arial, sans-serif" font-size="12" fill="#333">
      原生架构 - 遮盖疗法使用纯Android原生界面，需要精确硬件控制
    </text>
    
    <text x="50" y="610" font-family="Arial, sans-serif" font-size="12" fill="#666">
      • WebView应用通过JavaScript接口与原生功能交互
    </text>
    <text x="50" y="630" font-family="Arial, sans-serif" font-size="12" fill="#666">
      • 原生应用使用Fragment架构，提供更好的性能和用户体验
    </text>
    <text x="50" y="650" font-family="Arial, sans-serif" font-size="12" fill="#666">
      • 所有应用都支持翻转拍硬件控制和眼动追踪功能
    </text>
  </g>
  
  <!-- 版权信息 -->
  <text x="600" y="780" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#999">
    医疗眼动追踪应用架构图 - 生成时间: 2025-07-22
  </text>
</svg>
