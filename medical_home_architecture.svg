<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .layer-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .module-bg { fill: #ffffff; stroke: #3498db; stroke-width: 2; rx: 8; }
      .service-bg { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .data-bg { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .network-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 8; }
      .hardware-bg { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; rx: 8; }
      .flow-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">医疗家庭版眼动追踪系统架构图</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="60" width="1700" height="180" class="layer-bg"/>
  <text x="70" y="85" class="subtitle">用户界面层 (UI Layer)</text>
  
  <!-- 主界面模块 -->
  <rect x="80" y="100" width="200" height="120" class="module-bg"/>
  <text x="180" y="120" text-anchor="middle" class="subtitle">主界面</text>
  <text x="90" y="140" class="text">HomeMainFragment</text>
  <text x="90" y="155" class="text">医疗家庭版配置</text>
  <text x="90" y="170" class="text">疗程管理</text>
  <text x="90" y="185" class="text">用户绑定状态</text>
  <text x="90" y="200" class="text">设备状态显示</text>
  
  <!-- 遮盖疗法模块 -->
  <rect x="300" y="100" width="200" height="120" class="module-bg"/>
  <text x="400" y="120" text-anchor="middle" class="subtitle">遮盖疗法</text>
  <text x="310" y="140" class="text">数字遮盖治疗</text>
  <text x="310" y="155" class="text">弱视眼训练</text>
  <text x="310" y="170" class="text">治疗时长管理</text>
  <text x="310" y="185" class="text">治疗参数配置</text>
  <text x="310" y="200" class="text">治疗进度跟踪</text>
  
  <!-- 视觉训练模块 -->
  <rect x="520" y="100" width="200" height="120" class="module-bg"/>
  <text x="620" y="120" text-anchor="middle" class="subtitle">视觉训练</text>
  <text x="530" y="140" class="text">视觉训练疗法</text>
  <text x="530" y="155" class="text">训练配置获取</text>
  <text x="530" y="170" class="text">训练结果提交</text>
  <text x="530" y="185" class="text">训练类别管理</text>
  <text x="530" y="200" class="text">蓝牙翻拍器支持</text>
  
  <!-- 眼动评估模块 -->
  <rect x="740" y="100" width="200" height="120" class="module-bg"/>
  <text x="840" y="120" text-anchor="middle" class="subtitle">眼动评估</text>
  <text x="750" y="140" class="text">注视稳定性检测</text>
  <text x="750" y="155" class="text">扫视能力评估</text>
  <text x="750" y="170" class="text">追随能力检测</text>
  <text x="750" y="185" class="text">兴趣区域检测</text>
  <text x="750" y="200" class="text">患者信息管理</text>
  
  <!-- 校准模块 -->
  <rect x="960" y="100" width="200" height="120" class="module-bg"/>
  <text x="1060" y="120" text-anchor="middle" class="subtitle">校准系统</text>
  <text x="970" y="140" class="text">姿势校准</text>
  <text x="970" y="155" class="text">视标校准</text>
  <text x="970" y="170" class="text">校准参数管理</text>
  <text x="970" y="185" class="text">校准结果验证</text>
  <text x="970" y="200" class="text">校准失败处理</text>
  
  <!-- 设置管理模块 -->
  <rect x="1180" y="100" width="200" height="120" class="module-bg"/>
  <text x="1280" y="120" text-anchor="middle" class="subtitle">设置管理</text>
  <text x="1190" y="140" class="text">参数设置</text>
  <text x="1190" y="155" class="text">帮助中心</text>
  <text x="1190" y="170" class="text">版本更新</text>
  <text x="1190" y="185" class="text">菜单弹窗</text>
  <text x="1190" y="200" class="text">网络异常处理</text>
  
  <!-- AI助手模块 -->
  <rect x="1400" y="100" width="200" height="120" class="module-bg"/>
  <text x="1500" y="120" text-anchor="middle" class="subtitle">AI助手</text>
  <text x="1410" y="140" class="text">ChatWebActivity</text>
  <text x="1410" y="155" class="text">智能对话</text>
  <text x="1410" y="170" class="text">AdaViewModel</text>
  <text x="1410" y="185" class="text">语音交互</text>
  <text x="1410" y="200" class="text">问题解答</text>

  <!-- 业务逻辑层 -->
  <rect x="50" y="260" width="1700" height="200" class="layer-bg"/>
  <text x="70" y="285" class="subtitle">业务逻辑层 (Business Logic Layer)</text>
  
  <!-- ViewModel层 -->
  <rect x="80" y="300" width="300" height="140" class="service-bg"/>
  <text x="230" y="320" text-anchor="middle" class="subtitle">ViewModel层</text>
  <text x="90" y="340" class="text">HomeViewModel - 家庭版配置管理</text>
  <text x="90" y="355" class="text">MaskViewModel - 遮盖疗法状态</text>
  <text x="90" y="370" class="text">TreatmentViewModel - 疗程管理</text>
  <text x="90" y="385" class="text">TrainViewModel - 训练管理</text>
  <text x="90" y="400" class="text">UserViewModel - 用户管理</text>
  <text x="90" y="415" class="text">UpdateViewModel - 更新管理</text>
  
  <!-- Repository层 -->
  <rect x="400" y="300" width="300" height="140" class="service-bg"/>
  <text x="550" y="320" text-anchor="middle" class="subtitle">Repository层</text>
  <text x="410" y="340" class="text">HomeRepository - 家庭版API</text>
  <text x="410" y="355" class="text">MaskRepository - 遮盖疗法API</text>
  <text x="410" y="370" class="text">TreatmentRepository - 疗程API</text>
  <text x="410" y="385" class="text">TrainRepository - 训练API</text>
  <text x="410" y="400" class="text">UserRepository - 用户API</text>
  <text x="410" y="415" class="text">PatientRepository - 患者API</text>
  
  <!-- 管理器层 -->
  <rect x="720" y="300" width="300" height="140" class="service-bg"/>
  <text x="870" y="320" text-anchor="middle" class="subtitle">管理器层</text>
  <text x="730" y="340" class="text">UserManager - 用户状态管理</text>
  <text x="730" y="355" class="text">TreatmentManager - 疗程状态</text>
  <text x="730" y="370" class="text">MaskManager - 遮盖参数管理</text>
  <text x="730" y="385" class="text">DeviceManager - 设备管理</text>
  <text x="730" y="400" class="text">UpdateManager - 更新管理</text>
  <text x="730" y="415" class="text">FlipBeatManager - 翻拍器</text>
  
  <!-- 眼动追踪服务 -->
  <rect x="1040" y="300" width="300" height="140" class="service-bg"/>
  <text x="1190" y="320" text-anchor="middle" class="subtitle">眼动追踪服务</text>
  <text x="1050" y="340" class="text">GazeTrackService - 追踪服务</text>
  <text x="1050" y="355" class="text">TrackingManager - 追踪管理</text>
  <text x="1050" y="370" class="text">GazeTrack - 追踪核心</text>
  <text x="1050" y="385" class="text">AppliedManager - 应用管理</text>
  <text x="1050" y="400" class="text">CalibrationActivity - 校准</text>
  <text x="1050" y="415" class="text">WidgetManager - 视点显示</text>
  
  <!-- 通信服务 -->
  <rect x="1360" y="300" width="300" height="140" class="service-bg"/>
  <text x="1510" y="320" text-anchor="middle" class="subtitle">通信服务</text>
  <text x="1370" y="340" class="text">MQTTInitManager - MQTT初始化</text>
  <text x="1370" y="355" class="text">MQTTManager - MQTT管理</text>
  <text x="1370" y="370" class="text">NetworkManager - 网络管理</text>
  <text x="1370" y="385" class="text">ReportManager - 数据上报</text>
  <text x="1370" y="400" class="text">LiveEventBus - 事件总线</text>
  <text x="1370" y="415" class="text">RefreshBindUserReceiver</text>

  <!-- 数据层 -->
  <rect x="50" y="480" width="1700" height="180" class="layer-bg"/>
  <text x="70" y="505" class="subtitle">数据层 (Data Layer)</text>

  <!-- 本地存储 -->
  <rect x="80" y="520" width="250" height="120" class="data-bg"/>
  <text x="205" y="540" text-anchor="middle" class="subtitle">本地存储</text>
  <text x="90" y="560" class="text">MMKV - 键值存储</text>
  <text x="90" y="575" class="text">用户偏好设置</text>
  <text x="90" y="590" class="text">设备配置信息</text>
  <text x="90" y="605" class="text">校准参数缓存</text>
  <text x="90" y="620" class="text">疗程状态缓存</text>

  <!-- 文件系统 -->
  <rect x="350" y="520" width="250" height="120" class="data-bg"/>
  <text x="475" y="540" text-anchor="middle" class="subtitle">文件系统</text>
  <text x="360" y="560" class="text">AI模型文件</text>
  <text x="360" y="575" class="text">校准配置文件</text>
  <text x="360" y="590" class="text">图像缓存</text>
  <text x="360" y="605" class="text">日志文件</text>
  <text x="360" y="620" class="text">媒体资源</text>

  <!-- 数据库 -->
  <rect x="620" y="520" width="250" height="120" class="data-bg"/>
  <text x="745" y="540" text-anchor="middle" class="subtitle">数据库</text>
  <text x="630" y="560" class="text">患者信息表</text>
  <text x="630" y="575" class="text">检测结果表</text>
  <text x="630" y="590" class="text">训练记录表</text>
  <text x="630" y="605" class="text">校准数据表</text>
  <text x="630" y="620" class="text">审计字段支持</text>

  <!-- 缓存管理 -->
  <rect x="890" y="520" width="250" height="120" class="data-bg"/>
  <text x="1015" y="540" text-anchor="middle" class="subtitle">缓存管理</text>
  <text x="900" y="560" class="text">眼动轨迹缓存</text>
  <text x="900" y="575" class="text">图像处理缓存</text>
  <text x="900" y="590" class="text">网络请求缓存</text>
  <text x="900" y="605" class="text">配置信息缓存</text>
  <text x="900" y="620" class="text">用户状态缓存</text>

  <!-- 日志系统 -->
  <rect x="1160" y="520" width="250" height="120" class="data-bg"/>
  <text x="1285" y="540" text-anchor="middle" class="subtitle">日志系统</text>
  <text x="1170" y="560" class="text">Logger - 统一日志</text>
  <text x="1170" y="575" class="text">错误日志记录</text>
  <text x="1170" y="590" class="text">性能监控日志</text>
  <text x="1170" y="605" class="text">用户行为日志</text>
  <text x="1170" y="620" class="text">调试信息输出</text>

  <!-- 配置管理 -->
  <rect x="1430" y="520" width="250" height="120" class="data-bg"/>
  <text x="1555" y="540" text-anchor="middle" class="subtitle">配置管理</text>
  <text x="1440" y="560" class="text">UrlConfig - URL配置</text>
  <text x="1440" y="575" class="text">环境配置切换</text>
  <text x="1440" y="590" class="text">API域名管理</text>
  <text x="1440" y="605" class="text">版本配置</text>
  <text x="1440" y="620" class="text">功能开关配置</text>

  <!-- 网络层 -->
  <rect x="50" y="680" width="1700" height="160" class="layer-bg"/>
  <text x="70" y="705" class="subtitle">网络层 (Network Layer)</text>

  <!-- API服务 -->
  <rect x="80" y="720" width="280" height="100" class="network-bg"/>
  <text x="220" y="740" text-anchor="middle" class="subtitle">API服务</text>
  <text x="90" y="760" class="text">HomeApiService - 家庭版配置API</text>
  <text x="90" y="775" class="text">TreatmentApiService - 疗程API</text>
  <text x="90" y="790" class="text">MaskApiService - 遮盖疗法API</text>
  <text x="90" y="805" class="text">TrainApiService - 训练API</text>

  <!-- 网络客户端 -->
  <rect x="380" y="720" width="280" height="100" class="network-bg"/>
  <text x="520" y="740" text-anchor="middle" class="subtitle">网络客户端</text>
  <text x="390" y="760" class="text">MainRetrofitClient - 主API客户端</text>
  <text x="390" y="775" class="text">MovementClient - 运动评估客户端</text>
  <text x="390" y="790" class="text">OverseasRetrofitClient - 海外客户端</text>
  <text x="390" y="805" class="text">NetworkUtils - 网络工具</text>

  <!-- 数据上传 -->
  <rect x="680" y="720" width="280" height="100" class="network-bg"/>
  <text x="820" y="740" text-anchor="middle" class="subtitle">数据上传</text>
  <text x="690" y="760" class="text">图像上传服务</text>
  <text x="690" y="775" class="text">检测结果上传</text>
  <text x="690" y="790" class="text">训练数据上传</text>
  <text x="690" y="805" class="text">错误信息上报</text>

  <!-- 物联网通信 -->
  <rect x="980" y="720" width="280" height="100" class="network-bg"/>
  <text x="1120" y="740" text-anchor="middle" class="subtitle">物联网通信</text>
  <text x="990" y="760" class="text">MQTT消息推送</text>
  <text x="990" y="775" class="text">设备状态同步</text>
  <text x="990" y="790" class="text">远程控制指令</text>
  <text x="990" y="805" class="text">阿里云IoT集成</text>

  <!-- 错误处理 -->
  <rect x="1280" y="720" width="280" height="100" class="network-bg"/>
  <text x="1420" y="740" text-anchor="middle" class="subtitle">错误处理</text>
  <text x="1290" y="760" class="text">网络异常处理</text>
  <text x="1290" y="775" class="text">重试机制</text>
  <text x="1290" y="790" class="text">错误信息统一</text>
  <text x="1290" y="805" class="text">Toast消息提示</text>

  <!-- 硬件层 -->
  <rect x="50" y="860" width="1700" height="160" class="layer-bg"/>
  <text x="70" y="885" class="subtitle">硬件层 (Hardware Layer)</text>

  <!-- 摄像头系统 -->
  <rect x="80" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="205" y="920" text-anchor="middle" class="subtitle">摄像头系统</text>
  <text x="90" y="940" class="text">Camera2 API</text>
  <text x="90" y="955" class="text">ImageAnalysis</text>
  <text x="90" y="970" class="text">图像预处理</text>
  <text x="90" y="985" class="text">实时图像流</text>

  <!-- AI算法引擎 -->
  <rect x="350" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="475" y="920" text-anchor="middle" class="subtitle">AI算法引擎</text>
  <text x="360" y="940" class="text">C++ Native层</text>
  <text x="360" y="955" class="text">人脸检测算法</text>
  <text x="360" y="970" class="text">眼动追踪算法</text>
  <text x="360" y="985" class="text">校准算法</text>

  <!-- 图像处理 -->
  <rect x="620" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="745" y="920" text-anchor="middle" class="subtitle">图像处理</text>
  <text x="630" y="940" class="text">OpenCV处理</text>
  <text x="630" y="955" class="text">特征提取</text>
  <text x="630" y="970" class="text">图像增强</text>
  <text x="630" y="985" class="text">噪声过滤</text>

  <!-- 外设支持 -->
  <rect x="890" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="1015" y="920" text-anchor="middle" class="subtitle">外设支持</text>
  <text x="900" y="940" class="text">蓝牙翻拍器</text>
  <text x="900" y="955" class="text">触摸屏交互</text>
  <text x="900" y="970" class="text">音频播放</text>
  <text x="900" y="985" class="text">传感器数据</text>

  <!-- 性能优化 -->
  <rect x="1160" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="1285" y="920" text-anchor="middle" class="subtitle">性能优化</text>
  <text x="1170" y="940" class="text">多线程处理</text>
  <text x="1170" y="955" class="text">内存管理</text>
  <text x="1170" y="970" class="text">GPU加速</text>
  <text x="1170" y="985" class="text">电池优化</text>

  <!-- 系统集成 -->
  <rect x="1430" y="900" width="250" height="100" class="hardware-bg"/>
  <text x="1555" y="920" text-anchor="middle" class="subtitle">系统集成</text>
  <text x="1440" y="940" class="text">Android系统API</text>
  <text x="1440" y="955" class="text">权限管理</text>
  <text x="1440" y="970" class="text">生命周期管理</text>
  <text x="1440" y="985" class="text">后台服务</text>

  <!-- 业务流程图 -->
  <rect x="50" y="1040" width="1700" height="320" class="layer-bg"/>
  <text x="70" y="1065" class="subtitle">核心业务流程 (Business Process Flow)</text>

  <!-- 用户登录流程 -->
  <rect x="80" y="1080" width="200" height="80" class="module-bg"/>
  <text x="180" y="1100" text-anchor="middle" class="subtitle">用户登录</text>
  <text x="90" y="1120" class="small-text">1. 设备绑定检查</text>
  <text x="90" y="1135" class="small-text">2. 账户信息获取</text>
  <text x="90" y="1150" class="small-text">3. 疗程状态同步</text>

  <!-- 眼动校准流程 -->
  <rect x="300" y="1080" width="200" height="80" class="module-bg"/>
  <text x="400" y="1100" text-anchor="middle" class="subtitle">眼动校准</text>
  <text x="310" y="1120" class="small-text">1. 姿势校准</text>
  <text x="310" y="1135" class="small-text">2. 视标校准</text>
  <text x="310" y="1150" class="small-text">3. 参数验证</text>

  <!-- 遮盖疗法流程 -->
  <rect x="520" y="1080" width="200" height="80" class="module-bg"/>
  <text x="620" y="1100" text-anchor="middle" class="subtitle">遮盖疗法</text>
  <text x="530" y="1120" class="small-text">1. 疗法参数获取</text>
  <text x="530" y="1135" class="small-text">2. 实时眼动追踪</text>
  <text x="530" y="1150" class="small-text">3. 治疗时长记录</text>

  <!-- 视觉训练流程 -->
  <rect x="740" y="1080" width="200" height="80" class="module-bg"/>
  <text x="840" y="1100" text-anchor="middle" class="subtitle">视觉训练</text>
  <text x="750" y="1120" class="small-text">1. 训练配置获取</text>
  <text x="750" y="1135" class="small-text">2. 训练执行</text>
  <text x="750" y="1150" class="small-text">3. 结果数据上传</text>

  <!-- 眼动评估流程 -->
  <rect x="960" y="1080" width="200" height="80" class="module-bg"/>
  <text x="1060" y="1100" text-anchor="middle" class="subtitle">眼动评估</text>
  <text x="970" y="1120" class="small-text">1. 患者信息录入</text>
  <text x="970" y="1135" class="small-text">2. 多项检测执行</text>
  <text x="970" y="1150" class="small-text">3. 结果图像上传</text>

  <!-- 数据同步流程 -->
  <rect x="1180" y="1080" width="200" height="80" class="module-bg"/>
  <text x="1280" y="1100" text-anchor="middle" class="subtitle">数据同步</text>
  <text x="1190" y="1120" class="small-text">1. 本地数据缓存</text>
  <text x="1190" y="1135" class="small-text">2. 网络状态检查</text>
  <text x="1190" y="1150" class="small-text">3. 云端数据同步</text>

  <!-- 系统监控流程 -->
  <rect x="1400" y="1080" width="200" height="80" class="module-bg"/>
  <text x="1500" y="1100" text-anchor="middle" class="subtitle">系统监控</text>
  <text x="1410" y="1120" class="small-text">1. 设备状态监控</text>
  <text x="1410" y="1135" class="small-text">2. 性能数据收集</text>
  <text x="1410" y="1150" class="small-text">3. 异常处理</text>

  <!-- 数据流连接线 -->
  <!-- UI层到业务逻辑层 -->
  <line x1="180" y1="220" x2="230" y2="300" class="flow-arrow"/>
  <line x1="400" y1="220" x2="550" y2="300" class="flow-arrow"/>
  <line x1="620" y1="220" x2="870" y2="300" class="flow-arrow"/>
  <line x1="840" y1="220" x2="1190" y2="300" class="flow-arrow"/>
  <line x1="1060" y1="220" x2="1190" y2="300" class="flow-arrow"/>

  <!-- 业务逻辑层到数据层 -->
  <line x1="230" y1="440" x2="205" y2="520" class="flow-arrow"/>
  <line x1="550" y1="440" x2="475" y2="520" class="flow-arrow"/>
  <line x1="870" y1="440" x2="745" y2="520" class="flow-arrow"/>
  <line x1="1190" y1="440" x2="1015" y2="520" class="flow-arrow"/>

  <!-- 业务逻辑层到网络层 -->
  <line x1="550" y1="440" x2="520" y2="720" class="flow-arrow"/>
  <line x1="870" y1="440" x2="820" y2="720" class="flow-arrow"/>
  <line x1="1510" y1="440" x2="1120" y2="720" class="flow-arrow"/>

  <!-- 眼动追踪到硬件层 -->
  <line x1="1190" y1="440" x2="475" y2="900" class="flow-arrow"/>
  <line x1="1190" y1="440" x2="745" y2="900" class="flow-arrow"/>
  <line x1="1190" y1="440" x2="205" y2="900" class="flow-arrow"/>

  <!-- 数据流箭头 (虚线表示数据流) -->
  <line x1="205" y1="900" x2="475" y2="900" class="data-flow"/>
  <line x1="475" y1="900" x2="745" y2="900" class="data-flow"/>
  <line x1="745" y1="900" x2="1015" y2="900" class="data-flow"/>

  <!-- 业务流程连接 -->
  <line x1="280" y1="1120" x2="300" y2="1120" class="flow-arrow"/>
  <line x1="500" y1="1120" x2="520" y2="1120" class="flow-arrow"/>
  <line x1="720" y1="1120" x2="740" y2="1120" class="flow-arrow"/>
  <line x1="940" y1="1120" x2="960" y2="1120" class="flow-arrow"/>
  <line x1="1160" y1="1120" x2="1180" y2="1120" class="flow-arrow"/>
  <line x1="1380" y1="1120" x2="1400" y2="1120" class="flow-arrow"/>

  <!-- 详细说明 -->
  <rect x="80" y="1180" width="1600" height="160" class="layer-bg"/>
  <text x="100" y="1205" class="subtitle">系统特性说明</text>

  <text x="100" y="1230" class="text">• 模块化架构：采用MVVM架构模式，UI层、业务逻辑层、数据层分离，便于维护和扩展</text>
  <text x="100" y="1250" class="text">• 实时眼动追踪：基于C++算法引擎，支持30fps实时眼动数据采集和处理</text>
  <text x="100" y="1270" class="text">• 多疗法支持：集成数字遮盖疗法、视觉训练疗法，支持个性化治疗参数配置</text>
  <text x="100" y="1290" class="text">• 智能校准系统：支持姿势校准和视标校准，确保眼动追踪精度</text>
  <text x="100" y="1310" class="text">• 云端数据同步：支持治疗数据、检测结果的云端存储和同步，便于医生远程监控</text>
  <text x="100" y="1330" class="text">• 物联网集成：通过MQTT协议实现设备远程管理和状态监控</text>

</svg>
