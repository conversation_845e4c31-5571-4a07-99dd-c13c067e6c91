<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #34495e; }
      .small-text { font-family: Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      
      .ui-layer { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .business-layer { fill: #f0f8e8; stroke: #27ae60; stroke-width: 2; }
      .data-layer { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .native-layer { fill: #f4e8fd; stroke: #9b59b6; stroke-width: 2; }
      
      .ui-module { fill: #d6eaf8; stroke: #2980b9; stroke-width: 1; }
      .business-module { fill: #d5f4e6; stroke: #229954; stroke-width: 1; }
      .data-module { fill: #fdeaa7; stroke: #d68910; stroke-width: 1; }
      .native-module { fill: #e8daef; stroke: #8e44ad; stroke-width: 1; }
      
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">医疗家庭版眼动追踪系统架构分析图</text>
  
  <!-- 需求分析层 -->
  <rect x="50" y="60" width="1500" height="120" class="ui-layer"/>
  <text x="800" y="85" text-anchor="middle" class="layer-title">需求分析层 (Requirements Analysis)</text>
  
  <rect x="80" y="100" width="200" height="60" class="ui-module"/>
  <text x="180" y="120" text-anchor="middle" class="module-title">功能需求</text>
  <text x="90" y="135" class="text">• 眼动追踪与校准</text>
  <text x="90" y="150" class="text">• 数字遮盖疗法</text>
  <text x="90" y="165" class="text">• 视觉训练疗法</text>
  
  <rect x="300" y="100" width="200" height="60" class="ui-module"/>
  <text x="400" y="120" text-anchor="middle" class="module-title">用户体验需求</text>
  <text x="310" y="135" class="text">• 直观的界面设计</text>
  <text x="310" y="150" class="text">• 实时反馈机制</text>
  <text x="310" y="165" class="text">• 个性化配置</text>
  
  <rect x="520" y="100" width="200" height="60" class="ui-module"/>
  <text x="620" y="120" text-anchor="middle" class="module-title">性能需求</text>
  <text x="530" y="135" class="text">• 30fps实时追踪</text>
  <text x="530" y="150" class="text">• 低延迟响应</text>
  <text x="530" y="165" class="text">• 稳定性保证</text>
  
  <rect x="740" y="100" width="200" height="60" class="ui-module"/>
  <text x="840" y="120" text-anchor="middle" class="module-title">数据需求</text>
  <text x="750" y="135" class="text">• 治疗数据存储</text>
  <text x="750" y="150" class="text">• 云端同步</text>
  <text x="750" y="165" class="text">• 隐私保护</text>
  
  <rect x="960" y="100" width="200" height="60" class="ui-module"/>
  <text x="1060" y="120" text-anchor="middle" class="module-title">设备需求</text>
  <text x="970" y="135" class="text">• 摄像头集成</text>
  <text x="970" y="150" class="text">• 硬件兼容性</text>
  <text x="970" y="165" class="text">• 物联网支持</text>
  
  <rect x="1180" y="100" width="200" height="60" class="ui-module"/>
  <text x="1280" y="120" text-anchor="middle" class="module-title">医疗需求</text>
  <text x="1190" y="135" class="text">• 临床标准遵循</text>
  <text x="1190" y="150" class="text">• 治疗效果评估</text>
  <text x="1190" y="165" class="text">• 医生监控接口</text>

  <!-- XML布局层 -->
  <rect x="50" y="200" width="1500" height="140" class="ui-layer"/>
  <text x="800" y="225" text-anchor="middle" class="layer-title">XML布局层 (Layout Design)</text>
  
  <rect x="80" y="250" width="180" height="70" class="ui-module"/>
  <text x="170" y="270" text-anchor="middle" class="module-title">主界面布局</text>
  <text x="90" y="285" class="text">fragment_home_main.xml</text>
  <text x="90" y="300" class="text">• ConstraintLayout根布局</text>
  <text x="90" y="315" class="text">• 用户信息区域</text>
  
  <rect x="280" y="250" width="180" height="70" class="ui-module"/>
  <text x="370" y="270" text-anchor="middle" class="module-title">治疗模块布局</text>
  <text x="290" y="285" class="text">item_treatment_module.xml</text>
  <text x="290" y="300" class="text">• FrameLayout容器</text>
  <text x="290" y="315" class="text">• 动态Fragment加载</text>
  
  <rect x="480" y="250" width="180" height="70" class="ui-module"/>
  <text x="570" y="270" text-anchor="middle" class="module-title">校准界面布局</text>
  <text x="490" y="285" class="text">view_posture_calibration.xml</text>
  <text x="490" y="300" class="text">• 姿势矫正视图</text>
  <text x="490" y="315" class="text">• 实时位置显示</text>
  
  <rect x="680" y="250" width="180" height="70" class="ui-module"/>
  <text x="770" y="270" text-anchor="middle" class="module-title">异常处理布局</text>
  <text x="690" y="285" class="text">view_common_exception.xml</text>
  <text x="690" y="300" class="text">• 统一异常显示</text>
  <text x="690" y="315" class="text">• 刷新重试机制</text>
  
  <rect x="880" y="250" width="180" height="70" class="ui-module"/>
  <text x="970" y="270" text-anchor="middle" class="module-title">遮盖疗法布局</text>
  <text x="890" y="285" class="text">fragment_mask_therapy.xml</text>
  <text x="890" y="300" class="text">• 进度条控制</text>
  <text x="890" y="315" class="text">• 应用网格显示</text>
  
  <rect x="1080" y="250" width="180" height="70" class="ui-module"/>
  <text x="1170" y="270" text-anchor="middle" class="module-title">训练界面布局</text>
  <text x="1090" y="285" class="text">fragment_visual_train.xml</text>
  <text x="1090" y="300" class="text">• 训练游戏界面</text>
  <text x="1090" y="315" class="text">• 实时数据显示</text>
  
  <rect x="1280" y="250" width="180" height="70" class="ui-module"/>
  <text x="1370" y="270" text-anchor="middle" class="module-title">菜单弹窗布局</text>
  <text x="1290" y="285" class="text">layout_read_home_menu.xml</text>
  <text x="1290" y="300" class="text">• PopupWindow实现</text>
  <text x="1290" y="315" class="text">• 功能快捷入口</text>

  <!-- 自定义View层 -->
  <rect x="50" y="360" width="1500" height="140" class="business-layer"/>
  <text x="800" y="385" text-anchor="middle" class="layer-title">自定义View层 (Custom Views)</text>
  
  <rect x="80" y="410" width="200" height="70" class="business-module"/>
  <text x="180" y="430" text-anchor="middle" class="module-title">PostureCalibrationView</text>
  <text x="90" y="445" class="text">• 姿势校准核心组件</text>
  <text x="90" y="460" class="text">• 实时位置计算</text>
  <text x="90" y="475" class="text">• 角度倾斜检测</text>
  
  <rect x="300" y="410" width="200" height="70" class="business-module"/>
  <text x="400" y="430" text-anchor="middle" class="module-title">CommonExceptionView</text>
  <text x="310" y="445" class="text">• 统一异常处理</text>
  <text x="310" y="460" class="text">• 可配置异常信息</text>
  <text x="310" y="475" class="text">• 刷新回调机制</text>
  
  <rect x="520" y="410" width="200" height="70" class="business-module"/>
  <text x="620" y="430" text-anchor="middle" class="module-title">TreatmentModuleAdapter</text>
  <text x="530" y="445" class="text">• 治疗模块适配器</text>
  <text x="530" y="460" class="text">• 动态Fragment管理</text>
  <text x="530" y="475" class="text">• 响应式布局支持</text>
  
  <rect x="740" y="410" width="200" height="70" class="business-module"/>
  <text x="840" y="430" text-anchor="middle" class="module-title">TrainCenterWebView</text>
  <text x="750" y="445" class="text">• 训练中心WebView</text>
  <text x="750" y="460" class="text">• JS交互桥接</text>
  <text x="750" y="475" class="text">• 进度状态管理</text>
  
  <rect x="960" y="410" width="200" height="70" class="business-module"/>
  <text x="1060" y="430" text-anchor="middle" class="module-title">MenuPopupWindow</text>
  <text x="970" y="445" class="text">• 菜单弹窗组件</text>
  <text x="970" y="460" class="text">• 版本信息显示</text>
  <text x="970" y="475" class="text">• 功能快捷访问</text>
  
  <rect x="1180" y="410" width="200" height="70" class="business-module"/>
  <text x="1280" y="430" text-anchor="middle" class="module-title">HospitalModuleAdapter</text>
  <text x="1190" y="445" class="text">• 医院版模块适配器</text>
  <text x="1190" y="460" class="text">• 多语言支持</text>
  <text x="1190" y="475" class="text">• 图片加载优化</text>

  <!-- Activity层 -->
  <rect x="50" y="520" width="1500" height="160" class="business-layer"/>
  <text x="800" y="545" text-anchor="middle" class="layer-title">Activity层 (Activities & Fragments)</text>
  
  <rect x="80" y="570" width="180" height="90" class="business-module"/>
  <text x="170" y="590" text-anchor="middle" class="module-title">HomeMainActivity</text>
  <text x="90" y="605" class="text">• 主Activity容器</text>
  <text x="90" y="620" class="text">• Fragment管理</text>
  <text x="90" y="635" class="text">• MQTT连接管理</text>
  <text x="90" y="650" class="text">• 用户绑定处理</text>
  
  <rect x="280" y="570" width="180" height="90" class="business-module"/>
  <text x="370" y="590" text-anchor="middle" class="module-title">HomeMainFragment</text>
  <text x="290" y="605" class="text">• 主界面Fragment</text>
  <text x="290" y="620" class="text">• 治疗模块显示</text>
  <text x="290" y="635" class="text">• 用户状态管理</text>
  <text x="290" y="650" class="text">• 设备信息显示</text>
  
  <rect x="480" y="570" width="180" height="90" class="business-module"/>
  <text x="570" y="590" text-anchor="middle" class="module-title">MaskTherapyFragment</text>
  <text x="490" y="605" class="text">• 遮盖疗法界面</text>
  <text x="490" y="620" class="text">• 时间进度控制</text>
  <text x="490" y="635" class="text">• 应用选择管理</text>
  <text x="490" y="650" class="text">• 性别差异化UI</text>
  
  <rect x="680" y="570" width="180" height="90" class="business-module"/>
  <text x="770" y="590" text-anchor="middle" class="module-title">CalibrationActivity</text>
  <text x="690" y="605" class="text">• 校准Activity</text>
  <text x="690" y="620" class="text">• 姿势校准管理</text>
  <text x="690" y="635" class="text">• 眼动校准流程</text>
  <text x="690" y="650" class="text">• 校准结果处理</text>
  
  <rect x="880" y="570" width="180" height="90" class="business-module"/>
  <text x="970" y="590" text-anchor="middle" class="module-title">TrainCenterActivity</text>
  <text x="890" y="605" class="text">• 训练中心Activity</text>
  <text x="890" y="620" class="text">• WebView集成</text>
  <text x="890" y="635" class="text">• 训练数据收集</text>
  <text x="890" y="650" class="text">• 进度跟踪</text>
  
  <rect x="1080" y="570" width="180" height="90" class="business-module"/>
  <text x="1170" y="590" text-anchor="middle" class="module-title">HospitalMainActivity</text>
  <text x="1090" y="605" class="text">• 医院版主Activity</text>
  <text x="1090" y="620" class="text">• 检查中心集成</text>
  <text x="1090" y="635" class="text">• 训练中心管理</text>
  <text x="1090" y="650" class="text">• 遮盖疗法控制</text>
  
  <rect x="1280" y="570" width="180" height="90" class="business-module"/>
  <text x="1370" y="590" text-anchor="middle" class="module-title">DeviceExceptionActivity</text>
  <text x="1290" y="605" class="text">• 设备异常处理</text>
  <text x="1290" y="620" class="text">• 异常信息显示</text>
  <text x="1290" y="635" class="text">• 设备状态检测</text>
  <text x="1290" y="650" class="text">• 恢复机制</text>

  <!-- 架构特点说明 -->
  <rect x="50" y="700" width="1500" height="120" class="data-layer"/>
  <text x="800" y="725" text-anchor="middle" class="layer-title">架构特点与设计模式</text>
  
  <text x="80" y="750" class="text">• MVVM架构模式：采用ViewModel进行数据绑定，实现UI与业务逻辑分离</text>
  <text x="80" y="770" class="text">• 模块化设计：治疗模块通过Adapter动态加载，支持灵活配置和扩展</text>
  <text x="80" y="790" class="text">• 自定义View组件：封装复杂UI逻辑，提高代码复用性和维护性</text>
  <text x="80" y="810" class="text">• Fragment容器模式：使用FrameLayout作为Fragment容器，支持动态切换和管理</text>
  
  <!-- 数据流向箭头 -->
  <path class="arrow" d="M 800 180 L 800 200"/>
  <path class="arrow" d="M 800 340 L 800 360"/>
  <path class="arrow" d="M 800 500 L 800 520"/>
  <path class="arrow" d="M 800 680 L 800 700"/>
  
  <!-- 流程说明 -->
  <text x="820" y="190" class="small-text">需求驱动设计</text>
  <text x="820" y="350" class="small-text">布局实现</text>
  <text x="820" y="510" class="small-text">组件封装</text>
  <text x="820" y="690" class="small-text">业务实现</text>

  <!-- 技术实现细节层 -->
  <rect x="50" y="840" width="1500" height="180" class="native-layer"/>
  <text x="800" y="865" text-anchor="middle" class="layer-title">技术实现细节 (Technical Implementation)</text>

  <!-- 数据绑定与状态管理 -->
  <rect x="80" y="890" width="220" height="100" class="native-module"/>
  <text x="190" y="910" text-anchor="middle" class="module-title">数据绑定与状态管理</text>
  <text x="90" y="925" class="text">• LiveData观察者模式</text>
  <text x="90" y="940" class="text">• ViewModel生命周期管理</text>
  <text x="90" y="955" class="text">• Repository数据仓库</text>
  <text x="90" y="970" class="text">• 用户状态实时更新</text>
  <text x="90" y="985" class="text">• 设备状态监控</text>

  <!-- 自定义View实现技术 -->
  <rect x="320" y="890" width="220" height="100" class="native-module"/>
  <text x="430" y="910" text-anchor="middle" class="module-title">自定义View实现技术</text>
  <text x="330" y="925" class="text">• ConstraintSet动态布局</text>
  <text x="330" y="940" class="text">• Canvas绘制与动画</text>
  <text x="330" y="955" class="text">• 触摸事件处理</text>
  <text x="330" y="970" class="text">• 属性动画系统</text>
  <text x="330" y="985" class="text">• 自定义属性支持</text>

  <!-- Fragment管理策略 -->
  <rect x="560" y="890" width="220" height="100" class="native-module"/>
  <text x="670" y="910" text-anchor="middle" class="module-title">Fragment管理策略</text>
  <text x="570" y="925" class="text">• FragmentTransaction管理</text>
  <text x="570" y="940" class="text">• 动态Fragment替换</text>
  <text x="570" y="955" class="text">• 生命周期同步</text>
  <text x="570" y="970" class="text">• 状态保存与恢复</text>
  <text x="570" y="985" class="text">• 内存泄漏防护</text>

  <!-- 性能优化技术 -->
  <rect x="800" y="890" width="220" height="100" class="native-module"/>
  <text x="910" y="910" text-anchor="middle" class="module-title">性能优化技术</text>
  <text x="810" y="925" class="text">• RecyclerView优化</text>
  <text x="810" y="940" class="text">• 图片加载缓存</text>
  <text x="810" y="955" class="text">• 内存管理策略</text>
  <text x="810" y="970" class="text">• 异步任务处理</text>
  <text x="810" y="985" class="text">• UI线程优化</text>

  <!-- 事件处理机制 -->
  <rect x="1040" y="890" width="220" height="100" class="native-module"/>
  <text x="1150" y="910" text-anchor="middle" class="module-title">事件处理机制</text>
  <text x="1050" y="925" class="text">• 单击防抖处理</text>
  <text x="1050" y="940" class="text">• 回调接口设计</text>
  <text x="1050" y="955" class="text">• 事件总线机制</text>
  <text x="1050" y="970" class="text">• 生命周期感知</text>
  <text x="1050" y="985" class="text">• 异常处理策略</text>

  <!-- 多语言与适配 -->
  <rect x="1280" y="890" width="220" height="100" class="native-module"/>
  <text x="1390" y="910" text-anchor="middle" class="module-title">多语言与适配</text>
  <text x="1290" y="925" class="text">• 国际化资源管理</text>
  <text x="1290" y="940" class="text">• 屏幕密度适配</text>
  <text x="1290" y="955" class="text">• 字体大小动态调整</text>
  <text x="1290" y="970" class="text">• 布局方向支持</text>
  <text x="1290" y="985" class="text">• 设备兼容性处理</text>

  <!-- 开发流程总结 -->
  <rect x="50" y="1040" width="1500" height="120" class="data-layer"/>
  <text x="800" y="1065" text-anchor="middle" class="layer-title">医疗家庭版开发流程总结</text>

  <text x="80" y="1090" class="text">1. 需求分析：明确功能需求、用户体验需求、性能需求、数据需求、设备需求和医疗需求</text>
  <text x="80" y="1110" class="text">2. XML布局设计：创建主界面、治疗模块、校准界面、异常处理等布局文件，采用ConstraintLayout和FrameLayout</text>
  <text x="80" y="1130" class="text">3. 自定义View开发：实现PostureCalibrationView、CommonExceptionView、各种Adapter等核心组件</text>
  <text x="80" y="1150" class="text">4. Activity集成：通过HomeMainActivity、Fragment管理、ViewModel数据绑定完成业务逻辑实现</text>

</svg>
