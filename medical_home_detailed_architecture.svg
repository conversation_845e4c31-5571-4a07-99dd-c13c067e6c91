<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .flow-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 12px; fill: #2c3e50; }
      
      .ui-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .ui-module { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .data-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .data-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .service-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .service-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .view-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .view-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .api-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .api-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .native-layer { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 10; }
      .native-module { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">医疗家庭版眼动追踪系统架构图</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="70" width="450" height="200" class="ui-layer"/>
  <text x="275" y="95" text-anchor="middle" class="layer-title">用户界面层</text>
  
  <!-- 主界面模块 -->
  <rect x="70" y="110" width="120" height="70" class="ui-module"/>
  <text x="130" y="130" text-anchor="middle" class="module-title">HomeMainFragment</text>
  <text x="80" y="145" class="text">医疗家庭版主界面</text>
  <text x="80" y="160" class="text">疗程模块显示</text>
  <text x="80" y="175" class="text">用户绑定状态</text>
  
  <!-- 遮盖疗法界面 -->
  <rect x="200" y="110" width="120" height="70" class="ui-module"/>
  <text x="260" y="130" text-anchor="middle" class="module-title">MaskTherapyActivity</text>
  <text x="210" y="145" class="text">数字遮盖疗法</text>
  <text x="210" y="160" class="text">治疗参数设置</text>
  <text x="210" y="175" class="text">治疗进度显示</text>
  
  <!-- 视觉训练界面 -->
  <rect x="330" y="110" width="120" height="70" class="ui-module"/>
  <text x="390" y="130" text-anchor="middle" class="module-title">VisualTrainFragment</text>
  <text x="340" y="145" class="text">视觉训练疗法</text>
  <text x="340" y="160" class="text">训练类别选择</text>
  <text x="340" y="175" class="text">训练结果展示</text>
  
  <!-- 眼动评估界面 -->
  <rect x="70" y="190" width="120" height="70" class="ui-module"/>
  <text x="130" y="210" text-anchor="middle" class="module-title">EyeMovementActivity</text>
  <text x="80" y="225" class="text">注视稳定性检测</text>
  <text x="80" y="240" class="text">扫视能力评估</text>
  <text x="80" y="255" class="text">追随能力检测</text>
  
  <!-- 校准界面 -->
  <rect x="200" y="190" width="120" height="70" class="ui-module"/>
  <text x="260" y="210" text-anchor="middle" class="module-title">CalibrationActivity</text>
  <text x="210" y="225" class="text">姿势校准</text>
  <text x="210" y="240" class="text">视标校准</text>
  <text x="210" y="255" class="text">校准参数验证</text>
  
  <!-- 设置界面 -->
  <rect x="330" y="190" width="120" height="70" class="ui-module"/>
  <text x="390" y="210" text-anchor="middle" class="module-title">SettingsFragment</text>
  <text x="340" y="225" class="text">参数设置</text>
  <text x="340" y="240" class="text">帮助中心</text>
  <text x="340" y="255" class="text">版本更新</text>

  <!-- 数据模型层 -->
  <rect x="550" y="70" width="450" height="200" class="data-layer"/>
  <text x="775" y="95" text-anchor="middle" class="layer-title">数据模型层</text>
  
  <!-- 治疗数据 -->
  <rect x="570" y="110" width="120" height="70" class="data-module"/>
  <text x="630" y="130" text-anchor="middle" class="module-title">TreatmentData</text>
  <text x="580" y="145" class="text">疗程信息</text>
  <text x="580" y="160" class="text">治疗参数</text>
  <text x="580" y="175" class="text">治疗进度</text>
  
  <!-- 眼动轨迹数据 -->
  <rect x="700" y="110" width="120" height="70" class="data-module"/>
  <text x="760" y="130" text-anchor="middle" class="module-title">GazeTrajectory</text>
  <text x="710" y="145" class="text">眼动轨迹数据</text>
  <text x="710" y="160" class="text">注视点坐标</text>
  <text x="710" y="175" class="text">时间戳信息</text>
  
  <!-- 检测结果 -->
  <rect x="830" y="110" width="120" height="70" class="data-module"/>
  <text x="890" y="130" text-anchor="middle" class="module-title">DetectionResult</text>
  <text x="840" y="145" class="text">检测结果数据</text>
  <text x="840" y="160" class="text">评估分析</text>
  <text x="840" y="175" class="text">图像数据</text>
  
  <!-- 用户信息 -->
  <rect x="570" y="190" width="120" height="70" class="data-module"/>
  <text x="630" y="210" text-anchor="middle" class="module-title">UserProfile</text>
  <text x="580" y="225" class="text">用户账户信息</text>
  <text x="580" y="240" class="text">设备绑定状态</text>
  <text x="580" y="255" class="text">权限管理</text>
  
  <!-- 配置信息 -->
  <rect x="700" y="190" width="120" height="70" class="data-module"/>
  <text x="760" y="210" text-anchor="middle" class="module-title">ConfigData</text>
  <text x="710" y="225" class="text">系统配置</text>
  <text x="710" y="240" class="text">校准参数</text>
  <text x="710" y="255" class="text">网络配置</text>
  
  <!-- 患者信息 -->
  <rect x="830" y="190" width="120" height="70" class="data-module"/>
  <text x="890" y="210" text-anchor="middle" class="module-title">PatientInfo</text>
  <text x="840" y="225" class="text">患者基本信息</text>
  <text x="840" y="240" class="text">病历数据</text>
  <text x="840" y="255" class="text">检查记录</text>

  <!-- 服务层 -->
  <rect x="1050" y="70" width="450" height="200" class="service-layer"/>
  <text x="1275" y="95" text-anchor="middle" class="layer-title">服务层</text>
  
  <!-- 眼动追踪服务 -->
  <rect x="1070" y="110" width="120" height="70" class="service-module"/>
  <text x="1130" y="130" text-anchor="middle" class="module-title">GazeTrackService</text>
  <text x="1080" y="145" class="text">眼动追踪服务</text>
  <text x="1080" y="160" class="text">实时数据处理</text>
  <text x="1080" y="175" class="text">状态管理</text>
  
  <!-- 应用管理器 -->
  <rect x="1200" y="110" width="120" height="70" class="service-module"/>
  <text x="1260" y="130" text-anchor="middle" class="module-title">AppliedManager</text>
  <text x="1210" y="145" class="text">应用模式管理</text>
  <text x="1210" y="160" class="text">数据收集</text>
  <text x="1210" y="175" class="text">结果分析</text>
  
  <!-- 追踪管理器 -->
  <rect x="1330" y="110" width="120" height="70" class="service-module"/>
  <text x="1390" y="130" text-anchor="middle" class="module-title">TrackingManager</text>
  <text x="1340" y="145" class="text">追踪状态控制</text>
  <text x="1340" y="160" class="text">图像处理调度</text>
  <text x="1340" y="175" class="text">校准管理</text>
  
  <!-- 治疗管理器 -->
  <rect x="1070" y="190" width="120" height="70" class="service-module"/>
  <text x="1130" y="210" text-anchor="middle" class="module-title">TreatmentManager</text>
  <text x="1080" y="225" class="text">疗程状态管理</text>
  <text x="1080" y="240" class="text">治疗参数控制</text>
  <text x="1080" y="255" class="text">进度跟踪</text>
  
  <!-- 用户管理器 -->
  <rect x="1200" y="190" width="120" height="70" class="service-module"/>
  <text x="1260" y="210" text-anchor="middle" class="module-title">UserManager</text>
  <text x="1210" y="225" class="text">用户状态管理</text>
  <text x="1210" y="240" class="text">账户信息</text>
  <text x="1210" y="255" class="text">权限控制</text>
  
  <!-- 设备管理器 -->
  <rect x="1330" y="190" width="120" height="70" class="service-module"/>
  <text x="1390" y="210" text-anchor="middle" class="module-title">DeviceManager</text>
  <text x="1340" y="225" class="text">设备状态监控</text>
  <text x="1340" y="240" class="text">硬件控制</text>
  <text x="1340" y="255" class="text">性能优化</text>

  <!-- 视图组件层 -->
  <rect x="50" y="290" width="450" height="150" class="view-layer"/>
  <text x="275" y="315" text-anchor="middle" class="layer-title">视图组件层</text>

  <!-- 眼动追踪视图 -->
  <rect x="70" y="330" width="130" height="50" class="view-module"/>
  <text x="135" y="350" text-anchor="middle" class="module-title">GazeTrackView</text>
  <text x="80" y="365" class="text">实时眼动显示</text>
  <text x="80" y="375" class="text">视点绘制</text>

  <!-- 校准视图 -->
  <rect x="210" y="330" width="130" height="50" class="view-module"/>
  <text x="275" y="350" text-anchor="middle" class="module-title">CalibrationView</text>
  <text x="220" y="365" class="text">校准界面显示</text>
  <text x="220" y="375" class="text">视标绘制</text>

  <!-- 媒体播放器 -->
  <rect x="350" y="330" width="130" height="50" class="view-module"/>
  <text x="415" y="350" text-anchor="middle" class="module-title">MediaPlayer</text>
  <text x="360" y="365" class="text">音频播放</text>
  <text x="360" y="375" class="text">视频播放</text>

  <!-- 绘制模式 -->
  <rect x="140" y="390" width="200" height="40" class="view-module"/>
  <text x="240" y="410" text-anchor="middle" class="module-title">绘制模式与动画控制</text>
  <text x="150" y="425" class="text">遮盖效果绘制、训练动画、UI交互反馈</text>

  <!-- API层 -->
  <rect x="550" y="290" width="450" height="150" class="api-layer"/>
  <text x="775" y="315" text-anchor="middle" class="layer-title">API层</text>

  <!-- 家庭版API -->
  <rect x="570" y="330" width="130" height="50" class="api-module"/>
  <text x="635" y="350" text-anchor="middle" class="module-title">HomeApiService</text>
  <text x="580" y="365" class="text">家庭版配置API</text>
  <text x="580" y="375" class="text">疗程管理API</text>

  <!-- 数据仓库 -->
  <rect x="710" y="330" width="130" height="50" class="api-module"/>
  <text x="775" y="350" text-anchor="middle" class="module-title">Repository层</text>
  <text x="720" y="365" class="text">数据访问封装</text>
  <text x="720" y="375" class="text">网络请求管理</text>

  <!-- 运动评估API -->
  <rect x="850" y="330" width="130" height="50" class="api-module"/>
  <text x="915" y="350" text-anchor="middle" class="module-title">MovementApiService</text>
  <text x="860" y="365" class="text">眼动评估API</text>
  <text x="860" y="375" class="text">结果上传API</text>

  <!-- 网络客户端 -->
  <rect x="640" y="390" width="200" height="40" class="api-module"/>
  <text x="740" y="410" text-anchor="middle" class="module-title">网络客户端与数据同步</text>
  <text x="650" y="425" class="text">Retrofit客户端、MQTT通信、数据缓存</text>

  <!-- Native层 -->
  <rect x="1050" y="290" width="450" height="150" class="native-layer"/>
  <text x="1275" y="315" text-anchor="middle" class="layer-title">Native层</text>

  <!-- 眼动算法 -->
  <rect x="1070" y="330" width="130" height="50" class="native-module"/>
  <text x="1135" y="350" text-anchor="middle" class="module-title">GazeTracking</text>
  <text x="1080" y="365" class="text">nativeGazeTracking</text>
  <text x="1080" y="375" class="text">实时眼动计算</text>

  <!-- 校准算法 -->
  <rect x="1210" y="330" width="130" height="50" class="native-module"/>
  <text x="1275" y="350" text-anchor="middle" class="module-title">Calibration</text>
  <text x="1220" y="365" class="text">nativeCalibration</text>
  <text x="1220" y="375" class="text">校准参数计算</text>

  <!-- 图像处理 -->
  <rect x="1350" y="330" width="130" height="50" class="native-module"/>
  <text x="1415" y="350" text-anchor="middle" class="module-title">ImageProcessing</text>
  <text x="1360" y="365" class="text">人脸检测</text>
  <text x="1360" y="375" class="text">特征提取</text>

  <!-- C++核心算法 -->
  <rect x="1140" y="390" width="200" height="40" class="native-module"/>
  <text x="1240" y="410" text-anchor="middle" class="module-title">C++核心算法引擎</text>
  <text x="1150" y="425" class="text">OpenCV处理、AI模型推理、性能优化</text>

  <!-- 连接箭头 -->
  <!-- UI层到数据层 -->
  <line x1="275" y1="270" x2="775" y2="270" class="arrow"/>
  <!-- UI层到服务层 -->
  <line x1="450" y1="170" x2="1050" y2="170" class="arrow"/>
  <!-- 数据层到API层 -->
  <line x1="775" y1="270" x2="775" y2="290" class="arrow"/>
  <!-- 服务层到Native层 -->
  <line x1="1275" y1="270" x2="1275" y2="290" class="arrow"/>
  <!-- 视图组件到UI层 -->
  <line x1="275" y1="290" x2="275" y2="270" class="arrow"/>

  <!-- 数据流箭头 -->
  <line x1="635" y1="380" x2="1135" y2="380" class="data-arrow"/>
  <line x1="775" y1="380" x2="775" y2="330" class="data-arrow"/>
  <line x1="1275" y1="380" x2="1275" y2="330" class="data-arrow"/>

  <!-- 医疗家庭版眼动评估实现流程 -->
  <rect x="50" y="460" width="1450" height="700" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="775" y="485" text-anchor="middle" class="title" style="font-size: 22px;">医疗家庭版眼动评估实现流程</text>

  <!-- 流程步骤 -->
  <text x="70" y="520" class="flow-text" style="font-weight: bold;">1. 初始化阶段：</text>
  <text x="90" y="540" class="flow-text">• 用户在HomeMainFragment中查看分配的疗程信息</text>
  <text x="90" y="555" class="flow-text">• 进行设备绑定检查和账户信息同步</text>
  <text x="90" y="570" class="flow-text">• 准备开始眼动追踪测试</text>

  <text x="70" y="600" class="flow-text" style="font-weight: bold;">2. 测试阶段：</text>
  <text x="90" y="620" class="flow-text">• EyeMovementActivity提供多种检测选项</text>
  <text x="90" y="635" class="flow-text">• 发送MSG_TURN_ON_CAMERA、MSG_START_TRACK、MSG_START_APPLIED消息启动眼动追踪</text>
  <text x="90" y="650" class="flow-text">• Native层每帧调用nativeGazeTracking进行实时眼动数据采集</text>
  <text x="90" y="665" class="flow-text">• 用户测试过程中生成眼动轨迹数据(x,y坐标)</text>

  <text x="70" y="695" class="flow-text" style="font-weight: bold;">3. 结果阶段：</text>
  <text x="90" y="715" class="flow-text">• 用户点击完成按钮，系统收集测试数据</text>
  <text x="90" y="730" class="flow-text">• 发送MSG_GET_GAZE_TRAJECTORY获取JSON格式的GazeTrajectory数据</text>
  <text x="90" y="745" class="flow-text">• Native层调用nativeGetGazeTrajectory返回轨迹数据</text>
  <text x="90" y="760" class="flow-text">• 保存测试结果图像头像</text>

  <text x="70" y="790" class="flow-text" style="font-weight: bold;">4. 分析阶段：</text>
  <text x="90" y="810" class="flow-text">• ReadResultAnalysisActivity计算眼动速度(分/秒)</text>
  <text x="90" y="825" class="flow-text">• 根据眼动轨迹分析注视稳定性、扫视能力等指标</text>
  <text x="90" y="840" class="flow-text">• 显示详细分析、时长、速度等分析结果</text>

  <text x="70" y="870" class="flow-text" style="font-weight: bold;">5. 数据管理：</text>
  <text x="90" y="890" class="flow-text">• EyeMovementActivity提供三种检测模式：</text>
  <text x="110" y="905" class="flow-text">- 静态注视：ReadTrackView一次性绘制所有视线点和路径</text>
  <text x="110" y="920" class="flow-text">- 动态追随：逐点动画显示，每100ms显示下一个点</text>
  <text x="110" y="935" class="flow-text">- 示例视频：ExoPlayer播放标准眼动测试视频</text>

  <!-- 右侧流程 -->
  <text x="800" y="520" class="flow-text" style="font-weight: bold;">6. 疗法执行：</text>
  <text x="820" y="540" class="flow-text">• 数字遮盖疗法：MaskViewModel管理治疗参数</text>
  <text x="820" y="555" class="flow-text">• 视觉训练疗法：VisualTrainFragment执行训练任务</text>
  <text x="820" y="570" class="flow-text">• 实时眼动追踪：GazeTrackService提供持续监控</text>

  <text x="800" y="600" class="flow-text" style="font-weight: bold;">7. 数据上传：</text>
  <text x="820" y="620" class="flow-text">• 图像上传：先上传检测结果图像获取URL</text>
  <text x="820" y="635" class="flow-text">• 数据提交：调用相应API提交检测结果</text>
  <text x="820" y="650" class="flow-text">• 错误处理：网络异常时显示统一错误提示</text>

  <text x="800" y="680" class="flow-text" style="font-weight: bold;">8. 校准管理：</text>
  <text x="820" y="700" class="flow-text">• CalibrationActivity支持姿势校准和视标校准</text>
  <text x="820" y="715" class="flow-text">• 校准参数存储在本地配置文件中</text>
  <text x="820" y="730" class="flow-text">• 校准失败时提供重新校准选项</text>

  <text x="800" y="760" class="flow-text" style="font-weight: bold;">9. 系统监控：</text>
  <text x="820" y="780" class="flow-text">• DeviceManager监控设备状态和性能</text>
  <text x="820" y="795" class="flow-text">• MQTT通信实现远程设备管理</text>
  <text x="820" y="810" class="flow-text">• 日志系统记录所有操作和错误信息</text>

  <text x="800" y="840" class="flow-text" style="font-weight: bold;">10. 用户体验：</text>
  <text x="820" y="860" class="flow-text">• 成功操作显示Toast成功消息</text>
  <text x="820" y="875" class="flow-text">• 失败操作显示详细错误信息</text>
  <text x="820" y="890" class="flow-text">• 支持离线模式和数据同步</text>

  <!-- 核心特性说明 -->
  <rect x="70" y="960" width="1400" height="180" style="fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8;"/>
  <text x="90" y="985" class="flow-text" style="font-weight: bold; font-size: 16px;">系统核心特性：</text>

  <text x="90" y="1010" class="flow-text">• <tspan style="font-weight: bold;">实时眼动追踪：</tspan>基于C++算法引擎，支持30fps实时眼动数据采集，精确度达到亚像素级别</text>
  <text x="90" y="1030" class="flow-text">• <tspan style="font-weight: bold;">多疗法集成：</tspan>数字遮盖疗法、视觉训练疗法，支持个性化参数配置和治疗进度跟踪</text>
  <text x="90" y="1050" class="flow-text">• <tspan style="font-weight: bold;">智能校准系统：</tspan>双重校准机制(姿势+视标)，自动检测校准质量，确保追踪精度</text>
  <text x="90" y="1070" class="flow-text">• <tspan style="font-weight: bold;">云端数据管理：</tspan>支持检测结果、治疗数据的云端存储，便于医生远程监控和分析</text>
  <text x="90" y="1090" class="flow-text">• <tspan style="font-weight: bold;">物联网集成：</tspan>MQTT协议实现设备远程管理，支持状态监控和参数下发</text>
  <text x="90" y="1110" class="flow-text">• <tspan style="font-weight: bold;">模块化架构：</tspan>MVVM架构模式，组件解耦，便于维护和功能扩展</text>
  <text x="90" y="1130" class="flow-text">• <tspan style="font-weight: bold;">用户友好体验：</tspan>直观的UI设计，详细的操作指导，完善的错误处理和反馈机制</text>

</svg>
