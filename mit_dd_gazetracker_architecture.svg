<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="launcherGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="homeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#44a08d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hospitalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8e6cf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#88d8a3;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="readGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd93d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcd3c;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="movementGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8c8ec;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7d8dc1;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="coreGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    MIT DD GazeTracker 应用架构图
  </text>
  
  <!-- 启动层 -->
  <g id="launcher-layer">
    <rect x="600" y="60" width="200" height="80" rx="10" fill="url(#launcherGrad)" stroke="#c0392b" stroke-width="2"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">启动层</text>
    <text x="700" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">LauncherActivity</text>
    <text x="700" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">activity_launcher.xml</text>
  </g>
  
  <!-- 家庭医疗模块 -->
  <g id="home-module">
    <rect x="50" y="180" width="300" height="350" rx="15" fill="url(#homeGrad)" stroke="#27ae60" stroke-width="2"/>
    <text x="200" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">家庭医疗模块</text>
    
    <!-- HomeMainActivity -->
    <rect x="70" y="220" width="260" height="40" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="200" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">HomeMainActivity</text>
    <text x="200" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">activity_home_main.xml</text>
    
    <!-- 遮盖治疗 -->
    <rect x="70" y="280" width="120" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#3498db" stroke-width="1"/>
    <text x="130" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">遮盖治疗</text>
    <text x="130" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">MaskTherapy</text>
    <text x="130" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">SelectCommonApp</text>
    <text x="130" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ParamSetting</text>
    
    <!-- 视觉训练 -->
    <rect x="210" y="280" width="120" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#9b59b6" stroke-width="1"/>
    <text x="270" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">视觉训练</text>
    <text x="270" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">VisualTrainFragment</text>
    <text x="270" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">SelectTrainActivity</text>
    <text x="270" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">AITrainGuide</text>
    
    <!-- 治疗管理 -->
    <rect x="70" y="380" width="260" height="40" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e74c3c" stroke-width="1"/>
    <text x="200" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">治疗管理</text>
    <text x="200" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">TreatmentManagementActivity</text>
    
    <!-- Fragment说明 -->
    <rect x="70" y="440" width="260" height="60" rx="5" fill="rgba(255,255,255,0.7)" stroke="#95a5a6" stroke-width="1"/>
    <text x="200" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">主要Fragment</text>
    <text x="200" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">HomeMainFragment</text>
    <text x="200" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">fragment_home_main.xml</text>
  </g>
  
  <!-- 医院医疗模块 -->
  <g id="hospital-module">
    <rect x="380" y="180" width="280" height="350" rx="15" fill="url(#hospitalGrad)" stroke="#27ae60" stroke-width="2"/>
    <text x="520" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">医院医疗模块</text>
    
    <!-- HospitalMainActivity -->
    <rect x="400" y="220" width="240" height="40" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="520" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">HospitalMainActivity</text>
    <text x="520" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">activity_hospital_main.xml</text>
    
    <!-- 检查中心 -->
    <rect x="400" y="280" width="110" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#3498db" stroke-width="1"/>
    <text x="455" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">检查中心</text>
    <text x="455" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">InspectionCenter</text>
    <text x="455" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Activity</text>
    
    <!-- 医院遮盖治疗 -->
    <rect x="530" y="280" width="110" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#9b59b6" stroke-width="1"/>
    <text x="585" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">医院遮盖治疗</text>
    <text x="585" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">MHospitalMT</text>
    <text x="585" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Activity</text>
    
    <!-- 打印配置 -->
    <rect x="400" y="360" width="240" height="40" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e74c3c" stroke-width="1"/>
    <text x="520" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">打印配置</text>
    <text x="520" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">PrinterConfigurationActivity</text>
    
    <!-- Fragment说明 -->
    <rect x="400" y="420" width="240" height="80" rx="5" fill="rgba(255,255,255,0.7)" stroke="#95a5a6" stroke-width="1"/>
    <text x="520" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">主要Fragment</text>
    <text x="520" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">HospitalMainFragment</text>
    <text x="520" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">HospitalInitFragment</text>
    <text x="520" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">MHospitalMTFragment</text>
  </g>

  <!-- 阅读评估模块 -->
  <g id="read-module">
    <rect x="690" y="180" width="280" height="350" rx="15" fill="url(#readGrad)" stroke="#f39c12" stroke-width="2"/>
    <text x="830" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">阅读评估模块</text>

    <!-- ReadHomeMainActivity -->
    <rect x="710" y="220" width="240" height="40" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="830" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">ReadHomeMainActivity</text>
    <text x="830" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">activity_read_homo_main.xml</text>

    <!-- 阅读评估流程 -->
    <rect x="710" y="280" width="240" height="120" rx="5" fill="rgba(255,255,255,0.8)" stroke="#3498db" stroke-width="1"/>
    <text x="830" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">阅读评估流程</text>
    <text x="830" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadMainActivity → ReadInitActivity</text>
    <text x="830" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadActivity → ReadTrackActivity</text>
    <text x="830" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadResultAnalysisActivity</text>
    <text x="830" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadAssessmentReportActivity</text>
    <text x="830" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#e74c3c">完整的阅读评估链路</text>

    <!-- Fragment说明 -->
    <rect x="710" y="420" width="240" height="80" rx="5" fill="rgba(255,255,255,0.7)" stroke="#95a5a6" stroke-width="1"/>
    <text x="830" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">主要Fragment</text>
    <text x="830" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadHomeMainFragment</text>
    <text x="830" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadInitBasicInfoFragment</text>
    <text x="830" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ReadInitCalibrationFragment</text>
  </g>

  <!-- 眼动评估模块 -->
  <g id="movement-module">
    <rect x="1000" y="180" width="350" height="350" rx="15" fill="url(#movementGrad)" stroke="#3498db" stroke-width="2"/>
    <text x="1175" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">眼动评估模块</text>

    <!-- EyeMovementEvaluateActivity -->
    <rect x="1020" y="220" width="310" height="40" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="1175" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">EyeMovementEvaluateActivity</text>
    <text x="1175" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">activity_eye_movement_evaluate.xml</text>

    <!-- 四大评估功能 -->
    <rect x="1020" y="280" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e74c3c" stroke-width="1"/>
    <text x="1095" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">注视稳定性评估</text>
    <text x="1095" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">GazeStabilityEvaluate</text>
    <text x="1095" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Activity + Fragment</text>
    <text x="1095" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Result + Explain</text>

    <rect x="1180" y="280" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#9b59b6" stroke-width="1"/>
    <text x="1255" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">跟随能力评估</text>
    <text x="1255" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">FollowAbilityEvaluate</text>
    <text x="1255" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Activity + Fragment</text>
    <text x="1255" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Result + Explain</text>

    <rect x="1020" y="380" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#27ae60" stroke-width="1"/>
    <text x="1095" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">扫视能力评估</text>
    <text x="1095" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">SaccadeAbilityEvaluate</text>
    <text x="1095" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Activity + Fragment</text>
    <text x="1095" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">Result + Explain</text>

    <rect x="1180" y="380" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#f39c12" stroke-width="1"/>
    <text x="1255" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">ROI检测</text>
    <text x="1255" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ROIDetectionActivity</text>
    <text x="1255" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ROIDetectingActivity</text>
    <text x="1255" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">ROIDetectionResult</text>

    <!-- 患者信息 -->
    <rect x="1020" y="480" width="310" height="40" rx="5" fill="rgba(255,255,255,0.7)" stroke="#95a5a6" stroke-width="1"/>
    <text x="1175" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2c3e50">患者信息管理</text>
    <text x="1175" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f8c8d">EMPatientInfoActivity + EMPatientLibraryFragment</text>
  </g>

  <!-- 核心服务层 -->
  <g id="core-services">
    <rect x="50" y="580" width="1300" height="200" rx="15" fill="url(#coreGrad)" stroke="#8e44ad" stroke-width="2"/>
    <text x="700" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">核心技术服务层</text>

    <!-- 眼动追踪核心 -->
    <rect x="80" y="630" width="200" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="180" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">眼动追踪核心</text>
    <text x="180" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">GazeTrackService</text>
    <text x="180" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">CalibrationActivity</text>
    <text x="180" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">GazeTrackingManager</text>
    <text x="180" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">所有评估的基础服务</text>

    <!-- 设备管理 -->
    <rect x="300" y="630" width="180" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="390" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">设备管理</text>
    <text x="390" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">DeviceManager</text>
    <text x="390" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">DeviceExceptionActivity</text>
    <text x="390" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">设备状态监控</text>
    <text x="390" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">硬件设备管理</text>

    <!-- 用户管理 -->
    <rect x="500" y="630" width="180" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="590" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">用户管理</text>
    <text x="590" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">BindActivity</text>
    <text x="590" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">ProtocolWebActivity</text>
    <text x="590" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">用户绑定与认证</text>
    <text x="590" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">用户身份管理</text>

    <!-- 系统功能 -->
    <rect x="700" y="630" width="180" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="790" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">系统功能</text>
    <text x="790" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">ConfigActivity</text>
    <text x="790" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">UpdateActivity</text>
    <text x="790" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">HelpCenterActivity</text>
    <text x="790" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">系统配置与帮助</text>

    <!-- AI功能 -->
    <rect x="900" y="630" width="160" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="980" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">AI功能</text>
    <text x="980" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">ChatWebActivity</text>
    <text x="980" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">AI训练指导</text>
    <text x="980" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">智能辅助</text>
    <text x="980" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">AI智能服务</text>

    <!-- 后台服务 -->
    <rect x="1080" y="630" width="160" height="120" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1"/>
    <text x="1160" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">后台服务</text>
    <text x="1160" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">DesktopService</text>
    <text x="1160" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">WebSocketService</text>
    <text x="1160" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">MQTT服务</text>
    <text x="1160" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#e74c3c">通信与桌面服务</text>
  </g>

  <!-- 连接线 -->
  <g id="connections">
    <!-- 启动层到各主模块的连接 -->
    <line x1="700" y1="140" x2="200" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="140" x2="520" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="140" x2="830" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="140" x2="1175" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>

    <!-- 各模块到核心服务的依赖连接 -->
    <line x1="200" y1="530" x2="180" y2="630" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="520" y1="530" x2="180" y2="630" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="830" y1="530" x2="180" y2="630" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="1175" y1="530" x2="180" y2="630" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>

    <!-- 设备管理依赖 -->
    <line x1="200" y1="530" x2="390" y2="630" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="520" y1="530" x2="390" y2="630" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>

    <!-- 用户管理依赖 -->
    <line x1="200" y1="530" x2="590" y2="630" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="520" y1="530" x2="590" y2="630" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
  </g>

  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>

  <!-- 统计信息面板 -->
  <g id="statistics">
    <rect x="50" y="820" width="1300" height="150" rx="15" fill="rgba(52, 73, 94, 0.1)" stroke="#34495e" stroke-width="2"/>
    <text x="700" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2c3e50">
      MIT DD GazeTracker 架构统计
    </text>

    <!-- Activity统计 -->
    <rect x="80" y="860" width="200" height="80" rx="8" fill="rgba(231, 76, 60, 0.1)" stroke="#e74c3c" stroke-width="1"/>
    <text x="180" y="880" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#e74c3c">Activity</text>
    <text x="180" y="900" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#c0392b">40+</text>
    <text x="180" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">包含启动、主页、评估、功能类</text>

    <!-- Fragment统计 -->
    <rect x="300" y="860" width="200" height="80" rx="8" fill="rgba(155, 89, 182, 0.1)" stroke="#9b59b6" stroke-width="1"/>
    <text x="400" y="880" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#9b59b6">Fragment</text>
    <text x="400" y="900" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#8e44ad">25+</text>
    <text x="400" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">主页、评估、初始化Fragment</text>

    <!-- XML布局统计 -->
    <rect x="520" y="860" width="200" height="80" rx="8" fill="rgba(52, 152, 219, 0.1)" stroke="#3498db" stroke-width="1"/>
    <text x="620" y="880" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#3498db">XML布局</text>
    <text x="620" y="900" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2980b9">90+</text>
    <text x="620" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">Activity、Fragment、Dialog布局</text>

    <!-- 业务模块统计 -->
    <rect x="740" y="860" width="200" height="80" rx="8" fill="rgba(46, 204, 113, 0.1)" stroke="#2ecc71" stroke-width="1"/>
    <text x="840" y="880" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2ecc71">业务模块</text>
    <text x="840" y="900" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#27ae60">5大</text>
    <text x="840" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">家庭、医院、阅读、眼动、核心</text>

    <!-- 核心服务统计 -->
    <rect x="960" y="860" width="200" height="80" rx="8" fill="rgba(243, 156, 18, 0.1)" stroke="#f39c12" stroke-width="1"/>
    <text x="1060" y="880" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f39c12">核心服务</text>
    <text x="1060" y="900" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#e67e22">6类</text>
    <text x="1060" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">眼动、设备、用户、系统、AI、后台</text>
  </g>

  <!-- 图例说明 -->
  <g id="legend">
    <text x="70" y="990" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2c3e50">图例说明：</text>
    <line x1="150" y1="985" x2="180" y2="985" stroke="#34495e" stroke-width="3"/>
    <text x="190" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">启动流程</text>

    <line x1="250" y1="985" x2="280" y2="985" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="290" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">服务依赖</text>

    <rect x="350" y="980" width="15" height="10" fill="url(#homeGrad)"/>
    <text x="375" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">家庭模块</text>

    <rect x="450" y="980" width="15" height="10" fill="url(#hospitalGrad)"/>
    <text x="475" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">医院模块</text>

    <rect x="550" y="980" width="15" height="10" fill="url(#readGrad)"/>
    <text x="575" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">阅读模块</text>

    <rect x="650" y="980" width="15" height="10" fill="url(#movementGrad)"/>
    <text x="675" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">眼动模块</text>

    <rect x="750" y="980" width="15" height="10" fill="url(#coreGrad)"/>
    <text x="775" y="990" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">核心服务</text>
  </g>

</svg>
