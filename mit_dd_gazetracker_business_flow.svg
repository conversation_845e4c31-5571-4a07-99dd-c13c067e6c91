<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="startGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff7675;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d63031;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="homeFlowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00b894;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a085;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hospitalFlowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0984e3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0770c4;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="readFlowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fdcb6e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e17055;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="eyeFlowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a29bfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6c5ce7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="coreFlowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e84393;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2d3436"/>
    </marker>
    
    <marker id="arrowheadBlue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0984e3"/>
    </marker>
    
    <marker id="arrowheadGreen" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#00b894"/>
    </marker>
    
    <marker id="arrowheadOrange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#fdcb6e"/>
    </marker>
    
    <marker id="arrowheadPurple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#a29bfe"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2d3436">
    MIT DD GazeTracker 业务流程关系图
  </text>
  
  <!-- 应用启动 -->
  <g id="app-start">
    <circle cx="600" cy="80" r="30" fill="url(#startGrad)" stroke="#d63031" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">应用启动</text>
    <text x="600" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#636e72">LauncherActivity</text>
  </g>
  
  <!-- 家庭使用场景 -->
  <g id="home-scenario">
    <rect x="50" y="160" width="220" height="280" rx="15" fill="url(#homeFlowGrad)" stroke="#00a085" stroke-width="2"/>
    <text x="160" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">家庭使用场景</text>
    
    <!-- 家庭主页 -->
    <rect x="70" y="200" width="180" height="30" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="160" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3436">HomeMainActivity</text>
    
    <!-- 遮盖治疗 -->
    <rect x="70" y="250" width="80" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#0984e3" stroke-width="1"/>
    <text x="110" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">遮盖治疗</text>
    <text x="110" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">MaskTherapy</text>
    <text x="110" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">参数设置</text>
    
    <!-- 视觉训练 -->
    <rect x="170" y="250" width="80" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#6c5ce7" stroke-width="1"/>
    <text x="210" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">视觉训练</text>
    <text x="210" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">VisualTrain</text>
    <text x="210" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">AI指导</text>
    
    <!-- 治疗管理 -->
    <rect x="70" y="330" width="180" height="40" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e17055" stroke-width="1"/>
    <text x="160" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">治疗管理</text>
    <text x="160" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">TreatmentManagement</text>
    
    <!-- 用户绑定 -->
    <rect x="70" y="390" width="180" height="30" rx="5" fill="rgba(255,255,255,0.7)" stroke="#636e72" stroke-width="1"/>
    <text x="160" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2d3436">用户绑定 (BindActivity)</text>
  </g>
  
  <!-- 医院使用场景 -->
  <g id="hospital-scenario">
    <rect x="300" y="160" width="220" height="280" rx="15" fill="url(#hospitalFlowGrad)" stroke="#0770c4" stroke-width="2"/>
    <text x="410" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">医院使用场景</text>
    
    <!-- 医院主页 -->
    <rect x="320" y="200" width="180" height="30" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="410" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3436">HospitalMainActivity</text>
    
    <!-- 检查中心 -->
    <rect x="320" y="250" width="80" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#00b894" stroke-width="1"/>
    <text x="360" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">检查中心</text>
    <text x="360" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">Inspection</text>
    <text x="360" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">Center</text>
    
    <!-- 医院遮盖治疗 -->
    <rect x="420" y="250" width="80" height="60" rx="5" fill="rgba(255,255,255,0.8)" stroke="#fdcb6e" stroke-width="1"/>
    <text x="460" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">医院遮盖</text>
    <text x="460" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">MHospital</text>
    <text x="460" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">MT</text>
    
    <!-- 打印配置 -->
    <rect x="320" y="330" width="80" height="40" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e17055" stroke-width="1"/>
    <text x="360" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">打印配置</text>
    <text x="360" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">Printer</text>
    
    <!-- 训练中心 -->
    <rect x="420" y="330" width="80" height="40" rx="5" fill="rgba(255,255,255,0.8)" stroke="#a29bfe" stroke-width="1"/>
    <text x="460" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">训练中心</text>
    <text x="460" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">TrainCenter</text>
    
    <!-- 用户绑定 -->
    <rect x="320" y="390" width="180" height="30" rx="5" fill="rgba(255,255,255,0.7)" stroke="#636e72" stroke-width="1"/>
    <text x="410" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2d3436">用户绑定 (BindActivity)</text>
  </g>

  <!-- 阅读评估场景 -->
  <g id="read-scenario">
    <rect x="550" y="160" width="220" height="280" rx="15" fill="url(#readFlowGrad)" stroke="#e17055" stroke-width="2"/>
    <text x="660" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">阅读评估场景</text>

    <!-- 阅读主页 -->
    <rect x="570" y="200" width="180" height="30" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="660" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3436">ReadHomeMainActivity</text>

    <!-- 阅读评估流程 -->
    <rect x="570" y="250" width="180" height="120" rx="5" fill="rgba(255,255,255,0.8)" stroke="#0984e3" stroke-width="1"/>
    <text x="660" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">完整评估流程</text>
    <text x="660" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">1. ReadInitActivity (初始化)</text>
    <text x="660" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">2. ReadActivity (阅读测试)</text>
    <text x="660" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">3. ReadTrackActivity (追踪)</text>
    <text x="660" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">4. ReadResultAnalysis (分析)</text>
    <text x="660" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">5. ReadAssessmentReport (报告)</text>

    <!-- 校准与配置 -->
    <rect x="570" y="390" width="180" height="30" rx="5" fill="rgba(255,255,255,0.7)" stroke="#636e72" stroke-width="1"/>
    <text x="660" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2d3436">校准与配置 (CalibrationActivity)</text>
  </g>

  <!-- 眼动评估场景 -->
  <g id="eye-movement-scenario">
    <rect x="800" y="160" width="350" height="280" rx="15" fill="url(#eyeFlowGrad)" stroke="#6c5ce7" stroke-width="2"/>
    <text x="975" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">眼动评估场景</text>

    <!-- 眼动评估主页 -->
    <rect x="820" y="200" width="310" height="30" rx="5" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="975" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3436">EyeMovementEvaluateActivity</text>

    <!-- 四大评估功能 -->
    <rect x="820" y="250" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#e17055" stroke-width="1"/>
    <text x="895" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">注视稳定性评估</text>
    <text x="895" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">GazeStabilityEvaluate</text>
    <text x="895" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">→ Result → Explain</text>
    <text x="895" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#e17055">测试 → 结果 → 解释</text>

    <rect x="980" y="250" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#00b894" stroke-width="1"/>
    <text x="1055" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">跟随能力评估</text>
    <text x="1055" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">FollowAbilityEvaluate</text>
    <text x="1055" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">→ Result → Explain</text>
    <text x="1055" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#e17055">测试 → 结果 → 解释</text>

    <rect x="820" y="350" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#fdcb6e" stroke-width="1"/>
    <text x="895" y="370" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">扫视能力评估</text>
    <text x="895" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">SaccadeAbilityEvaluate</text>
    <text x="895" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">→ Result → Explain</text>
    <text x="895" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#e17055">测试 → 结果 → 解释</text>

    <rect x="980" y="350" width="150" height="80" rx="5" fill="rgba(255,255,255,0.8)" stroke="#a29bfe" stroke-width="1"/>
    <text x="1055" y="370" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2d3436">ROI检测</text>
    <text x="1055" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">ROIDetectionActivity</text>
    <text x="1055" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">→ Detecting → Result</text>
    <text x="1055" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#e17055">检测 → 处理 → 结果</text>
  </g>

  <!-- 核心技术支撑 -->
  <g id="core-support">
    <rect x="50" y="480" width="1100" height="120" rx="15" fill="url(#coreFlowGrad)" stroke="#e84393" stroke-width="2"/>
    <text x="600" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">核心技术支撑层</text>

    <!-- 眼动追踪核心 -->
    <rect x="80" y="520" width="160" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="160" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">眼动追踪核心</text>
    <text x="160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">GazeTrackService</text>
    <text x="160" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">CalibrationActivity</text>

    <!-- 设备管理 -->
    <rect x="260" y="520" width="140" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="330" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">设备管理</text>
    <text x="330" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">DeviceManager</text>
    <text x="330" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">DeviceException</text>

    <!-- 用户管理 -->
    <rect x="420" y="520" width="140" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="490" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">用户管理</text>
    <text x="490" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">BindActivity</text>
    <text x="490" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">ProtocolWeb</text>

    <!-- 系统配置 -->
    <rect x="580" y="520" width="140" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="650" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">系统配置</text>
    <text x="650" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">ConfigActivity</text>
    <text x="650" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">UpdateActivity</text>

    <!-- AI功能 -->
    <rect x="740" y="520" width="140" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="810" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">AI功能</text>
    <text x="810" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">ChatWebActivity</text>
    <text x="810" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">AI训练指导</text>

    <!-- 后台服务 -->
    <rect x="900" y="520" width="140" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#2d3436" stroke-width="1"/>
    <text x="970" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">后台服务</text>
    <text x="970" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">DesktopService</text>
    <text x="970" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#636e72">WebSocketService</text>
  </g>

  <!-- 流程连接线 -->
  <g id="flow-connections">
    <!-- 启动到各场景的连接 -->
    <line x1="600" y1="110" x2="160" y2="160" stroke="#00b894" stroke-width="3" marker-end="url(#arrowheadGreen)"/>
    <line x1="600" y1="110" x2="410" y2="160" stroke="#0984e3" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
    <line x1="600" y1="110" x2="660" y2="160" stroke="#fdcb6e" stroke-width="3" marker-end="url(#arrowheadOrange)"/>
    <line x1="600" y1="110" x2="975" y2="160" stroke="#a29bfe" stroke-width="3" marker-end="url(#arrowheadPurple)"/>

    <!-- 各场景到核心支撑的依赖连接 -->
    <line x1="160" y1="440" x2="160" y2="520" stroke="#fd79a8" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="410" y1="440" x2="330" y2="520" stroke="#fd79a8" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="660" y1="440" x2="490" y2="520" stroke="#fd79a8" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="975" y1="440" x2="650" y2="520" stroke="#fd79a8" stroke-width="2" stroke-dasharray="5,5"/>

    <!-- 眼动追踪核心到各评估模块的支撑 -->
    <line x1="240" y1="550" x2="660" y2="440" stroke="#e17055" stroke-width="2" stroke-dasharray="3,3"/>
    <line x1="240" y1="550" x2="975" y2="440" stroke="#e17055" stroke-width="2" stroke-dasharray="3,3"/>

    <!-- AI功能到训练模块的支撑 -->
    <line x1="810" y1="520" x2="210" y2="440" stroke="#6c5ce7" stroke-width="2" stroke-dasharray="3,3"/>
    <line x1="810" y1="520" x2="460" y2="440" stroke="#6c5ce7" stroke-width="2" stroke-dasharray="3,3"/>
  </g>

  <!-- 业务流程说明 -->
  <g id="business-flow-description">
    <rect x="50" y="630" width="1100" height="140" rx="10" fill="rgba(45, 52, 54, 0.05)" stroke="#636e72" stroke-width="1"/>
    <text x="600" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3436">
      业务流程说明
    </text>

    <!-- 流程步骤 -->
    <g id="flow-steps">
      <text x="80" y="680" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3436">1. 应用启动</text>
      <text x="80" y="695" font-family="Arial, sans-serif" font-size="10" fill="#636e72">LauncherActivity 根据用户选择进入不同使用场景</text>

      <text x="80" y="720" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3436">2. 场景选择</text>
      <text x="80" y="735" font-family="Arial, sans-serif" font-size="10" fill="#636e72">• 家庭场景：遮盖治疗、视觉训练、治疗管理</text>
      <text x="80" y="750" font-family="Arial, sans-serif" font-size="10" fill="#636e72">• 医院场景：检查中心、医院遮盖治疗、打印配置、训练中心</text>

      <text x="600" y="680" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3436">3. 专业评估</text>
      <text x="600" y="695" font-family="Arial, sans-serif" font-size="10" fill="#636e72">阅读评估：完整的阅读能力测试与分析流程</text>
      <text x="600" y="710" font-family="Arial, sans-serif" font-size="10" fill="#636e72">眼动评估：注视稳定性、跟随能力、扫视能力、ROI检测</text>

      <text x="600" y="735" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3436">4. 技术支撑</text>
      <text x="600" y="750" font-family="Arial, sans-serif" font-size="10" fill="#636e72">核心服务层提供眼动追踪、设备管理、用户管理、AI功能等技术支撑</text>
    </g>
  </g>

  <!-- 图例说明 -->
  <g id="legend">
    <text x="70" y="790" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3436">图例：</text>

    <line x1="120" y1="785" x2="150" y2="785" stroke="#00b894" stroke-width="3" marker-end="url(#arrowheadGreen)"/>
    <text x="160" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">启动流程</text>

    <line x1="220" y1="785" x2="250" y2="785" stroke="#fd79a8" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="260" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">依赖关系</text>

    <line x1="320" y1="785" x2="350" y2="785" stroke="#e17055" stroke-width="2" stroke-dasharray="3,3"/>
    <text x="360" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">技术支撑</text>

    <rect x="420" y="780" width="15" height="10" fill="url(#homeFlowGrad)"/>
    <text x="445" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">家庭场景</text>

    <rect x="510" y="780" width="15" height="10" fill="url(#hospitalFlowGrad)"/>
    <text x="535" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">医院场景</text>

    <rect x="600" y="780" width="15" height="10" fill="url(#readFlowGrad)"/>
    <text x="625" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">阅读评估</text>

    <rect x="690" y="780" width="15" height="10" fill="url(#eyeFlowGrad)"/>
    <text x="715" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">眼动评估</text>

    <rect x="780" y="780" width="15" height="10" fill="url(#coreFlowGrad)"/>
    <text x="805" y="790" font-family="Arial, sans-serif" font-size="9" fill="#636e72">核心支撑</text>
  </g>

</svg>
