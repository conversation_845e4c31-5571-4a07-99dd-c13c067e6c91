# 新增模块开发示例 - 语音控制模块

## 项目架构概述

基于分析，您的项目采用**分层架构模式**：

```
UI层 (Presentation) → 业务逻辑层 (Business) → 数据层 (Data) → 原生层 (Native)
```

## 开发步骤详解

### 1. UI层开发

#### 1.1 创建Activity
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/VoiceControlActivity.kt
class VoiceControlActivity : GTBaseActivity() {
    
    private val viewModel by lazy { VoiceControlViewModel() }
    private val voiceWaveView by id<VoiceWaveView>(R.id.voice_wave_view)
    private val tvVoiceStatus by id<TextView>(R.id.tv_voice_status)
    private val btnVoiceToggle by id<Button>(R.id.btn_voice_toggle)
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_voice_control)
        
        initViews()
        observeViewModel()
    }
    
    private fun initViews() {
        btnVoiceToggle.setOnSingleClickListener {
            viewModel.toggleVoiceRecognition()
        }
    }
    
    private fun observeViewModel() {
        viewModel.voiceStatus.observe(this) { status ->
            updateVoiceStatus(status)
        }
        
        viewModel.recognitionResult.observe(this) { result ->
            handleVoiceCommand(result)
        }
    }
}
```

#### 1.2 创建自定义View
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/widget/VoiceWaveView.kt
class VoiceWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var amplitude = 0f
    private var isRecording = false
    
    fun updateAmplitude(amplitude: Float) {
        this.amplitude = amplitude
        invalidate()
    }
    
    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        // 绘制语音波形
        drawWaveform(canvas)
    }
}
```

### 2. 业务逻辑层开发

#### 2.1 ViewModel
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/vm/VoiceControlViewModel.kt
class VoiceControlViewModel : ViewModel() {
    
    private val voiceManager = VoiceControlManager.getInstance()
    
    private val _voiceStatus = MutableLiveData<VoiceStatus>()
    val voiceStatus: LiveData<VoiceStatus> = _voiceStatus
    
    private val _recognitionResult = MutableLiveData<VoiceCommand>()
    val recognitionResult: LiveData<VoiceCommand> = _recognitionResult
    
    init {
        voiceManager.setVoiceListener(object : IVoiceListener {
            override fun onStatusChanged(status: VoiceStatus) {
                _voiceStatus.postValue(status)
            }
            
            override fun onCommandRecognized(command: VoiceCommand) {
                _recognitionResult.postValue(command)
            }
        })
    }
    
    fun toggleVoiceRecognition() {
        if (voiceManager.isRecording()) {
            voiceManager.stopRecognition()
        } else {
            voiceManager.startRecognition()
        }
    }
}
```

#### 2.2 Manager
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/manager/VoiceControlManager.kt
class VoiceControlManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: VoiceControlManager? = null
        
        fun getInstance(): VoiceControlManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: VoiceControlManager().also { INSTANCE = it }
            }
        }
    }
    
    private var voiceListener: IVoiceListener? = null
    private var isRecording = false
    
    fun setVoiceListener(listener: IVoiceListener) {
        this.voiceListener = listener
    }
    
    fun startRecognition() {
        if (!isRecording) {
            isRecording = true
            // 启动语音识别服务
            VoiceRecognitionService.start()
            voiceListener?.onStatusChanged(VoiceStatus.LISTENING)
        }
    }
    
    fun stopRecognition() {
        if (isRecording) {
            isRecording = false
            VoiceRecognitionService.stop()
            voiceListener?.onStatusChanged(VoiceStatus.STOPPED)
        }
    }
    
    fun isRecording(): Boolean = isRecording
}
```

#### 2.3 Service
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/service/VoiceRecognitionService.kt
class VoiceRecognitionService : Service() {
    
    companion object {
        fun start() {
            // 启动服务逻辑
        }
        
        fun stop() {
            // 停止服务逻辑
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startVoiceRecognition()
        return START_STICKY
    }
    
    private fun startVoiceRecognition() {
        // 实现语音识别逻辑
        // 可以集成第三方SDK或使用原生实现
    }
}
```

### 3. 数据层开发

#### 3.1 数据模型
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/bean/VoiceResult.kt
data class VoiceResult(
    val command: VoiceCommand,
    val confidence: Float,
    val timestamp: Long,
    val rawText: String
)

enum class VoiceCommand {
    START_CALIBRATION("开始校准"),
    STOP_TRACKING("停止追踪"),
    START_TRACKING("开始追踪"),
    PAUSE_TRACKING("暂停追踪"),
    UNKNOWN("未知命令");
    
    constructor(description: String) {
        this.description = description
    }
    
    val description: String
}

enum class VoiceStatus {
    STOPPED, LISTENING, PROCESSING, ERROR
}
```

#### 3.2 Repository
```kotlin
// app/src/main/java/com/mitdd/gazetracker/voice/repository/VoiceControlRepository.kt
class VoiceControlRepository {
    
    private val voicePreference = VoicePreference()
    
    fun saveVoiceSettings(settings: VoiceSettings) {
        voicePreference.saveSettings(settings)
    }
    
    fun getVoiceSettings(): VoiceSettings {
        return voicePreference.getSettings()
    }
    
    fun getCommandTemplates(): List<VoiceTemplate> {
        return voicePreference.getCommandTemplates()
    }
}
```

### 4. 集成到现有系统

#### 4.1 在AndroidManifest.xml中注册
```xml
<!-- app/src/main/AndroidManifest.xml -->
<activity
    android:name=".voice.VoiceControlActivity"
    android:screenOrientation="landscape"
    android:theme="@style/AppTheme" />

<service
    android:name=".voice.service.VoiceRecognitionService"
    android:enabled="true"
    android:exported="false" />

<!-- 添加录音权限 -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
```

#### 4.2 添加到主导航
```kotlin
// 在HomeMainFragment中添加语音控制入口
private fun initVoiceControl() {
    val voiceControlItem = ModuleItem(
        moduleName = "语音控制",
        moduleKey = "VOICE_CONTROL",
        moduleIcon = R.drawable.ic_voice_control
    )
    
    // 添加到模块列表
    moduleList.add(voiceControlItem)
}
```

### 5. 与现有眼动追踪系统集成

#### 5.1 集成GazeTrackingManager
```kotlin
// 在VoiceControlManager中集成眼动追踪
class VoiceControlManager {
    
    private val gazeTrackingManager = GazeTrackingManager.getInstance()
    
    private fun executeVoiceCommand(command: VoiceCommand) {
        when (command) {
            VoiceCommand.START_CALIBRATION -> {
                gazeTrackingManager.startCalibration()
            }
            VoiceCommand.START_TRACKING -> {
                gazeTrackingManager.startTracking()
            }
            VoiceCommand.STOP_TRACKING -> {
                gazeTrackingManager.stopTracking()
            }
            VoiceCommand.PAUSE_TRACKING -> {
                gazeTrackingManager.pauseTracking()
            }
            else -> {
                // 处理未知命令
            }
        }
    }
}
```

## 开发最佳实践

### 1. 遵循现有架构模式
- 严格按照分层架构开发
- 使用现有的基类（如GTBaseActivity）
- 遵循现有的命名规范

### 2. 错误处理
```kotlin
class VoiceControlManager {
    
    private fun handleVoiceError(error: VoiceError) {
        when (error.type) {
            VoiceErrorType.PERMISSION_DENIED -> {
                // 处理权限被拒绝
                voiceListener?.onError("录音权限被拒绝")
            }
            VoiceErrorType.NETWORK_ERROR -> {
                // 处理网络错误
                voiceListener?.onError("网络连接失败")
            }
            VoiceErrorType.RECOGNITION_FAILED -> {
                // 处理识别失败
                voiceListener?.onError("语音识别失败")
            }
        }
    }
}
```

### 3. 性能优化
- 使用后台服务处理语音识别，避免阻塞UI
- 合理管理内存，及时释放资源
- 避免影响眼动追踪的实时性能

### 4. 测试策略
```kotlin
// 单元测试示例
class VoiceControlViewModelTest {
    
    @Test
    fun testToggleVoiceRecognition() {
        val viewModel = VoiceControlViewModel()
        
        // 测试开始录音
        viewModel.toggleVoiceRecognition()
        assertEquals(VoiceStatus.LISTENING, viewModel.voiceStatus.value)
        
        // 测试停止录音
        viewModel.toggleVoiceRecognition()
        assertEquals(VoiceStatus.STOPPED, viewModel.voiceStatus.value)
    }
}
```

## 总结

通过以上步骤，您可以成功地在现有项目中新增语音控制模块。关键要点：

1. **遵循分层架构**：UI层 → 业务逻辑层 → 数据层 → 原生层
2. **保持代码一致性**：使用现有的基类、工具类和设计模式
3. **完善的错误处理**：确保系统稳定性
4. **性能考虑**：不影响核心眼动追踪功能
5. **充分测试**：单元测试和集成测试并重

这种模块化的开发方式确保了代码的可维护性和可扩展性，同时保持了与现有系统的良好集成。
