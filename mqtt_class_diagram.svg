<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .class-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-text { font-family: "Consolas", "Monaco", monospace; font-size: 11px; fill: #2c3e50; }
      .field-text { font-family: "Consolas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .interface-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; font-style: italic; }
      
      .manager-class { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .init-class { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .listener-class { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .constants-class { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .activity-class { fill: #fce4ec; stroke: #c2185b; stroke-width: 2; }
      .interface-class { fill: #f1f8e9; stroke: #689f38; stroke-width: 2; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .composition-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#diamond); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="diamond" markerWidth="12" markerHeight="8" refX="6" refY="4" orient="auto">
      <polygon points="0 4, 6 0, 12 4, 6 8" fill="white" stroke="#2c3e50" stroke-width="1" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">MQTT 类图</text>

  <!-- MQTTManager -->
  <rect x="50" y="60" width="300" height="280" class="manager-class" rx="5"/>
  <text x="200" y="85" text-anchor="middle" class="class-title">MQTTManager</text>
  <line x1="60" y1="95" x2="340" y2="95" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="60" y="115" class="field-text">- instanceId: AtomicReference&lt;String&gt;</text>
  <text x="60" y="130" class="field-text">- region: AtomicReference&lt;String&gt;</text>
  <text x="60" y="145" class="field-text">- deviceName: AtomicReference&lt;String&gt;</text>
  <text x="60" y="160" class="field-text">- deviceSecret: AtomicReference&lt;String&gt;</text>
  <text x="60" y="175" class="field-text">- productKey: AtomicReference&lt;String&gt;</text>
  <text x="60" y="190" class="field-text">- productSecret: AtomicReference&lt;String&gt;</text>
  <text x="60" y="205" class="field-text">- TOPIC_NOTIFY: AtomicReference&lt;String&gt;</text>
  <text x="60" y="220" class="field-text">- gson: Gson</text>
  
  <line x1="60" y1="230" x2="340" y2="230" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="60" y="250" class="method-text">+ parseMqttFourTuples(): void</text>
  <text x="60" y="265" class="method-text">+ publish(data, topic, qos, isReply, replyTopic, listener): void</text>
  <text x="60" y="280" class="method-text">+ subscribe(subTopic, qos, listener): void</text>
  <text x="60" y="295" class="method-text">+ unsubscribe(unSubTopic, listener): void</text>
  <text x="60" y="310" class="method-text">+ getDeviceInfo(): DeviceInfo</text>
  <text x="60" y="325" class="method-text">+ getInstanceId(): String</text>

  <!-- MQTTInitManager -->
  <rect x="400" y="60" width="320" height="280" class="init-class" rx="5"/>
  <text x="560" y="85" text-anchor="middle" class="class-title">MQTTInitManager</text>
  <line x1="410" y1="95" x2="710" y2="95" stroke="#7b1fa2" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="410" y="115" class="field-text">- isInitDone: Boolean</text>
  <text x="410" y="130" class="field-text">+ connectState: ConnectState</text>
  <text x="410" y="145" class="field-text">- iConnectNotifyHolder: IConnectNotifyHolder</text>
  
  <line x1="410" y1="155" x2="710" y2="155" stroke="#7b1fa2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="410" y="175" class="method-text">+ init(context, deviceName, deviceSecret, productKey,</text>
  <text x="410" y="190" class="method-text">       productSecret, mqttHost, listener): void</text>
  <text x="410" y="205" class="method-text">+ checkInit(): Boolean</text>
  <text x="410" y="220" class="method-text">+ registerDevice(context, productKey, deviceName,</text>
  <text x="410" y="235" class="method-text">                 productSecret, listener): void</text>
  <text x="410" y="250" class="method-text">+ addIConnectNotifyCallBack(callBack): void</text>
  <text x="410" y="265" class="method-text">+ removeIConnectNotifyCallBack(callBack): void</text>
  <text x="410" y="280" class="method-text">+ getConnectState(): ConnectState</text>
  <text x="410" y="295" class="method-text">+ isConnected(): Boolean</text>
  <text x="410" y="310" class="method-text">+ disconnect(): void</text>
  <text x="410" y="325" class="method-text">+ reconnect(): void</text>

  <!-- IConnectNotifyHolder -->
  <rect x="760" y="60" width="320" height="280" class="listener-class" rx="5"/>
  <text x="920" y="85" text-anchor="middle" class="class-title">IConnectNotifyHolder</text>
  <line x1="770" y1="95" x2="1070" y2="95" stroke="#388e3c" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="770" y="115" class="field-text">- listenerSet: HashSet&lt;IConnectNotifyCallBack&gt;</text>
  
  <line x1="770" y1="125" x2="1070" y2="125" stroke="#388e3c" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="770" y="145" class="method-text">+ addIConnectNotifyCallBack(callBack): void</text>
  <text x="770" y="160" class="method-text">+ removeIConnectNotifyCallBack(callBack): void</text>
  <text x="770" y="175" class="method-text">+ onNotify(connectId, topic, aMessage): void</text>
  <text x="770" y="190" class="method-text">+ shouldHandle(connectId, topic): Boolean</text>
  <text x="770" y="205" class="method-text">+ onConnectStateChange(connectId, connectState): void</text>
  <text x="770" y="220" class="method-text">- handlerNotify(data): void</text>

  <!-- MQTTConstants -->
  <rect x="1120" y="60" width="320" height="280" class="constants-class" rx="5"/>
  <text x="1280" y="85" text-anchor="middle" class="class-title">MQTTConstants</text>
  <line x1="1130" y1="95" x2="1430" y2="95" stroke="#f57c00" stroke-width="1"/>
  
  <!-- Constants -->
  <text x="1130" y="115" class="field-text">+ MQTT_AES_KEY: String</text>
  <text x="1130" y="130" class="field-text">+ SYSTEM_PROPERTIES_KEY_MQTT_QUAD: String</text>
  <text x="1130" y="145" class="field-text">+ LIVE_EVENT_INIT_DONE: String</text>
  <text x="1130" y="160" class="field-text">+ TOPIC_NOTIFY_PARAM_EVENT: String</text>
  <text x="1130" y="175" class="field-text">+ TOPIC_NOTIFY_PARAM_TYPE: String</text>
  <text x="1130" y="190" class="field-text">+ TOPIC_NOTIFY_PARAM_BIZ_KEY: String</text>
  <text x="1130" y="205" class="field-text">+ TOPIC_NOTIFY_PARAM_MESSAGE: String</text>
  <text x="1130" y="220" class="field-text">+ TOPIC_NOTIFY_PARAM_CODE: String</text>
  <text x="1130" y="235" class="field-text">+ TOPIC_NOTIFY_EVENT_DEVICE_UNBIND: String</text>

  <!-- GTBaseActivity -->
  <rect x="50" y="380" width="300" height="160" class="activity-class" rx="5"/>
  <text x="200" y="405" text-anchor="middle" class="class-title">GTBaseActivity</text>
  <line x1="60" y1="415" x2="340" y2="415" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="60" y="435" class="method-text">+ initMQTT(): void</text>
  <text x="60" y="450" class="method-text">+ onCreate(savedInstanceState): void</text>
  <text x="60" y="465" class="method-text">+ onDestroy(): void</text>
  <text x="60" y="480" class="method-text">+ onResume(): void</text>
  <text x="60" y="495" class="method-text">+ onPause(): void</text>
  <text x="60" y="510" class="method-text">- setupMQTTConnection(): void</text>
  <text x="60" y="525" class="method-text">- handleMQTTError(error): void</text>

  <!-- HomeMainActivity -->
  <rect x="400" y="380" width="300" height="160" class="activity-class" rx="5"/>
  <text x="550" y="405" text-anchor="middle" class="class-title">HomeMainActivity</text>
  <line x1="410" y1="415" x2="690" y2="415" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Fields -->
  <text x="410" y="435" class="field-text">- iConnectNotifyCallBack: IConnectNotifyCallBack</text>
  
  <line x1="410" y1="445" x2="690" y2="445" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="410" y="465" class="method-text">+ initListener(): void</text>
  <text x="410" y="480" class="method-text">+ onDestroy(): void</text>
  <text x="410" y="495" class="method-text">- handleDeviceUnbind(): void</text>
  <text x="410" y="510" class="method-text">- updateUIOnMQTTStateChange(): void</text>

  <!-- IConnectNotifyCallBack Interface -->
  <rect x="760" y="380" width="280" height="120" class="interface-class" rx="5"/>
  <text x="900" y="405" text-anchor="middle" class="class-title">&lt;&lt;interface&gt;&gt;</text>
  <text x="900" y="425" text-anchor="middle" class="class-title">IConnectNotifyCallBack</text>
  <line x1="770" y1="435" x2="1030" y2="435" stroke="#689f38" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="770" y="455" class="interface-text">+ onDeviceUnbind(): void</text>
  <text x="770" y="470" class="interface-text">+ onMQTTConnected(): void</text>
  <text x="770" y="485" class="interface-text">+ onMQTTDisconnected(): void</text>

  <!-- ConfigActivity -->
  <rect x="1120" y="380" width="280" height="120" class="activity-class" rx="5"/>
  <text x="1260" y="405" text-anchor="middle" class="class-title">ConfigActivity</text>
  <line x1="1130" y1="415" x2="1390" y2="415" stroke="#c2185b" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="1130" y="435" class="method-text">+ initData(): void</text>
  <text x="1130" y="450" class="method-text">+ updateMQTTStatus(): void</text>
  <text x="1130" y="465" class="method-text">+ displayConnectionState(): void</text>
  <text x="1130" y="480" class="method-text">- refreshMQTTInfo(): void</text>

  <!-- DeviceManager -->
  <rect x="50" y="580" width="280" height="140" class="manager-class" rx="5"/>
  <text x="190" y="605" text-anchor="middle" class="class-title">DeviceManager</text>
  <line x1="60" y1="615" x2="320" y2="615" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="60" y="635" class="method-text">+ getMQTTFourTuples(): String</text>
  <text x="60" y="650" class="method-text">+ getDeviceSn(): String</text>
  <text x="60" y="665" class="method-text">+ startConfigActivity(context): void</text>
  <text x="60" y="680" class="method-text">+ getSystemProperty(key): String</text>
  <text x="60" y="695" class="method-text">+ setSystemProperty(key, value): void</text>
  <text x="60" y="710" class="method-text">- decryptMQTTInfo(encrypted): String</text>

  <!-- AesManager -->
  <rect x="370" y="580" width="280" height="140" class="manager-class" rx="5"/>
  <text x="510" y="605" text-anchor="middle" class="class-title">AesManager</text>
  <line x1="380" y1="615" x2="640" y2="615" stroke="#1976d2" stroke-width="1"/>
  
  <!-- Methods -->
  <text x="380" y="635" class="method-text">+ encrypt(data, key): String</text>
  <text x="380" y="650" class="method-text">+ decrypt(data, key): String</text>
  <text x="380" y="665" class="method-text">+ getKey(keyName): String</text>
  <text x="380" y="680" class="method-text">+ generateKey(): String</text>
  <text x="380" y="695" class="method-text">- initCipher(mode, key): Cipher</text>
  <text x="380" y="710" class="method-text">- base64Encode(data): String</text>

  <!-- 关系线 -->
  <!-- MQTTManager uses MQTTInitManager -->
  <line x1="350" y1="200" x2="400" y2="200" class="arrow"/>
  <text x="375" y="195" class="field-text">uses</text>

  <!-- MQTTInitManager contains IConnectNotifyHolder -->
  <line x1="720" y1="200" x2="760" y2="200" class="composition-arrow"/>
  <text x="740" y="195" class="field-text">contains</text>

  <!-- MQTTManager uses MQTTConstants -->
  <line x1="350" y1="150" x2="1120" y2="150" class="dashed-arrow"/>
  <text x="735" y="145" class="field-text">uses</text>

  <!-- GTBaseActivity uses MQTTManager -->
  <line x1="200" y1="380" x2="200" y2="340" class="arrow"/>
  <text x="210" y="360" class="field-text">uses</text>

  <!-- GTBaseActivity uses MQTTInitManager -->
  <line x1="350" y1="460" x2="400" y2="300" class="arrow"/>
  <text x="375" y="380" class="field-text">uses</text>

  <!-- HomeMainActivity extends GTBaseActivity -->
  <line x1="400" y1="460" x2="350" y2="460" class="arrow"/>
  <text x="375" y="455" class="field-text">extends</text>

  <!-- HomeMainActivity implements IConnectNotifyCallBack -->
  <line x1="700" y1="460" x2="760" y2="460" class="dashed-arrow"/>
  <text x="730" y="455" class="field-text">implements</text>

  <!-- IConnectNotifyHolder uses IConnectNotifyCallBack -->
  <line x1="920" y1="340" x2="920" y2="380" class="dashed-arrow"/>
  <text x="930" y="360" class="field-text">uses</text>

  <!-- ConfigActivity uses MQTTInitManager -->
  <line x1="1260" y1="380" x2="700" y2="300" class="dashed-arrow"/>
  <text x="980" y="340" class="field-text">uses</text>

  <!-- MQTTManager uses DeviceManager -->
  <line x1="200" y1="340" x2="190" y2="580" class="arrow"/>
  <text x="210" y="460" class="field-text">uses</text>

  <!-- MQTTManager uses AesManager -->
  <line x1="300" y1="340" x2="450" y2="580" class="arrow"/>
  <text x="375" y="460" class="field-text">uses</text>

  <!-- 图例 -->
  <rect x="50" y="760" width="1400" height="120" fill="#f5f5f5" stroke="#cccccc" stroke-width="1" rx="5"/>
  <text x="70" y="785" class="class-title">图例说明</text>
  
  <line x1="70" y1="800" x2="120" y2="800" class="arrow"/>
  <text x="130" y="805" class="field-text">依赖关系 (uses)</text>
  
  <line x1="270" y1="800" x2="320" y2="800" class="dashed-arrow"/>
  <text x="330" y="805" class="field-text">实现关系 (implements)</text>
  
  <line x1="500" y1="800" x2="550" y2="800" class="composition-arrow"/>
  <text x="560" y="805" class="field-text">组合关系 (contains)</text>
  
  <rect x="70" y="820" width="80" height="20" class="manager-class"/>
  <text x="160" y="835" class="field-text">管理类</text>
  
  <rect x="250" y="820" width="80" height="20" class="init-class"/>
  <text x="340" y="835" class="field-text">初始化类</text>
  
  <rect x="430" y="820" width="80" height="20" class="listener-class"/>
  <text x="520" y="835" class="field-text">监听器类</text>
  
  <rect x="610" y="820" width="80" height="20" class="constants-class"/>
  <text x="700" y="835" class="field-text">常量类</text>
  
  <rect x="790" y="820" width="80" height="20" class="activity-class"/>
  <text x="880" y="835" class="field-text">Activity类</text>
  
  <rect x="970" y="820" width="80" height="20" class="interface-class"/>
  <text x="1060" y="835" class="field-text">接口类</text>

  <!-- 核心功能说明 -->
  <text x="70" y="860" class="field-text">核心功能：设备与阿里云IoT平台的MQTT通信，支持消息发布、订阅、设备解绑通知等功能</text>

</svg>
