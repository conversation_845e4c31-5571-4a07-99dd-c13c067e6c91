<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .message-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .note-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .step-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #e74c3c; }
      
      .actor-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .lifeline { stroke: #1976d2; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #2c3e50; stroke-width: 1; fill: none; stroke-dasharray: 3,3; marker-end: url(#arrowhead); }
      .self-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .activation-box { fill: #ffecb3; stroke: #ff8f00; stroke-width: 1; }
      .note-box { fill: #fff9c4; stroke: #f57f17; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">MQTT 时序图 - 初始化和消息处理流程</text>

  <!-- 参与者 -->
  <!-- GTBaseActivity -->
  <rect x="50" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="110" y="85" text-anchor="middle" class="actor-title">GTBaseActivity</text>
  <line x1="110" y1="100" x2="110" y2="1500" class="lifeline"/>

  <!-- MQTTManager -->
  <rect x="220" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="280" y="85" text-anchor="middle" class="actor-title">MQTTManager</text>
  <line x1="280" y1="100" x2="280" y2="1500" class="lifeline"/>

  <!-- DeviceManager -->
  <rect x="390" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="450" y="85" text-anchor="middle" class="actor-title">DeviceManager</text>
  <line x1="450" y1="100" x2="450" y2="1500" class="lifeline"/>

  <!-- AesManager -->
  <rect x="560" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="620" y="85" text-anchor="middle" class="actor-title">AesManager</text>
  <line x1="620" y1="100" x2="620" y2="1500" class="lifeline"/>

  <!-- MQTTInitManager -->
  <rect x="730" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="790" y="85" text-anchor="middle" class="actor-title">MQTTInitManager</text>
  <line x1="790" y1="100" x2="790" y2="1500" class="lifeline"/>

  <!-- IConnectNotifyHolder -->
  <rect x="900" y="60" width="140" height="40" class="actor-box" rx="5"/>
  <text x="970" y="85" text-anchor="middle" class="actor-title">IConnectNotifyHolder</text>
  <line x1="970" y1="100" x2="970" y2="1500" class="lifeline"/>

  <!-- LinkKit -->
  <rect x="1090" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1150" y="85" text-anchor="middle" class="actor-title">LinkKit</text>
  <line x1="1150" y1="100" x2="1150" y2="1500" class="lifeline"/>

  <!-- 阿里云IoT -->
  <rect x="1260" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1320" y="85" text-anchor="middle" class="actor-title">阿里云IoT</text>
  <line x1="1320" y1="100" x2="1320" y2="1500" class="lifeline"/>

  <!-- HomeMainActivity -->
  <rect x="1430" y="60" width="140" height="40" class="actor-box" rx="5"/>
  <text x="1500" y="85" text-anchor="middle" class="actor-title">HomeMainActivity</text>
  <line x1="1500" y1="100" x2="1500" y2="1500" class="lifeline"/>

  <!-- 步骤1: MQTT初始化 -->
  <text x="50" y="140" class="step-text">步骤1: MQTT初始化流程</text>
  
  <!-- 1.1 initMQTT -->
  <rect x="105" y="160" width="10" height="20" class="activation-box"/>
  <line x1="110" y1="170" x2="280" y2="170" class="message-arrow"/>
  <text x="195" y="165" class="message-text">initMQTT()</text>

  <!-- 1.2 parseMqttFourTuples -->
  <rect x="275" y="180" width="10" height="40" class="activation-box"/>
  <line x1="280" y1="190" x2="450" y2="190" class="message-arrow"/>
  <text x="365" y="185" class="message-text">getMQTTFourTuples()</text>

  <!-- 1.3 decrypt -->
  <line x1="280" y1="210" x2="620" y2="210" class="message-arrow"/>
  <text x="450" y="205" class="message-text">decrypt(property, key)</text>

  <!-- 1.4 return decrypted -->
  <line x1="620" y1="230" x2="280" y2="230" class="return-arrow"/>
  <text x="450" y="225" class="message-text">decrypted data</text>

  <!-- 1.5 init MQTT -->
  <line x1="280" y1="250" x2="790" y2="250" class="message-arrow"/>
  <text x="535" y="245" class="message-text">init(context, deviceName, deviceSecret, productKey, productSecret, mqttHost)</text>

  <!-- 1.6 create LinkKitInitParams -->
  <rect x="785" y="270" width="10" height="80" class="activation-box"/>
  <line x1="790" y1="280" x2="830" y2="280" class="self-arrow"/>
  <line x1="830" y1="280" x2="830" y2="300" stroke="#2c3e50" stroke-width="2"/>
  <line x1="830" y1="300" x2="790" y2="300" class="message-arrow"/>
  <text x="840" y="275" class="message-text">create LinkKitInitParams</text>
  <text x="840" y="290" class="message-text">configure DeviceInfo</text>
  <text x="840" y="305" class="message-text">setup IoTMqttClientConfig</text>

  <!-- 1.7 register listener -->
  <line x1="790" y1="320" x2="970" y2="320" class="message-arrow"/>
  <text x="880" y="315" class="message-text">registerOnPushListener()</text>

  <!-- 1.8 LinkKit init -->
  <line x1="790" y1="340" x2="1150" y2="340" class="message-arrow"/>
  <text x="970" y="335" class="message-text">LinkKit.init(context, params, listener)</text>

  <!-- 1.9 connect to cloud -->
  <rect x="1145" y="360" width="10" height="40" class="activation-box"/>
  <line x1="1150" y1="370" x2="1320" y2="370" class="message-arrow"/>
  <text x="1235" y="365" class="message-text">connect to IoT Hub</text>

  <!-- 1.10 connection result -->
  <line x1="1320" y1="390" x2="1150" y2="390" class="return-arrow"/>
  <text x="1235" y="385" class="message-text">connection result</text>

  <!-- 1.11 onInitDone callback -->
  <line x1="1150" y1="410" x2="790" y2="410" class="return-arrow"/>
  <text x="970" y="405" class="message-text">onInitDone(data)</text>

  <!-- 1.12 post init done event -->
  <line x1="790" y1="430" x2="830" y2="430" class="self-arrow"/>
  <line x1="830" y1="430" x2="830" y2="450" stroke="#2c3e50" stroke-width="2"/>
  <line x1="830" y1="450" x2="790" y2="450" class="message-arrow"/>
  <text x="840" y="425" class="message-text">LiveEventBus.post(true)</text>
  <text x="840" y="440" class="message-text">isInitDone = true</text>

  <!-- 步骤2: 消息发布 -->
  <text x="50" y="500" class="step-text">步骤2: 消息发布流程</text>

  <!-- 2.1 publish message -->
  <rect x="275" y="520" width="10" height="60" class="activation-box"/>
  <line x1="110" y1="530" x2="280" y2="530" class="message-arrow"/>
  <text x="195" y="525" class="message-text">publish(data, topic, qos, listener)</text>

  <!-- 2.2 check init -->
  <line x1="280" y1="550" x2="790" y2="550" class="message-arrow"/>
  <text x="535" y="545" class="message-text">checkInit()</text>

  <!-- 2.3 create publish request -->
  <line x1="280" y1="570" x2="320" y2="570" class="self-arrow"/>
  <line x1="320" y1="570" x2="320" y2="590" stroke="#2c3e50" stroke-width="2"/>
  <line x1="320" y1="590" x2="280" y2="590" class="message-arrow"/>
  <text x="330" y="565" class="message-text">create MqttPublishRequest</text>
  <text x="330" y="580" class="message-text">set topic, qos, payload</text>

  <!-- 2.4 LinkKit publish -->
  <line x1="280" y1="610" x2="1150" y2="610" class="message-arrow"/>
  <text x="715" y="605" class="message-text">LinkKit.publish(request, listener)</text>

  <!-- 2.5 send to cloud -->
  <rect x="1145" y="630" width="10" height="40" class="activation-box"/>
  <line x1="1150" y1="640" x2="1320" y2="640" class="message-arrow"/>
  <text x="1235" y="635" class="message-text">send message</text>

  <!-- 2.6 publish response -->
  <line x1="1320" y1="660" x2="1150" y2="660" class="return-arrow"/>
  <text x="1235" y="655" class="message-text">publish response</text>

  <!-- 2.7 callback -->
  <line x1="1150" y1="680" x2="280" y2="680" class="return-arrow"/>
  <text x="715" y="675" class="message-text">onResponse(request, response)</text>

  <!-- 步骤3: 消息订阅 -->
  <text x="50" y="730" class="step-text">步骤3: 消息订阅流程</text>

  <!-- 3.1 subscribe -->
  <rect x="275" y="750" width="10" height="60" class="activation-box"/>
  <line x1="110" y1="760" x2="280" y2="760" class="message-arrow"/>
  <text x="195" y="755" class="message-text">subscribe(topic, qos, listener)</text>

  <!-- 3.2 create subscribe request -->
  <line x1="280" y1="780" x2="320" y2="780" class="self-arrow"/>
  <line x1="320" y1="780" x2="320" y2="800" stroke="#2c3e50" stroke-width="2"/>
  <line x1="320" y1="800" x2="280" y2="800" class="message-arrow"/>
  <text x="330" y="775" class="message-text">create MqttSubscribeRequest</text>
  <text x="330" y="790" class="message-text">set topic, qos, isSubscribe</text>

  <!-- 3.3 LinkKit subscribe -->
  <line x1="280" y1="820" x2="1150" y2="820" class="message-arrow"/>
  <text x="715" y="815" class="message-text">LinkKit.subscribe(request, listener)</text>

  <!-- 3.4 subscribe to cloud -->
  <rect x="1145" y="840" width="10" height="40" class="activation-box"/>
  <line x1="1150" y1="850" x2="1320" y2="850" class="message-arrow"/>
  <text x="1235" y="845" class="message-text">subscribe topic</text>

  <!-- 3.5 subscribe response -->
  <line x1="1320" y1="870" x2="1150" y2="870" class="return-arrow"/>
  <text x="1235" y="865" class="message-text">subscribe success</text>

  <!-- 3.6 callback -->
  <line x1="1150" y1="890" x2="280" y2="890" class="return-arrow"/>
  <text x="715" y="885" class="message-text">onSuccess()</text>

  <!-- 步骤4: 接收下行消息 -->
  <text x="50" y="940" class="step-text">步骤4: 接收下行消息流程</text>

  <!-- 4.1 cloud sends message -->
  <line x1="1320" y1="960" x2="1150" y2="960" class="message-arrow"/>
  <text x="1235" y="955" class="message-text">send notification</text>

  <!-- 4.2 LinkKit receives -->
  <rect x="1145" y="980" width="10" height="40" class="activation-box"/>
  <line x1="1150" y1="990" x2="970" y2="990" class="message-arrow"/>
  <text x="1060" y="985" class="message-text">onNotify(connectId, topic, message)</text>

  <!-- 4.3 shouldHandle check -->
  <rect x="965" y="1010" width="10" height="80" class="activation-box"/>
  <line x1="970" y1="1020" x2="1010" y2="1020" class="self-arrow"/>
  <line x1="1010" y1="1020" x2="1010" y2="1040" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1010" y1="1040" x2="970" y2="1040" class="message-arrow"/>
  <text x="1020" y="1015" class="message-text">shouldHandle(connectId, topic)</text>
  <text x="1020" y="1030" class="message-text">return true</text>

  <!-- 4.4 handle notify -->
  <line x1="970" y1="1060" x2="1010" y2="1060" class="self-arrow"/>
  <line x1="1010" y1="1060" x2="1010" y2="1080" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1010" y1="1080" x2="970" y2="1080" class="message-arrow"/>
  <text x="1020" y="1055" class="message-text">handlerNotify(data)</text>
  <text x="1020" y="1070" class="message-text">parse JSON, extract event</text>

  <!-- 步骤5: 设备解绑处理 -->
  <text x="50" y="1130" class="step-text">步骤5: 设备解绑通知处理</text>

  <!-- 5.1 device unbind event -->
  <rect x="965" y="1150" width="10" height="100" class="activation-box"/>
  <line x1="970" y1="1160" x2="450" y2="1160" class="message-arrow"/>
  <text x="710" y="1155" class="message-text">getDeviceSn()</text>

  <!-- 5.2 compare device SN -->
  <line x1="970" y1="1180" x2="1010" y2="1180" class="self-arrow"/>
  <line x1="1010" y1="1180" x2="1010" y2="1200" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1010" y1="1200" x2="970" y2="1200" class="message-arrow"/>
  <text x="1020" y="1175" class="message-text">compare bizKey with deviceSn</text>
  <text x="1020" y="1190" class="message-text">if match, notify listeners</text>

  <!-- 5.3 notify HomeMainActivity -->
  <line x1="970" y1="1220" x2="1500" y2="1220" class="message-arrow"/>
  <text x="1235" y="1215" class="message-text">onDeviceUnbind()</text>

  <!-- 5.4 handle unbind -->
  <rect x="1495" y="1240" width="10" height="40" class="activation-box"/>
  <line x1="1500" y1="1250" x2="1540" y2="1250" class="self-arrow"/>
  <line x1="1540" y1="1250" x2="1540" y2="1270" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1540" y1="1270" x2="1500" y2="1270" class="message-arrow"/>
  <text x="1550" y="1245" class="message-text">handle device unbind</text>
  <text x="1550" y="1260" class="message-text">update UI, clear data</text>

  <!-- 注释框 -->
  <rect x="50" y="1320" width="1500" height="160" class="note-box" rx="5"/>
  <text x="70" y="1345" class="step-text">关键流程说明：</text>
  
  <text x="70" y="1365" class="note-text">1. 初始化：GTBaseActivity启动时调用initMQTT()，解析四元组信息，初始化LinkKit连接阿里云IoT</text>
  <text x="70" y="1380" class="note-text">2. 消息发布：通过MQTTManager.publish()发送数据到指定Topic，支持QoS和RPC模式</text>
  <text x="70" y="1395" class="note-text">3. 消息订阅：通过MQTTManager.subscribe()订阅Topic，接收云端下行消息</text>
  <text x="70" y="1410" class="note-text">4. 下行处理：IConnectNotifyHolder接收云端消息，根据Topic和事件类型进行分发处理</text>
  <text x="70" y="1425" class="note-text">5. 设备解绑：接收device-unbind事件，比较设备SN，通知相关Activity处理解绑逻辑</text>
  <text x="70" y="1440" class="note-text">6. 连接管理：自动重连机制，连接状态监控，支持断线重连和状态通知</text>
  <text x="70" y="1455" class="note-text">7. 安全机制：四元组信息AES加密存储，支持动态注册和一型一密认证</text>

</svg>
