<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .step-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .condition-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #e74c3c; font-style: italic; }
      
      .message-step { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .param-step { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .header-step { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .url-step { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .duration-step { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .start-step { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 10; }
      
      .step-module { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 8; }
      .param-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .header-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .url-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .duration-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      .start-module { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
      .flow-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">MSG_START_APPLIED_CURE 消息处理工作流程图</text>
  
  <!-- 第一步：消息接收 -->
  <rect x="50" y="70" width="350" height="100" class="message-step"/>
  <text x="225" y="95" text-anchor="middle" class="section-title">1. 消息接收</text>
  
  <rect x="70" y="110" width="310" height="50" class="step-module"/>
  <text x="225" y="130" text-anchor="middle" class="method-title">GazeConstants.MSG_START_APPLIED_CURE</text>
  <text x="80" y="150" class="text">• 接收来自MaskTherapyFragment的启动消息</text>

  <!-- 第二步：报告参数处理 -->
  <rect x="450" y="70" width="400" height="100" class="param-step"/>
  <text x="650" y="95" text-anchor="middle" class="section-title">2. 报告参数处理</text>
  
  <rect x="470" y="110" width="360" height="50" class="param-module"/>
  <text x="650" y="130" text-anchor="middle" class="method-title">KEY_REPORT_PARAM 处理</text>
  <text x="480" y="150" class="text">• 解析JSON格式的报告参数，设置到ReportManager</text>

  <!-- 第三步：请求头处理 -->
  <rect x="900" y="70" width="400" height="100" class="header-step"/>
  <text x="1100" y="95" text-anchor="middle" class="section-title">3. 请求头处理</text>
  
  <rect x="920" y="110" width="360" height="50" class="header-module"/>
  <text x="1100" y="130" text-anchor="middle" class="method-title">KEY_REPORT_HEADER 处理</text>
  <text x="930" y="150" class="text">• 解析设备信息和版本信息，设置HTTP请求头</text>

  <!-- 第四步：URL配置 -->
  <rect x="1350" y="70" width="400" height="100" class="url-step"/>
  <text x="1550" y="95" text-anchor="middle" class="section-title">4. URL配置</text>
  
  <rect x="1370" y="110" width="360" height="50" class="url-module"/>
  <text x="1550" y="130" text-anchor="middle" class="method-title">URL配置设置</text>
  <text x="1380" y="150" class="text">• 设置报告URL和基础URL到ReportManager</text>

  <!-- 第五步：时长参数处理 -->
  <rect x="50" y="200" width="600" height="100" class="duration-step"/>
  <text x="350" y="225" text-anchor="middle" class="section-title">5. 时长参数处理</text>
  
  <rect x="70" y="240" width="560" height="50" class="duration-module"/>
  <text x="350" y="260" text-anchor="middle" class="method-title">计划时长和治疗时长获取</text>
  <text x="80" y="280" class="text">• 从消息中获取或使用默认值：plannedDuration, treatmentDuration</text>

  <!-- 第六步：启动遮盖疗法 -->
  <rect x="700" y="200" width="600" height="100" class="start-step"/>
  <text x="1000" y="225" text-anchor="middle" class="section-title">6. 启动遮盖疗法</text>
  
  <rect x="720" y="240" width="560" height="50" class="start-module"/>
  <text x="1000" y="260" text-anchor="middle" class="method-title">startAppliedCure(plannedDuration, treatmentDuration)</text>
  <text x="730" y="280" class="text">• 调用核心方法启动遮盖疗法，传递时长参数</text>

  <!-- 连接箭头 -->
  <line x1="400" y1="120" x2="450" y2="120" class="arrow"/>
  <line x1="850" y1="120" x2="900" y2="120" class="arrow"/>
  <line x1="1300" y1="120" x2="1350" y2="120" class="arrow"/>
  <line x1="225" y1="170" x2="350" y2="200" class="flow-arrow"/>
  <line x1="650" y1="200" x2="700" y2="250" class="flow-arrow"/>

  <!-- 详细处理流程 -->
  <rect x="50" y="320" width="1700" height="1030" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="345" text-anchor="middle" class="title" style="font-size: 22px;">MSG_START_APPLIED_CURE 详细处理流程</text>

  <!-- 第一部分：报告参数处理详解 -->
  <text x="70" y="380" class="step-title">📊 报告参数处理 (KEY_REPORT_PARAM)</text>
  
  <text x="90" y="405" class="flow-text" style="font-weight: bold;">1. 参数存在性检查</text>
  <text x="110" y="425" class="code-text">if (msg.data.containsKey(GazeConstants.KEY_REPORT_PARAM))</text>
  <text x="110" y="440" class="flow-text">• 检查消息Bundle中是否包含报告参数键</text>
  <text x="110" y="455" class="flow-text">• 确保参数传递的完整性和安全性</text>
  
  <text x="90" y="480" class="flow-text" style="font-weight: bold;">2. JSON参数解析</text>
  <text x="110" y="500" class="code-text">val params = msg.data.getString(GazeConstants.KEY_REPORT_PARAM).orEmpty()</text>
  <text x="110" y="515" class="code-text">val type = object : TypeToken&lt;HashMap&lt;String, Any&gt;&gt;() {}.type</text>
  <text x="110" y="530" class="code-text">ReportManager.setReportParams(mGson.fromJson&lt;HashMap&lt;String, Any&gt;&gt;(params, type))</text>
  
  <text x="110" y="550" class="flow-text">• <tspan style="font-weight: bold;">参数获取：</tspan>从Bundle中获取JSON字符串，使用orEmpty()防止null</text>
  <text x="110" y="565" class="flow-text">• <tspan style="font-weight: bold;">类型定义：</tspan>使用TypeToken定义HashMap&lt;String, Any&gt;类型</text>
  <text x="110" y="580" class="flow-text">• <tspan style="font-weight: bold;">JSON解析：</tspan>使用Gson将JSON字符串转换为HashMap</text>
  <text x="110" y="595" class="flow-text">• <tspan style="font-weight: bold;">参数设置：</tspan>将解析后的参数设置到ReportManager中</text>
  
  <text x="90" y="620" class="flow-text" style="font-weight: bold;">3. 异常处理机制</text>
  <text x="110" y="640" class="code-text">try { ... } catch (e: Exception) { e.printStackTrace() }</text>
  <text x="110" y="655" class="flow-text">• 捕获JSON解析异常，防止程序崩溃</text>
  <text x="110" y="670" class="flow-text">• 打印异常堆栈，便于问题排查和调试</text>
  <text x="110" y="685" class="flow-text">• 确保即使参数解析失败，遮盖疗法仍能正常启动</text>

  <!-- 第二部分：请求头处理详解 -->
  <text x="900" y="380" class="step-title">🌐 请求头处理 (KEY_REPORT_HEADER)</text>
  
  <text x="920" y="405" class="flow-text" style="font-weight: bold;">1. 请求头存在性检查</text>
  <text x="940" y="425" class="code-text">if (msg.data.containsKey(GazeConstants.KEY_REPORT_HEADER))</text>
  <text x="940" y="440" class="flow-text">• 检查消息Bundle中是否包含请求头配置</text>
  <text x="940" y="455" class="flow-text">• 确保HTTP请求头的正确配置</text>
  
  <text x="920" y="480" class="flow-text" style="font-weight: bold;">2. 请求头解析和设置</text>
  <text x="940" y="500" class="code-text">val headers = msg.data.getString(GazeConstants.KEY_REPORT_HEADER).orEmpty()</text>
  <text x="940" y="515" class="code-text">val type = object : TypeToken&lt;HashMap&lt;String, Any&gt;&gt;() {}.type</text>
  <text x="940" y="530" class="code-text">ReportManager.setReportHeaders(mGson.fromJson&lt;HashMap&lt;String, Any&gt;&gt;(headers, type))</text>
  
  <text x="940" y="550" class="flow-text">• <tspan style="font-weight: bold;">请求头内容：</tspan>包含设备信息、版本信息、语言设置等</text>
  <text x="940" y="565" class="flow-text">• <tspan style="font-weight: bold;">设备标识：</tspan>X-Device-Sn, X-Device-Mode用于设备识别</text>
  <text x="940" y="580" class="flow-text">• <tspan style="font-weight: bold;">版本信息：</tspan>X-Rom-Version, X-App-Version用于兼容性</text>
  <text x="940" y="595" class="flow-text">• <tspan style="font-weight: bold;">客户端标识：</tspan>X-Airdoc-Client用于客户端识别</text>
  <text x="940" y="610" class="flow-text">• <tspan style="font-weight: bold;">国际化支持：</tspan>Accept-Language支持多语言</text>
  
  <text x="920" y="635" class="flow-text" style="font-weight: bold;">3. 请求头的作用</text>
  <text x="940" y="655" class="flow-text">• 服务器端可以根据设备信息进行个性化处理</text>
  <text x="940" y="670" class="flow-text">• 版本信息用于API兼容性检查和功能控制</text>
  <text x="940" y="685" class="flow-text">• 语言设置确保返回数据的本地化</text>

  <!-- 第三部分：URL配置详解 -->
  <text x="70" y="720" class="step-title">🔗 URL配置处理</text>
  
  <text x="90" y="745" class="flow-text" style="font-weight: bold;">1. 报告URL设置</text>
  <text x="110" y="765" class="code-text">if (msg.data.containsKey(GazeConstants.KEY_REPORT_URL))</text>
  <text x="110" y="780" class="code-text">ReportManager.setReportUrl(msg.data.getString(GazeConstants.KEY_REPORT_URL).orEmpty())</text>
  <text x="110" y="800" class="flow-text">• <tspan style="font-weight: bold;">API端点：</tspan>"dt/api/train/v1/occlusion-therapy/event/report"</text>
  <text x="110" y="815" class="flow-text">• <tspan style="font-weight: bold;">用途：</tspan>遮盖疗法事件和结果数据上报</text>
  <text x="110" y="830" class="flow-text">• <tspan style="font-weight: bold;">实时性：</tspan>支持治疗过程中的实时数据上报</text>
  
  <text x="90" y="855" class="flow-text" style="font-weight: bold;">2. 基础URL设置</text>
  <text x="110" y="875" class="code-text">if (msg.data.containsKey(GazeConstants.KEY_BASE_URL))</text>
  <text x="110" y="890" class="code-text">ReportManager.setBaseUrl(msg.data.getString(GazeConstants.KEY_BASE_URL).orEmpty())</text>
  <text x="110" y="910" class="flow-text">• <tspan style="font-weight: bold;">基础域名：</tspan>UrlConfig.MAIN_DOMAIN</text>
  <text x="110" y="925" class="flow-text">• <tspan style="font-weight: bold;">完整URL：</tspan>基础URL + 报告URL组成完整的API地址</text>
  <text x="110" y="940" class="flow-text">• <tspan style="font-weight: bold;">环境切换：</tspan>支持开发、测试、生产环境的URL切换</text>

  <!-- 第四部分：时长参数处理详解 -->
  <text x="900" y="720" class="step-title">⏱️ 时长参数处理</text>
  
  <text x="920" y="745" class="flow-text" style="font-weight: bold;">1. 计划时长获取</text>
  <text x="940" y="765" class="code-text">val plannedDuration = if (msg.data.containsKey(GazeConstants.KEY_PLANNED_DURATION)) {</text>
  <text x="960" y="780" class="code-text">msg.data.getInt(GazeConstants.KEY_PLANNED_DURATION)</text>
  <text x="940" y="795" class="code-text">} else { mPlannedDuration }</text>
  
  <text x="940" y="815" class="flow-text">• <tspan style="font-weight: bold;">优先级：</tspan>消息中的参数优先于默认值</text>
  <text x="940" y="830" class="flow-text">• <tspan style="font-weight: bold;">单位：</tspan>秒为单位的治疗时长</text>
  <text x="940" y="845" class="flow-text">• <tspan style="font-weight: bold;">来源：</tspan>来自MaskViewModel的计划治疗时长</text>
  
  <text x="920" y="870" class="flow-text" style="font-weight: bold;">2. 已治疗时长获取</text>
  <text x="940" y="890" class="code-text">val treatmentDuration = if (msg.data.containsKey(GazeConstants.KEY_TREATMENT_DURATION)) {</text>
  <text x="960" y="905" class="code-text">msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)</text>
  <text x="940" y="920" class="code-text">} else { mTreatmentDuration }</text>
  
  <text x="940" y="940" class="flow-text">• <tspan style="font-weight: bold;">累计时长：</tspan>用户今日已完成的治疗时长</text>
  <text x="940" y="955" class="flow-text">• <tspan style="font-weight: bold;">断点续传：</tspan>支持治疗中断后的时长累计</text>
  <text x="940" y="970" class="flow-text">• <tspan style="font-weight: bold;">进度计算：</tspan>用于计算治疗进度和剩余时长</text>

  <!-- 第五部分：启动遮盖疗法 -->
  <text x="70" y="1005" class="step-title">🚀 启动遮盖疗法</text>
  
  <text x="90" y="1030" class="flow-text" style="font-weight: bold;">1. 核心方法调用</text>
  <text x="110" y="1050" class="code-text">startAppliedCure(plannedDuration, treatmentDuration)</text>
  <text x="110" y="1070" class="flow-text">• <tspan style="font-weight: bold;">参数传递：</tspan>将处理后的时长参数传递给核心启动方法</text>
  <text x="110" y="1085" class="flow-text">• <tspan style="font-weight: bold;">状态管理：</tspan>开始遮盖疗法的状态管理和监控</text>
  <text x="110" y="1100" class="flow-text">• <tspan style="font-weight: bold;">资源分配：</tspan>分配必要的系统资源用于遮盖疗法</text>
  
  <text x="90" y="1125" class="flow-text" style="font-weight: bold;">2. 后续处理流程</text>
  <text x="110" y="1145" class="flow-text">• Native层开始眼动追踪和遮盖算法</text>
  <text x="110" y="1160" class="flow-text">• 实时数据上报使用配置的URL和请求头</text>
  <text x="110" y="1175" class="flow-text">• 治疗进度监控和时长管理</text>
  <text x="110" y="1190" class="flow-text">• 治疗完成后的结果处理和数据上报</text>

  <!-- 第六部分：工作总结 -->
  <rect x="900" y="1005" width="750" height="180" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="920" y="1030" class="step-title">🌟 MSG_START_APPLIED_CURE 工作总结</text>
  
  <text x="920" y="1055" class="flow-text">• <tspan style="font-weight: bold;">配置管理：</tspan>统一管理报告参数、请求头、URL等配置信息</text>
  <text x="920" y="1075" class="flow-text">• <tspan style="font-weight: bold;">参数处理：</tspan>灵活处理时长参数，支持默认值和动态传递</text>
  <text x="920" y="1095" class="flow-text">• <tspan style="font-weight: bold;">异常安全：</tspan>完善的异常处理机制，确保系统稳定性</text>
  <text x="920" y="1115" class="flow-text">• <tspan style="font-weight: bold;">数据上报：</tspan>为后续的实时数据上报做好完整的配置准备</text>
  <text x="920" y="1135" class="flow-text">• <tspan style="font-weight: bold;">模块解耦：</tspan>通过ReportManager统一管理，实现模块间的解耦</text>
  <text x="920" y="1155" class="flow-text">• <tspan style="font-weight: bold;">启动控制：</tspan>最终调用核心方法启动遮盖疗法，完成整个启动流程</text>

  <!-- 底部流程箭头 -->
  <text x="70" y="1230" class="flow-text" style="font-weight: bold; font-size: 14px;">处理流程：消息接收 → 参数解析 → 配置设置 → 时长处理 → 启动疗法</text>
  <line x1="70" y1="1250" x2="1650" y2="1250" class="flow-arrow"/>
  <text x="860" y="1270" text-anchor="middle" class="flow-text">完整的消息处理和配置管理流程</text>

</svg>
