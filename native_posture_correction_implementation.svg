<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <linearGradient id="inputGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976d2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="jniGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f57c00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cppGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d32f2f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="detectionGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9c27b0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outputGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4caf50;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    nativePostureCorrection 方法实现详解
  </text>

  <!-- 输入数据层 -->
  <rect x="50" y="60" width="1500" height="100" rx="10" fill="url(#inputGrad)" stroke="#1976d2" stroke-width="2"/>
  <text x="800" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">输入数据处理层</text>

  <!-- 入参详细说明 -->
  <rect x="100" y="100" width="300" height="50" rx="5" fill="#fff" stroke="#1976d2" stroke-width="1"/>
  <text x="250" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">JNI入参</text>
  <text x="250" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">jlong thiz, jbyteArray inputImage_</text>
  <text x="250" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">jint width, jint height</text>

  <rect x="420" y="100" width="300" height="50" rx="5" fill="#fff" stroke="#1976d2" stroke-width="1"/>
  <text x="570" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">图像数据类型</text>
  <text x="570" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">jbyteArray: Y通道灰度数据</text>
  <text x="570" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">GTUtils.getDataFromImageY(ImageProxy)</text>

  <rect x="740" y="100" width="300" height="50" rx="5" fill="#fff" stroke="#1976d2" stroke-width="1"/>
  <text x="890" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">数据转换</text>
  <text x="890" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">jbyte* → unsigned char*</text>
  <text x="890" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Mat grayimg(height, width, CV_8UC1)</text>

  <rect x="1060" y="100" width="300" height="50" rx="5" fill="#fff" stroke="#1976d2" stroke-width="1"/>
  <text x="1210" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">GazeService调用</text>
  <text x="1210" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">tracker_service->process_poseAlign</text>
  <text x="1210" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">返回pose_align_result</text>

  <!-- C++处理层 -->
  <rect x="50" y="200" width="1500" height="120" rx="10" fill="url(#cppGrad)" stroke="#d32f2f" stroke-width="2"/>
  <text x="800" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">C++ process_poseAlign 处理层</text>

  <!-- 状态检查 -->
  <rect x="100" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="200" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">状态检查</text>
  <text x="200" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">processing_state ==</text>
  <text x="200" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">SERVICE_POSE_ALIGN</text>

  <!-- 人脸检测调用 -->
  <rect x="320" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="420" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">人脸检测</text>
  <text x="420" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">detector.detection(Image)</text>
  <text x="420" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">→ frame_detection_result</text>

  <!-- 姿势校准处理 -->
  <rect x="540" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="640" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">姿势校准</text>
  <text x="640" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pose_align_class.</text>
  <text x="640" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">head_pose_aligning</text>

  <!-- 视频保存 -->
  <rect x="760" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="860" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">视频保存</text>
  <text x="860" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">dataWriter.save_videos_func</text>
  <text x="860" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">(Image, detect_result)</text>

  <!-- 状态更新 -->
  <rect x="980" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="1080" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">状态更新</text>
  <text x="1080" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pose_aligned完成时</text>
  <text x="1080" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">→ SERVICE_NONE</text>

  <!-- 可视化处理 -->
  <rect x="1200" y="250" width="200" height="60" rx="5" fill="#fff" stroke="#d32f2f" stroke-width="1"/>
  <text x="1300" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">可视化处理</text>
  <text x="1300" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">visual_pose_align_func</text>
  <text x="1300" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">BGR→RGBA转换</text>

  <!-- 人脸检测详细流程 -->
  <rect x="50" y="360" width="1500" height="200" rx="10" fill="url(#detectionGrad)" stroke="#9c27b0" stroke-width="2"/>
  <text x="800" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">Detection.detection() 三级检测流程</text>

  <!-- 第一级：人脸检测 -->
  <rect x="100" y="410" width="280" height="120" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="240" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9c27b0">1. 人脸检测 (faceDetect)</text>
  <text x="240" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输入: Mat grayimg (CV_8UC1)</text>
  <text x="240" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">resize → 224x160</text>
  <text x="240" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">RKNN模型推理</text>
  <text x="240" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">阈值: 0.6</text>
  <text x="240" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输出: left_eye_rect, right_eye_rect</text>
  <text x="240" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">det_failed状态更新</text>

  <!-- 第二级：眼部跟踪 -->
  <rect x="400" y="410" width="280" height="120" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="540" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9c27b0">2. 眼部跟踪 (eyeTrack)</text>
  <text x="540" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输入: eye_rect区域</text>
  <text x="540" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">resize → 160x128</text>
  <text x="540" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">虹膜分割推理</text>
  <text x="540" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">阈值: 0.6</text>
  <text x="540" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输出: left_iris_rect, right_iris_rect</text>
  <text x="540" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">create_eye_tracking_rect_func</text>

  <!-- 第三级：瞳孔检测 -->
  <rect x="700" y="410" width="280" height="120" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="840" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9c27b0">3. 瞳孔检测 (lightsDetect)</text>
  <text x="840" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输入: iris_rect区域</text>
  <text x="840" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">resize → 64x64</text>
  <text x="840" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">普尔钦斑检测</text>
  <text x="840" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">瞳孔中心点检测</text>
  <text x="840" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输出: lights_point_list</text>
  <text x="840" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pupil_cent_point</text>

  <!-- 数据组装 -->
  <rect x="1000" y="410" width="280" height="120" rx="5" fill="#fff" stroke="#9c27b0" stroke-width="1"/>
  <text x="1140" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9c27b0">4. 数据组装</text>
  <text x="1140" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">assignment_func组装</text>
  <text x="1140" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">one_eye_detect_result</text>
  <text x="1140" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">计算瞳孔间距离</text>
  <text x="1140" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">interpupillary_distance</text>
  <text x="1140" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">返回: frame_detection_result</text>
  <text x="1140" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">{left_eye, right_eye, 瞳距}</text>

  <!-- 姿势校准算法详解 -->
  <rect x="50" y="600" width="1500" height="220" rx="10" fill="#fff8e1" stroke="#ffc107" stroke-width="2"/>
  <text x="800" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#f57c00">head_pose_aligning 姿势校准算法详解</text>

  <!-- 虹膜宽度比例检查 -->
  <rect x="100" y="650" width="220" height="100" rx="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="210" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976d2">虹膜宽度比例检查</text>
  <text x="210" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">two_iris_width_ratio =</text>
  <text x="210" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">left_iris_width / right_iris_width</text>
  <text x="210" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">范围: [0.8, 1.25]</text>
  <text x="210" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">通过则计算期望瞳距</text>
  <text x="210" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">expect_interpupillary_distance</text>

  <!-- 距离计算 -->
  <rect x="340" y="650" width="220" height="100" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="450" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f57c00">距离计算</text>
  <text x="450" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">mean_iris_width = (left + right) / 2</text>
  <text x="450" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">tmp_rate = mean_iris_width /</text>
  <text x="450" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">setting_pose_iris_width</text>
  <text x="450" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">tmp_norm_dist = interpupillary /</text>
  <text x="450" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">tmp_rate</text>

  <!-- 姿势有效性判断 -->
  <rect x="580" y="650" width="220" height="100" rx="5" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
  <text x="690" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#388e3c">姿势有效性判断</text>
  <text x="690" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">recognize_pose_valid()</text>
  <text x="690" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">计算眼部位置偏移</text>
  <text x="690" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">距离比例检查</text>
  <text x="690" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">返回: !skew (aligning)</text>
  <text x="690" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pose_result.aligning</text>

  <!-- 稳定性检测 -->
  <rect x="820" y="650" width="220" height="100" rx="5" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
  <text x="930" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7b1fa2">稳定性检测</text>
  <text x="930" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">diff = cur_p - pose_preview</text>
  <text x="930" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">阈值: pose_stable_move_min</text>
  <text x="930" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">稳定时开始倒计时</text>
  <text x="930" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">不稳定时重置计时器</text>
  <text x="930" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pose_align_time = now()</text>

  <!-- 倒计时机制 -->
  <rect x="1060" y="650" width="220" height="100" rx="5" fill="#ffebee" stroke="#f44336" stroke-width="1"/>
  <text x="1170" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d32f2f">倒计时机制</text>
  <text x="1170" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">keep_time = (now - pose_align_time)</text>
  <text x="1170" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">/ 1e+9 (纳秒→秒)</text>
  <text x="1170" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">remaining = max(0, cunt_down -</text>
  <text x="1170" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">(int)keep_time)</text>
  <text x="1170" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">remaining == 0 → 完成</text>

  <!-- 距离映射 -->
  <rect x="1300" y="650" width="220" height="100" rx="5" fill="#fff8e1" stroke="#ffc107" stroke-width="1"/>
  <text x="1410" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#f57c00">距离映射</text>
  <text x="1410" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">norm_dist_ratio = expect /</text>
  <text x="1410" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">det_result.interpupillary</text>
  <text x="1410" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">distance_sigmoid(ratio)</text>
  <text x="1410" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Sigmoid函数映射到[0,1]</text>
  <text x="1410" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">pose_result.dist</text>

  <!-- 距离Sigmoid函数详解 -->
  <rect x="50" y="860" width="1500" height="150" rx="10" fill="#f5f5f5" stroke="#999" stroke-width="2"/>
  <text x="800" y="885" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#333">distance_sigmoid 距离映射函数详解</text>

  <!-- Sigmoid公式 -->
  <rect x="100" y="910" width="400" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="300" y="930" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">Sigmoid函数公式</text>
  <text x="300" y="945" text-anchor="middle" font-family="Courier New, monospace" font-size="10" fill="#333">y = 1.0346 * (1.0 / (1.0 + exp(-2.6 * (x - 0.994))) + 0.83) - 0.88</text>
  <text x="300" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">参数: k=2.6, alpha=1.0346, x0=0.994, beta=-0.83, gamma=-0.88</text>
  <text x="300" y="975" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">输出范围: [0.0, 1.0] (clamp限制)</text>

  <!-- 距离阈值映射 -->
  <rect x="520" y="910" width="400" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="720" y="930" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">距离阈值映射关系</text>
  <text x="720" y="945" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.0-0.4: 很近 (红色警告) | 0.4-0.45: 较近 (橙色)</text>
  <text x="720" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.45-0.55: 适中 (绿色正常) | 0.55-0.6: 较远 (橙色)</text>
  <text x="720" y="975" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.6-1.0: 很远 (红色警告)</text>

  <!-- 瞳孔坐标归一化 -->
  <rect x="940" y="910" width="400" height="80" rx="5" fill="#fff" stroke="#666" stroke-width="1"/>
  <text x="1140" y="930" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">瞳孔坐标归一化</text>
  <text x="1140" y="945" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">left_pupil = Point2f(cur_p.left_pupil.x / image_width,</text>
  <text x="1140" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">                     cur_p.left_pupil.y / image_height)</text>
  <text x="1140" y="975" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">right_pupil同理，归一化到[0,1]范围</text>

  <!-- 输出数据结构 -->
  <rect x="50" y="1050" width="1500" height="200" rx="10" fill="url(#outputGrad)" stroke="#4caf50" stroke-width="2"/>
  <text x="800" y="1075" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#fff">输出数据结构组装</text>

  <!-- pose_align_result结构 -->
  <rect x="100" y="1100" width="300" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="250" y="1120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">pose_align_result</text>
  <text x="250" y="1135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Point2f left_pupil</text>
  <text x="250" y="1150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Point2f right_pupil</text>
  <text x="250" y="1165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">bool pose_aligned</text>
  <text x="250" y="1180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">bool aligning</text>
  <text x="250" y="1195" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">float dist</text>
  <text x="250" y="1210" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">int countdown</text>

  <!-- JNI HashMap组装 -->
  <rect x="420" y="1100" width="300" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="570" y="1120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">JNI HashMap组装</text>
  <text x="570" y="1135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"state" → pose_aligned (Boolean)</text>
  <text x="570" y="1150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"aligned" → aligning (Boolean)</text>
  <text x="570" y="1165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"dist" → dist (Float)</text>
  <text x="570" y="1180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"countdown" → countdown (Integer)</text>
  <text x="570" y="1195" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"left_x", "left_y" → left_pupil</text>
  <text x="570" y="1210" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">"right_x", "right_y" → right_pupil</text>

  <!-- Kotlin数据类转换 -->
  <rect x="740" y="1100" width="300" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="890" y="1120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">Kotlin数据类转换</text>
  <text x="890" y="1135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">PostureCalibrationResult().apply {</text>
  <text x="890" y="1150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">  state = result["state"] as Boolean</text>
  <text x="890" y="1165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">  aligned = result["aligned"] as Boolean</text>
  <text x="890" y="1180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">  dist = result["dist"] as Float</text>
  <text x="890" y="1195" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">  countdown = result["countdown"] as Int</text>
  <text x="890" y="1210" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">  leftX, leftY, rightX, rightY }</text>

  <!-- UI层应用 -->
  <rect x="1060" y="1100" width="300" height="120" rx="5" fill="#fff" stroke="#4caf50" stroke-width="1"/>
  <text x="1210" y="1120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4caf50">UI层应用</text>
  <text x="1210" y="1135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">头像缩放: scale = 0.5 / dist</text>
  <text x="1210" y="1150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">颜色反馈: 根据dist值变化</text>
  <text x="1210" y="1165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">文字提示: 距离状态提示</text>
  <text x="1210" y="1180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">倒计时显示: countdown值</text>
  <text x="1210" y="1195" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">瞳孔位置: leftX*width, leftY*height</text>
  <text x="1210" y="1210" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">实时位置反馈</text>

  <!-- 数据流向箭头 -->
  <line x1="800" y1="160" x2="800" y2="200" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="320" x2="800" y2="360" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="560" x2="800" y2="600" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="820" x2="800" y2="860" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="1010" x2="800" y2="1050" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 检测流程内部箭头 -->
  <line x1="380" y1="470" x2="400" y2="470" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="680" y1="470" x2="700" y2="470" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="980" y1="470" x2="1000" y2="470" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 姿势校准算法内部箭头 -->
  <line x1="320" y1="700" x2="340" y2="700" stroke="#ffc107" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="560" y1="700" x2="580" y2="700" stroke="#ffc107" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="700" x2="820" y2="700" stroke="#ffc107" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1040" y1="700" x2="1060" y2="700" stroke="#ffc107" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1280" y1="700" x2="1300" y2="700" stroke="#ffc107" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 输出组装内部箭头 -->
  <line x1="400" y1="1160" x2="420" y2="1160" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="720" y1="1160" x2="740" y2="1160" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1040" y1="1160" x2="1060" y2="1160" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 性能指标 -->
  <rect x="1200" y="1300" width="350" height="80" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="1375" y="1320" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ff9800">性能指标与技术特点</text>
  <text x="1375" y="1335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 入参: 字节数组(Y通道灰度数据) + 宽高参数</text>
  <text x="1375" y="1350" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 三级检测: 人脸→眼部→瞳孔 (RKNN模型推理)</text>
  <text x="1375" y="1365" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 距离精度: ±2cm (Sigmoid函数映射)</text>
  <text x="1375" y="1380" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">• 处理延迟: <33ms/帧 (30fps实时处理)</text>
</svg>