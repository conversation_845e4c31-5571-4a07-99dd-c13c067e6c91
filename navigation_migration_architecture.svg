<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #1a1a2e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #16213e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #0f3460; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #533483; }
      .old-bg { fill: #ff6b6b; stroke: #ee5a52; stroke-width: 2; rx: 8; }
      .new-bg { fill: #4ecdc4; stroke: #45b7aa; stroke-width: 2; rx: 8; }
      .activity-bg { fill: #45b7d1; stroke: #3a9bc1; stroke-width: 2; rx: 8; }
      .fragment-bg { fill: #f9ca24; stroke: #f0b90b; stroke-width: 2; rx: 8; }
      .navigation-bg { fill: #a55eea; stroke: #8b5cf6; stroke-width: 2; rx: 8; }
      .arrow { stroke: #1a1a2e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .migration-arrow { stroke: #ff6b6b; stroke-width: 3; fill: none; marker-end: url(#migration-arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1a1a2e" />
    </marker>
    <marker id="migration-arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#ff6b6b" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">导航架构迁移：从Activity+Fragment到Navigation组件</text>
  
  <!-- 当前架构 (左侧) -->
  <text x="300" y="70" text-anchor="middle" class="subtitle">当前导航架构 (传统方式)</text>
  
  <!-- Activity层级 -->
  <rect x="50" y="90" width="500" height="200" class="old-bg"/>
  <text x="300" y="115" text-anchor="middle" class="subtitle" fill="white">Activity层级导航</text>
  
  <!-- LauncherActivity -->
  <rect x="70" y="130" width="140" height="60" class="activity-bg"/>
  <text x="140" y="150" text-anchor="middle" class="text" fill="white">LauncherActivity</text>
  <text x="80" y="170" class="small-text" fill="white">启动页选择模式</text>
  
  <!-- HomeMainActivity -->
  <rect x="230" y="130" width="140" height="60" class="activity-bg"/>
  <text x="300" y="150" text-anchor="middle" class="text" fill="white">HomeMainActivity</text>
  <text x="240" y="170" class="small-text" fill="white">家庭版主页</text>
  
  <!-- ReadActivity -->
  <rect x="390" y="130" width="140" height="60" class="activity-bg"/>
  <text x="460" y="150" text-anchor="middle" class="text" fill="white">ReadActivity</text>
  <text x="400" y="170" class="small-text" fill="white">阅读评估</text>
  
  <!-- Intent跳转 -->
  <text x="70" y="210" class="text" fill="white">Intent跳转方式:</text>
  <text x="70" y="225" class="small-text" fill="white">• startActivity(Intent)</text>
  <text x="70" y="240" class="small-text" fill="white">• createIntent(context, params)</text>
  <text x="70" y="255" class="small-text" fill="white">• finish()关闭当前Activity</text>
  <text x="70" y="270" class="small-text" fill="white">• 手动管理Activity栈</text>

  <!-- Fragment管理 -->
  <rect x="50" y="310" width="500" height="180" class="old-bg"/>
  <text x="300" y="335" text-anchor="middle" class="subtitle" fill="white">Fragment手动管理</text>
  
  <!-- FragmentManager -->
  <rect x="70" y="350" width="200" height="80" class="fragment-bg"/>
  <text x="170" y="375" text-anchor="middle" class="text" fill="white">FragmentManager</text>
  <text x="80" y="395" class="small-text" fill="white">supportFragmentManager</text>
  <text x="80" y="410" class="small-text" fill="white">childFragmentManager</text>
  <text x="80" y="425" class="small-text" fill="white">手动事务管理</text>

  <!-- FragmentTransaction -->
  <rect x="290" y="350" width="240" height="80" class="fragment-bg"/>
  <text x="410" y="375" text-anchor="middle" class="text" fill="white">FragmentTransaction</text>
  <text x="300" y="395" class="small-text" fill="white">beginTransaction()</text>
  <text x="300" y="410" class="small-text" fill="white">replace(R.id.container, fragment)</text>
  <text x="300" y="425" class="small-text" fill="white">commitAllowingStateLoss()</text>

  <!-- 问题列表 -->
  <text x="70" y="450" class="text" fill="white">存在问题:</text>
  <text x="70" y="465" class="small-text" fill="white">• 手动管理Fragment生命周期复杂</text>
  <text x="70" y="480" class="small-text" fill="white">• 回退栈管理困难，容易出现状态丢失</text>

  <!-- 迁移箭头 -->
  <path d="M 600 300 Q 700 250 800 300" class="migration-arrow"/>
  <text x="700" y="240" text-anchor="middle" class="subtitle" fill="#e74c3c">迁移到Navigation</text>

  <!-- Navigation架构 (右侧) -->
  <text x="1200" y="70" text-anchor="middle" class="subtitle">Navigation组件架构</text>

  <!-- Navigation Graph -->
  <rect x="850" y="90" width="700" height="200" class="new-bg"/>
  <text x="1200" y="115" text-anchor="middle" class="subtitle" fill="white">Navigation Graph (nav_graph.xml)</text>
  
  <!-- 目的地定义 -->
  <rect x="870" y="130" width="200" height="80" class="navigation-bg"/>
  <text x="970" y="155" text-anchor="middle" class="text" fill="white">目的地定义</text>
  <text x="880" y="175" class="small-text" fill="white">&lt;fragment&gt;</text>
  <text x="880" y="190" class="small-text" fill="white">android:id="@+id/homeFragment"</text>
  <text x="880" y="205" class="small-text" fill="white">android:name="...HomeFragment"</text>

  <!-- 动作定义 -->
  <rect x="1090" y="130" width="200" height="80" class="navigation-bg"/>
  <text x="1190" y="155" text-anchor="middle" class="text" fill="white">动作定义</text>
  <text x="1100" y="175" class="small-text" fill="white">&lt;action&gt;</text>
  <text x="1100" y="190" class="small-text" fill="white">android:id="@+id/action_to_read"</text>
  <text x="1100" y="205" class="small-text" fill="white">app:destination="@+id/readFragment"</text>

  <!-- 参数传递 -->
  <rect x="1310" y="130" width="220" height="80" class="navigation-bg"/>
  <text x="1420" y="155" text-anchor="middle" class="text" fill="white">参数传递</text>
  <text x="1320" y="175" class="small-text" fill="white">&lt;argument&gt;</text>
  <text x="1320" y="190" class="small-text" fill="white">android:name="identity"</text>
  <text x="1320" y="205" class="small-text" fill="white">app:argType="string"</text>

  <!-- 优势说明 -->
  <text x="870" y="235" class="text" fill="white">声明式导航定义，可视化编辑，类型安全的参数传递</text>
  <text x="870" y="250" class="text" fill="white">自动处理回退栈，支持深度链接，动画过渡效果</text>
  <text x="870" y="265" class="text" fill="white">编译时检查，减少运行时错误，统一的导航管理</text>

  <!-- NavController -->
  <rect x="850" y="310" width="700" height="120" class="new-bg"/>
  <text x="1200" y="335" text-anchor="middle" class="subtitle" fill="white">NavController导航控制</text>
  
  <rect x="870" y="350" width="200" height="60" class="navigation-bg"/>
  <text x="970" y="375" text-anchor="middle" class="text" fill="white">导航操作</text>
  <text x="880" y="395" class="small-text" fill="white">findNavController().navigate()</text>

  <rect x="1090" y="350" width="200" height="60" class="navigation-bg"/>
  <text x="1190" y="375" text-anchor="middle" class="text" fill="white">回退操作</text>
  <text x="1100" y="395" class="small-text" fill="white">navController.popBackStack()</text>

  <rect x="1310" y="350" width="220" height="60" class="navigation-bg"/>
  <text x="1420" y="375" text-anchor="middle" class="text" fill="white">深度链接</text>
  <text x="1320" y="395" class="small-text" fill="white">navController.navigate(deepLink)</text>

  <!-- 具体迁移示例 -->
  <rect x="50" y="520" width="1500" height="300" class="navigation-bg"/>
  <text x="800" y="545" text-anchor="middle" class="subtitle" fill="white">具体迁移示例</text>

  <!-- 迁移前 -->
  <text x="70" y="570" class="subtitle" fill="white">迁移前 (传统方式):</text>
  
  <rect x="70" y="580" width="700" height="120" class="old-bg"/>
  <text x="80" y="600" class="text" fill="white">// Activity跳转</text>
  <text x="80" y="615" class="text" fill="white">startActivity(ReadActivity.createIntent(this, identity, grade))</text>
  <text x="80" y="630" class="text" fill="white">finish()</text>
  <text x="80" y="650" class="text" fill="white">// Fragment替换</text>
  <text x="80" y="665" class="text" fill="white">supportFragmentManager.beginTransaction()</text>
  <text x="80" y="680" class="text" fill="white">    .replace(R.id.container, ReadInitFragment.newInstance())</text>
  <text x="80" y="695" class="text" fill="white">    .commitAllowingStateLoss()</text>

  <!-- 迁移后 -->
  <text x="820" y="570" class="subtitle" fill="white">迁移后 (Navigation组件):</text>
  
  <rect x="820" y="580" width="700" height="120" class="new-bg"/>
  <text x="830" y="600" class="text" fill="white">// 导航到目的地</text>
  <text x="830" y="615" class="text" fill="white">val bundle = bundleOf("identity" to identity, "grade" to grade)</text>
  <text x="830" y="630" class="text" fill="white">findNavController().navigate(R.id.action_to_read, bundle)</text>
  <text x="830" y="650" class="text" fill="white">// 或使用Safe Args</text>
  <text x="830" y="665" class="text" fill="white">val directions = HomeFragmentDirections</text>
  <text x="830" y="680" class="text" fill="white">    .actionToRead(identity, grade)</text>
  <text x="830" y="695" class="text" fill="white">findNavController().navigate(directions)</text>

  <!-- 项目结构重构 -->
  <rect x="70" y="720" width="700" height="80" class="old-bg"/>
  <text x="420" y="745" text-anchor="middle" class="subtitle" fill="white">当前项目结构问题</text>
  <text x="80" y="765" class="text" fill="white">• 多个独立Activity: LauncherActivity → HomeMainActivity → ReadActivity</text>
  <text x="80" y="780" class="text" fill="white">• Fragment嵌套复杂: HomeMainFragment → TreatmentModuleAdapter → MaskTherapyFragment</text>
  <text x="80" y="795" class="text" fill="white">• 手动管理生命周期和状态传递</text>

  <rect x="820" y="720" width="700" height="80" class="new-bg"/>
  <text x="1170" y="745" text-anchor="middle" class="subtitle" fill="white">Navigation重构方案</text>
  <text x="830" y="765" class="text" fill="white">• 单Activity架构: MainActivity + NavHostFragment</text>
  <text x="830" y="780" class="text" fill="white">• Fragment作为目的地: HomeFragment, ReadFragment, MaskTherapyFragment</text>
  <text x="830" y="795" class="text" fill="white">• 自动管理回退栈和状态保存</text>

  <!-- 实施步骤 -->
  <rect x="50" y="840" width="1500" height="200" class="activity-bg"/>
  <text x="800" y="865" text-anchor="middle" class="subtitle" fill="white">Navigation迁移实施步骤</text>
  
  <text x="70" y="890" class="text" fill="white">1️⃣ <tspan style="font-weight: bold;">添加Navigation依赖：</tspan>implementation "androidx.navigation:navigation-fragment-ktx"</text>
  <text x="70" y="910" class="text" fill="white">2️⃣ <tspan style="font-weight: bold;">创建导航图：</tspan>res/navigation/nav_graph.xml，定义所有Fragment目的地</text>
  <text x="70" y="930" class="text" fill="white">3️⃣ <tspan style="font-weight: bold;">重构MainActivity：</tspan>使用NavHostFragment替代手动Fragment管理</text>
  <text x="70" y="950" class="text" fill="white">4️⃣ <tspan style="font-weight: bold;">迁移Fragment：</tspan>移除newInstance()，使用Safe Args传递参数</text>
  <text x="70" y="970" class="text" fill="white">5️⃣ <tspan style="font-weight: bold;">替换导航调用：</tspan>startActivity() → findNavController().navigate()</text>
  <text x="70" y="990" class="text" fill="white">6️⃣ <tspan style="font-weight: bold;">处理回退逻辑：</tspan>使用NavController的回退栈管理</text>
  <text x="70" y="1010" class="text" fill="white">7️⃣ <tspan style="font-weight: bold;">测试验证：</tspan>确保所有导航路径正常，状态保存恢复正确</text>

  <!-- 技术细节 -->
  <rect x="50" y="1060" width="1500" height="160" class="fragment-bg"/>
  <text x="800" y="1085" text-anchor="middle" class="subtitle" fill="white">技术实现细节</text>
  
  <text x="70" y="1110" class="text" fill="white">📦 <tspan style="font-weight: bold;">依赖配置：</tspan>navigation-fragment-ktx, navigation-ui-ktx, navigation-safe-args-gradle-plugin</text>
  <text x="70" y="1130" class="text" fill="white">🎯 <tspan style="font-weight: bold;">Safe Args：</tspan>编译时生成类型安全的参数传递代码，避免Bundle手动操作</text>
  <text x="70" y="1150" class="text" fill="white">🔄 <tspan style="font-weight: bold;">深度链接：</tspan>支持URL导航，可以直接跳转到应用内任意页面</text>
  <text x="70" y="1170" class="text" fill="white">🎨 <tspan style="font-weight: bold;">动画过渡：</tspan>在导航图中定义进入/退出动画，提升用户体验</text>
  <text x="70" y="1190" class="text" fill="white">🧪 <tspan style="font-weight: bold;">测试支持：</tspan>NavigationTestRule简化导航相关的UI测试</text>
  <text x="70" y="1210" class="text" fill="white">📱 <tspan style="font-weight: bold;">单Activity架构：</tspan>减少Activity切换开销，统一状态管理，更好的性能</text>

  <!-- 迁移优势 -->
  <rect x="50" y="1240" width="1500" height="120" class="new-bg"/>
  <text x="800" y="1265" text-anchor="middle" class="subtitle" fill="white">Navigation组件优势</text>
  
  <text x="70" y="1290" class="text" fill="white">✅ <tspan style="font-weight: bold;">类型安全：</tspan>Safe Args插件生成类型安全的参数传递代码</text>
  <text x="70" y="1310" class="text" fill="white">✅ <tspan style="font-weight: bold;">可视化编辑：</tspan>Android Studio提供图形化导航编辑器</text>
  <text x="70" y="1330" class="text" fill="white">✅ <tspan style="font-weight: bold;">自动管理：</tspan>回退栈、状态保存、生命周期自动处理</text>
  <text x="70" y="1350" class="text" fill="white">✅ <tspan style="font-weight: bold;">统一API：</tspan>一致的导航API，减少样板代码，提高开发效率</text>

  <!-- 连接线 -->
  <path d="M 300 290 L 300 310" class="arrow"/>
  <path d="M 1200 290 L 1200 310" class="arrow"/>
  <path d="M 170 430 L 170 450" class="arrow"/>
  <path d="M 410 430 L 410 450" class="arrow"/>
  <path d="M 970 210 L 970 230" class="arrow"/>
  <path d="M 1190 210 L 1190 230" class="arrow"/>
  <path d="M 1420 210 L 1420 230" class="arrow"/>

</svg>
