<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #e74c3c; }
      .layer-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .description { font-family: Arial, sans-serif; font-size: 12px; fill: #34495e; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #27ae60; }
      .step-box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .ui-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .business-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .data-box { fill: #fef9e7; stroke: #f39c12; stroke-width: 2; }
      .native-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; }
      .arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#smallarrow); }
      .highlight { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
    <marker id="smallarrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" class="title">基于现有封装开发新功能指南</text>
  <text x="600" y="70" text-anchor="middle" class="subtitle">以"姿势检测与提醒"功能为例</text>

  <!-- 步骤1: 需求分析 -->
  <rect x="50" y="100" width="1100" height="120" class="step-box" rx="10"/>
  <text x="70" y="125" class="step-title">步骤1: 需求分析与设计</text>
  <text x="70" y="150" class="description">• 功能需求: 实时检测用户坐姿，当检测到不良姿势时进行提醒</text>
  <text x="70" y="170" class="description">• 技术需求: 利用现有摄像头和人脸检测算法，分析用户头部位置和角度</text>
  <text x="70" y="190" class="description">• 架构设计: 遵循现有分层架构，UI层展示姿势状态，业务层处理姿势逻辑，原生层进行姿势检测</text>
  <text x="70" y="210" class="description">• 接口设计: 定义姿势检测回调接口，设计姿势状态枚举，规划数据传递格式</text>

  <!-- 步骤2: UI层开发 -->
  <rect x="50" y="240" width="1100" height="280" class="ui-box" rx="10"/>
  <text x="70" y="265" class="step-title">步骤2: UI层开发</text>
  
  <rect x="80" y="280" width="500" height="220" class="step-box" rx="5"/>
  <text x="90" y="300" class="module-title">2.1 创建Activity和Fragment</text>
  <text x="90" y="325" class="code">// PostureDetectionActivity.kt
class PostureDetectionActivity : GTBaseActivity() {
    
    private val viewModel by lazy { PostureDetectionViewModel() }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_posture_detection)
        
        initViews()
        observeViewModel()
    }
    
    private fun observeViewModel() {
        viewModel.postureStatus.observe(this) { status ->
            updatePostureStatus(status)
        }
    }
}</text>

  <rect x="600" y="280" width="520" height="220" class="step-box" rx="5"/>
  <text x="610" y="300" class="module-title">2.2 创建自定义View</text>
  <text x="610" y="325" class="code">// PostureStatusView.kt
class PostureStatusView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var postureStatus = PostureStatus.NORMAL
    
    fun updateStatus(status: PostureStatus) {
        this.postureStatus = status
        invalidate()
    }
    
    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        // 绘制姿势状态指示器
        drawPostureIndicator(canvas)
    }
}</text>

  <!-- 步骤3: 业务逻辑层开发 -->
  <rect x="50" y="540" width="1100" height="320" class="business-box" rx="10"/>
  <text x="70" y="565" class="step-title">步骤3: 业务逻辑层开发</text>

  <rect x="80" y="580" width="500" height="120" class="step-box" rx="5"/>
  <text x="90" y="600" class="module-title">3.1 ViewModel</text>
  <text x="90" y="625" class="code">// PostureDetectionViewModel.kt
class PostureDetectionViewModel : ViewModel() {
    
    private val postureManager = PostureDetectionManager.getInstance()
    
    private val _postureStatus = MutableLiveData<PostureStatus>()
    val postureStatus: LiveData<PostureStatus> = _postureStatus
    
    init {
        postureManager.setPostureListener(object : IPostureListener {
            override fun onPostureChanged(status: PostureStatus) {
                _postureStatus.postValue(status)
            }
        })
    }
    
    fun startPostureDetection() {
        postureManager.startDetection()
    }
    
    fun stopPostureDetection() {
        postureManager.stopDetection()
    }
}</text>

  <rect x="600" y="580" width="520" height="120" class="step-box" rx="5"/>
  <text x="610" y="600" class="module-title">3.2 Manager</text>
  <text x="610" y="625" class="code">// PostureDetectionManager.kt
class PostureDetectionManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: PostureDetectionManager? = null
        
        fun getInstance(): PostureDetectionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PostureDetectionManager().also { INSTANCE = it }
            }
        }
    }
    
    private var postureListener: IPostureListener? = null
    private var isDetecting = false
    
    // 集成现有的GazeTrackingManager
    private val gazeTrackingManager = GazeTrackingManager.getInstance()
    
    fun setPostureListener(listener: IPostureListener) {
        this.postureListener = listener
    }
    
    fun startDetection() {
        if (!isDetecting) {
            isDetecting = true
            // 启动姿势检测
            PostureDetectionService.start()
        }
    }
}</text>

  <rect x="80" y="720" width="1040" height="120" class="step-box" rx="5"/>
  <text x="90" y="740" class="module-title">3.3 Service和接口定义</text>
  <text x="90" y="765" class="code">// PostureDetectionService.kt
class PostureDetectionService : Service() {
    
    companion object {
        fun start() {
            // 启动服务逻辑
        }
        
        fun stop() {
            // 停止服务逻辑
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startPostureDetection()
        return START_STICKY
    }
    
    private fun startPostureDetection() {
        // 调用原生层进行姿势检测
        PostureDetection.startDetection(object : PostureCallback {
            override fun onPostureDetected(status: Int) {
                // 处理检测结果
                val postureStatus = PostureStatus.fromValue(status)
                PostureDetectionManager.getInstance().notifyPostureChanged(postureStatus)
            }
        })
    }
}</text>

  <!-- 步骤4: 数据层开发 -->
  <rect x="50" y="880" width="1100" height="200" class="data-box" rx="10"/>
  <text x="70" y="905" class="step-title">步骤4: 数据层开发</text>

  <rect x="80" y="920" width="500" height="140" class="step-box" rx="5"/>
  <text x="90" y="940" class="module-title">4.1 数据模型</text>
  <text x="90" y="965" class="code">// PostureStatus.kt
enum class PostureStatus(val value: Int) {
    NORMAL(0),       // 正常姿势
    TOO_CLOSE(1),    // 距离屏幕太近
    TOO_FAR(2),      // 距离屏幕太远
    HEAD_TILT(3),    // 头部倾斜
    INCORRECT_ANGLE(4); // 不正确的角度
    
    companion object {
        fun fromValue(value: Int): PostureStatus {
            return values().find { it.value == value } ?: NORMAL
        }
    }
}

// IPostureListener.kt
interface IPostureListener {
    fun onPostureChanged(status: PostureStatus)
}</text>

  <rect x="600" y="920" width="520" height="140" class="step-box" rx="5"/>
  <text x="610" y="940" class="module-title">4.2 配置存储</text>
  <text x="610" y="965" class="code">// PosturePreference.kt
class PosturePreference {
    
    private val mmkv = MMKV.defaultMMKV()
    
    fun saveSettings(settings: PostureSettings) {
        mmkv.encode(KEY_POSTURE_SENSITIVITY, settings.sensitivity)
        mmkv.encode(KEY_POSTURE_ALERT_INTERVAL, settings.alertInterval)
        mmkv.encode(KEY_POSTURE_ENABLED, settings.enabled)
    }
    
    fun getSettings(): PostureSettings {
        val sensitivity = mmkv.decodeInt(KEY_POSTURE_SENSITIVITY, 5)
        val alertInterval = mmkv.decodeInt(KEY_POSTURE_ALERT_INTERVAL, 30)
        val enabled = mmkv.decodeBool(KEY_POSTURE_ENABLED, true)
        
        return PostureSettings(sensitivity, alertInterval, enabled)
    }
    
    companion object {
        private const val KEY_POSTURE_SENSITIVITY = "posture_sensitivity"
        private const val KEY_POSTURE_ALERT_INTERVAL = "posture_alert_interval"
        private const val KEY_POSTURE_ENABLED = "posture_enabled"
    }
}</text>

  <!-- 步骤5: 原生层开发 -->
  <rect x="50" y="1100" width="1100" height="240" class="native-box" rx="10"/>
  <text x="70" y="1125" class="step-title">步骤5: 原生层开发</text>

  <rect x="80" y="1140" width="500" height="180" class="step-box" rx="5"/>
  <text x="90" y="1160" class="module-title">5.1 JNI接口</text>
  <text x="90" y="1185" class="code">// PostureDetection.kt
object PostureDetection {
    
    init {
        System.loadLibrary("posture_detection")
    }
    
    // JNI方法声明
    external fun startDetection(callback: PostureCallback): Boolean
    external fun stopDetection(): Boolean
    external fun setSensitivity(sensitivity: Int): Boolean
    
    // 回调接口
    interface PostureCallback {
        fun onPostureDetected(status: Int)
    }
}</text>

  <rect x="600" y="1140" width="520" height="180" class="step-box" rx="5"/>
  <text x="610" y="1160" class="module-title">5.2 C++实现</text>
  <text x="610" y="1185" class="code">// posture_detection.cpp
#include <jni.h>
#include "posture_detection.h"
#include "face_detector.h"

// 全局引用，保存回调对象
static jobject gCallbackObject = nullptr;
static jmethodID gOnPostureDetected = nullptr;

// JNI方法实现
extern "C" JNIEXPORT jboolean JNICALL
Java_com_mitdd_gazetracker_posture_PostureDetection_startDetection(
        JNIEnv *env, jobject thiz, jobject callback) {
    
    // 保存回调对象的全局引用
    if (gCallbackObject != nullptr) {
        env->DeleteGlobalRef(gCallbackObject);
    }
    gCallbackObject = env->NewGlobalRef(callback);
    
    // 获取回调方法ID
    jclass callbackClass = env->GetObjectClass(gCallbackObject);
    gOnPostureDetected = env->GetMethodID(callbackClass, 
                                         "onPostureDetected", 
                                         "(I)V");
    
    // 启动姿势检测线程
    startPostureDetectionThread();
    
    return JNI_TRUE;
}</text>

  <!-- 步骤6: 集成与测试 -->
  <rect x="50" y="1360" width="1100" height="180" class="step-box" rx="10"/>
  <text x="70" y="1385" class="step-title">步骤6: 集成与测试</text>

  <rect x="80" y="1400" width="500" height="120" class="step-box" rx="5"/>
  <text x="90" y="1420" class="module-title">6.1 AndroidManifest.xml配置</text>
  <text x="90" y="1445" class="code"><!-- AndroidManifest.xml -->
<activity
    android:name=".posture.PostureDetectionActivity"
    android:screenOrientation="landscape"
    android:theme="@style/AppTheme" />

<service
    android:name=".posture.service.PostureDetectionService"
    android:enabled="true"
    android:exported="false" /></text>

  <rect x="600" y="1400" width="520" height="120" class="step-box" rx="5"/>
  <text x="610" y="1420" class="module-title">6.2 集成到主导航</text>
  <text x="610" y="1445" class="code">// 在HomeMainFragment中添加姿势检测入口
private fun initPostureDetection() {
    val postureDetectionItem = ModuleItem(
        moduleName = "姿势检测",
        moduleKey = "POSTURE_DETECTION",
        moduleIcon = R.drawable.ic_posture_detection
    )
    
    // 添加到模块列表
    moduleList.add(postureDetectionItem)
}</text>

  <!-- 开发流程箭头 -->
  <line x1="600" y1="220" x2="600" y2="240" class="arrow"/>
  <line x1="600" y1="520" x2="600" y2="540" class="arrow"/>
  <line x1="600" y1="860" x2="600" y2="880" class="arrow"/>
  <line x1="600" y1="1080" x2="600" y2="1100" class="arrow"/>
  <line x1="600" y1="1340" x2="600" y2="1360" class="arrow"/>

  <!-- 关键注意事项 -->
  <rect x="50" y="1560" width="1100" height="20" class="highlight" rx="10"/>
  <text x="600" y="1575" text-anchor="middle" class="description">遵循现有架构模式 • 使用现有基类 • 完善错误处理 • 性能优化考虑 • 向后兼容保证 • 充分测试验证</text>
</svg>