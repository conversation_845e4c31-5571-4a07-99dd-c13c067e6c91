<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .subtitle { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #34495e; }
      .description { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .step-box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .ui-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .business-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .data-box { fill: #fef9e7; stroke: #f39c12; stroke-width: 2; }
      .native-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; }
      .arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#smallarrow); }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
    <marker id="smallarrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">新增模块开发指南 - 以"语音识别模块"为例</text>

  <!-- 步骤1: 需求分析 -->
  <rect x="50" y="60" width="1100" height="100" class="step-box" rx="10"/>
  <text x="70" y="85" class="step-title">步骤1: 需求分析与设计</text>
  <text x="70" y="105" class="description">• 功能需求: 实现语音指令控制眼动追踪设备，支持"开始校准"、"停止追踪"等语音命令</text>
  <text x="70" y="120" class="description">• 技术需求: 集成语音识别SDK，实现实时语音处理，与现有眼动追踪系统集成</text>
  <text x="70" y="135" class="description">• 架构设计: 遵循现有分层架构，UI层展示语音状态，业务层处理语音逻辑，原生层进行语音处理</text>
  <text x="70" y="150" class="description">• 接口设计: 定义语音识别回调接口，设计语音命令枚举，规划数据传递格式</text>

  <!-- 步骤2: UI层开发 -->
  <rect x="50" y="180" width="1100" height="160" class="ui-box" rx="10"/>
  <text x="70" y="205" class="step-title">步骤2: UI层开发</text>
  
  <rect x="80" y="220" width="250" height="100" class="step-box" rx="5"/>
  <text x="90" y="240" class="subtitle">2.1 创建Activity</text>
  <text x="90" y="255" class="code">VoiceControlActivity.kt</text>
  <text x="90" y="270" class="description">• 继承GTBaseActivity</text>
  <text x="90" y="285" class="description">• 实现语音状态UI展示</text>
  <text x="90" y="300" class="description">• 添加语音开关控制</text>
  <text x="90" y="315" class="description">• 显示识别结果</text>

  <rect x="350" y="220" width="250" height="100" class="step-box" rx="5"/>
  <text x="360" y="240" class="subtitle">2.2 创建Fragment</text>
  <text x="360" y="255" class="code">VoiceControlFragment.kt</text>
  <text x="360" y="270" class="description">• 语音设置界面</text>
  <text x="360" y="285" class="description">• 语音命令列表</text>
  <text x="360" y="300" class="description">• 语音识别状态显示</text>

  <rect x="620" y="220" width="250" height="100" class="step-box" rx="5"/>
  <text x="630" y="240" class="subtitle">2.3 自定义View</text>
  <text x="630" y="255" class="code">VoiceWaveView.kt</text>
  <text x="630" y="270" class="description">• 语音波形显示</text>
  <text x="630" y="285" class="description">• 实时音量可视化</text>
  <text x="630" y="300" class="description">• 语音状态指示器</text>

  <rect x="890" y="220" width="250" height="100" class="step-box" rx="5"/>
  <text x="900" y="240" class="subtitle">2.4 布局文件</text>
  <text x="900" y="255" class="code">activity_voice_control.xml</text>
  <text x="900" y="270" class="code">fragment_voice_settings.xml</text>
  <text x="900" y="285" class="code">view_voice_wave.xml</text>
  <text x="900" y="300" class="description">• 遵循现有设计规范</text>

  <!-- 步骤3: 业务逻辑层开发 -->
  <rect x="50" y="360" width="1100" height="180" class="business-box" rx="10"/>
  <text x="70" y="385" class="step-title">步骤3: 业务逻辑层开发</text>

  <rect x="80" y="400" width="200" height="120" class="step-box" rx="5"/>
  <text x="90" y="420" class="subtitle">3.1 ViewModel</text>
  <text x="90" y="435" class="code">VoiceControlViewModel.kt</text>
  <text x="90" y="450" class="description">• 语音状态管理</text>
  <text x="90" y="465" class="description">• 命令识别结果处理</text>
  <text x="90" y="480" class="description">• 与UI层数据绑定</text>
  <text x="90" y="495" class="description">• LiveData状态更新</text>
  <text x="90" y="510" class="description">• 错误处理</text>

  <rect x="300" y="400" width="200" height="120" class="step-box" rx="5"/>
  <text x="310" y="420" class="subtitle">3.2 Manager</text>
  <text x="310" y="435" class="code">VoiceControlManager.kt</text>
  <text x="310" y="450" class="description">• 语音识别生命周期管理</text>
  <text x="310" y="465" class="description">• 语音命令解析</text>
  <text x="310" y="480" class="description">• 与GazeTrackingManager集成</text>
  <text x="310" y="495" class="description">• 权限管理</text>
  <text x="310" y="510" class="description">• 单例模式实现</text>

  <rect x="520" y="400" width="200" height="120" class="step-box" rx="5"/>
  <text x="530" y="420" class="subtitle">3.3 Service</text>
  <text x="530" y="435" class="code">VoiceRecognitionService.kt</text>
  <text x="530" y="450" class="description">• 后台语音监听</text>
  <text x="530" y="465" class="description">• 实时语音处理</text>
  <text x="530" y="480" class="description">• 与原生层交互</text>
  <text x="530" y="495" class="description">• 生命周期管理</text>

  <rect x="740" y="400" width="200" height="120" class="step-box" rx="5"/>
  <text x="750" y="420" class="subtitle">3.4 Repository</text>
  <text x="750" y="435" class="code">VoiceControlRepository.kt</text>
  <text x="750" y="450" class="description">• 语音配置数据管理</text>
  <text x="750" y="465" class="description">• 命令模板存储</text>
  <text x="750" y="480" class="description">• 用户语音偏好</text>
  <text x="750" y="495" class="description">• 本地缓存管理</text>

  <rect x="960" y="400" width="180" height="120" class="step-box" rx="5"/>
  <text x="970" y="420" class="subtitle">3.5 枚举与常量</text>
  <text x="970" y="435" class="code">VoiceCommand.kt</text>
  <text x="970" y="450" class="code">VoiceConstants.kt</text>
  <text x="970" y="465" class="description">• 语音命令枚举</text>
  <text x="970" y="480" class="description">• 识别状态定义</text>
  <text x="970" y="495" class="description">• 配置常量</text>

  <!-- 步骤4: 数据层开发 -->
  <rect x="50" y="560" width="1100" height="140" class="data-box" rx="10"/>
  <text x="70" y="585" class="step-title">步骤4: 数据层开发</text>

  <rect x="80" y="600" width="220" height="80" class="step-box" rx="5"/>
  <text x="90" y="620" class="subtitle">4.1 本地存储</text>
  <text x="90" y="635" class="code">VoicePreference.kt</text>
  <text x="90" y="650" class="description">• MMKV存储语音设置</text>
  <text x="90" y="665" class="description">• 用户语音偏好保存</text>

  <rect x="320" y="600" width="220" height="80" class="step-box" rx="5"/>
  <text x="330" y="620" class="subtitle">4.2 网络接口</text>
  <text x="330" y="635" class="code">VoiceApiService.kt</text>
  <text x="330" y="650" class="description">• 语音模型下载接口</text>
  <text x="330" y="665" class="description">• 语音数据上传接口</text>

  <rect x="560" y="600" width="220" height="80" class="step-box" rx="5"/>
  <text x="570" y="620" class="subtitle">4.3 数据模型</text>
  <text x="570" y="635" class="code">VoiceResult.kt</text>
  <text x="570" y="650" class="code">VoiceConfig.kt</text>
  <text x="570" y="665" class="description">• 语音识别结果数据类</text>

  <rect x="800" y="600" width="220" height="80" class="step-box" rx="5"/>
  <text x="810" y="620" class="subtitle">4.4 权限管理</text>
  <text x="810" y="635" class="description">• 录音权限检查</text>
  <text x="810" y="650" class="description">• 动态权限申请</text>
  <text x="810" y="665" class="description">• 权限状态监听</text>

  <!-- 步骤5: 原生层开发 -->
  <rect x="50" y="720" width="1100" height="160" class="native-box" rx="10"/>
  <text x="70" y="745" class="step-title">步骤5: 原生层开发 (可选)</text>

  <rect x="80" y="760" width="250" height="100" class="step-box" rx="5"/>
  <text x="90" y="780" class="subtitle">5.1 JNI接口</text>
  <text x="90" y="795" class="code">VoiceRecognition.kt</text>
  <text x="90" y="810" class="description">• external fun声明</text>
  <text x="90" y="825" class="description">• System.loadLibrary</text>
  <text x="90" y="840" class="description">• 回调接口定义</text>
  <text x="90" y="855" class="description">• 生命周期管理</text>

  <rect x="350" y="760" width="250" height="100" class="step-box" rx="5"/>
  <text x="360" y="780" class="subtitle">5.2 C++实现</text>
  <text x="360" y="795" class="code">voice_recognition.cpp</text>
  <text x="360" y="810" class="code">voice_recognition.h</text>
  <text x="360" y="825" class="description">• JNIEXPORT函数实现</text>
  <text x="360" y="840" class="description">• 语音处理算法</text>
  <text x="360" y="855" class="description">• 第三方SDK集成</text>

  <rect x="620" y="760" width="250" height="100" class="step-box" rx="5"/>
  <text x="630" y="780" class="subtitle">5.3 CMakeLists配置</text>
  <text x="630" y="795" class="description">• 添加源文件</text>
  <text x="630" y="810" class="description">• 链接第三方库</text>
  <text x="630" y="825" class="description">• 编译选项配置</text>
  <text x="630" y="840" class="description">• 架构适配</text>

  <rect x="890" y="760" width="250" height="100" class="step-box" rx="5"/>
  <text x="900" y="780" class="subtitle">5.4 SO库管理</text>
  <text x="900" y="795" class="description">• jniLibs目录结构</text>
  <text x="900" y="810" class="description">• 多架构支持</text>
  <text x="900" y="825" class="description">• 依赖库管理</text>
  <text x="900" y="840" class="description">• 版本控制</text>

  <!-- 步骤6: 集成与测试 -->
  <rect x="50" y="900" width="1100" height="140" class="step-box" rx="10"/>
  <text x="70" y="925" class="step-title">步骤6: 集成与测试</text>

  <rect x="80" y="940" width="200" height="80" class="step-box" rx="5"/>
  <text x="90" y="960" class="subtitle">6.1 模块集成</text>
  <text x="90" y="975" class="description">• 在AndroidManifest.xml注册</text>
  <text x="90" y="990" class="description">• 添加到主导航流程</text>
  <text x="90" y="1005" class="description">• 权限声明</text>
  <text x="90" y="1020" class="description">• 依赖注入配置</text>

  <rect x="300" y="940" width="200" height="80" class="step-box" rx="5"/>
  <text x="310" y="960" class="subtitle">6.2 单元测试</text>
  <text x="310" y="975" class="description">• ViewModel测试</text>
  <text x="310" y="990" class="description">• Repository测试</text>
  <text x="310" y="1005" class="description">• Manager测试</text>
  <text x="310" y="1020" class="description">• Mock数据测试</text>

  <rect x="520" y="940" width="200" height="80" class="step-box" rx="5"/>
  <text x="530" y="960" class="subtitle">6.3 集成测试</text>
  <text x="530" y="975" class="description">• 端到端功能测试</text>
  <text x="530" y="990" class="description">• 与现有模块集成测试</text>
  <text x="530" y="1005" class="description">• 性能测试</text>
  <text x="530" y="1020" class="description">• 兼容性测试</text>

  <rect x="740" y="940" width="200" height="80" class="step-box" rx="5"/>
  <text x="750" y="960" class="subtitle">6.4 文档编写</text>
  <text x="750" y="975" class="description">• API文档</text>
  <text x="750" y="990" class="description">• 使用说明</text>
  <text x="750" y="1005" class="description">• 架构文档</text>
  <text x="750" y="1020" class="description">• 测试报告</text>

  <rect x="960" y="940" width="180" height="80" class="step-box" rx="5"/>
  <text x="970" y="960" class="subtitle">6.5 部署发布</text>
  <text x="970" y="975" class="description">• 版本管理</text>
  <text x="970" y="990" class="description">• 发布流程</text>
  <text x="970" y="1005" class="description">• 回滚方案</text>

  <!-- 开发流程箭头 -->
  <line x1="600" y1="160" x2="600" y2="180" class="arrow"/>
  <line x1="600" y1="340" x2="600" y2="360" class="arrow"/>
  <line x1="600" y1="540" x2="600" y2="560" class="arrow"/>
  <line x1="600" y1="700" x2="600" y2="720" class="arrow"/>
  <line x1="600" y1="880" x2="600" y2="900" class="arrow"/>

  <!-- 关键注意事项 -->
  <rect x="50" y="1060" width="1100" height="120" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
  <text x="70" y="1085" class="step-title">关键注意事项</text>
  <text x="70" y="1105" class="description">1. 遵循现有架构模式: 严格按照分层架构开发，确保代码风格一致</text>
  <text x="70" y="1120" class="description">2. 接口设计: 定义清晰的接口，便于后续扩展和维护</text>
  <text x="70" y="1135" class="description">3. 错误处理: 完善的异常处理机制，确保系统稳定性</text>
  <text x="70" y="1150" class="description">4. 性能优化: 考虑内存使用、CPU占用，避免影响眼动追踪性能</text>
  <text x="70" y="1165" class="description">5. 向后兼容: 确保新模块不影响现有功能，提供平滑的升级路径</text>

  <!-- 示例代码结构 -->
  <rect x="50" y="1200" width="1100" height="180" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="10"/>
  <text x="70" y="1225" class="step-title">示例代码结构</text>
  <text x="70" y="1245" class="code">app/src/main/java/com/mitdd/gazetracker/voice/</text>
  <text x="70" y="1260" class="code">├── VoiceControlActivity.kt</text>
  <text x="70" y="1275" class="code">├── VoiceControlFragment.kt</text>
  <text x="70" y="1290" class="code">├── widget/VoiceWaveView.kt</text>
  <text x="70" y="1305" class="code">├── vm/VoiceControlViewModel.kt</text>
  <text x="70" y="1320" class="code">├── manager/VoiceControlManager.kt</text>
  <text x="70" y="1335" class="code">├── service/VoiceRecognitionService.kt</text>
  <text x="70" y="1350" class="code">├── repository/VoiceControlRepository.kt</text>
  <text x="70" y="1365" class="code">└── bean/VoiceResult.kt, VoiceConfig.kt</text>
</svg>
