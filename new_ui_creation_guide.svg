<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .section-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2980b9; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .step-text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .xml-box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
      .kotlin-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .theme-box { fill: #fdf2e9; stroke: #f39c12; stroke-width: 2; }
      .custom-view-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; }
      .step-box { fill: #ffffff; stroke: #34495e; stroke-width: 2; }
      .decision-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; }
      .arrow { stroke: #7f8c8d; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .thick-arrow { stroke: #2980b9; stroke-width: 3; fill: none; marker-end: url(#bluearrow); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d" />
    </marker>
    <marker id="bluearrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2980b9" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">眼动追踪项目新建UI完整指南</text>
  <text x="800" y="60" text-anchor="middle" class="subtitle">Complete Guide for Creating New UI in Gaze Tracker Project</text>

  <!-- 第一步：需求分析与设计决策 -->
  <rect x="50" y="90" width="1500" height="120" class="decision-box" rx="10"/>
  <text x="800" y="115" text-anchor="middle" class="section-title">🎯 第一步：需求分析与UI类型选择</text>
  
  <rect x="80" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="180" y="150" text-anchor="middle" class="step-text">📋 普通Activity/Fragment</text>
  <text x="180" y="165" text-anchor="middle" class="step-text">适用：设置页面、列表页面</text>
  <text x="180" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐</text>

  <rect x="320" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="420" y="150" text-anchor="middle" class="step-text">🎨 自定义View</text>
  <text x="420" y="165" text-anchor="middle" class="step-text">适用：图形绘制、交互控件</text>
  <text x="420" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐⭐⭐</text>

  <rect x="560" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="660" y="150" text-anchor="middle" class="step-text">👁️ 眼动评估界面</text>
  <text x="660" y="165" text-anchor="middle" class="step-text">适用：新的评估模块</text>
  <text x="660" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐⭐⭐⭐</text>

  <rect x="800" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="900" y="150" text-anchor="middle" class="step-text">🔧 复合组件</text>
  <text x="900" y="165" text-anchor="middle" class="step-text">适用：可复用的UI组件</text>
  <text x="900" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐⭐</text>

  <rect x="1040" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="1140" y="150" text-anchor="middle" class="step-text">📱 全屏沉浸式</text>
  <text x="1140" y="165" text-anchor="middle" class="step-text">适用：校准、测试界面</text>
  <text x="1140" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐⭐⭐</text>

  <rect x="1280" y="130" width="200" height="60" class="step-box" rx="5"/>
  <text x="1380" y="150" text-anchor="middle" class="step-text">🎭 透明悬浮</text>
  <text x="1380" y="165" text-anchor="middle" class="step-text">适用：提示、引导界面</text>
  <text x="1380" y="180" text-anchor="middle" class="step-text">复杂度：⭐⭐⭐</text>

  <!-- 第二步：创建流程图 -->
  <text x="800" y="250" text-anchor="middle" class="section-title">🛠️ 第二步：具体创建流程（以眼动评估界面为例）</text>

  <!-- 2.1 XML布局创建 -->
  <rect x="50" y="280" width="350" height="200" class="xml-box" rx="8"/>
  <text x="225" y="305" text-anchor="middle" class="subtitle">📄 2.1 创建XML布局文件</text>
  <text x="60" y="330" class="step-text">📁 位置：app/src/main/res/layout/</text>
  <text x="60" y="350" class="step-text">📝 文件名：activity_new_evaluation.xml</text>
  <text x="60" y="375" class="code">&lt;?xml version="1.0" encoding="utf-8"?&gt;</text>
  <text x="60" y="390" class="code">&lt;androidx.constraintlayout.widget.ConstraintLayout</text>
  <text x="60" y="405" class="code">    xmlns:android="http://schemas.android.com/apk/res/android"</text>
  <text x="60" y="420" class="code">    android:layout_width="match_parent"</text>
  <text x="60" y="435" class="code">    android:layout_height="match_parent"&gt;</text>
  <text x="60" y="455" class="code">    &lt;!-- 自定义评估View --&gt;</text>
  <text x="60" y="470" class="code">    &lt;com.mitdd.gazetracker.movement.custom.</text>

  <!-- 2.2 自定义View创建 -->
  <rect x="430" y="280" width="350" height="200" class="custom-view-box" rx="8"/>
  <text x="605" y="305" text-anchor="middle" class="subtitle">🎨 2.2 创建自定义View</text>
  <text x="440" y="330" class="step-text">📁 位置：app/src/main/java/com/mitdd/gazetracker/movement/</text>
  <text x="440" y="350" class="step-text">📝 文件名：NewEvaluationView.kt</text>
  <text x="440" y="375" class="code">class NewEvaluationView @JvmOverloads constructor(</text>
  <text x="440" y="390" class="code">    context: Context,</text>
  <text x="440" y="405" class="code">    attrs: AttributeSet? = null</text>
  <text x="440" y="420" class="code">) : FrameLayout(context, attrs) {</text>
  <text x="440" y="440" class="code">    private val screenWidth = ScreenUtil.getScreenWidth(context)</text>
  <text x="440" y="455" class="code">    private val screenHeight = ScreenUtil.getScreenHeight(context)</text>
  <text x="440" y="470" class="code">    // Canvas绘制逻辑</text>

  <!-- 2.3 Activity创建 -->
  <rect x="810" y="280" width="350" height="200" class="kotlin-box" rx="8"/>
  <text x="985" y="305" text-anchor="middle" class="subtitle">📱 2.3 创建Activity</text>
  <text x="820" y="330" class="step-text">📁 位置：app/src/main/java/com/mitdd/gazetracker/movement/</text>
  <text x="820" y="350" class="step-text">📝 文件名：NewEvaluationActivity.kt</text>
  <text x="820" y="375" class="code">class NewEvaluationActivity : BaseActivity() {</text>
  <text x="820" y="390" class="code">    private lateinit var binding: ActivityNewEvaluationBinding</text>
  <text x="820" y="405" class="code">    private lateinit var viewModel: NewEvaluationViewModel</text>
  <text x="820" y="425" class="code">    override fun onCreate(savedInstanceState: Bundle?) {</text>
  <text x="820" y="440" class="code">        binding = DataBindingUtil.setContentView(this,</text>
  <text x="820" y="455" class="code">            R.layout.activity_new_evaluation)</text>
  <text x="820" y="470" class="code">    }</text>

  <!-- 2.4 样式配置 -->
  <rect x="1190" y="280" width="350" height="200" class="theme-box" rx="8"/>
  <text x="1365" y="305" text-anchor="middle" class="subtitle">🎨 2.4 配置样式主题</text>
  <text x="1200" y="330" class="step-text">📁 位置：app/src/main/res/values/</text>
  <text x="1200" y="350" class="step-text">📝 修改文件：themes.xml, styles.xml, colors.xml</text>
  <text x="1200" y="375" class="code">&lt;style name="NewEvaluationTheme"</text>
  <text x="1200" y="390" class="code">    parent="Theme.AppCompat.NoActionBar"&gt;</text>
  <text x="1200" y="405" class="code">    &lt;item name="android:windowFullscreen"&gt;true&lt;/item&gt;</text>
  <text x="1200" y="420" class="code">    &lt;item name="android:screenOrientation"&gt;landscape&lt;/item&gt;</text>
  <text x="1200" y="440" class="code">&lt;color name="evaluation_primary"&gt;#2196F3&lt;/color&gt;</text>
  <text x="1200" y="455" class="code">&lt;color name="gaze_point_color"&gt;#FF5722&lt;/color&gt;</text>

  <!-- 连接箭头 -->
  <line x1="225" y1="480" x2="225" y2="520" class="thick-arrow"/>
  <line x1="605" y1="480" x2="605" y2="520" class="thick-arrow"/>
  <line x1="985" y1="480" x2="985" y2="520" class="thick-arrow"/>
  <line x1="1365" y1="480" x2="1365" y2="520" class="thick-arrow"/>

  <!-- 第三步：详细实现步骤 -->
  <text x="800" y="550" text-anchor="middle" class="section-title">⚙️ 第三步：详细实现步骤</text>

  <!-- 3.1 Canvas绘制实现 -->
  <rect x="50" y="570" width="380" height="180" class="custom-view-box" rx="8"/>
  <text x="240" y="595" text-anchor="middle" class="subtitle">🖌️ 3.1 Canvas绘制实现</text>
  <text x="60" y="620" class="code">override fun onDraw(canvas: Canvas) {</text>
  <text x="60" y="635" class="code">    super.onDraw(canvas)</text>
  <text x="60" y="650" class="code">    // 绘制背景</text>
  <text x="60" y="665" class="code">    canvas.drawColor(Color.BLACK)</text>
  <text x="60" y="680" class="code">    // 绘制目标点</text>
  <text x="60" y="695" class="code">    canvas.drawCircle(targetX, targetY, radius, targetPaint)</text>
  <text x="60" y="710" class="code">    // 绘制视线轨迹</text>
  <text x="60" y="725" class="code">    canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="60" y="740" class="code">}</text>

  <!-- 3.2 坐标转换系统 -->
  <rect x="460" y="570" width="380" height="180" class="kotlin-box" rx="8"/>
  <text x="650" y="595" text-anchor="middle" class="subtitle">📐 3.2 坐标转换系统</text>
  <text x="470" y="620" class="code">// 相对坐标转屏幕坐标</text>
  <text x="470" y="635" class="code">private fun convertToScreenCoords(relativeX: Float, relativeY: Float): PointF {</text>
  <text x="470" y="650" class="code">    val screenX = relativeX * screenWidth</text>
  <text x="470" y="665" class="code">    val screenY = relativeY * screenHeight</text>
  <text x="470" y="680" class="code">    return PointF(screenX, screenY)</text>
  <text x="470" y="695" class="code">}</text>
  <text x="470" y="715" class="code">// 屏幕坐标转相对坐标</text>
  <text x="470" y="730" class="code">private fun convertToRelativeCoords(screenX: Float, screenY: Float): PointF</text>

  <!-- 3.3 触摸事件处理 -->
  <rect x="870" y="570" width="380" height="180" class="custom-view-box" rx="8"/>
  <text x="1060" y="595" text-anchor="middle" class="subtitle">👆 3.3 触摸事件处理</text>
  <text x="880" y="620" class="code">override fun onTouchEvent(event: MotionEvent): Boolean {</text>
  <text x="880" y="635" class="code">    when (event.action) {</text>
  <text x="880" y="650" class="code">        MotionEvent.ACTION_DOWN -> {</text>
  <text x="880" y="665" class="code">            startX = event.x</text>
  <text x="880" y="680" class="code">            startY = event.y</text>
  <text x="880" y="695" class="code">        }</text>
  <text x="880" y="710" class="code">        MotionEvent.ACTION_MOVE -> {</text>
  <text x="880" y="725" class="code">            updatePath(event.x, event.y)</text>
  <text x="880" y="740" class="code">            invalidate()</text>

  <!-- 3.4 数据管理 -->
  <rect x="1280" y="570" width="270" height="180" class="kotlin-box" rx="8"/>
  <text x="1415" y="595" text-anchor="middle" class="subtitle">💾 3.4 数据管理</text>
  <text x="1290" y="620" class="code">// ViewModel创建</text>
  <text x="1290" y="635" class="code">class NewEvaluationViewModel : BaseViewModel() {</text>
  <text x="1290" y="650" class="code">    private val repository = NewEvaluationRepository()</text>
  <text x="1290" y="665" class="code">    val evaluationData = MutableLiveData&lt;EvaluationResult&gt;()</text>
  <text x="1290" y="685" class="code">    fun submitData(data: EvaluationData) {</text>
  <text x="1290" y="700" class="code">        repository.submitEvaluation(data)</text>
  <text x="1290" y="715" class="code">    }</text>
  <text x="1290" y="730" class="code">}</text>

  <!-- 第四步：配置文件修改 -->
  <text x="800" y="790" text-anchor="middle" class="section-title">📝 第四步：配置文件修改</text>

  <!-- 4.1 AndroidManifest.xml -->
  <rect x="50" y="810" width="350" height="160" class="xml-box" rx="8"/>
  <text x="225" y="835" text-anchor="middle" class="subtitle">📋 4.1 AndroidManifest.xml</text>
  <text x="60" y="860" class="code">&lt;activity</text>
  <text x="60" y="875" class="code">    android:name=".movement.NewEvaluationActivity"</text>
  <text x="60" y="890" class="code">    android:theme="@style/NewEvaluationTheme"</text>
  <text x="60" y="905" class="code">    android:screenOrientation="landscape"</text>
  <text x="60" y="920" class="code">    android:configChanges="orientation|screenSize"</text>
  <text x="60" y="935" class="code">    android:exported="false" /&gt;</text>
  <text x="60" y="955" class="step-text">🔧 配置Activity属性和主题</text>

  <!-- 4.2 依赖注入配置 -->
  <rect x="430" y="810" width="350" height="160" class="kotlin-box" rx="8"/>
  <text x="605" y="835" text-anchor="middle" class="subtitle">🔌 4.2 依赖注入配置</text>
  <text x="440" y="860" class="code">// 在相应的Module中添加</text>
  <text x="440" y="875" class="code">@Provides</text>
  <text x="440" y="890" class="code">fun provideNewEvaluationRepository(): NewEvaluationRepository {</text>
  <text x="440" y="905" class="code">    return NewEvaluationRepository()</text>
  <text x="440" y="920" class="code">}</text>
  <text x="440" y="940" class="code">@Provides</text>
  <text x="440" y="955" class="code">fun provideNewEvaluationViewModel(): NewEvaluationViewModel</text>

  <!-- 4.3 网络接口配置 -->
  <rect x="810" y="810" width="350" height="160" class="kotlin-box" rx="8"/>
  <text x="985" y="835" text-anchor="middle" class="subtitle">🌐 4.3 网络接口配置</text>
  <text x="820" y="860" class="code">interface NewEvaluationApiService {</text>
  <text x="820" y="875" class="code">    @POST("/api/movement/new-evaluation/submit")</text>
  <text x="820" y="890" class="code">    suspend fun submitEvaluation(</text>
  <text x="820" y="905" class="code">        @Body request: NewEvaluationRequest</text>
  <text x="820" y="920" class="code">    ): ApiResponse&lt;Long&gt;</text>
  <text x="820" y="935" class="code">}</text>
  <text x="820" y="955" class="step-text">🔗 定义API接口和数据模型</text>

  <!-- 4.4 数据库配置 -->
  <rect x="1190" y="810" width="360" height="160" class="kotlin-box" rx="8"/>
  <text x="1370" y="835" text-anchor="middle" class="subtitle">🗄️ 4.4 数据库配置</text>
  <text x="1200" y="860" class="code">@Entity(tableName = "new_evaluation_results")</text>
  <text x="1200" y="875" class="code">data class NewEvaluationResult(</text>
  <text x="1200" y="890" class="code">    @PrimaryKey val id: String,</text>
  <text x="1200" y="905" class="code">    val patientId: String,</text>
  <text x="1200" y="920" class="code">    val evaluationData: String,</text>
  <text x="1200" y="935" class="code">    val createdAt: Long</text>
  <text x="1200" y="950" class="code">)</text>

  <!-- 第五步：测试与调试 -->
  <text x="800" y="1010" text-anchor="middle" class="section-title">🧪 第五步：测试与调试</text>

  <!-- 5.1 单元测试 -->
  <rect x="50" y="1030" width="280" height="140" class="kotlin-box" rx="8"/>
  <text x="190" y="1055" text-anchor="middle" class="subtitle">🔬 5.1 单元测试</text>
  <text x="60" y="1080" class="code">@Test</text>
  <text x="60" y="1095" class="code">fun testCoordinateConversion() {</text>
  <text x="60" y="1110" class="code">    val view = NewEvaluationView(context)</text>
  <text x="60" y="1125" class="code">    val result = view.convertToScreenCoords(0.5f, 0.5f)</text>
  <text x="60" y="1140" class="code">    assertEquals(screenWidth/2, result.x)</text>
  <text x="60" y="1155" class="code">}</text>

  <!-- 5.2 UI测试 -->
  <rect x="360" y="1030" width="280" height="140" class="kotlin-box" rx="8"/>
  <text x="500" y="1055" text-anchor="middle" class="subtitle">📱 5.2 UI测试</text>
  <text x="370" y="1080" class="code">@Test</text>
  <text x="370" y="1095" class="code">fun testTouchInteraction() {</text>
  <text x="370" y="1110" class="code">    onView(withId(R.id.new_evaluation_view))</text>
  <text x="370" y="1125" class="code">        .perform(click())</text>
  <text x="370" y="1140" class="code">        .check(matches(isDisplayed()))</text>
  <text x="370" y="1155" class="code">}</text>

  <!-- 5.3 性能测试 -->
  <rect x="670" y="1030" width="280" height="140" class="kotlin-box" rx="8"/>
  <text x="810" y="1055" text-anchor="middle" class="subtitle">⚡ 5.3 性能测试</text>
  <text x="680" y="1080" class="code">@Test</text>
  <text x="680" y="1095" class="code">fun testDrawingPerformance() {</text>
  <text x="680" y="1110" class="code">    val startTime = System.currentTimeMillis()</text>
  <text x="680" y="1125" class="code">    view.invalidate()</text>
  <text x="680" y="1140" class="code">    val duration = System.currentTimeMillis() - startTime</text>
  <text x="680" y="1155" class="code">    assertTrue(duration &lt; 16) // 60fps</text>

  <!-- 5.4 集成测试 -->
  <rect x="980" y="1030" width="280" height="140" class="kotlin-box" rx="8"/>
  <text x="1120" y="1055" text-anchor="middle" class="subtitle">🔗 5.4 集成测试</text>
  <text x="990" y="1080" class="code">@Test</text>
  <text x="990" y="1095" class="code">fun testDataSubmission() {</text>
  <text x="990" y="1110" class="code">    val mockData = createMockEvaluationData()</text>
  <text x="990" y="1125" class="code">    viewModel.submitData(mockData)</text>
  <text x="990" y="1140" class="code">    verify(repository).submitEvaluation(mockData)</text>
  <text x="990" y="1155" class="code">}</text>

  <!-- 5.5 调试技巧 -->
  <rect x="1290" y="1030" width="260" height="140" class="custom-view-box" rx="8"/>
  <text x="1420" y="1055" text-anchor="middle" class="subtitle">🐛 5.5 调试技巧</text>
  <text x="1300" y="1080" class="step-text">• 使用Logger.d()打印关键信息</text>
  <text x="1300" y="1095" class="step-text">• Canvas.drawText()显示调试信息</text>
  <text x="1300" y="1110" class="step-text">• 使用不同颜色区分绘制元素</text>
  <text x="1300" y="1125" class="step-text">• 添加FPS计数器监控性能</text>
  <text x="1300" y="1140" class="step-text">• 使用Layout Inspector检查布局</text>
  <text x="1300" y="1155" class="step-text">• GPU渲染分析工具</text>

  <!-- 第六步：最佳实践 -->
  <text x="800" y="1210" text-anchor="middle" class="section-title">✨ 第六步：最佳实践与注意事项</text>

  <!-- 6.1 性能优化 -->
  <rect x="50" y="1230" width="320" height="120" class="performance-box" rx="8"/>
  <text x="210" y="1255" text-anchor="middle" class="subtitle">🚀 6.1 性能优化</text>
  <text x="60" y="1275" class="step-text">• Paint对象复用，避免在onDraw中创建</text>
  <text x="60" y="1290" class="step-text">• 使用invalidate(Rect)精确重绘区域</text>
  <text x="60" y="1305" class="step-text">• 坐标计算结果缓存</text>
  <text x="60" y="1320" class="step-text">• 避免频繁的内存分配</text>
  <text x="60" y="1335" class="step-text">• 使用硬件加速</text>

  <!-- 6.2 代码规范 -->
  <rect x="390" y="1230" width="320" height="120" class="kotlin-box" rx="8"/>
  <text x="550" y="1255" text-anchor="middle" class="subtitle">📋 6.2 代码规范</text>
  <text x="400" y="1275" class="step-text">• 遵循Kotlin编码规范</text>
  <text x="400" y="1290" class="step-text">• 使用有意义的变量和方法名</text>
  <text x="400" y="1305" class="step-text">• 添加详细的注释说明</text>
  <text x="400" y="1320" class="step-text">• 合理的类和方法拆分</text>
  <text x="400" y="1335" class="step-text">• 使用设计模式</text>

  <!-- 6.3 用户体验 -->
  <rect x="730" y="1230" width="320" height="120" class="custom-view-box" rx="8"/>
  <text x="890" y="1255" text-anchor="middle" class="subtitle">👥 6.3 用户体验</text>
  <text x="740" y="1275" class="step-text">• 提供清晰的视觉反馈</text>
  <text x="740" y="1290" class="step-text">• 合理的动画和过渡效果</text>
  <text x="740" y="1305" class="step-text">• 错误状态的友好提示</text>
  <text x="740" y="1320" class="step-text">• 支持无障碍访问</text>
  <text x="740" y="1335" class="step-text">• 响应式设计</text>

  <!-- 6.4 维护性 -->
  <rect x="1070" y="1230" width="480" height="120" class="theme-box" rx="8"/>
  <text x="1310" y="1255" text-anchor="middle" class="subtitle">🔧 6.4 可维护性</text>
  <text x="1080" y="1275" class="step-text">• 模块化设计，职责单一</text>
  <text x="1080" y="1290" class="step-text">• 配置参数外部化（如校准参数）</text>
  <text x="1080" y="1305" class="step-text">• 完善的错误处理和日志记录</text>
  <text x="1080" y="1320" class="step-text">• 版本兼容性考虑</text>
  <text x="1080" y="1335" class="step-text">• 文档和示例代码</text>

  <!-- 总结流程图 -->
  <rect x="50" y="1370" width="1500" height="25" class="decision-box" rx="5"/>
  <text x="800" y="1387" text-anchor="middle" class="section-title">🎯 完整流程：需求分析 → XML布局 → 自定义View → Activity → 配置文件 → 测试调试 → 优化发布</text>

</svg>
