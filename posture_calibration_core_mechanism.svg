<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="foundationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="coordinateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="qualityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="algorithmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">姿势校准：为什么是眼动检测的基础？</text>
  
  <!-- 核心问题 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心问题：眼动检测需要精确的空间坐标系统和稳定的几何基准</text>
  <text x="70" y="110" class="text">• 眼动追踪本质上是一个3D到2D的坐标映射问题</text>
  <text x="70" y="125" class="text">• 没有准确的姿势基准，眼动数据就无法正确映射到屏幕坐标</text>
  
  <!-- 四大核心价值详解 -->
  <rect x="50" y="160" width="1700" height="500" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">姿势校准的四大核心价值详解</text>
  
  <!-- 1. 建立坐标系统 -->
  <rect x="80" y="210" width="400" height="200" rx="8" fill="url(#coordinateGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="280" y="235" text-anchor="middle" class="subtitle">1. 建立坐标系统</text>
  <text x="280" y="255" text-anchor="middle" class="highlight">空间几何基准建立</text>
  
  <text x="100" y="280" class="text"><tspan class="highlight">瞳距标定:</tspan></text>
  <text x="120" y="300" class="code">expect_interpupillary_distance</text>
  <text x="120" y="315" class="text">• 建立50cm标准距离的瞳距基准</text>
  <text x="120" y="330" class="text">• 用于后续距离计算和深度估算</text>
  
  <text x="100" y="355" class="text"><tspan class="highlight">参考点设定:</tspan></text>
  <text x="120" y="375" class="code">setting_refer_points</text>
  <text x="120" y="390" class="text">• 记录标准姿势下的眼部位置</text>
  <text x="120" y="405" class="text">• 作为后续偏移检测的基准点</text>
  
  <!-- 2. 质量保证 -->
  <rect x="520" y="210" width="400" height="200" rx="8" fill="url(#qualityGradient)" stroke="#2196f3" stroke-width="2"/>
  <text x="720" y="235" text-anchor="middle" class="subtitle">2. 质量保证</text>
  <text x="720" y="255" text-anchor="middle" class="highlight">最佳检测状态确保</text>
  
  <text x="540" y="280" class="text"><tspan class="highlight">虹膜比例检查:</tspan></text>
  <text x="560" y="300" class="code">two_iris_width_ratio [0.95, 1.05]</text>
  <text x="560" y="315" class="text">• 确保双眼检测质量一致</text>
  <text x="560" y="330" class="text">• 避免单眼遮挡或光照不均</text>
  
  <text x="540" y="355" class="text"><tspan class="highlight">距离范围控制:</tspan></text>
  <text x="560" y="375" class="code">dist_ratio [0.85, 1.17]</text>
  <text x="560" y="390" class="text">• 40cm-60cm最佳检测距离</text>
  <text x="560" y="405" class="text">• 保证瞳孔和虹膜清晰可见</text>
  
  <!-- 3. 实时反馈 -->
  <rect x="960" y="210" width="400" height="200" rx="8" fill="url(#foundationGradient)" stroke="#ff9800" stroke-width="2"/>
  <text x="1160" y="235" text-anchor="middle" class="subtitle">3. 实时反馈</text>
  <text x="1160" y="255" text-anchor="middle" class="highlight">动态姿势引导</text>
  
  <text x="980" y="280" class="text"><tspan class="highlight">位置偏移检测:</tspan></text>
  <text x="1000" y="300" class="code">left_distance, right_distance</text>
  <text x="1000" y="315" class="text">• 实时计算眼部位置偏移</text>
  <text x="1000" y="330" class="text">• 提供"向左/右/上/下"调整指导</text>
  
  <text x="980" y="355" class="text"><tspan class="highlight">稳定性监控:</tspan></text>
  <text x="1000" y="375" class="code">diff < pose_stable_move_min</text>
  <text x="1000" y="390" class="text">• 检测头部移动幅度</text>
  <text x="1000" y="405" class="text">• 确保姿势稳定后开始倒计时</text>
  
  <!-- 4. 数据基准 -->
  <rect x="80" y="440" width="400" height="200" rx="8" fill="url(#algorithmGradient)" stroke="#9c27b0" stroke-width="2"/>
  <text x="280" y="465" text-anchor="middle" class="subtitle">4. 数据基准</text>
  <text x="280" y="485" text-anchor="middle" class="highlight">个性化模型基础</text>
  
  <text x="100" y="510" class="text"><tspan class="highlight">几何参数记录:</tspan></text>
  <text x="120" y="530" class="code">left_pupil, right_pupil</text>
  <text x="120" y="545" class="text">• 记录用户特定的眼部几何</text>
  <text x="120" y="560" class="text">• 为个性化校准提供基础数据</text>
  
  <text x="100" y="585" class="text"><tspan class="highlight">视频数据保存:</tspan></text>
  <text x="120" y="605" class="code">saving_videos = true</text>
  <text x="120" y="620" class="text">• 保存校准过程视频</text>
  <text x="120" y="635" class="text">• 用于算法优化和问题诊断</text>
  
  <!-- 姿势对齐核心算法 -->
  <rect x="50" y="680" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="705" text-anchor="middle" class="subtitle">姿势对齐核心算法实现</text>
  
  <!-- recognize_pose_valid 函数 -->
  <rect x="80" y="730" width="800" height="320" rx="8" fill="#f8f9fa"/>
  <text x="480" y="755" text-anchor="middle" class="subtitle">recognize_pose_valid() 姿势有效性判断</text>
  
  <text x="100" y="780" class="highlight">1. 位置范围检查</text>
  <text x="120" y="800" class="code">left_distance = sqrt((eye_left_x - refer_x)² + (eye_left_y - refer_y)²)</text>
  <text x="120" y="815" class="code">right_distance = sqrt((eye_right_x - refer_x)² + (eye_right_y - refer_y)²)</text>
  <text x="120" y="835" class="text">• 计算当前眼部位置与参考点的欧氏距离</text>
  <text x="120" y="850" class="text">• 判断是否在 pose_align_eye_radius (200像素) 范围内</text>
  
  <text x="100" y="875" class="highlight">2. 距离比例验证</text>
  <text x="120" y="895" class="code">dist_ratio = det_result.interpupillary_distance / expect_interpupillary_distance</text>
  <text x="120" y="915" class="text">• 当前瞳距与期望瞳距的比值</text>
  <text x="120" y="930" class="text">• 必须在 [0.85, 1.17] 范围内 (对应40cm-60cm距离)</text>
  
  <text x="100" y="955" class="highlight">3. 综合判断逻辑</text>
  <text x="120" y="975" class="code">pose_is_skew = !(left_distance < 200 && right_distance < 200 && </text>
  <text x="140" y="990" class="code">dist_ratio > 0.85 && dist_ratio < 1.17)</text>
  <text x="120" y="1010" class="text">• 所有条件同时满足才认为姿势有效</text>
  <text x="120" y="1025" class="text">• 返回 false 表示姿势正确，true 表示需要调整</text>
  
  <!-- head_pose_aligning 函数 -->
  <rect x="920" y="730" width="800" height="320" rx="8" fill="#f8f9fa"/>
  <text x="1320" y="755" text-anchor="middle" class="subtitle">head_pose_aligning() 姿势校准主流程</text>
  
  <text x="940" y="780" class="highlight">1. 虹膜质量检查</text>
  <text x="960" y="800" class="code">two_iris_width_ratio = left_iris_width / right_iris_width</text>
  <text x="960" y="815" class="code">if (ratio >= 0.95 && ratio < 1.05) // 双眼检测质量一致</text>
  <text x="960" y="835" class="text">• 确保双眼虹膜检测质量相近</text>
  <text x="960" y="850" class="text">• 避免单眼遮挡或检测异常</text>
  
  <text x="940" y="875" class="highlight">2. 稳定性检测</text>
  <text x="960" y="895" class="code">diff = current_eye_points - previous_eye_points</text>
  <text x="960" y="910" class="code">if (pose_valid && diff < pose_stable_move_min) // 50像素</text>
  <text x="960" y="930" class="text">• 检测眼部位置变化幅度</text>
  <text x="960" y="945" class="text">• 稳定状态下才开始倒计时</text>
  
  <text x="940" y="970" class="highlight">3. 倒计时完成判断</text>
  <text x="960" y="990" class="code">remaining = max(0, countdown_second - elapsed_time)</text>
  <text x="960" y="1005" class="code">if (remaining == 0) pose_aligned = true</text>
  <text x="960" y="1025" class="text">• 稳定保持2秒后校准完成</text>
  <text x="960" y="1040" class="text">• 设置 finish_flag = true</text>
  
  <!-- 距离映射算法 -->
  <rect x="50" y="1100" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1125" text-anchor="middle" class="subtitle">距离映射算法 - distance_sigmoid()</text>
  
  <rect x="80" y="1150" width="600" height="180" rx="8" fill="#fff3e0"/>
  <text x="380" y="1175" text-anchor="middle" class="subtitle">Sigmoid函数参数</text>
  <text x="100" y="1200" class="code">y = 1.0346 * (1.0 / (1.0 + exp(-2.6 * (x - 0.994))) + 0.83) - 0.88</text>
  <text x="100" y="1220" class="text">• k = 2.6 (陡峭度参数)</text>
  <text x="100" y="1240" class="text">• x0 = 0.994 (中心点)</text>
  <text x="100" y="1260" class="text">• alpha = 1.0346 (缩放因子)</text>
  <text x="100" y="1280" class="text">• beta = -0.83, gamma = -0.88 (偏移参数)</text>
  <text x="100" y="1300" class="text">• 输出范围: [0, 1]</text>
  <text x="100" y="1320" class="text">• 0.5 表示理想距离 (50cm)</text>
  
  <rect x="720" y="1150" width="600" height="180" rx="8" fill="#e8f5e8"/>
  <text x="1020" y="1175" text-anchor="middle" class="subtitle">距离映射意义</text>
  <text x="740" y="1200" class="text">• <tspan class="highlight">dist_ratio = 1.0:</tspan> 用户在理想距离 (50cm)</text>
  <text x="740" y="1220" class="text">• <tspan class="highlight">dist_ratio > 1.0:</tspan> 用户距离过近</text>
  <text x="740" y="1240" class="text">• <tspan class="highlight">dist_ratio < 1.0:</tspan> 用户距离过远</text>
  <text x="740" y="1260" class="text">• <tspan class="highlight">输出 0.45-0.55:</tspan> 理想距离范围</text>
  <text x="740" y="1280" class="text">• <tspan class="highlight">输出 < 0.4:</tspan> 太近，需要后退</text>
  <text x="740" y="1300" class="text">• <tspan class="highlight">输出 > 0.6:</tspan> 太远，需要前进</text>
  <text x="740" y="1320" class="text">• 为UI提供直观的距离反馈</text>
  
  <!-- 为什么是眼动检测基础 -->
  <rect x="50" y="1370" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1395" text-anchor="middle" class="subtitle">为什么姿势校准是眼动检测的基础？</text>
  
  <text x="80" y="1425" class="highlight">1. 坐标系统一致性</text>
  <text x="100" y="1445" class="text">• 眼动追踪需要将3D眼球运动映射到2D屏幕坐标</text>
  <text x="100" y="1465" class="text">• 没有固定的头部姿势基准，映射关系就会不断变化</text>
  <text x="100" y="1485" class="text">• 姿势校准建立了稳定的几何参考系</text>
  
  <text x="80" y="1515" class="highlight">2. 算法精度保证</text>
  <text x="100" y="1535" class="text">• 眼动算法基于特定的几何假设 (瞳距、距离、角度)</text>
  <text x="100" y="1555" class="text">• 偏离标准姿势会导致算法误差急剧增大</text>
  <text x="100" y="1575" class="text">• 姿势校准确保用户处于算法最优工作区间</text>
  
  <text x="80" y="1605" class="highlight">3. 个性化适配基础</text>
  <text x="100" y="1625" class="text">• 每个用户的眼部几何特征不同 (瞳距、眼球大小、眼睑形状)</text>
  <text x="100" y="1645" class="text">• 姿势校准记录用户特定的几何参数</text>
  <text x="100" y="1665" class="text">• 为后续的个性化眼动模型提供基础数据</text>
  
  <!-- 总结 -->
  <rect x="100" y="1690" width="1600" height="80" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1715" text-anchor="middle" class="highlight">
    总结：姿势校准通过精确的几何基准建立、质量控制和个性化适配
  </text>
  <text x="900" y="1735" text-anchor="middle" class="text">
    为眼动追踪提供了稳定可靠的坐标系统基础，是整个眼动检测链路的第一环和关键环节
  </text>
  <text x="900" y="1755" text-anchor="middle" class="text">
    没有准确的姿势校准，后续的眼动数据就失去了空间参考意义
  </text>
</svg>
