<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="calibrationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="correctionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1400" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">showPostureCalibration() 方法详解</text>
  
  <!-- 方法概述 -->
  <rect x="50" y="60" width="1500" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="800" y="85" text-anchor="middle" class="highlight">📋 方法作用：显示姿势校准界面，根据不同模式配置UI和交互逻辑</text>
  <text x="70" y="110" class="text">• 清除现有视图并创建PostureCalibrationView</text>
  <text x="70" y="125" class="text">• 根据isCorrection标志设置不同的校准模式和背景色</text>
  
  <!-- 代码流程分析 -->
  <rect x="50" y="160" width="1500" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="185" text-anchor="middle" class="subtitle">代码流程分析</text>
  
  <!-- 步骤1: 清除视图 -->
  <rect x="80" y="210" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="230" y="235" text-anchor="middle" class="subtitle">1. 清除现有视图</text>
  <text x="100" y="255" class="code">flCalibrationRoot.removeAllViews()</text>
  <text x="100" y="275" class="text">清空校准根容器中的所有子视图</text>
  
  <!-- 步骤2: 设置背景色 -->
  <rect x="400" y="210" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="550" y="235" text-anchor="middle" class="subtitle">2. 设置背景色</text>
  <text x="420" y="255" class="code">if (isCorrection) black_70</text>
  <text x="420" y="270" class="code">else "#191443"</text>
  <text x="420" y="285" class="text">矫正模式：半透明黑色</text>
  
  <!-- 步骤3: 创建校准视图 -->
  <rect x="720" y="210" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="870" y="235" text-anchor="middle" class="subtitle">3. 创建校准视图</text>
  <text x="740" y="255" class="code">PostureCalibrationView(this)</text>
  <text x="740" y="275" class="text">创建姿势校准视图实例</text>
  
  <!-- 步骤4: 配置校准类型 -->
  <rect x="1040" y="210" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="1190" y="235" text-anchor="middle" class="subtitle">4. 配置校准类型</text>
  <text x="1060" y="255" class="code">TYPE_POSTURE_CORRECTION</text>
  <text x="1060" y="270" class="code">TYPE_POSTURE_CALIBRATION</text>
  <text x="1060" y="285" class="text">根据isCorrection设置类型</text>
  
  <!-- 步骤5: 设置参考点 -->
  <rect x="80" y="310" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="230" y="335" text-anchor="middle" class="subtitle">5. 设置参考点</text>
  <text x="100" y="355" class="code">DEFAULT_REFERENCE_X = 0.5f</text>
  <text x="100" y="370" class="code">DEFAULT_REFERENCE_Y = 0.5f</text>
  <text x="100" y="385" class="text">屏幕中心点作为参考位置</text>
  
  <!-- 步骤6: 设置回调 -->
  <rect x="400" y="310" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="550" y="335" text-anchor="middle" class="subtitle">6. 设置回调函数</text>
  <text x="420" y="355" class="code">onCloseCalibrationClick</text>
  <text x="420" y="370" class="code">onCalibrationClick</text>
  <text x="420" y="385" class="text">关闭校准和开始校准回调</text>
  
  <!-- 步骤7: 添加到容器 -->
  <rect x="720" y="310" width="300" height="80" rx="8" fill="url(#uiGradient)"/>
  <text x="870" y="335" text-anchor="middle" class="subtitle">7. 添加到容器</text>
  <text x="740" y="355" class="code">flCalibrationRoot.addView()</text>
  <text x="740" y="370" class="code">MATCH_PARENT</text>
  <text x="740" y="385" class="text">全屏显示校准视图</text>
  
  <!-- 连接箭头 -->
  <line x1="380" y1="250" x2="400" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="250" x2="720" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1020" y1="250" x2="1040" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="380" y1="350" x2="400" y2="350" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="700" y1="350" x2="720" y2="350" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 两种模式对比 -->
  <rect x="50" y="580" width="1500" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="605" text-anchor="middle" class="subtitle">两种校准模式对比</text>
  
  <!-- 姿势校准模式 -->
  <rect x="100" y="630" width="600" height="220" rx="8" fill="url(#calibrationGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="400" y="655" text-anchor="middle" class="subtitle">姿势校准模式 (TYPE_POSTURE_CALIBRATION)</text>
  <text x="400" y="675" text-anchor="middle" class="highlight">isCorrection = false</text>
  
  <text x="120" y="700" class="text"><tspan class="highlight">背景色:</tspan> #191443 (深蓝紫色)</text>
  <text x="120" y="720" class="text"><tspan class="highlight">按钮文字:</tspan> "关闭校准"</text>
  <text x="120" y="740" class="text"><tspan class="highlight">语音提示:</tspan> "请找到舒适的位置" + "请保持头部位置"</text>
  <text x="120" y="760" class="text"><tspan class="highlight">校准按钮:</tspan> 隐藏 (llCalibration.isVisible = false)</text>
  <text x="120" y="780" class="text"><tspan class="highlight">使用场景:</tspan> 首次校准或完整校准流程</text>
  <text x="120" y="800" class="text"><tspan class="highlight">完成后:</tspan> 进入视标校准阶段</text>
  <text x="120" y="820" class="text"><tspan class="highlight">目标:</tspan> 建立用户的标准姿势基准</text>
  
  <!-- 姿势矫正模式 -->
  <rect x="750" y="630" width="600" height="220" rx="8" fill="url(#correctionGradient)" stroke="#ff9800" stroke-width="2"/>
  <text x="1050" y="655" text-anchor="middle" class="subtitle">姿势矫正模式 (TYPE_POSTURE_CORRECTION)</text>
  <text x="1050" y="675" text-anchor="middle" class="highlight">isCorrection = true</text>
  
  <text x="770" y="700" class="text"><tspan class="highlight">背景色:</tspan> black_70 (半透明黑色)</text>
  <text x="770" y="720" class="text"><tspan class="highlight">按钮文字:</tspan> "关闭提示"</text>
  <text x="770" y="740" class="text"><tspan class="highlight">语音提示:</tspan> "请回到正确位置"</text>
  <text x="770" y="760" class="text"><tspan class="highlight">校准按钮:</tspan> 显示 (llCalibration.isVisible = true)</text>
  <text x="770" y="780" class="text"><tspan class="highlight">使用场景:</tspan> 治疗过程中的姿势纠正</text>
  <text x="770" y="800" class="text"><tspan class="highlight">完成后:</tspan> 直接返回治疗界面</text>
  <text x="770" y="820" class="text"><tspan class="highlight">目标:</tspan> 快速纠正偏离的姿势</text>
  
  <!-- 参考点说明 -->
  <rect x="50" y="900" width="1500" height="180" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="925" text-anchor="middle" class="subtitle">参考点 (DEFAULT_REFERENCE_X/Y) 详解</text>
  
  <text x="70" y="955" class="highlight">参考点作用：</text>
  <text x="90" y="975" class="text">• DEFAULT_REFERENCE_X = 0.5f, DEFAULT_REFERENCE_Y = 0.5f (屏幕中心点)</text>
  <text x="90" y="995" class="text">• 用于计算用户眼部位置与理想位置的偏差</text>
  <text x="90" y="1015" class="text">• 判断用户是否需要向左/右、上/下调整姿势</text>
  
  <text x="800" y="955" class="highlight">姿势判断逻辑：</text>
  <text x="820" y="975" class="text">• 距离判断：dist < 0.4f (太近) | 0.45f~0.55f (适中) | > 0.6f (太远)</text>
  <text x="820" y="995" class="text">• 水平偏移：centerX - referenceX * screenWidth</text>
  <text x="820" y="1015" class="text">• 垂直偏移：centerY - referenceY * screenHeight</text>
  <text x="820" y="1035" class="text">• 倾斜角度：calculateAngleOfTilt(leftPoint, rightPoint)</text>
  
  <!-- 回调函数说明 -->
  <rect x="50" y="1100" width="1500" height="180" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="1125" text-anchor="middle" class="subtitle">回调函数详解</text>
  
  <!-- 关闭校准回调 -->
  <rect x="100" y="1150" width="600" height="100" rx="5" fill="#ffebee"/>
  <text x="400" y="1170" text-anchor="middle" class="subtitle">onCloseCalibrationClick 回调</text>
  <text x="120" y="1190" class="text">• 设置 isCLoseCalibration = true</text>
  <text x="120" y="1210" class="text">• 调用 stopPostureCalibration()</text>
  <text x="120" y="1230" class="text">• 用户主动关闭校准流程</text>
  
  <!-- 校准按钮回调 -->
  <rect x="750" y="1150" width="600" height="100" rx="5" fill="#e8f5e8"/>
  <text x="1050" y="1170" text-anchor="middle" class="subtitle">onCalibrationClick 回调</text>
  <text x="770" y="1190" class="text">• 设置 calibrationMode = CalibrationMode.CALIBRATION</text>
  <text x="770" y="1210" class="text">• 切换背景色为 #191443</text>
  <text x="770" y="1230" class="text">• 从矫正模式切换到完整校准模式</text>
  
  <!-- 总结 -->
  <rect x="100" y="1300" width="1400" height="60" rx="5" fill="#e3f2fd"/>
  <text x="800" y="1325" text-anchor="middle" class="highlight">
    总结：showPostureCalibration() 是校准流程的入口，根据isCorrection标志创建不同模式的姿势校准界面
  </text>
  <text x="800" y="1345" text-anchor="middle" class="text">
    支持完整校准和快速矫正两种场景，为后续的视标校准或治疗过程奠定姿势基础
  </text>
</svg>
