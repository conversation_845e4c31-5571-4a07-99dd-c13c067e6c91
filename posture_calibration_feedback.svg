<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; fill: #666; }
      .label { font-family: Arial, sans-serif; font-size: 14px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #555; }
      .status-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; }
      .green-status { fill: #4CAF50; }
      .yellow-status { fill: #FF9800; }
      .red-status { fill: #F44336; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">姿势校准实时反馈机制</text>
  
  <!-- 检测区域 -->
  <rect x="50" y="60" width="300" height="200" fill="#f5f5f5" stroke="#ddd" stroke-width="2" rx="10"/>
  <text x="200" y="80" text-anchor="middle" class="subtitle">实时检测</text>
  
  <!-- 人脸检测 -->
  <circle cx="120" cy="120" r="15" fill="#FFB74D"/>
  <circle cx="110" cy="115" r="3" fill="#333"/>
  <circle cx="130" cy="115" r="3" fill="#333"/>
  <path d="M 115 125 Q 120 130 125 125" stroke="#333" stroke-width="2" fill="none"/>
  <text x="120" y="150" text-anchor="middle" class="small-text">双眼检测</text>
  
  <!-- 检测参数 -->
  <text x="200" y="110" class="small-text">• 左眼坐标: (leftX, leftY)</text>
  <text x="200" y="125" class="small-text">• 右眼坐标: (rightX, rightY)</text>
  <text x="200" y="140" class="small-text">• 距离参数: dist</text>
  <text x="200" y="155" class="small-text">• 对齐状态: aligned</text>
  
  <!-- 计算处理 -->
  <rect x="400" y="60" width="350" height="200" fill="#e3f2fd" stroke="#2196F3" stroke-width="2" rx="10"/>
  <text x="575" y="80" text-anchor="middle" class="subtitle">多维度异常检测</text>
  
  <!-- 距离检测 -->
  <rect x="420" y="100" width="100" height="60" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="470" y="115" text-anchor="middle" class="label">距离检测</text>
  <text x="470" y="130" text-anchor="middle" class="small-text">0.45-0.55正常</text>
  <text x="470" y="145" text-anchor="middle" class="small-text">scale = 0.5/dist</text>
  
  <!-- 位置检测 -->
  <rect x="540" y="100" width="100" height="60" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="590" y="115" text-anchor="middle" class="label">位置检测</text>
  <text x="590" y="130" text-anchor="middle" class="small-text">±192px正常</text>
  <text x="590" y="145" text-anchor="middle" class="small-text">centerX,centerY</text>
  
  <!-- 倾斜检测 -->
  <rect x="660" y="100" width="80" height="60" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="700" y="115" text-anchor="middle" class="label">倾斜检测</text>
  <text x="700" y="130" text-anchor="middle" class="small-text">±10°正常</text>
  <text x="700" y="145" text-anchor="middle" class="small-text">atan2计算</text>
  
  <!-- 异常等级 -->
  <rect x="420" y="180" width="320" height="60" fill="#fff3e0" stroke="#FF9800" stroke-width="1" rx="5"/>
  <text x="580" y="200" text-anchor="middle" class="label">异常等级判定</text>
  <text x="480" y="220" class="small-text">degree = 0: 正常 (绿色)</text>
  <text x="480" y="235" class="small-text">degree = 1: 轻微异常 (黄色)</text>
  <text x="650" y="220" class="small-text">degree = 2: 严重异常 (红色)</text>
  
  <!-- 反馈机制 -->
  <rect x="50" y="300" width="1100" height="450" fill="#f9f9f9" stroke="#ddd" stroke-width="2" rx="10"/>
  <text x="600" y="325" text-anchor="middle" class="subtitle">四维实时反馈机制</text>
  
  <!-- 颜色反馈 -->
  <rect x="80" y="350" width="250" height="180" fill="#fff" stroke="#4CAF50" stroke-width="2" rx="8"/>
  <text x="205" y="375" text-anchor="middle" class="label">1. 颜色反馈</text>
  
  <!-- 绿色头像 -->
  <circle cx="130" cy="420" r="25" fill="#4CAF50"/>
  <circle cx="120" cy="410" r="3" fill="#fff"/>
  <circle cx="140" cy="410" r="3" fill="#fff"/>
  <path d="M 120 430 Q 130 440 140 430" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="130" y="455" text-anchor="middle" class="small-text green-status">正常姿势</text>
  
  <!-- 黄色头像 -->
  <circle cx="205" cy="420" r="25" fill="#FF9800"/>
  <circle cx="195" cy="410" r="3" fill="#fff"/>
  <circle cx="215" cy="410" r="3" fill="#fff"/>
  <path d="M 195 430 Q 205 435 215 430" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="205" y="455" text-anchor="middle" class="small-text yellow-status">轻微偏差</text>
  
  <!-- 红色头像 -->
  <circle cx="280" cy="420" r="25" fill="#F44336"/>
  <circle cx="270" cy="410" r="3" fill="#fff"/>
  <circle cx="290" cy="410" r="3" fill="#fff"/>
  <path d="M 270 430 Q 280 425 290 430" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="280" y="455" text-anchor="middle" class="small-text red-status">严重偏差</text>
  
  <!-- 大小反馈 -->
  <rect x="350" y="350" width="200" height="180" fill="#fff" stroke="#2196F3" stroke-width="2" rx="8"/>
  <text x="450" y="375" text-anchor="middle" class="label">2. 大小反馈</text>
  
  <!-- 不同大小的头像 -->
  <circle cx="380" cy="420" r="15" fill="#FFB74D"/>
  <text x="380" y="445" text-anchor="middle" class="small-text">太远 (50%)</text>
  
  <circle cx="450" cy="420" r="25" fill="#FFB74D"/>
  <text x="450" y="455" text-anchor="middle" class="small-text">正常 (100%)</text>
  
  <circle cx="520" cy="420" r="35" fill="#FFB74D"/>
  <text x="520" y="470" text-anchor="middle" class="small-text">太近 (150%)</text>
  
  <!-- 位置反馈 -->
  <rect x="570" y="350" width="200" height="180" fill="#fff" stroke="#9C27B0" stroke-width="2" rx="8"/>
  <text x="670" y="375" text-anchor="middle" class="label">3. 位置反馈</text>
  
  <!-- 参考位置 -->
  <circle cx="670" cy="420" r="20" fill="#E0E0E0" stroke="#999" stroke-width="2" stroke-dasharray="3,3"/>
  <text x="670" y="430" text-anchor="middle" class="small-text">参考</text>
  
  <!-- 实际位置 -->
  <circle cx="620" cy="400" r="15" fill="#9C27B0"/>
  <text x="620" y="385" text-anchor="middle" class="small-text">实际位置</text>
  
  <!-- 箭头指示 -->
  <line x1="635" y1="400" x2="655" y2="410" class="arrow"/>
  <text x="645" y="395" text-anchor="middle" class="small-text">动态跟踪</text>
  
  <!-- 角度反馈 -->
  <rect x="790" y="350" width="200" height="180" fill="#fff" stroke="#FF5722" stroke-width="2" rx="8"/>
  <text x="890" y="375" text-anchor="middle" class="label">4. 角度反馈</text>
  
  <!-- 正常角度 -->
  <ellipse cx="850" cy="420" rx="20" ry="25" fill="#FFB74D"/>
  <text x="850" y="455" text-anchor="middle" class="small-text">正常 (0°)</text>
  
  <!-- 倾斜角度 -->
  <ellipse cx="930" cy="420" rx="20" ry="25" fill="#FFB74D" transform="rotate(15 930 420)"/>
  <text x="930" y="455" text-anchor="middle" class="small-text">倾斜 (15°)</text>
  
  <!-- 文字和语音提示 -->
  <rect x="80" y="550" width="450" height="180" fill="#fff" stroke="#607D8B" stroke-width="2" rx="8"/>
  <text x="305" y="575" text-anchor="middle" class="label">5. 文字 + 语音提示</text>
  
  <!-- 提示消息列表 -->
  <text x="100" y="600" class="small-text">• "请靠近一点" (FAR)</text>
  <text x="100" y="620" class="small-text">• "请远离一点" (NEARLY)</text>
  <text x="100" y="640" class="small-text">• "请向右转" (LEFT)</text>
  <text x="100" y="660" class="small-text">• "请向左转" (RIGHT)</text>
  <text x="300" y="600" class="small-text">• "请向下移动" (UP)</text>
  <text x="300" y="620" class="small-text">• "请向上移动" (DOWN)</text>
  <text x="300" y="640" class="small-text">• "请不要倾斜头部" (SKEW)</text>
  <text x="300" y="660" class="small-text">• "请保持当前位置" (正常)</text>
  
  <!-- 语音文件 -->
  <rect x="100" y="680" width="400" height="40" fill="#E8F5E8" stroke="#4CAF50" rx="5"/>
  <text x="300" y="700" text-anchor="middle" class="small-text">🔊 语音文件: calibration_return_correct_position.wav</text>
  
  <!-- 技术实现 -->
  <rect x="550" y="550" width="600" height="180" fill="#fff" stroke="#795548" stroke-width="2" rx="8"/>
  <text x="850" y="575" text-anchor="middle" class="label">6. 技术实现细节</text>
  
  <!-- 实现代码片段 -->
  <rect x="570" y="590" width="560" height="130" fill="#f5f5f5" stroke="#ccc" rx="3"/>
  <text x="580" y="610" class="small-text">// 实时更新头像属性</text>
  <text x="580" y="625" class="small-text">ivRealLocation.layoutParams.width = (width * scale).toInt()</text>
  <text x="580" y="640" class="small-text">ivRealLocation.x = centerX - width / 2</text>
  <text x="580" y="655" class="small-text">ivRealLocation.y = centerY - height / 2</text>
  <text x="580" y="670" class="small-text">ivRealLocation.rotation = rotationAngle.toFloat()</text>
  <text x="580" y="685" class="small-text">ivRealLocation.setImageResource(colorResource)</text>
  <text x="580" y="700" class="small-text">tvCorrectionPrompt.text = getString(promptResource)</text>
  
  <!-- 连接线 -->
  <line x1="350" y1="160" x2="400" y2="160" class="arrow"/>
  <line x1="575" y1="280" x2="600" y2="320" class="arrow"/>
  
  <!-- 检测频率说明 -->
  <rect x="1020" y="350" width="150" height="100" fill="#FFF3E0" stroke="#FF9800" stroke-width="1" rx="5"/>
  <text x="1095" y="375" text-anchor="middle" class="label">实时性</text>
  <text x="1095" y="395" text-anchor="middle" class="small-text">检测频率:</text>
  <text x="1095" y="410" text-anchor="middle" class="small-text">30-60 FPS</text>
  <text x="1095" y="425" text-anchor="middle" class="small-text">响应延迟:</text>
  <text x="1095" y="440" text-anchor="middle" class="small-text">< 50ms</text>
</svg>
