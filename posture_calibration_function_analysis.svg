<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="javaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="cppGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="algorithmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">gazeTrack.startPostureCalibration() 功能详解</text>
  
  <!-- 核心作用概述 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心作用：启动C++层的姿势校准算法，初始化人脸检测和眼部定位系统</text>
  <text x="70" y="110" class="text">• 激活底层计算机视觉算法进行实时人脸检测和眼部追踪</text>
  <text x="70" y="125" class="text">• 建立用户标准姿势基准，为后续眼动追踪提供准确的坐标系统</text>
  
  <!-- 调用链路图 -->
  <rect x="50" y="160" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">调用链路分析</text>
  
  <!-- Java层调用链 -->
  <rect x="80" y="210" width="200" height="80" rx="8" fill="url(#javaGradient)"/>
  <text x="180" y="235" text-anchor="middle" class="subtitle">CalibrationActivity</text>
  <text x="180" y="255" text-anchor="middle" class="code">startPostureCalibration()</text>
  <text x="180" y="275" text-anchor="middle" class="text">发送消息到Service</text>
  
  <rect x="320" y="210" width="200" height="80" rx="8" fill="url(#javaGradient)"/>
  <text x="420" y="235" text-anchor="middle" class="subtitle">GazeTrackService</text>
  <text x="420" y="255" text-anchor="middle" class="code">MSG_START_POSTURE_CALIBRATION</text>
  <text x="420" y="275" text-anchor="middle" class="text">处理校准消息</text>
  
  <rect x="560" y="210" width="200" height="80" rx="8" fill="url(#javaGradient)"/>
  <text x="660" y="235" text-anchor="middle" class="subtitle">TrackingManager</text>
  <text x="660" y="255" text-anchor="middle" class="code">startPostureCalibration()</text>
  <text x="660" y="275" text-anchor="middle" class="text">管理校准状态</text>
  
  <rect x="800" y="210" width="200" height="80" rx="8" fill="url(#javaGradient)"/>
  <text x="900" y="235" text-anchor="middle" class="subtitle">GazeTrack</text>
  <text x="900" y="255" text-anchor="middle" class="code">startPostureCalibration()</text>
  <text x="900" y="275" text-anchor="middle" class="text">Java-JNI接口</text>
  
  <!-- JNI层 -->
  <rect x="1040" y="210" width="200" height="80" rx="8" fill="url(#cppGradient)"/>
  <text x="1140" y="235" text-anchor="middle" class="subtitle">JNI Bridge</text>
  <text x="1140" y="255" text-anchor="middle" class="code">nativeStartPostureCalibration</text>
  <text x="1140" y="275" text-anchor="middle" class="text">原生方法调用</text>
  
  <!-- C++层 -->
  <rect x="1280" y="210" width="200" height="80" rx="8" fill="url(#cppGradient)"/>
  <text x="1380" y="235" text-anchor="middle" class="subtitle">GazeService</text>
  <text x="1380" y="255" text-anchor="middle" class="code">start_aligning_head_pose()</text>
  <text x="1380" y="275" text-anchor="middle" class="text">C++核心算法</text>
  
  <!-- 连接箭头 -->
  <line x1="280" y1="250" x2="320" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="520" y1="250" x2="560" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="760" y1="250" x2="800" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1000" y1="250" x2="1040" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1240" y1="250" x2="1280" y2="250" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 参数传递 -->
  <text x="300" y="240" text-anchor="middle" class="note">isCorrection</text>
  <text x="540" y="240" text-anchor="middle" class="note">context</text>
  <text x="780" y="240" text-anchor="middle" class="note">boolean</text>
  <text x="1020" y="240" text-anchor="middle" class="note">jlong thiz</text>
  <text x="1260" y="240" text-anchor="middle" class="note">bool calib</text>
  
  <!-- 状态变化 -->
  <rect x="80" y="320" width="1400" height="120" rx="8" fill="#f8f9fa"/>
  <text x="780" y="345" text-anchor="middle" class="subtitle">系统状态变化</text>
  
  <text x="100" y="370" class="text"><tspan class="highlight">1. 服务模式切换:</tspan> SERVICE_NONE → SERVICE_POSE_ALIGN</text>
  <text x="100" y="390" class="text"><tspan class="highlight">2. 算法初始化:</tspan> pose_align_class.start_aligning_head_pose()</text>
  <text x="100" y="410" class="text"><tspan class="highlight">3. 视频保存:</tspan> 如果是校准模式(calib=true)，启动视频录制</text>
  <text x="100" y="430" class="text"><tspan class="highlight">4. 回调通知:</tspan> onGazeServiceModeChange(SERVICE_POSE_ALIGN)</text>
  
  <!-- C++核心算法详解 -->
  <rect x="50" y="480" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="505" text-anchor="middle" class="subtitle">C++核心算法实现</text>
  
  <!-- 算法模块 -->
  <rect x="80" y="530" width="300" height="120" rx="8" fill="url(#algorithmGradient)"/>
  <text x="230" y="555" text-anchor="middle" class="subtitle">人脸检测模块</text>
  <text x="230" y="575" text-anchor="middle" class="highlight">faceDetect</text>
  <text x="100" y="595" class="text">• SCRFD模型检测人脸区域</text>
  <text x="100" y="615" class="text">• 定位左右眼区域</text>
  <text x="100" y="635" class="text">• 计算人脸关键点</text>
  
  <rect x="420" y="530" width="300" height="120" rx="8" fill="url(#algorithmGradient)"/>
  <text x="570" y="555" text-anchor="middle" class="subtitle">眼部追踪模块</text>
  <text x="570" y="575" text-anchor="middle" class="highlight">eyeTrack</text>
  <text x="440" y="595" class="text">• 双眼区域分割</text>
  <text x="440" y="615" class="text">• 瞳孔中心定位</text>
  <text x="440" y="635" class="text">• 虹膜边界检测</text>
  
  <rect x="760" y="530" width="300" height="120" rx="8" fill="url(#algorithmGradient)"/>
  <text x="910" y="555" text-anchor="middle" class="subtitle">光斑检测模块</text>
  <text x="910" y="575" text-anchor="middle" class="highlight">lightsDetect</text>
  <text x="780" y="595" class="text">• 普尔钦斑检测</text>
  <text x="780" y="615" class="text">• 瞳孔中心精确定位</text>
  <text x="780" y="635" class="text">• 光照条件评估</text>
  
  <rect x="1100" y="530" width="300" height="120" rx="8" fill="url(#algorithmGradient)"/>
  <text x="1250" y="555" text-anchor="middle" class="subtitle">姿势对齐模块</text>
  <text x="1250" y="575" text-anchor="middle" class="highlight">PoseAlign</text>
  <text x="1120" y="595" class="text">• 瞳距计算和验证</text>
  <text x="1120" y="615" class="text">• 头部姿势稳定性检测</text>
  <text x="1120" y="635" class="text">• 距离和角度评估</text>
  
  <!-- 姿势校准算法流程 -->
  <rect x="80" y="670" width="1400" height="180" rx="8" fill="#f8f9fa"/>
  <text x="780" y="695" text-anchor="middle" class="subtitle">姿势校准算法流程</text>
  
  <text x="100" y="720" class="highlight">1. 初始化阶段</text>
  <text x="120" y="740" class="text">• 重置校准参数：pose_preview_eye_points, countdown, finish_flag</text>
  <text x="120" y="755" class="text">• 设置倒计时：cunt_down_second_pose_align (通常3-5秒)</text>
  <text x="120" y="770" class="text">• 启动时间记录：pose_align_time = Clock::now()</text>
  
  <text x="100" y="795" class="highlight">2. 实时检测循环</text>
  <text x="120" y="815" class="text">• 每帧图像进行人脸和眼部检测：detection(Image)</text>
  <text x="120" y="830" class="text">• 计算瞳距和虹膜比例：interpupillary_distance, iris_width_ratio</text>
  <text x="120" y="845" class="text">• 姿势有效性判断：recognize_pose_valid(det_result, false)</text>
  
  <!-- 关键参数说明 -->
  <rect x="50" y="900" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="925" text-anchor="middle" class="subtitle">关键参数和判断逻辑</text>
  
  <!-- 距离判断 -->
  <rect x="80" y="950" width="400" height="120" rx="8" fill="#fff3e0"/>
  <text x="280" y="975" text-anchor="middle" class="subtitle">距离判断算法</text>
  <text x="100" y="995" class="code">expect_interpupillary_distance / det_result.interpupillary_distance</text>
  <text x="100" y="1015" class="text">• 基于瞳距计算用户与屏幕的距离</text>
  <text x="100" y="1035" class="text">• 通过sigmoid函数归一化到[0,1]区间</text>
  <text x="100" y="1055" class="text">• 0.45-0.55为理想距离范围</text>
  
  <!-- 稳定性判断 -->
  <rect x="520" y="950" width="400" height="120" rx="8" fill="#e8f5e8"/>
  <text x="720" y="975" text-anchor="middle" class="subtitle">稳定性判断</text>
  <text x="540" y="995" class="code">diff = cur_p - pose_preview_eye_points</text>
  <text x="540" y="1015" class="text">• 计算当前眼部位置与上一帧的差异</text>
  <text x="540" y="1035" class="text">• diff < pose_stable_move_min 表示稳定</text>
  <text x="540" y="1055" class="text">• 稳定状态下开始倒计时</text>
  
  <!-- 对齐判断 -->
  <rect x="960" y="950" width="400" height="120" rx="8" fill="#e3f2fd"/>
  <text x="1160" y="975" text-anchor="middle" class="subtitle">姿势对齐判断</text>
  <text x="980" y="995" class="code">recognize_pose_valid(det_result, false)</text>
  <text x="980" y="1015" class="text">• 检查头部是否在合适角度</text>
  <text x="980" y="1035" class="text">• 验证双眼虹膜大小比例</text>
  <text x="980" y="1055" class="text">• 确保光照条件充足</text>
  
  <!-- 完成条件 -->
  <rect x="80" y="1090" width="1400" height="80" rx="8" fill="#d4edda"/>
  <text x="780" y="1115" text-anchor="middle" class="subtitle">校准完成条件</text>
  <text x="100" y="1140" class="text"><tspan class="highlight">完成条件:</tspan> pose_result.aligning = true AND diff < pose_stable_move_min AND remaining = 0</text>
  <text x="100" y="1160" class="text"><tspan class="highlight">结果输出:</tspan> pose_result.pose_aligned = true, finish_flag = true, processing_state = SERVICE_NONE</text>
  
  <!-- 返回数据结构 -->
  <rect x="50" y="1190" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1215" text-anchor="middle" class="subtitle">返回数据结构 (PostureCalibrationResult)</text>
  
  <rect x="80" y="1240" width="300" height="120" rx="5" fill="#f8f9fa"/>
  <text x="230" y="1260" text-anchor="middle" class="subtitle">状态字段</text>
  <text x="100" y="1280" class="text">• state: 校准是否完成</text>
  <text x="100" y="1300" class="text">• aligned: 当前姿势是否正确</text>
  <text x="100" y="1320" class="text">• countdown: 倒计时剩余秒数</text>
  <text x="100" y="1340" class="text">• dist: 距离评分 [0,1]</text>
  
  <rect x="420" y="1240" width="300" height="120" rx="5" fill="#f8f9fa"/>
  <text x="570" y="1260" text-anchor="middle" class="subtitle">位置坐标</text>
  <text x="440" y="1280" class="text">• leftX, leftY: 左眼坐标 [0,1]</text>
  <text x="440" y="1300" class="text">• rightX, rightY: 右眼坐标 [0,1]</text>
  <text x="440" y="1320" class="text">• 归一化屏幕坐标系统</text>
  <text x="440" y="1340" class="text">• 用于UI显示和姿势引导</text>
  
  <!-- 总结 -->
  <rect x="100" y="1410" width="1600" height="60" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1435" text-anchor="middle" class="highlight">
    总结：gazeTrack.startPostureCalibration() 启动了完整的计算机视觉管道
  </text>
  <text x="900" y="1455" text-anchor="middle" class="text">
    从人脸检测到眼部追踪，建立准确的用户姿势基准，为后续眼动追踪提供坐标系统基础
  </text>
</svg>
