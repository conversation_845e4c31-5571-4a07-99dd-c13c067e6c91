<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="distanceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="positionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="tiltGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="bugGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef9a9a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">createPostureException 姿势异常检测详解</text>
  
  <!-- 核心概述 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心功能：多维度检测用户姿势异常，按优先级返回最严重的异常类型</text>
  <text x="70" y="110" class="text">• 检测距离、水平位置、垂直位置、头部倾斜四个维度</text>
  <text x="70" y="125" class="text">• 每种异常分为两个严重程度：degree 1(轻微) 和 degree 2(严重)</text>
  
  <!-- 检测维度概览 -->
  <rect x="50" y="160" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">四个检测维度概览</text>
  
  <!-- 距离检测 -->
  <rect x="80" y="210" width="400" height="120" rx="8" fill="url(#distanceGradient)"/>
  <text x="280" y="235" text-anchor="middle" class="subtitle">1. 距离检测</text>
  <text x="280" y="255" text-anchor="middle" class="highlight">基于 data.dist</text>
  
  <text x="100" y="280" class="text">• <tspan class="highlight">NEARLY度2:</tspan> dist < 0.4 (太近)</text>
  <text x="100" y="300" class="text">• <tspan class="highlight">NEARLY度1:</tspan> 0.4 ≤ dist < 0.45</text>
  <text x="100" y="320" class="text">• <tspan class="highlight">FAR度1:</tspan> 0.55 < dist ≤ 0.6</text>
  
  <!-- 水平位置检测 -->
  <rect x="520" y="210" width="400" height="120" rx="8" fill="url(#positionGradient)"/>
  <text x="720" y="235" text-anchor="middle" class="subtitle">2. 水平位置检测</text>
  <text x="720" y="255" text-anchor="middle" class="highlight">基于 horizontalDistance</text>
  
  <text x="540" y="280" class="text">• <tspan class="highlight">LEFT度2:</tspan> < -384px (偏右太多)</text>
  <text x="540" y="300" class="text">• <tspan class="highlight">LEFT度1:</tspan> [-384, -192)px</text>
  <text x="540" y="320" class="text">• <tspan class="highlight">RIGHT度1:</tspan> (192, 384]px</text>
  
  <!-- 垂直位置检测 -->
  <rect x="960" y="210" width="400" height="120" rx="8" fill="url(#positionGradient)"/>
  <text x="1160" y="235" text-anchor="middle" class="subtitle">3. 垂直位置检测</text>
  <text x="1160" y="255" text-anchor="middle" class="highlight">基于 verticalDistance</text>
  
  <text x="980" y="280" class="text">• <tspan class="highlight">UP度2:</tspan> < -324px (偏下太多)</text>
  <text x="980" y="300" class="text">• <tspan class="highlight">UP度1:</tspan> [-324, -216)px</text>
  <text x="980" y="320" class="text">• <tspan class="highlight">DOWN度1:</tspan> (216, 324]px</text>
  
  <!-- 倾斜检测 -->
  <rect x="1400" y="210" width="320" height="120" rx="8" fill="url(#tiltGradient)"/>
  <text x="1560" y="235" text-anchor="middle" class="subtitle">4. 倾斜检测</text>
  <text x="1560" y="255" text-anchor="middle" class="highlight">基于 tiltAngle</text>
  
  <text x="1420" y="280" class="text">• <tspan class="highlight">SKEW度2:</tspan> |角度| > 15°</text>
  <text x="1420" y="300" class="text">• <tspan class="highlight">SKEW度1:</tspan> 10° < |角度| ≤ 15°</text>
  
  <!-- 距离检测详解 -->
  <rect x="50" y="380" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="405" text-anchor="middle" class="subtitle">1. 距离检测详解</text>
  
  <rect x="80" y="430" width="1600" height="180" rx="8" fill="url(#distanceGradient)"/>
  <text x="880" y="455" text-anchor="middle" class="subtitle">基于 data.dist 值判断用户与屏幕的距离</text>
  
  <text x="100" y="480" class="code">when {</text>
  <text x="120" y="500" class="code">data.dist < 0.4f -> NEARLY.apply { degree = 2 }           // 太近，严重</text>
  <text x="120" y="520" class="code">data.dist >= 0.4f && data.dist < 0.45f -> NEARLY.apply { degree = 1 }  // 较近，轻微</text>
  <text x="120" y="540" class="code">data.dist > 0.55f && data.dist <= 0.6f -> FAR.apply { degree = 1 }     // 较远，轻微</text>
  <text x="120" y="560" class="code">data.dist > 0.6f -> FAR.apply { degree = 2 }            // 太远，严重</text>
  <text x="100" y="580" class="code">}</text>
  
  <text x="900" y="480" class="text">• <tspan class="highlight">理想距离:</tspan> 0.45 ≤ dist ≤ 0.55 (约50cm)</text>
  <text x="900" y="500" class="text">• <tspan class="highlight">dist值来源:</tspan> C++层distance_sigmoid()函数输出</text>
  <text x="900" y="520" class="text">• <tspan class="highlight">计算基础:</tspan> 基于瞳距比例的距离估算</text>
  <text x="900" y="540" class="text">• <tspan class="highlight">用户反馈:</tspan> "请向前/后移动"</text>
  
  <!-- 水平位置检测详解 -->
  <rect x="50" y="650" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="675" text-anchor="middle" class="subtitle">2. 水平位置检测详解</text>
  
  <rect x="80" y="700" width="1600" height="230" rx="8" fill="url(#positionGradient)"/>
  <text x="880" y="725" text-anchor="middle" class="subtitle">基于眼部中心点与参考点的水平偏移</text>
  
  <text x="100" y="750" class="code">// 计算眼部中心点</text>
  <text x="100" y="770" class="code">val centerX = (data.leftX + (data.rightX - data.leftX) / 2) * screenWidth</text>
  <text x="100" y="790" class="code">val horizontalDistance = centerX - referenceX * screenWidth</text>
  
  <text x="100" y="820" class="code">when {</text>
  <text x="120" y="840" class="code">horizontalDistance < -384 -> LEFT.apply { degree = 2 }      // 用户偏右太多，需要向右移动</text>
  <text x="120" y="860" class="code">horizontalDistance >= -384 && horizontalDistance < -192 -> LEFT.apply { degree = 1 }</text>
  <text x="120" y="880" class="code">horizontalDistance > 192 && horizontalDistance <= 384 -> RIGHT.apply { degree = 1 }</text>
  <text x="120" y="900" class="code">horizontalDistance > 384 -> RIGHT.apply { degree = 2 }     // 用户偏左太多，需要向左移动</text>
  <text x="100" y="920" class="code">}</text>
  
  <text x="900" y="750" class="text">• <tspan class="highlight">参考点:</tspan> referenceX = 0.5 (屏幕中心)</text>
  <text x="900" y="770" class="text">• <tspan class="highlight">负值:</tspan> 用户在屏幕左侧，提示"向右移动"</text>
  <text x="900" y="790" class="text">• <tspan class="highlight">正值:</tspan> 用户在屏幕右侧，提示"向左移动"</text>
  <text x="900" y="810" class="text">• <tspan class="highlight">阈值:</tspan> 192px(轻微) / 384px(严重)</text>
  
  <!-- 垂直位置检测详解 + BUG发现 -->
  <rect x="50" y="970" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="995" text-anchor="middle" class="subtitle">3. 垂直位置检测详解 + BUG发现</text>
  
  <rect x="80" y="1020" width="800" height="230" rx="8" fill="url(#positionGradient)"/>
  <text x="480" y="1045" text-anchor="middle" class="subtitle">正确的垂直位置检测逻辑</text>
  
  <text x="100" y="1070" class="code">// 计算垂直偏移</text>
  <text x="100" y="1090" class="code">val centerY = (data.leftY + (data.rightY - data.leftY) / 2) * screenHeight</text>
  <text x="100" y="1110" class="code">val verticalDistance = centerY - referenceY * screenHeight  // 应该用screenHeight</text>
  
  <text x="100" y="1140" class="code">when {</text>
  <text x="120" y="1160" class="code">verticalDistance < -324 -> UP.apply { degree = 2 }</text>
  <text x="120" y="1180" class="code">verticalDistance >= -324 && verticalDistance < -216 -> UP.apply { degree = 1 }</text>
  <text x="120" y="1200" class="code">verticalDistance > 216 && verticalDistance <= 324 -> DOWN.apply { degree = 1 }</text>
  <text x="120" y="1220" class="code">verticalDistance > 324 -> DOWN.apply { degree = 2 }</text>
  <text x="100" y="1240" class="code">}</text>
  
  <!-- BUG警告 -->
  <rect x="920" y="1020" width="760" height="230" rx="8" fill="url(#bugGradient)" stroke="#f44336" stroke-width="3"/>
  <text x="1300" y="1045" text-anchor="middle" class="subtitle">⚠️ 代码BUG发现</text>
  
  <text x="940" y="1070" class="highlight">原代码中的错误：</text>
  <text x="940" y="1090" class="code">val verticalDistance = centerY - referenceY * screenWidth  // 错误！</text>
  <text x="940" y="1110" class="text">应该使用 screenHeight 而不是 screenWidth</text>
  
  <text x="940" y="1140" class="highlight">判断条件中的错误：</text>
  <text x="940" y="1160" class="code">horizontalDistance >= -324 && horizontalDistance < -216  // 错误！</text>
  <text x="940" y="1180" class="text">应该使用 verticalDistance 而不是 horizontalDistance</text>
  
  <text x="940" y="1210" class="highlight">影响：</text>
  <text x="940" y="1230" class="text">• 垂直位置检测可能不准确</text>
  
  <!-- 倾斜检测详解 -->
  <rect x="50" y="1290" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1315" text-anchor="middle" class="subtitle">4. 倾斜检测详解</text>
  
  <rect x="80" y="1340" width="1600" height="180" rx="8" fill="url(#tiltGradient)"/>
  <text x="880" y="1365" text-anchor="middle" class="subtitle">基于双眼连线与水平线的夹角</text>
  
  <text x="100" y="1390" class="code">// 计算倾斜角度</text>
  <text x="100" y="1410" class="code">val tiltAngle = calculateAngleOfTilt(</text>
  <text x="120" y="1430" class="code">PointF(data.leftX * screenWidth, data.leftY * screenHeight),</text>
  <text x="120" y="1450" class="code">PointF(data.rightX * screenWidth, data.rightY * screenHeight)</text>
  <text x="100" y="1470" class="code">)</text>
  
  <text x="100" y="1500" class="code">when {</text>
  <text x="120" y="1520" class="code">tiltAngle < -15f || tiltAngle > 15f -> SKEW.apply { degree = 2 }  // 严重倾斜</text>
  
  <text x="900" y="1390" class="text">• <tspan class="highlight">计算方法:</tspan> atan2(deltaY, deltaX) * 180 / π</text>
  <text x="900" y="1410" class="text">• <tspan class="highlight">正值:</tspan> 头部向右倾斜</text>
  <text x="900" y="1430" class="text">• <tspan class="highlight">负值:</tspan> 头部向左倾斜</text>
  <text x="900" y="1450" class="text">• <tspan class="highlight">阈值:</tspan> 10°(轻微) / 15°(严重)</text>
  <text x="900" y="1470" class="text">• <tspan class="highlight">用户反馈:</tspan> "请调整头部角度"</text>
  
  <!-- 优先级处理逻辑 -->
  <rect x="50" y="1560" width="1700" height="180" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1585" text-anchor="middle" class="subtitle">异常优先级处理逻辑</text>
  
  <rect x="80" y="1610" width="1600" height="110" rx="8" fill="#f8f9fa"/>
  <text x="880" y="1635" text-anchor="middle" class="subtitle">只保留最严重的异常 (degree值最大)</text>
  
  <text x="100" y="1660" class="code">if (postureException == null || postureException.degree < newDegree) {</text>
  <text x="120" y="1680" class="code">postureException = NEW_EXCEPTION.apply { degree = newDegree }</text>
  <text x="100" y="1700" class="code">}</text>
  
  <text x="900" y="1660" class="text">• <tspan class="highlight">优先级:</tspan> degree 2 > degree 1</text>
  <text x="900" y="1680" class="text">• <tspan class="highlight">覆盖规则:</tspan> 后检测的高优先级异常会覆盖前面的低优先级异常</text>
  <text x="900" y="1700" class="text">• <tspan class="highlight">返回结果:</tspan> 最终只返回一个最严重的异常类型</text>
  
  <!-- 总结 -->
  <rect x="100" y="1760" width="1600" height="30" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1780" text-anchor="middle" class="highlight">
    总结：多维度检测用户姿势异常，按严重程度优先级返回最需要调整的方向，但代码中存在垂直检测的BUG
  </text>
</svg>
