<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="cppGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="websocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadBlue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">位置偏移检测与UI引导完整流程</text>
  
  <!-- 核心问题 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心机制：C++计算偏移 → WebSocket传输 → UI层解析 → 视觉引导</text>
  <text x="70" y="110" class="text">• 实时计算眼部位置偏移量，通过WebSocket发送到UI层</text>
  <text x="70" y="125" class="text">• UI层根据偏移数据提供"向左/右/上/下/前/后"的视觉和语音引导</text>
  
  <!-- 完整数据流程 -->
  <rect x="50" y="160" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">完整数据流程图</text>
  
  <!-- C++层计算 -->
  <rect x="80" y="210" width="300" height="120" rx="8" fill="url(#cppGradient)" stroke="#ff9800" stroke-width="2"/>
  <text x="230" y="235" text-anchor="middle" class="subtitle">C++层偏移计算</text>
  <text x="230" y="255" text-anchor="middle" class="highlight">poseAlign.cpp</text>
  
  <text x="100" y="280" class="code">left_distance = sqrt((eye_left_x - refer_x)² + (eye_left_y - refer_y)²)</text>
  <text x="100" y="295" class="code">right_distance = sqrt((eye_right_x - refer_x)² + (eye_right_y - refer_y)²)</text>
  <text x="100" y="315" class="text">• 计算双眼与参考点的欧氏距离</text>
  
  <!-- WebSocket传输 -->
  <rect x="420" y="210" width="300" height="120" rx="8" fill="url(#websocketGradient)" stroke="#2196f3" stroke-width="2"/>
  <text x="570" y="235" text-anchor="middle" class="subtitle">WebSocket传输</text>
  <text x="570" y="255" text-anchor="middle" class="highlight">GazeTrackService</text>
  
  <text x="440" y="280" class="code">ACTION_POSTURE_CALIBRATION_RESULT</text>
  <text x="440" y="295" class="code">PostureCalibrationResult</text>
  <text x="440" y="315" class="text">• 实时广播姿势校准结果</text>
  
  <!-- UI层解析 -->
  <rect x="760" y="210" width="300" height="120" rx="8" fill="url(#uiGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="910" y="235" text-anchor="middle" class="subtitle">UI层解析引导</text>
  <text x="910" y="255" text-anchor="middle" class="highlight">PostureCalibrationView</text>
  
  <text x="780" y="280" class="code">createPostureException(data)</text>
  <text x="780" y="295" class="code">handlePostureException()</text>
  <text x="780" y="315" class="text">• 计算偏移方向和程度</text>
  
  <!-- 视觉反馈 -->
  <rect x="1100" y="210" width="300" height="120" rx="8" fill="url(#uiGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="1250" y="235" text-anchor="middle" class="subtitle">视觉语音反馈</text>
  <text x="1250" y="255" text-anchor="middle" class="highlight">实时引导</text>
  
  <text x="1120" y="280" class="text">• 头像位置和颜色变化</text>
  <text x="1120" y="295" class="text">• 文字提示和语音播放</text>
  <text x="1120" y="315" class="text">• 缩放和旋转动画</text>
  
  <!-- 连接箭头 -->
  <line x1="380" y1="270" x2="420" y2="270" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="720" y1="270" x2="760" y2="270" stroke="#2196f3" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
  <line x1="1060" y1="270" x2="1100" y2="270" stroke="#4caf50" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 数据结构详解 -->
  <rect x="80" y="350" width="1320" height="180" rx="8" fill="#f8f9fa"/>
  <text x="740" y="375" text-anchor="middle" class="subtitle">PostureCalibrationResult 数据结构</text>
  
  <text x="100" y="400" class="code">data class PostureCalibrationResult(</text>
  <text x="120" y="420" class="code">var leftX: Float = -1f,    // 左眼X坐标 [0,1]</text>
  <text x="120" y="435" class="code">var leftY: Float = -1f,    // 左眼Y坐标 [0,1]</text>
  <text x="120" y="450" class="code">var rightX: Float = -1f,   // 右眼X坐标 [0,1]</text>
  <text x="120" y="465" class="code">var rightY: Float = -1f,   // 右眼Y坐标 [0,1]</text>
  <text x="120" y="480" class="code">var aligned: Boolean = false, // 当前姿势是否正确</text>
  <text x="120" y="495" class="code">var dist: Float = -1f,     // 距离评分 [0,1]</text>
  <text x="120" y="510" class="code">)</text>
  
  <text x="700" y="400" class="text">• <tspan class="highlight">leftX/Y, rightX/Y:</tspan> 归一化眼部坐标</text>
  <text x="700" y="420" class="text">• <tspan class="highlight">aligned:</tspan> 姿势是否在允许范围内</text>
  <text x="700" y="440" class="text">• <tspan class="highlight">dist:</tspan> 距离评分，0.5为理想距离</text>
  <text x="700" y="460" class="text">• 通过WebSocket实时传输到UI层</text>
  <text x="700" y="480" class="text">• UI层根据这些数据计算偏移方向</text>
  
  <!-- UI层偏移计算详解 -->
  <rect x="50" y="580" width="1700" height="450" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="605" text-anchor="middle" class="subtitle">UI层偏移计算与引导逻辑</text>
  
  <!-- 水平偏移计算 -->
  <rect x="80" y="630" width="800" height="180" rx="8" fill="#fff3e0"/>
  <text x="480" y="655" text-anchor="middle" class="subtitle">水平偏移计算 (左右调整)</text>
  
  <text x="100" y="680" class="code">// 计算双眼中心点</text>
  <text x="100" y="695" class="code">val centerX = (data.leftX + (data.rightX - data.leftX) / 2) * screenWidth</text>
  <text x="100" y="710" class="code">val centerY = (data.leftY + (data.rightY - data.leftY) / 2) * screenHeight</text>
  
  <text x="100" y="735" class="code">// 计算水平偏移距离</text>
  <text x="100" y="750" class="code">val horizontalDistance = centerX - referenceX * screenWidth</text>
  
  <text x="100" y="775" class="text">• <tspan class="highlight">horizontalDistance < -384:</tspan> 向右移动 (程度2)</text>
  <text x="100" y="790" class="text">• <tspan class="highlight">[-384, -192):</tspan> 向右移动 (程度1)</text>
  <text x="100" y="805" class="text">• <tspan class="highlight">[192, 384]:</tspan> 向左移动 (程度1)</text>
  
  <!-- 垂直偏移计算 -->
  <rect x="920" y="630" width="800" height="180" rx="8" fill="#e8f5e8"/>
  <text x="1320" y="655" text-anchor="middle" class="subtitle">垂直偏移计算 (上下调整)</text>
  
  <text x="940" y="680" class="code">// 计算垂直偏移距离</text>
  <text x="940" y="695" class="code">val verticalDistance = centerY - referenceY * screenHeight</text>
  
  <text x="940" y="720" class="text">• <tspan class="highlight">verticalDistance < -324:</tspan> 向下移动 (程度2)</text>
  <text x="940" y="735" class="text">• <tspan class="highlight">[-324, -216):</tspan> 向下移动 (程度1)</text>
  <text x="940" y="750" class="text">• <tspan class="highlight">[216, 324]:</tspan> 向上移动 (程度1)</text>
  <text x="940" y="765" class="text">• <tspan class="highlight">> 324:</tspan> 向上移动 (程度2)</text>
  
  <text x="940" y="790" class="note">注意：代码中verticalDistance计算可能有bug，</text>
  <text x="940" y="805" class="note">应该使用screenHeight而不是screenWidth</text>
  
  <!-- 距离偏移计算 -->
  <rect x="80" y="830" width="800" height="180" rx="8" fill="#e3f2fd"/>
  <text x="480" y="855" text-anchor="middle" class="subtitle">距离偏移计算 (前后调整)</text>
  
  <text x="100" y="880" class="code">// 距离评分解析</text>
  <text x="100" y="895" class="code">when {</text>
  <text x="120" y="910" class="code">data.dist < 0.4f -> NEARLY (太近，需要后退)</text>
  <text x="120" y="925" class="code">data.dist in 0.45f..0.55f -> 理想距离</text>
  <text x="120" y="940" class="code">data.dist > 0.6f -> FAR (太远，需要前进)</text>
  <text x="100" y="955" class="code">}</text>
  
  <text x="100" y="980" class="text">• 通过头像缩放反映距离变化</text>
  <text x="100" y="995" class="text">• scale = 0.5f / data.dist (限制在0.5-1.5范围)</text>
  
  <!-- 视觉反馈实现 -->
  <rect x="920" y="830" width="800" height="180" rx="8" fill="#f3e5f5"/>
  <text x="1320" y="855" text-anchor="middle" class="subtitle">视觉反馈实现</text>
  
  <text x="940" y="880" class="code">// 头像位置调整</text>
  <text x="940" y="895" class="code">ivRealLocation.translationX = centerX - avatarOriginalW / 2</text>
  <text x="940" y="910" class="code">ivRealLocation.translationY = centerY - avatarOriginalH / 2</text>
  
  <text x="940" y="935" class="code">// 头像颜色状态</text>
  <text x="940" y="950" class="code">if (data.aligned) {</text>
  <text x="960" y="965" class="code">setImageResource(R.drawable.icon_avatar_green) // 绿色</text>
  <text x="940" y="980" class="code">} else {</text>
  <text x="960" y="995" class="code">setImageResource(R.drawable.icon_avatar_red)   // 红色</text>
  
  <!-- WebSocket传输机制 -->
  <rect x="50" y="1050" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1075" text-anchor="middle" class="subtitle">WebSocket传输机制详解</text>
  
  <text x="80" y="1105" class="highlight">1. 服务端广播 (GazeTrackService)</text>
  <text x="100" y="1125" class="code">override fun onPostureCalibration(result: PostureCalibrationResult) {</text>
  <text x="120" y="1140" class="code">val gazeMessage = GazeMessage&lt;PostureCalibrationResult&gt;().apply {</text>
  <text x="140" y="1155" class="code">action = GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT</text>
  <text x="140" y="1170" class="code">data = result</text>
  <text x="120" y="1185" class="code">}</text>
  <text x="120" y="1200" class="code">mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))</text>
  <text x="100" y="1215" class="code">}</text>
  
  <text x="900" y="1105" class="highlight">2. 客户端接收 (CalibrationActivity)</text>
  <text x="920" y="1125" class="code">override fun onMessage(message: String?) {</text>
  <text x="940" y="1140" class="code">val action = jsonObject.opt("action")</text>
  <text x="940" y="1155" class="code">when(action) {</text>
  <text x="960" y="1170" class="code">GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT -> {</text>
  <text x="980" y="1185" class="code">val result = gson.fromJson(data, PostureCalibrationResult::class.java)</text>
  <text x="980" y="1200" class="code">mCalibrationViewModel.setPostureCalibrationResult(result)</text>
  <text x="960" y="1215" class="code">}</text>
  
  <!-- 引导效果展示 -->
  <rect x="50" y="1270" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1295" text-anchor="middle" class="subtitle">引导效果展示</text>
  
  <!-- 文字提示 -->
  <rect x="80" y="1320" width="400" height="180" rx="8" fill="#fff3e0"/>
  <text x="280" y="1345" text-anchor="middle" class="subtitle">文字提示</text>
  
  <text x="100" y="1370" class="text">• <tspan class="highlight">LEFT:</tspan> "请向右移动"</text>
  <text x="100" y="1390" class="text">• <tspan class="highlight">RIGHT:</tspan> "请向左移动"</text>
  <text x="100" y="1410" class="text">• <tspan class="highlight">UP:</tspan> "请向下移动"</text>
  <text x="100" y="1430" class="text">• <tspan class="highlight">DOWN:</tspan> "请向上移动"</text>
  <text x="100" y="1450" class="text">• <tspan class="highlight">NEARLY:</tspan> "请向后移动"</text>
  <text x="100" y="1470" class="text">• <tspan class="highlight">FAR:</tspan> "请向前移动"</text>
  <text x="100" y="1490" class="text">• <tspan class="highlight">ALIGNED:</tspan> "请保持位置"</text>
  
  <!-- 语音播放 -->
  <rect x="520" y="1320" width="400" height="180" rx="8" fill="#e8f5e8"/>
  <text x="720" y="1345" text-anchor="middle" class="subtitle">语音播放</text>
  
  <text x="540" y="1370" class="code">PlayManager.playRawMedia(context, RawMedia(resId))</text>
  <text x="540" y="1390" class="text">• 根据偏移类型播放对应语音</text>
  <text x="540" y="1410" class="text">• 支持开关控制 (isOpenSound)</text>
  <text x="540" y="1430" class="text">• 实时语音引导用户调整姿势</text>
  
  <!-- 视觉动画 -->
  <rect x="960" y="1320" width="400" height="180" rx="8" fill="#e3f2fd"/>
  <text x="1160" y="1345" text-anchor="middle" class="subtitle">视觉动画</text>
  
  <text x="980" y="1370" class="text">• <tspan class="highlight">位置:</tspan> 头像跟随眼部位置移动</text>
  <text x="980" y="1390" class="text">• <tspan class="highlight">缩放:</tspan> 根据距离调整头像大小</text>
  <text x="980" y="1410" class="text">• <tspan class="highlight">旋转:</tspan> 根据头部倾斜角度旋转</text>
  <text x="980" y="1430" class="text">• <tspan class="highlight">颜色:</tspan> 绿色(正确) / 红色(需调整)</text>
  <text x="980" y="1450" class="text">• <tspan class="highlight">倒计时:</tspan> 稳定状态下显示倒计时</text>
  
  <!-- 总结 -->
  <rect x="100" y="1540" width="1600" height="40" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1565" text-anchor="middle" class="highlight">
    总结：通过实时计算眼部偏移 → WebSocket传输 → UI解析引导，实现精确的姿势调整指导
  </text>
</svg>
