<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="printGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="flipbeatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hardwareGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff8e1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#00000030"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
    
    <marker id="arrowheadBlue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1976D2"/>
    </marker>
    
    <marker id="arrowheadPurple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7B1FA2"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1200" fill="#fafafa"/>
  
  <!-- 标题 -->
  <text x="800" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    打印功能与翻转拍硬件控制流程图
  </text>
  
  <!-- 打印功能流程 (上半部分) -->
  <g>
    <rect x="50" y="80" width="1500" height="500" rx="15" fill="url(#printGradient)" filter="url(#shadow)"/>
    <text x="800" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1565C0">
      📄 打印功能使用流程
    </text>
    
    <!-- Web端触发 -->
    <rect x="80" y="140" width="200" height="80" rx="8" fill="#42a5f5"/>
    <text x="180" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      Web页面
    </text>
    <text x="180" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      检查报告页面
    </text>
    <text x="180" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      点击打印按钮
    </text>
    
    <!-- JavaScript调用 -->
    <rect x="320" y="140" width="200" height="80" rx="8" fill="#1e88e5"/>
    <text x="420" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      JavaScript调用
    </text>
    <text x="420" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      window.Inspection
    </text>
    <text x="420" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      .printPage()
    </text>
    
    <!-- Android接口处理 -->
    <rect x="560" y="140" width="200" height="80" rx="8" fill="#1976d2"/>
    <text x="660" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      Android接口
    </text>
    <text x="660" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      @JavascriptInterface
    </text>
    <text x="660" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      printPage()
    </text>
    
    <!-- 打印管理器 -->
    <rect x="800" y="140" width="200" height="80" rx="8" fill="#1565c0"/>
    <text x="900" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      PrintManager
    </text>
    <text x="900" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      createWebPrintJob()
    </text>
    <text x="900" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      系统打印服务
    </text>
    
    <!-- 打印输出 -->
    <rect x="1040" y="140" width="200" height="80" rx="8" fill="#0d47a1"/>
    <text x="1140" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      打印输出
    </text>
    <text x="1140" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      PDF文档生成
    </text>
    <text x="1140" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      A4彩色600DPI
    </text>
    
    <!-- 打印参数详情 -->
    <rect x="80" y="250" width="1160" height="120" rx="8" fill="#e3f2fd"/>
    <text x="660" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565C0">
      打印参数配置详情
    </text>
    
    <text x="100" y="300" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 媒体尺寸:</tspan> PrintAttributes.MediaSize.ISO_A4 (A4纸张)
    </text>
    <text x="100" y="320" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 颜色模式:</tspan> PrintAttributes.COLOR_MODE_COLOR (彩色打印)
    </text>
    <text x="100" y="340" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 分辨率:</tspan> 600x600 DPI (高质量打印)
    </text>
    <text x="100" y="360" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 页边距:</tspan> PrintAttributes.Margins.NO_MARGINS (无边距)
    </text>
    
    <text x="700" y="300" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 文档名称:</tspan> getString(R.string.app_name) + " Document"
    </text>
    <text x="700" y="320" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 适配器:</tspan> webView.createPrintDocumentAdapter()
    </text>
    <text x="700" y="340" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 异步处理:</tspan> lifecycleScope.launch 协程执行
    </text>
    <text x="700" y="360" font-family="Arial, sans-serif" font-size="12" fill="#1565C0">
      <tspan font-weight="bold">• 使用场景:</tspan> 检查报告、诊断结果打印输出
    </text>
    
    <!-- 代码示例 -->
    <rect x="80" y="390" width="1160" height="170" rx="8" fill="#bbdefb"/>
    <text x="660" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0d47a1">
      核心代码实现
    </text>
    
    <text x="100" y="440" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      private fun createWebPrintJob(webView: WebView) {
    </text>
    <text x="120" y="455" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      val printManager = getSystemService(PRINT_SERVICE) as PrintManager
    </text>
    <text x="120" y="470" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      val jobName = getString(R.string.app_name) + " Document"
    </text>
    <text x="120" y="485" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      val printAdapter = webView.createPrintDocumentAdapter(jobName)
    </text>
    <text x="120" y="500" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      printManager.print(jobName, printAdapter, PrintAttributes.Builder()
    </text>
    <text x="140" y="515" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
    </text>
    <text x="140" y="530" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
    </text>
    <text x="140" y="545" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600)).build())
    </text>
    <text x="100" y="560" font-family="Courier New, monospace" font-size="10" fill="#0d47a1">
      }
    </text>
    
    <!-- 连接箭头 -->
    <line x1="280" y1="180" x2="320" y2="180" stroke="#1976D2" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
    <line x1="520" y1="180" x2="560" y2="180" stroke="#1976D2" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
    <line x1="760" y1="180" x2="800" y2="180" stroke="#1976D2" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
    <line x1="1000" y1="180" x2="1040" y2="180" stroke="#1976D2" stroke-width="3" marker-end="url(#arrowheadBlue)"/>
  </g>
  
  <!-- 翻转拍功能流程 (下半部分) -->
  <g>
    <rect x="50" y="620" width="1500" height="520" rx="15" fill="url(#flipbeatGradient)" filter="url(#shadow)"/>
    <text x="800" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#6A1B9A">
      🎮 翻转拍硬件控制流程
    </text>
    
    <!-- Web端触发 -->
    <rect x="80" y="680" width="180" height="80" rx="8" fill="#ab47bc"/>
    <text x="170" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      Web训练游戏
    </text>
    <text x="170" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      触发翻转拍动作
    </text>
    <text x="170" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      postMessage(JSON)
    </text>
    
    <!-- JavaScript接口 -->
    <rect x="290" y="680" width="180" height="80" rx="8" fill="#9c27b0"/>
    <text x="380" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      JavaScript接口
    </text>
    <text x="380" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      解析action字段
    </text>
    <text x="380" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      flipFlipClap/Recover
    </text>
    
    <!-- FlipBeatManager -->
    <rect x="500" y="680" width="180" height="80" rx="8" fill="#8e24aa"/>
    <text x="590" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      FlipBeatManager
    </text>
    <text x="590" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      蓝牙通信管理
    </text>
    <text x="590" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      writeDataToFlipBeat()
    </text>
    
    <!-- 蓝牙协议 -->
    <rect x="710" y="680" width="180" height="80" rx="8" fill="#7b1fa2"/>
    <text x="800" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      蓝牙GATT协议
    </text>
    <text x="800" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      十六进制指令
    </text>
    <text x="800" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      Characteristic写入
    </text>
    
    <!-- 硬件设备 -->
    <rect x="920" y="680" width="180" height="80" rx="8" fill="#6a1b9a"/>
    <text x="1010" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      翻转拍硬件
    </text>
    <text x="1010" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      物理翻转动作
    </text>
    <text x="1010" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      视觉训练辅助
    </text>
    
    <!-- 状态反馈 -->
    <rect x="1130" y="680" width="180" height="80" rx="8" fill="#4a148c"/>
    <text x="1220" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      状态反馈
    </text>
    <text x="1220" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      连接状态监听
    </text>
    <text x="1220" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      FlipBeatListener
    </text>
    
    <!-- 翻转拍指令详情 -->
    <rect x="80" y="790" width="1160" height="120" rx="8" fill="#f3e5f5"/>
    <text x="660" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6A1B9A">
      翻转拍控制指令详情
    </text>
    
    <text x="100" y="840" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 翻转指令:</tspan> ["0xAA","0x55","0xA5","0x5A","0x21","0x01","0x00","0xCC","0x66","0xC6","0x6C"]
    </text>
    <text x="100" y="860" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 恢复指令:</tspan> ["0xAA","0x55","0xA5","0x5A","0x21","0x01","0x01","0xCC","0x66","0xC6","0x6C"]
    </text>
    <text x="100" y="880" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 区别:</tspan> 第7个字节 0x00(翻转) vs 0x01(恢复)
    </text>
    <text x="100" y="900" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 通信方式:</tspan> BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
    </text>
    
    <text x="700" y="840" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 设备前缀:</tspan> DM/AF/VICLEER 开头的蓝牙设备
    </text>
    <text x="700" y="860" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 连接状态:</tspan> DISCONNECTED → CONNECTING → CONNECTED
    </text>
    <text x="700" y="880" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 状态通知:</tspan> {"type":"fliperStatus", "data":1/0}
    </text>
    <text x="700" y="900" font-family="Arial, sans-serif" font-size="12" fill="#6A1B9A">
      <tspan font-weight="bold">• 使用场景:</tspan> 视觉训练、眼动检查、遮盖疗法
    </text>
    
    <!-- 蓝牙连接流程 -->
    <rect x="80" y="930" width="1160" height="190" rx="8" fill="#e1bee7"/>
    <text x="660" y="955" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4A148C">
      蓝牙连接与通信流程
    </text>
    
    <text x="100" y="980" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">1. 设备扫描:</tspan> 扫描DM/AF/VICLEER前缀的蓝牙设备
    </text>
    <text x="100" y="1000" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">2. 建立连接:</tspan> device.connectGatt() → BluetoothGattCallback
    </text>
    <text x="100" y="1020" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">3. 服务发现:</tspan> gatt.discoverServices() → 查找写入特征值
    </text>
    <text x="100" y="1040" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">4. 指令发送:</tspan> characteristic.setValue(bytes) → gatt.writeCharacteristic()
    </text>
    <text x="100" y="1060" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">5. 状态监听:</tspan> FlipBeatListener.onConnectionStateChange()
    </text>
    <text x="100" y="1080" font-family="Arial, sans-serif" font-size="12" fill="#4A148C">
      <tspan font-weight="bold">6. Web通知:</tspan> sendDataToWebView(fliperStatus) → 更新UI状态
    </text>
    
    <text x="700" y="980" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      // 翻转拍指令发送核心代码
    </text>
    <text x="700" y="1000" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      fun writeDataToFlipBeat(data: Array&lt;String&gt;) {
    </text>
    <text x="720" y="1020" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      val bytes = data.map { 
    </text>
    <text x="740" y="1040" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      it.substring(2).toInt(16).toByte() 
    </text>
    <text x="720" y="1060" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      }.toByteArray()
    </text>
    <text x="720" y="1080" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      characteristic.setValue(bytes)
    </text>
    <text x="720" y="1100" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      bluetoothGatt?.writeCharacteristic(characteristic)
    </text>
    <text x="700" y="1120" font-family="Courier New, monospace" font-size="10" fill="#4A148C">
      }
    </text>
    
    <!-- 连接箭头 -->
    <line x1="260" y1="720" x2="290" y2="720" stroke="#7B1FA2" stroke-width="3" marker-end="url(#arrowheadPurple)"/>
    <line x1="470" y1="720" x2="500" y2="720" stroke="#7B1FA2" stroke-width="3" marker-end="url(#arrowheadPurple)"/>
    <line x1="680" y1="720" x2="710" y2="720" stroke="#7B1FA2" stroke-width="3" marker-end="url(#arrowheadPurple)"/>
    <line x1="890" y1="720" x2="920" y2="720" stroke="#7B1FA2" stroke-width="3" marker-end="url(#arrowheadPurple)"/>
    <line x1="1100" y1="720" x2="1130" y2="720" stroke="#7B1FA2" stroke-width="3" marker-end="url(#arrowheadPurple)"/>
    
    <!-- 反馈箭头 -->
    <line x1="1130" y1="740" x2="500" y2="740" stroke="#FF9800" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
    <text x="815" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FF9800">
      状态反馈到Web
    </text>
  </g>
</svg>
