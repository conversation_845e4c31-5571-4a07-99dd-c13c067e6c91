<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #2c3e50; }
      .description { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .code-text { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .ui-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; }
      .business-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; }
      .data-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; }
      .native-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; }
      .hardware-layer { fill: #1abc9c; stroke: #16a085; stroke-width: 3; }
      .module-box { fill: #ffffff; stroke: #bdc3c7; stroke-width: 1; }
      .highlight-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e67e22; stroke-width: 2; fill: none; stroke-dasharray: 8,4; }
      .jni-flow { stroke: #9b59b6; stroke-width: 3; fill: none; stroke-dasharray: 6,3; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#34495e" />
    </marker>
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5dade2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ec7063;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f7dc6f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="nativeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bb8fce;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">MIT DD GazeTracker 项目架构图</text>
  <text x="800" y="55" text-anchor="middle" class="description">分层架构 + JNI集成 + 眼动追踪核心算法</text>

  <!-- UI层 -->
  <rect x="50" y="80" width="1500" height="200" fill="url(#uiGradient)" stroke="#2980b9" stroke-width="3" rx="15"/>
  <text x="70" y="110" class="layer-title">UI层 (Presentation Layer) - 41个Activity + 多个Fragment</text>

  <!-- Activities -->
  <rect x="80" y="130" width="240" height="130" class="module-box" rx="8"/>
  <text x="90" y="150" class="module-title">Activities (41个)</text>
  <text x="90" y="170" class="description">• LauncherActivity (启动页)</text>
  <text x="90" y="185" class="description">• HomeMainActivity (医疗主页)</text>
  <text x="90" y="200" class="description">• EyeMovementEvaluateActivity</text>
  <text x="90" y="215" class="description">• GazeStabilityEvaluateActivity</text>
  <text x="90" y="230" class="description">• ROIDetectionActivity</text>
  <text x="90" y="245" class="description">• ReadTrackActivity + 35个其他</text>

  <!-- Fragments -->
  <rect x="340" y="130" width="240" height="130" class="module-box" rx="8"/>
  <text x="350" y="150" class="module-title">Fragments (模块化)</text>
  <text x="350" y="170" class="description">• HomeMainFragment</text>
  <text x="350" y="185" class="description">• ReadHomeMainFragment</text>
  <text x="350" y="200" class="description">• EMPatientInfoFragment</text>
  <text x="350" y="215" class="description">• GazeStabilityExplainFragment</text>
  <text x="350" y="230" class="description">• FollowAbilityEvaluatingFragment</text>
  <text x="350" y="245" class="description">• SaccadeAbilityEvaluatingFragment</text>

  <!-- Custom Views -->
  <rect x="600" y="130" width="240" height="130" class="module-box" rx="8"/>
  <text x="610" y="150" class="module-title">Custom Views (眼动专用)</text>
  <text x="610" y="170" class="description">• PostureCalibrationView (姿势校准)</text>
  <text x="610" y="185" class="description">• VisualCalibrationView (视觉校准)</text>
  <text x="610" y="200" class="description">• ROIDetectionResultView (ROI检测)</text>
  <text x="610" y="215" class="description">• FollowAbilityEvaluatingView</text>
  <text x="610" y="230" class="description">• SaccadeAbilityEvaluatingView</text>
  <text x="610" y="245" class="description">• ReadTrackView + 更多</text>

  <!-- WebViews -->
  <rect x="860" y="130" width="240" height="130" class="module-box" rx="8"/>
  <text x="870" y="150" class="module-title">WebViews (Web集成)</text>
  <text x="870" y="170" class="description">• TrainWebView (训练)</text>
  <text x="870" y="185" class="description">• TrainCenterWebView</text>
  <text x="870" y="200" class="description">• InspectionCenterWebView</text>
  <text x="870" y="215" class="description">• ChatWebActivity</text>
  <text x="870" y="230" class="description">• ProtocolWebActivity</text>
  <text x="870" y="245" class="description">• JS交互 + 数据回调</text>

  <!-- Adapters & Dialogs -->
  <rect x="1120" y="130" width="240" height="130" class="module-box" rx="8"/>
  <text x="1130" y="150" class="module-title">Adapters & Dialogs</text>
  <text x="1130" y="170" class="description">• ReadHomeModuleAdapter</text>
  <text x="1130" y="185" class="description">• HospitalModuleAdapter</text>
  <text x="1130" y="200" class="description">• CalibrationFailureDialog</text>
  <text x="1130" y="215" class="description">• TreatmentActivationDialog</text>
  <text x="1130" y="230" class="description">• UpdateDialog</text>
  <text x="1130" y="245" class="description">• ConfirmBindDialog</text>

  <!-- 新增模块示例 -->
  <rect x="1380" y="130" width="150" height="130" class="highlight-box" rx="8"/>
  <text x="1390" y="150" class="module-title">新增模块示例</text>
  <text x="1390" y="170" class="description">• VoiceControlActivity</text>
  <text x="1390" y="185" class="description">• VoiceControlFragment</text>
  <text x="1390" y="200" class="description">• VoiceWaveView</text>
  <text x="1390" y="215" class="description">• 遵循现有架构</text>
  <text x="1390" y="230" class="description">• 集成到导航</text>
  <text x="1390" y="245" class="description">• 权限管理</text>

  <!-- 业务逻辑层 -->
  <rect x="50" y="300" width="1500" height="180" fill="url(#businessGradient)" stroke="#c0392b" stroke-width="3" rx="15"/>
  <text x="70" y="330" class="layer-title">业务逻辑层 (Business Logic Layer) - MVVM + Manager + Service</text>

  <!-- ViewModels -->
  <rect x="80" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="90" y="370" class="module-title">ViewModels (MVVM)</text>
  <text x="90" y="390" class="description">• TreatmentViewModel</text>
  <text x="90" y="405" class="description">• HospitalViewModel</text>
  <text x="90" y="420" class="description">• ROIDetectionViewModel</text>
  <text x="90" y="435" class="description">• UpdateViewModel</text>
  <text x="90" y="450" class="description">• AdaViewModel + 更多</text>

  <!-- Managers -->
  <rect x="320" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="330" y="370" class="module-title">Managers (核心业务)</text>
  <text x="330" y="390" class="description">• GazeTrackingManager ⭐</text>
  <text x="330" y="405" class="description">• DeviceManager</text>
  <text x="330" y="420" class="description">• UserManager</text>
  <text x="330" y="435" class="description">• EMPatientManager</text>
  <text x="330" y="450" class="description">• FlipBeatManager</text>

  <!-- Services -->
  <rect x="560" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="570" y="370" class="module-title">Services (后台服务)</text>
  <text x="570" y="390" class="description">• GazeTrackService ⭐</text>
  <text x="570" y="405" class="description">• DesktopService</text>
  <text x="570" y="420" class="description">• GTWebSocketService</text>
  <text x="570" y="435" class="description">• BootStartGTServiceWorker</text>
  <text x="570" y="450" class="description">• VoiceRecognitionService</text>

  <!-- Repositories -->
  <rect x="800" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="810" y="370" class="module-title">Repositories (数据仓库)</text>
  <text x="810" y="390" class="description">• GazeStabilityRepository</text>
  <text x="810" y="405" class="description">• ROIDetectionRepository</text>
  <text x="810" y="420" class="description">• FollowAbilityRepository</text>
  <text x="810" y="435" class="description">• SaccadeAbilityRepository</text>
  <text x="810" y="450" class="description">• UserRepository</text>

  <!-- APIs -->
  <rect x="1040" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="1050" y="370" class="module-title">API Services (网络)</text>
  <text x="1050" y="390" class="description">• EMPatientApiService</text>
  <text x="1050" y="405" class="description">• GazeStabilityApiService</text>
  <text x="1050" y="420" class="description">• ROIDetectionApiService</text>
  <text x="1050" y="435" class="description">• UserApiService</text>
  <text x="1050" y="450" class="description">• DeviceApiService</text>

  <!-- Utils -->
  <rect x="1280" y="350" width="220" height="110" class="module-box" rx="8"/>
  <text x="1290" y="370" class="module-title">Utils & Helpers</text>
  <text x="1290" y="390" class="description">• CommonUtils</text>
  <text x="1290" y="405" class="description">• GTUtils</text>
  <text x="1290" y="420" class="description">• LocaleManager</text>
  <text x="1290" y="435" class="description">• YUVUtils</text>
  <text x="1290" y="450" class="description">• EmailValidator</text>

  <!-- 数据层 -->
  <rect x="50" y="500" width="1500" height="140" fill="url(#dataGradient)" stroke="#e67e22" stroke-width="3" rx="15"/>
  <text x="70" y="530" class="layer-title">数据层 (Data Layer) - 网络 + 存储 + 通信 + 媒体</text>

  <!-- Network -->
  <rect x="80" y="550" width="280" height="70" class="module-box" rx="8"/>
  <text x="90" y="570" class="module-title">Network (网络)</text>
  <text x="90" y="590" class="description">• MainRetrofitClient • AdaRetrofitClient</text>
  <text x="90" y="605" class="description">• OverseasRetrofitClient • CommonParamsInterceptor</text>

  <!-- Storage -->
  <rect x="380" y="550" width="280" height="70" class="module-box" rx="8"/>
  <text x="390" y="570" class="module-title">Local Storage (本地存储)</text>
  <text x="390" y="590" class="description">• MMKV (Key-Value) • SharedPreferences</text>
  <text x="390" y="605" class="description">• File System • Assets • 配置文件</text>

  <!-- Communication -->
  <rect x="680" y="550" width="280" height="70" class="module-box" rx="8"/>
  <text x="690" y="570" class="module-title">Communication (通信)</text>
  <text x="690" y="590" class="description">• WebSocket • MQTT • LiveEventBus</text>
  <text x="690" y="605" class="description">• FlipBeat Protocol • 设备通信</text>

  <!-- Media -->
  <rect x="980" y="550" width="280" height="70" class="module-box" rx="8"/>
  <text x="990" y="570" class="module-title">Media & Camera (媒体)</text>
  <text x="990" y="590" class="description">• CameraX ⭐ • LibMedia (ExoPlayer)</text>
  <text x="990" y="605" class="description">• Image Processing • Video Recording</text>

  <!-- Cloud -->
  <rect x="1280" y="550" width="220" height="70" class="module-box" rx="8"/>
  <text x="1290" y="570" class="module-title">Cloud Services</text>
  <text x="1290" y="590" class="description">• 阿里云OSS • Backend APIs</text>
  <text x="1290" y="605" class="description">• Research APIs • Data Upload</text>

  <!-- 原生层 -->
  <rect x="50" y="660" width="1500" height="160" fill="url(#nativeGradient)" stroke="#8e44ad" stroke-width="3" rx="15"/>
  <text x="70" y="690" class="layer-title">原生层 (Native Layer - JNI/C++) - 核心算法 + 性能优化</text>

  <!-- Core Libraries -->
  <rect x="80" y="710" width="280" height="90" class="module-box" rx="8"/>
  <text x="90" y="730" class="module-title">Core Libraries (核心SO库)</text>
  <text x="90" y="750" class="code-text">libGazeTracker.so ⭐</text>
  <text x="90" y="765" class="description">  - GazeService (眼动追踪核心)</text>
  <text x="90" y="780" class="description">  - GazeApplication (应用模式)</text>
  <text x="90" y="795" class="code-text">libBehaviorGuidance.so</text>

  <!-- Third-party Libraries -->
  <rect x="380" y="710" width="280" height="90" class="module-box" rx="8"/>
  <text x="390" y="730" class="module-title">Third-party Libraries</text>
  <text x="390" y="750" class="code-text">libopencv_java4.so (OpenCV) ⭐</text>
  <text x="390" y="765" class="description">• NCNN (深度学习) • RKNN推理</text>
  <text x="390" y="780" class="description">• JsonCpp • 阿里云OSS SDK</text>
  <text x="390" y="795" class="description">• libpq_blr.so (图像处理)</text>

  <!-- Algorithms -->
  <rect x="680" y="710" width="280" height="90" class="module-box" rx="8"/>
  <text x="690" y="730" class="module-title">Core Algorithms (核心算法)</text>
  <text x="690" y="750" class="description">• 眼动追踪算法 ⭐ • 人脸检测 (SCRFD)</text>
  <text x="690" y="765" class="description">• 姿态检测 • 校准算法</text>
  <text x="690" y="780" class="description">• 图像处理算法 • 卡尔曼滤波</text>
  <text x="690" y="795" class="description">• ROI检测 • 扫视分析</text>

  <!-- Hardware Integration -->
  <rect x="980" y="710" width="280" height="90" class="module-box" rx="8"/>
  <text x="990" y="730" class="module-title">Hardware Integration</text>
  <text x="990" y="750" class="description">• Camera Control ⭐ • FlipBeat Device</text>
  <text x="990" y="765" class="description">• RKNN NPU • GPU (Vulkan)</text>
  <text x="990" y="780" class="description">• Sensors • 设备通信协议</text>
  <text x="990" y="795" class="description">• 实时数据处理</text>

  <!-- JNI Interface -->
  <rect x="1280" y="710" width="220" height="90" class="highlight-box" rx="8"/>
  <text x="1290" y="730" class="module-title">JNI Interface</text>
  <text x="1290" y="750" class="code-text">System.loadLibrary()</text>
  <text x="1290" y="765" class="description">• external fun 声明</text>
  <text x="1290" y="780" class="description">• JNIEXPORT 实现</text>
  <text x="1290" y="795" class="description">• 回调机制</text>

  <!-- 硬件层 -->
  <rect x="50" y="840" width="1500" height="100" class="hardware-layer" rx="15"/>
  <text x="70" y="870" class="layer-title">硬件层 (Hardware Layer) - 设备 + 传感器</text>

  <rect x="80" y="890" width="280" height="40" class="module-box" rx="8"/>
  <text x="90" y="910" class="module-title">Camera ⭐ • FlipBeat Device ⭐ • NPU • Sensors</text>

  <rect x="380" y="890" width="280" height="40" class="module-box" rx="8"/>
  <text x="390" y="910" class="module-title">RKNN芯片 • GPU • 摄像头模组 • 眼动设备</text>

  <rect x="680" y="890" width="280" height="40" class="module-box" rx="8"/>
  <text x="690" y="910" class="module-title">实时图像采集 • 设备状态监控 • 数据传输</text>

  <rect x="980" y="890" width="520" height="40" class="module-box" rx="8"/>
  <text x="990" y="910" class="module-title">硬件抽象层 • 驱动程序 • 设备配置管理 • 性能监控</text>

  <!-- 数据流箭头 - 垂直流向 -->
  <line x1="200" y1="280" x2="200" y2="300" class="arrow"/>
  <line x1="460" y1="280" x2="460" y2="300" class="arrow"/>
  <line x1="720" y1="280" x2="720" y2="300" class="arrow"/>
  <line x1="980" y1="280" x2="980" y2="300" class="arrow"/>
  <line x1="1240" y1="280" x2="1240" y2="300" class="arrow"/>

  <line x1="220" y1="480" x2="220" y2="500" class="arrow"/>
  <line x1="520" y1="480" x2="520" y2="500" class="arrow"/>
  <line x1="820" y1="480" x2="820" y2="500" class="arrow"/>
  <line x1="1120" y1="480" x2="1120" y2="500" class="arrow"/>
  <line x1="1390" y1="480" x2="1390" y2="500" class="arrow"/>

  <line x1="220" y1="640" x2="220" y2="660" class="arrow"/>
  <line x1="520" y1="640" x2="520" y2="660" class="arrow"/>
  <line x1="820" y1="640" x2="820" y2="660" class="arrow"/>
  <line x1="1120" y1="640" x2="1120" y2="660" class="arrow"/>

  <line x1="220" y1="820" x2="220" y2="840" class="arrow"/>
  <line x1="520" y1="820" x2="520" y2="840" class="arrow"/>
  <line x1="820" y1="820" x2="820" y2="840" class="arrow"/>
  <line x1="1120" y1="820" x2="1120" y2="840" class="arrow"/>

  <!-- JNI调用箭头 - 特殊标注 -->
  <line x1="430" y1="460" x2="220" y2="710" class="jni-flow"/>
  <line x1="680" y1="460" x2="520" y2="710" class="jni-flow"/>
  <text x="300" y="600" class="module-title" fill="#9b59b6">JNI调用</text>
  <text x="550" y="600" class="module-title" fill="#9b59b6">原生接口</text>

  <!-- 新增模块集成箭头 -->
  <line x1="1455" y1="260" x2="430" y2="350" class="data-flow"/>
  <text x="1000" y="320" class="description" fill="#ffc107">新增模块集成路径</text>

  <!-- 架构说明和新增模块指南 -->
  <rect x="50" y="960" width="1500" height="220" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="70" y="985" class="layer-title" fill="#2c3e50">架构说明与新增模块开发指南</text>

  <!-- 架构层次说明 -->
  <text x="70" y="1010" class="module-title" fill="#3498db">🔵 UI层 (Presentation)</text>
  <text x="70" y="1025" class="description">41个Activity + 多个Fragment + 自定义View，负责用户界面展示和交互</text>

  <text x="70" y="1045" class="module-title" fill="#e74c3c">🔴 业务逻辑层 (Business)</text>
  <text x="70" y="1060" class="description">MVVM架构，ViewModel + Manager + Service + Repository，处理核心业务逻辑</text>

  <text x="70" y="1080" class="module-title" fill="#f39c12">🟡 数据层 (Data)</text>
  <text x="70" y="1095" class="description">网络通信 + 本地存储 + 媒体处理 + 设备通信，负责数据管理和传输</text>

  <text x="70" y="1115" class="module-title" fill="#9b59b6">🟣 原生层 (Native)</text>
  <text x="70" y="1130" class="description">JNI/C++实现，眼动追踪核心算法 + OpenCV + AI推理，性能关键模块</text>

  <text x="70" y="1150" class="module-title" fill="#1abc9c">🟢 硬件层 (Hardware)</text>
  <text x="70" y="1165" class="description">摄像头 + FlipBeat设备 + NPU + 传感器，底层硬件抽象和控制</text>

  <!-- 新增模块开发步骤 -->
  <text x="800" y="1010" class="module-title" fill="#e67e22">📋 新增模块开发步骤 (6步法)</text>
  <text x="800" y="1030" class="description">1️⃣ 需求分析 → 功能设计 → 技术选型 → 接口定义</text>
  <text x="800" y="1045" class="description">2️⃣ UI层开发 → Activity + Fragment + CustomView + 布局</text>
  <text x="800" y="1060" class="description">3️⃣ 业务层开发 → ViewModel + Manager + Service + Repository</text>
  <text x="800" y="1075" class="description">4️⃣ 数据层开发 → 网络接口 + 本地存储 + 数据模型</text>
  <text x="800" y="1090" class="description">5️⃣ 原生层开发 → JNI接口 + C++实现 + SO库管理 (可选)</text>
  <text x="800" y="1105" class="description">6️⃣ 集成测试 → 模块集成 + 单元测试 + 文档编写</text>

  <text x="800" y="1130" class="module-title" fill="#c0392b">⚠️ 关键注意事项</text>
  <text x="800" y="1145" class="description">• 遵循现有架构模式 • 使用现有基类 • 完善错误处理</text>
  <text x="800" y="1160" class="description">• 性能优化考虑 • 向后兼容保证 • 充分测试验证</text>

  <!-- 数据流说明 -->
  <rect x="1300" y="1000" width="240" height="160" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="8"/>
  <text x="1310" y="1020" class="module-title">数据流向</text>
  <text x="1310" y="1040" class="description">用户操作 ↓</text>
  <text x="1310" y="1055" class="description">UI层 (Activity/Fragment)</text>
  <text x="1310" y="1070" class="description">↓</text>
  <text x="1310" y="1085" class="description">业务层 (ViewModel/Manager)</text>
  <text x="1310" y="1100" class="description">↓</text>
  <text x="1310" y="1115" class="description">数据层 (Repository/API)</text>
  <text x="1310" y="1130" class="description">↓</text>
  <text x="1310" y="1145" class="description">原生层 (JNI/C++算法)</text>
  <text x="1310" y="1160" class="description">↓</text>
  <text x="1310" y="1175" class="description">硬件层 (设备控制)</text>

</svg>
