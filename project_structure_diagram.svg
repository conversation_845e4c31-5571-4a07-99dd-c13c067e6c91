<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .folder { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .description { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .module-box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 8; }
      .main-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .lib-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; rx: 8; }
      .backend-box { fill: #d1ecf1; stroke: #17a2b8; stroke-width: 2; rx: 8; }
      .research-box { fill: #f8d7da; stroke: #dc3545; stroke-width: 2; rx: 8; }
      .docs-box { fill: #e2e3e5; stroke: #6c757d; stroke-width: 2; rx: 8; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">MIT-DD 眼动追踪系统项目架构图</text>
  
  <!-- 主应用模块 app -->
  <rect x="50" y="60" width="320" height="380" class="main-box"/>
  <text x="210" y="85" text-anchor="middle" class="folder">app (主应用模块)</text>
  
  <!-- app 子模块 -->
  <text x="70" y="110" class="description">├── gaze/ - 眼动追踪核心功能</text>
  <text x="70" y="125" class="description">│   ├── track/ - 眼动跟踪算法</text>
  <text x="70" y="140" class="description">│   └── application/ - 眼动应用管理</text>
  
  <text x="70" y="160" class="description">├── movement/ - 眼球运动评估</text>
  <text x="70" y="175" class="description">│   ├── gaze/ - 注视稳定性检测</text>
  <text x="70" y="190" class="description">│   ├── follow/ - 追随能力评估</text>
  <text x="70" y="205" class="description">│   ├── saccade/ - 扫视能力评估</text>
  <text x="70" y="220" class="description">│   └── roi/ - 兴趣区域检测</text>
  
  <text x="70" y="240" class="description">├── medicalhome/ - 医疗家庭版</text>
  <text x="70" y="255" class="description">│   ├── treatment/ - 治疗管理</text>
  <text x="70" y="270" class="description">│   └── mask/ - 遮盖疗法</text>
  
  <text x="70" y="290" class="description">├── medicalhospital/ - 医疗医院版</text>
  <text x="70" y="305" class="description">│   ├── inspection/ - 检查中心</text>
  <text x="70" y="320" class="description">│   └── train/ - 训练中心</text>
  
  <text x="70" y="340" class="description">├── read/ - 阅读训练模块</text>
  <text x="70" y="355" class="description">├── user/ - 用户管理</text>
  <text x="70" y="370" class="description">├── net/ - 网络通信</text>
  <text x="70" y="385" class="description">├── ai/ - AI助手集成</text>
  <text x="70" y="400" class="description">├── device/ - 设备管理</text>
  <text x="70" y="415" class="description">└── utils/ - 工具类库</text>
  
  <!-- LibBehaviorGuidance 库 -->
  <rect x="390" y="60" width="280" height="180" class="lib-box"/>
  <text x="530" y="85" text-anchor="middle" class="folder">LibBehaviorGuidance</text>
  <text x="530" y="100" text-anchor="middle" class="description">(行为指导库)</text>
  
  <text x="410" y="125" class="description">├── BehaviorGuidance.kt</text>
  <text x="410" y="140" class="description">│   └── 图像数据处理接口</text>
  <text x="410" y="155" class="description">├── cpp/ - C++算法实现</text>
  <text x="410" y="170" class="description">│   ├── SimplePose.cpp - 姿态检测</text>
  <text x="410" y="185" class="description">│   ├── MonitorReport - 监控报告</text>
  <text x="410" y="200" class="description">│   └── PostProcess - 后处理算法</text>
  <text x="410" y="215" class="description">└── assets/ - AI模型文件</text>
  
  <!-- LibMedia 库 -->
  <rect x="690" y="60" width="280" height="180" class="lib-box"/>
  <text x="830" y="85" text-anchor="middle" class="folder">LibMedia</text>
  <text x="830" y="100" text-anchor="middle" class="description">(媒体播放库)</text>
  
  <text x="710" y="125" class="description">├── PlayManager.kt</text>
  <text x="710" y="140" class="description">│   └── 媒体播放管理器</text>
  <text x="710" y="155" class="description">├── bean/ - 媒体数据模型</text>
  <text x="710" y="170" class="description">│   ├── AssetsMedia - 资源媒体</text>
  <text x="710" y="185" class="description">│   ├── RawMedia - 原始媒体</text>
  <text x="710" y="200" class="description">│   └── StreamMedia - 流媒体</text>
  <text x="710" y="215" class="description">└── 支持多种音频播放格式</text>
  
  <!-- backend 后端模块 -->
  <rect x="990" y="60" width="350" height="180" class="backend-box"/>
  <text x="1165" y="85" text-anchor="middle" class="folder">backend (后端服务)</text>
  
  <text x="1010" y="110" class="description">├── Spring Boot 3.5.0 + MyBatis Plus</text>
  <text x="1010" y="125" class="description">├── 患者信息管理</text>
  <text x="1010" y="140" class="description">├── 设备信息管理</text>
  <text x="1010" y="155" class="description">├── 检测记录管理</text>
  <text x="1010" y="170" class="description">├── 视线轨迹数据存储</text>
  <text x="1010" y="185" class="description">├── 审计功能 (创建/修改时间)</text>
  <text x="1010" y="200" class="description">├── 软删除支持</text>
  <text x="1010" y="215" class="description">└── 乐观锁并发控制</text>
  
  <!-- research 研究模块 -->
  <rect x="50" y="460" width="450" height="200" class="research-box"/>
  <text x="275" y="485" text-anchor="middle" class="folder">research (研究后端)</text>
  
  <text x="70" y="510" class="description">Movement检测结果提交系统</text>
  <text x="70" y="530" class="description">├── API接口:</text>
  <text x="70" y="545" class="description">│   ├── /api/movement/gaze-stability/submit</text>
  <text x="70" y="560" class="description">│   ├── /api/movement/follow-ability/submit</text>
  <text x="70" y="575" class="description">│   ├── /api/movement/saccade-ability/submit</text>
  <text x="70" y="590" class="description">│   └── /api/movement/roi-detection/submit</text>
  <text x="70" y="610" class="description">├── 数据库表:</text>
  <text x="70" y="625" class="description">│   ├── em_patients - 患者信息</text>
  <text x="70" y="640" class="description">│   ├── em_test_records - 检测记录</text>
  <text x="70" y="655" class="description">│   └── gaze_trajectory_data - 轨迹数据</text>
  
  <!-- docs 文档模块 -->
  <rect x="520" y="460" width="350" height="200" class="docs-box"/>
  <text x="695" y="485" text-anchor="middle" class="folder">docs (项目文档)</text>
  
  <text x="540" y="510" class="description">├── API文档</text>
  <text x="540" y="525" class="description">├── 数据格式说明</text>
  <text x="540" y="540" class="description">├── ROI检测数据上传指南</text>
  <text x="540" y="555" class="description">├── 扫视能力后端集成文档</text>
  <text x="540" y="570" class="description">├── 反应时间修复总结</text>
  <text x="540" y="585" class="description">├── 视线轨迹增强格式</text>
  <text x="540" y="600" class="description">├── ROI坐标增强说明</text>
  <text x="540" y="615" class="description">└── 依赖注入指南</text>
  
  <!-- 配置文件区域 -->
  <rect x="890" y="460" width="450" height="200" class="module-box"/>
  <text x="1115" y="485" text-anchor="middle" class="folder">配置文件 & 构建脚本</text>
  
  <text x="910" y="510" class="description">├── build.gradle.kts - Gradle构建配置</text>
  <text x="910" y="525" class="description">├── settings.gradle.kts - 项目设置</text>
  <text x="910" y="540" class="description">├── gradle.properties - Gradle属性</text>
  <text x="910" y="555" class="description">├── local.properties - 本地配置</text>
  <text x="910" y="570" class="description">├── gazetracker.jks - 签名密钥</text>
  <text x="910" y="585" class="description">├── sign_and_install.bat - 签名安装脚本</text>
  <text x="910" y="600" class="description">├── gradlew / gradlew.bat - Gradle包装器</text>
  <text x="910" y="615" class="description">└── gradle/wrapper/ - Gradle包装器文件</text>
  
  <!-- 数据流箭头 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- app -> LibBehaviorGuidance -->
  <line x1="370" y1="150" x2="390" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="380" y="145" text-anchor="middle" class="description" font-size="10">调用</text>
  
  <!-- app -> LibMedia -->
  <line x1="370" y1="200" x2="690" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="530" y="170" text-anchor="middle" class="description" font-size="10">媒体播放</text>
  
  <!-- app -> backend -->
  <line x1="370" y1="120" x2="990" y2="120" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="680" y="115" text-anchor="middle" class="description" font-size="10">API调用</text>
  
  <!-- app -> research -->
  <line x1="210" y1="440" x2="275" y2="460" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="240" y="445" text-anchor="middle" class="description" font-size="10">数据提交</text>
  
  <!-- 系统架构说明 -->
  <rect x="50" y="680" width="1290" height="280" class="module-box"/>
  <text x="695" y="705" text-anchor="middle" class="folder">系统架构说明</text>
  
  <text x="70" y="730" class="description">1. app模块: Android主应用，包含眼动追踪、运动评估、医疗管理等核心功能</text>
  <text x="70" y="750" class="description">2. LibBehaviorGuidance: 行为指导算法库，提供C++实现的姿态检测和监控功能</text>
  <text x="70" y="770" class="description">3. LibMedia: 媒体播放库，支持多种音频格式的播放功能</text>
  <text x="70" y="790" class="description">4. backend: 后端数据服务，基于Spring Boot，提供数据存储和管理</text>
  <text x="70" y="810" class="description">5. research: 研究后端，专门处理Movement模块的检测结果提交</text>
  <text x="70" y="830" class="description">6. docs: 项目文档，包含API文档、数据格式说明等</text>
  
  <text x="70" y="860" class="description">核心功能流程:</text>
  <text x="70" y="880" class="description">• 眼动追踪: 通过摄像头采集 → 算法处理 → 实时追踪 → 数据分析</text>
  <text x="70" y="900" class="description">• 运动评估: 注视稳定性 → 追随能力 → 扫视能力 → ROI检测 → 结果上传</text>
  <text x="70" y="920" class="description">• 医疗应用: 家庭版治疗 → 医院版检查 → 阅读训练 → 数据管理</text>
  <text x="70" y="940" class="description">• 数据管理: 患者信息 → 检测记录 → 轨迹数据 → 审计日志</text>
</svg>
