<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .model-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .data-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .process-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .jni-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .kotlin-box { fill: #fff8e1; stroke: #ffc107; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">瞳孔大小检测与传递流程</text>
  
  <!-- 输入层 -->
  <rect x="50" y="70" width="200" height="60" class="data-box"/>
  <text x="150" y="90" text-anchor="middle" class="subtitle">相机图像输入</text>
  <text x="150" y="110" text-anchor="middle" class="text">4048x3040 灰度图像</text>
  
  <!-- 人脸检测模型 -->
  <rect x="350" y="70" width="300" height="80" class="model-box"/>
  <text x="500" y="90" text-anchor="middle" class="subtitle">人脸检测模型</text>
  <text x="500" y="110" text-anchor="middle" class="code">face_keypoints_mobileone_seg_heatmap</text>
  <text x="500" y="125" text-anchor="middle" class="code">_224x160_epoch60_0000753.rknn</text>
  <text x="500" y="140" text-anchor="middle" class="text">输出：左右眼区域坐标</text>
  
  <!-- 眼部分割模型 -->
  <rect x="50" y="200" width="300" height="80" class="model-box"/>
  <text x="200" y="220" text-anchor="middle" class="subtitle">眼部分割模型</text>
  <text x="200" y="240" text-anchor="middle" class="code">ckpt_nir_eye_tracking_iris_mobileoneSeg</text>
  <text x="200" y="255" text-anchor="middle" class="code">_160x128_epoch27_iou951.rknn</text>
  <text x="200" y="270" text-anchor="middle" class="text">输出：虹膜分割掩码</text>
  
  <!-- 瞳孔检测模型 -->
  <rect x="400" y="200" width="300" height="80" class="model-box"/>
  <text x="550" y="220" text-anchor="middle" class="subtitle">瞳孔检测模型</text>
  <text x="550" y="240" text-anchor="middle" class="code">pupil_nir_2lights_keypoints_mobileoneSeg</text>
  <text x="550" y="255" text-anchor="middle" class="code">_heatmap_128_epoch170_00028.rknn</text>
  <text x="550" y="270" text-anchor="middle" class="text">输出：瞳孔中心点</text>
  
  <!-- 瞳孔分割模型 -->
  <rect x="750" y="200" width="300" height="80" class="model-box"/>
  <text x="900" y="220" text-anchor="middle" class="subtitle">瞳孔分割模型</text>
  <text x="900" y="240" text-anchor="middle" class="code">ckpt_nir_refine_seg_iris_pupil_mobileone</text>
  <text x="900" y="255" text-anchor="middle" class="code">_s4_128_epoch31_iou972.rknn</text>
  <text x="900" y="270" text-anchor="middle" class="text">输出：精确瞳孔分割</text>
  
  <!-- 数据结构层 -->
  <rect x="200" y="330" width="250" height="100" class="data-box"/>
  <text x="325" y="350" text-anchor="middle" class="subtitle">one_eye_detect_result</text>
  <text x="220" y="370" class="code">bool valid;</text>
  <text x="220" y="385" class="code">Point2f pupil;</text>
  <text x="220" y="400" class="code">Point2f light_left/right;</text>
  <text x="220" y="415" class="code">cv::Rect iris_rect; // 瞳孔大小</text>
  
  <rect x="500" y="330" width="250" height="100" class="data-box"/>
  <text x="625" y="350" text-anchor="middle" class="subtitle">frame_detection_result</text>
  <text x="520" y="370" class="code">one_eye_detect_result left_eye;</text>
  <text x="520" y="385" class="code">one_eye_detect_result right_eye;</text>
  <text x="520" y="400" class="code">float interpupillary_distance;</text>
  
  <!-- JNI接口层 -->
  <rect x="100" y="480" width="400" height="120" class="jni-box"/>
  <text x="300" y="500" text-anchor="middle" class="subtitle">JNI接口修改</text>
  <text x="120" y="520" class="code">nativeGazeTracking() 函数中添加：</text>
  <text x="120" y="535" class="code">jfloat left_pupil_width = detect_result.left_eye.iris_rect.width;</text>
  <text x="120" y="550" class="code">jfloat left_pupil_height = detect_result.left_eye.iris_rect.height;</text>
  <text x="120" y="565" class="code">jfloat right_pupil_width = detect_result.right_eye.iris_rect.width;</text>
  <text x="120" y="580" class="code">jfloat right_pupil_height = detect_result.right_eye.iris_rect.height;</text>
  
  <rect x="550" y="480" width="350" height="120" class="jni-box"/>
  <text x="725" y="500" text-anchor="middle" class="subtitle">HashMap返回值</text>
  <text x="570" y="520" class="code">hashMap.put("left_pupil_width", ...);</text>
  <text x="570" y="535" class="code">hashMap.put("left_pupil_height", ...);</text>
  <text x="570" y="550" class="code">hashMap.put("right_pupil_width", ...);</text>
  <text x="570" y="565" class="code">hashMap.put("right_pupil_height", ...);</text>
  <text x="570" y="580" class="code">hashMap.put("gaze_x", ...);</text>
  
  <!-- Kotlin层 -->
  <rect x="200" y="650" width="300" height="120" class="kotlin-box"/>
  <text x="350" y="670" text-anchor="middle" class="subtitle">GazeTrackResult.kt</text>
  <text x="220" y="690" class="code">var leftPupilWidth: Float = -1f</text>
  <text x="220" y="705" class="code">var leftPupilHeight: Float = -1f</text>
  <text x="220" y="720" class="code">var rightPupilWidth: Float = -1f</text>
  <text x="220" y="735" class="code">var rightPupilHeight: Float = -1f</text>
  <text x="220" y="750" class="code">// 原有字段：x, y, dist, duration等</text>
  
  <rect x="550" y="650" width="300" height="120" class="kotlin-box"/>
  <text x="700" y="670" text-anchor="middle" class="subtitle">GazeTrack.kt</text>
  <text x="570" y="690" class="code">leftPupilWidth = result?.get("left_pupil_width")</text>
  <text x="570" y="705" class="code">leftPupilHeight = result?.get("left_pupil_height")</text>
  <text x="570" y="720" class="code">rightPupilWidth = result?.get("right_pupil_width")</text>
  <text x="570" y="735" class="code">rightPupilHeight = result?.get("right_pupil_height")</text>
  
  <!-- 应用层 -->
  <rect x="950" y="650" width="300" height="120" class="process-box"/>
  <text x="1100" y="670" text-anchor="middle" class="subtitle">应用层使用</text>
  <text x="970" y="690" class="text">• 瞳孔大小监测</text>
  <text x="970" y="705" class="text">• 疲劳检测</text>
  <text x="970" y="720" class="text">• 医学诊断</text>
  <text x="970" y="735" class="text">• 眼动分析</text>
  <text x="970" y="750" class="text">• 距离估算优化</text>
  
  <!-- 箭头连接 -->
  <line x1="250" y1="100" x2="350" y2="100" class="arrow"/>
  <line x1="500" y1="150" x2="200" y2="200" class="arrow"/>
  <line x1="500" y1="150" x2="550" y2="200" class="arrow"/>
  <line x1="550" y1="150" x2="900" y2="200" class="arrow"/>
  
  <line x1="200" y1="280" x2="325" y2="330" class="arrow"/>
  <line x1="550" y1="280" x2="325" y2="330" class="arrow"/>
  <line x1="900" y1="280" x2="325" y2="330" class="arrow"/>
  
  <line x1="450" y1="380" x2="500" y2="380" class="arrow"/>
  <line x1="625" y1="430" x2="300" y2="480" class="arrow"/>
  <line x1="500" y1="540" x2="550" y2="540" class="arrow"/>
  
  <line x1="725" y1="600" x2="350" y2="650" class="arrow"/>
  <line x1="725" y1="600" x2="700" y2="650" class="arrow"/>
  <line x1="850" y1="710" x2="950" y2="710" class="arrow"/>
  
  <!-- 说明文字 -->
  <text x="50" y="820" class="subtitle">关键技术点：</text>
  <text x="50" y="840" class="text">1. 使用4个深度学习模型协同工作：人脸检测 → 眼部分割 → 瞳孔检测 → 瞳孔分割</text>
  <text x="50" y="855" class="text">2. 瞳孔大小通过iris_rect的width和height获得（像素单位）</text>
  <text x="50" y="870" class="text">3. 通过修改JNI接口将C++层的瞳孔大小数据传递到Kotlin层</text>
  <text x="50" y="885" class="text">4. 在GazeTrackResult中新增leftPupilWidth/Height和rightPupilWidth/Height字段</text>
  <text x="50" y="900" class="text">5. 可用于疲劳检测、医学诊断、距离估算等应用场景</text>
  
  <text x="50" y="930" class="subtitle">模型文件位置：</text>
  <text x="50" y="950" class="code">app/src/main/assets/configs/*.rknn</text>
  <text x="50" y="965" class="text">运行时拷贝到：/data/data/包名/app_configs/</text>
</svg>
