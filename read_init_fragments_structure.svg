<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .fragment1-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .fragment2-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .fragment3-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .ui-component { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 1; }
      .logic-box { fill: #ffebee; stroke: #f44336; stroke-width: 1; }
      .flow-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">ReadInit三个Fragment结构与功能详解</text>
  
  <!-- Fragment 1: ReadInitBasicInfoFragment -->
  <rect x="50" y="70" width="480" height="320" class="fragment1-box" rx="8"/>
  <text x="290" y="95" text-anchor="middle" class="subtitle">Fragment 1: ReadInitBasicInfoFragment</text>
  <text x="70" y="115" class="description-text">基础信息设置Fragment - 139行代码</text>
  
  <!-- UI组件 -->
  <rect x="70" y="130" width="440" height="120" class="ui-component" rx="5"/>
  <text x="290" y="150" text-anchor="middle" class="step-title">UI组件结构</text>
  
  <text x="80" y="170" class="step-text" font-weight="bold">身份选择 (RadioGroup):</text>
  <text x="100" y="185" class="code-text">• rb_identity_primary_school - 小学生</text>
  <text x="100" y="200" class="code-text">• rb_identity_junior_high_school - 初中生</text>
  <text x="100" y="215" class="code-text">• rb_identity_senior_high_school - 高中生</text>
  <text x="100" y="230" class="code-text">• rb_identity_adult - 成人</text>
  
  <text x="300" y="170" class="step-text" font-weight="bold">年级选择 (RadioGroup):</text>
  <text x="320" y="185" class="code-text">• rb_grade_first - 一年级</text>
  <text x="320" y="200" class="code-text">• rb_grade_second - 二年级</text>
  <text x="320" y="215" class="code-text">• rb_grade_three - 三年级</text>
  <text x="320" y="230" class="code-text">• rb_grade_four/five/six - 四五六年级</text>
  
  <!-- 业务逻辑 -->
  <rect x="70" y="260" width="440" height="120" class="logic-box" rx="5"/>
  <text x="290" y="280" text-anchor="middle" class="step-title">业务逻辑</text>
  
  <text x="80" y="300" class="step-text" font-weight="bold">动态UI控制:</text>
  <text x="100" y="315" class="code-text">• 小学生: 显示4-6年级选项</text>
  <text x="100" y="330" class="code-text">• 初中生/高中生: 只显示1-3年级</text>
  <text x="100" y="345" class="code-text">• 成人: 隐藏年级选择</text>
  
  <text x="300" y="300" class="step-text" font-weight="bold">自动跳转:</text>
  <text x="320" y="315" class="code-text">checkIdentityAndGrade()</text>
  <text x="320" y="330" class="code-text">• 选择完成后自动跳转到校准页面</text>
  <text x="320" y="345" class="code-text">• 调用 goToCalibrationTab()</text>
  
  <!-- Fragment 2: ReadInitCalibrationFragment -->
  <rect x="560" y="70" width="480" height="320" class="fragment2-box" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">Fragment 2: ReadInitCalibrationFragment</text>
  <text x="580" y="115" class="description-text">眼动校准Fragment - 73行代码</text>
  
  <!-- UI组件 -->
  <rect x="580" y="130" width="440" height="80" class="ui-component" rx="5"/>
  <text x="800" y="150" text-anchor="middle" class="step-title">UI组件结构</text>
  
  <text x="590" y="170" class="step-text" font-weight="bold">操作按钮:</text>
  <text x="610" y="185" class="code-text">• tv_start_calibration - 开始校准按钮</text>
  <text x="610" y="200" class="code-text">• tv_skip_calibration - 跳过校准按钮</text>
  
  <!-- 校准流程 -->
  <rect x="580" y="220" width="440" height="160" class="logic-box" rx="5"/>
  <text x="800" y="240" text-anchor="middle" class="step-title">校准流程处理</text>
  
  <text x="590" y="260" class="step-text" font-weight="bold">ActivityResultLauncher:</text>
  <text x="610" y="275" class="code-text">calibrationLauncher - 处理校准结果</text>
  
  <text x="590" y="295" class="step-text" font-weight="bold">校准成功:</text>
  <text x="610" y="310" class="code-text">• 显示成功Toast</text>
  <text x="610" y="325" class="code-text">• 自动进入下一步</text>
  
  <text x="590" y="345" class="step-text" font-weight="bold">校准失败:</text>
  <text x="610" y="360" class="code-text">• 显示失败对话框</text>
  <text x="610" y="375" class="code-text">• 提供重新校准选项</text>
  
  <!-- Fragment 3: ReadInitStartEvaluateFragment -->
  <rect x="1070" y="70" width="480" height="320" class="fragment3-box" rx="8"/>
  <text x="1310" y="95" text-anchor="middle" class="subtitle">Fragment 3: ReadInitStartEvaluateFragment</text>
  <text x="1090" y="115" class="description-text">开始评估Fragment - 38行代码</text>
  
  <!-- UI组件 -->
  <rect x="1090" y="130" width="440" height="80" class="ui-component" rx="5"/>
  <text x="1310" y="150" text-anchor="middle" class="step-title">UI组件结构</text>
  
  <text x="1100" y="170" class="step-text" font-weight="bold">操作按钮:</text>
  <text x="1120" y="185" class="code-text">• tv_start_read - 开始阅读测试按钮</text>
  <text x="1120" y="200" class="code-text">• 点击后跳转到ReadActivity</text>
  
  <!-- 跳转逻辑 -->
  <rect x="1090" y="220" width="440" height="160" class="logic-box" rx="5"/>
  <text x="1310" y="240" text-anchor="middle" class="step-title">跳转逻辑</text>
  
  <text x="1100" y="260" class="step-text" font-weight="bold">最简单的Fragment:</text>
  <text x="1120" y="275" class="code-text">• 只有一个按钮和点击事件</text>
  <text x="1120" y="290" class="code-text">• 调用 (mActivity as? ReadInitActivity)?.goToRead()</text>
  
  <text x="1100" y="310" class="step-text" font-weight="bold">功能职责:</text>
  <text x="1120" y="325" class="code-text">• 作为最后一步的确认页面</text>
  <text x="1120" y="340" class="code-text">• 启动正式的阅读测试</text>
  <text x="1120" y="355" class="code-text">• 传递身份和年级参数</text>
  
  <!-- Fragment切换流程 -->
  <rect x="50" y="420" width="1500" height="200" class="flow-box" rx="8"/>
  <text x="800" y="445" text-anchor="middle" class="subtitle">Fragment切换流程</text>
  
  <!-- 流程步骤 -->
  <rect x="80" y="470" width="200" height="80" class="fragment1-box" rx="5"/>
  <text x="180" y="490" text-anchor="middle" class="step-title">步骤1: 基础信息</text>
  <text x="90" y="510" class="step-text">• 选择身份</text>
  <text x="90" y="525" class="step-text">• 选择年级</text>
  <text x="90" y="540" class="step-text">• 自动跳转</text>
  
  <rect x="320" y="470" width="200" height="80" class="fragment2-box" rx="5"/>
  <text x="420" y="490" text-anchor="middle" class="step-title">步骤2: 眼动校准</text>
  <text x="330" y="510" class="step-text">• 启动校准Activity</text>
  <text x="330" y="525" class="step-text">• 处理校准结果</text>
  <text x="330" y="540" class="step-text">• 跳转或重试</text>
  
  <rect x="560" y="470" width="200" height="80" class="fragment3-box" rx="5"/>
  <text x="660" y="490" text-anchor="middle" class="step-title">步骤3: 开始评估</text>
  <text x="570" y="510" class="step-text">• 最终确认</text>
  <text x="570" y="525" class="step-text">• 启动ReadActivity</text>
  <text x="570" y="540" class="step-text">• 传递参数</text>
  
  <rect x="800" y="470" width="200" height="80" class="logic-box" rx="5"/>
  <text x="900" y="490" text-anchor="middle" class="step-title">ReadActivity</text>
  <text x="810" y="510" class="step-text">• 接收身份年级参数</text>
  <text x="810" y="525" class="step-text">• 开始阅读测试</text>
  <text x="810" y="540" class="step-text">• 视线追踪</text>
  
  <!-- 切换箭头 -->
  <line x1="280" y1="510" x2="320" y2="510" class="flow-arrow"/>
  <line x1="520" y1="510" x2="560" y2="510" class="flow-arrow"/>
  <line x1="760" y1="510" x2="800" y2="510" class="flow-arrow"/>
  
  <!-- 代码实现细节 -->
  <rect x="50" y="650" width="1500" height="400" class="logic-box" rx="8"/>
  <text x="800" y="675" text-anchor="middle" class="subtitle">代码实现细节</text>
  
  <!-- Fragment 1 代码 -->
  <rect x="80" y="700" width="440" height="160" class="fragment1-box" rx="5"/>
  <text x="300" y="720" text-anchor="middle" class="step-title">ReadInitBasicInfoFragment 关键代码</text>
  
  <text x="90" y="740" class="code-text">rgIdentity.setOnCheckedChangeListener { _, checkedId -></text>
  <text x="100" y="755" class="code-text">    when(checkedId) {</text>
  <text x="110" y="770" class="code-text">        R.id.rb_identity_primary_school -> {</text>
  <text x="120" y="785" class="code-text">            (mActivity as? ReadInitActivity)?.setIdentity(ReadIdentity.PRIMARY_SCHOOL)</text>
  <text x="120" y="800" class="code-text">            // 显示4-6年级选项</text>
  <text x="120" y="815" class="code-text">            rbGradeFour.isVisible = true</text>
  <text x="110" y="830" class="code-text">        }</text>
  <text x="110" y="845" class="code-text">        // 其他身份选项...</text>
  <text x="100" y="860" class="code-text">    }</text>
  
  <!-- Fragment 2 代码 -->
  <rect x="540" y="700" width="440" height="160" class="fragment2-box" rx="5"/>
  <text x="760" y="720" text-anchor="middle" class="step-title">ReadInitCalibrationFragment 关键代码</text>
  
  <text x="550" y="740" class="code-text">private var calibrationLauncher = registerForActivityResult(</text>
  <text x="560" y="755" class="code-text">    ActivityResultContracts.StartActivityForResult()</text>
  <text x="550" y="770" class="code-text">) { result -></text>
  <text x="560" y="785" class="code-text">    if (result.resultCode == Activity.RESULT_OK) {</text>
  <text x="570" y="800" class="code-text">        val isSucceed = data?.getBooleanExtra(GazeConstants.KEY_IS_SUCCESS, false)</text>
  <text x="570" y="815" class="code-text">        if (isSucceed) {</text>
  <text x="580" y="830" class="code-text">            Toast.makeText(mActivity, "校准成功", Toast.LENGTH_SHORT).show()</text>
  <text x="570" y="845" class="code-text">        } else {</text>
  <text x="580" y="860" class="code-text">            // 显示失败对话框</text>
  
  <!-- Fragment 3 代码 -->
  <rect x="1000" y="700" width="440" height="160" class="fragment3-box" rx="5"/>
  <text x="1220" y="720" text-anchor="middle" class="step-title">ReadInitStartEvaluateFragment 关键代码</text>
  
  <text x="1010" y="740" class="code-text">override fun initView() {</text>
  <text x="1020" y="755" class="code-text">    super.initView()</text>
  <text x="1020" y="770" class="code-text">    </text>
  <text x="1020" y="785" class="code-text">    tvStartRead.setOnSingleClickListener {</text>
  <text x="1030" y="800" class="code-text">        (mActivity as? ReadInitActivity)?.goToRead()</text>
  <text x="1020" y="815" class="code-text">    }</text>
  <text x="1010" y="830" class="code-text">}</text>
  
  <text x="1010" y="850" class="description-text">最简单的Fragment，只有38行代码</text>
  <text x="1010" y="865" class="description-text">主要功能就是提供一个启动按钮</text>
  
  <!-- 总结 -->
  <rect x="80" y="880" width="1360" height="150" class="flow-box" rx="5"/>
  <text x="760" y="900" text-anchor="middle" class="step-title">三个Fragment特点总结</text>
  
  <text x="100" y="925" class="step-text" font-weight="bold">Fragment 1 (139行):</text>
  <text x="120" y="940" class="step-text">• 最复杂，包含动态UI控制逻辑</text>
  <text x="120" y="955" class="step-text">• 处理身份和年级的联动关系</text>
  <text x="120" y="970" class="step-text">• 自动跳转到下一步</text>
  
  <text x="400" y="925" class="step-text" font-weight="bold">Fragment 2 (73行):</text>
  <text x="420" y="940" class="step-text">• 中等复杂度，处理校准流程</text>
  <text x="420" y="955" class="step-text">• 使用ActivityResultLauncher</text>
  <text x="420" y="970" class="step-text">• 处理成功/失败两种情况</text>
  
  <text x="700" y="925" class="step-text" font-weight="bold">Fragment 3 (38行):</text>
  <text x="720" y="940" class="step-text">• 最简单，只有一个按钮</text>
  <text x="720" y="955" class="step-text">• 作为最终确认步骤</text>
  <text x="720" y="970" class="step-text">• 启动正式的阅读测试</text>
  
  <text x="1000" y="925" class="step-text" font-weight="bold">设计特点:</text>
  <text x="1020" y="940" class="step-text">• 职责单一，功能明确</text>
  <text x="1020" y="955" class="step-text">• 渐进式引导用户</text>
  <text x="1020" y="970" class="step-text">• 良好的用户体验</text>
  
  <text x="100" y="995" class="step-text" font-weight="bold">代码量分布:</text>
  <text x="120" y="1010" class="step-text">基础信息(139行) > 眼动校准(73行) > 开始评估(38行)</text>
  <text x="120" y="1025" class="step-text">复杂度与功能需求成正比，体现了良好的模块化设计</text>
</svg>
