<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .activity-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .view-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .exoplayer-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .static-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .dynamic-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .data-flow { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">ReadTrackActivity视图加载与轨迹绘制架构详解</text>
  
  <!-- Activity架构 -->
  <rect x="50" y="70" width="1500" height="200" class="activity-box" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">ReadTrackActivity 整体架构</text>
  
  <!-- 布局结构 -->
  <rect x="80" y="120" width="450" height="130" class="data-flow" rx="5"/>
  <text x="305" y="140" text-anchor="middle" class="step-title">布局结构 (activity_read_track.xml)</text>
  <text x="90" y="160" class="code-text">ConstraintLayout (根布局)</text>
  <text x="100" y="175" class="code-text">├── ImageView (iv_read) - 阅读内容背景图</text>
  <text x="100" y="190" class="code-text">├── ReadTrackView (read_track_figure) - 自定义轨迹绘制</text>
  <text x="100" y="205" class="code-text">├── PlayerView (player_view) - ExoPlayer视频播放</text>
  <text x="100" y="220" class="code-text">└── RadioGroup (rg_tools) - 模式切换按钮</text>
  <text x="110" y="235" class="code-text">    ├── rb_player (视频播放)</text>
  <text x="110" y="250" class="code-text">    ├── rb_heat_map (动态轨迹)</text>
  <text x="110" y="265" class="code-text">    └── rb_track_figure (静态轨迹)</text>
  
  <!-- 数据流 -->
  <rect x="550" y="120" width="450" height="130" class="data-flow" rx="5"/>
  <text x="775" y="140" text-anchor="middle" class="step-title">数据流向</text>
  <text x="560" y="160" class="code-text">ReadResultAnalysisActivity</text>
  <text x="570" y="175" class="code-text">↓ LiveEventBus.post(gazeTrajectory)</text>
  <text x="560" y="190" class="code-text">ReadTrackActivity</text>
  <text x="570" y="205" class="code-text">↓ observeSticky(INPUT_PARAM_TRACK_RESULT)</text>
  <text x="560" y="220" class="code-text">mGazeTrajectory = it</text>
  <text x="570" y="235" class="code-text">↓ rgTools.check(R.id.rb_track_figure)</text>
  <text x="560" y="250" class="code-text">ReadTrackView.drawReadTrac()</text>
  
  <!-- 初始化流程 -->
  <rect x="1020" y="120" width="450" height="130" class="data-flow" rx="5"/>
  <text x="1245" y="140" text-anchor="middle" class="step-title">初始化流程</text>
  <text x="1030" y="160" class="code-text">onCreate() {</text>
  <text x="1040" y="175" class="code-text">    initParam()     // 参数初始化</text>
  <text x="1040" y="190" class="code-text">    initView()      // 视图初始化</text>
  <text x="1040" y="205" class="code-text">    initObserver()  // 数据观察</text>
  <text x="1040" y="220" class="code-text">    initData()      // 数据初始化</text>
  <text x="1030" y="235" class="code-text">}</text>
  <text x="1030" y="250" class="code-text">ExoPlayer.Builder(this).build()</text>
  
  <!-- 三种显示模式 -->
  <rect x="50" y="290" width="1500" height="280" class="view-box" rx="8"/>
  <text x="800" y="315" text-anchor="middle" class="subtitle">三种显示模式切换机制</text>
  
  <!-- 视频播放模式 -->
  <rect x="80" y="340" width="450" height="230" class="exoplayer-box" rx="5"/>
  <text x="305" y="360" text-anchor="middle" class="step-title">模式1: 视频播放 (ExoPlayer)</text>
  <text x="90" y="380" class="step-text" font-weight="bold">触发条件:</text>
  <text x="90" y="395" class="code-text">rgTools.setOnCheckedChangeListener { _, checkedId -></text>
  <text x="100" y="410" class="code-text">    when (checkedId) {</text>
  <text x="110" y="425" class="code-text">        R.id.rb_player -> {</text>
  <text x="120" y="440" class="code-text">            readTrackFigure.isVisible = false</text>
  <text x="120" y="455" class="code-text">            playerView.isVisible = true</text>
  <text x="120" y="470" class="code-text">            play(true)</text>
  <text x="110" y="485" class="code-text">        }</text>
  
  <text x="90" y="505" class="step-text" font-weight="bold">ExoPlayer实现:</text>
  <text x="90" y="520" class="code-text">val mediaItem = MediaItem.fromUri(</text>
  <text x="100" y="535" class="code-text">    Uri.parse("asset:///video/read_track_normal_video.mp4")</text>
  <text x="90" y="550" class="code-text">)</text>
  <text x="90" y="565" class="code-text">exoPlayer?.setMediaItem(mediaItem)</text>
  
  <!-- 动态轨迹模式 -->
  <rect x="550" y="340" width="450" height="230" class="dynamic-box" rx="5"/>
  <text x="775" y="360" text-anchor="middle" class="step-title">模式2: 动态轨迹 (Heat Map)</text>
  <text x="560" y="380" class="step-text" font-weight="bold">触发条件:</text>
  <text x="560" y="395" class="code-text">R.id.rb_heat_map -> {</text>
  <text x="570" y="410" class="code-text">    readTrackFigure.isVisible = true</text>
  <text x="570" y="425" class="code-text">    playerView.isVisible = false</text>
  <text x="570" y="440" class="code-text">    play(false)</text>
  <text x="570" y="455" class="code-text">    readTrackFigure.drawReadTrac(</text>
  <text x="580" y="470" class="code-text">        mGazeTrajectory, DrawMode.DYNAMIC</text>
  <text x="570" y="485" class="code-text">    )</text>
  <text x="560" y="500" class="code-text">}</text>
  
  <text x="560" y="520" class="step-text" font-weight="bold">动画效果:</text>
  <text x="560" y="535" class="code-text">• 逐点绘制轨迹</text>
  <text x="560" y="550" class="code-text">• 100ms延迟间隔</text>
  <text x="560" y="565" class="code-text">• 协程异步处理</text>
  
  <!-- 静态轨迹模式 -->
  <rect x="1020" y="340" width="450" height="230" class="static-box" rx="5"/>
  <text x="1245" y="360" text-anchor="middle" class="step-title">模式3: 静态轨迹 (Track Figure)</text>
  <text x="1030" y="380" class="step-text" font-weight="bold">触发条件:</text>
  <text x="1030" y="395" class="code-text">R.id.rb_track_figure -> {</text>
  <text x="1040" y="410" class="code-text">    readTrackFigure.isVisible = true</text>
  <text x="1040" y="425" class="code-text">    playerView.isVisible = false</text>
  <text x="1040" y="440" class="code-text">    play(false)</text>
  <text x="1040" y="455" class="code-text">    readTrackFigure.drawReadTrac(</text>
  <text x="1050" y="470" class="code-text">        mGazeTrajectory, DrawMode.STATIC</text>
  <text x="1040" y="485" class="code-text">    )</text>
  <text x="1030" y="500" class="code-text">}</text>
  
  <text x="1030" y="520" class="step-text" font-weight="bold">即时显示:</text>
  <text x="1030" y="535" class="code-text">• 一次性绘制完整轨迹</text>
  <text x="1030" y="550" class="code-text">• 显示所有视线点</text>
  <text x="1030" y="565" class="code-text">• 默认选中模式</text>
  
  <!-- ReadTrackView详解 -->
  <rect x="50" y="590" width="1500" height="350" class="view-box" rx="8"/>
  <text x="800" y="615" text-anchor="middle" class="subtitle">ReadTrackView 自定义绘制详解</text>
  
  <!-- 静态绘制 -->
  <rect x="80" y="640" width="700" height="280" class="static-box" rx="5"/>
  <text x="430" y="660" text-anchor="middle" class="step-title">静态绘制 (staticDraw)</text>
  
  <text x="90" y="680" class="code-text">private fun staticDraw() {</text>
  <text x="100" y="695" class="code-text">    readPoints.clear()</text>
  <text x="100" y="710" class="code-text">    trackPath.reset()</text>
  <text x="100" y="725" class="code-text">    </text>
  <text x="100" y="740" class="code-text">    // 过滤有效数据点</text>
  <text x="100" y="755" class="code-text">    val gaze = mGazeTrajectory?.gaze?.filter { it.checkValid() } ?: emptyList()</text>
  <text x="100" y="770" class="code-text">    readPoints.addAll(gaze)</text>
  <text x="100" y="785" class="code-text">    </text>
  <text x="100" y="800" class="code-text">    // 构建路径</text>
  <text x="100" y="815" class="code-text">    readPoints.forEachIndexed { index, result -></text>
  <text x="110" y="830" class="code-text">        val x = result.x!! * screenWidth</text>
  <text x="110" y="845" class="code-text">        val y = result.y!! * screenHeight</text>
  <text x="110" y="860" class="code-text">        if (index == 0) {</text>
  <text x="120" y="875" class="code-text">            trackPath.moveTo(x, y)  // 起点</text>
  <text x="110" y="890" class="code-text">        } else {</text>
  <text x="120" y="905" class="code-text">            trackPath.lineTo(x, y)  // 连线</text>
  <text x="110" y="920" class="code-text">        }</text>
  <text x="100" y="935" class="code-text">    }</text>
  <text x="100" y="950" class="code-text">    invalidate()  // 触发重绘</text>
  <text x="90" y="965" class="code-text">}</text>
  
  <!-- 动态绘制 -->
  <rect x="800" y="640" width="700" height="280" class="dynamic-box" rx="5"/>
  <text x="1150" y="660" text-anchor="middle" class="step-title">动态绘制 (dynamicDraw)</text>
  
  <text x="810" y="680" class="code-text">private fun dynamicDraw() {</text>
  <text x="820" y="695" class="code-text">    dynamicDrawJob?.cancel()  // 取消之前的任务</text>
  <text x="820" y="710" class="code-text">    readPoints.clear()</text>
  <text x="820" y="725" class="code-text">    trackPath.reset()</text>
  <text x="820" y="740" class="code-text">    </text>
  <text x="820" y="755" class="code-text">    val gaze = mGazeTrajectory?.gaze?.filter { it.checkValid() } ?: emptyList()</text>
  <text x="820" y="770" class="code-text">    val points = mutableListOf&lt;GazePoint&gt;()</text>
  <text x="820" y="785" class="code-text">    points.addAll(gaze)</text>
  <text x="820" y="800" class="code-text">    </text>
  <text x="820" y="815" class="code-text">    // 协程动画</text>
  <text x="820" y="830" class="code-text">    dynamicDrawJob = coroutineScope.launch(Dispatchers.Default) {</text>
  <text x="830" y="845" class="code-text">        while (points.isNotEmpty()) {</text>
  <text x="840" y="860" class="code-text">            withContext(Dispatchers.Main) {</text>
  <text x="850" y="875" class="code-text">                val pointData = points.removeAt(0)</text>
  <text x="850" y="890" class="code-text">                // 添加点到路径</text>
  <text x="850" y="905" class="code-text">                readPoints.add(pointData)</text>
  <text x="850" y="920" class="code-text">                invalidate()  // 逐步重绘</text>
  <text x="840" y="935" class="code-text">            }</text>
  <text x="840" y="950" class="code-text">            delay(100)  // 100ms间隔</text>
  <text x="830" y="965" class="code-text">        }</text>
  <text x="820" y="980" class="code-text">    }</text>
  <text x="810" y="995" class="code-text">}</text>
  
  <!-- Canvas绘制 -->
  <rect x="50" y="960" width="1500" height="200" class="data-flow" rx="8"/>
  <text x="800" y="985" text-anchor="middle" class="subtitle">Canvas绘制实现 (onDraw)</text>
  
  <!-- 绘制元素 -->
  <rect x="80" y="1010" width="450" height="130" class="view-box" rx="5"/>
  <text x="305" y="1030" text-anchor="middle" class="step-title">绘制元素</text>
  <text x="90" y="1050" class="code-text">override fun onDraw(canvas: Canvas) {</text>
  <text x="100" y="1065" class="code-text">    super.onDraw(canvas)</text>
  <text x="100" y="1080" class="code-text">    </text>
  <text x="100" y="1095" class="code-text">    // 1. 绘制轨迹路径</text>
  <text x="100" y="1110" class="code-text">    canvas.drawPath(trackPath, trackPathPaint)</text>
  <text x="100" y="1125" class="code-text">    </text>
  <text x="100" y="1140" class="code-text">    // 2. 绘制视线点和序号</text>
  
  <!-- 画笔配置 -->
  <rect x="550" y="1010" width="450" height="130" class="view-box" rx="5"/>
  <text x="775" y="1030" text-anchor="middle" class="step-title">画笔配置</text>
  <text x="560" y="1050" class="code-text">// 轨迹路径画笔</text>
  <text x="560" y="1065" class="code-text">trackPathPaint: color = "#FFA118", strokeWidth = 1dp</text>
  <text x="560" y="1080" class="code-text">// 视线点画笔</text>
  <text x="560" y="1095" class="code-text">trackPointPaint: color = "#FFA118", style = FILL</text>
  <text x="560" y="1110" class="code-text">// 序号文字画笔</text>
  <text x="560" y="1125" class="code-text">trackIndexPaint: color = "#333333", textSize = 10f</text>
  
  <!-- 坐标转换 -->
  <rect x="1020" y="1010" width="450" height="130" class="view-box" rx="5"/>
  <text x="1245" y="1030" text-anchor="middle" class="step-title">坐标转换</text>
  <text x="1030" y="1050" class="code-text">// 比例坐标转屏幕坐标</text>
  <text x="1030" y="1065" class="code-text">val circleX = result.x!! * screenWidth</text>
  <text x="1030" y="1080" class="code-text">val circleY = result.y!! * screenHeight</text>
  <text x="1030" y="1095" class="code-text">// 绘制圆点和文字</text>
  <text x="1030" y="1110" class="code-text">canvas.drawCircle(circleX, circleY, radius, paint)</text>
  <text x="1030" y="1125" class="code-text">canvas.drawText(index, circleX, circleY, textPaint)</text>
  
  <!-- ExoPlayer使用详解 -->
  <rect x="50" y="1180" width="1500" height="180" class="exoplayer-box" rx="8"/>
  <text x="800" y="1205" text-anchor="middle" class="subtitle">ExoPlayer使用详解</text>
  
  <!-- ExoPlayer配置 -->
  <rect x="80" y="1230" width="450" height="110" class="data-flow" rx="5"/>
  <text x="305" y="1250" text-anchor="middle" class="step-title">ExoPlayer配置</text>
  <text x="90" y="1270" class="code-text">// 初始化</text>
  <text x="90" y="1285" class="code-text">exoPlayer = ExoPlayer.Builder(this).build()</text>
  <text x="90" y="1300" class="code-text">playerView.player = exoPlayer</text>
  <text x="90" y="1315" class="code-text">// 资源释放</text>
  <text x="90" y="1330" class="code-text">exoPlayer?.release()</text>
  
  <!-- 媒体播放 -->
  <rect x="550" y="1230" width="450" height="110" class="data-flow" rx="5"/>
  <text x="775" y="1250" text-anchor="middle" class="step-title">媒体播放控制</text>
  <text x="560" y="1270" class="code-text">private fun play(isPlay: Boolean) {</text>
  <text x="570" y="1285" class="code-text">    if (isPlay) {</text>
  <text x="580" y="1300" class="code-text">        val mediaItem = MediaItem.fromUri(</text>
  <text x="590" y="1315" class="code-text">            Uri.parse("asset:///video/read_track_normal_video.mp4")</text>
  <text x="580" y="1330" class="code-text">        )</text>
  
  <!-- 视频资源 -->
  <rect x="1020" y="1230" width="450" height="110" class="data-flow" rx="5"/>
  <text x="1245" y="1250" text-anchor="middle" class="step-title">视频资源</text>
  <text x="1030" y="1270" class="code-text">• 位置: assets/video/read_track_normal_video.mp4</text>
  <text x="1030" y="1285" class="code-text">• 用途: 展示正常阅读轨迹示例</text>
  <text x="1030" y="1300" class="code-text">• 格式: MP4视频文件</text>
  <text x="1030" y="1315" class="code-text">• 控制: 自动播放/停止</text>
  <text x="1030" y="1330" class="code-text">• UI: Media3 PlayerView组件</text>
  
  <!-- 流程箭头 -->
  <line x1="305" y1="270" x2="305" y2="340" class="data-arrow"/>
  <line x1="775" y1="270" x2="775" y2="340" class="data-arrow"/>
  <line x1="1245" y1="270" x2="1245" y2="340" class="data-arrow"/>
  
  <line x1="430" y1="570" x2="430" y2="640" class="data-arrow"/>
  <line x1="1150" y1="570" x2="1150" y2="640" class="data-arrow"/>
  
  <line x1="800" y1="940" x2="800" y2="1010" class="data-arrow"/>
  <line x1="800" y1="1160" x2="800" y2="1230" class="data-arrow"/>
</svg>
