<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .activity-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .view-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .static-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .dynamic-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .canvas-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .lifecycle-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 2; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#bluearrowhead); }
      .lifecycle-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#purplearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#3498db" />
    </marker>
    
    <marker id="purplearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">ReadTrackView执行顺序与Activity集成流程</text>
  
  <!-- Activity生命周期与集成 -->
  <rect x="50" y="70" width="1500" height="250" class="activity-box" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">ReadTrackActivity生命周期与View集成</text>
  
  <!-- onCreate -->
  <rect x="80" y="120" width="280" height="180" class="lifecycle-box" rx="5"/>
  <text x="220" y="140" text-anchor="middle" class="step-title">1. onCreate()</text>
  <text x="90" y="160" class="code-text">override fun onCreate(savedInstanceState: Bundle?) {</text>
  <text x="100" y="175" class="code-text">    super.onCreate(savedInstanceState)</text>
  <text x="100" y="190" class="code-text">    setContentView(R.layout.activity_read_track)</text>
  <text x="100" y="205" class="code-text">    </text>
  <text x="100" y="220" class="code-text">    initParam()     // 参数初始化</text>
  <text x="100" y="235" class="code-text">    initView()      // 视图初始化</text>
  <text x="100" y="250" class="code-text">    initObserver()  // 数据观察</text>
  <text x="100" y="265" class="code-text">    initData()      // 数据初始化</text>
  <text x="90" y="280" class="code-text">}</text>
  
  <!-- initView -->
  <rect x="380" y="120" width="280" height="180" class="view-box" rx="5"/>
  <text x="520" y="140" text-anchor="middle" class="step-title">2. initView()</text>
  <text x="390" y="160" class="code-text">private fun initView() {</text>
  <text x="400" y="175" class="code-text">    // 获取ReadTrackView引用</text>
  <text x="400" y="190" class="code-text">    readTrackFigure = findViewById(R.id.read_track_figure)</text>
  <text x="400" y="205" class="code-text">    </text>
  <text x="400" y="220" class="code-text">    // 设置RadioGroup监听</text>
  <text x="400" y="235" class="code-text">    rgTools.setOnCheckedChangeListener { _, checkedId -></text>
  <text x="410" y="250" class="code-text">        when (checkedId) {</text>
  <text x="420" y="265" class="code-text">            R.id.rb_track_figure -> showStaticTrack()</text>
  <text x="420" y="280" class="code-text">            R.id.rb_heat_map -> showDynamicTrack()</text>
  
  <!-- initObserver -->
  <rect x="680" y="120" width="280" height="180" class="lifecycle-box" rx="5"/>
  <text x="820" y="140" text-anchor="middle" class="step-title">3. initObserver()</text>
  <text x="690" y="160" class="code-text">private fun initObserver() {</text>
  <text x="700" y="175" class="code-text">    // 监听轨迹数据</text>
  <text x="700" y="190" class="code-text">    LiveEventBus.get&lt;GazeTrajectory&gt;(</text>
  <text x="710" y="205" class="code-text">        INPUT_PARAM_TRACK_RESULT</text>
  <text x="700" y="220" class="code-text">    ).observeSticky(this) { trajectory -></text>
  <text x="710" y="235" class="code-text">        mGazeTrajectory = trajectory</text>
  <text x="710" y="250" class="code-text">        // 默认显示静态轨迹</text>
  <text x="710" y="265" class="code-text">        rgTools.check(R.id.rb_track_figure)</text>
  <text x="700" y="280" class="code-text">    }</text>
  <text x="690" y="295" class="code-text">}</text>
  
  <!-- 数据传递 -->
  <rect x="980" y="120" width="280" height="180" class="activity-box" rx="5"/>
  <text x="1120" y="140" text-anchor="middle" class="step-title">4. 数据传递链</text>
  <text x="990" y="160" class="code-text">ReadResultAnalysisActivity:</text>
  <text x="1000" y="175" class="code-text">tvLookReadTrack.setOnSingleClickListener {</text>
  <text x="1010" y="190" class="code-text">    LiveEventBus.get&lt;GazeTrajectory&gt;(</text>
  <text x="1020" y="205" class="code-text">        INPUT_PARAM_TRACK_RESULT</text>
  <text x="1010" y="220" class="code-text">    ).post(mReadResult?.gazeTrajectory)</text>
  <text x="1010" y="235" class="code-text">    </text>
  <text x="1010" y="250" class="code-text">    startActivity(ReadTrackActivity.createIntent(this))</text>
  <text x="1000" y="265" class="code-text">}</text>
  
  <!-- ReadTrackView执行流程 -->
  <rect x="50" y="340" width="1500" height="300" class="view-box" rx="8"/>
  <text x="800" y="365" text-anchor="middle" class="subtitle">ReadTrackView执行流程详解</text>
  
  <!-- 构造函数 -->
  <rect x="80" y="390" width="280" height="230" class="lifecycle-box" rx="5"/>
  <text x="220" y="410" text-anchor="middle" class="step-title">1. 构造函数初始化</text>
  <text x="90" y="430" class="code-text">class ReadTrackView @JvmOverloads constructor(</text>
  <text x="100" y="445" class="code-text">    context: Context,</text>
  <text x="100" y="460" class="code-text">    attrs: AttributeSet? = null,</text>
  <text x="100" y="475" class="code-text">    defStyleAttr: Int = 0</text>
  <text x="90" y="490" class="code-text">) : View(context, attrs, defStyleAttr) {</text>
  <text x="100" y="505" class="code-text">    </text>
  <text x="100" y="520" class="code-text">    // 初始化Paint对象</text>
  <text x="100" y="535" class="code-text">    trackPathPaint = Paint().apply { ... }</text>
  <text x="100" y="550" class="code-text">    trackPointPaint = Paint().apply { ... }</text>
  <text x="100" y="565" class="code-text">    trackIndexPaint = Paint().apply { ... }</text>
  <text x="100" y="580" class="code-text">    </text>
  <text x="100" y="595" class="code-text">    // 初始化Path和数据结构</text>
  <text x="100" y="610" class="code-text">    trackPath = Path()</text>
  
  <!-- drawReadTrac -->
  <rect x="380" y="390" width="280" height="230" class="view-box" rx="5"/>
  <text x="520" y="410" text-anchor="middle" class="step-title">2. drawReadTrac()调用</text>
  <text x="390" y="430" class="code-text">fun drawReadTrac(</text>
  <text x="400" y="445" class="code-text">    gazeTrajectory: GazeTrajectory?,</text>
  <text x="400" y="460" class="code-text">    mode: DrawMode = DrawMode.STATIC</text>
  <text x="390" y="475" class="code-text">) {</text>
  <text x="400" y="490" class="code-text">    mGazeTrajectory = gazeTrajectory</text>
  <text x="400" y="505" class="code-text">    mDrawMode = mode</text>
  <text x="400" y="520" class="code-text">    when(mode) {</text>
  <text x="410" y="535" class="code-text">        DrawMode.STATIC -> staticDraw()</text>
  <text x="410" y="550" class="code-text">        DrawMode.DYNAMIC -> dynamicDraw()</text>
  <text x="400" y="565" class="code-text">    }</text>
  <text x="390" y="580" class="code-text">}</text>
  <text x="390" y="600" class="step-text" font-weight="bold">触发时机:</text>
  <text x="390" y="615" class="code-text">• RadioGroup选择变化时调用</text>
  
  <!-- 模式选择 -->
  <rect x="680" y="390" width="280" height="230" class="static-box" rx="5"/>
  <text x="820" y="410" text-anchor="middle" class="step-title">3. 绘制模式选择</text>
  <text x="690" y="430" class="step-text" font-weight="bold">静态模式 (STATIC):</text>
  <text x="690" y="445" class="code-text">• 一次性处理所有数据点</text>
  <text x="690" y="460" class="code-text">• 构建完整Path路径</text>
  <text x="690" y="475" class="code-text">• 单次invalidate()触发重绘</text>
  
  <text x="690" y="500" class="step-text" font-weight="bold">动态模式 (DYNAMIC):</text>
  <text x="690" y="515" class="code-text">• 协程逐点处理</text>
  <text x="690" y="530" class="code-text">• 100ms延迟间隔</text>
  <text x="690" y="545" class="code-text">• 每点都触发invalidate()</text>
  
  <text x="690" y="570" class="step-text" font-weight="bold">数据处理:</text>
  <text x="690" y="585" class="code-text">• filter { it.checkValid() }</text>
  <text x="690" y="600" class="code-text">• 坐标转换: [0~1] → 屏幕坐标</text>
  <text x="690" y="615" class="code-text">• Path构建: moveTo() + lineTo()</text>
  
  <!-- onDraw -->
  <rect x="980" y="390" width="280" height="230" class="canvas-box" rx="5"/>
  <text x="1120" y="410" text-anchor="middle" class="step-title">4. onDraw()绘制</text>
  <text x="990" y="430" class="code-text">override fun onDraw(canvas: Canvas) {</text>
  <text x="1000" y="445" class="code-text">    super.onDraw(canvas)</text>
  <text x="1000" y="460" class="code-text">    </text>
  <text x="1000" y="475" class="code-text">    // 1. 绘制轨迹路径</text>
  <text x="1000" y="490" class="code-text">    canvas.drawPath(trackPath, trackPathPaint)</text>
  <text x="1000" y="505" class="code-text">    </text>
  <text x="1000" y="520" class="code-text">    // 2. 绘制视线点和序号</text>
  <text x="1000" y="535" class="code-text">    readPoints.forEach { result -></text>
  <text x="1010" y="550" class="code-text">        canvas.drawCircle(...)</text>
  <text x="1010" y="565" class="code-text">        canvas.drawText(...)</text>
  <text x="1000" y="580" class="code-text">    }</text>
  <text x="990" y="595" class="code-text">}</text>
  
  <!-- 静态绘制详细流程 -->
  <rect x="50" y="660" width="750" height="300" class="static-box" rx="8"/>
  <text x="425" y="685" text-anchor="middle" class="subtitle">静态绘制详细流程 (staticDraw)</text>
  
  <!-- 步骤1 -->
  <rect x="80" y="710" width="160" height="120" class="lifecycle-box" rx="5"/>
  <text x="160" y="730" text-anchor="middle" class="step-title">1. 数据清理</text>
  <text x="90" y="750" class="code-text">readPoints.clear()</text>
  <text x="90" y="765" class="code-text">trackPath.reset()</text>
  <text x="90" y="785" class="step-text">清空之前的数据</text>
  <text x="90" y="800" class="step-text">重置绘制路径</text>
  <text x="90" y="815" class="step-text">准备新的绘制</text>
  
  <!-- 步骤2 -->
  <rect x="260" y="710" width="160" height="120" class="view-box" rx="5"/>
  <text x="340" y="730" text-anchor="middle" class="step-title">2. 数据过滤</text>
  <text x="270" y="750" class="code-text">val gaze = mGazeTrajectory</text>
  <text x="280" y="765" class="code-text">?.gaze?.filter {</text>
  <text x="290" y="780" class="code-text">    it.checkValid()</text>
  <text x="280" y="795" class="code-text">} ?: emptyList()</text>
  <text x="270" y="815" class="step-text">过滤无效数据点</text>
  
  <!-- 步骤3 -->
  <rect x="440" y="710" width="160" height="120" class="static-box" rx="5"/>
  <text x="520" y="730" text-anchor="middle" class="step-title">3. 路径构建</text>
  <text x="450" y="750" class="code-text">readPoints.addAll(gaze)</text>
  <text x="450" y="765" class="code-text">forEachIndexed { index, result -></text>
  <text x="460" y="780" class="code-text">if (index == 0)</text>
  <text x="470" y="795" class="code-text">trackPath.moveTo(x,y)</text>
  <text x="460" y="810" class="code-text">else trackPath.lineTo(x,y)</text>
  <text x="450" y="825" class="code-text">}</text>
  
  <!-- 步骤4 -->
  <rect x="620" y="710" width="160" height="120" class="canvas-box" rx="5"/>
  <text x="700" y="730" text-anchor="middle" class="step-title">4. 触发重绘</text>
  <text x="630" y="750" class="code-text">invalidate()</text>
  <text x="630" y="770" class="step-text">触发onDraw()调用</text>
  <text x="630" y="785" class="step-text">系统调度重绘</text>
  <text x="630" y="800" class="step-text">Canvas绘制执行</text>
  <text x="630" y="815" class="step-text">UI更新完成</text>
  
  <!-- 动态绘制详细流程 -->
  <rect x="820" y="660" width="730" height="300" class="dynamic-box" rx="8"/>
  <text x="1185" y="685" text-anchor="middle" class="subtitle">动态绘制详细流程 (dynamicDraw)</text>
  
  <!-- 协程启动 -->
  <rect x="850" y="710" width="160" height="120" class="lifecycle-box" rx="5"/>
  <text x="930" y="730" text-anchor="middle" class="step-title">1. 协程启动</text>
  <text x="860" y="750" class="code-text">dynamicDrawJob?.cancel()</text>
  <text x="860" y="765" class="code-text">dynamicDrawJob = </text>
  <text x="870" y="780" class="code-text">coroutineScope.launch(</text>
  <text x="880" y="795" class="code-text">Dispatchers.Default</text>
  <text x="870" y="810" class="code-text">) { ... }</text>
  <text x="860" y="825" class="step-text">后台线程处理</text>
  
  <!-- 逐点处理 -->
  <rect x="1030" y="710" width="160" height="120" class="dynamic-box" rx="5"/>
  <text x="1110" y="730" text-anchor="middle" class="step-title">2. 逐点处理</text>
  <text x="1040" y="750" class="code-text">while (points.isNotEmpty()) {</text>
  <text x="1050" y="765" class="code-text">val pointData = </text>
  <text x="1060" y="780" class="code-text">points.removeAt(0)</text>
  <text x="1050" y="795" class="code-text">readPoints.add(pointData)</text>
  <text x="1050" y="810" class="code-text">trackPath.lineTo(x,y)</text>
  <text x="1050" y="825" class="code-text">invalidate()</text>
  
  <!-- 延迟控制 -->
  <rect x="1210" y="710" width="160" height="120" class="view-box" rx="5"/>
  <text x="1290" y="730" text-anchor="middle" class="step-title">3. 延迟控制</text>
  <text x="1220" y="750" class="code-text">withContext(Dispatchers.Main) {</text>
  <text x="1230" y="765" class="code-text">    // UI更新</text>
  <text x="1230" y="780" class="code-text">    invalidate()</text>
  <text x="1220" y="795" class="code-text">}</text>
  <text x="1220" y="810" class="code-text">delay(100)  // 100ms间隔</text>
  <text x="1220" y="825" class="step-text">动画效果控制</text>
  
  <!-- 协程动画循环 -->
  <rect x="850" y="850" width="500" height="90" class="dynamic-box" rx="5"/>
  <text x="1100" y="870" text-anchor="middle" class="step-title">协程动画循环详解</text>
  <text x="860" y="890" class="code-text">1. 后台线程取出一个点 → 2. 切换到主线程更新UI → 3. 延迟100ms → 4. 重复直到所有点处理完</text>
  <text x="860" y="905" class="step-text">• 避免阻塞UI线程 • 提供流畅的动画效果 • 可以随时取消 • 内存安全</text>
  <text x="860" y="920" class="step-text">• NonCancellable确保资源清理 • withContext保证线程安全 • Job管理生命周期</text>
  
  <!-- 生命周期管理 -->
  <rect x="50" y="980" width="1500" height="200" class="lifecycle-box" rx="8"/>
  <text x="800" y="1005" text-anchor="middle" class="subtitle">生命周期管理与资源清理</text>
  
  <!-- onDetachedFromWindow -->
  <rect x="80" y="1030" width="450" height="130" class="canvas-box" rx="5"/>
  <text x="305" y="1050" text-anchor="middle" class="step-title">onDetachedFromWindow()资源清理</text>
  <text x="90" y="1070" class="code-text">override fun onDetachedFromWindow() {</text>
  <text x="100" y="1085" class="code-text">    super.onDetachedFromWindow()</text>
  <text x="100" y="1100" class="code-text">    coroutineScope.cancel()  // 取消所有协程</text>
  <text x="90" y="1115" class="code-text">}</text>
  <text x="90" y="1135" class="step-text">• 防止内存泄漏 • 取消动画协程 • 清理资源引用</text>
  <text x="90" y="1150" class="step-text">• View从窗口分离时自动调用</text>
  
  <!-- Activity生命周期影响 -->
  <rect x="550" y="1030" width="450" height="130" class="activity-box" rx="5"/>
  <text x="775" y="1050" text-anchor="middle" class="step-title">Activity生命周期影响</text>
  <text x="560" y="1070" class="step-text" font-weight="bold">onPause() / onStop():</text>
  <text x="560" y="1085" class="code-text">• 动态绘制协程继续运行</text>
  <text x="560" y="1100" class="code-text">• 但UI更新会被暂停</text>
  
  <text x="560" y="1120" class="step-text" font-weight="bold">onDestroy():</text>
  <text x="560" y="1135" class="code-text">• View自动调用onDetachedFromWindow()</text>
  <text x="560" y="1150" class="code-text">• 协程被正确取消和清理</text>
  
  <!-- 性能优化 -->
  <rect x="1020" y="1030" width="450" height="130" class="view-box" rx="5"/>
  <text x="1245" y="1050" text-anchor="middle" class="step-title">性能优化要点</text>
  <text x="1030" y="1070" class="step-text" font-weight="bold">Paint对象复用:</text>
  <text x="1030" y="1085" class="code-text">• 构造函数中初始化，避免onDraw()中创建</text>
  
  <text x="1030" y="1105" class="step-text" font-weight="bold">Path重用:</text>
  <text x="1030" y="1120" class="code-text">• reset()重置而非重新创建</text>
  
  <text x="1030" y="1140" class="step-text" font-weight="bold">协程优化:</text>
  <text x="1030" y="1155" class="code-text">• 后台处理数据，主线程只负责UI更新</text>
  
  <!-- 执行时序图 -->
  <rect x="50" y="1200" width="1500" height="350" class="lifecycle-box" rx="8"/>
  <text x="800" y="1225" text-anchor="middle" class="subtitle">完整执行时序图</text>
  
  <!-- 时序步骤 -->
  <rect x="80" y="1250" width="120" height="60" class="activity-box" rx="3"/>
  <text x="140" y="1270" text-anchor="middle" class="step-text">Activity</text>
  <text x="140" y="1285" text-anchor="middle" class="step-text">onCreate</text>
  <text x="140" y="1300" text-anchor="middle" class="code-text">布局加载</text>
  
  <rect x="220" y="1250" width="120" height="60" class="view-box" rx="3"/>
  <text x="280" y="1270" text-anchor="middle" class="step-text">View</text>
  <text x="280" y="1285" text-anchor="middle" class="step-text">构造函数</text>
  <text x="280" y="1300" text-anchor="middle" class="code-text">Paint初始化</text>
  
  <rect x="360" y="1250" width="120" height="60" class="lifecycle-box" rx="3"/>
  <text x="420" y="1270" text-anchor="middle" class="step-text">LiveEventBus</text>
  <text x="420" y="1285" text-anchor="middle" class="step-text">数据接收</text>
  <text x="420" y="1300" text-anchor="middle" class="code-text">轨迹数据</text>
  
  <rect x="500" y="1250" width="120" height="60" class="static-box" rx="3"/>
  <text x="560" y="1270" text-anchor="middle" class="step-text">RadioGroup</text>
  <text x="560" y="1285" text-anchor="middle" class="step-text">模式选择</text>
  <text x="560" y="1300" text-anchor="middle" class="code-text">触发绘制</text>
  
  <rect x="640" y="1250" width="120" height="60" class="view-box" rx="3"/>
  <text x="700" y="1270" text-anchor="middle" class="step-text">drawReadTrac</text>
  <text x="700" y="1285" text-anchor="middle" class="step-text">方法调用</text>
  <text x="700" y="1300" text-anchor="middle" class="code-text">模式分发</text>
  
  <rect x="780" y="1250" width="120" height="60" class="static-box" rx="3"/>
  <text x="840" y="1270" text-anchor="middle" class="step-text">staticDraw/</text>
  <text x="840" y="1285" text-anchor="middle" class="step-text">dynamicDraw</text>
  <text x="840" y="1300" text-anchor="middle" class="code-text">数据处理</text>
  
  <rect x="920" y="1250" width="120" height="60" class="canvas-box" rx="3"/>
  <text x="980" y="1270" text-anchor="middle" class="step-text">invalidate</text>
  <text x="980" y="1285" text-anchor="middle" class="step-text">触发重绘</text>
  <text x="980" y="1300" text-anchor="middle" class="code-text">系统调度</text>
  
  <rect x="1060" y="1250" width="120" height="60" class="canvas-box" rx="3"/>
  <text x="1120" y="1270" text-anchor="middle" class="step-text">onDraw</text>
  <text x="1120" y="1285" text-anchor="middle" class="step-text">Canvas绘制</text>
  <text x="1120" y="1300" text-anchor="middle" class="code-text">UI渲染</text>
  
  <rect x="1200" y="1250" width="120" height="60" class="lifecycle-box" rx="3"/>
  <text x="1260" y="1270" text-anchor="middle" class="step-text">用户可见</text>
  <text x="1260" y="1285" text-anchor="middle" class="step-text">轨迹显示</text>
  <text x="1260" y="1300" text-anchor="middle" class="code-text">交互完成</text>
  
  <!-- 时序箭头 -->
  <line x1="200" y1="1280" x2="220" y2="1280" class="flow-arrow"/>
  <line x1="340" y1="1280" x2="360" y2="1280" class="flow-arrow"/>
  <line x1="480" y1="1280" x2="500" y2="1280" class="flow-arrow"/>
  <line x1="620" y1="1280" x2="640" y2="1280" class="flow-arrow"/>
  <line x1="760" y1="1280" x2="780" y2="1280" class="flow-arrow"/>
  <line x1="900" y1="1280" x2="920" y2="1280" class="flow-arrow"/>
  <line x1="1040" y1="1280" x2="1060" y2="1280" class="flow-arrow"/>
  <line x1="1180" y1="1280" x2="1200" y2="1280" class="flow-arrow"/>
  
  <!-- 动态模式循环 -->
  <rect x="80" y="1330" width="1400" height="80" class="dynamic-box" rx="5"/>
  <text x="780" y="1350" text-anchor="middle" class="step-title">动态模式特殊流程：协程循环</text>
  <text x="100" y="1370" class="code-text">dynamicDraw() → 协程启动 → while循环 → 取点 → 主线程更新 → invalidate() → onDraw() → delay(100ms) → 下一个点...</text>
  <text x="100" y="1390" class="step-text">• 每100ms处理一个点 • 每次都触发重绘 • 直到所有点处理完成 • 可随时取消</text>
  
  <!-- 关键特性总结 -->
  <rect x="50" y="1430" width="1500" height="120" class="lifecycle-box" rx="8"/>
  <text x="800" y="1455" text-anchor="middle" class="subtitle">ReadTrackView关键特性总结</text>
  
  <text x="80" y="1480" class="step-text" font-weight="bold">🎯 核心功能:</text>
  <text x="100" y="1495" class="step-text">• 静态轨迹显示：一次性绘制完整阅读路径 • 动态轨迹显示：100ms间隔逐点动画绘制</text>
  
  <text x="80" y="1515" class="step-text" font-weight="bold">⚡ 性能优化:</text>
  <text x="100" y="1530" class="step-text">• Paint对象复用 • Path重用 • 协程异步处理 • 精确的invalidate()控制 • 自动资源清理</text>
</svg>
