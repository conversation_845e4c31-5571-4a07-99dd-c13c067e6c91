<?xml version="1.0" encoding="UTF-8"?>
<svg width="1500" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .class-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .method-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .inheritance-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#inheritanceArrow); }
      .composition-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#compositionArrow); }
      
      .readactivity-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .gtbase-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .basecommon-box { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      
      .feature-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      .flow-box { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
    </style>
    
    <marker id="inheritanceArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
    
    <marker id="compositionArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="750" y="30" text-anchor="middle" class="title">ReadActivity 继承层次结构与功能分析</text>
  
  <!-- ReadActivity -->
  <rect x="50" y="70" width="320" height="220" class="readactivity-box" rx="8"/>
  <text x="210" y="95" text-anchor="middle" class="class-title">ReadActivity</text>
  <text x="70" y="115" class="description-text">阅读测试Activity - 核心业务实现</text>
  
  <text x="70" y="140" class="method-text" font-weight="bold">核心功能:</text>
  <text x="70" y="155" class="method-text">• 阅读测试流程控制</text>
  <text x="70" y="170" class="method-text">• 3秒倒计时准备</text>
  <text x="70" y="185" class="method-text">• 视线追踪数据收集</text>
  <text x="70" y="200" class="method-text">• 阅读结果计算和跳转</text>
  
  <text x="70" y="225" class="method-text" font-weight="bold">重写方法:</text>
  <text x="70" y="240" class="method-text">• enableBack() = false</text>
  <text x="70" y="255" class="method-text">• onStart() - 绑定GazeTrackService</text>
  <text x="70" y="270" class="method-text">• onStop() - 停止追踪和解绑服务</text>
  
  <!-- GTBaseActivity -->
  <rect x="450" y="70" width="350" height="220" class="gtbase-box" rx="8"/>
  <text x="625" y="95" text-anchor="middle" class="class-title">GTBaseActivity</text>
  <text x="470" y="115" class="description-text">项目基础Activity - 提供MQTT和全屏功能</text>
  
  <text x="470" y="140" class="method-text" font-weight="bold">重写方法:</text>
  <text x="470" y="155" class="method-text">• enableFullScreen(): Boolean = true</text>
  <text x="470" y="170" class="description-text">  强制启用全屏模式</text>
  
  <text x="470" y="195" class="method-text" font-weight="bold">新增功能:</text>
  <text x="470" y="210" class="method-text">• initMQTT() - 初始化MQTT连接</text>
  <text x="470" y="225" class="description-text">  阿里云IoT Hub设备通信</text>
  <text x="470" y="240" class="description-text">  协程异步处理连接逻辑</text>
  
  <text x="470" y="265" class="method-text" font-weight="bold">技术特性:</text>
  <text x="470" y="280" class="method-text">• 项目级通用功能封装</text>
  
  <!-- BaseCommonActivity -->
  <rect x="900" y="70" width="400" height="220" class="basecommon-box" rx="8"/>
  <text x="1100" y="95" text-anchor="middle" class="class-title">BaseCommonActivity</text>
  <text x="920" y="115" class="description-text">通用基础Activity - 提供通用功能和生命周期管理</text>
  
  <text x="920" y="140" class="method-text" font-weight="bold">核心抽象方法:</text>
  <text x="920" y="155" class="method-text">• enableFullScreen(): Boolean</text>
  <text x="920" y="170" class="method-text">• enableBack(): Boolean</text>
  
  <text x="920" y="195" class="method-text" font-weight="bold">通用功能:</text>
  <text x="920" y="210" class="method-text">• sendMessageToService() - 服务通信</text>
  <text x="920" y="225" class="method-text">• countdown() - 倒计时功能</text>
  <text x="920" y="240" class="method-text">• 生命周期统一管理</text>
  <text x="920" y="255" class="method-text">• Service绑定解绑管理</text>
  
  <text x="920" y="280" class="method-text" font-weight="bold">扩展功能:</text>
  <text x="920" y="295" class="method-text">• UI操作扩展方法</text>
  
  <!-- Android Activity -->
  <rect x="1100" y="340" width="300" height="120" class="activity-box" rx="8"/>
  <text x="1250" y="365" text-anchor="middle" class="class-title">Android Activity</text>
  <text x="1120" y="385" class="description-text">Android框架基础Activity类</text>
  
  <text x="1120" y="405" class="method-text" font-weight="bold">系统功能:</text>
  <text x="1120" y="420" class="method-text">• 生命周期管理</text>
  <text x="1120" y="435" class="method-text">• Intent处理, UI管理</text>
  <text x="1120" y="450" class="method-text">• 系统资源管理</text>
  
  <!-- 继承箭头 -->
  <line x1="370" y1="180" x2="450" y2="180" class="inheritance-arrow"/>
  <line x1="800" y1="180" x2="900" y2="180" class="inheritance-arrow"/>
  <line x1="1100" y1="290" x2="1250" y2="340" class="inheritance-arrow"/>
  
  <!-- ReadActivity详细功能分析 -->
  <rect x="50" y="320" width="1000" height="620" class="feature-box" rx="5"/>
  <text x="550" y="345" text-anchor="middle" class="class-title">ReadActivity 详细功能分析</text>
  
  <!-- 业务流程 -->
  <rect x="70" y="360" width="460" height="280" class="flow-box" rx="5"/>
  <text x="300" y="380" text-anchor="middle" class="method-text" font-weight="bold">阅读测试业务流程</text>
  
  <text x="90" y="405" class="method-text" font-weight="bold">1. 初始化阶段 (onCreate):</text>
  <text x="110" y="420" class="method-text">• initParam() - 获取身份和年级参数</text>
  <text x="110" y="435" class="method-text">• initView() - 启动3秒倒计时</text>
  <text x="110" y="450" class="method-text">• countdown(3000, 1000, onTick, onCompletion)</text>
  
  <text x="90" y="475" class="method-text" font-weight="bold">2. 测试开始 (startRead):</text>
  <text x="110" y="490" class="method-text">• 隐藏倒计时界面</text>
  <text x="110" y="505" class="method-text">• 发送MSG_TURN_ON_CAMERA</text>
  <text x="110" y="520" class="method-text">• 发送MSG_START_TRACK</text>
  <text x="110" y="535" class="method-text">• 发送MSG_START_APPLIED_READING</text>
  
  <text x="90" y="560" class="method-text" font-weight="bold">3. 测试结束:</text>
  <text x="110" y="575" class="method-text">• 用户点击"完成阅读"</text>
  <text x="110" y="590" class="method-text">• 记录结束时间</text>
  <text x="110" y="605" class="method-text">• 发送MSG_GET_GAZE_TRAJECTORY</text>
  <text x="110" y="620" class="method-text">• 跳转到结果分析页面</text>
  
  <!-- 服务通信机制 -->
  <rect x="550" y="360" width="480" height="280" class="readactivity-box" rx="5"/>
  <text x="790" y="380" text-anchor="middle" class="method-text" font-weight="bold">服务通信机制</text>
  
  <text x="570" y="405" class="method-text" font-weight="bold">1. Service连接:</text>
  <text x="590" y="420" class="method-text">• onStart() - bindService(GazeTrackService)</text>
  <text x="590" y="435" class="method-text">• ServiceConnection回调处理</text>
  <text x="590" y="450" class="method-text">• Messenger双向通信建立</text>
  
  <text x="570" y="475" class="method-text" font-weight="bold">2. 消息处理:</text>
  <text x="590" y="490" class="method-text">• parseMessage() - 处理Service回调</text>
  <text x="590" y="505" class="method-text">• MSG_GAZE_TRACKING_STATE - 追踪状态</text>
  <text x="590" y="520" class="method-text">• MSG_APPLIED_READING_STATE - 阅读状态</text>
  <text x="590" y="535" class="method-text">• MSG_GAZE_TRAJECTORY_RESULT - 轨迹数据</text>
  
  <text x="570" y="560" class="method-text" font-weight="bold">3. 数据处理:</text>
  <text x="590" y="575" class="method-text">• JSON解析GazeTrajectory</text>
  <text x="590" y="590" class="method-text">• 创建ReadResult(621字, 时长, 轨迹)</text>
  <text x="590" y="605" class="method-text">• LiveEventBus传递结果数据</text>
  <text x="590" y="620" class="method-text">• 停止追踪和关闭摄像头</text>
  
  <!-- 技术特性分析 -->
  <rect x="70" y="660" width="960" height="260" class="basecommon-box" rx="5"/>
  <text x="550" y="680" text-anchor="middle" class="method-text" font-weight="bold">ReadActivity 技术特性分析</text>
  
  <text x="90" y="705" class="method-text" font-weight="bold">1. 生命周期管理:</text>
  <text x="110" y="720" class="method-text">• onCreate() - 初始化参数和界面</text>
  <text x="110" y="735" class="method-text">• onStart() - 绑定GazeTrackService服务</text>
  <text x="110" y="750" class="method-text">• onStop() - 停止追踪、关闭摄像头、解绑服务</text>
  <text x="110" y="765" class="method-text">• enableBack() = false - 禁用返回键</text>
  
  <text x="90" y="790" class="method-text" font-weight="bold">2. 异步处理:</text>
  <text x="110" y="805" class="method-text">• LifecycleHandler - 生命周期感知的消息处理</text>
  <text x="110" y="820" class="method-text">• lifecycleScope.launch - 协程处理轨迹数据</text>
  <text x="110" y="835" class="method-text">• @Volatile isFinishRead - 线程安全的状态标记</text>
  
  <text x="90" y="860" class="method-text" font-weight="bold">3. 数据传递:</text>
  <text x="110" y="875" class="method-text">• Intent参数传递 - ReadIdentity, ReadGrade</text>
  <text x="110" y="890" class="method-text">• LiveEventBus - 跨组件数据传递</text>
  <text x="110" y="905" class="method-text">• Messenger - 与Service双向通信</text>
  
  <!-- 继承关系说明 -->
  <text x="1100" y="520" class="method-text" font-weight="bold">继承关系:</text>
  <text x="1100" y="540" class="method-text">ReadActivity</text>
  <text x="1120" y="555" class="method-text">↓ 继承</text>
  <text x="1100" y="570" class="method-text">GTBaseActivity</text>
  <text x="1120" y="585" class="method-text">↓ 继承</text>
  <text x="1100" y="600" class="method-text">BaseCommonActivity</text>
  <text x="1120" y="615" class="method-text">↓ 继承</text>
  <text x="1100" y="630" class="method-text">Android Activity</text>
  
  <text x="1100" y="660" class="method-text" font-weight="bold">功能层次:</text>
  <text x="1100" y="675" class="method-text">• 阅读测试业务层</text>
  <text x="1100" y="690" class="method-text">• 项目基础功能层</text>
  <text x="1100" y="705" class="method-text">• 通用组件功能层</text>
  <text x="1100" y="720" class="method-text">• Android系统层</text>
  
  <text x="1100" y="750" class="method-text" font-weight="bold">核心特点:</text>
  <text x="1100" y="765" class="method-text">• 完整的测试流程</text>
  <text x="1100" y="780" class="method-text">• 服务通信机制</text>
  <text x="1100" y="795" class="method-text">• 异步数据处理</text>
  <text x="1100" y="810" class="method-text">• 生命周期管理</text>
  <text x="1100" y="825" class="method-text">• 禁用返回键</text>
</svg>
