<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .activity-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .service-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .message-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .step-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
      .lifecycle-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#purplearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
    
    <marker id="purplearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">ReadActivity ↔ GazeTrackService 通信模式详解</text>
  
  <!-- 主要组件 -->
  <rect x="100" y="70" width="300" height="80" class="activity-box" rx="8"/>
  <text x="250" y="95" text-anchor="middle" class="subtitle">ReadActivity</text>
  <text x="120" y="115" class="step-text">• mServiceMessage: Messenger?</text>
  <text x="120" y="130" class="step-text">• mClientMessage: Messenger</text>
  <text x="120" y="145" class="step-text">• serviceConnection: ServiceConnection</text>
  
  <rect x="1200" y="70" width="300" height="80" class="service-box" rx="8"/>
  <text x="1350" y="95" text-anchor="middle" class="subtitle">GazeTrackService</text>
  <text x="1220" y="115" class="step-text">• mServiceMessage: Messenger</text>
  <text x="1220" y="130" class="step-text">• 处理各种消息类型</text>
  <text x="1220" y="145" class="step-text">• 返回处理结果</text>
  
  <!-- 通信流程步骤 -->
  <rect x="50" y="180" width="1500" height="980" class="step-box" rx="5"/>
  <text x="800" y="205" text-anchor="middle" class="subtitle">通信流程执行步骤</text>
  
  <!-- 步骤1: 生命周期启动 -->
  <rect x="80" y="230" width="1440" height="80" class="message-box" rx="5"/>
  <text x="100" y="250" class="step-title">步骤1: 生命周期启动 - onStart()</text>
  <text x="100" y="270" class="step-text">作用: Activity启动时自动绑定Service，建立通信连接</text>
  <text x="100" y="285" class="code-text">override fun onStart() { super.onStart(); bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE) }</text>
  <text x="100" y="300" class="description-text">• 在Activity可见时绑定Service • 使用BIND_AUTO_CREATE自动创建Service • 传入serviceConnection回调处理连接结果</text>
  
  <!-- 箭头1 -->
  <line x1="250" y1="320" x2="1350" y2="320" class="lifecycle-arrow"/>
  <text x="800" y="315" text-anchor="middle" class="description-text">bindService()</text>
  
  <!-- 步骤2: Service连接建立 -->
  <rect x="80" y="340" width="1440" height="80" class="service-box" rx="5"/>
  <text x="100" y="360" class="step-title">步骤2: Service连接建立 - onServiceConnected()</text>
  <text x="100" y="380" class="step-text">作用: Service绑定成功后，获取Service的Messenger，建立双向通信通道</text>
  <text x="100" y="395" class="code-text">mServiceMessage = Messenger(service); sendMessageToService(MSG_SERVICE_CONNECTED, replyTo = mClientMessage)</text>
  <text x="100" y="410" class="description-text">• 保存Service的Messenger引用 • 发送连接确认消息 • 传递客户端Messenger给Service用于回复</text>
  
  <!-- 箭头2 -->
  <line x1="1350" y1="430" x2="250" y2="430" class="return-arrow"/>
  <text x="800" y="425" text-anchor="middle" class="description-text">onServiceConnected(IBinder)</text>
  
  <!-- 步骤3: 发送连接确认 -->
  <rect x="80" y="450" width="1440" height="80" class="activity-box" rx="5"/>
  <text x="100" y="470" class="step-title">步骤3: 发送连接确认 - MSG_SERVICE_CONNECTED</text>
  <text x="100" y="490" class="step-text">作用: 通知Service连接已建立，传递客户端Messenger用于双向通信</text>
  <text x="100" y="505" class="code-text">Message.obtain().apply { what = MSG_SERVICE_CONNECTED; replyTo = mClientMessage }</text>
  <text x="100" y="520" class="description-text">• 确认连接状态 • 建立双向通信通道 • Service获得回复消息的能力</text>
  
  <!-- 箭头3 -->
  <line x1="250" y1="540" x2="1350" y2="540" class="arrow"/>
  <text x="800" y="535" text-anchor="middle" class="description-text">MSG_SERVICE_CONNECTED + replyTo</text>
  
  <!-- 步骤4: 开始测试消息 -->
  <rect x="80" y="560" width="1440" height="80" class="activity-box" rx="5"/>
  <text x="100" y="580" class="step-title">步骤4: 开始测试 - 批量发送控制消息</text>
  <text x="100" y="600" class="step-text">作用: 启动阅读测试，发送多个控制消息到Service执行相应操作</text>
  <text x="100" y="615" class="code-text">sendMessageToService(MSG_TURN_ON_CAMERA, MSG_START_TRACK, MSG_START_APPLIED_READING)</text>
  <text x="100" y="630" class="description-text">• 开启摄像头 • 启动视线追踪 • 开始应用阅读模式 • 批量发送提高效率</text>
  
  <!-- 箭头4 -->
  <line x1="250" y1="650" x2="1350" y2="650" class="arrow"/>
  <text x="800" y="645" text-anchor="middle" class="description-text">MSG_TURN_ON_CAMERA + MSG_START_TRACK + MSG_START_APPLIED_READING</text>
  
  <!-- 步骤5: Service处理和状态回复 -->
  <rect x="80" y="670" width="1440" height="80" class="service-box" rx="5"/>
  <text x="100" y="690" class="step-title">步骤5: Service处理和状态回复</text>
  <text x="100" y="710" class="step-text">作用: Service执行相应操作并回复状态，Activity接收状态更新</text>
  <text x="100" y="725" class="code-text">Service发送: MSG_GAZE_TRACKING_STATE, MSG_APPLIED_READING_STATE</text>
  <text x="100" y="740" class="description-text">• Service执行摄像头、追踪等操作 • 回复操作状态给Activity • Activity更新UI和内部状态</text>
  
  <!-- 箭头5 -->
  <line x1="1350" y1="760" x2="250" y2="760" class="return-arrow"/>
  <text x="800" y="755" text-anchor="middle" class="description-text">MSG_GAZE_TRACKING_STATE + MSG_APPLIED_READING_STATE</text>
  
  <!-- 步骤6: 测试过程中的数据交换 -->
  <rect x="80" y="780" width="1440" height="80" class="message-box" rx="5"/>
  <text x="100" y="800" class="step-title">步骤6: 测试过程中的数据交换</text>
  <text x="100" y="820" class="step-text">作用: 测试进行中，Activity和Service持续交换数据和状态信息</text>
  <text x="100" y="835" class="code-text">双向通信: Activity ↔ Service (状态更新、数据收集、实时反馈)</text>
  <text x="100" y="850" class="description-text">• 实时视线数据收集 • 状态监控和反馈 • 异常处理和恢复</text>
  
  <!-- 步骤7: 获取测试结果 -->
  <rect x="80" y="890" width="1440" height="80" class="activity-box" rx="5"/>
  <text x="100" y="910" class="step-title">步骤7: 获取测试结果 - MSG_GET_GAZE_TRAJECTORY</text>
  <text x="100" y="930" class="step-text">作用: 测试结束时，请求Service返回完整的视线轨迹数据</text>
  <text x="100" y="945" class="code-text">sendMessageToService(MSG_GET_GAZE_TRAJECTORY)</text>
  <text x="100" y="960" class="description-text">• 请求完整轨迹数据 • 准备结束测试流程 • 等待数据返回</text>
  
  <!-- 箭头7 -->
  <line x1="250" y1="980" x2="1350" y2="980" class="arrow"/>
  <text x="800" y="975" text-anchor="middle" class="description-text">MSG_GET_GAZE_TRAJECTORY</text>
  
  <!-- 步骤8: 返回轨迹数据 -->
  <rect x="80" y="1000" width="1440" height="80" class="service-box" rx="5"/>
  <text x="100" y="1020" class="step-title">步骤8: 返回轨迹数据 - MSG_GAZE_TRAJECTORY_RESULT</text>
  <text x="100" y="1040" class="step-text">作用: Service返回JSON格式的完整视线轨迹数据给Activity</text>
  <text x="100" y="1055" class="code-text">Service返回: MSG_GAZE_TRAJECTORY_RESULT + JSON数据</text>
  <text x="100" y="1070" class="description-text">• 返回完整轨迹JSON • Activity解析数据 • 创建ReadResult对象</text>
  
  <!-- 箭头8 -->
  <line x1="1350" y1="1090" x2="250" y2="1090" class="return-arrow"/>
  <text x="800" y="1085" text-anchor="middle" class="description-text">MSG_GAZE_TRAJECTORY_RESULT + JSON数据</text>
  
  <!-- 步骤9: 停止测试和清理 -->
  <rect x="80" y="1110" width="1440" height="50" class="activity-box" rx="5"/>
  <text x="100" y="1130" class="step-title">步骤9: 停止测试和清理 - onStop()</text>
  <text x="100" y="1145" class="step-text">作用: Activity停止时，发送停止消息并解绑Service，清理资源</text>
  <text x="100" y="1155" class="code-text">sendMessageToService(MSG_STOP_APPLIED_READING, MSG_STOP_TRACK, MSG_TURN_OFF_CAMERA); unbindService()</text>
  
  <!-- 关键技术点说明 -->
  <rect x="500" y="70" width="650" height="80" class="message-box" rx="8"/>
  <text x="825" y="90" text-anchor="middle" class="subtitle">关键技术点</text>
  <text x="520" y="110" class="step-text">• Messenger双向通信: 支持Activity ↔ Service双向消息传递</text>
  <text x="520" y="125" class="step-text">• 生命周期绑定: onStart()绑定, onStop()解绑, 确保资源安全</text>
  <text x="520" y="140" class="step-text">• 异步消息处理: 所有通信都是异步的，不阻塞UI线程</text>
</svg>
