<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .component { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .ui-box { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }
      .data-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .service-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .view-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .api-box { fill: #fce4ec; stroke: #c2185b; stroke-width: 2; }
      .native-box { fill: #f1f8e9; stroke: #689f38; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#redarrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">阅读运动评估系统架构图</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="60" width="300" height="180" class="ui-box" rx="5"/>
  <text x="200" y="80" text-anchor="middle" class="layer-title">用户界面层</text>
  
  <rect x="70" y="90" width="120" height="30" class="ui-box" rx="3"/>
  <text x="130" y="110" text-anchor="middle" class="component">ReadInitActivity</text>
  <text x="130" y="125" text-anchor="middle" class="component" font-size="10">阅读初始化页面</text>
  
  <rect x="210" y="90" width="120" height="30" class="ui-box" rx="3"/>
  <text x="270" y="110" text-anchor="middle" class="component">ReadActivity</text>
  <text x="270" y="125" text-anchor="middle" class="component" font-size="10">阅读测试页面</text>
  
  <rect x="70" y="140" width="120" height="30" class="ui-box" rx="3"/>
  <text x="130" y="160" text-anchor="middle" class="component">ReadResultAnalysisActivity</text>
  <text x="130" y="175" text-anchor="middle" class="component" font-size="10">结果分析页面</text>
  
  <rect x="210" y="140" width="120" height="30" class="ui-box" rx="3"/>
  <text x="270" y="160" text-anchor="middle" class="component">ReadTrackActivity</text>
  <text x="270" y="175" text-anchor="middle" class="component" font-size="10">轨迹追踪页面</text>
  
  <rect x="70" y="190" width="80" height="25" class="ui-box" rx="3"/>
  <text x="110" y="207" text-anchor="middle" class="component" font-size="10">基础信息Fragment</text>
  
  <rect x="160" y="190" width="80" height="25" class="ui-box" rx="3"/>
  <text x="200" y="207" text-anchor="middle" class="component" font-size="10">眼动校准Fragment</text>
  
  <rect x="250" y="190" width="80" height="25" class="ui-box" rx="3"/>
  <text x="290" y="207" text-anchor="middle" class="component" font-size="10">开始评估Fragment</text>
  
  <!-- 数据模型层 -->
  <rect x="400" y="60" width="250" height="180" class="data-box" rx="5"/>
  <text x="525" y="80" text-anchor="middle" class="layer-title">数据模型层</text>
  
  <rect x="420" y="90" width="100" height="40" class="data-box" rx="3"/>
  <text x="470" y="110" text-anchor="middle" class="component">ReadResult</text>
  <text x="470" y="125" text-anchor="middle" class="component" font-size="10">阅读结果数据</text>
  
  <rect x="530" y="90" width="100" height="40" class="data-box" rx="3"/>
  <text x="580" y="110" text-anchor="middle" class="component">GazeTrajectory</text>
  <text x="580" y="125" text-anchor="middle" class="component" font-size="10">视线轨迹数据</text>
  
  <rect x="420" y="140" width="100" height="40" class="data-box" rx="3"/>
  <text x="470" y="160" text-anchor="middle" class="component">GazePoint</text>
  <text x="470" y="175" text-anchor="middle" class="component" font-size="10">视线点数据</text>
  
  <rect x="530" y="140" width="100" height="40" class="data-box" rx="3"/>
  <text x="580" y="160" text-anchor="middle" class="component">ReadIdentity</text>
  <text x="580" y="175" text-anchor="middle" class="component" font-size="10">身份枚举</text>
  
  <rect x="475" y="190" width="100" height="30" class="data-box" rx="3"/>
  <text x="525" y="210" text-anchor="middle" class="component">ReadGrade</text>
  <text x="525" y="225" text-anchor="middle" class="component" font-size="10">年级枚举</text>
  
  <!-- 服务层 -->
  <rect x="700" y="60" width="250" height="120" class="service-box" rx="5"/>
  <text x="825" y="80" text-anchor="middle" class="layer-title">服务层</text>
  
  <rect x="720" y="90" width="100" height="30" class="service-box" rx="3"/>
  <text x="770" y="110" text-anchor="middle" class="component">GazeTrackService</text>
  <text x="770" y="125" text-anchor="middle" class="component" font-size="10">视线追踪服务</text>
  
  <rect x="830" y="90" width="100" height="30" class="service-box" rx="3"/>
  <text x="880" y="110" text-anchor="middle" class="component">AppliedManager</text>
  <text x="880" y="125" text-anchor="middle" class="component" font-size="10">应用管理器</text>
  
  <rect x="775" y="130" width="100" height="30" class="service-box" rx="3"/>
  <text x="825" y="150" text-anchor="middle" class="component">GazeApplied</text>
  <text x="825" y="165" text-anchor="middle" class="component" font-size="10">视线应用</text>
  
  <!-- 视图组件层 -->
  <rect x="50" y="280" width="300" height="120" class="view-box" rx="5"/>
  <text x="200" y="300" text-anchor="middle" class="layer-title">视图组件层</text>
  
  <rect x="70" y="320" width="100" height="30" class="view-box" rx="3"/>
  <text x="120" y="340" text-anchor="middle" class="component">ReadTrackView</text>
  <text x="120" y="355" text-anchor="middle" class="component" font-size="10">轨迹绘制视图</text>
  
  <rect x="180" y="320" width="80" height="30" class="view-box" rx="3"/>
  <text x="220" y="340" text-anchor="middle" class="component">ExoPlayer</text>
  <text x="220" y="355" text-anchor="middle" class="component" font-size="10">视频播放器</text>
  
  <rect x="270" y="320" width="70" height="30" class="view-box" rx="3"/>
  <text x="305" y="340" text-anchor="middle" class="component">DrawMode</text>
  <text x="305" y="355" text-anchor="middle" class="component" font-size="10">绘制模式</text>
  
  <rect x="100" y="360" width="150" height="25" class="view-box" rx="3"/>
  <text x="175" y="377" text-anchor="middle" class="component" font-size="10">静态/动态轨迹绘制</text>
  
  <!-- API层 -->
  <rect x="400" y="280" width="250" height="120" class="api-box" rx="5"/>
  <text x="525" y="300" text-anchor="middle" class="layer-title">API层</text>
  
  <rect x="420" y="320" width="100" height="30" class="api-box" rx="3"/>
  <text x="470" y="340" text-anchor="middle" class="component">ReadHomeApiService</text>
  <text x="470" y="355" text-anchor="middle" class="component" font-size="10">阅读家庭版API</text>
  
  <rect x="530" y="320" width="100" height="30" class="api-box" rx="3"/>
  <text x="580" y="340" text-anchor="middle" class="component">ReadHomeRepository</text>
  <text x="580" y="355" text-anchor="middle" class="component" font-size="10">数据仓库</text>
  
  <!-- Native层 -->
  <rect x="700" y="280" width="250" height="120" class="native-box" rx="5"/>
  <text x="825" y="300" text-anchor="middle" class="layer-title">Native层</text>
  
  <rect x="720" y="320" width="100" height="25" class="native-box" rx="3"/>
  <text x="770" y="337" text-anchor="middle" class="component" font-size="10">nativeCollectGaze</text>
  
  <rect x="830" y="320" width="100" height="25" class="native-box" rx="3"/>
  <text x="880" y="337" text-anchor="middle" class="component" font-size="10">nativeGetGazeTrajectory</text>
  
  <rect x="775" y="350" width="100" height="25" class="native-box" rx="3"/>
  <text x="825" y="367" text-anchor="middle" class="component" font-size="10">nativeStartAppliedReading</text>
  
  <!-- 流程箭头 -->
  <line x1="190" y1="120" x2="210" y2="120" class="flow-arrow"/>
  <line x1="270" y1="140" x2="270" y2="155" class="flow-arrow"/>
  <line x1="190" y1="155" x2="210" y2="155" class="flow-arrow"/>
  
  <!-- 数据关系箭头 -->
  <line x1="520" y1="110" x2="530" y2="110" class="arrow"/>
  <line x1="580" y1="130" x2="520" y2="150" class="arrow"/>
  
  <!-- 服务调用箭头 -->
  <line x1="330" y1="105" x2="720" y2="105" class="arrow"/>
  <line x1="820" y1="105" x2="830" y2="105" class="arrow"/>
  <line x1="825" y1="130" x2="825" y2="320" class="arrow"/>
  
  <!-- 视图关系箭头 -->
  <line x1="270" y1="170" x2="200" y2="280" class="arrow"/>
  
  <!-- API调用箭头 -->
  <line x1="520" y1="335" x2="530" y2="335" class="arrow"/>
  
  <!-- 流程说明 -->
  <rect x="50" y="450" width="900" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="5"/>
  <text x="500" y="475" text-anchor="middle" class="layer-title">阅读运动评估实现流程</text>
  
  <text x="70" y="500" class="component" font-weight="bold">1. 初始化阶段:</text>
  <text x="90" y="520" class="component">• 用户在ReadInitActivity设置身份和年级信息</text>
  <text x="90" y="535" class="component">• 进行眼动校准确保追踪准确性</text>
  <text x="90" y="550" class="component">• 准备开始阅读测试</text>
  
  <text x="70" y="580" class="component" font-weight="bold">2. 测试阶段:</text>
  <text x="90" y="600" class="component">• ReadActivity显示3秒倒计时</text>
  <text x="90" y="615" class="component">• 发送MSG_TURN_ON_CAMERA、MSG_START_TRACK、MSG_START_APPLIED_READING消息</text>
  <text x="90" y="630" class="component">• Native层通过nativeCollectGaze()实时收集视线数据</text>
  <text x="90" y="645" class="component">• 用户阅读固定文本内容(621字)</text>
  
  <text x="500" y="500" class="component" font-weight="bold">3. 结束阶段:</text>
  <text x="520" y="520" class="component">• 用户点击"完成阅读"按钮</text>
  <text x="520" y="535" class="component">• 发送MSG_GET_GAZE_TRAJECTORY获取完整轨迹</text>
  <text x="520" y="550" class="component">• Native层返回JSON格式的GazeTrajectory数据</text>
  <text x="520" y="565" class="component">• 停止追踪和摄像头</text>
  
  <text x="500" y="590" class="component" font-weight="bold">4. 分析阶段:</text>
  <text x="520" y="610" class="component">• ReadResultAnalysisActivity计算阅读速度(字/分钟)</text>
  <text x="520" y="625" class="component">• 根据身份和年级判断是否达标</text>
  <text x="520" y="640" class="component">• 显示阅读字数、时长、速度等统计信息</text>
  
  <text x="70" y="670" class="component" font-weight="bold">5. 轨迹查看:</text>
  <text x="90" y="690" class="component">• ReadTrackActivity提供三种查看模式:</text>
  <text x="110" y="705" class="component">- 静态轨迹图: ReadTrackView一次性绘制所有视线点和路径</text>
  <text x="110" y="720" class="component">- 动态轨迹图: 逐点动画绘制，每100ms显示一个点</text>
  <text x="110" y="735" class="component">- 示例视频: ExoPlayer播放标准阅读轨迹视频</text>
</svg>
