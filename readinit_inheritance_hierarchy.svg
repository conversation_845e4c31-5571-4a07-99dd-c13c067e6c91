<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .class-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .method-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .inheritance-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#inheritanceArrow); }
      .composition-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#compositionArrow); }
      
      .readinit-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .gtbase-box { fill: #fff3e0; stroke: #f39c12; stroke-width: 2; }
      .basecommon-box { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      
      .feature-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
    </style>
    
    <marker id="inheritanceArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
    
    <marker id="compositionArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">ReadInitActivity 继承层次结构图</text>
  
  <!-- ReadInitActivity -->
  <rect x="50" y="70" width="300" height="200" class="readinit-box" rx="8"/>
  <text x="200" y="95" text-anchor="middle" class="class-title">ReadInitActivity</text>
  <text x="70" y="115" class="description-text">阅读初始化Activity - 具体业务实现</text>
  
  <text x="70" y="140" class="method-text" font-weight="bold">主要功能:</text>
  <text x="70" y="155" class="method-text">• 管理三个Tab页面切换</text>
  <text x="70" y="170" class="method-text">• goToBasicInfoTab() - 基础信息页</text>
  <text x="70" y="185" class="method-text">• goToCalibrationTab() - 眼动校准页</text>
  <text x="70" y="200" class="method-text">• goToStartEvaluate() - 开始评估页</text>
  <text x="70" y="215" class="method-text">• goToRead() - 跳转阅读测试</text>
  
  <text x="70" y="240" class="method-text" font-weight="bold">业务逻辑:</text>
  <text x="70" y="255" class="method-text">• Fragment管理和切换</text>
  
  <!-- GTBaseActivity -->
  <rect x="450" y="70" width="350" height="200" class="gtbase-box" rx="8"/>
  <text x="625" y="95" text-anchor="middle" class="class-title">GTBaseActivity</text>
  <text x="470" y="115" class="description-text">项目基础Activity - 提供MQTT和全屏功能</text>
  
  <text x="470" y="140" class="method-text" font-weight="bold">重写方法:</text>
  <text x="470" y="155" class="method-text">• enableFullScreen(): Boolean = true</text>
  <text x="470" y="170" class="description-text">  强制启用全屏模式</text>
  
  <text x="470" y="195" class="method-text" font-weight="bold">新增功能:</text>
  <text x="470" y="210" class="method-text">• initMQTT() - 初始化MQTT连接</text>
  <text x="470" y="225" class="description-text">  用于设备间通信和数据传输</text>
  
  <text x="470" y="250" class="method-text" font-weight="bold">技术特性:</text>
  <text x="470" y="265" class="method-text">• 协程处理异步MQTT初始化</text>
  
  <!-- BaseCommonActivity -->
  <rect x="900" y="70" width="400" height="200" class="basecommon-box" rx="8"/>
  <text x="1100" y="95" text-anchor="middle" class="class-title">BaseCommonActivity</text>
  <text x="920" y="115" class="description-text">通用基础Activity - 提供通用功能和生命周期管理</text>
  
  <text x="920" y="140" class="method-text" font-weight="bold">核心功能:</text>
  <text x="920" y="155" class="method-text">• enableFullScreen(): Boolean - 全屏控制</text>
  <text x="920" y="170" class="method-text">• enableBack(): Boolean - 返回键控制</text>
  <text x="920" y="185" class="method-text">• sendMessageToService() - 服务通信</text>
  <text x="920" y="200" class="method-text">• countdown() - 倒计时功能</text>
  
  <text x="920" y="225" class="method-text" font-weight="bold">扩展功能:</text>
  <text x="920" y="240" class="method-text">• 生命周期管理</text>
  <text x="920" y="255" class="method-text">• 通用UI操作扩展</text>
  
  <!-- Android Activity -->
  <rect x="1100" y="320" width="300" height="120" class="activity-box" rx="8"/>
  <text x="1250" y="345" text-anchor="middle" class="class-title">Android Activity</text>
  <text x="1120" y="365" class="description-text">Android框架基础Activity类</text>
  
  <text x="1120" y="385" class="method-text" font-weight="bold">系统功能:</text>
  <text x="1120" y="400" class="method-text">• onCreate(), onStart(), onResume()...</text>
  <text x="1120" y="415" class="method-text">• 生命周期管理</text>
  <text x="1120" y="430" class="method-text">• Intent处理, UI管理等</text>
  
  <!-- 继承箭头 -->
  <line x1="350" y1="170" x2="450" y2="170" class="inheritance-arrow"/>
  <line x1="800" y1="170" x2="900" y2="170" class="inheritance-arrow"/>
  <line x1="1100" y1="270" x2="1250" y2="320" class="inheritance-arrow"/>
  
  <!-- 功能详解区域 -->
  <rect x="50" y="320" width="1000" height="520" class="feature-box" rx="5"/>
  <text x="550" y="345" text-anchor="middle" class="class-title">各层功能详细说明</text>
  
  <!-- ReadInitActivity详解 -->
  <rect x="70" y="360" width="460" height="180" class="readinit-box" rx="5"/>
  <text x="300" y="380" text-anchor="middle" class="method-text" font-weight="bold">ReadInitActivity 具体实现</text>
  
  <text x="90" y="400" class="method-text" font-weight="bold">1. Tab管理功能:</text>
  <text x="110" y="415" class="method-text">• 三个Fragment的动态切换</text>
  <text x="110" y="430" class="method-text">• ReadInitBasicInfoFragment - 设置身份和年级</text>
  <text x="110" y="445" class="method-text">• ReadInitCalibrationFragment - 眼动校准</text>
  <text x="110" y="460" class="method-text">• ReadInitStartEvaluateFragment - 开始评估</text>
  
  <text x="90" y="485" class="method-text" font-weight="bold">2. 页面跳转:</text>
  <text x="110" y="500" class="method-text">• goToRead() - 创建ReadActivity Intent</text>
  <text x="110" y="515" class="method-text">• 传递身份(ReadIdentity)和年级(ReadGrade)参数</text>
  <text x="110" y="530" class="method-text">• finish()当前Activity</text>
  
  <!-- GTBaseActivity详解 -->
  <rect x="550" y="360" width="480" height="180" class="gtbase-box" rx="5"/>
  <text x="790" y="380" text-anchor="middle" class="method-text" font-weight="bold">GTBaseActivity 项目基础层</text>
  
  <text x="570" y="400" class="method-text" font-weight="bold">1. 全屏模式:</text>
  <text x="590" y="415" class="method-text">• override enableFullScreen() = true</text>
  <text x="590" y="430" class="method-text">• 强制所有继承的Activity使用全屏</text>
  
  <text x="570" y="450" class="method-text" font-weight="bold">2. MQTT通信:</text>
  <text x="590" y="465" class="method-text">• initMQTT() - 阿里云IoT Hub连接</text>
  <text x="590" y="480" class="method-text">• 设备间数据传输和通信</text>
  <text x="590" y="495" class="method-text">• 协程异步处理连接逻辑</text>
  
  <text x="570" y="515" class="method-text" font-weight="bold">3. 技术特性:</text>
  <text x="590" y="530" class="method-text">• 使用Kotlin协程处理异步操作</text>
  
  <!-- BaseCommonActivity详解 -->
  <rect x="70" y="560" width="960" height="260" class="basecommon-box" rx="5"/>
  <text x="550" y="580" text-anchor="middle" class="method-text" font-weight="bold">BaseCommonActivity 通用基础层 (外部库)</text>
  
  <text x="90" y="605" class="method-text" font-weight="bold">1. 核心抽象方法:</text>
  <text x="110" y="620" class="method-text">• enableFullScreen(): Boolean - 控制是否全屏显示</text>
  <text x="110" y="635" class="method-text">• enableBack(): Boolean - 控制返回键行为</text>
  
  <text x="90" y="655" class="method-text" font-weight="bold">2. 服务通信功能:</text>
  <text x="110" y="670" class="method-text">• sendMessageToService(vararg messages: Message) - 向Service发送消息</text>
  <text x="110" y="685" class="method-text">• 支持批量消息发送，用于与GazeTrackService通信</text>
  <text x="110" y="700" class="method-text">• 消息类型: MSG_TURN_ON_CAMERA, MSG_START_TRACK等</text>
  
  <text x="90" y="720" class="method-text" font-weight="bold">3. 倒计时功能:</text>
  <text x="110" y="735" class="method-text">• countdown(duration, interval, onTick, onCompletion, onCatch)</text>
  <text x="110" y="750" class="method-text">• 提供倒计时回调处理，广泛用于测试准备阶段</text>
  
  <text x="90" y="770" class="method-text" font-weight="bold">4. 生命周期管理:</text>
  <text x="110" y="785" class="method-text">• 统一的onCreate, onStart, onStop处理</text>
  <text x="110" y="800" class="method-text">• Service绑定和解绑管理</text>
  <text x="110" y="815" class="method-text">• 资源释放和清理</text>
  
  <!-- 继承关系说明 -->
  <text x="1100" y="600" class="method-text" font-weight="bold">继承关系说明:</text>
  <text x="1100" y="620" class="method-text">ReadInitActivity</text>
  <text x="1120" y="635" class="method-text">↓ 继承</text>
  <text x="1100" y="650" class="method-text">GTBaseActivity</text>
  <text x="1120" y="665" class="method-text">↓ 继承</text>
  <text x="1100" y="680" class="method-text">BaseCommonActivity</text>
  <text x="1120" y="695" class="method-text">↓ 继承</text>
  <text x="1100" y="710" class="method-text">Android Activity</text>
  
  <text x="1100" y="740" class="method-text" font-weight="bold">功能层次:</text>
  <text x="1100" y="755" class="method-text">• 业务逻辑层</text>
  <text x="1100" y="770" class="method-text">• 项目基础层</text>
  <text x="1100" y="785" class="method-text">• 通用功能层</text>
  <text x="1100" y="800" class="method-text">• 系统框架层</text>
</svg>
