<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; fill: #666; font-weight: bold; }
      .label { font-family: Arial, sans-serif; font-size: 14px; fill: #333; font-weight: bold; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #555; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #333; background: #f8f9fa; }
      .formula { font-family: 'Times New Roman', serif; font-size: 13px; fill: #2c3e50; font-style: italic; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .thick-arrow { stroke: #333; stroke-width: 3; fill: none; marker-end: url(#thick-arrowhead); }
      .dashed { stroke-dasharray: 8,4; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="thick-arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#333" />
    </marker>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">实时头像调整机制详解</text>
  <text x="800" y="60" text-anchor="middle" class="subtitle">根据检测结果调整颜色、大小、位置、角度 + 文字语音提示</text>
  
  <!-- 检测数据输入 -->
  <rect x="50" y="90" width="350" height="200" fill="#e8f4fd" stroke="#1976d2" stroke-width="2" rx="10"/>
  <text x="225" y="115" text-anchor="middle" class="subtitle">1. 检测数据输入</text>
  
  <rect x="70" y="130" width="310" height="140" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="80" y="150" class="label">PostureCorrectionResult 数据结构:</text>
  <text x="80" y="170" class="code">• leftX, leftY: Double    // 左眼坐标</text>
  <text x="80" y="185" class="code">• rightX, rightY: Double  // 右眼坐标</text>
  <text x="80" y="200" class="code">• dist: Double            // 距离参数</text>
  <text x="80" y="215" class="code">• aligned: Boolean        // 对齐状态</text>
  <text x="80" y="230" class="code">• hasEyes: Boolean        // 是否检测到眼睛</text>
  <text x="80" y="245" class="code">• timestamp: Long         // 时间戳</text>
  <text x="80" y="260" class="formula">调用频率: 30-60 FPS (每16-33ms更新一次)</text>
  
  <!-- 计算处理模块 -->
  <rect x="450" y="90" width="500" height="200" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
  <text x="700" y="115" text-anchor="middle" class="subtitle">2. 多维度参数计算</text>
  
  <!-- 距离计算 -->
  <rect x="470" y="130" width="140" height="75" fill="#fff" stroke="#ff9800" rx="5"/>
  <text x="540" y="150" text-anchor="middle" class="label">距离计算</text>
  <text x="480" y="170" class="formula">scale = 0.5 / dist</text>
  <text x="480" y="185" class="text">正常范围: 0.45-0.55</text>
  <text x="480" y="200" class="text">scale ∈ [0.9, 1.1]</text>
  
  <!-- 中心点计算 -->
  <rect x="620" y="130" width="150" height="75" fill="#fff" stroke="#ff9800" rx="5"/>
  <text x="695" y="150" text-anchor="middle" class="label">中心点计算</text>
  <text x="630" y="170" class="formula">centerX = (leftX + rightX) / 2</text>
  <text x="630" y="185" class="formula">centerY = (leftY + rightY) / 2</text>
  <text x="630" y="200" class="text">屏幕坐标系</text>
  
  <!-- 角度计算 -->
  <rect x="780" y="130" width="160" height="75" fill="#fff" stroke="#ff9800" rx="5"/>
  <text x="860" y="150" text-anchor="middle" class="label">倾斜角度计算</text>
  <text x="790" y="170" class="formula">angle = atan2(rightY - leftY,</text>
  <text x="790" y="185" class="formula">              rightX - leftX)</text>
  <text x="790" y="200" class="text">弧度转角度: × 180/π</text>
  
  <!-- 异常检测 -->
  <rect x="470" y="215" width="470" height="65" fill="#ffebee" stroke="#d32f2f" rx="5"/>
  <text x="705" y="235" text-anchor="middle" class="label">异常等级检测 (createPostureException)</text>
  <text x="480" y="255" class="text">degree = 0 (正常) | degree = 1 (轻微异常) | degree = 2 (严重异常)</text>
  <text x="480" y="270" class="text">阈值: 距离±0.05, 位置±192/384px, 角度±10°/15°</text>
  
  <!-- 四维调整机制 -->
  <rect x="50" y="320" width="1500" height="400" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="3" rx="15"/>
  <text x="800" y="350" text-anchor="middle" class="subtitle">3. 四维实时调整机制</text>
  
  <!-- 颜色调整 -->
  <rect x="80" y="370" width="340" height="160" fill="#fff" stroke="#4caf50" stroke-width="2" rx="8"/>
  <text x="250" y="395" text-anchor="middle" class="label">A. 颜色调整 (setImageResource)</text>
  
  <!-- 正常状态 -->
  <circle cx="130" cy="430" r="20" fill="#4CAF50" filter="url(#glow)"/>
  <circle cx="120" cy="420" r="3" fill="#fff"/>
  <circle cx="140" cy="420" r="3" fill="#fff"/>
  <path d="M 120 440 Q 130 450 140 440" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="130" y="465" text-anchor="middle" class="text">degree = 0</text>
  <text x="130" y="480" text-anchor="middle" class="code">R.drawable.head_green</text>
  
  <!-- 警告状态 -->
  <circle cx="250" cy="430" r="20" fill="#FF9800" filter="url(#glow)"/>
  <circle cx="240" cy="420" r="3" fill="#fff"/>
  <circle cx="260" cy="420" r="3" fill="#fff"/>
  <path d="M 240 440 Q 250 445 260 440" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="250" y="465" text-anchor="middle" class="text">degree = 1</text>
  <text x="250" y="480" text-anchor="middle" class="code">R.drawable.head_yellow</text>
  
  <!-- 错误状态 -->
  <circle cx="370" cy="430" r="20" fill="#F44336" filter="url(#glow)"/>
  <circle cx="360" cy="420" r="3" fill="#fff"/>
  <circle cx="380" cy="420" r="3" fill="#fff"/>
  <path d="M 360 440 Q 370 435 380 440" stroke="#fff" stroke-width="2" fill="none"/>
  <text x="370" y="465" text-anchor="middle" class="text">degree = 2</text>
  <text x="370" y="480" text-anchor="middle" class="code">R.drawable.head_red</text>
  
  <rect x="90" y="490" width="320" height="30" fill="#e8f5e8" stroke="#4caf50" rx="3"/>
  <text x="250" y="510" text-anchor="middle" class="code">ivRealLocation.setImageResource(colorResource)</text>
  
  <!-- 大小调整 -->
  <rect x="450" y="370" width="340" height="160" fill="#fff" stroke="#2196f3" stroke-width="2" rx="8"/>
  <text x="620" y="395" text-anchor="middle" class="label">B. 大小调整 (LayoutParams)</text>
  
  <!-- 不同大小示例 -->
  <circle cx="500" cy="430" r="12" fill="#FFB74D"/>
  <text x="500" y="455" text-anchor="middle" class="text">太远</text>
  <text x="500" y="470" text-anchor="middle" class="formula">scale = 1.2</text>
  
  <circle cx="620" cy="430" r="20" fill="#FFB74D"/>
  <text x="620" y="460" text-anchor="middle" class="text">正常</text>
  <text x="620" y="475" text-anchor="middle" class="formula">scale = 1.0</text>
  
  <circle cx="740" cy="430" r="28" fill="#FFB74D"/>
  <text x="740" y="470" text-anchor="middle" class="text">太近</text>
  <text x="740" y="485" text-anchor="middle" class="formula">scale = 0.8</text>
  
  <rect x="460" y="490" width="320" height="30" fill="#e3f2fd" stroke="#2196f3" rx="3"/>
  <text x="620" y="510" text-anchor="middle" class="code">layoutParams.width/height = (size * scale).toInt()</text>
  
  <!-- 位置调整 -->
  <rect x="820" y="370" width="340" height="160" fill="#fff" stroke="#9c27b0" stroke-width="2" rx="8"/>
  <text x="990" y="395" text-anchor="middle" class="label">C. 位置调整 (x, y坐标)</text>
  
  <!-- 位置示例 -->
  <rect x="850" y="410" width="280" height="60" fill="#f5f5f5" stroke="#ddd" rx="3"/>
  <!-- 参考位置 -->
  <circle cx="990" cy="440" r="15" fill="#E0E0E0" stroke="#999" stroke-width="2" stroke-dasharray="3,3"/>
  <text x="990" y="448" text-anchor="middle" class="text">参考</text>
  
  <!-- 实际位置 -->
  <circle cx="920" cy="425" r="12" fill="#9C27B0"/>
  <line x1="932" y1="425" x2="975" y2="435" class="arrow"/>
  <text x="950" y="415" text-anchor="middle" class="text">实时跟踪</text>
  
  <text x="990" y="485" text-anchor="middle" class="formula">x = centerX - width/2</text>
  <text x="990" y="500" text-anchor="middle" class="formula">y = centerY - height/2</text>
  
  <rect x="830" y="490" width="320" height="30" fill="#f3e5f5" stroke="#9c27b0" rx="3"/>
  <text x="990" y="510" text-anchor="middle" class="code">ivRealLocation.x = centerX; ivRealLocation.y = centerY</text>
  
  <!-- 角度调整 -->
  <rect x="1190" y="370" width="340" height="160" fill="#fff" stroke="#ff5722" stroke-width="2" rx="8"/>
  <text x="1360" y="395" text-anchor="middle" class="label">D. 角度调整 (rotation)</text>
  
  <!-- 角度示例 -->
  <ellipse cx="1280" cy="430" rx="18" ry="22" fill="#FFB74D"/>
  <text x="1280" y="460" text-anchor="middle" class="text">正常 0°</text>
  
  <ellipse cx="1400" cy="430" rx="18" ry="22" fill="#FFB74D" transform="rotate(20 1400 430)"/>
  <text x="1400" y="460" text-anchor="middle" class="text">倾斜 20°</text>
  
  <text x="1360" y="485" text-anchor="middle" class="formula">rotation = angle * 180 / π</text>
  
  <rect x="1200" y="490" width="320" height="30" fill="#ffebee" stroke="#ff5722" rx="3"/>
  <text x="1360" y="510" text-anchor="middle" class="code">ivRealLocation.rotation = rotationAngle.toFloat()</text>
  
  <!-- 提示系统 -->
  <rect x="50" y="750" width="750" height="400" fill="#fff8e1" stroke="#ff8f00" stroke-width="3" rx="15"/>
  <text x="425" y="780" text-anchor="middle" class="subtitle">4. 文字 + 语音提示系统</text>
  
  <!-- 文字提示 -->
  <rect x="80" y="800" width="340" height="180" fill="#fff" stroke="#ff9800" stroke-width="2" rx="8"/>
  <text x="250" y="825" text-anchor="middle" class="label">文字提示 (TextView)</text>
  
  <rect x="90" y="840" width="320" height="130" fill="#fffde7" stroke="#fbc02d" rx="5"/>
  <text x="100" y="860" class="text">异常类型 → 提示文字:</text>
  <text x="100" y="880" class="code">FAR → "请靠近一点"</text>
  <text x="100" y="895" class="code">NEARLY → "请远离一点"</text>
  <text x="100" y="910" class="code">LEFT → "请向右转"</text>
  <text x="100" y="925" class="code">RIGHT → "请向左转"</text>
  <text x="100" y="940" class="code">UP → "请向下移动"</text>
  <text x="100" y="955" class="code">DOWN → "请向上移动"</text>
  <text x="100" y="970" class="code">SKEW → "请不要倾斜头部"</text>
  
  <!-- 语音提示 -->
  <rect x="450" y="800" width="340" height="180" fill="#fff" stroke="#4caf50" stroke-width="2" rx="8"/>
  <text x="620" y="825" text-anchor="middle" class="label">语音提示 (PlayManager)</text>
  
  <rect x="460" y="840" width="320" height="130" fill="#e8f5e8" stroke="#4caf50" rx="5"/>
  <text x="470" y="860" class="text">🔊 语音文件播放:</text>
  <text x="470" y="880" class="code">calibration_return_correct_position.wav</text>
  <text x="470" y="900" class="text">播放条件:</text>
  <text x="470" y="920" class="code">if (degree > 0) {</text>
  <text x="470" y="935" class="code">  PlayManager.playRawMedia(</text>
  <text x="470" y="950" class="code">    RawMedia.CALIBRATION_RETURN_CORRECT)</text>
  <text x="470" y="965" class="code">}</text>
  
  <!-- 实现代码 -->
  <rect x="80" y="1000" width="710" height="130" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="8"/>
  <text x="435" y="1025" text-anchor="middle" class="label">核心实现代码 (setPostureCorrectionResult)</text>
  
  <rect x="90" y="1040" width="690" height="80" fill="#fff" stroke="#ddd" rx="3"/>
  <text x="100" y="1060" class="code">val exception = createPostureException(result)  // 检测异常</text>
  <text x="100" y="1075" class="code">val (colorResource, promptResource) = getResourcesByException(exception)</text>
  <text x="100" y="1090" class="code">updateAvatarAppearance(scale, centerX, centerY, rotationAngle, colorResource)</text>
  <text x="100" y="1105" class="code">updatePromptText(promptResource)  // 更新提示文字</text>
  
  <!-- 技术细节 -->
  <rect x="850" y="750" width="700" height="400" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="3" rx="15"/>
  <text x="1200" y="780" text-anchor="middle" class="subtitle">5. 技术实现细节</text>
  
  <!-- 性能优化 -->
  <rect x="880" y="800" width="320" height="120" fill="#fff" stroke="#9c27b0" stroke-width="2" rx="8"/>
  <text x="1040" y="825" text-anchor="middle" class="label">性能优化</text>
  <text x="890" y="845" class="text">• 避免频繁对象创建</text>
  <text x="890" y="860" class="text">• 缓存资源ID</text>
  <text x="890" y="875" class="text">• 异步处理音频播放</text>
  <text x="890" y="890" class="text">• 限制更新频率 (16ms间隔)</text>
  <text x="890" y="905" class="text">• 使用硬件加速渲染</text>
  
  <!-- 坐标系统 -->
  <rect x="1220" y="800" width="320" height="120" fill="#fff" stroke="#ff5722" stroke-width="2" rx="8"/>
  <text x="1380" y="825" text-anchor="middle" class="label">坐标系统</text>
  <text x="1230" y="845" class="text">• 屏幕坐标系: 左上角(0,0)</text>
  <text x="1230" y="860" class="text">• 眼动坐标: 相对屏幕比例</text>
  <text x="1230" y="875" class="text">• 头像锚点: 中心对齐</text>
  <text x="1230" y="890" class="text">• 角度: 弧度制转角度制</text>
  <text x="1230" y="905" class="text">• Z轴: rotation属性控制</text>
  
  <!-- 异常处理 -->
  <rect x="880" y="940" width="320" height="120" fill="#fff" stroke="#f44336" stroke-width="2" rx="8"/>
  <text x="1040" y="965" text-anchor="middle" class="label">异常处理</text>
  <text x="890" y="985" class="text">• 无人脸检测: 显示红色提示</text>
  <text x="890" y="1000" class="text">• 数据异常: 使用上一帧数据</text>
  <text x="890" y="1015" class="text">• 内存不足: 降低更新频率</text>
  <text x="890" y="1030" class="text">• 音频冲突: 队列管理</text>
  <text x="890" y="1045" class="text">• 线程安全: UI线程更新</text>
  
  <!-- 状态管理 -->
  <rect x="1220" y="940" width="320" height="120" fill="#fff" stroke="#607d8b" stroke-width="2" rx="8"/>
  <text x="1380" y="965" text-anchor="middle" class="label">状态管理</text>
  <text x="1230" y="985" class="text">• 校准状态: IDLE/RUNNING/PAUSED</text>
  <text x="1230" y="1000" class="text">• 头像状态: 颜色/大小/位置/角度</text>
  <text x="1230" y="1015" class="text">• 提示状态: 文字/语音播放状态</text>
  <text x="1230" y="1030" class="text">• 检测状态: 正常/异常等级</text>
  <text x="1230" y="1045" class="text">• 动画状态: 过渡动画控制</text>
  
  <!-- 数据流箭头 -->
  <line x1="400" y1="190" x2="450" y2="190" class="thick-arrow"/>
  <line x1="700" y1="290" x2="800" y2="350" class="thick-arrow"/>
  
  <!-- 反馈循环箭头 -->
  <path d="M 1500 600 Q 1550 400 1550 200 Q 1550 100 400 100" stroke="#e91e63" stroke-width="3" fill="none" stroke-dasharray="10,5" marker-end="url(#thick-arrowhead)"/>
  <text x="1450" y="350" class="label" fill="#e91e63">实时反馈循环</text>
  
  <!-- 频率标注 -->
  <rect x="1400" y="50" width="180" height="60" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
  <text x="1490" y="70" text-anchor="middle" class="label">实时性指标</text>
  <text x="1490" y="90" text-anchor="middle" class="text">更新频率: 30-60 FPS</text>
  <text x="1490" y="105" text-anchor="middle" class="text">响应延迟: &lt; 50ms</text>

  <!-- 算法流程图 -->
  <rect x="880" y="1080" width="660" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="8"/>
  <text x="1210" y="1105" text-anchor="middle" class="label">算法执行流程</text>
  <text x="890" y="1125" class="code">1. 接收PostureCorrectionResult → 2. 计算scale/center/angle</text>
  <text x="890" y="1140" class="code">3. 检测异常等级 → 4. 选择资源ID → 5. 更新UI属性</text>
  <text x="890" y="1155" class="code">6. 播放语音提示 → 7. 等待下一帧数据 → 循环执行</text>
  <text x="890" y="1170" class="formula">总耗时: &lt; 16ms (保证60FPS流畅度)</text>
</svg>
