<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="javaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="cppGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="configGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1400" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">参考点传递给Native层的完整流程</text>
  
  <!-- 核心发现 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🔍 重要发现：参考点实际上是在C++层硬编码的，并非从Java层传递！</text>
  <text x="70" y="110" class="text">• Java层的DEFAULT_REFERENCE_X/Y只用于UI层计算偏移方向</text>
  <text x="70" y="125" class="text">• C++层使用硬编码的setting_pose_left_eye_x/y作为参考点</text>
  
  <!-- 误解澄清 -->
  <rect x="50" y="160" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">常见误解澄清</text>
  
  <rect x="80" y="210" width="500" height="120" rx="8" fill="#ffebee"/>
  <text x="330" y="235" text-anchor="middle" class="subtitle">❌ 错误理解</text>
  <text x="100" y="260" class="text">认为Java层的DEFAULT_REFERENCE_X/Y</text>
  <text x="100" y="280" class="text">会通过JNI传递给C++层作为参考点</text>
  <text x="100" y="300" class="text">用于计算left_distance和right_distance</text>
  
  <rect x="620" y="210" width="500" height="120" rx="8" fill="#e8f5e8"/>
  <text x="870" y="235" text-anchor="middle" class="subtitle">✅ 实际情况</text>
  <text x="640" y="260" class="text">C++层使用硬编码的参考点坐标</text>
  <text x="640" y="280" class="text">Java层的参考点仅用于UI层计算</text>
  <text x="640" y="300" class="text">两者是独立的坐标系统</text>
  
  <!-- 实际的参考点来源 -->
  <rect x="50" y="380" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="405" text-anchor="middle" class="subtitle">实际的参考点来源分析</text>
  
  <!-- C++层硬编码参考点 -->
  <rect x="80" y="430" width="800" height="220" rx="8" fill="url(#cppGradient)"/>
  <text x="480" y="455" text-anchor="middle" class="subtitle">C++层硬编码参考点</text>
  <text x="480" y="475" text-anchor="middle" class="highlight">poseAlign.cpp 构造函数</text>
  
  <text x="100" y="500" class="code">PoseAlign::PoseAlign() {</text>
  <text x="120" y="520" class="code">setting_refer_points = pose_pupil_points(</text>
  <text x="140" y="540" class="code">cv::Point2f(setting_pose_left_eye_x, setting_pose_left_eye_y),</text>
  <text x="140" y="560" class="code">cv::Point2f(setting_pose_right_eye_x, setting_pose_right_eye_y)</text>
  <text x="120" y="580" class="code">);</text>
  <text x="100" y="600" class="code">}</text>
  
  <text x="100" y="625" class="text">• <tspan class="highlight">setting_pose_left_eye_x/y:</tspan> 左眼参考点坐标</text>
  <text x="100" y="645" class="text">• <tspan class="highlight">setting_pose_right_eye_x/y:</tspan> 右眼参考点坐标</text>
  
  <!-- Java层UI参考点 -->
  <rect x="920" y="430" width="800" height="220" rx="8" fill="url(#javaGradient)"/>
  <text x="1320" y="455" text-anchor="middle" class="subtitle">Java层UI参考点</text>
  <text x="1320" y="475" text-anchor="middle" class="highlight">PostureCalibrationView</text>
  
  <text x="940" y="500" class="code">companion object {</text>
  <text x="960" y="520" class="code">const val DEFAULT_REFERENCE_X = 0.5f</text>
  <text x="960" y="540" class="code">const val DEFAULT_REFERENCE_Y = 0.5f</text>
  <text x="940" y="560" class="code">}</text>
  
  <text x="940" y="585" class="code">fun setPostureCorrectionParam(referenceX: Float, referenceY: Float) {</text>
  <text x="960" y="605" class="code">this.referenceX = referenceX  // 仅用于UI计算</text>
  <text x="960" y="625" class="code">this.referenceY = referenceY  // 仅用于UI计算</text>
  <text x="940" y="645" class="code">}</text>
  
  <!-- 两个坐标系统的用途 -->
  <rect x="50" y="700" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="725" text-anchor="middle" class="subtitle">两个独立坐标系统的用途</text>
  
  <!-- C++层用途 -->
  <rect x="80" y="750" width="800" height="180" rx="8" fill="#fff3e0"/>
  <text x="480" y="775" text-anchor="middle" class="subtitle">C++层参考点用途</text>
  
  <text x="100" y="800" class="highlight">1. 姿势有效性判断</text>
  <text x="120" y="820" class="code">float left_distance = sqrt((eye_left_x - setting_refer_points.left_pupil.x)² +</text>
  <text x="140" y="840" class="code">(eye_left_y - setting_refer_points.left_pupil.y)²);</text>
  <text x="120" y="860" class="text">• 计算当前眼部位置与C++参考点的距离</text>
  <text x="120" y="880" class="text">• 判断是否在pose_align_eye_radius(200像素)范围内</text>
  
  <text x="100" y="905" class="highlight">2. 返回aligned状态</text>
  <text x="120" y="925" class="text">• 基于C++参考点判断姿势是否正确</text>
  
  <!-- Java层用途 -->
  <rect x="920" y="750" width="800" height="180" rx="8" fill="#e3f2fd"/>
  <text x="1320" y="775" text-anchor="middle" class="subtitle">Java层参考点用途</text>
  
  <text x="940" y="800" class="highlight">1. UI偏移方向计算</text>
  <text x="960" y="820" class="code">val horizontalDistance = centerX - referenceX * screenWidth</text>
  <text x="960" y="840" class="code">val verticalDistance = centerY - referenceY * screenHeight</text>
  <text x="960" y="860" class="text">• 计算用户需要向哪个方向调整</text>
  <text x="960" y="880" class="text">• 生成"向左/右/上/下"的文字和语音提示</text>
  
  <text x="940" y="905" class="highlight">2. 头像位置显示</text>
  <text x="960" y="925" class="text">• 在UI上显示用户当前位置相对于理想位置的偏移</text>
  
  <!-- 配置文件中的参考点 -->
  <rect x="50" y="970" width="1700" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="995" text-anchor="middle" class="subtitle">C++参考点的配置来源</text>
  
  <rect x="80" y="1020" width="1600" height="130" rx="8" fill="url(#configGradient)"/>
  <text x="880" y="1045" text-anchor="middle" class="subtitle">utils.h 中的硬编码配置</text>
  
  <text x="100" y="1070" class="code">// 姿势校准参考点配置 (像素坐标)</text>
  <text x="100" y="1090" class="code">#define setting_pose_left_eye_x  ???    // 左眼参考点X坐标</text>
  <text x="100" y="1110" class="code">#define setting_pose_left_eye_y  ???    // 左眼参考点Y坐标</text>
  <text x="100" y="1130" class="code">#define setting_pose_right_eye_x ???    // 右眼参考点X坐标</text>
  <text x="100" y="1150" class="code">#define setting_pose_right_eye_y ???    // 右眼参考点Y坐标</text>
  
  <text x="900" y="1070" class="text">• 这些值在编译时确定，无法动态修改</text>
  <text x="900" y="1090" class="text">• 通常基于特定屏幕尺寸和摄像头位置设计</text>
  <text x="900" y="1110" class="text">• 可能需要根据不同设备进行调整</text>
  <text x="900" y="1130" class="text">• 代表理想情况下用户眼部应该出现的像素位置</text>
  
  <!-- 数据流向图 -->
  <rect x="50" y="1190" width="1700" height="150" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1215" text-anchor="middle" class="subtitle">实际数据流向</text>
  
  <!-- C++流程 -->
  <rect x="80" y="1240" width="200" height="60" rx="5" fill="#fff3e0"/>
  <text x="180" y="1265" text-anchor="middle" class="text">C++硬编码参考点</text>
  <text x="180" y="1285" text-anchor="middle" class="text">setting_refer_points</text>
  
  <rect x="320" y="1240" width="200" height="60" rx="5" fill="#fff3e0"/>
  <text x="420" y="1265" text-anchor="middle" class="text">计算距离偏差</text>
  <text x="420" y="1285" text-anchor="middle" class="text">left/right_distance</text>
  
  <rect x="560" y="1240" width="200" height="60" rx="5" fill="#fff3e0"/>
  <text x="660" y="1265" text-anchor="middle" class="text">返回aligned状态</text>
  <text x="660" y="1285" text-anchor="middle" class="text">pose_result.aligning</text>
  
  <!-- Java流程 -->
  <rect x="800" y="1240" width="200" height="60" rx="5" fill="#e3f2fd"/>
  <text x="900" y="1265" text-anchor="middle" class="text">Java接收数据</text>
  <text x="900" y="1285" text-anchor="middle" class="text">PostureCalibrationResult</text>
  
  <rect x="1040" y="1240" width="200" height="60" rx="5" fill="#e3f2fd"/>
  <text x="1140" y="1265" text-anchor="middle" class="text">UI计算偏移方向</text>
  <text x="1140" y="1285" text-anchor="middle" class="text">使用Java参考点</text>
  
  <rect x="1280" y="1240" width="200" height="60" rx="5" fill="#e3f2fd"/>
  <text x="1380" y="1265" text-anchor="middle" class="text">显示调整提示</text>
  <text x="1380" y="1285" text-anchor="middle" class="text">"向左/右移动"</text>
  
  <!-- 连接箭头 -->
  <line x1="280" y1="1270" x2="320" y2="1270" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="520" y1="1270" x2="560" y2="1270" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="760" y1="1270" x2="800" y2="1270" stroke="#2196f3" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1000" y1="1270" x2="1040" y2="1270" stroke="#2196f3" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1240" y1="1270" x2="1280" y2="1270" stroke="#2196f3" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 总结 -->
  <rect x="100" y="1360" width="1600" height="30" rx="5" fill="#ffebee"/>
  <text x="900" y="1380" text-anchor="middle" class="highlight">
    总结：参考点从未传递给Native层，C++使用硬编码参考点，Java使用独立参考点进行UI计算
  </text>
</svg>
