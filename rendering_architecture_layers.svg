<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .kotlin-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 12; }
      .jni-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .cpp-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      .gpu-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .system-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      
      .kotlin-module { fill: #ebf3fd; stroke: #3498db; stroke-width: 2; rx: 8; }
      .jni-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .cpp-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .gpu-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .system-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .kotlin-arrow { stroke: #3498db; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .jni-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .cpp-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .gpu-arrow { stroke: #f39c12; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">渲染架构分层结构详解</text>
  
  <!-- 第一层：Kotlin/Java层 -->
  <rect x="50" y="70" width="320" height="120" class="kotlin-layer"/>
  <text x="210" y="95" text-anchor="middle" class="section-title">Kotlin/Java层</text>
  
  <rect x="70" y="110" width="280" height="70" class="kotlin-module"/>
  <text x="210" y="130" text-anchor="middle" class="method-title">UI控制和参数管理</text>
  <text x="80" y="150" class="text">• Activity/Fragment界面</text>
  <text x="80" y="165" class="text">• 参数设置和状态管理</text>

  <!-- 第二层：JNI桥接层 -->
  <rect x="390" y="70" width="320" height="120" class="jni-layer"/>
  <text x="550" y="95" text-anchor="middle" class="section-title">JNI桥接层</text>
  
  <rect x="410" y="110" width="280" height="70" class="jni-module"/>
  <text x="550" y="130" text-anchor="middle" class="method-title">Java ↔ Native桥接</text>
  <text x="420" y="150" class="text">• 参数传递和类型转换</text>
  <text x="420" y="165" class="text">• Surface对象传递</text>

  <!-- 第三层：C++业务层 -->
  <rect x="730" y="70" width="320" height="120" class="cpp-layer"/>
  <text x="890" y="95" text-anchor="middle" class="section-title">C++业务层</text>
  
  <rect x="750" y="110" width="280" height="70" class="cpp-module"/>
  <text x="890" y="130" text-anchor="middle" class="method-title">逻辑控制和参数计算</text>
  <text x="760" y="150" class="text">• 虚化逻辑和坐标计算</text>
  <text x="760" y="165" class="text">• IPQ_BLR引擎调用</text>

  <!-- 第四层：GPU渲染层 -->
  <rect x="1070" y="70" width="320" height="120" class="gpu-layer"/>
  <text x="1230" y="95" text-anchor="middle" class="section-title">GPU渲染层</text>
  
  <rect x="1090" y="110" width="280" height="70" class="gpu-module"/>
  <text x="1230" y="130" text-anchor="middle" class="method-title">OpenGL着色器渲染</text>
  <text x="1100" y="150" class="text">• 高斯模糊算法</text>
  <text x="1100" y="165" class="text">• GPU并行计算</text>

  <!-- 第五层：系统显示层 -->
  <rect x="1410" y="70" width="320" height="120" class="system-layer"/>
  <text x="1570" y="95" text-anchor="middle" class="section-title">系统显示层</text>
  
  <rect x="1430" y="110" width="280" height="70" class="system-module"/>
  <text x="1570" y="130" text-anchor="middle" class="method-title">Surface/屏幕显示</text>
  <text x="1440" y="150" class="text">• ANativeWindow</text>
  <text x="1440" y="165" class="text">• 帧缓冲显示</text>

  <!-- 连接箭头 -->
  <line x1="370" y1="130" x2="390" y2="130" class="kotlin-arrow"/>
  <line x1="710" y1="130" x2="730" y2="130" class="jni-arrow"/>
  <line x1="1050" y1="130" x2="1070" y2="130" class="cpp-arrow"/>
  <line x1="1390" y1="130" x2="1410" y2="130" class="gpu-arrow"/>

  <!-- 详细分析 -->
  <rect x="50" y="220" width="1700" height="1330" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="245" text-anchor="middle" class="title" style="font-size: 22px;">渲染架构分层详细分析</text>

  <!-- 第一部分：Kotlin/Java层 -->
  <text x="70" y="280" class="layer-title">📱 Kotlin/Java层 - UI控制和参数管理</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 界面控制</text>
  <text x="110" y="325" class="code-text">// GazeTrackService.kt</text>
  <text x="110" y="340" class="code-text">class GazeTrackService : Service() {</text>
  <text x="130" y="355" class="code-text">override fun onGazeTracking(gazeTrackResult: GazeTrackResult) {</text>
  <text x="150" y="370" class="code-text">// 🔥 UI层处理眼动结果</text>
  <text x="150" y="385" class="code-text">if (isDisplayViewpoint) {</text>
  <text x="170" y="400" class="code-text">WidgetManager.showDotView(this, gazeTrackResult, mScreenWidth, mScreenHeight)</text>
  <text x="150" y="415" class="code-text">}</text>
  <text x="130" y="430" class="code-text">}</text>
  <text x="110" y="445" class="code-text">}</text>
  
  <text x="90" y="470" class="flow-text" style="font-weight: bold;">2. 参数设置</text>
  <text x="110" y="490" class="code-text">// 虚化参数设置</text>
  <text x="110" y="505" class="code-text">GazeTracker.setRedBlurEnable(true)        // 开启虚化</text>
  <text x="110" y="520" class="code-text">GazeTracker.setRedBlurRadius(2.5f)        // 设置半径</text>
  <text x="110" y="535" class="code-text">GazeTracker.setRedBlurSigma(30f)          // 设置模糊强度</text>
  <text x="110" y="550" class="code-text">GazeTracker.setRedBlurMode(0)             // 设置模式</text>
  
  <text x="90" y="575" class="flow-text" style="font-weight: bold;">3. Surface管理</text>
  <text x="110" y="595" class="code-text">// SurfaceView/TextureView</text>
  <text x="110" y="610" class="code-text">surfaceView.holder.addCallback(object : SurfaceHolder.Callback {</text>
  <text x="130" y="625" class="code-text">override fun surfaceCreated(holder: SurfaceHolder) {</text>
  <text x="150" y="640" class="code-text">// 🔥 将Surface传递给Native层</text>
  <text x="150" y="655" class="code-text">GazeTracker.setANativeWindow(holder.surface)</text>
  <text x="130" y="670" class="code-text">}</text>
  <text x="110" y="685" class="code-text">})</text>

  <!-- 第二部分：JNI桥接层 -->
  <text x="900" y="280" class="layer-title">🌉 JNI桥接层 - Java ↔ Native桥接</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 参数传递</text>
  <text x="940" y="325" class="code-text">// GazeTracker.kt - JNI声明</text>
  <text x="940" y="340" class="code-text">external fun setRedBlurEnable(enable: Boolean)</text>
  <text x="940" y="355" class="code-text">external fun setRedBlurRadius(radius: Float)</text>
  <text x="940" y="370" class="code-text">external fun setRedBlurSigma(sigma: Float)</text>
  <text x="940" y="385" class="code-text">external fun setRedBlurMode(mode: Int)</text>
  
  <text x="920" y="410" class="flow-text" style="font-weight: bold;">2. Surface传递</text>
  <text x="940" y="430" class="code-text">// JNI实现</text>
  <text x="940" y="445" class="code-text">JNIEXPORT void JNICALL</text>
  <text x="940" y="460" class="code-text">Java_com_mitdd_gazetracker_GazeTracker_setANativeWindow(</text>
  <text x="960" y="475" class="code-text">JNIEnv *env, jobject thiz, jobject surface) {</text>
  <text x="980" y="490" class="code-text">// 🔥 将Java Surface转换为ANativeWindow</text>
  <text x="980" y="505" class="code-text">ANativeWindow *window = ANativeWindow_fromSurface(env, surface);</text>
  <text x="980" y="520" class="code-text">gazeService.setANativeWindow(window);</text>
  <text x="960" y="535" class="code-text">}</text>
  
  <text x="920" y="560" class="flow-text" style="font-weight: bold;">3. 类型转换</text>
  <text x="940" y="580" class="flow-text">• <tspan style="color: #e74c3c;">Boolean → bool：</tspan>Java布尔值转C++布尔值</text>
  <text x="940" y="595" class="flow-text">• <tspan style="color: #e74c3c;">Float → float：</tspan>Java浮点数转C++浮点数</text>
  <text x="940" y="610" class="flow-text">• <tspan style="color: #e74c3c;">Int → int：</tspan>Java整数转C++整数</text>
  <text x="940" y="625" class="flow-text">• <tspan style="color: #e74c3c;">Surface → ANativeWindow：</tspan>Java Surface转Native窗口</text>

  <!-- 第三部分：C++业务层 -->
  <text x="70" y="720" class="layer-title">⚙️ C++业务层 - 逻辑控制和参数计算</text>
  
  <text x="90" y="745" class="flow-text" style="font-weight: bold;">1. 虚化逻辑控制</text>
  <text x="110" y="765" class="code-text">// Blur.cpp - 业务逻辑</text>
  <text x="110" y="780" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="130" y="795" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="150" y="810" class="code-text">// 🔥 C++层处理坐标转换和参数计算</text>
  <text x="150" y="825" class="code-text">int screen_x = (int)(x * visual_image_width);</text>
  <text x="150" y="840" class="code-text">int screen_y = (int)(y * visual_image_height);</text>
  <text x="150" y="855" class="code-text">// 动态计算虚化半径</text>
  <text x="150" y="870" class="code-text">int circle_radius = (int)(dist / 11.33F / 19.5F * 1080.0F / 3.0F * macular_blur_radius);</text>
  <text x="150" y="885" class="code-text">// 🔥 调用GPU引擎，但不直接渲染</text>
  <text x="150" y="900" class="code-text">pq_blr->SetRadius(circle_radius)->SetPos(screen_x, screen_y)->Done();</text>
  <text x="130" y="915" class="code-text">}</text>
  <text x="110" y="930" class="code-text">}</text>
  
  <text x="90" y="955" class="flow-text" style="font-weight: bold;">2. 参数管理</text>
  <text x="110" y="975" class="flow-text">• <tspan style="color: #27ae60;">坐标转换：</tspan>相对坐标[0,1] → 屏幕像素坐标</text>
  <text x="110" y="990" class="flow-text">• <tspan style="color: #27ae60;">半径计算：</tspan>根据距离动态计算虚化半径</text>
  <text x="110" y="1005" class="flow-text">• <tspan style="color: #27ae60;">边界检查：</tspan>确保参数在有效范围内</text>
  <text x="110" y="1020" class="flow-text">• <tspan style="color: #27ae60;">状态管理：</tspan>管理虚化开关和模式状态</text>
  
  <text x="90" y="1045" class="flow-text" style="font-weight: bold;">3. 引擎接口调用</text>
  <text x="110" y="1065" class="code-text">// IPQ_BLR接口调用</text>
  <text x="110" y="1080" class="code-text">pq_blr = newPQ_BLR();                    // 创建GPU引擎</text>
  <text x="110" y="1095" class="code-text">pq_blr->SetRadius(radius)                // 设置参数</text>
  <text x="130" y="1110" class="code-text">->SetChannel(channel)</text>
  <text x="130" y="1125" class="code-text">->SetPos(x, y)</text>
  <text x="130" y="1140" class="code-text">->SetGaussKer(size, size, sigma)</text>
  <text x="130" y="1155" class="code-text">->SetMode(mode)</text>
  <text x="130" y="1170" class="code-text">->Done();                        // 🔥 触发GPU渲染</text>

  <!-- 第四部分：GPU渲染层 -->
  <text x="900" y="720" class="layer-title">🎮 GPU渲染层 - OpenGL着色器渲染</text>
  
  <text x="920" y="745" class="flow-text" style="font-weight: bold;">1. IPQ_BLR引擎实现</text>
  <text x="940" y="765" class="flow-text">• <tspan style="color: #f39c12;">第三方库：</tspan>IPQ_BLR是专门的GPU虚化引擎</text>
  <text x="940" y="780" class="flow-text">• <tspan style="color: #f39c12;">OpenGL实现：</tspan>底层使用OpenGL ES进行渲染</text>
  <text x="940" y="795" class="flow-text">• <tspan style="color: #f39c12;">着色器程序：</tspan>顶点着色器 + 片段着色器</text>
  <text x="940" y="810" class="flow-text">• <tspan style="color: #f39c12;">GPU并行：</tspan>利用GPU的并行计算能力</text>
  
  <text x="920" y="835" class="flow-text" style="font-weight: bold;">2. 渲染管线</text>
  <text x="940" y="855" class="flow-text">• <tspan style="color: #f39c12;">顶点处理：</tspan>处理虚化区域的几何变换</text>
  <text x="940" y="870" class="flow-text">• <tspan style="color: #f39c12;">光栅化：</tspan>将几何图形转换为像素</text>
  <text x="940" y="885" class="flow-text">• <tspan style="color: #f39c12;">片段着色：</tspan>对每个像素进行高斯模糊计算</text>
  <text x="940" y="900" class="flow-text">• <tspan style="color: #f39c12;">混合输出：</tspan>将处理结果混合到帧缓冲</text>
  
  <text x="920" y="925" class="flow-text" style="font-weight: bold;">3. 高斯模糊实现</text>
  <text x="940" y="945" class="flow-text">• <tspan style="color: #f39c12;">高斯核：</tspan>根据sigma生成高斯权重矩阵</text>
  <text x="940" y="960" class="flow-text">• <tspan style="color: #f39c12;">分离卷积：</tspan>水平和垂直方向分别处理</text>
  <text x="940" y="975" class="flow-text">• <tspan style="color: #f39c12;">纹理采样：</tspan>对周围像素进行加权采样</text>
  <text x="940" y="990" class="flow-text">• <tspan style="color: #f39c12;">颜色混合：</tspan>处理指定颜色通道</text>

  <!-- 第五部分：系统显示层 -->
  <text x="70" y="1215" class="layer-title">🖥️ 系统显示层 - Surface/屏幕显示</text>
  
  <text x="90" y="1240" class="flow-text" style="font-weight: bold;">1. ANativeWindow显示</text>
  <text x="110" y="1260" class="code-text">// GazeService.cpp - 显示处理</text>
  <text x="110" y="1275" class="code-text">void GazeService::draw(const Mat& img) {</text>
  <text x="130" y="1290" class="code-text">// 🔥 系统层处理最终显示</text>
  <text x="130" y="1305" class="code-text">ANativeWindow_setBuffersGeometry(window, img.cols, img.rows, WINDOW_FORMAT_RGBA_8888);</text>
  <text x="130" y="1320" class="code-text">ANativeWindow_Buffer buffer;</text>
  <text x="130" y="1335" class="code-text">ANativeWindow_lock(window, &buffer, nullptr);</text>
  <text x="130" y="1350" class="code-text">// 拷贝渲染结果到屏幕缓冲区</text>
  <text x="130" y="1365" class="code-text">memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize);</text>
  <text x="110" y="1380" class="code-text">}</text>
  
  <text x="90" y="1405" class="flow-text" style="font-weight: bold;">2. 显示特点</text>
  <text x="110" y="1425" class="flow-text">• <tspan style="color: #9b59b6;">帧缓冲：</tspan>GPU渲染结果写入帧缓冲区</text>
  <text x="110" y="1440" class="flow-text">• <tspan style="color: #9b59b6;">双缓冲：</tspan>避免画面撕裂</text>
  <text x="110" y="1455" class="flow-text">• <tspan style="color: #9b59b6;">硬件加速：</tspan>利用硬件加速显示</text>
  <text x="110" y="1470" class="flow-text">• <tspan style="color: #9b59b6;">实时显示：</tspan>30fps实时显示效果</text>

  <!-- 第六部分：分工总结 -->
  <text x="900" y="1215" class="layer-title">🎯 各层分工总结</text>
  
  <text x="920" y="1240" class="flow-text" style="font-weight: bold;">1. 渲染分工</text>
  <text x="940" y="1260" class="flow-text">• <tspan style="color: #3498db;">Kotlin/Java层：</tspan>UI控制、参数设置、Surface管理</text>
  <text x="940" y="1275" class="flow-text">• <tspan style="color: #e74c3c;">JNI层：</tspan>类型转换、参数传递、接口桥接</text>
  <text x="940" y="1290" class="flow-text">• <tspan style="color: #27ae60;">C++层：</tspan>业务逻辑、坐标计算、引擎调用</text>
  <text x="940" y="1305" class="flow-text">• <tspan style="color: #f39c12;">GPU层：</tspan>实际渲染、着色器计算、图像处理</text>
  <text x="940" y="1320" class="flow-text">• <tspan style="color: #9b59b6;">系统层：</tspan>帧缓冲管理、屏幕显示</text>
  
  <text x="920" y="1345" class="flow-text" style="font-weight: bold;">2. 关键发现</text>
  <text x="940" y="1365" class="highlight-text">C++层主要负责业务逻辑和参数计算，真正的渲染在GPU层！</text>
  <text x="940" y="1385" class="flow-text">• <tspan style="color: #e74c3c;">C++不直接渲染：</tspan>只是调用IPQ_BLR引擎接口</text>
  <text x="940" y="1400" class="flow-text">• <tspan style="color: #e74c3c;">GPU负责渲染：</tspan>OpenGL着色器进行实际的图像处理</text>
  <text x="940" y="1415" class="flow-text">• <tspan style="color: #e74c3c;">分层架构：</tspan>每层有明确的职责分工</text>
  <text x="940" y="1430" class="flow-text">• <tspan style="color: #e74c3c;">高效协作：</tspan>各层协作实现高性能渲染</text>

  <!-- 总结 -->
  <rect x="70" y="1460" width="1600" height="80" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1485" class="layer-title">🌟 渲染架构总结</text>
  <text x="90" y="1510" class="flow-text">• <tspan style="font-weight: bold; color: #3498db;">分层架构：</tspan>Kotlin/Java(UI) → JNI(桥接) → C++(逻辑) → GPU(渲染) → 系统(显示)</text>
  <text x="90" y="1530" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">渲染分工：</tspan>C++负责业务逻辑和参数计算，GPU负责实际的图像渲染处理</text>

</svg>
