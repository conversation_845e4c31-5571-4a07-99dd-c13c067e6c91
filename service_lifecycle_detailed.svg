<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .phase-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .operation-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#redarrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">GazeTrackService 生命周期详细流程图</text>
  
  <!-- Service启动 -->
  <ellipse cx="100" cy="80" rx="60" ry="25" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
  <text x="100" y="87" text-anchor="middle" class="phase-title">Service启动</text>
  
  <!-- onCreate 阶段 -->
  <rect x="200" y="60" width="200" height="200" fill="#e3f2fd" stroke="#3498db" stroke-width="2" rx="10"/>
  <text x="300" y="80" text-anchor="middle" class="subtitle">onCreate 阶段</text>
  
  <rect x="220" y="90" width="160" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="300" y="107" text-anchor="middle" style="font-size: 12px; fill: white;">设置LifecycleRegistry</text>
  
  <rect x="220" y="120" width="160" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="300" y="137" text-anchor="middle" style="font-size: 12px; fill: white;">创建Handler和Messenger</text>
  
  <rect x="220" y="150" width="160" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="300" y="167" text-anchor="middle" style="font-size: 12px; fill: white;">获取屏幕宽高</text>
  
  <rect x="220" y="180" width="160" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="300" y="197" text-anchor="middle" style="font-size: 12px; fill: white;">启动前台服务</text>
  
  <rect x="220" y="210" width="160" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="300" y="227" text-anchor="middle" style="font-size: 12px; fill: white;">启动WebSocket服务</text>
  
  <!-- onStartCommand 阶段 -->
  <rect x="450" y="60" width="200" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
  <text x="550" y="80" text-anchor="middle" class="subtitle">onStartCommand</text>
  
  <rect x="470" y="90" width="160" height="25" fill="#4caf50" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="550" y="107" text-anchor="middle" style="font-size: 12px; fill: white;">设置STARTED状态</text>
  
  <rect x="470" y="120" width="160" height="25" fill="#4caf50" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="550" y="137" text-anchor="middle" style="font-size: 12px; fill: white;">设置RESUMED状态</text>
  
  <rect x="470" y="150" width="160" height="25" fill="#4caf50" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="550" y="167" text-anchor="middle" style="font-size: 12px; fill: white;">返回START_REDELIVER_INTENT</text>
  
  <!-- onBind 阶段 -->
  <rect x="700" y="60" width="200" height="120" fill="#fff8e1" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="800" y="80" text-anchor="middle" class="subtitle">onBind</text>
  
  <rect x="720" y="90" width="160" height="25" fill="#ff9800" stroke="#f57c00" stroke-width="1" rx="3"/>
  <text x="800" y="107" text-anchor="middle" style="font-size: 12px; fill: white;">返回Messenger.binder</text>
  
  <rect x="720" y="120" width="160" height="25" fill="#ff9800" stroke="#f57c00" stroke-width="1" rx="3"/>
  <text x="800" y="137" text-anchor="middle" style="font-size: 12px; fill: white;">建立Activity通信</text>
  
  <!-- Service运行中 -->
  <rect x="950" y="60" width="200" height="120" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" rx="10"/>
  <text x="1050" y="80" text-anchor="middle" class="subtitle">Service运行中</text>
  
  <rect x="970" y="90" width="160" height="25" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
  <text x="1050" y="107" text-anchor="middle" style="font-size: 12px; fill: white;">处理消息</text>
  
  <rect x="970" y="120" width="160" height="25" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
  <text x="1050" y="137" text-anchor="middle" style="font-size: 12px; fill: white;">管理摄像头和追踪</text>
  
  <!-- 消息处理详情 -->
  <rect x="200" y="300" width="800" height="200" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="600" y="325" text-anchor="middle" class="subtitle">消息处理详情 (parseMessage)</text>
  
  <rect x="220" y="340" width="150" height="30" fill="#17a2b8" stroke="#138496" stroke-width="1" rx="3"/>
  <text x="295" y="360" text-anchor="middle" style="font-size: 11px; fill: white;">MSG_TURN_ON_CAMERA</text>
  
  <rect x="390" y="340" width="150" height="30" fill="#28a745" stroke="#1e7e34" stroke-width="1" rx="3"/>
  <text x="465" y="360" text-anchor="middle" style="font-size: 11px; fill: white;">MSG_START_TRACK</text>
  
  <rect x="560" y="340" width="150" height="30" fill="#ffc107" stroke="#e0a800" stroke-width="1" rx="3"/>
  <text x="635" y="360" text-anchor="middle" style="font-size: 11px; fill: white;">MSG_START_APPLIED_*</text>
  
  <rect x="730" y="340" width="150" height="30" fill="#dc3545" stroke="#c82333" stroke-width="1" rx="3"/>
  <text x="805" y="360" text-anchor="middle" style="font-size: 11px; fill: white;">MSG_STOP_*</text>
  
  <text x="295" y="390" class="operation-text">开启摄像头</text>
  <text x="295" y="405" class="code-text">GTCameraManager.startCamera()</text>
  
  <text x="465" y="390" class="operation-text">开始眼动追踪</text>
  <text x="465" y="405" class="code-text">TrackingManager.startTrack()</text>
  
  <text x="635" y="390" class="operation-text">开始应用模式</text>
  <text x="635" y="405" class="code-text">AppliedManager.start*()</text>
  
  <text x="805" y="390" class="operation-text">停止相关操作</text>
  <text x="805" y="405" class="code-text">各种Manager.stop*()</text>
  
  <text x="220" y="440" class="operation-text">• MSG_GET_GAZE_TRAJECTORY: 获取眼动轨迹数据</text>
  <text x="220" y="455" class="operation-text">• MSG_START_APPLIED_READING: 开始阅读模式</text>
  <text x="220" y="470" class="operation-text">• MSG_START_APPLIED_FOLLOW: 开始追随模式</text>
  
  <!-- onUnbind 和 onDestroy -->
  <rect x="200" y="550" width="200" height="120" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="10"/>
  <text x="300" y="570" text-anchor="middle" class="subtitle">onUnbind</text>
  
  <rect x="220" y="580" width="160" height="25" fill="#e91e63" stroke="#ad1457" stroke-width="1" rx="3"/>
  <text x="300" y="597" text-anchor="middle" style="font-size: 12px; fill: white;">记录解绑日志</text>
  
  <rect x="220" y="610" width="160" height="25" fill="#e91e63" stroke="#ad1457" stroke-width="1" rx="3"/>
  <text x="300" y="627" text-anchor="middle" style="font-size: 12px; fill: white;">断开Activity连接</text>
  
  <rect x="450" y="550" width="200" height="200" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="10"/>
  <text x="550" y="570" text-anchor="middle" class="subtitle">onDestroy</text>
  
  <rect x="470" y="580" width="160" height="25" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="550" y="597" text-anchor="middle" style="font-size: 12px; fill: white;">设置DESTROYED状态</text>
  
  <rect x="470" y="610" width="160" height="25" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="550" y="627" text-anchor="middle" style="font-size: 12px; fill: white;">AppliedManager.destroy()</text>
  
  <rect x="470" y="640" width="160" height="25" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="550" y="657" text-anchor="middle" style="font-size: 12px; fill: white;">TrackingManager.destroy()</text>
  
  <rect x="470" y="670" width="160" height="25" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="550" y="687" text-anchor="middle" style="font-size: 12px; fill: white;">GTCameraManager.stopCamera()</text>
  
  <rect x="470" y="700" width="160" height="25" fill="#f44336" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="550" y="717" text-anchor="middle" style="font-size: 12px; fill: white;">关闭WebSocket服务</text>
  
  <!-- Service结束 -->
  <ellipse cx="750" cy="650" rx="60" ry="25" fill="#95a5a6" stroke="#7f8c8d" stroke-width="2"/>
  <text x="750" y="657" text-anchor="middle" class="phase-title">Service结束</text>
  
  <!-- 流程箭头 -->
  <line x1="160" y1="80" x2="200" y2="80" class="flow-arrow"/>
  <line x1="400" y1="80" x2="450" y2="80" class="flow-arrow"/>
  <line x1="650" y1="80" x2="700" y2="80" class="flow-arrow"/>
  <line x1="900" y1="80" x2="950" y2="80" class="flow-arrow"/>
  
  <line x1="1050" y1="180" x2="1050" y2="220" class="flow-arrow"/>
  <line x1="1050" y1="220" x2="600" y2="220" class="flow-arrow"/>
  <line x1="600" y1="220" x2="600" y2="300" class="flow-arrow"/>
  
  <line x1="600" y1="500" x2="600" y2="520" class="flow-arrow"/>
  <line x1="600" y1="520" x2="300" y2="520" class="flow-arrow"/>
  <line x1="300" y1="520" x2="300" y2="550" class="flow-arrow"/>
  
  <line x1="400" y1="610" x2="450" y2="610" class="flow-arrow"/>
  <line x1="650" y1="650" x2="690" y2="650" class="flow-arrow"/>
  
</svg>
