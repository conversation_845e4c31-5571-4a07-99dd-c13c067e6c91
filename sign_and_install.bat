@echo off
REM Batch script to zipalign, sign, and install an APK

REM --- Configuration Section ---
REM Path to Android SDK build-tools (containing zipalign and apks<PERSON>er)
set BUILD_TOOLS_DIR=C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0

REM Path to Android SDK platform-tools (containing adb)
set PLATFORM_TOOLS_DIR=D:\platform-tools_r28.0.0-windows\platform-tools

REM Paths for APK files and signing keys
set SIGN_DIR=D:\sign
set DEBUG_APK_NAME=app-debug.apk
set ALIGNED_APK_NAME=app-signed.apk
set SIGNED_OUTPUT_APK_NAME=app-signed-2.apk
set KEY_FILE=platform.pk8
set CERT_FILE=platform.x509.pem

REM --- End Configuration Section ---

REM Construct full paths
set DEBUG_APK_PATH=%SIGN_DIR%\%DEBUG_APK_NAME%
set ALIGNED_APK_PATH=%SIGN_DIR%\%ALIGNED_APK_NAME%
set SIGNED_OUTPUT_APK_PATH=%SIGN_DIR%\%SIGNED_OUTPUT_APK_NAME%
set KEY_PATH=%SIGN_DIR%\%KEY_FILE%
set CERT_PATH=%SIGN_DIR%\%CERT_FILE%

echo Starting APK processing...

REM 1. Zipalign
echo.
echo [1/3] Aligning APK...
echo Changing directory to Build Tools: %BUILD_TOOLS_DIR%
cd /d "%BUILD_TOOLS_DIR%"
if errorlevel 1 (
    echo ERROR: Failed to change directory to Build Tools.
    goto :eof
)

echo Running zipalign for %DEBUG_APK_PATH%
"%BUILD_TOOLS_DIR%\zipalign.exe" -p -f -v 4 "%DEBUG_APK_PATH%" "%ALIGNED_APK_PATH%"
if errorlevel 1 (
    echo ERROR: Zipalign failed.
    goto :eof
)
echo Zipalign successful. Output: %ALIGNED_APK_PATH%

REM 2. Sign APK
echo.
echo [2/3] Signing APK...
echo Running apksigner for %ALIGNED_APK_PATH%
"%BUILD_TOOLS_DIR%\apksigner.bat" sign --key "%KEY_PATH%" --cert "%CERT_PATH%" --out "%SIGNED_OUTPUT_APK_PATH%" "%ALIGNED_APK_PATH%"
if errorlevel 1 (
    echo ERROR: Apksigner failed.
    goto :eof
)
echo Apksigner successful. Output: %SIGNED_OUTPUT_APK_PATH%

REM 3. Install APK
echo.
echo [3/3] Installing APK...
echo Changing directory to Platform Tools: %PLATFORM_TOOLS_DIR%
cd /d "%PLATFORM_TOOLS_DIR%"
if errorlevel 1 (
    echo ERROR: Failed to change directory to Platform Tools.
    goto :eof
)

echo Running adb install for %SIGNED_OUTPUT_APK_PATH%
"%PLATFORM_TOOLS_DIR%\adb.exe" install "%SIGNED_OUTPUT_APK_PATH%"
if errorlevel 1 (
    echo ERROR: ADB install failed.
    goto :eof
)
echo ADB install successful.

echo.
echo All commands executed successfully!

:eof
echo Script finished.