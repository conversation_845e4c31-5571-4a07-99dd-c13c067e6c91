<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #7f8c8d; }
      .description-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      
      .stare-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .follow-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .glance-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .data-flow { fill: #fff9c4; stroke: #fbc02d; stroke-width: 1; }
      .algorithm-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 1; }
      .json-box { fill: #ffebee; stroke: #f44336; stroke-width: 1; }
      
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">STARE/FOLLOW/GLANCE数据采集与JSON组织详解</text>
  
  <!-- 数据采集阶段 -->
  <rect x="50" y="70" width="1500" height="200" class="data-flow" rx="8"/>
  <text x="800" y="95" text-anchor="middle" class="subtitle">第一阶段：数据采集 (collect_data)</text>
  
  <!-- STARE采集 -->
  <rect x="80" y="120" width="450" height="130" class="stare-box" rx="5"/>
  <text x="305" y="140" text-anchor="middle" class="step-title">STARE 注视数据采集</text>
  <text x="90" y="160" class="code-text">void GazeStare::collect_data(float x, float y, float duration) {</text>
  <text x="100" y="175" class="code-text">    gaze_data_list.emplace_back(Point3f(x, y, duration));</text>
  <text x="90" y="190" class="code-text">}</text>
  <text x="90" y="210" class="step-text">• 简单存储所有视线点</text>
  <text x="90" y="225" class="step-text">• Point3f(x坐标, y坐标, 持续时间)</text>
  <text x="90" y="240" class="step-text">• 无过滤条件，全量收集</text>
  
  <!-- FOLLOW采集 -->
  <rect x="550" y="120" width="450" height="130" class="follow-box" rx="5"/>
  <text x="775" y="140" text-anchor="middle" class="step-title">FOLLOW 追随数据采集</text>
  <text x="560" y="160" class="code-text">void GazeFollow::collect_data(float x, float y, float duration) {</text>
  <text x="570" y="175" class="code-text">    gaze_data_list.emplace_back(Point3f(x, y, duration));</text>
  <text x="560" y="190" class="code-text">}</text>
  <text x="560" y="210" class="step-text">• 与STARE相同的存储方式</text>
  <text x="560" y="225" class="step-text">• Point3f(x坐标, y坐标, 持续时间)</text>
  <text x="560" y="240" class="step-text">• 连续轨迹数据收集</text>
  
  <!-- GLANCE采集 -->
  <rect x="1020" y="120" width="450" height="130" class="glance-box" rx="5"/>
  <text x="1245" y="140" text-anchor="middle" class="step-title">GLANCE 扫视数据采集</text>
  <text x="1030" y="160" class="code-text">bool GazeGlance::collect_data(float x, float y, float duration) {</text>
  <text x="1040" y="175" class="code-text">    float dist = sqrt(pow(target_point.x - x, 2) + pow(target_point.y - y, 2));</text>
  <text x="1040" y="190" class="code-text">    if (dist &lt; app_hit_near_thres) {</text>
  <text x="1050" y="205" class="code-text">        gaze_data_list.emplace_back(Point3f(target_point.x, target_point.y, endTime - startTime));</text>
  <text x="1050" y="220" class="code-text">        return true;</text>
  <text x="1040" y="235" class="code-text">    }</text>
  <text x="1030" y="250" class="code-text">}</text>
  
  <!-- 数据后处理阶段 -->
  <rect x="50" y="300" width="1500" height="350" class="algorithm-box" rx="8"/>
  <text x="800" y="325" text-anchor="middle" class="subtitle">第二阶段：数据后处理 (postprocess_trajectory_data)</text>
  
  <!-- STARE后处理 -->
  <rect x="80" y="350" width="450" height="280" class="stare-box" rx="5"/>
  <text x="305" y="370" text-anchor="middle" class="step-title">STARE 注视点聚类算法</text>
  <text x="90" y="390" class="code-text">std::vector&lt;std::vector&lt;Point3f&gt;&gt; GazeStare::postprocess_trajectory_data() {</text>
  <text x="100" y="405" class="code-text">    Point3f pre_p = gaze_data_list[0];</text>
  <text x="100" y="420" class="code-text">    vector&lt;Point3f&gt; line_points = {pre_p};</text>
  <text x="100" y="435" class="code-text">    for (int i = 1; i &lt; num_p; i++) {</text>
  <text x="110" y="450" class="code-text">        Point3f p1 = gaze_data_list[i - 1];</text>
  <text x="110" y="465" class="code-text">        Point3f p2 = gaze_data_list[i];</text>
  <text x="110" y="480" class="code-text">        float grad1 = abs(p2.x - p1.x) + abs(p2.y - p1.y);</text>
  <text x="110" y="495" class="code-text">        float grad2 = abs(p2.x - pre_p.x) + abs(p2.y - pre_p.y);</text>
  <text x="110" y="510" class="code-text">        if (grad1 &lt; app_filter_thres) {</text>
  <text x="120" y="525" class="code-text">            if (grad2 &lt; app_merge_thres) {</text>
  <text x="130" y="540" class="code-text">                line_points.push_back(p2);  // 聚类</text>
  <text x="120" y="555" class="code-text">            } else {</text>
  <text x="130" y="570" class="code-text">                stare_points.push_back(line_points);  // 新聚类</text>
  <text x="120" y="585" class="code-text">            }</text>
  <text x="110" y="600" class="code-text">        }</text>
  <text x="100" y="615" class="code-text">    }</text>
  <text x="90" y="630" class="code-text">}</text>
  
  <!-- FOLLOW后处理 -->
  <rect x="550" y="350" width="450" height="280" class="follow-box" rx="5"/>
  <text x="775" y="370" text-anchor="middle" class="step-title">FOLLOW 轨迹分段算法</text>
  <text x="560" y="390" class="step-text" font-weight="bold">算法特点：</text>
  <text x="560" y="405" class="step-text">• 与STARE使用相同的聚类算法</text>
  <text x="560" y="420" class="step-text">• grad1: 相邻点距离阈值</text>
  <text x="560" y="435" class="step-text">• grad2: 与聚类起点距离阈值</text>
  
  <text x="560" y="460" class="step-text" font-weight="bold">处理逻辑：</text>
  <text x="560" y="475" class="step-text">1. 计算相邻点的曼哈顿距离</text>
  <text x="560" y="490" class="step-text">2. 如果距离小于filter_thres，继续判断</text>
  <text x="560" y="505" class="step-text">3. 如果与聚类起点距离小于merge_thres</text>
  <text x="560" y="520" class="step-text">   则加入当前聚类</text>
  <text x="560" y="535" class="step-text">4. 否则开始新的聚类</text>
  
  <text x="560" y="560" class="step-text" font-weight="bold">输出结果：</text>
  <text x="560" y="575" class="step-text">• vector&lt;vector&lt;Point3f&gt;&gt;</text>
  <text x="560" y="590" class="step-text">• 每个内层vector代表一个轨迹段</text>
  <text x="560" y="605" class="step-text">• 适用于追随路径的分段分析</text>
  
  <!-- GLANCE后处理 -->
  <rect x="1020" y="350" width="450" height="280" class="glance-box" rx="5"/>
  <text x="1245" y="370" text-anchor="middle" class="step-title">GLANCE 简化处理算法</text>
  <text x="1030" y="390" class="code-text">std::vector&lt;std::vector&lt;Point3f&gt;&gt; GazeGlance::postprocess_trajectory_data() {</text>
  <text x="1040" y="405" class="code-text">    std::vector&lt;std::vector&lt;Point3f&gt;&gt; stare_points;</text>
  <text x="1040" y="420" class="code-text">    if (!gaze_data_list.empty()) {</text>
  <text x="1050" y="435" class="code-text">        vector&lt;Point3f&gt; line_points = {};</text>
  <text x="1050" y="450" class="code-text">        for (int i = 0; i &lt; num_p; i++) {</text>
  <text x="1060" y="465" class="code-text">            line_points.push_back(gaze_data_list[i]);</text>
  <text x="1050" y="480" class="code-text">        }</text>
  <text x="1050" y="495" class="code-text">        stare_points.push_back(line_points);</text>
  <text x="1040" y="510" class="code-text">    }</text>
  <text x="1040" y="525" class="code-text">    return stare_points;</text>
  <text x="1030" y="540" class="code-text">}</text>
  
  <text x="1030" y="560" class="step-text" font-weight="bold">特点：</text>
  <text x="1030" y="575" class="step-text">• 不进行聚类分析</text>
  <text x="1030" y="590" class="step-text">• 所有数据点放入一个组</text>
  <text x="1030" y="605" class="step-text">• 因为已在collect_data阶段过滤</text>
  
  <!-- JSON转换阶段 -->
  <rect x="50" y="680" width="1500" height="300" class="json-box" rx="8"/>
  <text x="800" y="705" text-anchor="middle" class="subtitle">第三阶段：JSON转换 (transfer_trajectory_to_jsondata)</text>
  
  <!-- JSON转换算法 -->
  <rect x="80" y="730" width="1400" height="230" class="data-flow" rx="5"/>
  <text x="780" y="750" text-anchor="middle" class="step-title">统一的JSON转换算法</text>
  
  <text x="100" y="770" class="code-text">string transfer_trajectory_to_jsondata(std::vector&lt;std::vector&lt;Point3f&gt;&gt;&amp; stare_points) {</text>
  <text x="110" y="785" class="code-text">    Json::Value root;</text>
  <text x="110" y="800" class="code-text">    Json::Value pointsArray;</text>
  <text x="110" y="815" class="code-text">    </text>
  <text x="110" y="830" class="code-text">    for (int i = 0; i &lt; size; i++) {</text>
  <text x="120" y="845" class="code-text">        Json::Value one_point;</text>
  <text x="120" y="860" class="code-text">        vector&lt;Point3f&gt;&amp; points = stare_points[i];  // 获取一个聚类</text>
  <text x="120" y="875" class="code-text">        </text>
  <text x="120" y="890" class="code-text">        // 计算聚类中心点</text>
  <text x="120" y="905" class="code-text">        float mean_x = sum_x / (float)num;</text>
  <text x="120" y="920" class="code-text">        float mean_y = sum_y / (float)num;</text>
  <text x="120" y="935" class="code-text">        </text>
  <text x="120" y="950" class="code-text">        one_point["index"] = i + 1;</text>
  <text x="120" y="965" class="code-text">        one_point["x"] = mean_x;        // 聚类中心X坐标</text>
  <text x="120" y="980" class="code-text">        one_point["y"] = mean_y;        // 聚类中心Y坐标</text>
  <text x="120" y="995" class="code-text">        one_point["duration"] = sum_duration;  // 总持续时间</text>
  <text x="120" y="1010" class="code-text">        pointsArray.append(one_point);</text>
  <text x="110" y="1025" class="code-text">    }</text>
  <text x="100" y="1040" class="code-text">}</text>
  
  <!-- 最终JSON格式对比 -->
  <rect x="50" y="1010" width="1500" height="350" class="data-flow" rx="8"/>
  <text x="800" y="1035" text-anchor="middle" class="subtitle">最终JSON格式对比</text>
  
  <!-- STARE JSON -->
  <rect x="80" y="1060" width="450" height="280" class="stare-box" rx="5"/>
  <text x="305" y="1080" text-anchor="middle" class="step-title">STARE 注视分析JSON</text>
  <text x="90" y="1100" class="code-text">{</text>
  <text x="100" y="1115" class="code-text">  "gaze": [</text>
  <text x="110" y="1130" class="code-text">    {</text>
  <text x="120" y="1145" class="code-text">      "index": 1,</text>
  <text x="120" y="1160" class="code-text">      "x": 0.45,      // 注视点1中心</text>
  <text x="120" y="1175" class="code-text">      "y": 0.52,</text>
  <text x="120" y="1190" class="code-text">      "duration": 1500  // 注视总时长</text>
  <text x="110" y="1205" class="code-text">    },</text>
  <text x="110" y="1220" class="code-text">    {</text>
  <text x="120" y="1235" class="code-text">      "index": 2,</text>
  <text x="120" y="1250" class="code-text">      "x": 0.48,      // 注视点2中心</text>
  <text x="120" y="1265" class="code-text">      "y": 0.55,</text>
  <text x="120" y="1280" class="code-text">      "duration": 800</text>
  <text x="110" y="1295" class="code-text">    }</text>
  <text x="100" y="1310" class="code-text">  ]</text>
  <text x="90" y="1325" class="code-text">}</text>
  
  <!-- FOLLOW JSON -->
  <rect x="550" y="1060" width="450" height="280" class="follow-box" rx="5"/>
  <text x="775" y="1080" text-anchor="middle" class="step-title">FOLLOW 追随评估JSON</text>
  <text x="560" y="1100" class="code-text">{</text>
  <text x="570" y="1115" class="code-text">  "gaze": [</text>
  <text x="580" y="1130" class="code-text">    {</text>
  <text x="590" y="1145" class="code-text">      "index": 1,</text>
  <text x="590" y="1160" class="code-text">      "x": 0.2,       // 轨迹段1中心</text>
  <text x="590" y="1175" class="code-text">      "y": 0.3,</text>
  <text x="590" y="1190" class="code-text">      "duration": 2000  // 段总时长</text>
  <text x="580" y="1205" class="code-text">    },</text>
  <text x="580" y="1220" class="code-text">    {</text>
  <text x="590" y="1235" class="code-text">      "index": 2,</text>
  <text x="590" y="1250" class="code-text">      "x": 0.6,       // 轨迹段2中心</text>
  <text x="590" y="1265" class="code-text">      "y": 0.7,</text>
  <text x="590" y="1280" class="code-text">      "duration": 1800</text>
  <text x="580" y="1295" class="code-text">    }</text>
  <text x="570" y="1310" class="code-text">  ]</text>
  <text x="560" y="1325" class="code-text">}</text>
  
  <!-- GLANCE JSON -->
  <rect x="1020" y="1060" width="450" height="280" class="glance-box" rx="5"/>
  <text x="1245" y="1080" text-anchor="middle" class="step-title">GLANCE 扫视结果JSON</text>
  <text x="1030" y="1100" class="code-text">{</text>
  <text x="1040" y="1115" class="code-text">  "gaze": [</text>
  <text x="1050" y="1130" class="code-text">    {</text>
  <text x="1060" y="1145" class="code-text">      "index": 1,</text>
  <text x="1060" y="1160" class="code-text">      "x": 0.5,       // 所有命中点中心</text>
  <text x="1060" y="1175" class="code-text">      "y": 0.5,</text>
  <text x="1060" y="1190" class="code-text">      "duration": 3200  // 总反应时间</text>
  <text x="1050" y="1205" class="code-text">    }</text>
  <text x="1040" y="1220" class="code-text">  ]</text>
  <text x="1030" y="1235" class="code-text">}</text>
  
  <text x="1030" y="1260" class="step-text" font-weight="bold">特点：</text>
  <text x="1030" y="1275" class="step-text">• 通常只有一个数据点</text>
  <text x="1030" y="1290" class="step-text">• 包含所有命中目标的数据</text>
  <text x="1030" y="1305" class="step-text">• duration是累计反应时间</text>
  
  <!-- 流程箭头 -->
  <line x1="305" y1="270" x2="305" y2="350" class="data-arrow"/>
  <line x1="775" y1="270" x2="775" y2="350" class="data-arrow"/>
  <line x1="1245" y1="270" x2="1245" y2="350" class="data-arrow"/>
  
  <line x1="305" y1="650" x2="305" y2="730" class="data-arrow"/>
  <line x1="775" y1="650" x2="775" y2="730" class="data-arrow"/>
  <line x1="1245" y1="650" x2="1245" y2="730" class="data-arrow"/>
  
  <line x1="780" y1="980" x2="780" y2="1060" class="data-arrow"/>
</svg>
