<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .step-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .condition-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #e74c3c; font-style: italic; }
      
      .start-step { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .validation-step { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .dialog-step { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .service-step { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .decision-step { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      
      .step-module { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 8; }
      .validation-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .dialog-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .service-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .decision-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .condition-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 8,4; }
      .success-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .fail-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">switchMaskTherapy 方法工作流程图</text>
  
  <!-- 第一步：方法入口 -->
  <rect x="50" y="70" width="350" height="120" class="start-step"/>
  <text x="225" y="95" text-anchor="middle" class="section-title">1. 方法入口</text>
  
  <rect x="70" y="110" width="310" height="70" class="step-module"/>
  <text x="225" y="130" text-anchor="middle" class="method-title">switchMaskTherapy(state: Boolean)</text>
  <text x="80" y="150" class="text">• 接收用户开关操作状态参数</text>
  <text x="80" y="165" class="text">• Logger.d(TAG, "switchMaskTherapy state = $state")</text>
  <text x="80" y="180" class="text">• 开始执行遮盖疗法开关逻辑</text>

  <!-- 第二步：关闭状态处理 -->
  <rect x="450" y="70" width="350" height="120" class="validation-step"/>
  <text x="625" y="95" text-anchor="middle" class="section-title">2. 关闭状态处理</text>
  
  <rect x="470" y="110" width="310" height="70" class="validation-module"/>
  <text x="625" y="130" text-anchor="middle" class="method-title">if (!state) - 关闭遮盖疗法</text>
  <text x="480" y="150" class="text">• switchMaskTherapy.isChecked = false</text>
  <text x="480" y="165" class="text">• 发送停止消息序列到GazeTrackService</text>
  <text x="480" y="180" class="text">• return 直接退出方法</text>

  <!-- 停止消息详情 -->
  <rect x="850" y="70" width="400" height="120" class="service-step"/>
  <text x="1050" y="95" text-anchor="middle" class="section-title">停止消息序列</text>
  
  <rect x="870" y="110" width="360" height="70" class="service-module"/>
  <text x="1050" y="130" text-anchor="middle" class="method-title">sendMessageToService(三个消息)</text>
  <text x="880" y="150" class="code-text">• MSG_STOP_APPLIED_CURE - 停止遮盖疗法</text>
  <text x="880" y="165" class="code-text">• MSG_STOP_TRACK - 停止眼动追踪</text>
  <text x="880" y="180" class="code-text">• MSG_TURN_OFF_CAMERA - 关闭相机</text>

  <!-- 第三步：用户绑定验证 -->
  <rect x="50" y="220" width="350" height="120" class="validation-step"/>
  <text x="225" y="245" text-anchor="middle" class="section-title">3. 用户绑定验证</text>
  
  <rect x="70" y="260" width="310" height="70" class="validation-module"/>
  <text x="225" y="280" text-anchor="middle" class="method-title">UserManager.isBind() 检查</text>
  <text x="80" y="300" class="text">• 检查用户是否已绑定设备</text>
  <text x="80" y="315" class="text">• 未绑定：显示绑定提示对话框</text>
  <text x="80" y="330" class="text">• 引导用户跳转到绑定页面</text>

  <!-- 绑定失败处理 -->
  <rect x="450" y="220" width="350" height="120" class="dialog-step"/>
  <text x="625" y="245" text-anchor="middle" class="section-title">绑定失败处理</text>
  
  <rect x="470" y="260" width="310" height="70" class="dialog-module"/>
  <text x="625" y="280" text-anchor="middle" class="method-title">showNotificationDialog</text>
  <text x="480" y="300" class="text">• 显示"请先绑定用户再开启遮盖疗法"</text>
  <text x="480" y="315" class="text">• 确认回调：(mActivity as? HomeMainActivity)?.toBind()</text>
  <text x="480" y="330" class="text">• switchMaskTherapy.isChecked = false</text>

  <!-- 第四步：疗程数据验证 -->
  <rect x="850" y="220" width="400" height="120" class="validation-step"/>
  <text x="1050" y="245" text-anchor="middle" class="section-title">4. 疗程数据验证</text>
  
  <rect x="870" y="260" width="360" height="70" class="validation-module"/>
  <text x="1050" y="280" text-anchor="middle" class="method-title">疗程可用性检查</text>
  <text x="880" y="300" class="text">• maskVM.occlusionTherapy == null 检查</text>
  <text x="880" y="315" class="text">• UserManager.getTreatmentInfo() == null 检查</text>
  <text x="880" y="330" class="text">• 无疗程：显示"无可用疗程，请联系医生"</text>

  <!-- 第五步：疗程状态处理 -->
  <rect x="50" y="370" width="550" height="180" class="decision-step"/>
  <text x="325" y="395" text-anchor="middle" class="section-title">5. 疗程状态处理</text>
  
  <!-- INACTIVE状态 -->
  <rect x="70" y="410" width="160" height="60" class="decision-module"/>
  <text x="150" y="430" text-anchor="middle" class="method-title">INACTIVE状态</text>
  <text x="80" y="450" class="text">• 显示激活提醒对话框</text>
  <text x="80" y="465" class="text">• 激活对话框确认后调用</text>
  
  <!-- PENDING状态 -->
  <rect x="240" y="410" width="160" height="60" class="decision-module"/>
  <text x="320" y="430" text-anchor="middle" class="method-title">PENDING状态</text>
  <text x="250" y="450" class="text">• 显示暂停对话框</text>
  <text x="250" y="465" class="text">• 激活对话框确认后调用</text>
  
  <!-- COMPLETED状态 -->
  <rect x="410" y="410" width="160" height="60" class="decision-module"/>
  <text x="490" y="430" text-anchor="middle" class="method-title">COMPLETED状态</text>
  <text x="420" y="450" class="text">• 显示疗程过期对话框</text>
  <text x="420" y="465" class="text">• 直接返回，无法启动</text>
  
  <!-- 激活疗程调用 -->
  <rect x="70" y="480" width="500" height="60" class="dialog-module"/>
  <text x="320" y="500" text-anchor="middle" class="method-title">treatmentVM.activationTreatment(courseId, phone)</text>
  <text x="80" y="520" class="text">• 获取用户手机号：UserManager.getAccountInfo()?.phone</text>
  <text x="80" y="535" class="text">• 获取疗程ID：treatmentInfo.courseId，调用激活API</text>

  <!-- 第六步：治疗完成检查 -->
  <rect x="650" y="370" width="400" height="120" class="validation-step"/>
  <text x="850" y="395" text-anchor="middle" class="section-title">6. 治疗完成检查</text>
  
  <rect x="670" y="410" width="360" height="70" class="validation-module"/>
  <text x="850" y="430" text-anchor="middle" class="method-title">治疗时长验证</text>
  <text x="680" y="450" class="text">• isFinishUp || plannedDuration <= treatmentDuration</text>
  <text x="680" y="465" class="text">• 已完成：显示"今日治疗已完成"提示</text>
  <text x="680" y="480" class="text">• switchMaskTherapy.isChecked = false</text>

  <!-- 第七步：启动遮盖疗法 -->
  <rect x="1100" y="370" width="600" height="180" class="service-step"/>
  <text x="1400" y="395" text-anchor="middle" class="section-title">7. 启动遮盖疗法</text>
  
  <!-- 启动消息序列 -->
  <rect x="1120" y="410" width="560" height="130" class="service-module"/>
  <text x="1400" y="430" text-anchor="middle" class="method-title">sendMessageToService(启动消息序列)</text>
  <text x="1130" y="450" class="code-text">• MSG_TURN_ON_CAMERA - 开启相机</text>
  <text x="1130" y="465" class="code-text">• MSG_START_TRACK - 开始眼动追踪</text>
  <text x="1130" y="480" class="code-text">• MSG_START_APPLIED_CURE - 开始遮盖疗法</text>
  <text x="1130" y="500" class="text">携带参数：</text>
  <text x="1130" y="515" class="code-text">• KEY_PLANNED_DURATION: plannedDuration</text>
  <text x="1130" y="530" class="code-text">• KEY_TREATMENT_DURATION: treatmentDuration</text>

  <!-- 连接箭头 -->
  <line x1="400" y1="130" x2="450" y2="130" class="arrow"/>
  <line x1="800" y1="130" x2="850" y2="130" class="success-arrow"/>
  <line x1="225" y1="190" x2="225" y2="220" class="arrow"/>
  <line x1="400" y1="280" x2="450" y2="280" class="fail-arrow"/>
  <line x1="225" y1="340" x2="850" y2="220" class="success-arrow"/>
  <line x1="1050" y1="340" x2="325" y2="370" class="condition-arrow"/>
  <line x1="600" y1="460" x2="650" y2="430" class="arrow"/>
  <line x1="1050" y1="460" x2="1100" y2="460" class="success-arrow"/>

  <!-- 详细参数说明 -->
  <rect x="50" y="570" width="1650" height="980" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="875" y="595" text-anchor="middle" class="title" style="font-size: 22px;">switchMaskTherapy 详细工作内容</text>

  <!-- 第一部分：方法概述 -->
  <text x="70" y="630" class="step-title">🎯 方法核心功能</text>
  <text x="90" y="655" class="flow-text">• <tspan style="font-weight: bold;">智能开关控制：</tspan>根据用户操作和系统状态，智能控制遮盖疗法的启动和停止</text>
  <text x="90" y="675" class="flow-text">• <tspan style="font-weight: bold;">多层级验证：</tspan>用户绑定、疗程可用性、疗程状态、治疗完成度等全方位检查</text>
  <text x="90" y="695" class="flow-text">• <tspan style="font-weight: bold;">服务通信：</tspan>通过消息机制与GazeTrackService进行相机、追踪、遮盖的协调控制</text>
  <text x="90" y="715" class="flow-text">• <tspan style="font-weight: bold;">用户引导：</tspan>针对不同错误状态提供相应的对话框引导和解决方案</text>

  <!-- 第二部分：关闭状态处理详解 -->
  <text x="70" y="750" class="step-title">🔴 关闭状态处理 (state = false)</text>

  <text x="90" y="775" class="flow-text" style="font-weight: bold;">1. UI状态重置</text>
  <text x="110" y="795" class="flow-text">• switchMaskTherapy.isChecked = false - 确保开关UI状态正确</text>
  <text x="110" y="810" class="flow-text">• 防止UI状态与实际状态不一致的问题</text>

  <text x="90" y="835" class="flow-text" style="font-weight: bold;">2. 服务停止消息序列</text>
  <text x="110" y="855" class="code-text">sendMessageToService(</text>
  <text x="130" y="870" class="code-text">Message.obtain().apply { what = MSG_STOP_APPLIED_CURE },</text>
  <text x="130" y="885" class="code-text">Message.obtain().apply { what = MSG_STOP_TRACK },</text>
  <text x="130" y="900" class="code-text">Message.obtain().apply { what = MSG_TURN_OFF_CAMERA }</text>
  <text x="110" y="915" class="code-text">)</text>

  <text x="110" y="935" class="flow-text">• <tspan style="font-weight: bold;">MSG_STOP_APPLIED_CURE：</tspan>停止遮盖疗法算法，停止遮盖效果渲染</text>
  <text x="110" y="950" class="flow-text">• <tspan style="font-weight: bold;">MSG_STOP_TRACK：</tspan>停止眼动追踪，释放追踪资源</text>
  <text x="110" y="965" class="flow-text">• <tspan style="font-weight: bold;">MSG_TURN_OFF_CAMERA：</tspan>关闭相机，释放相机资源</text>
  <text x="110" y="980" class="flow-text">• <tspan style="font-weight: bold;">顺序重要：</tspan>先停止上层应用，再停止底层硬件，确保资源正确释放</text>

  <!-- 第三部分：开启状态验证详解 -->
  <text x="70" y="1015" class="step-title">🟢 开启状态验证 (state = true)</text>

  <text x="90" y="1040" class="flow-text" style="font-weight: bold;">1. 用户绑定验证</text>
  <text x="110" y="1060" class="code-text">val isBind = UserManager.isBind()</text>
  <text x="110" y="1075" class="flow-text">• 检查用户是否已完成设备绑定流程</text>
  <text x="110" y="1090" class="flow-text">• 未绑定处理：</text>
  <text x="130" y="1105" class="flow-text">- switchMaskTherapy.isChecked = false (重置开关状态)</text>
  <text x="130" y="1120" class="flow-text">- 显示绑定提示对话框："请先绑定用户再开启遮盖疗法"</text>
  <text x="130" y="1135" class="flow-text">- 确认回调：跳转到绑定页面 (mActivity as? HomeMainActivity)?.toBind()</text>

  <text x="90" y="1160" class="flow-text" style="font-weight: bold;">2. 疗程数据验证</text>
  <text x="110" y="1180" class="code-text">val treatmentInfo = UserManager.getTreatmentInfo()</text>
  <text x="110" y="1195" class="code-text">if (maskVM.occlusionTherapy == null || treatmentInfo == null)</text>
  <text x="110" y="1210" class="flow-text">• <tspan style="font-weight: bold;">maskVM.occlusionTherapy：</tspan>检查今日遮盖疗法配置是否存在</text>
  <text x="110" y="1225" class="flow-text">• <tspan style="font-weight: bold;">treatmentInfo：</tspan>检查用户疗程信息是否有效</text>
  <text x="110" y="1240" class="flow-text">• 无疗程处理：显示"无可用疗程，请联系医生"对话框</text>

  <!-- 第四部分：疗程状态处理详解 -->
  <text x="900" y="750" class="step-title">📋 疗程状态处理</text>

  <text x="920" y="775" class="flow-text" style="font-weight: bold;">1. INACTIVE状态 (未激活)</text>
  <text x="940" y="795" class="code-text">TreatmentStatus.INACTIVE.called</text>
  <text x="940" y="810" class="flow-text">• 显示激活提醒对话框：TreatmentManager.showTreatmentActivationRemindDialog</text>
  <text x="940" y="825" class="flow-text">• 用户确认后显示激活对话框：TreatmentManager.showTreatmentActivationDialog</text>
  <text x="940" y="840" class="flow-text">• 最终调用：treatmentVM.activationTreatment(courseId, phone)</text>

  <text x="920" y="865" class="flow-text" style="font-weight: bold;">2. PENDING状态 (暂停中)</text>
  <text x="940" y="885" class="code-text">TreatmentStatus.PENDING.called</text>
  <text x="940" y="900" class="flow-text">• 显示暂停对话框：TreatmentManager.showTreatmentPauseDialog</text>
  <text x="940" y="915" class="flow-text">• 用户确认后显示激活对话框，流程同INACTIVE状态</text>

  <text x="920" y="940" class="flow-text" style="font-weight: bold;">3. COMPLETED状态 (已完成)</text>
  <text x="940" y="960" class="code-text">TreatmentStatus.COMPLETED.called</text>
  <text x="940" y="975" class="flow-text">• 显示疗程过期对话框：TreatmentManager.showTreatmentExpirationDialog</text>
  <text x="940" y="990" class="flow-text">• 直接返回，无法启动遮盖疗法</text>

  <text x="920" y="1015" class="flow-text" style="font-weight: bold;">4. 对话框管理机制</text>
  <text x="940" y="1035" class="code-text">val dialogTaskManager = (mActivity as? HomeMainActivity)?.mainDialogTaskManager</text>
  <text x="940" y="1050" class="flow-text">• 使用DialogTaskManager进行对话框队列管理</text>
  <text x="940" y="1065" class="flow-text">• 确保对话框按顺序显示，避免重叠和冲突</text>
  <text x="940" y="1080" class="flow-text">• 支持嵌套对话框：提醒对话框 → 激活对话框 → API调用</text>

  <!-- 第五部分：治疗完成检查详解 -->
  <text x="900" y="1115" class="flow-text" style="font-weight: bold;">5. 治疗完成检查</text>
  <text x="920" y="1135" class="code-text">val isFinishUp = maskVM.isFinishUp</text>
  <text x="920" y="1150" class="code-text">val plannedDuration = maskVM.plannedDuration</text>
  <text x="920" y="1165" class="code-text">val treatmentDuration = maskVM.treatmentDuration</text>
  <text x="920" y="1180" class="code-text">if (isFinishUp || plannedDuration <= treatmentDuration)</text>

  <text x="920" y="1200" class="flow-text">• <tspan style="font-weight: bold;">isFinishUp：</tspan>标记是否已完成今日治疗</text>
  <text x="920" y="1215" class="flow-text">• <tspan style="font-weight: bold;">时长对比：</tspan>已治疗时长 >= 计划时长时认为已完成</text>
  <text x="920" y="1230" class="flow-text">• 已完成处理：显示"今日治疗已完成"提示，重置开关状态</text>

  <!-- 第六部分：启动遮盖疗法详解 -->
  <text x="70" y="1275" class="step-title">🚀 启动遮盖疗法</text>

  <text x="90" y="1300" class="flow-text" style="font-weight: bold;">1. 启动消息序列</text>
  <text x="110" y="1320" class="code-text">sendMessageToService(</text>
  <text x="130" y="1335" class="code-text">Message.obtain().apply { what = MSG_TURN_ON_CAMERA },</text>
  <text x="130" y="1350" class="code-text">Message.obtain().apply { what = MSG_START_TRACK },</text>
  <text x="130" y="1365" class="code-text">Message.obtain().apply { what = MSG_START_APPLIED_CURE }</text>
  <text x="110" y="1380" class="code-text">)</text>

  <text x="110" y="1400" class="flow-text">• <tspan style="font-weight: bold;">MSG_TURN_ON_CAMERA：</tspan>启动相机，开始图像采集</text>
  <text x="110" y="1415" class="flow-text">• <tspan style="font-weight: bold;">MSG_START_TRACK：</tspan>启动眼动追踪算法</text>
  <text x="110" y="1430" class="flow-text">• <tspan style="font-weight: bold;">MSG_START_APPLIED_CURE：</tspan>启动遮盖疗法，开始治疗</text>
  <text x="110" y="1445" class="flow-text">• <tspan style="font-weight: bold;">顺序重要：</tspan>先启动底层硬件，再启动上层应用，确保依赖关系正确</text>

  <text x="90" y="1470" class="flow-text" style="font-weight: bold;">2. 治疗参数传递</text>
  <text x="110" y="1490" class="code-text">data.putInt(KEY_PLANNED_DURATION, plannedDuration)</text>
  <text x="110" y="1505" class="code-text">data.putInt(KEY_TREATMENT_DURATION, treatmentDuration)</text>
  <text x="110" y="1520" class="flow-text">• 传递计划治疗时长和已治疗时长给Native层</text>
  <text x="110" y="1535" class="flow-text">• Native层根据这些参数控制治疗流程和进度</text>

  <text x="90" y="1560" class="flow-text" style="font-weight: bold;">3. API配置参数</text>
  <text x="110" y="1580" class="code-text">data.putString(KEY_REPORT_URL, "dt/api/train/v1/occlusion-therapy/event/report")</text>
  <text x="110" y="1595" class="code-text">data.putString(KEY_BASE_URL, UrlConfig.MAIN_DOMAIN)</text>
  <text x="110" y="1610" class="code-text">data.putString(KEY_REPORT_PARAM, "")</text>
  <text x="110" y="1625" class="flow-text">• 配置治疗数据上报的API端点和基础URL</text>
  <text x="110" y="1640" class="flow-text">• Native层将使用这些配置进行治疗结果的实时上报</text>

  <text x="90" y="1665" class="flow-text" style="font-weight: bold;">4. HTTP请求头配置</text>
  <text x="110" y="1685" class="code-text">val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)</text>
  <text x="110" y="1700" class="code-text">data.putString(KEY_REPORT_HEADER, mGson.toJson(HashMap&lt;String, String&gt;().apply {</text>
  <text x="130" y="1715" class="code-text">put("X-Rom-Version", DeviceManager.getOSVersion())</text>
  <text x="130" y="1730" class="code-text">put("X-Device-Sn", DeviceManager.getDeviceSn())</text>
  <text x="130" y="1745" class="code-text">put("X-App-Version", PackageUtils.getVersionName(...))</text>
  <text x="130" y="1760" class="code-text">put("X-Device-Mode", DeviceManager.getProductModel())</text>
  <text x="130" y="1775" class="code-text">put("X-Airdoc-Client", "d409e47e-95e6-41fc-868c-e5748957d546")</text>
  <text x="130" y="1790" class="code-text">put("Accept-Language", "${locale.language}-${locale.country}")</text>
  <text x="110" y="1805" class="code-text">}))</text>

  <text x="110" y="1825" class="flow-text">• <tspan style="font-weight: bold;">X-Rom-Version：</tspan>操作系统版本信息</text>
  <text x="110" y="1840" class="flow-text">• <tspan style="font-weight: bold;">X-Device-Sn：</tspan>设备序列号，用于设备识别</text>
  <text x="110" y="1855" class="flow-text">• <tspan style="font-weight: bold;">X-App-Version：</tspan>应用版本号，用于兼容性处理</text>
  <text x="110" y="1870" class="flow-text">• <tspan style="font-weight: bold;">X-Device-Mode：</tspan>设备型号信息</text>
  <text x="110" y="1885" class="flow-text">• <tspan style="font-weight: bold;">X-Airdoc-Client：</tspan>客户端唯一标识符</text>
  <text x="110" y="1900" class="flow-text">• <tspan style="font-weight: bold;">Accept-Language：</tspan>用户语言偏好，支持国际化</text>

  <!-- 第七部分：错误处理和用户体验 -->
  <text x="900" y="1275" class="step-title">🛡️ 错误处理和用户体验</text>

  <text x="920" y="1300" class="flow-text" style="font-weight: bold;">1. 统一的错误处理模式</text>
  <text x="940" y="1320" class="flow-text">• 每个验证失败都会重置开关状态：switchMaskTherapy.isChecked = false</text>
  <text x="940" y="1335" class="flow-text">• 显示相应的错误提示对话框，引导用户解决问题</text>
  <text x="940" y="1350" class="flow-text">• 提供明确的操作路径：绑定用户、联系医生、激活疗程等</text>

  <text x="920" y="1375" class="flow-text" style="font-weight: bold;">2. 用户引导机制</text>
  <text x="940" y="1395" class="flow-text">• <tspan style="font-weight: bold;">未绑定用户：</tspan>直接跳转到绑定页面</text>
  <text x="940" y="1410" class="flow-text">• <tspan style="font-weight: bold;">无可用疗程：</tspan>提示联系医生获取疗程</text>
  <text x="940" y="1425" class="flow-text">• <tspan style="font-weight: bold;">疗程未激活：</tspan>引导用户激活疗程</text>
  <text x="940" y="1440" class="flow-text">• <tspan style="font-weight: bold;">疗程已过期：</tspan>显示过期信息，无法启动</text>

  <text x="920" y="1465" class="flow-text" style="font-weight: bold;">3. 状态一致性保证</text>
  <text x="940" y="1485" class="flow-text">• UI开关状态与实际服务状态保持一致</text>
  <text x="940" y="1500" class="flow-text">• 任何失败情况都会重置UI状态，避免状态不一致</text>
  <text x="940" y="1515" class="flow-text">• 通过Logger记录操作日志，便于问题排查</text>

  <!-- 第八部分：方法特点总结 -->
  <rect x="70" y="1930" width="1560" height="120" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1955" class="step-title">🌟 switchMaskTherapy 方法特点总结</text>

  <text x="90" y="1980" class="flow-text">• <tspan style="font-weight: bold;">智能状态管理：</tspan>根据用户操作和系统状态，智能控制遮盖疗法的启停，确保操作的安全性和有效性</text>
  <text x="90" y="2000" class="flow-text">• <tspan style="font-weight: bold;">多层级验证体系：</tspan>从用户绑定到疗程状态，从治疗完成度到系统资源，全方位验证确保治疗条件满足</text>
  <text x="90" y="2020" class="flow-text">• <tspan style="font-weight: bold;">完善的错误处理：</tspan>针对每种错误情况提供明确的用户引导，通过对话框和页面跳转帮助用户解决问题</text>
  <text x="90" y="2040" class="flow-text">• <tspan style="font-weight: bold;">精确的服务控制：</tspan>通过消息序列精确控制相机、追踪、遮盖等服务的启停顺序，确保资源正确管理</text>

</svg>
