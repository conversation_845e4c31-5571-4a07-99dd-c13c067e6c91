<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .actor-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .message-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .note-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .step-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #e74c3c; }
      .explanation-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 11px; fill: #2c3e50; }
      
      .actor-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .lifeline { stroke: #1976d2; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #2c3e50; stroke-width: 1; fill: none; stroke-dasharray: 3,3; marker-end: url(#arrowhead); }
      .self-arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .activation-box { fill: #ffecb3; stroke: #ff8f00; stroke-width: 1; }
      .note-box { fill: #fff9c4; stroke: #f57f17; stroke-width: 1; }
      .explanation-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">TreatmentModuleAdapter 时序图 - 治疗模块动态加载流程</text>

  <!-- 参与者 -->
  <!-- Activity/Fragment -->
  <rect x="50" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="110" y="85" text-anchor="middle" class="actor-title">Activity/Fragment</text>
  <line x1="110" y1="100" x2="110" y2="1400" class="lifeline"/>

  <!-- RecyclerView -->
  <rect x="220" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="280" y="85" text-anchor="middle" class="actor-title">RecyclerView</text>
  <line x1="280" y1="100" x2="280" y2="1400" class="lifeline"/>

  <!-- TreatmentModuleAdapter -->
  <rect x="390" y="60" width="160" height="40" class="actor-box" rx="5"/>
  <text x="470" y="85" text-anchor="middle" class="actor-title">TreatmentModuleAdapter</text>
  <line x1="470" y1="100" x2="470" y2="1400" class="lifeline"/>

  <!-- TreatmentModuleHolder -->
  <rect x="600" y="60" width="160" height="40" class="actor-box" rx="5"/>
  <text x="680" y="85" text-anchor="middle" class="actor-title">TreatmentModuleHolder</text>
  <line x1="680" y1="100" x2="680" y2="1400" class="lifeline"/>

  <!-- FrameLayout -->
  <rect x="810" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="870" y="85" text-anchor="middle" class="actor-title">FrameLayout</text>
  <line x1="870" y1="100" x2="870" y2="1400" class="lifeline"/>

  <!-- FragmentManager -->
  <rect x="980" y="60" width="140" height="40" class="actor-box" rx="5"/>
  <text x="1050" y="85" text-anchor="middle" class="actor-title">FragmentManager</text>
  <line x1="1050" y1="100" x2="1050" y2="1400" class="lifeline"/>

  <!-- MaskTherapyFragment -->
  <rect x="1170" y="60" width="160" height="40" class="actor-box" rx="5"/>
  <text x="1250" y="85" text-anchor="middle" class="actor-title">MaskTherapyFragment</text>
  <line x1="1250" y1="100" x2="1250" y2="1400" class="lifeline"/>

  <!-- VisualTrainFragment -->
  <rect x="1380" y="60" width="160" height="40" class="actor-box" rx="5"/>
  <text x="1460" y="85" text-anchor="middle" class="actor-title">VisualTrainFragment</text>
  <line x1="1460" y1="100" x2="1460" y2="1400" class="lifeline"/>

  <!-- 步骤1: 数据设置和初始化 -->
  <text x="50" y="140" class="step-text">步骤1: 数据设置和初始化</text>
  
  <!-- 1.1 setTreatmentModuleData -->
  <rect x="465" y="160" width="10" height="60" class="activation-box"/>
  <line x1="110" y1="170" x2="470" y2="170" class="message-arrow"/>
  <text x="290" y="165" class="message-text">setTreatmentModuleData(data, fragmentManager)</text>

  <!-- 1.2 clear and add data -->
  <line x1="470" y1="190" x2="510" y2="190" class="self-arrow"/>
  <line x1="510" y1="190" x2="510" y2="210" stroke="#2c3e50" stroke-width="2"/>
  <line x1="510" y1="210" x2="470" y2="210" class="message-arrow"/>
  <text x="520" y="185" class="message-text">treatmentModules.clear()</text>
  <text x="520" y="200" class="message-text">treatmentModules.addAll(data)</text>
  <text x="520" y="215" class="message-text">this.fragmentManager = fragmentManager</text>

  <!-- 1.3 set adapter -->
  <line x1="110" y1="240" x2="280" y2="240" class="message-arrow"/>
  <text x="195" y="235" class="message-text">setAdapter(adapter)</text>

  <!-- 步骤2: ViewHolder创建 -->
  <text x="50" y="280" class="step-text">步骤2: ViewHolder创建流程</text>

  <!-- 2.1 onCreateViewHolder -->
  <rect x="465" y="300" width="10" height="80" class="activation-box"/>
  <line x1="280" y1="310" x2="470" y2="310" class="message-arrow"/>
  <text x="375" y="305" class="message-text">onCreateViewHolder(parent, viewType)</text>

  <!-- 2.2 inflate layout -->
  <line x1="470" y1="330" x2="510" y2="330" class="self-arrow"/>
  <line x1="510" y1="330" x2="510" y2="350" stroke="#2c3e50" stroke-width="2"/>
  <line x1="510" y1="350" x2="470" y2="350" class="message-arrow"/>
  <text x="520" y="325" class="message-text">LayoutInflater.inflate()</text>
  <text x="520" y="340" class="message-text">R.layout.item_treatment_module</text>

  <!-- 2.3 create holder -->
  <line x1="470" y1="370" x2="680" y2="370" class="message-arrow"/>
  <text x="575" y="365" class="message-text">new TreatmentModuleHolder(view)</text>

  <!-- 2.4 return holder -->
  <line x1="680" y1="390" x2="470" y2="390" class="return-arrow"/>
  <text x="575" y="385" class="message-text">return holder</text>

  <!-- 步骤3: 数据绑定 -->
  <text x="50" y="430" class="step-text">步骤3: 数据绑定和Fragment加载</text>

  <!-- 3.1 onBindViewHolder -->
  <rect x="465" y="450" width="10" height="40" class="activation-box"/>
  <line x1="280" y1="460" x2="470" y2="460" class="message-arrow"/>
  <text x="375" y="455" class="message-text">onBindViewHolder(holder, position)</text>

  <!-- 3.2 call bind -->
  <line x1="470" y1="480" x2="680" y2="480" class="message-arrow"/>
  <text x="575" y="475" class="message-text">holder.bind(module, position)</text>

  <!-- 3.3 bind method execution -->
  <rect x="675" y="500" width="10" height="200" class="activation-box"/>
  
  <!-- 3.4 set unique id -->
  <line x1="680" y1="510" x2="870" y2="510" class="message-arrow"/>
  <text x="775" y="505" class="message-text">flModuleRoot.id += position</text>

  <!-- 3.5 calculate layout -->
  <line x1="680" y1="530" x2="720" y2="530" class="self-arrow"/>
  <line x1="720" y1="530" x2="720" y2="570" stroke="#2c3e50" stroke-width="2"/>
  <line x1="720" y1="570" x2="680" y2="570" class="message-arrow"/>
  <text x="730" y="525" class="message-text">isFull = treatmentModules.size == 1</text>
  <text x="730" y="540" class="message-text">if (isFull) width = MATCH_PARENT</text>
  <text x="730" y="555" class="message-text">else width = 452.dp2px()</text>
  <text x="730" y="570" class="message-text">flModuleRoot.layoutParams = rootParams</text>

  <!-- 3.6 module type check -->
  <line x1="680" y1="590" x2="720" y2="590" class="self-arrow"/>
  <line x1="720" y1="590" x2="720" y2="610" stroke="#2c3e50" stroke-width="2"/>
  <line x1="720" y1="610" x2="680" y2="610" class="message-arrow"/>
  <text x="730" y="585" class="message-text">when(module.moduleKey)</text>
  <text x="730" y="600" class="message-text">check module type</text>

  <!-- 步骤4: 遮盖疗法Fragment加载 -->
  <text x="50" y="650" class="step-text">步骤4: 遮盖疗法Fragment加载 (OCCLUSION_THERAPY)</text>

  <!-- 4.1 fragment transaction -->
  <line x1="680" y1="670" x2="1050" y2="670" class="message-arrow"/>
  <text x="865" y="665" class="message-text">fragmentManager.beginTransaction()</text>

  <!-- 4.2 create fragment -->
  <rect x="1045" y="690" width="10" height="80" class="activation-box"/>
  <line x1="1050" y1="700" x2="1250" y2="700" class="message-arrow"/>
  <text x="1150" y="695" class="message-text">MaskTherapyFragment.newInstance(moduleName, isFull)</text>

  <!-- 4.3 replace fragment -->
  <line x1="1050" y1="720" x2="1090" y2="720" class="self-arrow"/>
  <line x1="1090" y1="720" x2="1090" y2="750" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1090" y1="750" x2="1050" y2="750" class="message-arrow"/>
  <text x="1100" y="715" class="message-text">beginTransaction.replace()</text>
  <text x="1100" y="730" class="message-text">(flModuleRoot.id, fragment)</text>
  <text x="1100" y="745" class="message-text">commitAllowingStateLoss()</text>

  <!-- 4.4 fragment attached -->
  <line x1="1250" y1="770" x2="870" y2="770" class="message-arrow"/>
  <text x="1060" y="765" class="message-text">fragment attached to FrameLayout</text>

  <!-- 步骤5: 视觉训练Fragment加载 -->
  <text x="50" y="810" class="step-text">步骤5: 视觉训练Fragment加载 (VISION_THERAPY)</text>

  <!-- 5.1 fragment transaction -->
  <line x1="680" y1="830" x2="1050" y2="830" class="message-arrow"/>
  <text x="865" y="825" class="message-text">fragmentManager.beginTransaction()</text>

  <!-- 5.2 create fragment -->
  <rect x="1045" y="850" width="10" height="80" class="activation-box"/>
  <line x1="1050" y1="860" x2="1460" y2="860" class="message-arrow"/>
  <text x="1255" y="855" class="message-text">VisualTrainFragment.newInstance(moduleName, isFull)</text>

  <!-- 5.3 replace fragment -->
  <line x1="1050" y1="880" x2="1090" y2="880" class="self-arrow"/>
  <line x1="1090" y1="880" x2="1090" y2="910" stroke="#2c3e50" stroke-width="2"/>
  <line x1="1090" y1="910" x2="1050" y2="910" class="message-arrow"/>
  <text x="1100" y="875" class="message-text">beginTransaction.replace()</text>
  <text x="1100" y="890" class="message-text">(flModuleRoot.id, fragment)</text>
  <text x="1100" y="905" class="message-text">commitAllowingStateLoss()</text>

  <!-- 5.4 fragment attached -->
  <line x1="1460" y1="930" x2="870" y2="930" class="message-arrow"/>
  <text x="1165" y="925" class="message-text">fragment attached to FrameLayout</text>

  <!-- 详细解释框 -->
  <rect x="50" y="980" width="1700" height="400" class="explanation-box" rx="5"/>
  <text x="70" y="1005" class="step-text">TreatmentModuleAdapter 详细作用解释</text>
  
  <text x="70" y="1030" class="explanation-text" style="font-weight: bold;">核心作用：</text>
  <text x="70" y="1050" class="explanation-text">1. 动态治疗模块管理器 - 根据配置数据动态加载不同的治疗模块Fragment</text>
  <text x="70" y="1070" class="explanation-text">2. 布局自适应控制器 - 根据模块数量自动调整布局（单模块全屏，多模块固定宽度）</text>
  <text x="70" y="1090" class="explanation-text">3. Fragment容器管理器 - 为每个治疗模块提供独立的Fragment容器</text>
  
  <text x="70" y="1120" class="explanation-text" style="font-weight: bold;">技术特点：</text>
  <text x="70" y="1140" class="explanation-text">• Fragment动态加载：通过FragmentManager动态替换Fragment，实现模块化治疗界面</text>
  <text x="70" y="1160" class="explanation-text">• 唯一ID生成：flModuleRoot.id += position 确保每个容器有唯一ID，避免Fragment冲突</text>
  <text x="70" y="1180" class="explanation-text">• 响应式布局：根据模块数量自动调整宽度（1个模块=全屏，多个模块=452dp固定宽度）</text>
  <text x="70" y="1200" class="explanation-text">• 模块类型识别：通过moduleKey区分不同治疗类型（遮盖疗法/视觉训练）</text>
  
  <text x="70" y="1230" class="explanation-text" style="font-weight: bold;">支持的治疗模块：</text>
  <text x="70" y="1250" class="explanation-text">• OCCLUSION_THERAPY（数字遮盖疗法）→ MaskTherapyFragment - 弱视遮盖治疗功能</text>
  <text x="70" y="1270" class="explanation-text">• VISION_THERAPY（视觉训练疗法）→ VisualTrainFragment - 视觉功能训练功能</text>
  
  <text x="70" y="1300" class="explanation-text" style="font-weight: bold;">应用场景：</text>
  <text x="70" y="1320" class="explanation-text">• 医疗主页治疗模块展示 - 为用户提供可选择的治疗方案</text>
  <text x="70" y="1340" class="explanation-text">• 个性化治疗配置 - 根据患者需求动态显示相应的治疗模块</text>
  <text x="70" y="1360" class="explanation-text">• 模块化医疗界面 - 支持治疗功能的插件化扩展和管理</text>

</svg>
