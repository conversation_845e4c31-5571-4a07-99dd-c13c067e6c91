<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .step-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .code-text { font-family: "Consolas", "Monaco", monospace; font-size: 11px; fill: #2c3e50; }
      .explanation-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      .note-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 10px; fill: #666666; }
      
      .process-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .decision-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .action-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .fragment-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .code-box { fill: #f5f5f5; stroke: #757575; stroke-width: 1; }
      .explanation-box { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .decision-arrow { stroke: #f57c00; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .fragment-arrow { stroke: #7b1fa2; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">TreatmentModuleHolder.bind() 方法详细实现流程</text>

  <!-- 步骤1: ID唯一化处理 -->
  <rect x="50" y="60" width="400" height="80" class="process-box" rx="10"/>
  <text x="250" y="85" text-anchor="middle" class="step-title">步骤1: ID唯一化处理</text>
  <text x="70" y="110" class="code-text">flModuleRoot.id += position</text>
  <text x="70" y="125" class="explanation-text">为FrameLayout生成唯一ID，避免Fragment容器冲突</text>

  <!-- 代码解释框1 -->
  <rect x="500" y="60" width="500" height="80" class="explanation-box" rx="5"/>
  <text x="520" y="85" class="highlight-text">ID唯一化原理：</text>
  <text x="520" y="105" class="explanation-text">• 原始ID + position = 唯一ID</text>
  <text x="520" y="120" class="explanation-text">• 例：原ID=2131296256, position=0 → 新ID=2131296256</text>
  <text x="520" y="135" class="explanation-text">• 例：原ID=2131296256, position=1 → 新ID=2131296257</text>

  <!-- 箭头1 -->
  <line x1="250" y1="140" x2="250" y2="180" class="arrow"/>

  <!-- 步骤2: 布局模式判断 -->
  <rect x="50" y="180" width="400" height="100" class="decision-box" rx="10"/>
  <text x="250" y="205" text-anchor="middle" class="step-title">步骤2: 布局模式判断</text>
  <text x="70" y="230" class="code-text">val isFull = treatmentModules.size == 1</text>
  <text x="70" y="245" class="explanation-text">判断是否为全屏模式</text>
  <text x="70" y="260" class="explanation-text">• 单模块 → 全屏显示</text>
  <text x="70" y="275" class="explanation-text">• 多模块 → 固定宽度并排显示</text>

  <!-- 代码解释框2 -->
  <rect x="500" y="180" width="500" height="100" class="explanation-box" rx="5"/>
  <text x="520" y="205" class="highlight-text">布局策略：</text>
  <text x="520" y="225" class="explanation-text">• isFull = true: 用户只选择了一种治疗方案</text>
  <text x="520" y="240" class="explanation-text">  → 全屏显示，充分利用屏幕空间</text>
  <text x="520" y="255" class="explanation-text">• isFull = false: 用户选择了多种治疗方案</text>
  <text x="520" y="270" class="explanation-text">  → 固定宽度452dp，支持横向滚动</text>

  <!-- 箭头2 -->
  <line x1="250" y1="280" x2="250" y2="320" class="arrow"/>

  <!-- 步骤3: 布局参数设置 -->
  <rect x="50" y="320" width="400" height="120" class="action-box" rx="10"/>
  <text x="250" y="345" text-anchor="middle" class="step-title">步骤3: 布局参数设置</text>
  <text x="70" y="370" class="code-text">val rootParams = flModuleRoot.layoutParams as LayoutParams</text>
  <text x="70" y="385" class="code-text">if (isFull) rootParams.width = MATCH_PARENT</text>
  <text x="70" y="400" class="code-text">else rootParams.width = 452.dp2px(context)</text>
  <text x="70" y="415" class="code-text">flModuleRoot.layoutParams = rootParams</text>
  <text x="70" y="430" class="explanation-text">动态调整FrameLayout的宽度</text>

  <!-- 分支：全屏模式 -->
  <rect x="500" y="320" width="200" height="60" class="action-box" rx="5"/>
  <text x="600" y="345" text-anchor="middle" class="step-title">全屏模式</text>
  <text x="520" y="365" class="explanation-text">width = MATCH_PARENT</text>
  <text x="520" y="375" class="note-text">占满父容器宽度</text>

  <!-- 分支：固定宽度模式 -->
  <rect x="750" y="320" width="200" height="60" class="action-box" rx="5"/>
  <text x="850" y="345" text-anchor="middle" class="step-title">固定宽度模式</text>
  <text x="770" y="365" class="explanation-text">width = 452dp</text>
  <text x="770" y="375" class="note-text">固定宽度，支持并排</text>

  <!-- 连接线 -->
  <line x1="250" y1="440" x2="600" y2="380" class="decision-arrow"/>
  <line x1="250" y1="440" x2="850" y2="380" class="decision-arrow"/>
  <line x1="600" y1="380" x2="250" y2="480" class="arrow"/>
  <line x1="850" y1="380" x2="250" y2="480" class="arrow"/>

  <!-- 箭头3 -->
  <line x1="250" y1="440" x2="250" y2="480" class="arrow"/>

  <!-- 步骤4: 模块类型判断 -->
  <rect x="50" y="480" width="400" height="100" class="decision-box" rx="10"/>
  <text x="250" y="505" text-anchor="middle" class="step-title">步骤4: 模块类型判断</text>
  <text x="70" y="530" class="code-text">when(module.moduleKey) {</text>
  <text x="90" y="545" class="code-text">TreatmentModule.OCCLUSION_THERAPY.moduleKey</text>
  <text x="90" y="560" class="code-text">TreatmentModule.VISION_THERAPY.moduleKey</text>
  <text x="70" y="575" class="code-text">}</text>

  <!-- 模块类型说明 -->
  <rect x="500" y="480" width="500" height="100" class="explanation-box" rx="5"/>
  <text x="520" y="505" class="highlight-text">支持的治疗模块：</text>
  <text x="520" y="525" class="explanation-text">• OCCLUSION_THERAPY: 数字遮盖疗法</text>
  <text x="520" y="540" class="explanation-text">  → 用于弱视治疗，遮盖健康眼</text>
  <text x="520" y="555" class="explanation-text">• VISION_THERAPY: 视觉训练疗法</text>
  <text x="520" y="570" class="explanation-text">  → 用于视觉功能训练和康复</text>

  <!-- 箭头4 -->
  <line x1="250" y1="580" x2="250" y2="620" class="arrow"/>

  <!-- 步骤5A: 遮盖疗法Fragment加载 -->
  <rect x="50" y="620" width="400" height="140" class="fragment-box" rx="10"/>
  <text x="250" y="645" text-anchor="middle" class="step-title">步骤5A: 遮盖疗法Fragment加载</text>
  <text x="70" y="670" class="code-text">fragmentManager?.let {</text>
  <text x="90" y="685" class="code-text">val beginTransaction = it.beginTransaction()</text>
  <text x="90" y="700" class="code-text">beginTransaction.replace(flModuleRoot.id,</text>
  <text x="110" y="715" class="code-text">MaskTherapyFragment.newInstance(</text>
  <text x="130" y="730" class="code-text">module.moduleName?:"", isFull))</text>
  <text x="90" y="745" class="code-text">beginTransaction.commitAllowingStateLoss()</text>
  <text x="70" y="760" class="code-text">}</text>

  <!-- 步骤5B: 视觉训练Fragment加载 -->
  <rect x="500" y="620" width="400" height="140" class="fragment-box" rx="10"/>
  <text x="700" y="645" text-anchor="middle" class="step-title">步骤5B: 视觉训练Fragment加载</text>
  <text x="520" y="670" class="code-text">fragmentManager?.let {</text>
  <text x="540" y="685" class="code-text">val beginTransaction = it.beginTransaction()</text>
  <text x="540" y="700" class="code-text">beginTransaction.replace(flModuleRoot.id,</text>
  <text x="560" y="715" class="code-text">VisualTrainFragment.newInstance(</text>
  <text x="580" y="730" class="code-text">module.moduleName?:"", isFull))</text>
  <text x="540" y="745" class="code-text">beginTransaction.commitAllowingStateLoss()</text>
  <text x="520" y="760" class="code-text">}</text>

  <!-- 连接线 -->
  <line x1="150" y1="580" x2="150" y2="620" class="fragment-arrow"/>
  <line x1="350" y1="580" x2="600" y2="620" class="fragment-arrow"/>

  <!-- Fragment详细说明 -->
  <rect x="950" y="620" width="400" height="140" class="explanation-box" rx="5"/>
  <text x="970" y="645" class="highlight-text">Fragment加载机制：</text>
  <text x="970" y="665" class="explanation-text">1. 安全检查：fragmentManager?.let {}</text>
  <text x="970" y="680" class="explanation-text">2. 事务开始：beginTransaction()</text>
  <text x="970" y="695" class="explanation-text">3. Fragment替换：replace(容器ID, Fragment实例)</text>
  <text x="970" y="710" class="explanation-text">4. 参数传递：moduleName, isFull</text>
  <text x="970" y="725" class="explanation-text">5. 提交事务：commitAllowingStateLoss()</text>
  <text x="970" y="740" class="explanation-text">6. 避免状态丢失异常</text>

  <!-- 箭头5 -->
  <line x1="250" y1="760" x2="250" y2="800" class="arrow"/>
  <line x1="700" y1="760" x2="700" y2="800" class="arrow"/>

  <!-- 最终结果 -->
  <rect x="50" y="800" width="850" height="80" class="action-box" rx="10"/>
  <text x="475" y="825" text-anchor="middle" class="step-title">最终结果：Fragment成功加载到FrameLayout容器中</text>
  <text x="70" y="850" class="explanation-text">• FrameLayout具有唯一ID，避免容器冲突</text>
  <text x="70" y="865" class="explanation-text">• 布局参数已设置，支持响应式显示</text>
  <text x="470" y="850" class="explanation-text">• 对应的治疗Fragment已加载并显示</text>
  <text x="470" y="865" class="explanation-text">• Fragment接收到模块名称和布局模式参数</text>

  <!-- 技术细节说明 -->
  <rect x="50" y="920" width="1700" height="300" class="code-box" rx="5"/>
  <text x="70" y="945" class="step-title">技术实现细节分析</text>
  
  <text x="70" y="970" class="highlight-text">1. ID唯一化机制：</text>
  <text x="90" y="990" class="explanation-text">• flModuleRoot.id += position 确保每个RecyclerView item的FrameLayout有唯一ID</text>
  <text x="90" y="1005" class="explanation-text">• 避免Fragment replace时的容器ID冲突问题</text>
  <text x="90" y="1020" class="explanation-text">• 支持RecyclerView的复用机制，每次bind都会重新计算ID</text>
  
  <text x="70" y="1045" class="highlight-text">2. 响应式布局设计：</text>
  <text x="90" y="1065" class="explanation-text">• 单模块时：width = MATCH_PARENT，充分利用屏幕空间，提供沉浸式体验</text>
  <text x="90" y="1080" class="explanation-text">• 多模块时：width = 452dp，固定宽度确保多个模块可以并排显示</text>
  <text x="90" y="1095" class="explanation-text">• 使用dp2px()进行密度适配，确保在不同屏幕密度下显示一致</text>
  
  <text x="70" y="1120" class="highlight-text">3. Fragment动态加载：</text>
  <text x="90" y="1140" class="explanation-text">• 使用FragmentTransaction.replace()替换Fragment，支持动态切换</text>
  <text x="90" y="1155" class="explanation-text">• commitAllowingStateLoss()避免在Activity状态保存后提交事务导致的异常</text>
  <text x="90" y="1170" class="explanation-text">• newInstance()工厂方法传递参数，遵循Fragment最佳实践</text>
  
  <text x="70" y="1195" class="highlight-text">4. 安全性考虑：</text>
  <text x="90" y="1215" class="explanation-text">• fragmentManager?.let {} 空安全检查，避免空指针异常</text>
  <text x="90" y="1230" class="explanation-text">• module.moduleName?:"" 提供默认值，处理空字符串情况</text>
  <text x="90" y="1245" class="explanation-text">• when表达式处理所有已知的模块类型，易于扩展新模块</text>

  <!-- 应用场景 -->
  <rect x="50" y="1260" width="1700" height="120" class="explanation-box" rx="5"/>
  <text x="70" y="1285" class="step-title">应用场景和优势</text>
  
  <text x="70" y="1310" class="explanation-text">• 医疗模块化界面：为不同的治疗方案提供独立的Fragment容器，实现功能隔离</text>
  <text x="70" y="1325" class="explanation-text">• 个性化治疗配置：根据患者需求动态加载相应的治疗模块，提供定制化医疗服务</text>
  <text x="70" y="1340" class="explanation-text">• 可扩展架构：新增治疗模块只需在when语句中添加分支，无需修改现有代码</text>
  <text x="70" y="1355" class="explanation-text">• 用户体验优化：响应式布局确保在不同使用场景下都能提供最佳的显示效果</text>

</svg>
