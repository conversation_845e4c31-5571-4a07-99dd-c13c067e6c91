<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="uiUpdateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="visualStartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="flowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">姿势校准UI更新 & 视标校准开启流程详解</text>
  
  <!-- 核心问题回答 -->
  <rect x="50" y="60" width="1700" height="100" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心回答：</text>
  <text x="70" y="110" class="text">• 姿势校准UI更新：通过 (calibrationView as? PostureCalibrationView)?.setPostureCorrectionResult(it) 实现</text>
  <text x="70" y="130" class="text">• 视标校准开启：通过 showVisualCalibration() + startVisualCalibration() 两步完成</text>
  <text x="70" y="150" class="text">• 这两个操作在同一个观察者中，但处理不同的逻辑分支</text>
  
  <!-- 姿势校准UI更新详解 -->
  <rect x="50" y="180" width="1700" height="450" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="205" text-anchor="middle" class="subtitle">1. 姿势校准UI更新详解</text>
  
  <rect x="80" y="230" width="1600" height="380" rx="8" fill="url(#uiUpdateGradient)"/>
  <text x="880" y="255" text-anchor="middle" class="subtitle">setPostureCorrectionResult(data) 实时更新UI</text>
  
  <!-- 关键代码 -->
  <text x="100" y="280" class="highlight">关键代码位置 (initObserver 第186行):</text>
  <text x="120" y="300" class="code">(calibrationView as? PostureCalibrationView)?.setPostureCorrectionResult(it)</text>
  <text x="120" y="320" class="text">• 这行代码在观察者的最后，无论校准是否完成都会执行</text>
  <text x="120" y="340" class="text">• 实现了实时的UI更新，每次数据变化都会刷新界面</text>
  
  <!-- UI更新的具体内容 -->
  <text x="100" y="370" class="highlight">UI更新的具体内容:</text>
  
  <text x="120" y="395" class="code">1. 头像位置更新:</text>
  <text x="140" y="415" class="code">val centerX = leftPoint.x + (rightPoint.x - leftPoint.x) / 2</text>
  <text x="140" y="435" class="code">val centerY = leftPoint.y + (rightPoint.y - leftPoint.y) / 2</text>
  <text x="140" y="455" class="code">ivRealLocation.translationX = centerX - avatarOriginalW / 2</text>
  <text x="140" y="475" class="code">ivRealLocation.translationY = centerY - avatarOriginalH / 2</text>
  
  <text x="120" y="505" class="code">2. 头像缩放更新:</text>
  <text x="140" y="525" class="code">val scale = 0.5f / data.dist  // 根据距离调整大小</text>
  <text x="140" y="545" class="code">ivRealLocation.scaleX = scale.coerceIn(0.5f, 1.5f)</text>
  
  <text x="120" y="575" class="code">3. 头像颜色和提示更新:</text>
  <text x="140" y="595" class="code">if (data.aligned) {</text>
  <text x="160" y="615" class="code">ivRealLocation.setImageResource(R.drawable.icon_avatar_green)  // 绿色</text>
  <text x="160" y="635" class="code">tvCorrectionPrompt.text = "请保持位置"</text>
  <text x="140" y="655" class="code">} else {</text>
  <text x="160" y="675" class="code">handlePostureException()  // 显示调整提示</text>
  <text x="140" y="695" class="code">}</text>
  
  <text x="900" y="395" class="text">• <tspan class="highlight">实时性:</tspan> 每帧数据都会更新UI</tspan>
  <text x="900" y="415" class="text">• <tspan class="highlight">多维度:</tspan> 位置、大小、颜色、文字</tspan>
  <text x="900" y="435" class="text">• <tspan class="highlight">用户友好:</tspan> 直观的视觉反馈</tspan>
  <text x="900" y="455" class="text">• <tspan class="highlight">响应式:</tspan> 基于LiveData自动更新</tspan>
  
  <!-- 视标校准开启详解 -->
  <rect x="50" y="650" width="1700" height="500" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="675" text-anchor="middle" class="subtitle">2. 视标校准开启详解</text>
  
  <rect x="80" y="700" width="1600" height="430" rx="8" fill="url(#visualStartGradient)"/>
  <text x="880" y="725" text-anchor="middle" class="subtitle">两步开启：界面切换 + 算法启动</text>
  
  <!-- 触发条件 -->
  <text x="100" y="750" class="highlight">触发条件 (initObserver 第162-170行):</text>
  <text x="120" y="770" class="code">if (it.state && !isCLoseCalibration) {  // 姿势校准完成且未手动关闭</text>
  <text x="140" y="790" class="code">when(calibrationMode) {</text>
  <text x="160" y="810" class="code">CalibrationMode.CALIBRATION -> {  // 完整校准模式</text>
  <text x="180" y="830" class="code">showVisualCalibration()    // 步骤1: 切换界面</text>
  <text x="180" y="850" class="code">delay(200)                 // 等待界面切换完成</text>
  <text x="180" y="870" class="code">startVisualCalibration()   // 步骤2: 启动算法</text>
  
  <!-- 步骤1详解 -->
  <text x="100" y="900" class="highlight">步骤1: showVisualCalibration() - 界面切换:</text>
  <text x="120" y="920" class="code">private fun showVisualCalibration() {</text>
  <text x="140" y="940" class="code">flCalibrationRoot.removeAllViews()  // 移除姿势校准界面</text>
  <text x="140" y="960" class="code">flCalibrationRoot.setBackgroundColor("#191443".toColorInt())</text>
  <text x="140" y="980" class="code">calibrationView = VisualCalibrationView(this).apply {</text>
  <text x="160" y="1000" class="code">// 设置视标校准完成回调</text>
  <text x="160" y="1020" class="code">this.onCalibrationComplete = { ... }</text>
  <text x="160" y="1040" class="code">// 设置视标准备就绪回调</text>
  <text x="160" y="1060" class="code">this.onNotifyVisualReady = { ... }</text>
  <text x="140" y="1080" class="code">}</text>
  <text x="140" y="1100" class="code">flCalibrationRoot.addView(calibrationView)  // 添加视标校准界面</text>
  <text x="120" y="1120" class="code">}</text>
  
  <!-- 步骤2详解 -->
  <text x="900" y="900" class="highlight">步骤2: startVisualCalibration() - 算法启动:</text>
  <text x="920" y="920" class="code">private fun startVisualCalibration() {</text>
  <text x="940" y="940" class="code">sendMessageToService(Message.obtain().apply {</text>
  <text x="960" y="960" class="code">what = GazeConstants.MSG_START_VISUAL_CALIBRATION</text>
  <text x="940" y="980" class="code">})</text>
  <text x="920" y="1000" class="code">}</text>
  
  <text x="920" y="1030" class="text">• <tspan class="highlight">消息传递:</tspan> 通过Service消息启动C++算法</text>
  <text x="920" y="1050" class="text">• <tspan class="highlight">算法切换:</tspan> 从姿势检测切换到视标校准</text>
  <text x="920" y="1070" class="text">• <tspan class="highlight">状态管理:</tspan> 服务状态切换到VISUAL_CALIBRATION</text>
  
  <!-- 完整流程图 -->
  <rect x="50" y="1170" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1195" text-anchor="middle" class="subtitle">完整流程时序图</text>
  
  <rect x="80" y="1220" width="1600" height="280" rx="8" fill="url(#flowGradient)"/>
  <text x="880" y="1245" text-anchor="middle" class="subtitle">从姿势校准到视标校准的完整切换过程</text>
  
  <!-- 流程步骤 -->
  <rect x="120" y="1270" width="150" height="60" rx="5" fill="#fff3e0"/>
  <text x="195" y="1295" text-anchor="middle" class="text">姿势校准完成</text>
  <text x="195" y="1315" text-anchor="middle" class="code">state = true</text>
  
  <rect x="300" y="1270" width="150" height="60" rx="5" fill="#e3f2fd"/>
  <text x="375" y="1295" text-anchor="middle" class="text">触发观察者</text>
  <text x="375" y="1315" text-anchor="middle" class="code">LiveData.observe</text>
  
  <rect x="480" y="1270" width="150" height="60" rx="5" fill="#e8f5e8"/>
  <text x="555" y="1295" text-anchor="middle" class="text">判断模式</text>
  <text x="555" y="1315" text-anchor="middle" class="code">CALIBRATION</text>
  
  <rect x="660" y="1270" width="150" height="60" rx="5" fill="#fff3e0"/>
  <text x="735" y="1295" text-anchor="middle" class="text">切换界面</text>
  <text x="735" y="1315" text-anchor="middle" class="code">showVisual...</text>
  
  <rect x="840" y="1270" width="150" height="60" rx="5" fill="#e3f2fd"/>
  <text x="915" y="1295" text-anchor="middle" class="text">启动算法</text>
  <text x="915" y="1315" text-anchor="middle" class="code">startVisual...</text>
  
  <rect x="1020" y="1270" width="150" height="60" rx="5" fill="#e8f5e8"/>
  <text x="1095" y="1295" text-anchor="middle" class="text">视标校准</text>
  <text x="1095" y="1315" text-anchor="middle" class="code">运行中...</text>
  
  <!-- 连接箭头 -->
  <line x1="270" y1="1300" x2="300" y2="1300" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="450" y1="1300" x2="480" y2="1300" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="630" y1="1300" x2="660" y2="1300" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="810" y1="1300" x2="840" y2="1300" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="990" y1="1300" x2="1020" y2="1300" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  
  <!-- 同时进行的UI更新 -->
  <rect x="120" y="1350" width="1050" height="60" rx="5" fill="#ffebee"/>
  <text x="645" y="1375" text-anchor="middle" class="text">同时进行：姿势校准UI持续更新</text>
  <text x="645" y="1395" text-anchor="middle" class="code">(calibrationView as? PostureCalibrationView)?.setPostureCorrectionResult(it)</text>
  
  <!-- 箭头指向UI更新 -->
  <line x1="195" y1="1330" x2="195" y2="1350" stroke="#f44336" stroke-width="2" marker-end="url(#arrowheadRed)"/>
  <line x1="375" y1="1330" x2="375" y2="1350" stroke="#f44336" stroke-width="2" marker-end="url(#arrowheadRed)"/>
  <line x1="555" y1="1330" x2="555" y2="1350" stroke="#f44336" stroke-width="2" marker-end="url(#arrowheadRed)"/>
  
  <!-- 关键说明 -->
  <rect x="80" y="1430" width="1600" height="60" rx="8" fill="#fff9c4"/>
  <text x="880" y="1455" text-anchor="middle" class="highlight">关键理解：</text>
  <text x="100" y="1475" class="text">• UI更新是无条件的，每次数据变化都执行</text>
  <text x="500" y="1475" class="text">• 视标校准开启是有条件的，只在姿势校准完成且为CALIBRATION模式时执行</text>
  <text x="1200" y="1475" class="text">• 两者在同一个观察者中，但逻辑独立</text>
  
  <!-- 总结 -->
  <rect x="100" y="1540" width="1600" height="40" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1565" text-anchor="middle" class="highlight">
    总结：姿势校准UI通过setPostureCorrectionResult实时更新，视标校准通过showVisual+startVisual两步开启
  </text>
</svg>
