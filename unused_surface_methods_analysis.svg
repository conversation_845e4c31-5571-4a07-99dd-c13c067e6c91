<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50; }
      .method-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code-text { font-family: "<PERSON>solas", "Monaco", monospace; font-size: 10px; fill: #2c3e50; }
      .flow-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .highlight-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 12px; fill: #e74c3c; font-weight: bold; }
      
      .unused-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 12; }
      .commented-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 12; }
      .missing-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 12; }
      .alternative-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 12; }
      
      .unused-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      .commented-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      .missing-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      .alternative-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .unused-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 10,5; }
      .missing-arrow { stroke: #9b59b6; stroke-width: 3; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">未使用的Surface方法分析</text>
  
  <!-- 第一层：未使用的setANativeWindow -->
  <rect x="50" y="70" width="400" height="120" class="unused-layer"/>
  <text x="250" y="95" text-anchor="middle" class="section-title">未使用的setANativeWindow</text>
  
  <rect x="70" y="110" width="360" height="70" class="unused-module"/>
  <text x="250" y="130" text-anchor="middle" class="method-title">GazeService::setANativeWindow</text>
  <text x="80" y="150" class="text">• 方法已实现但没有JNI接口</text>
  <text x="80" y="165" class="text">• 没有被任何地方调用</text>

  <!-- 第二层：被注释的draw调用 -->
  <rect x="470" y="70" width="400" height="120" class="commented-layer"/>
  <text x="670" y="95" text-anchor="middle" class="section-title">被注释的draw调用</text>
  
  <rect x="490" y="110" width="360" height="70" class="commented-module"/>
  <text x="670" y="130" text-anchor="middle" class="method-title">tracker_service->draw()</text>
  <text x="500" y="150" class="text">• 所有draw调用都被注释掉</text>
  <text x="500" y="165" class="text">• 实际没有进行Surface渲染</text>

  <!-- 第三层：缺失的JNI接口 -->
  <rect x="890" y="70" width="400" height="120" class="missing-layer"/>
  <text x="1090" y="95" text-anchor="middle" class="section-title">缺失的JNI接口</text>
  
  <rect x="910" y="110" width="360" height="70" class="missing-module"/>
  <text x="1090" y="130" text-anchor="middle" class="method-title">Java_*_setANativeWindow</text>
  <text x="920" y="150" class="text">• 没有对应的JNI接口实现</text>
  <text x="920" y="165" class="text">• Java层无法调用Native方法</text>

  <!-- 第四层：实际渲染方式 -->
  <rect x="1310" y="70" width="400" height="120" class="alternative-layer"/>
  <text x="1510" y="95" text-anchor="middle" class="section-title">实际渲染方式</text>
  
  <rect x="1330" y="110" width="360" height="70" class="alternative-module"/>
  <text x="1510" y="130" text-anchor="middle" class="method-title">GPU直接渲染</text>
  <text x="1340" y="150" class="text">• IPQ_BLR引擎直接渲染</text>
  <text x="1340" y="165" class="text">• 不依赖ANativeWindow</text>

  <!-- 连接箭头 -->
  <line x1="450" y1="130" x2="470" y2="130" class="unused-arrow"/>
  <line x1="870" y1="130" x2="890" y2="130" class="missing-arrow"/>
  <line x1="1290" y1="130" x2="1310" y2="130" class="arrow"/>

  <!-- 详细分析 -->
  <rect x="50" y="220" width="1700" height="1130" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="900" y="245" text-anchor="middle" class="title" style="font-size: 22px;">Surface方法未使用情况详细分析</text>

  <!-- 第一部分：setANativeWindow方法分析 -->
  <text x="70" y="280" class="layer-title">❌ setANativeWindow方法 - 已实现但未使用</text>
  
  <text x="90" y="305" class="flow-text" style="font-weight: bold;">1. 方法实现</text>
  <text x="110" y="325" class="code-text">// GazeService.cpp - 已实现的方法</text>
  <text x="110" y="340" class="code-text">void GazeService::setANativeWindow(ANativeWindow *win) {</text>
  <text x="130" y="355" class="code-text">pthread_mutex_lock(&mutex);</text>
  <text x="130" y="370" class="code-text">// 替换问题</text>
  <text x="130" y="385" class="code-text">ANATIVEWINDOW_RELEASE(this->window);</text>
  <text x="130" y="400" class="code-text">this->window = win;</text>
  <text x="130" y="415" class="code-text">pthread_mutex_unlock(&mutex);</text>
  <text x="110" y="430" class="code-text">}</text>
  
  <text x="90" y="455" class="flow-text" style="font-weight: bold;">2. 头文件声明</text>
  <text x="110" y="475" class="code-text">// GazeService.h - 公共接口声明</text>
  <text x="110" y="490" class="code-text">public:</text>
  <text x="130" y="505" class="code-text">// visual</text>
  <text x="130" y="520" class="code-text">void setANativeWindow(ANativeWindow *window);  //设置surface</text>
  <text x="130" y="535" class="code-text">void draw(const Mat& mat);   //渲染</text>
  
  <text x="90" y="560" class="flow-text" style="font-weight: bold;">3. 缺失的JNI接口</text>
  <text x="110" y="580" class="highlight-text">❌ 在native-lib.cpp中没有找到对应的JNI接口实现！</text>
  <text x="110" y="600" class="code-text">// 应该有但实际没有的JNI接口</text>
  <text x="110" y="615" class="code-text">extern "C" JNIEXPORT void JNICALL</text>
  <text x="110" y="630" class="code-text">Java_com_mitdd_gazetracker_gaze_track_GazeTrack_nativeSetANativeWindow(</text>
  <text x="130" y="645" class="code-text">JNIEnv *env, jclass clazz, jlong thiz, jobject surface) {</text>
  <text x="150" y="660" class="code-text">// 这个接口不存在！</text>
  <text x="130" y="675" class="code-text">}</text>

  <!-- 第二部分：draw方法分析 -->
  <text x="900" y="280" class="layer-title">💬 draw方法 - 被注释掉的调用</text>
  
  <text x="920" y="305" class="flow-text" style="font-weight: bold;">1. 被注释的调用位置</text>
  <text x="940" y="325" class="code-text">// native-lib.cpp - 眼动追踪处理</text>
  <text x="940" y="340" class="code-text">gazetracker_result_str result = tracker_service->process_gazetracker(grayimg);</text>
  <text x="940" y="355" class="code-text">Mat visual_img = tracker_service->visual_tracker_result_func(grayimg);</text>
  <text x="940" y="370" class="code-text">Mat rgba_visual;</text>
  <text x="940" y="385" class="code-text">cvtColor(visual_img, rgba_visual, COLOR_BGR2RGBA);</text>
  <text x="940" y="400" class="highlight-text">//        tracker_service->draw(rgba_visual);  // 🔥 被注释掉！</text>
  
  <text x="920" y="425" class="flow-text" style="font-weight: bold;">2. 其他被注释的位置</text>
  <text x="940" y="445" class="code-text">// 校准过程</text>
  <text x="940" y="460" class="code-text">cvtColor(tracker_service->visual_calibration_draw_func(), rgba_visual, COLOR_BGR2RGBA);</text>
  <text x="940" y="475" class="highlight-text">//            tracker_service->draw(rgba_visual);  // 🔥 被注释掉！</text>
  
  <text x="940" y="495" class="code-text">// 姿势校准过程</text>
  <text x="940" y="510" class="code-text">cvtColor(bgr_visual, rgba_visual, COLOR_BGR2RGBA);</text>
  <text x="940" y="525" class="highlight-text">//        tracker_service->draw(rgba_visual);  // 🔥 被注释掉！</text>
  
  <text x="920" y="550" class="flow-text" style="font-weight: bold;">3. 分析结论</text>
  <text x="940" y="570" class="flow-text">• <tspan style="color: #f39c12;">所有draw调用都被注释：</tspan>说明不使用ANativeWindow渲染</text>
  <text x="940" y="585" class="flow-text">• <tspan style="color: #f39c12;">图像处理完成：</tspan>rgba_visual图像已准备好但不显示</text>
  <text x="940" y="600" class="flow-text">• <tspan style="color: #f39c12;">可能的原因：</tspan>改用其他渲染方式或调试时关闭</text>

  <!-- 第三部分：实际渲染方式分析 -->
  <text x="70" y="720" class="layer-title">✅ 实际渲染方式 - GPU直接渲染</text>
  
  <text x="90" y="745" class="flow-text" style="font-weight: bold;">1. 遮盖疗法的实际渲染</text>
  <text x="110" y="765" class="code-text">// PqBlur.cpp - 实际使用的渲染方式</text>
  <text x="110" y="780" class="code-text">void PqBlur::draw_gaze_result_func(float x, float y, float dist) {</text>
  <text x="130" y="795" class="code-text">if(blur_enable_flag && pq_blr != nullptr) {</text>
  <text x="150" y="810" class="code-text">// 🔥 直接调用GPU引擎，不使用ANativeWindow</text>
  <text x="150" y="825" class="code-text">pq_blr->SetPos(screen_x, screen_y)->Done();</text>
  <text x="130" y="840" class="code-text">}</text>
  <text x="110" y="855" class="code-text">}</text>
  
  <text x="90" y="880" class="flow-text" style="font-weight: bold;">2. IPQ_BLR引擎的渲染机制</text>
  <text x="110" y="900" class="flow-text">• <tspan style="color: #27ae60;">独立渲染：</tspan>IPQ_BLR引擎有自己的渲染管线</text>
  <text x="110" y="915" class="flow-text">• <tspan style="color: #27ae60;">GPU直接操作：</tspan>直接操作GPU进行OpenGL渲染</text>
  <text x="110" y="930" class="flow-text">• <tspan style="color: #27ae60;">不依赖ANativeWindow：</tspan>可能有自己的Surface管理</text>
  <text x="110" y="945" class="flow-text">• <tspan style="color: #27ae60;">高性能：</tspan>避免了额外的Surface拷贝开销</text>
  
  <text x="90" y="970" class="flow-text" style="font-weight: bold;">3. 可能的架构设计</text>
  <text x="110" y="990" class="flow-text">• <tspan style="color: #27ae60;">分离设计：</tspan>眼动追踪和遮盖渲染使用不同的渲染路径</text>
  <text x="110" y="1005" class="flow-text">• <tspan style="color: #27ae60;">专用引擎：</tspan>遮盖疗法使用专门的GPU引擎</text>
  <text x="110" y="1020" class="flow-text">• <tspan style="color: #27ae60;">性能优化：</tspan>避免不必要的Surface操作</text>
  <text x="110" y="1035" class="flow-text">• <tspan style="color: #27ae60;">模块化：</tspan>不同功能使用不同的渲染方式</text>

  <!-- 第四部分：GazeApplication的Surface -->
  <text x="900" y="720" class="layer-title">🔍 GazeApplication的Surface使用</text>
  
  <text x="920" y="745" class="flow-text" style="font-weight: bold;">1. GazeApplication中的Surface</text>
  <text x="940" y="765" class="code-text">// GazeApplication.h</text>
  <text x="940" y="780" class="code-text">class GazeApplication {</text>
  <text x="960" y="795" class="code-text">public:</text>
  <text x="980" y="810" class="code-text">// Surface</text>
  <text x="980" y="825" class="code-text">pthread_mutex_t mutex{}; // 互斥锁</text>
  <text x="980" y="840" class="code-text">ANativeWindow *window = nullptr; // ANativeWindow 用来渲染画面的 == Surface对象</text>
  <text x="940" y="855" class="code-text">};</text>
  
  <text x="920" y="880" class="flow-text" style="font-weight: bold;">2. 关键发现</text>
  <text x="940" y="900" class="flow-text">• <tspan style="color: #9b59b6;">同样没有setANativeWindow：</tspan>GazeApplication也没有设置Surface的方法</text>
  <text x="940" y="915" class="flow-text">• <tspan style="color: #9b59b6;">window始终为nullptr：</tspan>没有被初始化</text>
  <text x="940" y="930" class="flow-text">• <tspan style="color: #9b59b6;">IPQ_BLR独立工作：</tspan>不依赖这个window变量</text>
  <text x="940" y="945" class="flow-text">• <tspan style="color: #9b59b6;">可能是预留接口：</tspan>为将来的功能预留</text>
  
  <text x="920" y="970" class="flow-text" style="font-weight: bold;">3. 实际工作机制</text>
  <text x="940" y="990" class="flow-text">• <tspan style="color: #9b59b6;">GPU引擎自管理：</tspan>IPQ_BLR引擎自己管理渲染目标</text>
  <text x="940" y="1005" class="flow-text">• <tspan style="color: #9b59b6;">可能的内部Surface：</tspan>引擎内部可能有自己的Surface</text>
  <text x="940" y="1020" class="flow-text">• <tspan style="color: #9b59b6;">系统级渲染：</tspan>可能直接渲染到系统帧缓冲</text>
  <text x="940" y="1035" class="flow-text">• <tspan style="color: #9b59b6;">黑盒操作：</tspan>具体实现在IPQ_BLR库内部</text>

  <!-- 第五部分：结论和影响 -->
  <text x="70" y="1080" class="layer-title">📊 结论和影响分析</text>
  
  <text x="90" y="1105" class="flow-text" style="font-weight: bold;">1. 主要发现</text>
  <text x="110" y="1125" class="highlight-text">• setANativeWindow方法完全没有被使用</text>
  <text x="110" y="1140" class="highlight-text">• draw方法的所有调用都被注释掉</text>
  <text x="110" y="1155" class="highlight-text">• 遮盖疗法使用独立的GPU渲染引擎</text>
  <text x="110" y="1170" class="highlight-text">• ANativeWindow变量始终为nullptr</text>
  
  <text x="90" y="1195" class="flow-text" style="font-weight: bold;">2. 对渲染架构的影响</text>
  <text x="110" y="1215" class="flow-text">• <tspan style="color: #e74c3c;">简化架构：</tspan>不需要复杂的Surface管理</text>
  <text x="110" y="1230" class="flow-text">• <tspan style="color: #e74c3c;">性能优化：</tspan>避免不必要的Surface拷贝</text>
  <text x="110" y="1245" class="flow-text">• <tspan style="color: #e74c3c;">模块独立：</tspan>遮盖功能完全独立工作</text>
  <text x="110" y="1260" class="flow-text">• <tspan style="color: #e74c3c;">代码冗余：</tspan>存在未使用的代码</text>

  <!-- 总结 -->
  <rect x="900" y="1080" width="750" height="200" style="fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 8;"/>
  <text x="920" y="1105" class="layer-title">🌟 核心结论</text>
  
  <text x="920" y="1130" class="flow-text">• <tspan style="font-weight: bold; color: #e74c3c;">Surface方法未使用：</tspan>setANativeWindow和draw方法都没有实际使用</text>
  <text x="920" y="1150" class="flow-text">• <tspan style="font-weight: bold; color: #f39c12;">独立渲染架构：</tspan>遮盖疗法使用IPQ_BLR引擎独立渲染</text>
  <text x="920" y="1170" class="flow-text">• <tspan style="font-weight: bold; color: #9b59b6;">黑盒GPU引擎：</tspan>具体渲染实现在第三方库内部</text>
  <text x="920" y="1190" class="flow-text">• <tspan style="font-weight: bold; color: #27ae60;">高效设计：</tspan>避免了复杂的Surface管理，直接GPU渲染</text>
  <text x="920" y="1210" class="flow-text">• <tspan style="font-weight: bold; color: #2c3e50;">代码清理：</tspan>可以考虑移除未使用的Surface相关代码</text>
  <text x="920" y="1230" class="flow-text">• <tspan style="font-weight: bold; color: #8e44ad;">渲染真相：</tspan>遮盖疗法的渲染完全不在C++业务层，而在GPU引擎内部</text>

</svg>
