<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="2800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }
      .text { font-family: Arial, sans-serif; font-size: 12px; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; }
      .small-text { font-family: Arial, sans-serif; font-size: 11px; }
      .activity-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .fragment-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .xml-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .lifecycle-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .module-box { fill: #ffebee; stroke: #d32f2f; stroke-width: 2; }
      .flow-box { fill: #e0f2f1; stroke: #00796b; stroke-width: 2; }
      .example-box { fill: #fce4ec; stroke: #c2185b; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">实例详解：用户个人资料管理模块开发</text>
  <text x="800" y="55" text-anchor="middle" class="subtitle">从需求分析到代码实现的完整流程</text>
  
  <!-- 需求分析 -->
  <g id="requirements">
    <rect x="20" y="80" width="1560" height="120" class="example-box" rx="10"/>
    <text x="800" y="105" text-anchor="middle" class="subtitle">📋 需求分析：用户个人资料管理模块</text>
    
    <text x="40" y="130" class="text" font-weight="bold">功能需求：</text>
    <text x="40" y="150" class="small-text">• 显示用户基本信息（头像、姓名、年龄、性别、联系方式）</text>
    <text x="40" y="170" class="small-text">• 支持编辑个人信息</text>
    <text x="40" y="190" class="small-text">• 头像上传和更换功能</text>
    
    <text x="500" y="130" class="text" font-weight="bold">界面设计：</text>
    <text x="500" y="150" class="small-text">• 主界面：UserProfileActivity（容器）</text>
    <text x="500" y="170" class="small-text">• 信息展示：ProfileDisplayFragment</text>
    <text x="500" y="190" class="small-text">• 编辑界面：ProfileEditFragment</text>
    
    <text x="1000" y="130" class="text" font-weight="bold">技术方案：</text>
    <text x="1000" y="150" class="small-text">• MVVM架构 + Repository模式</text>
    <text x="1000" y="170" class="small-text">• ViewBinding进行视图绑定</text>
    <text x="1000" y="190" class="small-text">• Retrofit处理网络请求</text>
  </g>
  
  <!-- 第一步：创建Activity -->
  <g id="step1-activity">
    <rect x="20" y="220" width="760" height="300" class="activity-box" rx="10"/>
    <text x="400" y="245" text-anchor="middle" class="subtitle">第1步：创建UserProfileActivity</text>
    
    <!-- Activity作用说明 -->
    <rect x="40" y="260" width="340" height="120" class="lifecycle-box" rx="8"/>
    <text x="210" y="280" text-anchor="middle" class="text" font-weight="bold">Activity的作用</text>
    <text x="50" y="300" class="small-text">🎯 界面容器：管理整个用户资料页面</text>
    <text x="50" y="320" class="small-text">🔄 生命周期：处理页面的创建、暂停、销毁</text>
    <text x="50" y="340" class="small-text">🧩 Fragment管理：协调显示和编辑Fragment</text>
    <text x="50" y="360" class="small-text">📱 系统交互：处理返回键、配置变化等</text>
    
    <!-- Activity代码实现 -->
    <rect x="400" y="260" width="360" height="250" class="activity-box" rx="8"/>
    <text x="580" y="280" text-anchor="middle" class="text" font-weight="bold">UserProfileActivity.kt</text>
    <text x="410" y="300" class="code">class UserProfileActivity : GTBaseActivity() {</text>
    <text x="410" y="315" class="code">    private lateinit var binding: ActivityUserProfileBinding</text>
    <text x="410" y="330" class="code">    private var isEditMode = false</text>
    <text x="410" y="345" class="code">    </text>
    <text x="410" y="360" class="code">    companion object {</text>
    <text x="410" y="375" class="code">        const val EXTRA_USER_ID = "user_id"</text>
    <text x="410" y="390" class="code">        </text>
    <text x="410" y="405" class="code">        fun createIntent(context: Context, userId: String): Intent {</text>
    <text x="410" y="420" class="code">            return Intent(context, UserProfileActivity::class.java).apply {</text>
    <text x="410" y="435" class="code">                putExtra(EXTRA_USER_ID, userId)</text>
    <text x="410" y="450" class="code">            }</text>
    <text x="410" y="465" class="code">        }</text>
    <text x="410" y="480" class="code">    }</text>
    <text x="410" y="495" class="code">}</text>
    
    <!-- Activity生命周期 -->
    <rect x="40" y="390" width="340" height="120" class="lifecycle-box" rx="8"/>
    <text x="210" y="410" text-anchor="middle" class="text" font-weight="bold">生命周期实现</text>
    <text x="50" y="430" class="code">override fun onCreate(savedInstanceState: Bundle?) {</text>
    <text x="50" y="445" class="code">    super.onCreate(savedInstanceState)</text>
    <text x="50" y="460" class="code">    binding = ActivityUserProfileBinding.inflate(layoutInflater)</text>
    <text x="50" y="475" class="code">    setContentView(binding.root)</text>
    <text x="50" y="490" class="code">    initView()</text>
    <text x="50" y="505" class="code">}</text>
  </g>
  
  <!-- 第二步：设计XML布局 -->
  <g id="step2-xml">
    <rect x="800" y="220" width="780" height="300" class="xml-box" rx="10"/>
    <text x="1190" y="245" text-anchor="middle" class="subtitle">第2步：设计XML布局文件</text>
    
    <!-- XML作用说明 -->
    <rect x="820" y="260" width="360" height="120" class="xml-box" rx="8"/>
    <text x="1000" y="280" text-anchor="middle" class="text" font-weight="bold">XML的作用</text>
    <text x="830" y="300" class="small-text">📐 布局结构：定义界面的层次结构</text>
    <text x="830" y="320" class="small-text">🎨 样式属性：设置颜色、尺寸、字体等</text>
    <text x="830" y="340" class="small-text">📱 响应式设计：适配不同屏幕尺寸</text>
    <text x="830" y="360" class="small-text">🔧 资源引用：使用统一的资源管理</text>
    
    <!-- XML代码实现 -->
    <rect x="1200" y="260" width="360" height="250" class="xml-box" rx="8"/>
    <text x="1380" y="280" text-anchor="middle" class="text" font-weight="bold">activity_user_profile.xml</text>
    <text x="1210" y="300" class="code">&lt;?xml version="1.0" encoding="utf-8"?&gt;</text>
    <text x="1210" y="315" class="code">&lt;LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"</text>
    <text x="1210" y="330" class="code">    android:layout_width="match_parent"</text>
    <text x="1210" y="345" class="code">    android:layout_height="match_parent"</text>
    <text x="1210" y="360" class="code">    android:orientation="vertical"&gt;</text>
    <text x="1210" y="375" class="code">    </text>
    <text x="1210" y="390" class="code">    &lt;!-- 顶部工具栏 --&gt;</text>
    <text x="1210" y="405" class="code">    &lt;include layout="@layout/toolbar_user_profile" /&gt;</text>
    <text x="1210" y="420" class="code">    </text>
    <text x="1210" y="435" class="code">    &lt;!-- Fragment容器 --&gt;</text>
    <text x="1210" y="450" class="code">    &lt;FrameLayout</text>
    <text x="1210" y="465" class="code">        android:id="@+id/fragment_container"</text>
    <text x="1210" y="480" class="code">        android:layout_width="match_parent"</text>
    <text x="1210" y="495" class="code">        android:layout_height="0dp"</text>
    <text x="1210" y="510" class="code">        android:layout_weight="1" /&gt;</text>
    
    <!-- 资源文件 -->
    <rect x="820" y="390" width="360" height="120" class="xml-box" rx="8"/>
    <text x="1000" y="410" text-anchor="middle" class="text" font-weight="bold">相关资源文件</text>
    <text x="830" y="430" class="code">res/values/colors.xml - 颜色定义</text>
    <text x="830" y="445" class="code">res/values/dimens.xml - 尺寸定义</text>
    <text x="830" y="460" class="code">res/values/strings.xml - 文本资源</text>
    <text x="830" y="475" class="code">res/drawable/ - 图标和背景</text>
    <text x="830" y="490" class="code">res/layout/toolbar_user_profile.xml - 工具栏</text>
    <text x="830" y="505" class="code">res/layout/fragment_profile_display.xml - Fragment布局</text>
  </g>
  
  <!-- 第三步：创建Fragment -->
  <g id="step3-fragment">
    <rect x="20" y="540" width="1560" height="280" class="fragment-box" rx="10"/>
    <text x="800" y="565" text-anchor="middle" class="subtitle">第3步：创建Fragment组件</text>
    
    <!-- 显示Fragment -->
    <rect x="40" y="580" width="480" height="230" class="fragment-box" rx="8"/>
    <text x="280" y="600" text-anchor="middle" class="text" font-weight="bold">ProfileDisplayFragment.kt - 信息显示</text>
    <text x="50" y="620" class="code">class ProfileDisplayFragment : BaseCommonFragment() {</text>
    <text x="50" y="635" class="code">    private var _binding: FragmentProfileDisplayBinding? = null</text>
    <text x="50" y="650" class="code">    private val binding get() = _binding!!</text>
    <text x="50" y="665" class="code">    private lateinit var viewModel: UserProfileViewModel</text>
    <text x="50" y="680" class="code">    </text>
    <text x="50" y="695" class="code">    companion object {</text>
    <text x="50" y="710" class="code">        fun newInstance(userId: String): ProfileDisplayFragment {</text>
    <text x="50" y="725" class="code">            return ProfileDisplayFragment().apply {</text>
    <text x="50" y="740" class="code">                arguments = Bundle().apply {</text>
    <text x="50" y="755" class="code">                    putString("userId", userId)</text>
    <text x="50" y="770" class="code">                }</text>
    <text x="50" y="785" class="code">            }</text>
    <text x="50" y="800" class="code">        }</text>
    
    <!-- 编辑Fragment -->
    <rect x="540" y="580" width="480" height="230" class="fragment-box" rx="8"/>
    <text x="780" y="600" text-anchor="middle" class="text" font-weight="bold">ProfileEditFragment.kt - 信息编辑</text>
    <text x="550" y="620" class="code">class ProfileEditFragment : BaseCommonFragment() {</text>
    <text x="550" y="635" class="code">    private var _binding: FragmentProfileEditBinding? = null</text>
    <text x="550" y="650" class="code">    private val binding get() = _binding!!</text>
    <text x="550" y="665" class="code">    private lateinit var viewModel: UserProfileViewModel</text>
    <text x="550" y="680" class="code">    </text>
    <text x="550" y="695" class="code">    override fun getLayoutResId(): Int {</text>
    <text x="550" y="710" class="code">        return R.layout.fragment_profile_edit</text>
    <text x="550" y="725" class="code">    }</text>
    <text x="550" y="740" class="code">    </text>
    <text x="550" y="755" class="code">    override fun initView() {</text>
    <text x="550" y="770" class="code">        super.initView()</text>
    <text x="550" y="785" class="code">        setupEditListeners()</text>
    <text x="550" y="800" class="code">    }</text>
    
    <!-- Fragment作用说明 -->
    <rect x="1040" y="580" width="520" height="230" class="lifecycle-box" rx="8"/>
    <text x="1300" y="600" text-anchor="middle" class="text" font-weight="bold">Fragment的作用与优势</text>
    <text x="1050" y="620" class="small-text">🧩 模块化设计：</text>
    <text x="1050" y="635" class="small-text">  • 显示Fragment专注于数据展示</text>
    <text x="1050" y="650" class="small-text">  • 编辑Fragment专注于数据修改</text>
    <text x="1050" y="665" class="small-text">  • 可在不同Activity中重用</text>
    <text x="1050" y="680" class="small-text">🔄 动态切换：</text>
    <text x="1050" y="695" class="small-text">  • 运行时在显示和编辑模式间切换</text>
    <text x="1050" y="710" class="small-text">  • 无需重新创建Activity</text>
    <text x="1050" y="725" class="small-text">📱 适配性强：</text>
    <text x="1050" y="740" class="small-text">  • 手机：单Fragment显示</text>
    <text x="1050" y="755" class="small-text">  • 平板：可同时显示多个Fragment</text>
    <text x="1050" y="770" class="small-text">🎯 独立生命周期：</text>
    <text x="1050" y="785" class="small-text">  • 独立管理自己的状态和数据</text>
    <text x="1050" y="800" class="small-text">  • 可独立进行网络请求和数据处理</text>
  </g>

  <!-- 第四步：Fragment布局设计 -->
  <g id="step4-fragment-layouts">
    <rect x="20" y="840" width="1560" height="280" class="xml-box" rx="10"/>
    <text x="800" y="865" text-anchor="middle" class="subtitle">第4步：Fragment布局文件设计</text>

    <!-- 显示Fragment布局 -->
    <rect x="40" y="880" width="480" height="230" class="xml-box" rx="8"/>
    <text x="280" y="900" text-anchor="middle" class="text" font-weight="bold">fragment_profile_display.xml</text>
    <text x="50" y="920" class="code">&lt;ScrollView xmlns:android="http://schemas.android.com/apk/res/android"</text>
    <text x="50" y="935" class="code">    android:layout_width="match_parent"</text>
    <text x="50" y="950" class="code">    android:layout_height="match_parent"&gt;</text>
    <text x="50" y="965" class="code">    </text>
    <text x="50" y="980" class="code">    &lt;LinearLayout</text>
    <text x="50" y="995" class="code">        android:layout_width="match_parent"</text>
    <text x="50" y="1010" class="code">        android:layout_height="wrap_content"</text>
    <text x="50" y="1025" class="code">        android:orientation="vertical"</text>
    <text x="50" y="1040" class="code">        android:padding="16dp"&gt;</text>
    <text x="50" y="1055" class="code">        </text>
    <text x="50" y="1070" class="code">        &lt;!-- 头像区域 --&gt;</text>
    <text x="50" y="1085" class="code">        &lt;ImageView</text>
    <text x="50" y="1100" class="code">            android:id="@+id/iv_avatar"
    <text x="50" y="1115" class="code">            android:layout_width="80dp"</text>
    <text x="50" y="1130" class="code">            android:layout_height="80dp"</text>
    <text x="50" y="1145" class="code">            android:layout_gravity="center_horizontal"</text>
    <text x="50" y="1160" class="code">            android:layout_marginBottom="16dp"</text>
    <text x="50" y="1175" class="code">            android:src="@drawable/ic_default_avatar" /&gt;</text>
    <text x="50" y="1190" class="code">        </text>
    <text x="50" y="1205" class="code">        &lt;!-- 用户姓名 --&gt;</text>
    <text x="50" y="1220" class="code">        &lt;TextView</text>
    <text x="50" y="1235" class="code">            android:id="@+id/tv_name"</text>
    <text x="50" y="1250" class="code">            android:layout_width="match_parent"</text>
    <text x="50" y="1265" class="code">            android:layout_height="wrap_content" /&gt;</text></text>

    <!-- 编辑Fragment布局 -->
    <rect x="540" y="880" width="480" height="230" class="xml-box" rx="8"/>
    <text x="780" y="900" text-anchor="middle" class="text" font-weight="bold">fragment_profile_edit.xml</text>
    <text x="550" y="920" class="code">&lt;ScrollView xmlns:android="http://schemas.android.com/apk/res/android"</text>
    <text x="550" y="935" class="code">    android:layout_width="match_parent"</text>
    <text x="550" y="950" class="code">    android:layout_height="match_parent"&gt;</text>
    <text x="550" y="965" class="code">    </text>
    <text x="550" y="980" class="code">    &lt;LinearLayout</text>
    <text x="550" y="995" class="code">        android:layout_width="match_parent"</text>
    <text x="550" y="1010" class="code">        android:layout_height="wrap_content"</text>
    <text x="550" y="1025" class="code">        android:orientation="vertical"</text>
    <text x="550" y="1040" class="code">        android:padding="16dp"&gt;</text>
    <text x="550" y="1055" class="code">        </text>
    <text x="550" y="1070" class="code">        &lt;!-- 头像编辑区域 --&gt;</text>
    <text x="550" y="1085" class="code">        &lt;RelativeLayout</text>
    <text x="550" y="1100" class="code">            android:layout_width="wrap_content"
    <text x="550" y="1115" class="code">            android:layout_height="wrap_content"</text>
    <text x="550" y="1130" class="code">            android:layout_centerHorizontal="true"&gt;</text>
    <text x="550" y="1145" class="code">            </text>
    <text x="550" y="1160" class="code">            &lt;ImageView</text>
    <text x="550" y="1175" class="code">                android:id="@+id/iv_avatar_edit"</text>
    <text x="550" y="1190" class="code">                android:layout_width="100dp"</text>
    <text x="550" y="1205" class="code">                android:layout_height="100dp"</text>
    <text x="550" y="1220" class="code">                android:src="@drawable/ic_default_avatar" /&gt;</text>
    <text x="550" y="1235" class="code">            </text>
    <text x="550" y="1250" class="code">            &lt;ImageButton</text>
    <text x="550" y="1265" class="code">                android:id="@+id/btn_change_avatar"</text>
    <text x="550" y="1280" class="code">                android:layout_width="32dp"</text></text>

    <!-- 布局设计原则 -->
    <rect x="1040" y="880" width="520" height="230" class="lifecycle-box" rx="8"/>
    <text x="1300" y="900" text-anchor="middle" class="text" font-weight="bold">布局设计原则与技巧</text>
    <text x="1050" y="920" class="small-text">📐 布局选择：</text>
    <text x="1050" y="935" class="small-text">  • ScrollView：支持内容滚动</text>
    <text x="1050" y="950" class="small-text">  • LinearLayout：垂直排列组件</text>
    <text x="1050" y="965" class="small-text">  • RelativeLayout：相对定位</text>
    <text x="1050" y="980" class="small-text">🎨 样式统一：</text>
    <text x="1050" y="995" class="small-text">  • 使用@dimen/定义统一间距</text>
    <text x="1050" y="1010" class="small-text">  • 使用@color/定义统一颜色</text>
    <text x="1050" y="1025" class="small-text">  • 使用@style/定义统一样式</text>
    <text x="1050" y="1040" class="small-text">📱 响应式设计：</text>
    <text x="1050" y="1055" class="small-text">  • 使用dp单位适配屏幕密度</text>
    <text x="1050" y="1070" class="small-text">  • 使用weight属性分配空间</text>
    <text x="1050" y="1085" class="small-text">  • 考虑横竖屏切换</text>
    <text x="1050" y="1100" class="small-text">🔧 性能优化：</text>
    <text x="1050" y="1115" class="small-text">  • 减少布局嵌套层级</text>
  </g>

  <!-- 第五步：业务逻辑实现 -->
  <g id="step5-business-logic">
    <rect x="20" y="1140" width="1560" height="320" class="flow-box" rx="10"/>
    <text x="800" y="1165" text-anchor="middle" class="subtitle">第5步：业务逻辑实现</text>

    <!-- ViewModel实现 -->
    <rect x="40" y="1180" width="480" height="270" class="flow-box" rx="8"/>
    <text x="280" y="1200" text-anchor="middle" class="text" font-weight="bold">UserProfileViewModel.kt</text>
    <text x="50" y="1220" class="code">class UserProfileViewModel(</text>
    <text x="50" y="1235" class="code">    private val repository: UserRepository</text>
    <text x="50" y="1250" class="code">) : ViewModel() {</text>
    <text x="50" y="1265" class="code">    </text>
    <text x="50" y="1280" class="code">    private val _userProfile = MutableLiveData&lt;UserProfile&gt;()</text>
    <text x="50" y="1295" class="code">    val userProfile: LiveData&lt;UserProfile&gt; = _userProfile</text>
    <text x="50" y="1310" class="code">    </text>
    <text x="50" y="1325" class="code">    private val _isLoading = MutableLiveData&lt;Boolean&gt;()</text>
    <text x="50" y="1340" class="code">    val isLoading: LiveData&lt;Boolean&gt; = _isLoading</text>
    <text x="50" y="1355" class="code">    </text>
    <text x="50" y="1370" class="code">    fun loadUserProfile(userId: String) {</text>
    <text x="50" y="1385" class="code">        viewModelScope.launch {</text>
    <text x="50" y="1400" class="code">            _isLoading.value = true</text>
    <text x="50" y="1415" class="code">            try {</text>
    <text x="50" y="1430" class="code">                val profile = repository.getUserProfile(userId)</text>
    <text x="50" y="1445" class="code">                _userProfile.value = profile
    <text x="50" y="1460" class="code">            } catch (e: Exception) {</text>
    <text x="50" y="1475" class="code">                _errorMessage.value = e.message</text>
    <text x="50" y="1490" class="code">            } finally {</text>
    <text x="50" y="1505" class="code">                _isLoading.value = false</text>
    <text x="50" y="1520" class="code">            }</text>
    <text x="50" y="1535" class="code">        }</text>
    <text x="50" y="1550" class="code">    }</text></text>

    <!-- Repository实现 -->
    <rect x="540" y="1180" width="480" height="270" class="module-box" rx="8"/>
    <text x="780" y="1200" text-anchor="middle" class="text" font-weight="bold">UserRepository.kt</text>
    <text x="550" y="1220" class="code">class UserRepository(</text>
    <text x="550" y="1235" class="code">    private val apiService: UserApiService,</text>
    <text x="550" y="1250" class="code">    private val userDao: UserDao</text>
    <text x="550" y="1265" class="code">) {</text>
    <text x="550" y="1280" class="code">    </text>
    <text x="550" y="1295" class="code">    suspend fun getUserProfile(userId: String): UserProfile {</text>
    <text x="550" y="1310" class="code">        return try {</text>
    <text x="550" y="1325" class="code">            // 先尝试从网络获取最新数据</text>
    <text x="550" y="1340" class="code">            val response = apiService.getUserProfile(userId)</text>
    <text x="550" y="1355" class="code">            if (response.isSuccessful) {</text>
    <text x="550" y="1370" class="code">                val profile = response.body()!!</text>
    <text x="550" y="1385" class="code">                // 缓存到本地数据库</text>
    <text x="550" y="1400" class="code">                userDao.insertUser(profile)</text>
    <text x="550" y="1415" class="code">                profile</text>
    <text x="550" y="1430" class="code">            } else {</text>
    <text x="550" y="1445" class="code">                // 网络失败时从本地获取
    <text x="550" y="1460" class="code">                userDao.getUserProfile(userId)</text>
    <text x="550" y="1475" class="code">            }</text>
    <text x="550" y="1490" class="code">        } catch (e: Exception) {</text>
    <text x="550" y="1505" class="code">            // 异常时从本地获取</text>
    <text x="550" y="1520" class="code">            userDao.getUserProfile(userId)</text>
    <text x="550" y="1535" class="code">        } ?: throw Exception("用户不存在")</text>
    <text x="550" y="1550" class="code">    }</text></text>

    <!-- API接口定义 -->
    <rect x="1040" y="1180" width="520" height="270" class="module-box" rx="8"/>
    <text x="1300" y="1200" text-anchor="middle" class="text" font-weight="bold">UserApiService.kt</text>
    <text x="1050" y="1220" class="code">interface UserApiService {</text>
    <text x="1050" y="1235" class="code">    </text>
    <text x="1050" y="1250" class="code">    @GET("users/{userId}/profile")</text>
    <text x="1050" y="1265" class="code">    suspend fun getUserProfile(</text>
    <text x="1050" y="1280" class="code">        @Path("userId") userId: String</text>
    <text x="1050" y="1295" class="code">    ): Response&lt;UserProfile&gt;</text>
    <text x="1050" y="1310" class="code">    </text>
    <text x="1050" y="1325" class="code">    @PUT("users/{userId}/profile")</text>
    <text x="1050" y="1340" class="code">    suspend fun updateUserProfile(</text>
    <text x="1050" y="1355" class="code">        @Path("userId") userId: String,</text>
    <text x="1050" y="1370" class="code">        @Body profile: UserProfile</text>
    <text x="1050" y="1385" class="code">    ): Response&lt;UserProfile&gt;</text>
    <text x="1050" y="1400" class="code">    </text>
    <text x="1050" y="1415" class="code">    @Multipart</text>
    <text x="1050" y="1430" class="code">    @POST("users/{userId}/avatar")</text>
    <text x="1050" y="1445" class="code">    suspend fun uploadAvatar(
    <text x="1050" y="1460" class="code">        @Path("userId") userId: String,</text>
    <text x="1050" y="1475" class="code">        @Part avatar: MultipartBody.Part</text>
    <text x="1050" y="1490" class="code">    ): Response&lt;AvatarUploadResponse&gt;</text>
    <text x="1050" y="1505" class="code">    </text>
    <text x="1050" y="1520" class="code">    @DELETE("users/{userId}")</text>
    <text x="1050" y="1535" class="code">    suspend fun deleteUser(</text>
    <text x="1050" y="1550" class="code">        @Path("userId") userId: String</text>
    <text x="1050" y="1565" class="code">    ): Response&lt;Unit&gt;</text>
    <text x="1050" y="1580" class="code">}</text></text>
  </g>

  <!-- 第六步：数据模型和数据库 -->
  <g id="step6-data-models">
    <rect x="20" y="1480" width="1560" height="280" class="lifecycle-box" rx="10"/>
    <text x="800" y="1505" text-anchor="middle" class="subtitle">第6步：数据模型与数据库设计</text>

    <!-- 数据模型 -->
    <rect x="40" y="1520" width="480" height="230" class="lifecycle-box" rx="8"/>
    <text x="280" y="1540" text-anchor="middle" class="text" font-weight="bold">UserProfile.kt - 数据模型</text>
    <text x="50" y="1560" class="code">@Entity(tableName = "user_profiles")</text>
    <text x="50" y="1575" class="code">data class UserProfile(</text>
    <text x="50" y="1590" class="code">    @PrimaryKey</text>
    <text x="50" y="1605" class="code">    val userId: String,</text>
    <text x="50" y="1620" class="code">    </text>
    <text x="50" y="1635" class="code">    val name: String,</text>
    <text x="50" y="1650" class="code">    val email: String,</text>
    <text x="50" y="1665" class="code">    val phone: String?,</text>
    <text x="50" y="1680" class="code">    val age: Int,</text>
    <text x="50" y="1695" class="code">    val gender: String,</text>
    <text x="50" y="1710" class="code">    val avatarUrl: String?,</text>
    <text x="50" y="1725" class="code">    val bio: String?,</text>
    <text x="50" y="1740" class="code">    val updatedAt: Long = System.currentTimeMillis()
    <text x="50" y="1755" class="code">)</text></text>

    <!-- DAO接口 -->
    <rect x="540" y="1520" width="480" height="230" class="lifecycle-box" rx="8"/>
    <text x="780" y="1540" text-anchor="middle" class="text" font-weight="bold">UserDao.kt - 数据访问对象</text>
    <text x="550" y="1560" class="code">@Dao</text>
    <text x="550" y="1575" class="code">interface UserDao {</text>
    <text x="550" y="1590" class="code">    </text>
    <text x="550" y="1605" class="code">    @Query("SELECT * FROM user_profiles WHERE userId = :userId")</text>
    <text x="550" y="1620" class="code">    suspend fun getUserProfile(userId: String): UserProfile?</text>
    <text x="550" y="1635" class="code">    </text>
    <text x="550" y="1650" class="code">    @Insert(onConflict = OnConflictStrategy.REPLACE)</text>
    <text x="550" y="1665" class="code">    suspend fun insertUser(user: UserProfile)</text>
    <text x="550" y="1680" class="code">    </text>
    <text x="550" y="1695" class="code">    @Update</text>
    <text x="550" y="1710" class="code">    suspend fun updateUser(user: UserProfile)</text>
    <text x="550" y="1725" class="code">    </text>
    <text x="550" y="1740" class="code">    @Delete
    <text x="550" y="1755" class="code">    suspend fun deleteUser(user: UserProfile)</text>
    <text x="550" y="1770" class="code">}</text></text>

    <!-- 数据库配置 -->
    <rect x="1040" y="1520" width="520" height="230" class="lifecycle-box" rx="8"/>
    <text x="1300" y="1540" text-anchor="middle" class="text" font-weight="bold">AppDatabase.kt - 数据库配置</text>
    <text x="1050" y="1560" class="code">@Database(</text>
    <text x="1050" y="1575" class="code">    entities = [UserProfile::class],</text>
    <text x="1050" y="1590" class="code">    version = 1,</text>
    <text x="1050" y="1605" class="code">    exportSchema = false</text>
    <text x="1050" y="1620" class="code">)</text>
    <text x="1050" y="1635" class="code">@TypeConverters(Converters::class)</text>
    <text x="1050" y="1650" class="code">abstract class AppDatabase : RoomDatabase() {</text>
    <text x="1050" y="1665" class="code">    </text>
    <text x="1050" y="1680" class="code">    abstract fun userDao(): UserDao</text>
    <text x="1050" y="1695" class="code">    </text>
    <text x="1050" y="1710" class="code">    companion object {</text>
    <text x="1050" y="1725" class="code">        @Volatile</text>
    <text x="1050" y="1740" class="code">        private var INSTANCE: AppDatabase? = null
    <text x="1050" y="1755" class="code">        </text>
    <text x="1050" y="1770" class="code">        fun getDatabase(context: Context): AppDatabase {</text>
    <text x="1050" y="1785" class="code">            return INSTANCE ?: synchronized(this) {</text>
    <text x="1050" y="1800" class="code">                Room.databaseBuilder(context, AppDatabase::class.java, "app_db")</text>
    <text x="1050" y="1815" class="code">                    .build().also { INSTANCE = it }</text>
    <text x="1050" y="1830" class="code">            }</text>
    <text x="1050" y="1845" class="code">        }</text>
    <text x="1050" y="1860" class="code">    }</text>
    <text x="1050" y="1875" class="code">}</text></text>
  </g>

  <!-- 第七步：Fragment间通信和Activity协调 -->
  <g id="step7-communication">
    <rect x="20" y="1780" width="1560" height="280" class="fragment-box" rx="10"/>
    <text x="800" y="1805" text-anchor="middle" class="subtitle">第7步：Fragment间通信与Activity协调</text>

    <!-- Activity协调逻辑 -->
    <rect x="40" y="1820" width="480" height="230" class="activity-box" rx="8"/>
    <text x="280" y="1840" text-anchor="middle" class="text" font-weight="bold">Activity协调Fragment切换</text>
    <text x="50" y="1860" class="code">class UserProfileActivity : GTBaseActivity() {</text>
    <text x="50" y="1875" class="code">    </text>
    <text x="50" y="1890" class="code">    private fun initView() {</text>
    <text x="50" y="1905" class="code">        // 默认显示信息展示Fragment</text>
    <text x="50" y="1920" class="code">        showDisplayFragment()</text>
    <text x="50" y="1935" class="code">        </text>
    <text x="50" y="1950" class="code">        // 设置工具栏编辑按钮</text>
    <text x="50" y="1965" class="code">        binding.toolbar.btnEdit.setOnClickListener {</text>
    <text x="50" y="1980" class="code">            if (isEditMode) {</text>
    <text x="50" y="1995" class="code">                showDisplayFragment()</text>
    <text x="50" y="2010" class="code">            } else {</text>
    <text x="50" y="2025" class="code">                showEditFragment()</text>
    <text x="50" y="2040" class="code">            }</text>
    <text x="50" y="2055" class="code">        }</text>

    <!-- Fragment切换方法 -->
    <rect x="540" y="1820" width="480" height="230" class="activity-box" rx="8"/>
    <text x="780" y="1840" text-anchor="middle" class="text" font-weight="bold">Fragment切换实现</text>
    <text x="550" y="1860" class="code">    private fun showDisplayFragment() {</text>
    <text x="550" y="1875" class="code">        val userId = intent.getStringExtra(EXTRA_USER_ID) ?: return</text>
    <text x="550" y="1890" class="code">        val fragment = ProfileDisplayFragment.newInstance(userId)</text>
    <text x="550" y="1905" class="code">        </text>
    <text x="550" y="1920" class="code">        supportFragmentManager.beginTransaction()</text>
    <text x="550" y="1935" class="code">            .replace(R.id.fragment_container, fragment)</text>
    <text x="550" y="1950" class="code">            .commit()</text>
    <text x="550" y="1965" class="code">        </text>
    <text x="550" y="1980" class="code">        isEditMode = false</text>
    <text x="550" y="1995" class="code">        updateToolbarState()</text>
    <text x="550" y="2010" class="code">    }</text>
    <text x="550" y="2025" class="code">    </text>
    <text x="550" y="2040" class="code">    private fun showEditFragment() {</text>
    <text x="550" y="2055" class="code">        // 类似的编辑Fragment显示逻辑</text>

    <!-- 通信机制说明 -->
    <rect x="1040" y="1820" width="520" height="230" class="lifecycle-box" rx="8"/>
    <text x="1300" y="1840" text-anchor="middle" class="text" font-weight="bold">通信机制与数据流</text>
    <text x="1050" y="1860" class="small-text">🔄 Activity ↔ Fragment：</text>
    <text x="1050" y="1875" class="small-text">  • Activity通过Bundle传递参数给Fragment</text>
    <text x="1050" y="1890" class="small-text">  • Fragment通过接口回调通知Activity</text>
    <text x="1050" y="1905" class="small-text">📡 Fragment ↔ ViewModel：</text>
    <text x="1050" y="1920" class="small-text">  • Fragment观察ViewModel的LiveData</text>
    <text x="1050" y="1935" class="small-text">  • Fragment调用ViewModel的方法触发操作</text>
    <text x="1050" y="1950" class="small-text">🌐 ViewModel ↔ Repository：</text>
    <text x="1050" y="1965" class="small-text">  • ViewModel调用Repository获取数据</text>
    <text x="1050" y="1980" class="small-text">  • Repository处理网络和本地数据源</text>
    <text x="1050" y="1995" class="small-text">💾 Repository ↔ 数据源：</text>
    <text x="1050" y="2010" class="small-text">  • 网络API获取最新数据</text>
    <text x="1050" y="2025" class="small-text">  • 本地数据库提供缓存和离线支持</text>
    <text x="1050" y="2040" class="small-text">  • 图片上传和文件管理</text>
    <text x="1050" y="2055" class="small-text">  • 错误处理和重试机制</text>
  </g>

  <!-- 第八步：Manifest注册和测试 -->
  <g id="step8-manifest-testing">
    <rect x="20" y="2080" width="1560" height="200" class="module-box" rx="10"/>
    <text x="800" y="2105" text-anchor="middle" class="subtitle">第8步：Manifest注册与功能测试</text>

    <!-- Manifest注册 -->
    <rect x="40" y="2120" width="480" height="150" class="module-box" rx="8"/>
    <text x="280" y="2140" text-anchor="middle" class="text" font-weight="bold">AndroidManifest.xml注册</text>
    <text x="50" y="2160" class="code">&lt;activity</text>
    <text x="50" y="2175" class="code">    android:name=".profile.UserProfileActivity"</text>
    <text x="50" y="2190" class="code">    android:label="@string/user_profile_title"</text>
    <text x="50" y="2205" class="code">    android:theme="@style/AppTheme.NoActionBar"</text>
    <text x="50" y="2220" class="code">    android:configChanges="orientation|keyboard|keyboardHidden|screenSize"</text>
    <text x="50" y="2235" class="code">    android:exported="false" /&gt;</text>
    <text x="50" y="2250" class="code">    </text>
    <text x="50" y="2265" class="code">&lt;!-- 权限声明 --&gt;</text>

    <!-- 启动方式 -->
    <rect x="540" y="2120" width="480" height="150" class="activity-box" rx="8"/>
    <text x="780" y="2140" text-anchor="middle" class="text" font-weight="bold">Activity启动方式</text>
    <text x="550" y="2160" class="code">// 从其他Activity启动</text>
    <text x="550" y="2175" class="code">val intent = UserProfileActivity.createIntent(this, "user123")</text>
    <text x="550" y="2190" class="code">startActivity(intent)</text>
    <text x="550" y="2205" class="code">    </text>
    <text x="550" y="2220" class="code">// 带动画效果启动</text>
    <text x="550" y="2235" class="code">startActivity(intent)</text>
    <text x="550" y="2250" class="code">overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)</text>
    <text x="550" y="2265" class="code">    </text>

    <!-- 测试要点 -->
    <rect x="1040" y="2120" width="520" height="150" class="lifecycle-box" rx="8"/>
    <text x="1300" y="2140" text-anchor="middle" class="text" font-weight="bold">功能测试要点</text>
    <text x="1050" y="2160" class="small-text">✅ 界面测试：</text>
    <text x="1050" y="2175" class="small-text">  • 信息正确显示、编辑功能正常</text>
    <text x="1050" y="2190" class="small-text">  • Fragment切换流畅、状态保持</text>
    <text x="1050" y="2205" class="small-text">✅ 数据测试：</text>
    <text x="1050" y="2220" class="small-text">  • 网络请求成功、错误处理</text>
    <text x="1050" y="2235" class="small-text">  • 本地缓存、离线功能</text>
    <text x="1050" y="2250" class="small-text">✅ 生命周期测试：</text>
    <text x="1050" y="2265" class="small-text">  • 横竖屏切换、后台恢复</text>
  </g>

  <!-- 总结和最佳实践 -->
  <g id="summary-best-practices">
    <rect x="20" y="2300" width="1560" height="280" class="example-box" rx="10"/>
    <text x="800" y="2325" text-anchor="middle" class="subtitle">📚 总结：Activity、Fragment、XML协作模式</text>

    <!-- 协作关系图 -->
    <rect x="40" y="2340" width="480" height="230" class="example-box" rx="8"/>
    <text x="280" y="2360" text-anchor="middle" class="text" font-weight="bold">三者协作关系</text>
    <text x="50" y="2380" class="small-text">🏗️ Activity（建筑框架）：</text>
    <text x="50" y="2395" class="small-text">  • 提供整体容器和生命周期管理</text>
    <text x="50" y="2410" class="small-text">  • 处理系统级交互和导航</text>
    <text x="50" y="2425" class="small-text">  • 协调多个Fragment的显示</text>
    <text x="50" y="2440" class="small-text">🧩 Fragment（功能模块）：</text>
    <text x="50" y="2455" class="small-text">  • 实现具体的业务功能</text>
    <text x="50" y="2470" class="small-text">  • 可重用、可组合的UI组件</text>
    <text x="50" y="2485" class="small-text">  • 独立的数据处理和状态管理</text>
    <text x="50" y="2500" class="small-text">📄 XML（装修设计）：</text>
    <text x="50" y="2515" class="small-text">  • 定义界面的视觉呈现</text>
    <text x="50" y="2530" class="small-text">  • 提供样式和布局规范</text>
    <text x="50" y="2545" class="small-text">  • 支持多设备适配</text>
    <text x="50" y="2560" class="small-text">  • 资源统一管理</text>

    <!-- 开发流程总结 -->
    <rect x="540" y="2340" width="480" height="230" class="flow-box" rx="8"/>
    <text x="780" y="2360" text-anchor="middle" class="text" font-weight="bold">开发流程总结</text>
    <text x="550" y="2380" class="small-text">1️⃣ 需求分析 → 确定功能和界面设计</text>
    <text x="550" y="2395" class="small-text">2️⃣ Activity创建 → 提供容器和生命周期</text>
    <text x="550" y="2410" class="small-text">3️⃣ XML设计 → 定义布局和样式</text>
    <text x="550" y="2425" class="small-text">4️⃣ Fragment实现 → 具体功能模块</text>
    <text x="550" y="2440" class="small-text">5️⃣ 业务逻辑 → ViewModel + Repository</text>
    <text x="550" y="2455" class="small-text">6️⃣ 数据层 → API + Database</text>
    <text x="550" y="2470" class="small-text">7️⃣ 通信协调 → Fragment间通信</text>
    <text x="550" y="2485" class="small-text">8️⃣ 注册测试 → Manifest + 功能验证</text>
    <text x="550" y="2500" class="small-text">    </text>
    <text x="550" y="2515" class="small-text">🔄 迭代优化：</text>
    <text x="550" y="2530" class="small-text">  • 性能优化、用户体验改进</text>
    <text x="550" y="2545" class="small-text">  • 代码重构、架构优化</text>
    <text x="550" y="2560" class="small-text">  • 测试覆盖、错误处理完善</text>

    <!-- 最佳实践建议 -->
    <rect x="1040" y="2340" width="520" height="230" class="lifecycle-box" rx="8"/>
    <text x="1300" y="2360" text-anchor="middle" class="text" font-weight="bold">最佳实践建议</text>
    <text x="1050" y="2380" class="small-text">🎯 架构原则：</text>
    <text x="1050" y="2395" class="small-text">  • 单一职责：每个组件专注自己的功能</text>
    <text x="1050" y="2410" class="small-text">  • 松耦合：通过接口和观察者模式通信</text>
    <text x="1050" y="2425" class="small-text">  • 可测试：业务逻辑与UI分离</text>
    <text x="1050" y="2440" class="small-text">🔧 技术选择：</text>
    <text x="1050" y="2455" class="small-text">  • ViewBinding替代findViewById</text>
    <text x="1050" y="2470" class="small-text">  • LiveData + ViewModel管理状态</text>
    <text x="1050" y="2485" class="small-text">  • Repository模式统一数据访问</text>
    <text x="1050" y="2500" class="small-text">📱 用户体验：</text>
    <text x="1050" y="2515" class="small-text">  • 加载状态提示、错误处理</text>
    <text x="1050" y="2530" class="small-text">  • 流畅的动画和转场效果</text>
    <text x="1050" y="2545" class="small-text">  • 响应式设计、多设备适配</text>
    <text x="1050" y="2560" class="small-text">  • 性能优化、内存管理</text>
  </g>
</svg>
