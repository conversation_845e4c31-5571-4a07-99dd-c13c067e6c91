<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 20px; font-weight: bold; text-anchor: middle; fill: #e74c3c; }
      .section-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; font-weight: bold; fill: white; text-anchor: middle; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #555; }
      .problem-text { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 13px; fill: #e74c3c; font-weight: bold; }
      .advantage-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; fill: #27ae60; font-weight: bold; }
      .weak-box { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 2; }
      .strong-box { fill: #00b894; stroke: #00a085; stroke-width: 2; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      .problem-box { fill: #ffebee; stroke: #f44336; stroke-width: 2; }
      .solution-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
    </style>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>

  <!-- 主标题 -->
  <text x="900" y="40" class="title">为什么其他版本需要发版？技术架构深度对比分析</text>
  <text x="900" y="70" class="subtitle">Fragment嵌入式 vs Activity跳转式 - 架构决定配置能力</text>

  <!-- 医疗家庭版架构分析 -->
  <rect x="50" y="100" width="800" height="450" class="weak-box" rx="15" filter="url(#shadow)"/>
  <text x="450" y="130" class="section-title" style="fill: #2d3436;">📱 医疗家庭版 - Fragment嵌入式架构 (需发版)</text>

  <!-- 代码固化问题 -->
  <rect x="70" y="150" width="760" height="120" class="code-box" rx="8"/>
  <text x="450" y="175" class="description" style="font-weight: bold; text-anchor: middle;">🔒 模块配置固化在代码中 - 无法动态修改</text>
  
  <text x="90" y="195" class="code-text">// TreatmentModule.kt - 枚举固化</text>
  <text x="90" y="210" class="code-text">enum class TreatmentModule(val moduleKey:String) {</text>
  <text x="110" y="225" class="code-text">OCCLUSION_THERAPY("occlusion-therapy"),  // 写死在代码中</text>
  <text x="110" y="240" class="code-text">VISION_THERAPY("vision-therapy")         // 写死在代码中</text>
  <text x="90" y="255" class="code-text">}</text>

  <!-- Fragment嵌入问题 -->
  <rect x="70" y="280" width="760" height="140" class="code-box" rx="8"/>
  <text x="450" y="305" class="description" style="font-weight: bold; text-anchor: middle;">🔗 Fragment嵌入式架构 - 模块耦合度高</text>
  
  <text x="90" y="325" class="code-text">// TreatmentModuleAdapter.kt - Fragment嵌入</text>
  <text x="90" y="340" class="code-text">when(module.moduleKey){</text>
  <text x="110" y="355" class="code-text">TreatmentModule.OCCLUSION_THERAPY.moduleKey -> {</text>
  <text x="130" y="370" class="code-text">// 直接嵌入Fragment，无法动态替换</text>
  <text x="130" y="385" class="code-text">beginTransaction.replace(flModuleRoot.id, MaskTherapyFragment.newInstance())</text>
  <text x="110" y="400" class="code-text">}</text>
  <text x="90" y="415" class="code-text">}</text>

  <!-- 过滤逻辑固化 -->
  <rect x="70" y="430" width="760" height="110" class="problem-box" rx="8"/>
  <text x="450" y="455" class="problem-text" style="text-anchor: middle;">❌ 模块过滤逻辑写死 - 新增模块需修改代码</text>
  
  <text x="90" y="475" class="code-text">// HomeMainFragment.kt - 过滤逻辑固化</text>
  <text x="90" y="490" class="code-text">val filterModules = modules.filter { module -></text>
  <text x="110" y="505" class="code-text">module.moduleEnable == true &&</text>
  <text x="110" y="520" class="code-text">(module.moduleKey == TreatmentModule.OCCLUSION_THERAPY.moduleKey ||</text>
  <text x="110" y="535" class="code-text"> module.moduleKey == TreatmentModule.VISION_THERAPY.moduleKey) // 硬编码</text>

  <!-- 阅读版架构分析 -->
  <rect x="900" y="100" width="800" height="450" class="weak-box" rx="15" filter="url(#shadow)"/>
  <text x="1300" y="130" class="section-title" style="fill: #2d3436;">📖 阅读版 - Fragment嵌入式架构 (需发版)</text>

  <!-- 阅读版代码固化 -->
  <rect x="920" y="150" width="760" height="120" class="code-box" rx="8"/>
  <text x="1300" y="175" class="description" style="font-weight: bold; text-anchor: middle;">🔒 模块配置同样固化在代码中</text>
  
  <text x="940" y="195" class="code-text">// ReadHomeMode.kt - 常量固化</text>
  <text x="940" y="210" class="code-text">companion object{</text>
  <text x="960" y="225" class="code-text">const val MYOPIA_P_C = "myopia-p-c"     // 写死在代码中</text>
  <text x="960" y="240" class="code-text">const val MYOPIA_R_T = "myopia-r-t"     // 写死在代码中</text>
  <text x="940" y="255" class="code-text">}</text>

  <!-- 阅读版Fragment嵌入 -->
  <rect x="920" y="280" width="760" height="140" class="code-box" rx="8"/>
  <text x="1300" y="305" class="description" style="font-weight: bold; text-anchor: middle;">🔗 同样的Fragment嵌入式架构</text>
  
  <text x="940" y="325" class="code-text">// ReadHomeModuleAdapter.kt - Fragment嵌入</text>
  <text x="940" y="340" class="code-text">when(module.moduleKey){</text>
  <text x="960" y="355" class="code-text">ReadHomeMode.MYOPIA_P_C -> {</text>
  <text x="980" y="370" class="code-text">// 直接嵌入Fragment，无法动态替换</text>
  <text x="980" y="385" class="code-text">beginTransaction.replace(flModuleRoot.id, MyopiaControlFragment.newInstance())</text>
  <text x="960" y="400" class="code-text">}</text>
  <text x="940" y="415" class="code-text">}</text>

  <!-- 阅读版过滤逻辑 -->
  <rect x="920" y="430" width="760" height="110" class="problem-box" rx="8"/>
  <text x="1300" y="455" class="problem-text" style="text-anchor: middle;">❌ 同样的硬编码过滤逻辑</text>
  
  <text x="940" y="475" class="code-text">// ReadHomeMainFragment.kt - 过滤逻辑固化</text>
  <text x="940" y="490" class="code-text">val filterModules = modules.filter { module -></text>
  <text x="960" y="505" class="code-text">module.moduleEnable == true &&</text>
  <text x="960" y="520" class="code-text">(module.moduleKey == ReadHomeMode.MYOPIA_P_C ||</text>
  <text x="960" y="535" class="code-text"> module.moduleKey == ReadHomeMode.MYOPIA_R_T) // 硬编码</text>

  <!-- 问题总结 -->
  <rect x="50" y="570" width="1650" height="120" class="problem-box" rx="15"/>
  <text x="875" y="600" class="problem-text" style="font-size: 18px; text-anchor: middle;">❌ 医疗家庭版 & 阅读版的核心问题 - 为什么需要发版？</text>
  
  <text x="70" y="625" class="problem-text">1. 🔒 模块枚举固化: TreatmentModule、ReadHomeMode 等枚举写死在代码中，新增模块必须修改源码</text>
  <text x="70" y="645" class="problem-text">2. 🔗 Fragment嵌入耦合: when语句硬编码Fragment映射关系，无法动态替换业务逻辑</text>
  <text x="70" y="665" class="problem-text">3. 📋 过滤逻辑固化: 模块过滤条件写死在代码中，调整业务规则需要修改源码并重新编译</text>

  <!-- 医疗进院版解决方案 -->
  <rect x="50" y="710" width="1650" height="200" class="solution-box" rx="15"/>
  <text x="875" y="740" class="advantage-text" style="font-size: 18px; text-anchor: middle;">✅ 医疗进院版的解决方案 - 为什么不需要发版？</text>

  <text x="70" y="765" class="advantage-text">1. 🌐 服务器端配置: 通过 getHospitalEditionProfile() API 实时获取模块配置，无需硬编码</text>
  <text x="70" y="785" class="advantage-text">2. 🎯 Activity跳转架构: 根据 moduleKey 动态路由到对应Activity，模块间完全解耦</text>
  <text x="70" y="805" class="advantage-text">3. 🔄 动态过滤机制: 服务器端控制 moduleEnable 字段，客户端实时响应配置变化</text>
  <text x="70" y="825" class="advantage-text">4. 📡 实时配置更新: 配置变更立即生效，无需重启应用，用户无感知更新</text>

  <!-- 代码对比 -->
  <rect x="70" y="845" width="760" height="120" class="code-box" rx="8"/>
  <text x="450" y="870" class="advantage-text" style="text-anchor: middle;">✅ 医疗进院版 - 动态配置代码</text>
  
  <text x="90" y="890" class="code-text">// HospitalMainFragment.kt - 动态路由</text>
  <text x="90" y="905" class="code-text">when(it.moduleKey){  // it 来自服务器配置</text>
  <text x="110" y="920" class="code-text">HospitalModuleKey.INSPECTION_CENTER.moduleKey -> {</text>
  <text x="130" y="935" class="code-text">startActivity(InspectionCenterActivity.createIntent(mActivity,it.url))</text>
  <text x="110" y="950" class="code-text">} // Activity跳转，完全解耦</text>

  <rect x="870" y="845" width="760" height="120" class="code-box" rx="8"/>
  <text x="1250" y="870" class="advantage-text" style="text-anchor: middle;">✅ 服务器端配置数据结构</text>
  
  <text x="890" y="890" class="code-text">// MHospitalMode.kt - 完全动态配置</text>
  <text x="890" y="905" class="code-text">data class MHospitalMode(</text>
  <text x="910" y="920" class="code-text">var moduleEnable: Boolean?,  // 服务器端控制</text>
  <text x="910" y="935" class="code-text">var moduleKey: String?,      // 动态模块标识</text>
  <text x="910" y="950" class="code-text">var url: String?,            // 动态URL配置</text>

  <!-- 技术优势总结 -->
  <rect x="50" y="980" width="1650" height="180" class="solution-box" rx="15"/>
  <text x="875" y="1010" class="advantage-text" style="font-size: 18px; text-anchor: middle;">🏆 技术架构优势对比总结</text>

  <text x="70" y="1040" class="description" style="font-weight: bold;">📊 配置更新对比:</text>
  <text x="90" y="1060" class="description">• 医疗家庭版/阅读版: 修改代码 → 编译 → 测试 → 发版 → 用户更新 (周期: 天/周级)</text>
  <text x="90" y="1080" class="advantage-text">• 医疗进院版: 服务器端配置修改 → 客户端实时生效 (周期: 秒级)</text>

  <text x="70" y="1110" class="description" style="font-weight: bold;">🔧 模块扩展对比:</text>
  <text x="90" y="1130" class="description">• 医疗家庭版/阅读版: 新增模块需修改枚举、适配器、过滤逻辑等多处代码</text>
  <text x="90" y="1150" class="advantage-text">• 医疗进院版: 服务器端添加模块配置即可，客户端自动识别和渲染</text>

</svg>
