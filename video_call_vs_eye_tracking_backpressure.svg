<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #2c3e50; }
      .video-call-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .eye-tracking-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .strategy-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .comparison-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .frame-keep { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; }
      .frame-drop { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; opacity: 0.6; }
      .frame-encode { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .drop-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
      .network-line { stroke: #9b59b6; stroke-width: 3; stroke-dasharray: 10,5; }
    </style>
    
    <!-- Arrow markers -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1600" height="1000" fill="#ecf0f1"/>
  
  <!-- Title -->
  <text x="800" y="40" text-anchor="middle" class="title">视频通话 vs 眼动追踪：背压策略对比</text>
  
  <!-- Video Call Section -->
  <rect x="50" y="80" width="750" height="400" rx="15" class="video-call-box"/>
  <text x="425" y="110" text-anchor="middle" class="subtitle" fill="white">📹 视频通话系统</text>
  
  <!-- Video Call Components -->
  <rect x="80" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="140" y="165" text-anchor="middle" class="text">📷 相机</text>
  <text x="140" y="185" text-anchor="middle" class="small-text">30fps采集</text>
  
  <rect x="230" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="290" y="165" text-anchor="middle" class="text">🎬 编码器</text>
  <text x="290" y="185" text-anchor="middle" class="small-text">H.264/H.265</text>
  
  <rect x="380" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="440" y="165" text-anchor="middle" class="text">📡 网络传输</text>
  <text x="440" y="185" text-anchor="middle" class="small-text">WebRTC/RTP</text>
  
  <rect x="530" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="590" y="165" text-anchor="middle" class="text">🖥️ 远端显示</text>
  <text x="590" y="185" text-anchor="middle" class="small-text">解码播放</text>
  
  <!-- Video Call Strategy -->
  <rect x="80" y="230" width="570" height="120" rx="8" fill="#fadbd8"/>
  <text x="365" y="255" text-anchor="middle" class="subtitle" fill="#c0392b">背压策略：混合策略</text>
  
  <text x="100" y="280" class="text">• 编码前：STRATEGY_KEEP_ONLY_LATEST (丢弃未编码帧)</text>
  <text x="100" y="300" class="text">• 网络层：自适应码率 + 帧率调整</text>
  <text x="100" y="320" class="text">• 缓冲区：JitterBuffer 处理网络抖动</text>
  <text x="100" y="340" class="text">• 目标：平衡画质与流畅度，避免卡顿</text>
  
  <!-- Video Call Frame Flow -->
  <text x="80" y="380" class="small-text">帧处理流程：</text>
  <circle cx="120" cy="400" r="12" class="frame-keep"/>
  <text x="120" y="405" text-anchor="middle" class="small-text" fill="white">1</text>
  
  <circle cx="180" cy="400" r="12" class="frame-drop"/>
  <text x="180" y="405" text-anchor="middle" class="small-text" fill="white">2</text>
  <line x1="165" y1="385" x2="155" y2="375" class="drop-arrow"/>
  
  <circle cx="240" cy="400" r="12" class="frame-encode"/>
  <text x="240" y="405" text-anchor="middle" class="small-text" fill="white">3</text>
  <line x1="135" y1="400" x2="225" y2="400" class="arrow"/>
  
  <text x="280" y="405" class="small-text">→ 网络传输 →</text>
  
  <!-- Eye Tracking Section -->
  <rect x="850" y="80" width="700" height="400" rx="15" class="eye-tracking-box"/>
  <text x="1200" y="110" text-anchor="middle" class="subtitle" fill="white">👁️ 眼动追踪系统</text>
  
  <!-- Eye Tracking Components -->
  <rect x="880" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="940" y="165" text-anchor="middle" class="text">📷 相机</text>
  <text x="940" y="185" text-anchor="middle" class="small-text">30fps采集</text>
  
  <rect x="1030" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="1090" y="165" text-anchor="middle" class="text">🤖 AI算法</text>
  <text x="1090" y="185" text-anchor="middle" class="small-text">眼动检测</text>
  
  <rect x="1180" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="1240" y="165" text-anchor="middle" class="text">🎯 实时反馈</text>
  <text x="1240" y="185" text-anchor="middle" class="small-text">坐标显示</text>
  
  <rect x="1330" y="140" width="120" height="60" rx="8" fill="white"/>
  <text x="1390" y="165" text-anchor="middle" class="text">💾 本地存储</text>
  <text x="1390" y="185" text-anchor="middle" class="small-text">数据记录</text>
  
  <!-- Eye Tracking Strategy -->
  <rect x="880" y="230" width="570" height="120" rx="8" fill="#d6eaf8"/>
  <text x="1165" y="255" text-anchor="middle" class="subtitle" fill="#2980b9">背压策略：严格最新帧</text>
  
  <text x="900" y="280" class="text">• 策略：STRATEGY_KEEP_ONLY_LATEST (严格执行)</text>
  <text x="900" y="300" class="text">• 原因：实时性要求极高，延迟不可接受</text>
  <text x="900" y="320" class="text">• 特点：宁可丢帧也要保证实时响应</text>
  <text x="900" y="340" class="text">• 目标：最低延迟，最新状态反馈</text>
  
  <!-- Eye Tracking Frame Flow -->
  <text x="880" y="380" class="small-text">帧处理流程：</text>
  <circle cx="920" cy="400" r="12" class="frame-drop"/>
  <text x="920" y="405" text-anchor="middle" class="small-text" fill="white">1</text>
  <line x1="905" y1="385" x2="895" y2="375" class="drop-arrow"/>
  
  <circle cx="980" cy="400" r="12" class="frame-drop"/>
  <text x="980" y="405" text-anchor="middle" class="small-text" fill="white">2</text>
  <line x1="965" y1="385" x2="955" y2="375" class="drop-arrow"/>
  
  <circle cx="1040" cy="400" r="12" class="frame-keep"/>
  <text x="1040" y="405" text-anchor="middle" class="small-text" fill="white">3</text>
  <line x1="1055" y1="400" x2="1120" y2="400" class="arrow"/>
  
  <text x="1140" y="405" class="small-text">→ AI处理 → 实时显示</text>
  
  <!-- Detailed Comparison -->
  <text x="50" y="540" class="subtitle">详细对比分析：</text>
  
  <!-- Comparison Table -->
  <rect x="50" y="570" width="1500" height="350" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- Headers -->
  <rect x="70" y="590" width="200" height="40" fill="#34495e"/>
  <text x="170" y="615" text-anchor="middle" class="subtitle" fill="white">对比维度</text>
  
  <rect x="270" y="590" width="400" height="40" fill="#e74c3c"/>
  <text x="470" y="615" text-anchor="middle" class="subtitle" fill="white">📹 视频通话</text>
  
  <rect x="670" y="590" width="400" height="40" fill="#3498db"/>
  <text x="870" y="615" text-anchor="middle" class="subtitle" fill="white">👁️ 眼动追踪</text>
  
  <rect x="1070" y="590" width="400" height="40" fill="#27ae60"/>
  <text x="1270" y="615" text-anchor="middle" class="subtitle" fill="white">🎯 关键差异</text>
  
  <!-- Row 1: 背压策略 -->
  <rect x="70" y="630" width="200" height="50" fill="#ecf0f1"/>
  <text x="170" y="660" text-anchor="middle" class="text">背压策略</text>
  
  <rect x="270" y="630" width="400" height="50" fill="#fadbd8"/>
  <text x="290" y="650" class="small-text">混合策略：</text>
  <text x="290" y="665" class="small-text">• 编码前丢帧 + 网络自适应</text>
  
  <rect x="670" y="630" width="400" height="50" fill="#d6eaf8"/>
  <text x="690" y="650" class="small-text">严格最新帧：</text>
  <text x="690" y="665" class="small-text">• STRATEGY_KEEP_ONLY_LATEST</text>
  
  <rect x="1070" y="630" width="400" height="50" fill="#d5f4e6"/>
  <text x="1090" y="650" class="small-text">视频通话需要平衡画质与流畅度</text>
  <text x="1090" y="665" class="small-text">眼动追踪只关心最新状态</text>
  
  <!-- Row 2: 延迟容忍度 -->
  <rect x="70" y="680" width="200" height="50" fill="#ecf0f1"/>
  <text x="170" y="710" text-anchor="middle" class="text">延迟容忍度</text>
  
  <rect x="270" y="680" width="400" height="50" fill="#fadbd8"/>
  <text x="290" y="700" class="small-text">中等 (100-300ms)</text>
  <text x="290" y="715" class="small-text">缓冲区可以平滑播放</text>
  
  <rect x="670" y="680" width="400" height="50" fill="#d6eaf8"/>
  <text x="690" y="700" class="small-text">极低 (<50ms)</text>
  <text x="690" y="715" class="small-text">任何延迟都影响用户体验</text>
  
  <rect x="1070" y="680" width="400" height="50" fill="#d5f4e6"/>
  <text x="1090" y="700" class="small-text">眼动追踪对延迟极其敏感</text>
  <text x="1090" y="715" class="small-text">视频通话可以接受适度延迟</text>
  
  <!-- Row 3: 数据完整性 -->
  <rect x="70" y="730" width="200" height="50" fill="#ecf0f1"/>
  <text x="170" y="760" text-anchor="middle" class="text">数据完整性</text>
  
  <rect x="270" y="730" width="400" height="50" fill="#fadbd8"/>
  <text x="290" y="750" class="small-text">重要</text>
  <text x="290" y="765" class="small-text">需要保证视频连续性</text>
  
  <rect x="670" y="730" width="400" height="50" fill="#d6eaf8"/>
  <text x="690" y="750" class="small-text">次要</text>
  <text x="690" y="765" class="small-text">只需要当前最新状态</text>
  
  <rect x="1070" y="730" width="400" height="50" fill="#d5f4e6"/>
  <text x="1090" y="750" class="small-text">视频需要连续性</text>
  <text x="1090" y="765" class="small-text">眼动只需要实时性</text>
  
  <!-- Row 4: 网络适应性 -->
  <rect x="70" y="780" width="200" height="50" fill="#ecf0f1"/>
  <text x="170" y="810" text-anchor="middle" class="text">网络适应性</text>
  
  <rect x="270" y="780" width="400" height="50" fill="#fadbd8"/>
  <text x="290" y="800" class="small-text">强</text>
  <text x="290" y="815" class="small-text">动态调整码率、分辨率、帧率</text>
  
  <rect x="670" y="780" width="400" height="50" fill="#d6eaf8"/>
  <text x="690" y="800" class="small-text">无</text>
  <text x="690" y="815" class="small-text">本地处理，不依赖网络</text>
  
  <rect x="1070" y="780" width="400" height="50" fill="#d5f4e6"/>
  <text x="1090" y="800" class="small-text">视频通话需要网络自适应</text>
  <text x="1090" y="815" class="small-text">眼动追踪是本地实时处理</text>
  
  <!-- Row 5: 应用场景 -->
  <rect x="70" y="830" width="200" height="50" fill="#ecf0f1"/>
  <text x="170" y="860" text-anchor="middle" class="text">典型应用</text>
  
  <rect x="270" y="830" width="400" height="50" fill="#fadbd8"/>
  <text x="290" y="850" class="small-text">Zoom、Teams、WebRTC</text>
  <text x="290" y="865" class="small-text">直播、视频会议</text>
  
  <rect x="670" y="830" width="400" height="50" fill="#d6eaf8"/>
  <text x="690" y="850" class="small-text">医疗诊断、VR/AR</text>
  <text x="690" y="865" class="small-text">游戏控制、辅助技术</text>
  
  <rect x="1070" y="830" width="400" height="50" fill="#d5f4e6"/>
  <text x="1090" y="850" class="small-text">不同应用场景决定了</text>
  <text x="1090" y="865" class="small-text">不同的背压策略选择</text>
  
  <!-- Code Examples -->
  <text x="50" y="950" class="subtitle">代码实现对比：</text>
  
  <!-- Video Call Code -->
  <rect x="50" y="970" width="750" height="25" fill="#fadbd8"/>
  <text x="60" y="985" class="code">// 视频通话：混合策略</text>
  <text x="60" y="1000" class="code">videoEncoder.setBackpressureStrategy(ADAPTIVE_BITRATE | DROP_FRAMES)</text>
  
  <!-- Eye Tracking Code -->
  <rect x="820" y="970" width="750" height="25" fill="#d6eaf8"/>
  <text x="830" y="985" class="code">// 眼动追踪：严格最新帧</text>
  <text x="830" y="1000" class="code">imageAnalysis.setBackpressureStrategy(STRATEGY_KEEP_ONLY_LATEST)</text>
  
</svg>
