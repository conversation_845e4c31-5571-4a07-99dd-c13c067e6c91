<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .step-box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
      .process-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .model-box { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; }
      .result-box { fill: #f8d7da; stroke: #dc3545; stroke-width: 2; }
      .arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
    </marker>
  </defs>

  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" class="title">视频人脸检测实现流程</text>
  <text x="500" y="50" text-anchor="middle" class="subtitle">基于现有SCRFD模型的视频截帧检测方案</text>

  <!-- Step 1: Video Input -->
  <rect x="50" y="80" width="180" height="80" class="step-box" rx="5"/>
  <text x="60" y="100" class="subtitle">1. 视频输入</text>
  <text x="60" y="120" class="text">• 视频文件路径</text>
  <text x="60" y="135" class="text">• 实时摄像头流</text>
  <text x="60" y="150" class="code">VideoCapture cap(video_path)</text>

  <!-- Step 2: Frame Extraction -->
  <rect x="280" y="80" width="180" height="80" class="process-box" rx="5"/>
  <text x="290" y="100" class="subtitle">2. 视频截帧</text>
  <text x="290" y="120" class="text">• 按帧率提取帧</text>
  <text x="290" y="135" class="text">• 或按时间间隔截取</text>
  <text x="290" y="150" class="code">cap.read(frame)</text>

  <!-- Step 3: Image Preprocessing -->
  <rect x="510" y="80" width="180" height="80" class="process-box" rx="5"/>
  <text x="520" y="100" class="subtitle">3. 图像预处理</text>
  <text x="520" y="120" class="text">• 格式转换 (BGR→RGB)</text>
  <text x="520" y="135" class="text">• 尺寸调整 (640x640)</text>
  <text x="520" y="150" class="code">cvtColor, resize</text>

  <!-- Step 4: SCRFD Model -->
  <rect x="740" y="80" width="200" height="80" class="model-box" rx="5"/>
  <text x="750" y="100" class="subtitle">4. SCRFD人脸检测</text>
  <text x="750" y="120" class="text">• 现有模型: scrfd_500m_kps</text>
  <text x="750" y="135" class="text">• 检测置信度: 0.2</text>
  <text x="750" y="150" class="code">g_scrfd->detect(rgb, faces)</text>

  <!-- Arrows for main flow -->
  <line x1="230" y1="120" x2="280" y2="120" class="arrow"/>
  <line x1="460" y1="120" x2="510" y2="120" class="arrow"/>
  <line x1="690" y1="120" x2="740" y2="120" class="arrow"/>

  <!-- Step 5: Face Analysis -->
  <rect x="50" y="220" width="200" height="100" class="result-box" rx="5"/>
  <text x="60" y="240" class="subtitle">5. 人脸检测结果</text>
  <text x="60" y="260" class="text">• 人脸边界框 (x,y,w,h)</text>
  <text x="60" y="275" class="text">• 5个关键点坐标</text>
  <text x="60" y="290" class="text">• 检测置信度</text>
  <text x="60" y="305" class="code">FaceObject[] faces</text>

  <!-- Step 6: Face Count -->
  <rect x="300" y="220" width="180" height="100" class="result-box" rx="5"/>
  <text x="310" y="240" class="subtitle">6. 人脸数量统计</text>
  <text x="310" y="260" class="text">• 检测到的人脸数量</text>
  <text x="310" y="275" class="text">• 是否有人脸 (bool)</text>
  <text x="310" y="290" class="text">• 多人脸处理</text>
  <text x="310" y="305" class="code">face_count = faces.size()</text>

  <!-- Step 7: Result Output -->
  <rect x="530" y="220" width="180" height="100" class="result-box" rx="5"/>
  <text x="540" y="240" class="subtitle">7. 结果输出</text>
  <text x="540" y="260" class="text">• JSON格式结果</text>
  <text x="540" y="275" class="text">• 标注图像保存</text>
  <text x="540" y="290" class="text">• 日志记录</text>
  <text x="540" y="305" class="code">return detection_result</text>

  <!-- Step 8: Batch Processing -->
  <rect x="760" y="220" width="180" height="100" class="process-box" rx="5"/>
  <text x="770" y="240" class="subtitle">8. 批量处理</text>
  <text x="770" y="260" class="text">• 多帧并行处理</text>
  <text x="770" y="275" class="text">• 结果聚合统计</text>
  <text x="770" y="290" class="text">• 时间序列分析</text>
  <text x="770" y="305" class="code">process_video_batch()</text>

  <!-- Vertical arrows -->
  <line x1="840" y1="160" x2="840" y2="220" class="arrow"/>
  <line x1="150" y1="160" x2="150" y2="220" class="data-flow"/>

  <!-- Horizontal arrows for results -->
  <line x1="250" y1="270" x2="300" y2="270" class="arrow"/>
  <line x1="480" y1="270" x2="530" y2="270" class="arrow"/>
  <line x1="710" y1="270" x2="760" y2="270" class="arrow"/>

  <!-- Implementation Details Section -->
  <rect x="50" y="380" width="900" height="200" class="step-box" rx="5"/>
  <text x="60" y="400" class="subtitle">实现方案详细说明</text>

  <!-- Column 1: Core Components -->
  <text x="70" y="430" class="subtitle">核心组件复用:</text>
  <text x="70" y="450" class="text">• 复用现有SCRFD类和模型文件</text>
  <text x="70" y="465" class="text">• 复用FaceObject结构体</text>
  <text x="70" y="480" class="text">• 复用图像预处理流程</text>
  <text x="70" y="495" class="code">SCRFD* detector = new SCRFD();</text>
  <text x="70" y="510" class="code">detector->load(mgr, "500m_kps", use_gpu);</text>

  <!-- Column 2: New Components -->
  <text x="350" y="430" class="subtitle">新增组件:</text>
  <text x="350" y="450" class="text">• VideoProcessor类 - 视频处理</text>
  <text x="350" y="465" class="text">• FrameExtractor类 - 帧提取</text>
  <text x="350" y="480" class="text">• FaceDetectionResult类 - 结果封装</text>
  <text x="350" y="495" class="code">class VideoFaceDetector {</text>
  <text x="350" y="510" class="code">  detectFacesInVideo(video_path);</text>
  <text x="350" y="525" class="code">};</text>

  <!-- Column 3: Output Format -->
  <text x="650" y="430" class="subtitle">输出格式:</text>
  <text x="650" y="450" class="text">• 每帧检测结果JSON</text>
  <text x="650" y="465" class="text">• 视频级别统计信息</text>
  <text x="650" y="480" class="text">• 可选的标注视频输出</text>
  <text x="650" y="495" class="code">{</text>
  <text x="650" y="510" class="code">  "frame_id": 123,</text>
  <text x="650" y="525" class="code">  "face_count": 2,</text>
  <text x="650" y="540" class="code">  "faces": [...]</text>
  <text x="650" y="555" class="code">}</text>

  <!-- Performance Considerations -->
  <rect x="50" y="620" width="900" height="120" class="process-box" rx="5"/>
  <text x="60" y="640" class="subtitle">性能优化考虑</text>

  <text x="70" y="670" class="text">• 帧率控制: 可配置每秒处理帧数，避免重复处理</text>
  <text x="70" y="685" class="text">• 批量处理: 多帧并行处理提高效率</text>
  <text x="70" y="700" class="text">• 内存管理: 及时释放帧数据，避免内存泄漏</text>
  <text x="70" y="715" class="text">• GPU加速: 利用现有GPU支持加速推理</text>

  <text x="500" y="670" class="text">• 结果缓存: 相似帧可复用检测结果</text>
  <text x="500" y="685" class="text">• 多线程: 视频解码和推理并行执行</text>
  <text x="500" y="700" class="text">• 早停机制: 检测到人脸后可选择停止处理</text>
  <text x="500" y="715" class="text">• 输出控制: 可选择只输出统计信息而非详细结果</text>

</svg>