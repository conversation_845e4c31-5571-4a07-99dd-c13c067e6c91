<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #34495e; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .small-text { font-family: Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      
      .activity-layer { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .service-layer { fill: #f0f8e8; stroke: #27ae60; stroke-width: 2; }
      .broadcast-layer { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .camera-layer { fill: #f4e8fd; stroke: #9b59b6; stroke-width: 2; }
      
      .activity-module { fill: #d6eaf8; stroke: #2980b9; stroke-width: 1; }
      .service-module { fill: #d5f4e6; stroke: #229954; stroke-width: 1; }
      .broadcast-module { fill: #fdeaa7; stroke: #d68910; stroke-width: 1; }
      .camera-module { fill: #e8daef; stroke: #8e44ad; stroke-width: 1; }
      
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #7f8c8d; stroke-width: 1; stroke-dasharray: 5,5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">视训项目Android四大组件架构与抗压机制分析</text>
  
  <!-- 1. 抗压能力优化 -->
  <rect x="50" y="60" width="1700" height="140" class="camera-layer"/>
  <text x="900" y="85" text-anchor="middle" class="section-title">1. 抗压能力优化：相机启动消息移至服务连接回调</text>
  
  <!-- 优化前后对比 -->
  <rect x="80" y="110" width="400" height="70" class="camera-module"/>
  <text x="280" y="130" text-anchor="middle" class="module-title">优化前：延迟等待问题</text>
  <text x="90" y="150" class="text">• 相机启动消息在Activity中直接发送</text>
  <text x="90" y="165" class="text">• 服务连接状态不确定导致消息丢失</text>
  <text x="90" y="180" class="text">• 需要额外的延迟等待机制</text>
  
  <rect x="500" y="110" width="400" height="70" class="camera-module"/>
  <text x="700" y="130" text-anchor="middle" class="module-title">优化后：服务连接回调</text>
  <text x="510" y="150" class="code">override fun onServiceConnected() {</text>
  <text x="510" y="165" class="code">  sendMessage(MSG_TURN_ON_CAMERA)</text>
  <text x="510" y="180" class="code">}</text>
  
  <rect x="920" y="110" width="400" height="70" class="camera-module"/>
  <text x="1120" y="130" text-anchor="middle" class="module-title">抗压效果</text>
  <text x="930" y="150" class="text">• 确保服务连接后再启动相机</text>
  <text x="930" y="165" class="text">• 避免消息丢失和重复启动</text>
  <text x="930" y="180" class="text">• 提高系统稳定性和响应速度</text>
  
  <rect x="1340" y="110" width="400" height="70" class="camera-module"/>
  <text x="1540" y="130" text-anchor="middle" class="module-title">实现关键点</text>
  <text x="1350" y="150" class="text">• ServiceConnection回调时机</text>
  <text x="1350" y="165" class="text">• Messenger消息传递机制</text>
  <text x="1350" y="180" class="text">• 相机状态原子性管理</text>

  <!-- 2. Android四大组件使用情况 -->
  <rect x="50" y="220" width="1700" height="200" class="activity-layer"/>
  <text x="900" y="245" text-anchor="middle" class="section-title">2. Android四大组件在视训项目中的使用情况</text>
  
  <!-- Activity组件 -->
  <rect x="80" y="270" width="400" height="130" class="activity-module"/>
  <text x="280" y="290" text-anchor="middle" class="module-title">Activity组件</text>
  <text x="90" y="310" class="text">• LauncherActivity - 启动器Activity</text>
  <text x="90" y="325" class="text">• HomeMainActivity - 医疗家庭版主界面</text>
  <text x="90" y="340" class="text">• CalibrationActivity - 校准Activity</text>
  <text x="90" y="355" class="text">• TrainWebActivity - 训练Web界面</text>
  <text x="90" y="370" class="text">• ReadActivity - 阅读评估Activity</text>
  <text x="90" y="385" class="text">• EyeMovementEvaluateActivity - 眼动评估</text>
  
  <!-- Service组件 -->
  <rect x="500" y="270" width="400" height="130" class="service-module"/>
  <text x="700" y="290" text-anchor="middle" class="module-title">Service组件</text>
  <text x="510" y="310" class="text">• GazeTrackService - 眼动追踪前台服务</text>
  <text x="510" y="325" class="text">  - 独立进程运行 (:GazeTracker)</text>
  <text x="510" y="340" class="text">  - 前台服务类型：camera</text>
  <text x="510" y="355" class="text">  - Messenger进程间通信</text>
  <text x="510" y="370" class="text">• DesktopService - 桌面服务</text>
  <text x="510" y="385" class="text">  - 独立进程运行 (:Desktop)</text>
  
  <!-- BroadcastReceiver组件 -->
  <rect x="920" y="270" width="400" height="130" class="broadcast-module"/>
  <text x="1120" y="290" text-anchor="middle" class="module-title">BroadcastReceiver组件</text>
  <text x="930" y="310" class="text">• BootReceiver - 开机启动广播</text>
  <text x="930" y="325" class="text">  - 监听ACTION_BOOT_COMPLETED</text>
  <text x="930" y="340" class="text">• GlobalBootReceiver - 全局控制广播</text>
  <text x="930" y="355" class="text">  - 眼动追踪启停控制</text>
  <text x="930" y="370" class="text">• RefreshBindUserReceiver - 用户绑定刷新</text>
  <text x="930" y="385" class="text">  - LiveEventBus事件分发</text>
  
  <!-- ContentProvider组件 -->
  <rect x="1340" y="270" width="400" height="130" class="broadcast-module"/>
  <text x="1540" y="290" text-anchor="middle" class="module-title">ContentProvider组件</text>
  <text x="1350" y="310" class="text">• 项目中未直接使用ContentProvider</text>
  <text x="1350" y="325" class="text">• 数据存储采用以下方案：</text>
  <text x="1350" y="340" class="text">  - MMKV键值存储</text>
  <text x="1350" y="355" class="text">  - 文件系统存储</text>
  <text x="1350" y="370" class="text">  - 网络API数据交互</text>
  <text x="1350" y="385" class="text">  - SQLite数据库（间接使用）</text>

  <!-- 3. 相机防重机制 -->
  <rect x="50" y="440" width="1700" height="160" class="camera-layer"/>
  <text x="900" y="465" text-anchor="middle" class="section-title">3. 相机防重机制</text>
  
  <!-- 原子状态管理 -->
  <rect x="80" y="490" width="350" height="90" class="camera-module"/>
  <text x="255" y="510" text-anchor="middle" class="module-title">原子状态管理</text>
  <text x="90" y="530" class="code">private val isCameraStarted = </text>
  <text x="90" y="545" class="code">  AtomicBoolean(false)</text>
  <text x="90" y="560" class="code">if (isCameraStarted.get()) return</text>
  <text x="90" y="575" class="code">isCameraStarted.set(true)</text>
  
  <!-- 服务状态管理 -->
  <rect x="450" y="490" width="350" height="90" class="camera-module"/>
  <text x="625" y="510" text-anchor="middle" class="module-title">服务状态管理</text>
  <text x="460" y="530" class="code">private val serviceMode = </text>
  <text x="460" y="545" class="code">  AtomicReference(NONE)</text>
  <text x="460" y="560" class="text">• TRACK - 追踪模式</text>
  <text x="460" y="575" class="text">• POSTURE_CALIBRATION - 姿势校准</text>
  
  <!-- 线程安全保障 -->
  <rect x="820" y="490" width="350" height="90" class="camera-module"/>
  <text x="995" y="510" text-anchor="middle" class="module-title">线程安全保障</text>
  <text x="830" y="530" class="text">• AtomicBoolean原子操作</text>
  <text x="830" y="545" class="text">• AtomicReference复杂对象引用</text>
  <text x="830" y="560" class="text">• 避免竞态条件</text>
  <text x="830" y="575" class="text">• 确保状态一致性</text>
  
  <!-- 异常处理机制 -->
  <rect x="1190" y="490" width="350" height="90" class="camera-module"/>
  <text x="1365" y="510" text-anchor="middle" class="module-title">异常处理机制</text>
  <text x="1200" y="530" class="text">• try-catch包装相机操作</text>
  <text x="1200" y="545" class="text">• 异常时自动停止相机</text>
  <text x="1200" y="560" class="text">• 状态重置和资源释放</text>
  <text x="1200" y="575" class="text">• 错误日志记录</text>

  <!-- 4. 眼动追踪背压策略 -->
  <rect x="50" y="620" width="1700" height="180" class="service-layer"/>
  <text x="900" y="645" text-anchor="middle" class="section-title">4. 眼动追踪背压策略</text>
  
  <!-- 背压策略配置 -->
  <rect x="80" y="670" width="500" height="110" class="service-module"/>
  <text x="330" y="690" text-anchor="middle" class="module-title">背压策略配置</text>
  <text x="90" y="710" class="code">val imageAnalysis = ImageAnalysis.Builder()</text>
  <text x="90" y="725" class="code">  .setResolutionSelector(resolutionStrategy)</text>
  <text x="90" y="740" class="code">  .setBackpressureStrategy(</text>
  <text x="90" y="755" class="code">    ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)</text>
  <text x="90" y="770" class="code">  .build()</text>
  
  <!-- 数据流控制 -->
  <rect x="600" y="670" width="500" height="110" class="service-module"/>
  <text x="850" y="690" text-anchor="middle" class="module-title">数据流控制机制</text>
  <text x="610" y="710" class="text">• STRATEGY_KEEP_ONLY_LATEST策略</text>
  <text x="610" y="725" class="text">• 丢弃旧帧，保留最新帧</text>
  <text x="610" y="740" class="text">• 避免图像处理队列堆积</text>
  <text x="610" y="755" class="text">• 确保实时性能表现</text>
  <text x="610" y="770" class="text">• 30fps稳定输出</text>
  
  <!-- 协程异步处理 -->
  <rect x="1120" y="670" width="500" height="110" class="service-module"/>
  <text x="1370" y="690" text-anchor="middle" class="module-title">协程异步处理</text>
  <text x="1130" y="710" class="code">lifecycleScope.launch(Dispatchers.IO) {</text>
  <text x="1130" y="725" class="code">  val result = gazeTrack.gazeTracking(image)</text>
  <text x="1130" y="740" class="code">  withContext(Dispatchers.Main) {</text>
  <text x="1130" y="755" class="code">    updateUI(result)</text>
  <text x="1130" y="770" class="code">  }</text>

  <!-- 架构优势总结 -->
  <rect x="50" y="820" width="1700" height="120" class="activity-layer"/>
  <text x="900" y="845" text-anchor="middle" class="section-title">架构优势与设计亮点</text>
  
  <text x="80" y="870" class="text">• 进程隔离：GazeTrackService独立进程运行，避免主进程阻塞，提高系统稳定性</text>
  <text x="80" y="890" class="text">• 消息机制：Messenger进程间通信，确保服务连接状态下的可靠消息传递</text>
  <text x="80" y="910" class="text">• 原子操作：AtomicBoolean/AtomicReference保证多线程环境下的状态一致性</text>
  <text x="80" y="930" class="text">• 背压控制：STRATEGY_KEEP_ONLY_LATEST策略确保实时性能，避免内存溢出</text>

  <!-- 连接线 -->
  <path class="arrow" d="M 480 145 L 500 145"/>
  <path class="arrow" d="M 900 145 L 920 145"/>
  <path class="arrow" d="M 1320 145 L 1340 145"/>
  
  <path class="dashed-arrow" d="M 900 200 L 900 220"/>
  <path class="dashed-arrow" d="M 900 420 L 900 440"/>
  <path class="dashed-arrow" d="M 900 600 L 900 620"/>
  <path class="dashed-arrow" d="M 900 800 L 900 820"/>

</svg>
