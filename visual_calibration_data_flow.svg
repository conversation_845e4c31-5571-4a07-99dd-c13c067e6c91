<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="cppGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="websocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1700" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">视标校准数据流程：C++ → WebSocket → UI调整</text>
  
  <!-- 核心确认 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">✅ 确认：视标校准确实使用相同机制 - C++计算 → WebSocket传输 → UI调整</text>
  <text x="70" y="110" class="text">• 但视标校准传输两种数据：CalibrationResult(校准进度) 和 CalibrateCoordinate(视标位置)</text>
  <text x="70" y="125" class="text">• UI根据这两种数据分别控制视标动画效果和视标位置移动</text>
  
  <!-- 视标校准数据流程概览 -->
  <rect x="50" y="160" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">视标校准数据流程概览</text>
  
  <!-- C++计算 -->
  <rect x="80" y="210" width="250" height="100" rx="8" fill="url(#cppGradient)"/>
  <text x="205" y="235" text-anchor="middle" class="subtitle">1. C++算法计算</text>
  <text x="205" y="255" text-anchor="middle" class="highlight">GazeCalibrate</text>
  
  <text x="100" y="280" class="text">• 眼动数据收集</text>
  <text x="100" y="300" class="text">• 校准进度计算</text>
  
  <!-- WebSocket传输 -->
  <rect x="370" y="210" width="250" height="100" rx="8" fill="url(#websocketGradient)"/>
  <text x="495" y="235" text-anchor="middle" class="subtitle">2. WebSocket传输</text>
  <text x="495" y="255" text-anchor="middle" class="highlight">两种消息类型</text>
  
  <text x="390" y="280" class="text">• CalibrationResult</text>
  <text x="390" y="300" class="text">• CalibrateCoordinate</text>
  
  <!-- UI接收 -->
  <rect x="660" y="210" width="250" height="100" rx="8" fill="url(#uiGradient)"/>
  <text x="785" y="235" text-anchor="middle" class="subtitle">3. UI层接收</text>
  <text x="785" y="255" text-anchor="middle" class="highlight">LiveData观察</text>
  
  <text x="680" y="280" class="text">• 校准进度更新</text>
  <text x="680" y="300" class="text">• 视标位置调整</text>
  
  <!-- UI调整 -->
  <rect x="950" y="210" width="250" height="100" rx="8" fill="url(#uiGradient)"/>
  <text x="1075" y="235" text-anchor="middle" class="subtitle">4. UI界面调整</text>
  <text x="1075" y="255" text-anchor="middle" class="highlight">VisualCalibrationView</text>
  
  <text x="970" y="280" class="text">• 视标动画控制</text>
  <text x="970" y="300" class="text">• 视标位置移动</text>
  
  <!-- 连接箭头 -->
  <line x1="330" y1="260" x2="370" y2="260" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="620" y1="260" x2="660" y2="260" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  <line x1="910" y1="260" x2="950" y2="260" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowheadRed)"/>
  
  <!-- 两种数据类型详解 -->
  <rect x="50" y="480" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="505" text-anchor="middle" class="subtitle">两种数据类型详解</text>
  
  <!-- CalibrationResult -->
  <rect x="80" y="530" width="800" height="320" rx="8" fill="url(#websocketGradient)"/>
  <text x="480" y="555" text-anchor="middle" class="subtitle">1. CalibrationResult - 校准进度数据</text>
  
  <text x="100" y="580" class="highlight">数据结构:</text>
  <text x="120" y="600" class="code">data class CalibrationResult(</text>
  <text x="140" y="620" class="code">var calibFinish: Boolean = false,    // 校准是否完成</text>
  <text x="140" y="640" class="code">var leftConsistNum: Int = -1,        // 左眼校准进度 [0,1,2,3]</text>
  <text x="140" y="660" class="code">var rightConsistNum: Int = -1        // 右眼校准进度 [0,1,2,3]</text>
  <text x="120" y="680" class="code">)</text>
  
  <text x="100" y="710" class="highlight">C++计算来源:</text>
  <text x="120" y="730" class="code">eye_consist_num = calibration_class.run_calibrating(det_result, ...)</text>
  <text x="120" y="750" class="text">• 基于眼动检测结果计算注视稳定性</text>
  <text x="120" y="770" class="text">• 当≥2时表示注视有效，可以开始旋转动画</text>
  
  <text x="100" y="800" class="highlight">UI响应:</text>
  <text x="120" y="820" class="code">if (result.leftConsistNum >= 2 && result.rightConsistNum >= 2) {</text>
  <text x="140" y="840" class="code">startVisualRotationAnim()  // 开始旋转动画</text>
  
  <!-- CalibrateCoordinate -->
  <rect x="920" y="530" width="800" height="320" rx="8" fill="url(#uiGradient)"/>
  <text x="1320" y="555" text-anchor="middle" class="subtitle">2. CalibrateCoordinate - 视标位置数据</text>
  
  <text x="940" y="580" class="highlight">数据结构:</text>
  <text x="960" y="600" class="code">data class CalibrateCoordinate(</text>
  <text x="980" y="620" class="code">val x: Float,           // 视标X坐标 [0,1]</text>
  <text x="980" y="640" class="code">val y: Float,           // 视标Y坐标 [0,1]</text>
  <text x="980" y="660" class="code">val index: Int,         // 校准点索引 [0-8]</text>
  <text x="980" y="680" class="code">val state: Boolean,     // 校准是否完成</text>
  <text x="980" y="700" class="code">val succeed: Boolean,   // 校准是否成功</text>
  <text x="980" y="720" class="code">val score: Float        // 校准评分 [0,1]</text>
  <text x="960" y="740" class="code">)</text>
  
  <text x="940" y="770" class="highlight">C++计算来源:</text>
  <text x="960" y="790" class="code">callback_calib(finish_calibration, calibrate_succeed, calib_score,</text>
  <text x="980" y="810" class="code">decimal_left, decimal_right, x, y, index)</text>
  
  <text x="940" y="840" class="highlight">UI响应:</text>
  <text x="960" y="860" class="code">updateVisualPoint() // 移动视标到新位置</text>
  
  <!-- UI调整的具体实现 -->
  <rect x="50" y="900" width="1700" height="450" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="925" text-anchor="middle" class="subtitle">UI调整的具体实现</text>
  
  <!-- 校准进度UI调整 -->
  <rect x="80" y="950" width="800" height="180" rx="8" fill="#fff3e0"/>
  <text x="480" y="975" text-anchor="middle" class="subtitle">校准进度UI调整 - setCalibrationResult()</text>
  
  <text x="100" y="1000" class="code">fun setCalibrationResult(result: CalibrationResult) {</text>
  <text x="120" y="1020" class="code">if (isVisualReady.get()) {  // 视标准备就绪时</text>
  <text x="140" y="1040" class="code">if (result.leftConsistNum >= 2 && result.rightConsistNum >= 2) {</text>
  <text x="160" y="1060" class="code">startVisualRotationAnim()  // 开始旋转动画</text>
  <text x="140" y="1080" class="code">} else {</text>
  <text x="160" y="1100" class="code">stopVisualRotationAnim()   // 停止旋转动画</text>
  <text x="140" y="1120" class="code">}</text>
  <text x="120" y="1140" class="code">}</text>
  <text x="100" y="1160" class="code">}</text>
  
  <!-- 视标位置UI调整 -->
  <rect x="920" y="950" width="800" height="180" rx="8" fill="#e8f5e8"/>
  <text x="1320" y="975" text-anchor="middle" class="subtitle">视标位置UI调整 - setCalibrateCoordinate()</text>
  
  <text x="940" y="1000" class="code">fun setCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate) {</text>
  <text x="960" y="1020" class="code">if (calibrateCoordinate.state) {  // 校准完成</text>
  <text x="980" y="1040" class="code">playExplosionAnimation()  // 播放爆炸动画</text>
  <text x="980" y="1060" class="code">visualCalibrationComplete()  // 校准完成</text>
  <text x="960" y="1080" class="code">} else {  // 移动到下一个点</text>
  <text x="980" y="1100" class="code">updateVisualPoint(calibrateCoordinate)  // 更新视标位置</text>
  <text x="980" y="1120" class="code">notifyVisualReady(true)  // 通知准备就绪</text>
  <text x="960" y="1140" class="code">}</text>
  <text x="940" y="1160" class="code">}</text>
  
  <!-- 视标移动动画详解 -->
  <rect x="80" y="1150" width="1600" height="180" rx="8" fill="#f8f9fa"/>
  <text x="880" y="1175" text-anchor="middle" class="subtitle">视标移动动画详解 - updateVisualPoint()</text>
  
  <text x="100" y="1200" class="code">private fun updateVisualPoint(calibrateCoordinate: CalibrateCoordinate, animCompleted: () -> Unit) {</text>
  <text x="120" y="1220" class="code">val point = VisualPoint(calibrateCoordinate.x, calibrateCoordinate.y)</text>
  <text x="120" y="1240" class="code">// 计算屏幕像素位置</text>
  <text x="120" y="1260" class="code">val targetX = point.x * screenWidth - visualWidth / 2</text>
  <text x="120" y="1280" class="code">val targetY = point.y * screenHeight - visualHeight / 2</text>
  <text x="120" y="1300" class="code">// 创建移动动画</text>
  <text x="120" y="1320" class="code">val translationAnim = ValueAnimator.ofObject(PointFEvaluator(), currentPos, targetPos)</text>
  <text x="100" y="1340" class="code">}</text>
  
  <text x="900" y="1200" class="text">• <tspan class="highlight">坐标转换:</tspan> 归一化坐标[0,1] → 屏幕像素坐标</text>
  <text x="900" y="1220" class="text">• <tspan class="highlight">动画效果:</tspan> 平移动画 + 透明度动画</text>
  <text x="900" y="1240" class="text">• <tspan class="highlight">爆炸效果:</tspan> 完成一个点后播放爆炸动画</text>
  <text x="900" y="1260" class="text">• <tspan class="highlight">回调通知:</tspan> 动画完成后通知C++层准备就绪</text>
  
  <!-- WebSocket消息格式 -->
  <rect x="50" y="1370" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1395" text-anchor="middle" class="subtitle">WebSocket消息格式示例</text>
  
  <!-- CalibrationResult消息 -->
  <rect x="80" y="1420" width="800" height="180" rx="8" fill="#e3f2fd"/>
  <text x="480" y="1445" text-anchor="middle" class="subtitle">CalibrationResult 消息</text>
  
  <text x="100" y="1470" class="code">{</text>
  <text x="120" y="1490" class="code">"action": "ACTION_CALIBRATION_RESULT",</text>
  <text x="120" y="1510" class="code">"data": {</text>
  <text x="140" y="1530" class="code">"calib_finish": false,</text>
  <text x="140" y="1550" class="code">"left_consist_num": 2,</text>
  <text x="140" y="1570" class="code">"right_consist_num": 1</text>
  <text x="120" y="1590" class="code">}</text>
  <text x="100" y="1610" class="code">}</text>
  
  <!-- CalibrateCoordinate消息 -->
  <rect x="920" y="1420" width="800" height="180" rx="8" fill="#e8f5e8"/>
  <text x="1320" y="1445" text-anchor="middle" class="subtitle">CalibrateCoordinate 消息</text>
  
  <text x="940" y="1470" class="code">{</text>
  <text x="960" y="1490" class="code">"action": "ACTION_CALIBRATE_COORDINATE",</text>
  <text x="960" y="1510" class="code">"data": {</text>
  <text x="980" y="1530" class="code">"x": 0.5, "y": 0.5, "index": 4,</text>
  <text x="980" y="1550" class="code">"state": false, "succeed": true,</text>
  <text x="980" y="1570" class="code">"score": 0.85</text>
  <text x="960" y="1590" class="code">}</text>
  <text x="940" y="1610" class="code">}</text>
  
  <!-- 总结 -->
  <rect x="100" y="1640" width="1600" height="40" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1665" text-anchor="middle" class="highlight">
    总结：视标校准使用相同的WebSocket机制，但传输两种数据类型，分别控制视标动画和位置移动
  </text>
</svg>
