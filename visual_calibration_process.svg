<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; fill: #666; }
      .label { font-family: Arial, sans-serif; font-size: 14px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #555; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #333; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .grid-line { stroke: #ddd; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <radialGradient id="starGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA000;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="explosionGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F7931E;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFD23F;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">视标校准过程详解</text>
  
  <!-- 校准点网格 -->
  <rect x="50" y="60" width="400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="250" y="85" text-anchor="middle" class="subtitle">9点校准网格 (3×3)</text>
  
  <!-- 绘制3x3网格 -->
  <g transform="translate(80, 110)">
    <!-- 网格线 -->
    <line x1="0" y1="0" x2="340" y2="0" class="grid-line"/>
    <line x1="0" y1="120" x2="340" y2="120" class="grid-line"/>
    <line x1="0" y1="240" x2="340" y2="240" class="grid-line"/>
    <line x1="0" y1="0" x2="0" y2="240" class="grid-line"/>
    <line x1="170" y1="0" x2="170" y2="240" class="grid-line"/>
    <line x1="340" y1="0" x2="340" y2="240" class="grid-line"/>
    
    <!-- 校准点 -->
    <circle cx="34" cy="24" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="34" y="45" text-anchor="middle" class="small-text">0</text>
    <text x="34" y="10" text-anchor="middle" class="small-text">(0.1,0.1)</text>
    
    <circle cx="170" cy="24" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="170" y="45" text-anchor="middle" class="small-text">1</text>
    <text x="170" y="10" text-anchor="middle" class="small-text">(0.5,0.1)</text>
    
    <circle cx="306" cy="24" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="306" y="45" text-anchor="middle" class="small-text">2</text>
    <text x="306" y="10" text-anchor="middle" class="small-text">(0.9,0.1)</text>
    
    <circle cx="34" cy="120" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="34" y="140" text-anchor="middle" class="small-text">3</text>
    
    <circle cx="170" cy="120" r="12" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
    <text x="170" y="140" text-anchor="middle" class="small-text">4 (中心)</text>
    <text x="170" y="105" text-anchor="middle" class="small-text">(0.5,0.5)</text>
    
    <circle cx="306" cy="120" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="306" y="140" text-anchor="middle" class="small-text">5</text>
    
    <circle cx="34" cy="216" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="34" y="235" text-anchor="middle" class="small-text">6</text>
    
    <circle cx="170" cy="216" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="170" y="235" text-anchor="middle" class="small-text">7</text>
    
    <circle cx="306" cy="216" r="8" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="306" y="235" text-anchor="middle" class="small-text">8</text>
  </g>
  
  <!-- 校准顺序 -->
  <rect x="480" y="60" width="200" height="300" fill="#fff" stroke="#007bff" stroke-width="2" rx="8"/>
  <text x="580" y="85" text-anchor="middle" class="subtitle">校准顺序</text>
  
  <text x="500" y="110" class="label">point_index_order[]:</text>
  <text x="500" y="130" class="code-text">{4, 5, 8, 7, 6, 3, 0, 1, 2}</text>
  
  <text x="500" y="160" class="small-text">1. 中心点 (4) → 建立基准</text>
  <text x="500" y="180" class="small-text">2. 右中 (5) → 水平校准</text>
  <text x="500" y="200" class="small-text">3. 右下 (8) → 对角校准</text>
  <text x="500" y="220" class="small-text">4. 下中 (7) → 垂直校准</text>
  <text x="500" y="240" class="small-text">5. 左下 (6) → 对角校准</text>
  <text x="500" y="260" class="small-text">6. 左中 (3) → 水平校准</text>
  <text x="500" y="280" class="small-text">7. 左上 (0) → 对角校准</text>
  <text x="500" y="300" class="small-text">8. 上中 (1) → 垂直校准</text>
  <text x="500" y="320" class="small-text">9. 右上 (2) → 完成闭环</text>
  
  <!-- 动画状态 -->
  <rect x="720" y="60" width="320" height="300" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="8"/>
  <text x="880" y="85" text-anchor="middle" class="subtitle">视标动画状态</text>
  
  <!-- 移动动画 -->
  <g transform="translate(750, 110)">
    <text x="0" y="0" class="label">1. 移动动画 (600ms)</text>
    <circle cx="30" cy="20" r="8" fill="url(#starGradient)" opacity="0.3"/>
    <circle cx="80" cy="20" r="8" fill="url(#starGradient)" opacity="0.6"/>
    <circle cx="130" cy="20" r="8" fill="url(#starGradient)"/>
    <path d="M 38 20 Q 65 10 72 20" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
    <text x="65" y="5" text-anchor="middle" class="small-text">平移轨迹</text>
    <text x="0" y="45" class="code-text">ValueAnimator.ofObject(PointFEvaluator())</text>
  </g>
  
  <!-- 等待状态 -->
  <g transform="translate(750, 170)">
    <text x="0" y="0" class="label">2. 等待注视</text>
    <circle cx="50" cy="20" r="12" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2"/>
    <text x="0" y="45" class="small-text">静态显示，等待眼动数据</text>
  </g>
  
  <!-- 旋转动画 -->
  <g transform="translate(750, 220)">
    <text x="0" y="0" class="label">3. 数据收集 (旋转动画)</text>
    <circle cx="50" cy="20" r="10" fill="url(#starGradient)" stroke="#FF8F00" stroke-width="2">
      <animateTransform attributeName="transform" type="rotate" values="0 50 20;360 50 20" dur="1s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="45" class="code-text">rotation: 0° → 360° (300ms)</text>
    <text x="0" y="60" class="code-text">scale: 1.0 → 0.8 (缩放效果)</text>
  </g>
  
  <!-- 爆破动画 -->
  <g transform="translate(750, 280)">
    <text x="0" y="0" class="label">4. 爆破动画</text>
    <circle cx="50" cy="20" r="15" fill="url(#explosionGradient)" opacity="0.8"/>
    <circle cx="45" cy="15" r="8" fill="#FF6B35" opacity="0.6"/>
    <circle cx="55" cy="25" r="6" fill="#FFD23F" opacity="0.7"/>
    <text x="0" y="45" class="code-text">GIF: visual_logo_explosion.gif</text>
  </g>
  
  <!-- 数据收集过程 -->
  <rect x="1080" y="60" width="280" height="300" fill="#fff" stroke="#28a745" stroke-width="2" rx="8"/>
  <text x="1220" y="85" text-anchor="middle" class="subtitle">数据收集机制</text>
  
  <text x="1100" y="115" class="label">一致性检测:</text>
  <text x="1100" y="135" class="small-text">• 左眼一致性 ≥ 2次</text>
  <text x="1100" y="155" class="small-text">• 右眼一致性 ≥ 2次</text>
  
  <text x="1100" y="185" class="label">收集的特征:</text>
  <text x="1100" y="205" class="code-text">pupil_x, pupil_y</text>
  <text x="1100" y="220" class="code-text">light_p_lx, light_p_ly</text>
  <text x="1100" y="235" class="code-text">light_p_rx, light_p_ry</text>
  <text x="1100" y="250" class="code-text">eye_dist</text>
  <text x="1100" y="265" class="code-text">screen_x, screen_y</text>
  
  <text x="1100" y="295" class="label">校准状态编码:</text>
  <text x="1100" y="315" class="code-text">calibLeft: 9位二进制</text>
  <text x="1100" y="330" class="code-text">calibRight: 9位二进制</text>
  
  <!-- 算法流程 -->
  <rect x="50" y="400" width="600" height="450" fill="#f8f9fa" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="350" y="430" text-anchor="middle" class="subtitle">最小二乘法校准算法</text>
  
  <!-- 特征向量构建 -->
  <rect x="80" y="450" width="250" height="180" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="205" y="475" text-anchor="middle" class="label">1. 特征向量构建</text>
  <text x="90" y="500" class="code-text">calculate_feature_vector_func()</text>
  <text x="90" y="520" class="small-text">分离左眼、右眼、中心点数据</text>
  <text x="90" y="540" class="small-text">构建X方向和Y方向特征矩阵:</text>
  <text x="90" y="560" class="code-text">crs_feat_vec_x, crs_feat_vec_y</text>
  <text x="90" y="580" class="code-text">left_feat_vec_x, left_feat_vec_y</text>
  <text x="90" y="600" class="code-text">right_feat_vec_x, right_feat_vec_y</text>
  
  <!-- 最小二乘求解 -->
  <rect x="350" y="450" width="280" height="180" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="490" y="475" text-anchor="middle" class="label">2. 最小二乘求解</text>
  <text x="360" y="500" class="code-text">solveLeastSquares(data)</text>
  <text x="360" y="520" class="small-text">设计矩阵 A(n, num_params)</text>
  <text x="360" y="540" class="small-text">输出向量 b(n)</text>
  <text x="360" y="560" class="code-text">A.householderQr().solve(b)</text>
  <text x="360" y="580" class="small-text">生成映射系数:</text>
  <text x="360" y="600" class="code-text">result_C_x, result_C_y (中心)</text>
  <text x="360" y="615" class="code-text">result_L_x, result_L_y (左眼)</text>
  
  <!-- 质量评估 -->
  <rect x="80" y="650" width="250" height="180" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="205" y="675" text-anchor="middle" class="label">3. 质量评估</text>
  <text x="90" y="700" class="code-text">calculate_rmse()</text>
  <text x="90" y="720" class="small-text">计算预测值与实际值差异</text>
  <text x="90" y="740" class="code-text">residuals = result - prediction</text>
  <text x="90" y="760" class="code-text">rmse = sqrt(mean(residuals²))</text>
  <text x="90" y="780" class="small-text">RMSE评分范围: 0-1</text>
  <text x="90" y="800" class="small-text">评分越低，校准质量越好</text>
  
  <!-- 映射矩阵生成 -->
  <rect x="350" y="650" width="280" height="180" fill="#fff" stroke="#ccc" rx="5"/>
  <text x="490" y="675" text-anchor="middle" class="label">4. 映射矩阵生成</text>
  <text x="360" y="700" class="small-text">warpMatrix结构 (6×2矩阵):</text>
  <text x="360" y="720" class="code-text">warpMatrix_C: [result_C_x, result_C_y]</text>
  <text x="360" y="740" class="code-text">warpMatrix_L: [result_L_x, result_L_y]</text>
  <text x="360" y="760" class="code-text">warpMatrix_R: [result_R_x, result_R_y]</text>
  <text x="360" y="780" class="small-text">用于后续眼动追踪时的</text>
  <text x="360" y="800" class="small-text">坐标映射转换</text>
  
  <!-- 音频反馈 -->
  <rect x="700" y="400" width="650" height="450" fill="#fff3cd" stroke="#856404" stroke-width="2" rx="10"/>
  <text x="1025" y="430" text-anchor="middle" class="subtitle">音频反馈系统</text>
  
  <!-- 语音文件列表 -->
  <rect x="730" y="450" width="280" height="200" fill="#fff" stroke="#ffc107" rx="5"/>
  <text x="870" y="475" text-anchor="middle" class="label">语音文件</text>
  <text x="740" y="500" class="small-text">🔊 calibration_watch_stars_until_explode</text>
  <text x="740" y="520" class="small-text">🔊 calibration_visual_success</text>
  <text x="740" y="540" class="small-text">🔊 calibration_visual_failed</text>
  <text x="740" y="560" class="small-text">🔊 calibration_success</text>
  <text x="740" y="580" class="small-text">🔊 calibration_failure</text>
  
  <text x="740" y="610" class="label">播放时机:</text>
  <text x="740" y="630" class="small-text">• 开始: "注视星星直到爆破"</text>
  <text x="740" y="645" class="small-text">• 单点成功/失败音效</text>
  
  <!-- 技术实现 -->
  <rect x="1030" y="450" width="300" height="380" fill="#fff" stroke="#6c757d" rx="5"/>
  <text x="1180" y="475" text-anchor="middle" class="label">技术实现细节</text>
  
  <text x="1040" y="505" class="label">动画实现:</text>
  <text x="1040" y="525" class="code-text">ObjectAnimator.ofFloat()</text>
  <text x="1040" y="540" class="code-text">ValueAnimator.ofObject()</text>
  <text x="1040" y="555" class="code-text">AnimatorSet.playTogether()</text>
  
  <text x="1040" y="585" class="label">布局更新:</text>
  <text x="1040" y="605" class="code-text">ConstraintSet.clone()</text>
  <text x="1040" y="620" class="code-text">ConstraintSet.setMargin()</text>
  <text x="1040" y="635" class="code-text">ConstraintSet.applyTo()</text>
  
  <text x="1040" y="665" class="label">GIF动画:</text>
  <text x="1040" y="685" class="code-text">Glide.asGif().load()</text>
  <text x="1040" y="700" class="code-text">setLoopCount(1)</text>
  <text x="1040" y="715" class="code-text">AnimationCallback()</text>
  
  <text x="1040" y="745" class="label">状态管理:</text>
  <text x="1040" y="765" class="code-text">AtomicBoolean isVisualReady</text>
  <text x="1040" y="780" class="code-text">AtomicBoolean isComplete</text>
  
  <text x="1040" y="810" class="label">坐标转换:</text>
  <text x="1040" y="825" class="code-text">point.x * screenWidth</text>
  
  <!-- 连接箭头 -->
  <line x1="450" y1="250" x2="720" y2="200" class="arrow"/>
  <line x1="1040" y1="250" x2="1080" y2="200" class="arrow"/>
  <line x1="350" y1="380" x2="350" y2="420" class="arrow"/>
</svg>
