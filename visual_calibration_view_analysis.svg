<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .description { font-family: Arial, sans-serif; font-size: 11px; fill: #34495e; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #27ae60; }
      .highlight { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #e74c3c; }
      .step-box { fill: #ffffff; stroke: #bdc3c7; stroke-width: 2; }
      .process-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .animation-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .algorithm-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; }
      .data-box { fill: #e8f8f5; stroke: #27ae60; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#redarrow); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="redarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 标题 -->
  <rect x="0" y="0" width="1600" height="80" fill="url(#headerGradient)"/>
  <text x="800" y="35" text-anchor="middle" class="title" fill="white">VisualCalibrationView 视觉校准实现详解</text>
  <text x="800" y="60" text-anchor="middle" class="description" fill="white">9点校准 + 动画效果 + 实时反馈 + 音频提示</text>

  <!-- 核心组件结构 -->
  <rect x="50" y="100" width="1500" height="180" class="process-box" rx="10"/>
  <text x="70" y="130" class="section-title" fill="#3498db">核心组件结构</text>

  <!-- 布局结构 -->
  <rect x="80" y="150" width="300" height="110" class="step-box" rx="8"/>
  <text x="90" y="175" class="subtitle">布局结构</text>
  <text x="90" y="195" class="code">ConstraintLayout (容器)</text>
  <text x="90" y="210" class="code">├── ImageView (视标点)</text>
  <text x="90" y="225" class="code">│   ├── 180dp × 180dp</text>
  <text x="90" y="240" class="code">│   └── 动态位置调整</text>
  <text x="90" y="255" class="code">└── TextView (提示文本)</text>

  <!-- 关键属性 -->
  <rect x="400" y="150" width="300" height="110" class="step-box" rx="8"/>
  <text x="410" y="175" class="subtitle">关键属性</text>
  <text x="410" y="195" class="description">• isVisualReady: 视标准备状态</text>
  <text x="410" y="210" class="description">• lastPointIndex: 前一个校准点索引</text>
  <text x="410" y="225" class="description">• screenWidth/Height: 屏幕尺寸</text>
  <text x="410" y="240" class="description">• visualAnimSet: 动画集合</text>
  <text x="410" y="255" class="description">• isCalibrationComplete: 校准完成标志</text>

  <!-- 回调接口 -->
  <rect x="720" y="150" width="300" height="110" class="step-box" rx="8"/>
  <text x="730" y="175" class="subtitle">回调接口</text>
  <text x="730" y="195" class="code">onCalibrationComplete</text>
  <text x="730" y="210" class="description">  校准完成回调(成功/失败)</text>
  <text x="730" y="225" class="code">onNotifyVisualReady</text>
  <text x="730" y="240" class="description">  视标准备完成通知</text>
  <text x="730" y="255" class="description">  与外部组件通信</text>

  <!-- 初始化 -->
  <rect x="1040" y="150" width="300" height="110" class="step-box" rx="8"/>
  <text x="1050" y="175" class="subtitle">初始化过程</text>
  <text x="1050" y="195" class="description">1. 加载布局文件</text>
  <text x="1050" y="210" class="description">2. 播放语音提示</text>
  <text x="1050" y="225" class="code">"请注视星星，直到星星爆破"</text>
  <text x="1050" y="240" class="description">3. 3秒后隐藏提示文本</text>
  <text x="1050" y="255" class="description">4. 准备接收校准数据</text>

  <!-- 核心算法流程 -->
  <rect x="50" y="300" width="1500" height="200" class="algorithm-box" rx="10"/>
  <text x="70" y="330" class="section-title" fill="#9b59b6">核心算法流程</text>

  <!-- 9点校准算法 -->
  <rect x="80" y="350" width="280" height="130" class="step-box" rx="8"/>
  <text x="90" y="375" class="subtitle">9点校准算法</text>
  <text x="90" y="395" class="description">校准点布局 (3×3网格):</text>
  <text x="90" y="410" class="code">0  1  2</text>
  <text x="90" y="425" class="code">3  4  5</text>
  <text x="90" y="440" class="code">6  7  8</text>
  <text x="90" y="455" class="description">• 坐标范围: (0,0) 到 (1,1)</text>
  <text x="90" y="470" class="description">• 按屏幕比例计算实际位置</text>

  <!-- 二进制编码解析 -->
  <rect x="380" y="350" width="280" height="130" class="step-box" rx="8"/>
  <text x="390" y="375" class="subtitle">二进制编码解析</text>
  <text x="390" y="395" class="code">calibLeft/calibRight (Int)</text>
  <text x="390" y="410" class="description">↓ 转换为9位二进制</text>
  <text x="390" y="425" class="code">例: 341 → "101010101"</text>
  <text x="390" y="440" class="description">↓ 反转字符串</text>
  <text x="390" y="455" class="code">"101010101" → "101010101"</text>
  <text x="390" y="470" class="description">每位代表对应点校准结果</text>

  <!-- 校准状态判断 -->
  <rect x="680" y="350" width="280" height="130" class="step-box" rx="8"/>
  <text x="690" y="375" class="subtitle">校准状态判断</text>
  <text x="690" y="395" class="code">if (calibLeftCode == 1 && </text>
  <text x="690" y="410" class="code">    calibRightCode == 1)</text>
  <text x="690" y="425" class="description">→ 播放成功动画</text>
  <text x="690" y="440" class="code">else</text>
  <text x="690" y="455" class="description">→ 播放失败动画</text>
  <text x="690" y="470" class="description">双眼校准结果综合判断</text>

  <!-- 实时反馈机制 -->
  <rect x="980" y="350" width="280" height="130" class="step-box" rx="8"/>
  <text x="990" y="375" class="subtitle">实时反馈机制</text>
  <text x="990" y="395" class="code">CalibrationResult</text>
  <text x="990" y="410" class="description">leftConsistNum ≥ 2 &&</text>
  <text x="990" y="425" class="description">rightConsistNum ≥ 2</text>
  <text x="990" y="440" class="description">→ 开始旋转动画</text>
  <text x="990" y="455" class="description">→ 表示用户注视良好</text>
  <text x="990" y="470" class="description">实时视觉反馈</text>

  <!-- 动画系统 -->
  <rect x="50" y="520" width="1500" height="220" class="animation-box" rx="10"/>
  <text x="70" y="550" class="section-title" fill="#e67e22">动画系统</text>

  <!-- 视标移动动画 -->
  <rect x="80" y="570" width="350" height="150" class="step-box" rx="8"/>
  <text x="90" y="595" class="subtitle">视标移动动画</text>
  <text x="90" y="615" class="highlight">第一个点:</text>
  <text x="90" y="630" class="description">• 直接设置位置 (ConstraintSet)</text>
  <text x="90" y="645" class="description">• 立即显示，无动画</text>
  <text x="90" y="665" class="highlight">后续点:</text>
  <text x="90" y="680" class="description">• 爆破动画 → 位移动画</text>
  <text x="90" y="695" class="code">ValueAnimator.ofObject(PointFEvaluator)</text>
  <text x="90" y="710" class="description">• 600ms位移 + 300ms淡入</text>

  <!-- 旋转反馈动画 -->
  <rect x="450" y="570" width="350" height="150" class="step-box" rx="8"/>
  <text x="460" y="595" class="subtitle">旋转反馈动画</text>
  <text x="460" y="615" class="code">ObjectAnimator.ofFloat(</text>
  <text x="460" y="630" class="code">  "rotation", 0f, 360f)</text>
  <text x="460" y="645" class="description">• 300ms一圈，无限循环</text>
  <text x="460" y="665" class="code">scaleX/Y: 1.0f → 0.8f</text>
  <text x="460" y="680" class="description">• 缩放效果增强视觉反馈</text>
  <text x="460" y="695" class="description">• 表示用户注视状态良好</text>
  <text x="460" y="710" class="description">• 实时响应校准质量</text>

  <!-- 爆破动画 -->
  <rect x="820" y="570" width="350" height="150" class="step-box" rx="8"/>
  <text x="830" y="595" class="subtitle">爆破动画 (GIF)</text>
  <text x="830" y="615" class="code">Glide.asGif()</text>
  <text x="830" y="630" class="code">.load(R.drawable.visual_logo_explosion)</text>
  <text x="830" y="645" class="description">• 单次播放 (setLoopCount(1))</text>
  <text x="830" y="660" class="description">• 动画结束回调</text>
  <text x="830" y="675" class="code">Animatable2Compat.AnimationCallback</text>
  <text x="830" y="690" class="description">• 校准点完成标志</text>
  <text x="830" y="705" class="description">• 触发下一个点显示</text>

  <!-- 音频反馈 -->
  <rect x="1190" y="570" width="280" height="150" class="step-box" rx="8"/>
  <text x="1200" y="595" class="subtitle">音频反馈系统</text>
  <text x="1200" y="615" class="description">初始化:</text>
  <text x="1200" y="630" class="code">calibration_watch_stars_until_explode</text>
  <text x="1200" y="650" class="description">单点成功:</text>
  <text x="1200" y="665" class="code">calibration_visual_success</text>
  <text x="1200" y="685" class="description">单点失败:</text>
  <text x="1200" y="700" class="code">calibration_visual_failed</text>
  <text x="1200" y="715" class="description">完成: success/failure</text>

  <!-- 数据流程 -->
  <rect x="50" y="760" width="1500" height="180" class="data-box" rx="10"/>
  <text x="70" y="790" class="section-title" fill="#27ae60">数据流程</text>

  <!-- 输入数据 -->
  <rect x="80" y="810" width="280" height="110" class="step-box" rx="8"/>
  <text x="90" y="835" class="subtitle">输入数据</text>
  <text x="90" y="855" class="code">CalibrateCoordinate</text>
  <text x="90" y="870" class="description">• x, y: 校准点坐标 (0-1)</text>
  <text x="90" y="885" class="description">• index: 点索引 (0-8)</text>
  <text x="90" y="900" class="description">• state: 校准状态</text>
  <text x="90" y="915" class="description">• calibLeft/Right: 二进制编码</text>

  <!-- 处理流程 -->
  <rect x="380" y="810" width="280" height="110" class="step-box" rx="8"/>
  <text x="390" y="835" class="subtitle">处理流程</text>
  <text x="390" y="855" class="description">1. 接收校准坐标</text>
  <text x="390" y="870" class="description">2. 判断校准状态</text>
  <text x="390" y="885" class="description">3. 更新视标位置</text>
  <text x="390" y="900" class="description">4. 播放相应动画</text>
  <text x="390" y="915" class="description">5. 触发音频反馈</text>

  <!-- 状态管理 -->
  <rect x="680" y="810" width="280" height="110" class="step-box" rx="8"/>
  <text x="690" y="835" class="subtitle">状态管理</text>
  <text x="690" y="855" class="code">AtomicBoolean isVisualReady</text>
  <text x="690" y="870" class="description">• 线程安全的状态标志</text>
  <text x="690" y="885" class="description">• 防止重复触发</text>
  <text x="690" y="900" class="code">lastPointIndex</text>
  <text x="690" y="915" class="description">• 跟踪校准进度</text>

  <!-- 输出反馈 -->
  <rect x="980" y="810" width="280" height="110" class="step-box" rx="8"/>
  <text x="990" y="835" class="subtitle">输出反馈</text>
  <text x="990" y="855" class="description">• 视觉反馈: 动画效果</text>
  <text x="990" y="870" class="description">• 音频反馈: 语音提示</text>
  <text x="990" y="885" class="description">• 回调通知: 状态变化</text>
  <text x="990" y="900" class="description">• 校准结果: 成功/失败</text>
  <text x="990" y="915" class="description">• 准备状态: 就绪通知</text>

  <!-- 技术特点 -->
  <rect x="1280" y="810" width="240" height="110" class="step-box" rx="8"/>
  <text x="1290" y="835" class="subtitle">技术特点</text>
  <text x="1290" y="855" class="description">• 响应式坐标系统</text>
  <text x="1290" y="870" class="description">• 流畅动画过渡</text>
  <text x="1290" y="885" class="description">• 实时状态反馈</text>
  <text x="1290" y="900" class="description">• 多媒体集成</text>
  <text x="1290" y="915" class="description">• 线程安全设计</text>

  <!-- 流程箭头 -->
  <line x1="230" y1="280" x2="230" y2="300" class="flow-arrow"/>
  <line x1="520" y1="280" x2="520" y2="300" class="flow-arrow"/>
  <line x1="820" y1="280" x2="820" y2="300" class="flow-arrow"/>
  <line x1="1120" y1="280" x2="1120" y2="300" class="flow-arrow"/>

  <line x1="230" y1="500" x2="230" y2="520" class="flow-arrow"/>
  <line x1="520" y1="500" x2="520" y2="520" class="flow-arrow"/>
  <line x1="820" y1="500" x2="820" y2="520" class="flow-arrow"/>
  <line x1="1120" y1="500" x2="1120" y2="520" class="flow-arrow"/>

  <line x1="230" y1="740" x2="230" y2="760" class="flow-arrow"/>
  <line x1="520" y1="740" x2="520" y2="760" class="flow-arrow"/>
  <line x1="820" y1="740" x2="820" y2="760" class="flow-arrow"/>
  <line x1="1120" y1="740" x2="1120" y2="760" class="flow-arrow"/>

  <!-- 关键实现要点 -->
  <rect x="50" y="960" width="1500" height="200" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
  <text x="70" y="990" class="subtitle" fill="#856404">关键实现要点</text>

  <text x="70" y="1015" class="highlight">1. 坐标系统设计</text>
  <text x="70" y="1035" class="description">• 使用相对坐标 (0-1)，适配不同屏幕尺寸</text>
  <text x="70" y="1050" class="description">• ConstraintSet动态调整布局，精确定位视标</text>

  <text x="70" y="1075" class="highlight">2. 动画性能优化</text>
  <text x="70" y="1095" class="description">• AnimatorSet管理复合动画，避免冲突</text>
  <text x="70" y="1110" class="description">• 及时清理动画资源，防止内存泄漏</text>

  <text x="800" y="1015" class="highlight">3. 状态同步机制</text>
  <text x="800" y="1035" class="description">• AtomicBoolean确保线程安全</text>
  <text x="800" y="1050" class="description">• 回调接口实现松耦合通信</text>

  <text x="800" y="1075" class="highlight">4. 用户体验设计</text>
  <text x="800" y="1095" class="description">• 多模态反馈：视觉+听觉+触觉</text>
  <text x="800" y="1110" class="description">• 渐进式引导，降低学习成本</text>

  <!-- 使用示例 -->
  <rect x="50" y="1180" width="1500" height="180" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="10"/>
  <text x="70" y="1210" class="subtitle">使用示例</text>

  <text x="70" y="1235" class="code">// 1. 创建视图</text>
  <text x="70" y="1250" class="code">val visualCalibrationView = VisualCalibrationView(context)</text>

  <text x="70" y="1275" class="code">// 2. 设置回调</text>
  <text x="70" y="1290" class="code">visualCalibrationView.onCalibrationComplete = { success -></text>
  <text x="70" y="1305" class="code">    if (success) { /* 校准成功处理 */ } else { /* 校准失败处理 */ }</text>
  <text x="70" y="1320" class="code">}</text>

  <text x="800" y="1235" class="code">// 3. 接收校准数据</text>
  <text x="800" y="1250" class="code">visualCalibrationView.setCalibrateCoordinate(coordinate)</text>

  <text x="800" y="1275" class="code">// 4. 实时反馈</text>
  <text x="800" y="1290" class="code">visualCalibrationView.setCalibrationResult(result)</text>

  <text x="800" y="1315" class="code">// 5. 状态监听</text>
  <text x="800" y="1330" class="code">visualCalibrationView.onNotifyVisualReady = { ready -> /* 处理准备状态 */ }</text>

</svg>
