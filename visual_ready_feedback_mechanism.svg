<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="problemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef9a9a;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="solutionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
    
    <marker id="arrowheadRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    
    <marker id="arrowheadGreen" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="35" text-anchor="middle" class="title">为什么UI需要向C++反馈视标准备状态？</text>
  
  <!-- 核心问题 -->
  <rect x="50" y="60" width="1700" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="900" y="85" text-anchor="middle" class="highlight">🎯 核心问题：视标校准需要UI动画和C++算法的精确同步</text>
  <text x="70" y="110" class="text">• C++算法需要知道视标何时移动完成，才能开始收集眼动数据</text>
  <text x="70" y="125" class="text">• 如果在视标移动过程中收集数据，会导致校准不准确</text>
  
  <!-- 问题分析 -->
  <rect x="50" y="160" width="1700" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="185" text-anchor="middle" class="subtitle">问题分析：没有反馈机制会发生什么？</text>
  
  <rect x="80" y="210" width="1600" height="230" rx="8" fill="url(#problemGradient)"/>
  <text x="880" y="235" text-anchor="middle" class="subtitle">❌ 时序不同步导致的问题</text>
  
  <!-- 问题场景1 -->
  <text x="100" y="265" class="highlight">问题场景1: 视标移动期间收集数据</text>
  <text x="120" y="285" class="text">1. C++发送新的视标坐标给UI</text>
  <text x="120" y="305" class="text">2. UI开始播放移动动画（耗时600ms）</text>
  <text x="120" y="325" class="text">3. C++立即开始收集眼动数据 ❌</text>
  <text x="120" y="345" class="text">4. 用户眼睛还在追踪移动中的视标</text>
  <text x="120" y="365" class="text">5. 收集到的数据不是用户注视固定点的数据</text>
  
  <!-- 问题场景2 -->
  <text x="900" y="265" class="highlight">问题场景2: 用户体验混乱</text>
  <text x="920" y="285" class="text">1. 视标还在移动，但已经开始旋转动画</text>
  <text x="920" y="305" class="text">2. 用户不知道应该看哪里</text>
  <text x="920" y="325" class="text">3. 校准指令与视觉反馈不一致</text>
  <text x="920" y="345" class="text">4. 导致校准失败或精度下降</text>
  
  <!-- 结果 -->
  <text x="100" y="395" class="highlight">结果：</text>
  <text x="120" y="415" class="text">• 校准数据不准确，影响后续眼动追踪精度</text>
  <text x="120" y="435" class="text">• 用户体验差，不知道何时开始注视</text>
  
  <!-- 解决方案 -->
  <rect x="50" y="480" width="1700" height="350" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="505" text-anchor="middle" class="subtitle">解决方案：notifyVisualReady() 反馈机制</text>
  
  <rect x="80" y="530" width="1600" height="280" rx="8" fill="url(#solutionGradient)"/>
  <text x="880" y="555" text-anchor="middle" class="subtitle">✅ 精确的时序同步机制</text>
  
  <!-- 反馈机制流程 -->
  <text x="100" y="585" class="highlight">反馈机制工作流程：</text>
  
  <text x="120" y="610" class="code">1. C++发送新视标坐标 → UI</text>
  <text x="140" y="630" class="code">setCalibrateCoordinate(calibrateCoordinate)</text>
  
  <text x="120" y="655" class="code">2. UI立即反馈"未准备好" → C++</text>
  <text x="140" y="675" class="code">notifyVisualReady(false)  // 告诉C++暂停数据收集</text>
  
  <text x="120" y="700" class="code">3. UI播放移动动画（600ms）</text>
  <text x="140" y="720" class="code">updateVisualPoint(calibrateCoordinate) { animCompleted() }</text>
  
  <text x="120" y="745" class="code">4. 动画完成后反馈"准备好" → C++</text>
  <text x="140" y="765" class="code">notifyVisualReady(true)   // 告诉C++可以开始收集数据</text>
  
  <text x="120" y="790" class="code">5. C++开始收集眼动数据</text>
  <text x="140" y="810" class="code">calibration_class.run_calibrating(det_result, ...)</text>
  
  <!-- 关键代码实现 -->
  <rect x="50" y="850" width="1700" height="400" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="875" text-anchor="middle" class="subtitle">关键代码实现</text>
  
  <!-- UI层实现 -->
  <rect x="80" y="900" width="800" height="320" rx="8" fill="#fff3e0"/>
  <text x="480" y="925" text-anchor="middle" class="subtitle">UI层实现 - VisualCalibrationView</text>
  
  <text x="100" y="950" class="code">fun setCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate) {</text>
  <text x="120" y="970" class="code">if (calibrateCoordinate.state) {</text>
  <text x="140" y="990" class="code">// 校准完成</text>
  <text x="140" y="1010" class="code">isVisualReady.set(false)</text>
  <text x="140" y="1030" class="code">playExplosionAnimation() { visualCalibrationComplete() }</text>
  <text x="120" y="1050" class="code">} else {</text>
  <text x="140" y="1070" class="code">// 移动到新位置</text>
  <text x="140" y="1090" class="code">notifyVisualReady(false)  // 立即告知未准备好</text>
  <text x="140" y="1110" class="code">stopVisualRotationAnim()</text>
  <text x="140" y="1130" class="code">updateVisualPoint(calibrateCoordinate) {</text>
  <text x="160" y="1150" class="code">notifyVisualReady(true)  // 动画完成后告知准备好</text>
  <text x="140" y="1170" class="code">}</text>
  <text x="120" y="1190" class="code">}</text>
  <text x="100" y="1210" class="code">}</text>
  
  <!-- Service层实现 -->
  <rect x="920" y="900" width="800" height="320" rx="8" fill="#e3f2fd"/>
  <text x="1320" y="925" text-anchor="middle" class="subtitle">Service层实现 - GazeTrackService</text>
  
  <text x="940" y="950" class="code">// CalibrationActivity</text>
  <text x="940" y="970" class="code">private fun notifyVisualReady(isReady: Boolean) {</text>
  <text x="960" y="990" class="code">sendMessageToService(Message.obtain().apply {</text>
  <text x="980" y="1010" class="code">what = GazeConstants.MSG_VISUAL_READY</text>
  <text x="980" y="1030" class="code">data.putBoolean(GazeConstants.KEY_VISUAL_READY, isReady)</text>
  <text x="960" y="1050" class="code">})</text>
  <text x="940" y="1070" class="code">}</text>
  
  <text x="940" y="1100" class="code">// GazeTrackService</text>
  <text x="940" y="1120" class="code">GazeConstants.MSG_VISUAL_READY -> {</text>
  <text x="960" y="1140" class="code">val isReady = msg.data.getBoolean(GazeConstants.KEY_VISUAL_READY)</text>
  <text x="960" y="1160" class="code">TrackingManager.setVisualReady(isReady)  // 传递给C++</text>
  <text x="940" y="1180" class="code">}</text>
  
  <!-- 时序图 -->
  <rect x="50" y="1270" width="1700" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="900" y="1295" text-anchor="middle" class="subtitle">完整时序图</text>
  
  <rect x="80" y="1320" width="1600" height="180" rx="8" fill="url(#timelineGradient)"/>
  <text x="880" y="1345" text-anchor="middle" class="subtitle">UI动画与C++算法的精确同步</text>
  
  <!-- 时序步骤 -->
  <rect x="120" y="1370" width="120" height="50" rx="5" fill="#fff3e0"/>
  <text x="180" y="1390" text-anchor="middle" class="text">C++发送坐标</text>
  <text x="180" y="1405" text-anchor="middle" class="code">CalibrateCoordinate</text>
  
  <rect x="270" y="1370" width="120" height="50" rx="5" fill="#ffebee"/>
  <text x="330" y="1390" text-anchor="middle" class="text">UI反馈未准备</text>
  <text x="330" y="1405" text-anchor="middle" class="code">Ready(false)</text>
  
  <rect x="420" y="1370" width="120" height="50" rx="5" fill="#e3f2fd"/>
  <text x="480" y="1390" text-anchor="middle" class="text">UI播放动画</text>
  <text x="480" y="1405" text-anchor="middle" class="code">600ms移动</text>
  
  <rect x="570" y="1370" width="120" height="50" rx="5" fill="#e8f5e8"/>
  <text x="630" y="1390" text-anchor="middle" class="text">UI反馈准备好</text>
  <text x="630" y="1405" text-anchor="middle" class="code">Ready(true)</text>
  
  <rect x="720" y="1370" width="120" height="50" rx="5" fill="#fff3e0"/>
  <text x="780" y="1390" text-anchor="middle" class="text">C++收集数据</text>
  <text x="780" y="1405" text-anchor="middle" class="code">run_calibrating</text>
  
  <!-- 连接箭头 -->
  <line x1="240" y1="1395" x2="270" y2="1395" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowheadRed)"/>
  <line x1="390" y1="1395" x2="420" y2="1395" stroke="#2196f3" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="540" y1="1395" x2="570" y2="1395" stroke="#2196f3" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="690" y1="1395" x2="720" y2="1395" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowheadGreen)"/>
  
  <!-- 关键说明 -->
  <text x="120" y="1450" class="text">• <tspan class="highlight">Ready(false):</tspan> 阻止C++收集数据</text>
  <text x="120" y="1470" class="text">• <tspan class="highlight">动画期间:</tspan> 用户眼睛跟随移动的视标</text>
  <text x="120" y="1490" class="text">• <tspan class="highlight">Ready(true):</tspan> 视标稳定，可以开始收集数据</text>
  
  <text x="900" y="1450" class="text">• <tspan class="highlight">同步保证:</tspan> 只在视标完全静止时收集数据</text>
  <text x="900" y="1470" class="text">• <tspan class="highlight">用户体验:</tspan> 视觉反馈与算法状态一致</text>
  <text x="900" y="1490" class="text">• <tspan class="highlight">校准精度:</tspan> 确保数据质量和准确性</text>
  
  <!-- 总结 -->
  <rect x="100" y="1540" width="1600" height="40" rx="5" fill="#e8f5e8"/>
  <text x="900" y="1565" text-anchor="middle" class="highlight">
    总结：notifyVisualReady()确保UI动画与C++算法的精确同步，保证校准数据的准确性和用户体验的一致性
  </text>
</svg>
