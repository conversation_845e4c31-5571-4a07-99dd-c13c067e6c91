<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .class-name { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .method { font-family: Arial, sans-serif; font-size: 10px; }
      .field { font-family: Arial, sans-serif; font-size: 10px; }
      .interface { font-style: italic; }
      .association { stroke: #333; stroke-width: 1; marker-end: url(#arrowhead); }
      .inheritance { stroke: #333; stroke-width: 1; marker-end: url(#triangle); }
      .dependency { stroke: #666; stroke-width: 1; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .composition { stroke: #333; stroke-width: 1; marker-end: url(#diamond); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="triangle" markerWidth="12" markerHeight="10" refX="12" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="white" stroke="#333" stroke-width="1" />
    </marker>
    <marker id="diamond" markerWidth="12" markerHeight="8" refX="12" refY="4" orient="auto">
      <polygon points="0 4, 6 0, 12 4, 6 8" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="25" class="title">VisualTrainFragment 视觉训练疗法类图</text>
  
  <!-- VisualTrainFragment -->
  <rect x="50" y="60" width="280" height="200" fill="#e6f3ff" stroke="#333" stroke-width="2"/>
  <text x="190" y="80" class="class-name">VisualTrainFragment</text>
  <line x1="50" y1="85" x2="330" y2="85" stroke="#333"/>
  <text x="55" y="100" class="field">- treatmentVM: TreatmentViewModel</text>
  <text x="55" y="115" class="field">- trainVM: TrainViewModel</text>
  <text x="55" y="130" class="field">- mModuleName: String</text>
  <text x="55" y="145" class="field">- isFull: Boolean</text>
  <text x="55" y="160" class="field">- flipBeatListener: FlipBeatListener</text>
  <line x1="50" y1="165" x2="330" y2="165" stroke="#333"/>
  <text x="55" y="180" class="method">+ initView()</text>
  <text x="55" y="195" class="method">+ initObserver()</text>
  <text x="55" y="210" class="method">+ refreshVisionTherapy()</text>
  <text x="55" y="225" class="method">+ showTrainCategory()</text>
  <text x="55" y="240" class="method">+ showTrainList()</text>
  <text x="55" y="255" class="method">+ updateFlipConnected()</text>
  
  <!-- TreatmentViewModel -->
  <rect x="400" y="60" width="250" height="150" fill="#fff2e6" stroke="#333" stroke-width="2"/>
  <text x="525" y="80" class="class-name">TreatmentViewModel</text>
  <line x1="400" y1="85" x2="650" y2="85" stroke="#333"/>
  <text x="405" y="100" class="field">- treatmentRepository: TreatmentRepository</text>
  <text x="405" y="115" class="field">+ curTreatmentLiveData: MutableLiveData</text>
  <text x="405" y="130" class="field">+ treatmentsLiveData: MutableLiveData</text>
  <line x1="400" y1="135" x2="650" y2="135" stroke="#333"/>
  <text x="405" y="150" class="method">+ getCurrentTreatment()</text>
  <text x="405" y="165" class="method">+ getTreatments()</text>
  <text x="405" y="180" class="method">+ createTreatment()</text>
  <text x="405" y="195" class="method">+ updateTreatment()</text>
  
  <!-- TrainViewModel -->
  <rect x="700" y="60" width="250" height="180" fill="#fff2e6" stroke="#333" stroke-width="2"/>
  <text x="825" y="80" class="class-name">TrainViewModel</text>
  <line x1="700" y1="85" x2="950" y2="85" stroke="#333"/>
  <text x="705" y="100" class="field">- trainRepository: TrainRepository</text>
  <text x="705" y="115" class="field">+ todayVisionTherapyLiveData: MutableLiveData</text>
  <text x="705" y="130" class="field">+ submitTrainResultLiveData: MutableLiveData</text>
  <text x="705" y="145" class="field">+ trainConfigLiveData: MutableLiveData</text>
  <line x1="700" y1="150" x2="950" y2="150" stroke="#333"/>
  <text x="705" y="165" class="method">+ getTodayVisionTherapy()</text>
  <text x="705" y="180" class="method">+ submitTrainResult()</text>
  <text x="705" y="195" class="method">+ getTrainConfig()</text>
  <text x="705" y="210" class="method">+ get visionTherapy()</text>
  <text x="705" y="225" class="method">+ get allowTraining()</text>
  
  <!-- TrainRepository -->
  <rect x="1000" y="60" width="250" height="120" fill="#e6ffe6" stroke="#333" stroke-width="2"/>
  <text x="1125" y="80" class="class-name">TrainRepository</text>
  <line x1="1000" y1="85" x2="1250" y2="85" stroke="#333"/>
  <line x1="1000" y1="90" x2="1250" y2="90" stroke="#333"/>
  <text x="1005" y="105" class="method">+ getTodayVisionTherapy()</text>
  <text x="1005" y="120" class="method">+ submitTrainResult()</text>
  <text x="1005" y="135" class="method">+ getTrainConfig()</text>
  <text x="1005" y="150" class="method">- executeHttp()</text>
  <text x="1005" y="165" class="method">- createService()</text>
  
  <!-- FlipBeatManager -->
  <rect x="50" y="300" width="280" height="150" fill="#ffe6f3" stroke="#333" stroke-width="2"/>
  <text x="190" y="320" class="class-name">FlipBeatManager</text>
  <line x1="50" y1="325" x2="330" y2="325" stroke="#333"/>
  <line x1="50" y1="330" x2="330" y2="330" stroke="#333"/>
  <text x="55" y="345" class="method">+ registerFlipBeatListener()</text>
  <text x="55" y="360" class="method">+ unRegisterFlipBeatListener()</text>
  <text x="55" y="375" class="method">+ getFlipBeatState()</text>
  <text x="55" y="390" class="method">+ startScanFlip()</text>
  <text x="55" y="405" class="method">+ stopScanFlip()</text>
  <text x="55" y="420" class="method">+ connectFlipBeat()</text>
  <text x="55" y="435" class="method">+ writeDataToFlipBeat()</text>
  
  <!-- ConnectFlipDialog -->
  <rect x="400" y="300" width="250" height="120" fill="#f0f0f0" stroke="#333" stroke-width="2"/>
  <text x="525" y="320" class="class-name">ConnectFlipDialog</text>
  <line x1="400" y1="325" x2="650" y2="325" stroke="#333"/>
  <text x="405" y="340" class="field">- flipDevices: MutableList</text>
  <text x="405" y="355" class="field">- connectFlipAdapter: Adapter</text>
  <line x1="400" y1="360" x2="650" y2="360" stroke="#333"/>
  <text x="405" y="375" class="method">+ show()</text>
  <text x="405" y="390" class="method">+ refreshFlipDevice()</text>
  <text x="405" y="405" class="method">+ connectFlipBeat()</text>
  
  <!-- UserManager -->
  <rect x="700" y="300" width="250" height="120" fill="#f5f5dc" stroke="#333" stroke-width="2"/>
  <text x="825" y="320" class="class-name">UserManager</text>
  <line x1="700" y1="325" x2="950" y2="325" stroke="#333"/>
  <line x1="700" y1="330" x2="950" y2="330" stroke="#333"/>
  <text x="705" y="345" class="method">+ isBind(): Boolean</text>
  <text x="705" y="360" class="method">+ getTreatmentInfo()</text>
  <text x="705" y="375" class="method">+ getAccountInfo()</text>
  <text x="705" y="390" class="method">+ setTreatmentInfo()</text>
  
  <!-- Child Fragments -->
  <rect x="50" y="500" width="200" height="80" fill="#e6f3ff" stroke="#333" stroke-width="2"/>
  <text x="150" y="520" class="class-name">TrainCategoryListFragment</text>
  <line x1="50" y1="525" x2="250" y2="525" stroke="#333"/>
  <line x1="50" y1="530" x2="250" y2="530" stroke="#333"/>
  <text x="55" y="545" class="method">+ newInstance()</text>
  <text x="55" y="560" class="method">+ initObserver()</text>
  <text x="55" y="575" class="method">+ onItemClick()</text>
  
  <rect x="280" y="500" width="200" height="80" fill="#e6f3ff" stroke="#333" stroke-width="2"/>
  <text x="380" y="520" class="class-name">TrainListFragment</text>
  <line x1="280" y1="525" x2="480" y2="525" stroke="#333"/>
  <line x1="280" y1="530" x2="480" y2="530" stroke="#333"/>
  <text x="285" y="545" class="method">+ newInstance()</text>
  <text x="285" y="560" class="method">+ initObserver()</text>
  <text x="285" y="575" class="method">+ onItemClick()</text>
  
  <rect x="510" y="500" width="200" height="80" fill="#e6f3ff" stroke="#333" stroke-width="2"/>
  <text x="610" y="520" class="class-name">VisualTrainUnBindFragment</text>
  <line x1="510" y1="525" x2="710" y2="525" stroke="#333"/>
  <line x1="510" y1="530" x2="710" y2="530" stroke="#333"/>
  <text x="515" y="545" class="method">+ newInstance()</text>
  <text x="515" y="560" class="method">+ getLayoutResId()</text>
  
  <rect x="740" y="500" width="200" height="80" fill="#e6f3ff" stroke="#333" stroke-width="2"/>
  <text x="840" y="520" class="class-name">VisualTrainNoOpenFragment</text>
  <line x1="740" y1="525" x2="940" y2="525" stroke="#333"/>
  <line x1="740" y1="530" x2="940" y2="530" stroke="#333"/>
  <text x="745" y="545" class="method">+ newInstance()</text>
  <text x="745" y="560" class="method">+ getLayoutResId()</text>
  
  <!-- Data Classes -->
  <rect x="50" y="620" width="180" height="100" fill="#ffffcc" stroke="#333" stroke-width="2"/>
  <text x="140" y="640" class="class-name">VisionTherapy</text>
  <line x1="50" y1="645" x2="230" y2="645" stroke="#333"/>
  <text x="55" y="660" class="field">+ list: List&lt;TrainCategory&gt;</text>
  <text x="55" y="675" class="field">+ isCategory: Boolean</text>
  <text x="55" y="690" class="field">+ allowTraining: Boolean</text>
  <text x="55" y="705" class="field">+ plannedDuration: Int</text>
  
  <rect x="260" y="620" width="180" height="100" fill="#ffffcc" stroke="#333" stroke-width="2"/>
  <text x="350" y="640" class="class-name">TrainCategory</text>
  <line x1="260" y1="645" x2="440" y2="645" stroke="#333"/>
  <text x="265" y="660" class="field">+ items: List&lt;Train&gt;</text>
  <text x="265" y="675" class="field">+ allowTraining: Boolean</text>
  <text x="265" y="690" class="field">+ plannedDuration: Int</text>
  <text x="265" y="705" class="field">+ categoryName: String</text>
  
  <rect x="470" y="620" width="180" height="100" fill="#ffffcc" stroke="#333" stroke-width="2"/>
  <text x="560" y="640" class="class-name">Train</text>
  <line x1="470" y1="645" x2="650" y2="645" stroke="#333"/>
  <text x="475" y="660" class="field">+ trainId: Int</text>
  <text x="475" y="675" class="field">+ trainName: String</text>
  <text x="475" y="690" class="field">+ mustBluetoothFlip: Boolean</text>
  <text x="475" y="705" class="field">+ trainUrl: String</text>
  
  <rect x="680" y="620" width="180" height="100" fill="#ffffcc" stroke="#333" stroke-width="2"/>
  <text x="770" y="640" class="class-name">TrainConfig</text>
  <line x1="680" y1="645" x2="860" y2="645" stroke="#333"/>
  <text x="685" y="660" class="field">+ trainId: Int</text>
  <text x="685" y="675" class="field">+ config: String</text>
  <text x="685" y="690" class="field">+ gameUrl: String</text>
  <text x="685" y="705" class="field">+ duration: Int</text>
  
  <!-- Interfaces -->
  <rect x="50" y="760" width="200" height="60" fill="#f0f0f0" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="150" y="780" class="class-name interface">FlipBeatListener</text>
  <line x1="50" y1="785" x2="250" y2="785" stroke="#333"/>
  <text x="55" y="800" class="method">+ onConnectionStateChange()</text>
  
  <!-- Relationships -->
  
  <!-- VisualTrainFragment uses ViewModels -->
  <line x1="330" y1="120" x2="400" y2="120" class="association"/>
  <line x1="330" y1="140" x2="700" y2="140" class="association"/>
  
  <!-- ViewModels use Repository -->
  <line x1="950" y1="120" x2="1000" y2="120" class="association"/>
  
  <!-- VisualTrainFragment uses FlipBeatManager -->
  <line x1="190" y1="260" x2="190" y2="300" class="dependency"/>
  
  <!-- VisualTrainFragment uses ConnectFlipDialog -->
  <line x1="330" y1="200" x2="400" y2="340" class="dependency"/>
  
  <!-- VisualTrainFragment uses UserManager -->
  <line x1="330" y1="180" x2="700" y2="340" class="dependency"/>
  
  <!-- VisualTrainFragment contains child fragments -->
  <line x1="190" y1="260" x2="150" y2="500" class="composition"/>
  <line x1="190" y1="260" x2="380" y2="500" class="composition"/>
  <line x1="190" y1="260" x2="610" y2="500" class="composition"/>
  <line x1="190" y1="260" x2="840" y2="500" class="composition"/>
  
  <!-- Data relationships -->
  <line x1="230" y1="670" x2="260" y2="670" class="association"/>
  <line x1="440" y1="670" x2="470" y2="670" class="association"/>
  
  <!-- FlipBeatListener implementation -->
  <line x1="190" y1="260" x2="150" y2="760" class="inheritance"/>
  
  <!-- ConnectFlipDialog uses FlipBeatManager -->
  <line x1="400" y1="380" x2="330" y2="380" class="dependency"/>
  
</svg>
