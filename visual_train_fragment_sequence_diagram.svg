<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .actor { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .message { font-family: Arial, sans-serif; font-size: 10px; }
      .lifeline { stroke: #333; stroke-width: 2; stroke-dasharray: 5,5; }
      .activation { fill: #e6f3ff; stroke: #0066cc; stroke-width: 1; }
      .arrow { stroke: #333; stroke-width: 1; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #666; stroke-width: 1; stroke-dasharray: 3,3; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="25" class="title">VisualTrainFragment 视觉训练疗法时序图</text>
  
  <!-- Actors -->
  <rect x="50" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="100" y="75" class="actor">User</text>
  
  <rect x="200" y="50" width="120" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="260" y="75" class="actor">VisualTrainFragment</text>
  
  <rect x="370" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="420" y="75" class="actor">TreatmentVM</text>
  
  <rect x="520" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="570" y="75" class="actor">TrainVM</text>
  
  <rect x="670" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="720" y="75" class="actor">TrainRepository</text>
  
  <rect x="820" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="870" y="75" class="actor">UserManager</text>
  
  <rect x="970" y="50" width="100" height="40" fill="#f0f8ff" stroke="#333" rx="5"/>
  <text x="1020" y="75" class="actor">FlipBeatManager</text>
  
  <!-- Lifelines -->
  <line x1="100" y1="90" x2="100" y2="750" class="lifeline"/>
  <line x1="260" y1="90" x2="260" y2="750" class="lifeline"/>
  <line x1="420" y1="90" x2="420" y2="750" class="lifeline"/>
  <line x1="570" y1="90" x2="570" y2="750" class="lifeline"/>
  <line x1="720" y1="90" x2="720" y2="750" class="lifeline"/>
  <line x1="870" y1="90" x2="870" y2="750" class="lifeline"/>
  <line x1="1020" y1="90" x2="1020" y2="750" class="lifeline"/>
  
  <!-- Sequence Messages -->
  
  <!-- 1. Fragment initialization -->
  <line x1="100" y1="120" x2="260" y2="120" class="arrow"/>
  <text x="180" y="115" class="message">1. 进入视觉训练页面</text>
  
  <rect x="255" y="125" width="10" height="30" class="activation"/>
  <line x1="260" y1="140" x2="420" y2="140" class="arrow"/>
  <text x="340" y="135" class="message">2. initObserver()</text>
  
  <!-- 3. Get current treatment -->
  <rect x="415" y="160" width="10" height="40" class="activation"/>
  <line x1="420" y1="170" x2="570" y2="170" class="arrow"/>
  <text x="495" y="165" class="message">3. getCurrentTreatment()</text>
  
  <line x1="570" y1="185" x2="420" y2="185" class="return-arrow"/>
  <text x="495" y="180" class="message">curTreatmentLiveData</text>
  
  <!-- 4. Get today vision therapy -->
  <rect x="565" y="210" width="10" height="60" class="activation"/>
  <line x1="420" y1="220" x2="570" y2="220" class="arrow"/>
  <text x="495" y="215" class="message">4. getTodayVisionTherapy()</text>
  
  <line x1="570" y1="240" x2="720" y2="240" class="arrow"/>
  <text x="645" y="235" class="message">5. getTodayVisionTherapy()</text>
  
  <rect x="715" y="250" width="10" height="20" class="activation"/>
  <line x1="720" y1="260" x2="570" y2="260" class="return-arrow"/>
  <text x="645" y="255" class="message">VisionTherapy</text>
  
  <!-- 6. Refresh vision therapy -->
  <line x1="570" y1="280" x2="260" y2="280" class="arrow"/>
  <text x="415" y="275" class="message">6. todayVisionTherapyLiveData</text>
  
  <rect x="255" y="290" width="10" height="120" class="activation"/>
  <line x1="260" y1="300" x2="870" y2="300" class="arrow"/>
  <text x="565" y="295" class="message">7. UserManager.isBind()</text>
  
  <line x1="870" y1="315" x2="260" y2="315" class="return-arrow"/>
  <text x="565" y="310" class="message">isBind</text>
  
  <line x1="260" y1="330" x2="870" y2="330" class="arrow"/>
  <text x="565" y="325" class="message">8. UserManager.getTreatmentInfo()</text>
  
  <line x1="870" y1="345" x2="260" y2="345" class="return-arrow"/>
  <text x="565" y="340" class="message">treatmentInfo</text>
  
  <!-- 9. Show appropriate fragment -->
  <text x="260" y="365" class="message">9. 根据状态显示相应Fragment:</text>
  <text x="260" y="380" class="message">   - showVisualTrainUnBind()</text>
  <text x="260" y="395" class="message">   - showVisualTrainNoOpen()</text>
  <text x="260" y="410" class="message">   - showTrainCategory()</text>
  <text x="260" y="425" class="message">   - showTrainList()</text>
  
  <!-- 10. FlipBeat connection -->
  <line x1="100" y1="450" x2="260" y2="450" class="arrow"/>
  <text x="180" y="445" class="message">10. 点击翻转拍连接</text>
  
  <rect x="255" y="460" width="10" height="40" class="activation"/>
  <line x1="260" y1="470" x2="1020" y2="470" class="arrow"/>
  <text x="640" y="465" class="message">11. ConnectFlipDialog.show()</text>
  
  <rect x="1015" y="480" width="10" height="60" class="activation"/>
  <line x1="1020" y1="490" x2="260" y2="490" class="arrow"/>
  <text x="640" y="485" class="message">12. FlipBeatListener.onConnectionStateChange()</text>
  
  <line x1="260" y1="510" x2="260" y2="510" class="arrow"/>
  <text x="270" y="505" class="message">13. updateFlipConnected()</text>
  
  <!-- 14. Training flow -->
  <line x1="100" y1="550" x2="260" y2="550" class="arrow"/>
  <text x="180" y="545" class="message">14. 选择训练项目</text>
  
  <rect x="255" y="560" width="10" height="80" class="activation"/>
  <text x="270" y="575" class="message">15. 检查训练权限:</text>
  <text x="270" y="590" class="message">   - allowTraining</text>
  <text x="270" y="605" class="message">   - mustBluetoothFlip</text>
  
  <line x1="260" y1="620" x2="570" y2="620" class="arrow"/>
  <text x="415" y="615" class="message">16. getTrainConfig(trainId)</text>
  
  <rect x="565" y="630" width="10" height="40" class="activation"/>
  <line x1="570" y1="640" x2="720" y2="640" class="arrow"/>
  <text x="645" y="635" class="message">17. getTrainConfig()</text>
  
  <rect x="715" y="650" width="10" height="20" class="activation"/>
  <line x1="720" y1="660" x2="570" y2="660" class="return-arrow"/>
  <text x="645" y="655" class="message">TrainConfig</text>
  
  <!-- 18. Start training -->
  <line x1="260" y1="690" x2="100" y2="690" class="arrow"/>
  <text x="180" y="685" class="message">18. 启动训练Activity</text>
  
  <!-- 19. Submit results -->
  <line x1="100" y1="720" x2="570" y2="720" class="arrow"/>
  <text x="335" y="715" class="message">19. submitTrainResult()</text>
  
  <rect x="565" y="730" width="10" height="20" class="activation"/>
  <line x1="570" y1="740" x2="720" y2="740" class="arrow"/>
  <text x="645" y="735" class="message">20. submitTrainResult()</text>
  
</svg>
