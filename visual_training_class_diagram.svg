<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .class-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .method { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .field { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .interface { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .activity { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .service { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .repository { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .manager { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .viewmodel { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .bean { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; marker-end: url(#arrowhead); }
      .dependency { stroke: #34495e; stroke-width: 1; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="800" y="30" text-anchor="middle" class="title">视觉训练疗法类图</text>

  <!-- EyeMovementEvaluateActivity -->
  <rect x="50" y="70" width="250" height="120" class="activity" rx="5"/>
  <text x="175" y="90" text-anchor="middle" class="class-title">EyeMovementEvaluateActivity</text>
  <line x1="60" y1="95" x2="290" y2="95" stroke="white" stroke-width="1"/>
  <text x="60" y="110" class="method">+ initView(): void</text>
  <text x="60" y="125" class="method">+ startCalibration(): void</text>
  <text x="60" y="140" class="method">- checkPatientInfo(): Boolean</text>
  <text x="60" y="155" class="method">- navigateToTest(testType): void</text>
  <text x="60" y="170" class="method">- showPatientDialog(): void</text>

  <!-- EMPatientManager -->
  <rect x="350" y="70" width="200" height="100" class="manager" rx="5"/>
  <text x="450" y="90" text-anchor="middle" class="class-title">EMPatientManager</text>
  <line x1="360" y1="95" x2="540" y2="95" stroke="white" stroke-width="1"/>
  <text x="360" y="110" class="method">+ getEMPatient(): EMPatient?</text>
  <text x="360" y="125" class="method">+ setEMPatient(patient): void</text>
  <text x="360" y="140" class="method">+ clearPatient(): void</text>
  <text x="360" y="155" class="method">+ isPatientValid(): Boolean</text>

  <!-- GazeStabilityEvaluateActivity -->
  <rect x="50" y="220" width="280" height="140" class="activity" rx="5"/>
  <text x="190" y="240" text-anchor="middle" class="class-title">GazeStabilityEvaluateActivity</text>
  <line x1="60" y1="245" x2="320" y2="245" stroke="white" stroke-width="1"/>
  <text x="60" y="260" class="method">+ showGazeStabilityEvaluateExplain(): void</text>
  <text x="60" y="275" class="method">+ showGazeStabilityEvaluating(): void</text>
  <text x="60" y="290" class="method">- startEvaluating(): void</text>
  <text x="60" y="305" class="method">- stopEvaluating(): void</text>
  <text x="60" y="320" class="method">- handleGazeTrajectory(): void</text>
  <text x="60" y="335" class="method">- uploadResultImage(): void</text>
  <text x="60" y="350" class="method">- submitResult(): void</text>

  <!-- SaccadeAbilityEvaluateActivity -->
  <rect x="350" y="220" width="280" height="140" class="activity" rx="5"/>
  <text x="490" y="240" text-anchor="middle" class="class-title">SaccadeAbilityEvaluateActivity</text>
  <line x1="360" y1="245" x2="620" y2="245" stroke="white" stroke-width="1"/>
  <text x="360" y="260" class="method">+ showSaccadeAbilityEvaluateExplain(): void</text>
  <text x="360" y="275" class="method">+ showSaccadeAbilityEvaluating(): void</text>
  <text x="360" y="290" class="method">- startEvaluating(): void</text>
  <text x="360" y="305" class="method">- generateTargetPoints(): List&lt;PointF&gt;</text>
  <text x="360" y="320" class="method">- analyzeGazeTrajectory(): void</text>
  <text x="360" y="335" class="method">- calculateSaccadeMetrics(): void</text>
  <text x="360" y="350" class="method">- submitResult(): void</text>

  <!-- ROIDetectionActivity -->
  <rect x="650" y="220" width="250" height="140" class="activity" rx="5"/>
  <text x="775" y="240" text-anchor="middle" class="class-title">ROIDetectionActivity</text>
  <line x1="660" y1="245" x2="890" y2="245" stroke="white" stroke-width="1"/>
  <text x="660" y="260" class="method">+ selectPicture(): void</text>
  <text x="660" y="275" class="method">+ markROI(): void</text>
  <text x="660" y="290" class="method">+ startDetection(): void</text>
  <text x="660" y="305" class="method">- analyzeGazeInROI(): void</text>
  <text x="660" y="320" class="method">- calculateCoverageRate(): void</text>
  <text x="660" y="335" class="method">- buildROIRegionsJson(): String</text>
  <text x="660" y="350" class="method">- submitResult(): void</text>

  <!-- GazeTrackService -->
  <rect x="950" y="220" width="220" height="140" class="service" rx="5"/>
  <text x="1060" y="240" text-anchor="middle" class="class-title">GazeTrackService</text>
  <line x1="960" y1="245" x2="1160" y2="245" stroke="white" stroke-width="1"/>
  <text x="960" y="260" class="method">+ startGazeTrack(): void</text>
  <text x="960" y="275" class="method">+ stopGazeTrack(): void</text>
  <text x="960" y="290" class="method">+ setGlancePoint(x, y): void</text>
  <text x="960" y="305" class="method">+ getGazeTrajectory(): String</text>
  <text x="960" y="320" class="method">- handleMessage(msg): void</text>
  <text x="960" y="335" class="method">- sendMessageToClient(): void</text>
  <text x="960" y="350" class="method">- onAnalyze(image): void</text>

  <!-- TrackingManager -->
  <rect x="1200" y="220" width="200" height="140" class="manager" rx="5"/>
  <text x="1300" y="240" text-anchor="middle" class="class-title">TrackingManager</text>
  <line x1="1210" y1="245" x2="1390" y2="245" stroke="white" stroke-width="1"/>
  <text x="1210" y="260" class="method">+ startTracking(): Int</text>
  <text x="1210" y="275" class="method">+ stopTracking(): Int</text>
  <text x="1210" y="290" class="method">+ sendImageProxy(): void</text>
  <text x="1210" y="305" class="method">- gazeTracking(): void</text>
  <text x="1210" y="320" class="method">- postureCalibration(): void</text>
  <text x="1210" y="335" class="method">- calibrating(): void</text>
  <text x="1210" y="350" class="method">+ checkCalibrationParam(): Boolean</text>

  <!-- ViewModels -->
  <rect x="50" y="400" width="220" height="100" class="viewmodel" rx="5"/>
  <text x="160" y="420" text-anchor="middle" class="class-title">GazeStabilityViewModel</text>
  <line x1="60" y1="425" x2="260" y2="425" stroke="white" stroke-width="1"/>
  <text x="60" y="440" class="method">+ uploadImage(): LiveData</text>
  <text x="60" y="455" class="method">+ submitResult(): LiveData</text>
  <text x="60" y="470" class="method">- buildRequestParams(): Map</text>
  <text x="60" y="485" class="method">- handleResponse(): void</text>

  <rect x="290" y="400" width="220" height="100" class="viewmodel" rx="5"/>
  <text x="400" y="420" text-anchor="middle" class="class-title">SaccadeAbilityViewModel</text>
  <line x1="300" y1="425" x2="500" y2="425" stroke="white" stroke-width="1"/>
  <text x="300" y="440" class="method">+ uploadImage(): LiveData</text>
  <text x="300" y="455" class="method">+ submitResult(): LiveData</text>
  <text x="300" y="470" class="method">- analyzeTrajectory(): void</text>
  <text x="300" y="485" class="method">- calculateMetrics(): void</text>

  <rect x="530" y="400" width="200" height="100" class="viewmodel" rx="5"/>
  <text x="630" y="420" text-anchor="middle" class="class-title">ROIDetectionViewModel</text>
  <line x1="540" y1="425" x2="720" y2="425" stroke="white" stroke-width="1"/>
  <text x="540" y="440" class="method">+ uploadImage(): LiveData</text>
  <text x="540" y="455" class="method">+ submitResult(): LiveData</text>
  <text x="540" y="470" class="method">- analyzeROI(): void</text>
  <text x="540" y="485" class="method">- buildROIData(): Map</text>

  <!-- Repositories -->
  <rect x="50" y="530" width="220" height="100" class="repository" rx="5"/>
  <text x="160" y="550" text-anchor="middle" class="class-title">GazeStabilityRepository</text>
  <line x1="60" y1="555" x2="260" y2="555" stroke="white" stroke-width="1"/>
  <text x="60" y="570" class="method">+ uploadImage(): Pair</text>
  <text x="60" y="585" class="method">+ submitResult(): Pair</text>
  <text x="60" y="600" class="method">- executeHttp(): ApiResponse</text>
  <text x="60" y="615" class="method">- handleError(): ErrorInfo</text>

  <rect x="290" y="530" width="220" height="100" class="repository" rx="5"/>
  <text x="400" y="550" text-anchor="middle" class="class-title">SaccadeAbilityRepository</text>
  <line x1="300" y1="555" x2="500" y2="555" stroke="white" stroke-width="1"/>
  <text x="300" y="570" class="method">+ uploadImage(): Pair</text>
  <text x="300" y="585" class="method">+ submitResult(): Pair</text>
  <text x="300" y="600" class="method">- executeHttp(): ApiResponse</text>
  <text x="300" y="615" class="method">- handleError(): ErrorInfo</text>

  <rect x="530" y="530" width="200" height="100" class="repository" rx="5"/>
  <text x="630" y="550" text-anchor="middle" class="class-title">ROIDetectionRepository</text>
  <line x1="540" y1="555" x2="720" y2="555" stroke="white" stroke-width="1"/>
  <text x="540" y="570" class="method">+ uploadImage(): Pair</text>
  <text x="540" y="585" class="method">+ submitResult(): Pair</text>
  <text x="540" y="600" class="method">- executeHttp(): ApiResponse</text>
  <text x="540" y="615" class="method">- handleError(): ErrorInfo</text>

  <rect x="750" y="530" width="180" height="100" class="repository" rx="5"/>
  <text x="840" y="550" text-anchor="middle" class="class-title">EMPatientRepository</text>
  <line x1="760" y1="555" x2="920" y2="555" stroke="white" stroke-width="1"/>
  <text x="760" y="570" class="method">+ getPatientList(): ApiResponse</text>
  <text x="760" y="585" class="method">+ addPatient(): ApiResponse</text>
  <text x="760" y="600" class="method">+ queryPatient(): ApiResponse</text>
  <text x="760" y="615" class="method">+ modifyPatient(): ApiResponse</text>

  <!-- API Services -->
  <rect x="50" y="660" width="200" height="80" class="interface" rx="5"/>
  <text x="150" y="680" text-anchor="middle" class="class-title">GazeStabilityApiService</text>
  <line x1="60" y1="685" x2="240" y2="685" stroke="white" stroke-width="1"/>
  <text x="60" y="700" class="method">+ addGazeStability(): ApiResponse</text>
  <text x="60" y="715" class="method">+ uploadImage(): FileUploadResponse</text>
  <text x="60" y="730" class="method">@POST("/api/movement/gaze-stability/submit")</text>

  <rect x="270" y="660" width="200" height="80" class="interface" rx="5"/>
  <text x="370" y="680" text-anchor="middle" class="class-title">SaccadeAbilityApiService</text>
  <line x1="280" y1="685" x2="460" y2="685" stroke="white" stroke-width="1"/>
  <text x="280" y="700" class="method">+ addSaccadeAbility(): ApiResponse</text>
  <text x="280" y="715" class="method">+ uploadImage(): FileUploadResponse</text>
  <text x="280" y="730" class="method">@POST("/api/movement/saccade-ability/submit")</text>

  <rect x="490" y="660" width="180" height="80" class="interface" rx="5"/>
  <text x="580" y="680" text-anchor="middle" class="class-title">ROIDetectionApiService</text>
  <line x1="500" y1="685" x2="660" y2="685" stroke="white" stroke-width="1"/>
  <text x="500" y="700" class="method">+ addROIDetection(): ApiResponse</text>
  <text x="500" y="715" class="method">+ uploadImage(): FileUploadResponse</text>
  <text x="500" y="730" class="method">@POST("/api/movement/roi-detection/submit")</text>

  <rect x="690" y="660" width="160" height="80" class="interface" rx="5"/>
  <text x="770" y="680" text-anchor="middle" class="class-title">EMPatientApiService</text>
  <line x1="700" y1="685" x2="840" y2="685" stroke="white" stroke-width="1"/>
  <text x="700" y="700" class="method">+ getEMPatientList(): ApiResponse</text>
  <text x="700" y="715" class="method">+ addEMPatient(): ApiResponse</text>
  <text x="700" y="730" class="method">@POST("/api/patients")</text>

  <!-- MovementClient -->
  <rect x="870" y="660" width="150" height="80" class="service" rx="5"/>
  <text x="945" y="680" text-anchor="middle" class="class-title">MovementClient</text>
  <line x1="880" y1="685" x2="1010" y2="685" stroke="white" stroke-width="1"/>
  <text x="880" y="700" class="method">+ createService(): T</text>
  <text x="880" y="715" class="method">+ fetchBaseUrl(): String</text>
  <text x="880" y="730" class="method">+ handleBuilder(): void</text>

  <!-- Data Beans -->
  <rect x="50" y="770" width="150" height="80" class="bean" rx="5"/>
  <text x="125" y="790" text-anchor="middle" class="class-title">EMPatient</text>
  <line x1="60" y1="795" x2="190" y2="795" stroke="white" stroke-width="1"/>
  <text x="60" y="810" class="field">+ id: Long</text>
  <text x="60" y="825" class="field">+ name: String</text>
  <text x="60" y="840" class="field">+ gender: Int</text>

  <rect x="220" y="770" width="150" height="80" class="bean" rx="5"/>
  <text x="295" y="790" text-anchor="middle" class="class-title">GazeTrajectory</text>
  <line x1="230" y1="795" x2="360" y2="795" stroke="white" stroke-width="1"/>
  <text x="230" y="810" class="field">+ gazePoints: List&lt;GazePoint&gt;</text>
  <text x="230" y="825" class="field">+ duration: Int</text>
  <text x="230" y="840" class="field">+ timestamp: Long</text>

  <rect x="390" y="770" width="150" height="80" class="bean" rx="5"/>
  <text x="465" y="790" text-anchor="middle" class="class-title">VisionTherapy</text>
  <line x1="400" y1="795" x2="530" y2="795" stroke="white" stroke-width="1"/>
  <text x="400" y="810" class="field">+ allowTraining: Boolean</text>
  <text x="400" y="825" class="field">+ list: List&lt;TrainCategory&gt;</text>
  <text x="400" y="840" class="field">+ plannedDuration: Int</text>

  <!-- Relationships -->
  <!-- Activity to Manager -->
  <line x1="300" y1="130" x2="350" y2="130" class="arrow"/>
  
  <!-- Activity to ViewModel -->
  <line x1="190" y1="360" x2="160" y2="400" class="arrow"/>
  <line x1="490" y1="360" x2="400" y2="400" class="arrow"/>
  <line x1="775" y1="360" x2="630" y2="400" class="arrow"/>
  
  <!-- ViewModel to Repository -->
  <line x1="160" y1="500" x2="160" y2="530" class="arrow"/>
  <line x1="400" y1="500" x2="400" y2="530" class="arrow"/>
  <line x1="630" y1="500" x2="630" y2="530" class="arrow"/>
  
  <!-- Repository to API Service -->
  <line x1="160" y1="630" x2="150" y2="660" class="arrow"/>
  <line x1="400" y1="630" x2="370" y2="660" class="arrow"/>
  <line x1="630" y1="630" x2="580" y2="660" class="arrow"/>
  <line x1="840" y1="630" x2="770" y2="660" class="arrow"/>
  
  <!-- API Service to MovementClient -->
  <line x1="850" y1="700" x2="870" y2="700" class="dependency"/>
  
  <!-- Activity to Service -->
  <line x1="330" y1="290" x2="950" y2="290" class="dependency"/>
  
  <!-- Service to Manager -->
  <line x1="1170" y1="290" x2="1200" y2="290" class="arrow"/>

  <!-- Legend -->
  <rect x="1050" y="770" width="300" height="150" fill="none" stroke="#34495e" stroke-width="1"/>
  <text x="1200" y="790" text-anchor="middle" class="class-title" fill="#34495e">图例</text>
  
  <rect x="1060" y="800" width="20" height="15" class="activity"/>
  <text x="1090" y="812" class="method">Activity</text>
  
  <rect x="1060" y="820" width="20" height="15" class="service"/>
  <text x="1090" y="832" class="method">Service</text>
  
  <rect x="1060" y="840" width="20" height="15" class="viewmodel"/>
  <text x="1090" y="852" class="method">ViewModel</text>
  
  <rect x="1060" y="860" width="20" height="15" class="repository"/>
  <text x="1090" y="872" class="method">Repository</text>
  
  <rect x="1060" y="880" width="20" height="15" class="interface"/>
  <text x="1090" y="892" class="method">API Interface</text>
  
  <rect x="1060" y="900" width="20" height="15" class="bean"/>
  <text x="1090" y="912" class="method">Data Bean</text>

  <rect x="1200" y="800" width="20" height="15" class="manager"/>
  <text x="1230" y="812" class="method">Manager</text>
  
  <line x1="1200" y1="830" x2="1240" y2="830" class="arrow"/>
  <text x="1250" y="835" class="method">依赖关系</text>
  
  <line x1="1200" y1="850" x2="1240" y2="850" class="dependency"/>
  <text x="1250" y="855" class="method">使用关系</text>
</svg>
