<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .actor-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .lifeline { stroke: #bdc3c7; stroke-width: 2; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #e74c3c; stroke-width: 2; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 2; marker-end: url(#arrowhead); stroke-dasharray: 3,3; }
      .activation-box { fill: #ecf0f1; stroke: #95a5a6; stroke-width: 1; }
      .note-box { fill: #f39c12; stroke: #e67e22; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">视觉训练疗法时序图</text>
  
  <!-- Actors -->
  <rect x="50" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="110" y="85" text-anchor="middle" class="subtitle">用户</text>
  
  <rect x="220" y="60" width="150" height="40" class="actor-box" rx="5"/>
  <text x="295" y="85" text-anchor="middle" class="subtitle">EyeMovementEvaluateActivity</text>
  
  <rect x="420" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="480" y="85" text-anchor="middle" class="subtitle">EMPatientManager</text>
  
  <rect x="590" y="60" width="150" height="40" class="actor-box" rx="5"/>
  <text x="665" y="85" text-anchor="middle" class="subtitle">GazeStabilityEvaluateActivity</text>
  
  <rect x="790" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="850" y="85" text-anchor="middle" class="subtitle">GazeTrackService</text>
  
  <rect x="960" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1020" y="85" text-anchor="middle" class="subtitle">TrackingManager</text>
  
  <rect x="1130" y="60" width="120" height="40" class="actor-box" rx="5"/>
  <text x="1190" y="85" text-anchor="middle" class="subtitle">MovementClient</text>

  <!-- Lifelines -->
  <line x1="110" y1="100" x2="110" y2="950" class="lifeline"/>
  <line x1="295" y1="100" x2="295" y2="950" class="lifeline"/>
  <line x1="480" y1="100" x2="480" y2="950" class="lifeline"/>
  <line x1="665" y1="100" x2="665" y2="950" class="lifeline"/>
  <line x1="850" y1="100" x2="850" y2="950" class="lifeline"/>
  <line x1="1020" y1="100" x2="1020" y2="950" class="lifeline"/>
  <line x1="1190" y1="100" x2="1190" y2="950" class="lifeline"/>

  <!-- Messages -->
  <!-- 1. 用户选择注视稳定性测试 -->
  <line x1="110" y1="130" x2="295" y2="130" class="message-arrow"/>
  <text x="200" y="125" class="text">1. 点击注视稳定性测试</text>
  
  <!-- 2. 检查患者信息 -->
  <line x1="295" y1="150" x2="480" y2="150" class="message-arrow"/>
  <text x="380" y="145" class="text">2. getEMPatient()</text>
  
  <!-- 3. 返回患者信息 -->
  <line x1="480" y1="170" x2="295" y2="170" class="return-arrow"/>
  <text x="380" y="165" class="text">3. 患者信息</text>
  
  <!-- 4. 启动测试活动 -->
  <line x1="295" y1="190" x2="665" y2="190" class="message-arrow"/>
  <text x="470" y="185" class="text">4. startActivity(GazeStabilityEvaluateActivity)</text>
  
  <!-- 5. 开始评估 -->
  <rect x="655" y="210" width="20" height="200" class="activation-box"/>
  <line x1="665" y1="230" x2="850" y2="230" class="message-arrow"/>
  <text x="750" y="225" class="text">5. MSG_TURN_ON_CAMERA</text>
  
  <!-- 6. 启动相机 -->
  <rect x="840" y="250" width="20" height="150" class="activation-box"/>
  <line x1="850" y1="270" x2="1020" y2="270" class="message-arrow"/>
  <text x="930" y="265" class="text">6. startTracking()</text>
  
  <!-- 7. 开始追踪 -->
  <rect x="1010" y="290" width="20" height="100" class="activation-box"/>
  <line x1="665" y1="310" x2="850" y2="310" class="message-arrow"/>
  <text x="750" y="305" class="text">7. MSG_START_TRACK</text>
  
  <!-- 8. 开始应用凝视 -->
  <line x1="665" y1="330" x2="850" y2="330" class="message-arrow"/>
  <text x="750" y="325" class="text">8. MSG_START_APPLIED_STARE</text>
  
  <!-- 9. 获取轨迹数据 -->
  <line x1="665" y1="350" x2="850" y2="350" class="message-arrow"/>
  <text x="750" y="345" class="text">9. MSG_GET_GAZE_TRAJECTORY</text>
  
  <!-- 10. 返回轨迹数据 -->
  <line x1="850" y1="370" x2="665" y2="370" class="return-arrow"/>
  <text x="750" y="365" class="text">10. 轨迹数据</text>
  
  <!-- 11. 停止追踪 -->
  <line x1="665" y1="390" x2="850" y2="390" class="message-arrow"/>
  <text x="750" y="385" class="text">11. MSG_STOP_TRACK</text>
  
  <!-- 12. 上传图片 -->
  <line x1="665" y1="430" x2="1190" y2="430" class="message-arrow"/>
  <text x="920" y="425" class="text">12. uploadImage()</text>
  
  <!-- 13. 返回图片URL -->
  <line x1="1190" y1="450" x2="665" y2="450" class="return-arrow"/>
  <text x="920" y="445" class="text">13. 图片URL</text>
  
  <!-- 14. 提交测试结果 -->
  <line x1="665" y1="470" x2="1190" y2="470" class="message-arrow"/>
  <text x="920" y="465" class="text">14. submitGazeStabilityResult()</text>
  
  <!-- 15. 返回提交结果 -->
  <line x1="1190" y1="490" x2="665" y2="490" class="return-arrow"/>
  <text x="920" y="485" class="text">15. 提交成功</text>
  
  <!-- 16. 显示结果 -->
  <line x1="665" y1="510" x2="110" y2="510" class="return-arrow"/>
  <text x="380" y="505" class="text">16. 显示测试结果</text>

  <!-- Notes -->
  <rect x="50" y="550" width="300" height="80" class="note-box" rx="5"/>
  <text x="60" y="570" class="subtitle">注意事项:</text>
  <text x="60" y="590" class="small-text">1. 测试前需要先检查患者信息</text>
  <text x="60" y="605" class="small-text">2. 相机启动后开始眼动追踪</text>
  <text x="60" y="620" class="small-text">3. 先上传图片获取URL，再提交数据</text>

  <!-- ROI Detection Flow -->
  <text x="700" y="680" text-anchor="middle" class="subtitle">ROI检测流程</text>
  
  <!-- ROI Messages -->
  <line x1="110" y1="710" x2="295" y2="710" class="message-arrow"/>
  <text x="200" y="705" class="text">17. 点击ROI检测</text>
  
  <line x1="295" y1="730" x2="665" y2="730" class="message-arrow"/>
  <text x="470" y="725" class="text">18. startActivity(ROIDetectionActivity)</text>
  
  <line x1="665" y1="750" x2="850" y2="750" class="message-arrow"/>
  <text x="750" y="745" class="text">19. MSG_START_APPLIED_FOLLOW</text>
  
  <line x1="665" y1="770" x2="1190" y2="770" class="message-arrow"/>
  <text x="920" y="765" class="text">20. submitROIDetectionResult()</text>
  
  <line x1="1190" y1="790" x2="665" y2="790" class="return-arrow"/>
  <text x="920" y="785" class="text">21. 提交成功</text>

  <!-- Saccade Flow -->
  <text x="700" y="830" text-anchor="middle" class="subtitle">扫视能力检测流程</text>
  
  <line x1="110" y1="860" x2="295" y2="860" class="message-arrow"/>
  <text x="200" y="855" class="text">22. 点击扫视能力测试</text>
  
  <line x1="295" y1="880" x2="665" y2="880" class="message-arrow"/>
  <text x="470" y="875" class="text">23. startActivity(SaccadeAbilityEvaluateActivity)</text>
  
  <line x1="665" y1="900" x2="1190" y2="900" class="message-arrow"/>
  <text x="920" y="895" class="text">24. submitSaccadeAbilityResult()</text>
  
  <line x1="1190" y1="920" x2="665" y2="920" class="return-arrow"/>
  <text x="920" y="915" class="text">25. 提交成功</text>
</svg>
