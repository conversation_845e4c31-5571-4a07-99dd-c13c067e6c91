<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #ffffff; }
      .module-title { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .flow-text { font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif; font-size: 12px; fill: #2c3e50; }
      
      .ui-layer { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .ui-module { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      
      .data-layer { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .data-module { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; rx: 8; }
      
      .service-layer { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .service-module { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; rx: 8; }
      
      .view-layer { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .view-module { fill: #fdeaa7; stroke: #f39c12; stroke-width: 2; rx: 8; }
      
      .api-layer { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .api-module { fill: #e8daef; stroke: #9b59b6; stroke-width: 2; rx: 8; }
      
      .native-layer { fill: #16a085; stroke: #138d75; stroke-width: 3; rx: 10; }
      .native-module { fill: #a3e4d7; stroke: #16a085; stroke-width: 2; rx: 8; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">视觉训练疗法系统架构图</text>
  
  <!-- 用户界面层 -->
  <rect x="50" y="70" width="450" height="180" class="ui-layer"/>
  <text x="275" y="95" text-anchor="middle" class="layer-title">用户界面层</text>
  
  <!-- 视觉训练主界面 -->
  <rect x="70" y="110" width="130" height="60" class="ui-module"/>
  <text x="135" y="130" text-anchor="middle" class="module-title">VisualTrainFragment</text>
  <text x="80" y="145" class="text">视觉训练疗法主界面</text>
  <text x="80" y="160" class="text">训练项目选择</text>
  
  <!-- 训练列表界面 -->
  <rect x="210" y="110" width="130" height="60" class="ui-module"/>
  <text x="275" y="130" text-anchor="middle" class="module-title">TrainListFragment</text>
  <text x="220" y="145" class="text">训练项目列表</text>
  <text x="220" y="160" class="text">难度级别选择</text>
  
  <!-- 训练执行界面 -->
  <rect x="350" y="110" width="130" height="60" class="ui-module"/>
  <text x="415" y="130" text-anchor="middle" class="module-title">TrainExecuteActivity</text>
  <text x="360" y="145" class="text">训练任务执行</text>
  <text x="360" y="160" class="text">实时反馈显示</text>
  
  <!-- 训练结果界面 -->
  <rect x="70" y="180" width="130" height="60" class="ui-module"/>
  <text x="135" y="200" text-anchor="middle" class="module-title">TrainResultView</text>
  <text x="80" y="215" class="text">训练成绩显示</text>
  <text x="80" y="230" class="text">进步分析</text>
  
  <!-- 训练设置界面 -->
  <rect x="210" y="180" width="130" height="60" class="ui-module"/>
  <text x="275" y="200" text-anchor="middle" class="module-title">TrainSettingView</text>
  <text x="220" y="215" class="text">训练参数配置</text>
  <text x="220" y="230" class="text">个性化设置</text>
  
  <!-- 进度跟踪界面 -->
  <rect x="350" y="180" width="130" height="60" class="ui-module"/>
  <text x="415" y="200" text-anchor="middle" class="module-title">ProgressTrackView</text>
  <text x="360" y="215" class="text">训练进度跟踪</text>
  <text x="360" y="230" class="text">历史成绩对比</text>

  <!-- 数据模型层 -->
  <rect x="550" y="70" width="450" height="180" class="data-layer"/>
  <text x="775" y="95" text-anchor="middle" class="layer-title">数据模型层</text>
  
  <!-- 训练项目数据 -->
  <rect x="570" y="110" width="130" height="60" class="data-module"/>
  <text x="635" y="130" text-anchor="middle" class="module-title">TrainProject</text>
  <text x="580" y="145" class="text">训练类型定义</text>
  <text x="580" y="160" class="text">难度级别配置</text>
  
  <!-- 训练参数数据 -->
  <rect x="710" y="110" width="130" height="60" class="data-module"/>
  <text x="775" y="130" text-anchor="middle" class="module-title">TrainParameters</text>
  <text x="720" y="145" class="text">速度/精度参数</text>
  <text x="720" y="160" class="text">视标大小/颜色</text>
  
  <!-- 训练记录数据 -->
  <rect x="850" y="110" width="130" height="60" class="data-module"/>
  <text x="915" y="130" text-anchor="middle" class="module-title">TrainRecord</text>
  <text x="860" y="145" class="text">训练历史记录</text>
  <text x="860" y="160" class="text">成绩统计数据</text>
  
  <!-- 眼动轨迹数据 -->
  <rect x="570" y="180" width="130" height="60" class="data-module"/>
  <text x="635" y="200" text-anchor="middle" class="module-title">GazeTrajectory</text>
  <text x="580" y="215" class="text">训练过程轨迹</text>
  <text x="580" y="230" class="text">注视点序列</text>
  
  <!-- 训练任务数据 -->
  <rect x="710" y="180" width="130" height="60" class="data-module"/>
  <text x="775" y="200" text-anchor="middle" class="module-title">TrainTask</text>
  <text x="720" y="215" class="text">任务目标定义</text>
  <text x="720" y="230" class="text">完成条件设置</text>
  
  <!-- 用户进度数据 -->
  <rect x="850" y="180" width="130" height="60" class="data-module"/>
  <text x="915" y="200" text-anchor="middle" class="module-title">UserProgress</text>
  <text x="860" y="215" class="text">个人训练进度</text>
  <text x="860" y="230" class="text">能力评估数据</text>

  <!-- 服务层 -->
  <rect x="1050" y="70" width="450" height="180" class="service-layer"/>
  <text x="1275" y="95" text-anchor="middle" class="layer-title">服务层</text>
  
  <!-- 视觉训练服务 -->
  <rect x="1070" y="110" width="130" height="60" class="service-module"/>
  <text x="1135" y="130" text-anchor="middle" class="module-title">VisualTrainService</text>
  <text x="1080" y="145" class="text">训练流程控制</text>
  <text x="1080" y="160" class="text">任务调度管理</text>
  
  <!-- 眼动分析服务 -->
  <rect x="1210" y="110" width="130" height="60" class="service-module"/>
  <text x="1275" y="130" text-anchor="middle" class="module-title">GazeAnalysisService</text>
  <text x="1220" y="145" class="text">眼动数据分析</text>
  <text x="1220" y="160" class="text">训练效果评估</text>
  
  <!-- 任务生成服务 -->
  <rect x="1350" y="110" width="130" height="60" class="service-module"/>
  <text x="1415" y="130" text-anchor="middle" class="module-title">TaskGeneratorService</text>
  <text x="1360" y="145" class="text">动态任务生成</text>
  <text x="1360" y="160" class="text">难度自适应调整</text>
  
  <!-- 进度管理服务 -->
  <rect x="1070" y="180" width="130" height="60" class="service-module"/>
  <text x="1135" y="200" text-anchor="middle" class="module-title">ProgressService</text>
  <text x="1080" y="215" class="text">训练进度管理</text>
  <text x="1080" y="230" class="text">成绩统计分析</text>
  
  <!-- 反馈生成服务 -->
  <rect x="1210" y="180" width="130" height="60" class="service-module"/>
  <text x="1275" y="200" text-anchor="middle" class="module-title">FeedbackService</text>
  <text x="1220" y="215" class="text">实时反馈生成</text>
  <text x="1220" y="230" class="text">训练建议推荐</text>
  
  <!-- 数据同步服务 -->
  <rect x="1350" y="180" width="130" height="60" class="service-module"/>
  <text x="1415" y="200" text-anchor="middle" class="module-title">SyncService</text>
  <text x="1360" y="215" class="text">训练数据同步</text>
  <text x="1360" y="230" class="text">云端备份管理</text>

  <!-- 视图组件层 -->
  <rect x="50" y="270" width="450" height="150" class="view-layer"/>
  <text x="275" y="295" text-anchor="middle" class="layer-title">视图组件层</text>
  
  <!-- 训练视标显示 -->
  <rect x="70" y="310" width="130" height="50" class="view-module"/>
  <text x="135" y="330" text-anchor="middle" class="module-title">TrainTargetView</text>
  <text x="80" y="345" class="text">动态视标显示</text>
  <text x="80" y="355" class="text">轨迹动画效果</text>
  
  <!-- 眼动轨迹显示 -->
  <rect x="210" y="310" width="130" height="50" class="view-module"/>
  <text x="275" y="330" text-anchor="middle" class="module-title">GazeTrajectoryView</text>
  <text x="220" y="345" class="text">实时轨迹绘制</text>
  <text x="220" y="355" class="text">历史轨迹对比</text>
  
  <!-- 成绩统计图表 -->
  <rect x="350" y="310" width="130" height="50" class="view-module"/>
  <text x="415" y="330" text-anchor="middle" class="module-title">ScoreChartView</text>
  <text x="360" y="345" class="text">成绩趋势图表</text>
  <text x="360" y="355" class="text">能力雷达图</text>
  
  <!-- 训练控制组件 -->
  <rect x="140" y="370" width="200" height="40" class="view-module"/>
  <text x="240" y="390" text-anchor="middle" class="module-title">训练控制组件与交互反馈</text>
  <text x="150" y="405" class="text">开始/暂停/重置按钮、实时得分显示、音效反馈</text>

  <!-- API层 -->
  <rect x="550" y="270" width="450" height="150" class="api-layer"/>
  <text x="775" y="295" text-anchor="middle" class="layer-title">API层</text>
  
  <!-- 视觉训练API -->
  <rect x="570" y="310" width="130" height="50" class="api-module"/>
  <text x="635" y="330" text-anchor="middle" class="module-title">VisualTrainAPI</text>
  <text x="580" y="345" class="text">训练项目配置API</text>
  <text x="580" y="355" class="text">训练记录上传API</text>
  
  <!-- 数据仓库 -->
  <rect x="710" y="310" width="130" height="50" class="api-module"/>
  <text x="775" y="330" text-anchor="middle" class="module-title">TrainRepository</text>
  <text x="720" y="345" class="text">训练数据管理</text>
  <text x="720" y="355" class="text">本地缓存策略</text>
  
  <!-- 成绩分析API -->
  <rect x="850" y="310" width="130" height="50" class="api-module"/>
  <text x="915" y="330" text-anchor="middle" class="module-title">ScoreAnalysisAPI</text>
  <text x="860" y="345" class="text">成绩统计分析</text>
  <text x="860" y="355" class="text">能力评估报告</text>
  
  <!-- 网络客户端 -->
  <rect x="640" y="370" width="200" height="40" class="api-module"/>
  <text x="740" y="390" text-anchor="middle" class="module-title">网络客户端与数据管理</text>
  <text x="650" y="405" class="text">训练配置下载、成绩数据上传、离线训练支持</text>

  <!-- Native层 -->
  <rect x="1050" y="270" width="450" height="150" class="native-layer"/>
  <text x="1275" y="295" text-anchor="middle" class="layer-title">Native层</text>
  
  <!-- 眼动追踪算法 -->
  <rect x="1070" y="310" width="130" height="50" class="native-module"/>
  <text x="1135" y="330" text-anchor="middle" class="module-title">GazeTracking</text>
  <text x="1080" y="345" class="text">高精度眼动追踪</text>
  <text x="1080" y="355" class="text">实时坐标计算</text>
  
  <!-- 轨迹分析算法 -->
  <rect x="1210" y="310" width="130" height="50" class="native-module"/>
  <text x="1275" y="330" text-anchor="middle" class="module-title">TrajectoryAnalysis</text>
  <text x="1220" y="345" class="text">轨迹平滑算法</text>
  <text x="1220" y="355" class="text">精度计算分析</text>
  
  <!-- 图形渲染引擎 -->
  <rect x="1350" y="310" width="130" height="50" class="native-module"/>
  <text x="1415" y="330" text-anchor="middle" class="module-title">GraphicsEngine</text>
  <text x="1360" y="345" class="text">高性能图形渲染</text>
  <text x="1360" y="355" class="text">动画效果处理</text>
  
  <!-- C++训练引擎 -->
  <rect x="1140" y="370" width="200" height="40" class="native-module"/>
  <text x="1240" y="390" text-anchor="middle" class="module-title">C++视觉训练引擎</text>
  <text x="1150" y="405" class="text">高性能算法处理、实时数据分析、内存优化管理</text>

  <!-- 连接箭头 -->
  <line x1="275" y1="250" x2="775" y2="250" class="arrow"/>
  <line x1="500" y1="160" x2="550" y2="160" class="arrow"/>
  <line x1="1000" y1="160" x2="1050" y2="160" class="arrow"/>
  <line x1="275" y1="250" x2="275" y2="270" class="arrow"/>
  <line x1="775" y1="250" x2="775" y2="270" class="arrow"/>
  <line x1="1275" y1="250" x2="1275" y2="270" class="arrow"/>
  
  <line x1="635" y1="360" x2="1135" y2="360" class="data-arrow"/>
  <line x1="775" y1="360" x2="775" y2="310" class="data-arrow"/>
  <line x1="1275" y1="360" x2="1275" y2="310" class="data-arrow"/>

  <!-- 视觉训练疗法实现流程 -->
  <rect x="50" y="440" width="1450" height="720" style="fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2; rx: 10;"/>
  <text x="775" y="465" text-anchor="middle" class="title" style="font-size: 22px;">视觉训练疗法实现流程</text>

  <!-- 流程步骤 -->
  <text x="70" y="500" class="flow-text" style="font-weight: bold;">1. 初始化阶段：</text>
  <text x="90" y="520" class="flow-text">• 用户在VisualTrainFragment中查看可用训练项目</text>
  <text x="90" y="535" class="flow-text">• 加载用户历史训练记录和进度数据</text>
  <text x="90" y="550" class="flow-text">• 初始化VisualTrainService和GazeAnalysisService</text>
  <text x="90" y="565" class="flow-text">• 检查眼动追踪校准状态</text>

  <text x="70" y="595" class="flow-text" style="font-weight: bold;">2. 训练选择阶段：</text>
  <text x="90" y="615" class="flow-text">• TrainListFragment显示训练项目网格列表</text>
  <text x="90" y="630" class="flow-text">• 用户选择训练类型：追踪训练、扫视训练、注视稳定性等</text>
  <text x="90" y="645" class="flow-text">• 根据用户能力推荐合适的难度级别</text>
  <text x="90" y="660" class="flow-text">• 配置训练参数：速度、精度、持续时间</text>

  <text x="70" y="690" class="flow-text" style="font-weight: bold;">3. 训练准备阶段：</text>
  <text x="90" y="710" class="flow-text">• TaskGeneratorService生成训练任务序列</text>
  <text x="90" y="725" class="flow-text">• 根据难度级别设置视标大小、移动速度</text>
  <text x="90" y="740" class="flow-text">• 初始化TrainTargetView和GazeTrajectoryView</text>
  <text x="90" y="755" class="flow-text">• 启动眼动追踪，开始记录基线数据</text>

  <text x="70" y="785" class="flow-text" style="font-weight: bold;">4. 训练执行阶段：</text>
  <text x="90" y="805" class="flow-text">• TrainExecuteActivity启动训练任务</text>
  <text x="90" y="820" class="flow-text">• 显示动态视标，用户跟随视标移动</text>
  <text x="90" y="835" class="flow-text">• Native层实时追踪用户眼动轨迹</text>
  <text x="90" y="850" class="flow-text">• TrajectoryAnalysis分析轨迹精度和延迟</text>
  <text x="90" y="865" class="flow-text">• FeedbackService提供实时反馈和得分</text>

  <text x="70" y="895" class="flow-text" style="font-weight: bold;">5. 数据分析阶段：</text>
  <text x="90" y="915" class="flow-text">• GazeAnalysisService分析眼动数据质量</text>
  <text x="90" y="930" class="flow-text">• 计算追踪精度、反应时间、平滑度指标</text>
  <text x="90" y="945" class="flow-text">• 评估双眼协调性和注视稳定性</text>
  <text x="90" y="960" class="flow-text">• 生成训练效果评估报告</text>

  <!-- 右侧流程 -->
  <text x="800" y="500" class="flow-text" style="font-weight: bold;">6. 成绩记录阶段：</text>
  <text x="820" y="520" class="flow-text">• ProgressService记录训练成绩和进步情况</text>
  <text x="820" y="535" class="flow-text">• 保存完整的眼动轨迹数据</text>
  <text x="820" y="550" class="flow-text">• 更新用户能力评估档案</text>
  <text x="820" y="565" class="flow-text">• 计算训练完成度和准确率</text>

  <text x="800" y="595" class="flow-text" style="font-weight: bold;">7. 结果展示阶段：</text>
  <text x="820" y="615" class="flow-text">• TrainResultView显示本次训练成绩</text>
  <text x="820" y="630" class="flow-text">• ScoreChartView展示成绩趋势图表</text>
  <text x="820" y="645" class="flow-text">• 对比历史最佳成绩和平均水平</text>
  <text x="820" y="660" class="flow-text">• 显示能力雷达图和进步分析</text>

  <text x="800" y="690" class="flow-text" style="font-weight: bold;">8. 数据同步阶段：</text>
  <text x="820" y="710" class="flow-text">• SyncService上传训练数据到云端</text>
  <text x="820" y="725" class="flow-text">• 调用ScoreAnalysisAPI进行深度分析</text>
  <text x="820" y="740" class="flow-text">• 同步训练配置和个性化参数</text>
  <text x="820" y="755" class="flow-text">• 备份训练历史和进度数据</text>

  <text x="800" y="785" class="flow-text" style="font-weight: bold;">9. 自适应调整阶段：</text>
  <text x="820" y="805" class="flow-text">• 根据训练表现自动调整难度级别</text>
  <text x="820" y="820" class="flow-text">• 优化下次训练的参数配置</text>
  <text x="820" y="835" class="flow-text">• 推荐个性化的训练计划</text>
  <text x="820" y="850" class="flow-text">• 设置训练提醒和目标</text>

  <text x="800" y="880" class="flow-text" style="font-weight: bold;">10. 进度跟踪阶段：</text>
  <text x="820" y="900" class="flow-text">• ProgressTrackView更新长期进度</text>
  <text x="820" y="915" class="flow-text">• 分析训练效果和能力提升</text>
  <text x="820" y="930" class="flow-text">• 生成训练报告和建议</text>
  <text x="820" y="945" class="flow-text">• 制定下阶段训练目标</text>

  <!-- 核心特性说明 -->
  <rect x="70" y="980" width="1400" height="160" style="fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 8;"/>
  <text x="90" y="1005" class="flow-text" style="font-weight: bold; font-size: 16px;">视觉训练疗法核心特性：</text>

  <text x="90" y="1030" class="flow-text">• <tspan style="font-weight: bold;">多样化训练项目：</tspan>包含追踪训练、扫视训练、注视稳定性训练等多种视觉功能训练</text>
  <text x="90" y="1050" class="flow-text">• <tspan style="font-weight: bold;">自适应难度调整：</tspan>根据用户表现动态调整训练难度，确保训练效果最大化</text>
  <text x="90" y="1070" class="flow-text">• <tspan style="font-weight: bold;">精确眼动分析：</tspan>实时分析眼动轨迹，提供精确的追踪精度和反应时间评估</text>
  <text x="90" y="1090" class="flow-text">• <tspan style="font-weight: bold;">个性化训练计划：</tspan>基于用户能力档案制定个性化训练方案和进度目标</text>
  <text x="90" y="1110" class="flow-text">• <tspan style="font-weight: bold;">科学效果评估：</tspan>多维度评估训练效果，包括双眼协调性、注视稳定性等指标</text>
  <text x="90" y="1130" class="flow-text">• <tspan style="font-weight: bold;">游戏化训练体验：</tspan>结合游戏元素和即时反馈，提高训练的趣味性和持续性</text>

</svg>
