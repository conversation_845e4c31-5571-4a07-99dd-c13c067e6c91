<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #27ae60; }
      .component-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .websocket-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .data-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .h5-box { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .native-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .flow-arrow { stroke: #ff5722; stroke-width: 3; fill: none; marker-end: url(#arrow); }
      .data-arrow { stroke: #2196f3; stroke-width: 2; fill: none; marker-end: url(#dataarrow); }
      .json-arrow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#jsonarrow); }
    </style>
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff5722" />
    </marker>
    <marker id="dataarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
    </marker>
    <marker id="jsonarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">眼动追踪WebSocket服务架构</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">WebSocket实现眼动数据与H5页面实时交互</text>

  <!-- 硬件层 -->
  <rect x="50" y="80" width="200" height="100" class="component-box" rx="8"/>
  <text x="150" y="100" text-anchor="middle" class="subtitle">硬件层</text>
  <text x="60" y="120" class="text">📷 相机采集 (4048x3040)</text>
  <text x="60" y="135" class="text">👁️ 眼动数据捕获</text>
  <text x="60" y="150" class="text">🔍 图像预处理</text>
  <text x="60" y="165" class="code">ImageProxy → ByteArray</text>

  <!-- Native层 -->
  <rect x="300" y="80" width="200" height="100" class="native-box" rx="8"/>
  <text x="400" y="100" text-anchor="middle" class="subtitle">Native层 (JNI)</text>
  <text x="310" y="120" class="text">🧠 AI模型推理</text>
  <text x="310" y="135" class="text">📊 轨迹数据处理</text>
  <text x="310" y="150" class="code">nativeGazeTracking()</text>
  <text x="310" y="165" class="code">GazeStare::collect_data()</text>

  <!-- 服务层 -->
  <rect x="550" y="80" width="250" height="100" class="component-box" rx="8"/>
  <text x="675" y="100" text-anchor="middle" class="subtitle">服务层</text>
  <text x="560" y="120" class="text">🚀 GazeTrackService</text>
  <text x="560" y="135" class="text">⚙️ TrackingManager</text>
  <text x="560" y="150" class="code">onGazeTracking(result)</text>
  <text x="560" y="165" class="code">AppliedManager.analyseTrackResult()</text>

  <!-- WebSocket服务 -->
  <rect x="850" y="80" width="250" height="100" class="websocket-box" rx="8"/>
  <text x="975" y="100" text-anchor="middle" class="subtitle">WebSocket服务</text>
  <text x="860" y="120" class="text">🌐 GazeWebSocketService</text>
  <text x="860" y="135" class="text">📡 端口: 9200</text>
  <text x="860" y="150" class="code">mGazeWebSocketService?.broadcast()</text>
  <text x="860" y="165" class="code">InetSocketAddress(localhost, 9200)</text>

  <!-- 数据流箭头 -->
  <line x1="250" y1="130" x2="300" y2="130" class="flow-arrow"/>
  <line x1="500" y1="130" x2="550" y2="130" class="flow-arrow"/>
  <line x1="800" y1="130" x2="850" y2="130" class="flow-arrow"/>

  <!-- 数据结构展示 -->
  <rect x="50" y="220" width="450" height="200" class="data-box" rx="8"/>
  <text x="275" y="240" text-anchor="middle" class="subtitle">眼动数据结构</text>
  
  <!-- GazeTrackResult -->
  <rect x="70" y="250" width="180" height="160" class="data-box" rx="5"/>
  <text x="160" y="270" text-anchor="middle" class="subtitle">GazeTrackResult</text>
  <text x="80" y="290" class="code">valid: Boolean</text>
  <text x="80" y="305" class="code">skew: Boolean</text>
  <text x="80" y="320" class="code">x: Float [0~1]</text>
  <text x="80" y="335" class="code">y: Float [0~1]</text>
  <text x="80" y="350" class="code">dist: Float [35~65cm]</text>
  <text x="80" y="365" class="code">duration: Int (ms)</text>
  <text x="80" y="380" class="code">timestamp: Long</text>
  <text x="80" y="395" class="code">confidence: Float</text>

  <!-- JSON格式 -->
  <rect x="270" y="250" width="210" height="160" class="data-box" rx="5"/>
  <text x="375" y="270" text-anchor="middle" class="subtitle">JSON格式</text>
  <text x="280" y="290" class="code">{</text>
  <text x="290" y="305" class="code">"valid": true,</text>
  <text x="290" y="320" class="code">"x": 0.525,</text>
  <text x="290" y="335" class="code">"y": 0.347,</text>
  <text x="290" y="350" class="code">"dist": 45.2,</text>
  <text x="290" y="365" class="code">"duration": 150,</text>
  <text x="290" y="380" class="code">"timestamp": 1642658421000</text>
  <text x="280" y="395" class="code">}</text>

  <!-- WebSocket消息类型 -->
  <rect x="550" y="220" width="300" height="200" class="websocket-box" rx="8"/>
  <text x="700" y="240" text-anchor="middle" class="subtitle">WebSocket消息类型</text>
  
  <!-- 实时数据广播 -->
  <rect x="570" y="250" width="120" height="80" class="websocket-box" rx="5"/>
  <text x="630" y="270" text-anchor="middle" class="subtitle">实时数据</text>
  <text x="580" y="290" class="code">眼动轨迹</text>
  <text x="580" y="305" class="code">注视点坐标</text>
  <text x="580" y="320" class="code">30fps频率</text>

  <!-- 校准消息 -->
  <rect x="710" y="250" width="120" height="80" class="websocket-box" rx="5"/>
  <text x="770" y="270" text-anchor="middle" class="subtitle">校准消息</text>
  <text x="720" y="290" class="code">GazeMessage</text>
  <text x="720" y="305" class="code">CalibrationResult</text>
  <text x="720" y="320" class="code">PostureResult</text>

  <!-- 控制命令 -->
  <rect x="570" y="350" width="120" height="60" class="websocket-box" rx="5"/>
  <text x="630" y="370" text-anchor="middle" class="subtitle">控制命令</text>
  <text x="580" y="390" class="code">START_TRACK</text>
  <text x="580" y="405" class="code">STOP_TRACK</text>

  <!-- 状态通知 -->
  <rect x="710" y="350" width="120" height="60" class="websocket-box" rx="5"/>
  <text x="770" y="370" text-anchor="middle" class="subtitle">状态通知</text>
  <text x="720" y="390" class="code">SERVICE_STATE</text>
  <text x="720" y="405" class="code">ERROR_MESSAGE</text>

  <!-- H5页面层 -->
  <rect x="900" y="220" width="450" height="200" class="h5-box" rx="8"/>
  <text x="1125" y="240" text-anchor="middle" class="subtitle">H5页面交互层</text>
  
  <!-- WebSocket客户端 -->
  <rect x="920" y="250" width="180" height="160" class="h5-box" rx="5"/>
  <text x="1010" y="270" text-anchor="middle" class="subtitle">WebSocket客户端</text>
  <text x="930" y="290" class="code">const ws = new WebSocket(</text>
  <text x="940" y="305" class="code">'ws://localhost:9200'</text>
  <text x="930" y="320" class="code">);</text>
  <text x="930" y="340" class="code">ws.onmessage = (event) => {</text>
  <text x="940" y="355" class="code">const data = JSON.parse(</text>
  <text x="940" y="370" class="code">event.data);</text>
  <text x="940" y="385" class="code">updateGazePoint(data);</text>
  <text x="930" y="400" class="code">};</text>

  <!-- 可视化渲染 -->
  <rect x="1130" y="250" width="200" height="160" class="h5-box" rx="5"/>
  <text x="1230" y="270" text-anchor="middle" class="subtitle">可视化渲染</text>
  <text x="1140" y="290" class="code">Canvas/SVG绘制</text>
  <text x="1140" y="305" class="code">实时注视点显示</text>
  <text x="1140" y="320" class="code">轨迹路径绘制</text>
  <text x="1140" y="335" class="code">热力图生成</text>
  <text x="1140" y="350" class="code">统计数据展示</text>
  <text x="1140" y="365" class="code">交互式控制</text>
  <text x="1140" y="380" class="code">实时性能监控</text>
  <text x="1140" y="395" class="code">用户操作反馈</text>

  <!-- 数据流向 -->
  <line x1="850" y1="130" x2="975" y2="220" class="data-arrow"/>
  <line x1="975" y1="180" x2="1125" y2="220" class="json-arrow"/>

  <!-- 核心实现流程 -->
  <rect x="50" y="460" width="1300" height="300" class="component-box" rx="8"/>
  <text x="700" y="480" text-anchor="middle" class="subtitle">WebSocket服务核心实现流程</text>
  
  <!-- 步骤1 -->
  <rect x="70" y="500" width="240" height="80" class="websocket-box" rx="5"/>
  <text x="190" y="520" text-anchor="middle" class="subtitle">1. 服务初始化</text>
  <text x="80" y="540" class="code">startWebSocketServer() {</text>
  <text x="90" y="555" class="code">val address = InetSocketAddress(</text>
  <text x="100" y="570" class="code">localhost, 9200)</text>
  <text x="80" y="585" class="code">}</text>

  <!-- 步骤2 -->
  <rect x="330" y="500" width="240" height="80" class="data-box" rx="5"/>
  <text x="450" y="520" text-anchor="middle" class="subtitle">2. 眼动数据采集</text>
  <text x="340" y="540" class="code">onGazeTracking(result) {</text>
  <text x="350" y="555" class="code">val json = gson.toJson(result)</text>
  <text x="350" y="570" class="code">broadcast(json)</text>
  <text x="340" y="585" class="code">}</text>

  <!-- 步骤3 -->
  <rect x="590" y="500" width="240" height="80" class="websocket-box" rx="5"/>
  <text x="710" y="520" text-anchor="middle" class="subtitle">3. 实时数据广播</text>
  <text x="600" y="540" class="code">broadcast(message) {</text>
  <text x="610" y="555" class="code">connections.forEach { conn -></text>
  <text x="620" y="570" class="code">conn.send(message)</text>
  <text x="610" y="585" class="code">}</text>

  <!-- 步骤4 -->
  <rect x="850" y="500" width="240" height="80" class="h5-box" rx="5"/>
  <text x="970" y="520" text-anchor="middle" class="subtitle">4. H5页面接收</text>
  <text x="860" y="540" class="code">ws.onmessage = (event) => {</text>
  <text x="870" y="555" class="code">const gazeData = JSON.parse(</text>
  <text x="880" y="570" class="code">event.data);</text>
  <text x="860" y="585" class="code">}</text>

  <!-- 步骤5 -->
  <rect x="1110" y="500" width="240" height="80" class="h5-box" rx="5"/>
  <text x="1230" y="520" text-anchor="middle" class="subtitle">5. 界面更新</text>
  <text x="1120" y="540" class="code">updateGazePoint(data) {</text>
  <text x="1130" y="555" class="code">canvas.drawCircle(</text>
  <text x="1140" y="570" class="code">data.x, data.y);</text>
  <text x="1120" y="585" class="code">}</text>

  <!-- 流程箭头 -->
  <line x1="310" y1="540" x2="330" y2="540" class="flow-arrow"/>
  <line x1="570" y1="540" x2="590" y2="540" class="flow-arrow"/>
  <line x1="830" y1="540" x2="850" y2="540" class="flow-arrow"/>
  <line x1="1090" y1="540" x2="1110" y2="540" class="flow-arrow"/>

  <!-- 消息类型详解 -->
  <rect x="70" y="600" width="380" height="140" class="data-box" rx="5"/>
  <text x="260" y="620" text-anchor="middle" class="subtitle">消息类型详解</text>
  <text x="80" y="640" class="code">1. 实时眼动数据: GazeTrackResult JSON</text>
  <text x="80" y="655" class="code">2. 校准消息: GazeMessage&lt;CalibrationResult&gt;</text>
  <text x="80" y="670" class="code">3. 姿势校准: GazeMessage&lt;PostureCalibrationResult&gt;</text>
  <text x="80" y="685" class="code">4. 坐标校准: GazeMessage&lt;CalibrateCoordinate&gt;</text>
  <text x="80" y="700" class="code">5. 轨迹数据: JSON格式眼动轨迹</text>
  <text x="80" y="715" class="code">6. 控制命令: START/STOP 等操作指令</text>
  <text x="80" y="730" class="code">7. 状态通知: 服务状态变化通知</text>

  <!-- 性能优化 -->
  <rect x="470" y="600" width="380" height="140" class="component-box" rx="5"/>
  <text x="660" y="620" text-anchor="middle" class="subtitle">性能优化策略</text>
  <text x="480" y="640" class="code">📊 30fps数据采集频率控制</text>
  <text x="480" y="655" class="code">🔄 异步数据处理 (Dispatchers.IO)</text>
  <text x="480" y="670" class="code">💾 内存优化 (image.close())</text>
  <text x="480" y="685" class="code">🌐 连接管理 (自动重连机制)</text>
  <text x="480" y="700" class="code">🔒 线程安全 (ConcurrentHashMap)</text>
  <text x="480" y="715" class="code">⚡ 数据压缩 (JSON最小化)</text>
  <text x="480" y="730" class="code">📈 性能监控 (延迟测量)</text>

  <!-- 应用场景 -->
  <rect x="870" y="600" width="480" height="140" class="h5-box" rx="5"/>
  <text x="1110" y="620" text-anchor="middle" class="subtitle">应用场景</text>
  <text x="880" y="640" class="code">🎯 实时眼动追踪可视化</text>
  <text x="880" y="655" class="code">🏥 医疗诊断辅助界面</text>
  <text x="880" y="670" class="code">📚 阅读行为分析</text>
  <text x="880" y="685" class="code">🎮 游戏交互控制</text>
  <text x="880" y="700" class="code">🎓 学习专注度监测</text>
  <text x="880" y="715" class="code">🔬 科研数据采集</text>
  <text x="880" y="730" class="code">🌐 远程眼动数据传输</text>

  <!-- 技术栈标注 -->
  <rect x="50" y="780" width="1300" height="60" class="native-box" rx="8"/>
  <text x="700" y="800" text-anchor="middle" class="subtitle">技术栈</text>
  <text x="60" y="820" class="text">
    🔧 <tspan class="subtitle">后端</tspan>: Kotlin + JNI + C++ + OpenCV + RKNN | 
    🌐 <tspan class="subtitle">WebSocket</tspan>: Java-WebSocket Library | 
    🎨 <tspan class="subtitle">前端</tspan>: HTML5 + JavaScript + Canvas/WebGL | 
    📱 <tspan class="subtitle">Android</tspan>: CameraX + Lifecycle + Coroutines
  </text>

  <!-- 版本信息 -->
  <text x="700" y="860" text-anchor="middle" class="text">MIT DD GazeTracker v1.2.0 - WebSocket眼动数据实时传输架构</text>
</svg>
