<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: Courier New, monospace; font-size: 10px; fill: #e74c3c; }
      .highlight { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .note { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
    </style>
    
    <!-- 渐变色定义 -->
    <linearGradient id="serverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a5d6a7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="clientGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#90caf9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc80;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1200" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" class="title">为什么眼动校准需要WebSocket连接？为什么延迟2秒？</text>
  
  <!-- 问题背景 -->
  <rect x="50" y="60" width="1500" height="80" rx="10" fill="#fff9c4" stroke="#f9a825" stroke-width="2"/>
  <text x="800" y="85" text-anchor="middle" class="highlight">🤔 核心问题分析</text>
  <text x="70" y="110" class="text">• WebSocket连接地址: ws://127.0.0.1:9200/Tracker (本地回环地址)</text>
  <text x="70" y="125" class="text">• 延迟连接: lifecycleScope.launch { delay(2000); mCalibrationWSClient.connect() }</text>
  
  <!-- WebSocket架构图 -->
  <rect x="50" y="160" width="1500" height="300" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="185" text-anchor="middle" class="subtitle">WebSocket通信架构</text>
  
  <!-- 服务端 -->
  <rect x="100" y="210" width="300" height="200" rx="8" fill="url(#serverGradient)" stroke="#4caf50" stroke-width="2"/>
  <text x="250" y="235" text-anchor="middle" class="subtitle">WebSocket服务端</text>
  <text x="250" y="255" text-anchor="middle" class="highlight">GazeTrackService</text>
  
  <text x="120" y="280" class="text">• 启动时机: Service.onCreate()</text>
  <text x="120" y="300" class="text">• 监听端口: 127.0.0.1:9200</text>
  <text x="120" y="320" class="text">• 服务类: GazeWebSocketService</text>
  <text x="120" y="340" class="text">• 数据广播: broadcast(json)</text>
  
  <rect x="120" y="360" width="260" height="40" rx="5" fill="#c8e6c9"/>
  <text x="250" y="375" text-anchor="middle" class="code">startWebSocketServer()</text>
  <text x="250" y="390" text-anchor="middle" class="code">InetSocketAddress(localhost, 9200)</text>
  
  <!-- 客户端 -->
  <rect x="450" y="210" width="300" height="200" rx="8" fill="url(#clientGradient)" stroke="#2196f3" stroke-width="2"/>
  <text x="600" y="235" text-anchor="middle" class="subtitle">WebSocket客户端</text>
  <text x="600" y="255" text-anchor="middle" class="highlight">CalibrationActivity</text>
  
  <text x="470" y="280" class="text">• 连接时机: onCreate() + delay(2s)</text>
  <text x="470" y="300" class="text">• 连接地址: ws://127.0.0.1:9200/Tracker</text>
  <text x="470" y="320" class="text">• 客户端类: CalibrationWSClient</text>
  <text x="470" y="340" class="text">• 数据接收: onMessage(json)</text>
  
  <rect x="470" y="360" width="260" height="40" rx="5" fill="#bbdefb"/>
  <text x="600" y="375" text-anchor="middle" class="code">mCalibrationWSClient.connect()</text>
  <text x="600" y="390" text-anchor="middle" class="code">URI("ws://127.0.0.1:9200/Tracker")</text>
  
  <!-- 数据流 -->
  <rect x="800" y="210" width="300" height="200" rx="8" fill="url(#dataGradient)" stroke="#ff9800" stroke-width="2"/>
  <text x="950" y="235" text-anchor="middle" class="subtitle">传输数据</text>
  <text x="950" y="255" text-anchor="middle" class="highlight">CalibrateCoordinate</text>
  
  <text x="820" y="280" class="text">• 校准点坐标: x, y [0,1]</text>
  <text x="820" y="300" class="text">• 校准状态: state (完成/进行中)</text>
  <text x="820" y="320" class="text">• 校准结果: succeed (成功/失败)</text>
  <text x="820" y="340" class="text">• 校准评分: score [0,1]</text>
  
  <rect x="820" y="360" width="260" height="40" rx="5" fill="#ffe0b2"/>
  <text x="950" y="375" text-anchor="middle" class="code">ACTION_CALIBRATE_COORDINATE</text>
  <text x="950" y="390" text-anchor="middle" class="code">GazeMessage&lt;CalibrateCoordinate&gt;</text>
  
  <!-- 连接箭头 -->
  <line x1="400" y1="310" x2="450" y2="310" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="425" y="305" text-anchor="middle" class="note">连接</text>
  
  <line x1="750" y1="310" x2="800" y2="310" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="775" y="305" text-anchor="middle" class="note">传输</text>
  
  <!-- 为什么需要WebSocket -->
  <rect x="50" y="480" width="1500" height="200" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="505" text-anchor="middle" class="subtitle">为什么需要WebSocket连接？</text>
  
  <text x="70" y="535" class="highlight">1. 实时数据传输需求</text>
  <text x="90" y="555" class="text">• C++校准引擎产生的校准坐标需要实时传输到UI界面</text>
  <text x="90" y="575" class="text">• 校准过程中视标位置需要根据算法结果动态更新</text>
  <text x="90" y="595" class="text">• 传统的Intent/Bundle方式无法满足高频实时通信需求</text>
  
  <text x="70" y="625" class="highlight">2. 跨进程通信架构</text>
  <text x="90" y="645" class="text">• GazeTrackService作为独立服务运行，需要与CalibrationActivity通信</text>
  <text x="90" y="665" class="text">• WebSocket提供了稳定的双向通信通道</text>
  
  <text x="800" y="535" class="highlight">3. 数据流向分析</text>
  <text x="820" y="555" class="text">• C++引擎 → TrackingManager → GazeTrackService</text>
  <text x="820" y="575" class="text">• GazeTrackService → WebSocket广播 → CalibrationActivity</text>
  <text x="820" y="595" class="text">• CalibrationActivity → VisualCalibrationView → UI更新</text>
  
  <text x="800" y="625" class="highlight">4. 解耦合设计</text>
  <text x="820" y="645" class="text">• 校准算法与UI界面完全分离</text>
  <text x="820" y="665" class="text">• 支持多客户端同时监听校准状态</text>
  
  <!-- 为什么延迟2秒 -->
  <rect x="50" y="700" width="1500" height="180" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="725" text-anchor="middle" class="subtitle">为什么需要延迟2秒连接？</text>
  
  <text x="70" y="755" class="highlight">1. 服务启动时序问题</text>
  <text x="90" y="775" class="text">• CalibrationActivity.onCreate() 启动 GazeTrackService</text>
  <text x="90" y="795" class="text">• Service.onCreate() 需要时间初始化WebSocket服务器</text>
  <text x="90" y="815" class="text">• 如果立即连接，服务器可能还未完全启动，导致连接失败</text>
  
  <text x="800" y="755" class="highlight">2. 网络端口绑定延迟</text>
  <text x="820" y="775" class="text">• InetSocketAddress(localhost, 9200) 端口绑定需要时间</text>
  <text x="820" y="795" class="text">• 操作系统分配端口资源存在延迟</text>
  <text x="820" y="815" class="text">• 2秒延迟确保服务器完全就绪</text>
  
  <text x="70" y="845" class="highlight">3. 避免竞态条件</text>
  <text x="90" y="865" class="text">• 防止客户端连接时服务器还在初始化状态</text>
  
  <text x="800" y="845" class="highlight">4. 经验值设定</text>
  <text x="820" y="865" class="text">• 2秒是经过测试的稳定连接时间</text>
  
  <!-- 时序图 -->
  <rect x="50" y="900" width="1500" height="250" rx="10" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
  <text x="800" y="925" text-anchor="middle" class="subtitle">连接时序详解</text>
  
  <!-- 时间轴 -->
  <line x1="100" y1="960" x2="1500" y2="960" stroke="#bdc3c7" stroke-width="2"/>
  
  <!-- 时间点标记 -->
  <circle cx="200" cy="960" r="5" fill="#e74c3c"/>
  <text x="200" y="980" text-anchor="middle" class="text">T0: onCreate()</text>
  <text x="200" y="995" text-anchor="middle" class="note">启动Service</text>
  
  <circle cx="400" cy="960" r="5" fill="#f39c12"/>
  <text x="400" y="980" text-anchor="middle" class="text">T0+500ms</text>
  <text x="400" y="995" text-anchor="middle" class="note">Service初始化</text>
  
  <circle cx="600" cy="960" r="5" fill="#3498db"/>
  <text x="600" y="980" text-anchor="middle" class="text">T0+1000ms</text>
  <text x="600" y="995" text-anchor="middle" class="note">WebSocket启动</text>
  
  <circle cx="800" cy="960" r="5" fill="#27ae60"/>
  <text x="800" y="980" text-anchor="middle" class="text">T0+2000ms</text>
  <text x="800" y="995" text-anchor="middle" class="note">客户端连接</text>
  
  <circle cx="1000" cy="960" r="5" fill="#9b59b6"/>
  <text x="1000" y="980" text-anchor="middle" class="text">T0+2100ms</text>
  <text x="1000" y="995" text-anchor="middle" class="note">连接建立</text>
  
  <!-- 代码示例 -->
  <rect x="100" y="1020" width="600" height="100" rx="5" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="400" y="1040" text-anchor="middle" class="subtitle">关键代码</text>
  <text x="120" y="1060" class="code">// CalibrationActivity.onCreate()</text>
  <text x="120" y="1075" class="code">startForegroundService(Intent(this, GazeTrackService::class.java))</text>
  <text x="120" y="1090" class="code">lifecycleScope.launch { delay(2000); mCalibrationWSClient.connect() }</text>
  <text x="120" y="1105" class="code">// 确保服务器完全启动后再连接</text>
  
  <rect x="750" y="1020" width="600" height="100" rx="5" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="1050" y="1040" text-anchor="middle" class="subtitle">数据传输示例</text>
  <text x="770" y="1060" class="code">// GazeTrackService广播校准数据</text>
  <text x="770" y="1075" class="code">val gazeMessage = GazeMessage&lt;CalibrateCoordinate&gt;()</text>
  <text x="770" y="1090" class="code">mGazeWebSocketService?.broadcast(gson.toJson(gazeMessage))</text>
  <text x="770" y="1105" class="code">// CalibrationActivity接收并更新UI</text>
  
  <!-- 总结 -->
  <rect x="100" y="1160" width="1400" height="30" rx="5" fill="#e8f5e8"/>
  <text x="800" y="1180" text-anchor="middle" class="highlight">
    总结：WebSocket用于校准数据的实时传输，2秒延迟确保服务器完全启动，避免连接失败
  </text>
</svg>
