<?xml version="1.0" encoding="UTF-8"?>
<svg width="2000" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 40px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .section-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; fill: white; }
      .reason-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #2c3e50; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #555; }
      .problem-text { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 13px; fill: #e74c3c; font-weight: bold; }
      .advantage-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; fill: #27ae60; font-weight: bold; }
      .process-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      
      .home-box { fill: #ffebee; stroke: #f44336; stroke-width: 3; }
      .hospital-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; }
      .code-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1; }
      .process-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .result-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      
      .need-release-arrow { stroke: #f44336; stroke-width: 4; marker-end: url(#redArrow); }
      .no-release-arrow { stroke: #4caf50; stroke-width: 4; marker-end: url(#greenArrow); }
      .process-arrow { stroke: #ff9800; stroke-width: 3; marker-end: url(#orangeArrow); }
      
      .vs-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 60px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
    </style>
    
    <marker id="redArrow" markerWidth="15" markerHeight="12" refX="12" refY="6" orient="auto">
      <polygon points="0 0, 15 6, 0 12" fill="#f44336"/>
    </marker>
    <marker id="greenArrow" markerWidth="15" markerHeight="12" refX="12" refY="6" orient="auto">
      <polygon points="0 0, 15 6, 0 12" fill="#4caf50"/>
    </marker>
    <marker id="orangeArrow" markerWidth="12" markerHeight="10" refX="10" refY="5" orient="auto">
      <polygon points="0 0, 12 5, 0 10" fill="#ff9800"/>
    </marker>
    
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 主标题 -->
  <text x="1000" y="50" class="main-title">为什么进院版不需要发版 VS 为什么家庭版需要发版</text>
  
  <!-- VS 标识 -->
  <text x="1000" y="120" class="vs-text">VS</text>

  <!-- 家庭版 - 左侧：为什么需要发版 -->
  <rect x="50" y="160" width="900" height="1400" class="home-box" rx="20" filter="url(#shadow)"/>
  <text x="500" y="200" class="section-title" style="fill: #d32f2f;">📱 医疗家庭版 - 为什么需要发版？</text>

  <!-- 原因1：模块配置固化 -->
  <rect x="80" y="230" width="840" height="200" class="code-box" rx="10"/>
  <text x="500" y="260" class="reason-title" style="text-anchor: middle;">❌ 原因1: 模块配置固化在代码中</text>
  
  <text x="100" y="285" class="code-text">// TreatmentModule.kt - 枚举写死在代码中</text>
  <text x="100" y="300" class="code-text">enum class TreatmentModule(val moduleKey:String) {</text>
  <text x="120" y="315" class="code-text">OCCLUSION_THERAPY("occlusion-therapy"),    // 固化配置</text>
  <text x="120" y="330" class="code-text">VISION_THERAPY("vision-therapy")           // 固化配置</text>
  <text x="100" y="345" class="code-text">}</text>
  
  <text x="100" y="370" class="problem-text">问题：新增模块必须修改此枚举 → 重新编译 → 发版</text>
  <text x="100" y="390" class="problem-text">影响：无法动态添加或删除模块</text>
  <text x="100" y="410" class="problem-text">结果：任何模块变更都需要发版更新</text>

  <!-- 原因2：Fragment嵌入架构 -->
  <rect x="80" y="450" width="840" height="220" class="code-box" rx="10"/>
  <text x="500" y="480" class="reason-title" style="text-anchor: middle;">❌ 原因2: Fragment嵌入式架构高耦合</text>
  
  <text x="100" y="505" class="code-text">// TreatmentModuleAdapter.kt - Fragment映射硬编码</text>
  <text x="100" y="520" class="code-text">when(module.moduleKey){</text>
  <text x="120" y="535" class="code-text">TreatmentModule.OCCLUSION_THERAPY.moduleKey -> {</text>
  <text x="140" y="550" class="code-text">// Fragment直接嵌入，编译时绑定</text>
  <text x="140" y="565" class="code-text">beginTransaction.replace(flModuleRoot.id,</text>
  <text x="160" y="580" class="code-text">MaskTherapyFragment.newInstance())</text>
  <text x="120" y="595" class="code-text">}</text>
  <text x="120" y="610" class="code-text">TreatmentModule.VISION_THERAPY.moduleKey -> {</text>
  <text x="140" y="625" class="code-text">beginTransaction.replace(flModuleRoot.id,</text>
  <text x="160" y="640" class="code-text">VisualTrainFragment.newInstance())</text>
  <text x="120" y="655" class="code-text">}</text>
  <text x="100" y="670" class="code-text">}</text>

  <!-- 原因3：业务逻辑硬编码 -->
  <rect x="80" y="690" width="840" height="180" class="code-box" rx="10"/>
  <text x="500" y="720" class="reason-title" style="text-anchor: middle;">❌ 原因3: 业务逻辑硬编码</text>
  
  <text x="100" y="745" class="code-text">// HomeMainFragment.kt - 过滤逻辑写死</text>
  <text x="100" y="760" class="code-text">val filterModules = modules.filter { module -></text>
  <text x="120" y="775" class="code-text">module.moduleEnable == true &&</text>
  <text x="120" y="790" class="code-text">(module.moduleKey == TreatmentModule.OCCLUSION_THERAPY.moduleKey ||</text>
  <text x="120" y="805" class="code-text"> module.moduleKey == TreatmentModule.VISION_THERAPY.moduleKey)</text>
  <text x="100" y="820" class="code-text">}</text>
  
  <text x="100" y="845" class="problem-text">问题：过滤条件硬编码，调整业务规则需要修改源码</text>
  <text x="100" y="860" class="problem-text">结果：业务流程调整必须发版</text>

  <!-- 家庭版发版流程 -->
  <rect x="80" y="890" width="840" height="280" class="process-box" rx="10"/>
  <text x="500" y="920" class="reason-title" style="text-anchor: middle;">🔄 家庭版新增模块的发版流程</text>
  
  <text x="100" y="950" class="process-text" style="font-weight: bold;">步骤1: 修改代码 (耗时2-3天)</text>
  <text x="120" y="970" class="process-text">• 修改 TreatmentModule 枚举添加新模块</text>
  <text x="120" y="985" class="process-text">• 修改 TreatmentModuleAdapter 添加Fragment映射</text>
  <text x="120" y="1000" class="process-text">• 修改 HomeMainFragment 更新过滤逻辑</text>
  <text x="120" y="1015" class="process-text">• 开发新的Fragment实现业务逻辑</text>
  
  <text x="100" y="1040" class="process-text" style="font-weight: bold;">步骤2: 编译测试 (耗时3-5天)</text>
  <text x="120" y="1060" class="process-text">• 编译打包 → 单元测试 → 集成测试 → UI测试</text>
  
  <text x="100" y="1085" class="process-text" style="font-weight: bold;">步骤3: 发版更新 (耗时1-2周)</text>
  <text x="120" y="1105" class="process-text">• 提交应用商店审核 → 等待审核通过 → 用户更新</text>
  
  <text x="100" y="1130" class="problem-text" style="font-weight: bold;">总耗时: 2-3周，用户才能使用新功能</text>
  <text x="100" y="1150" class="problem-text">风险: 发版后发现问题需要再次发版修复</text>

  <!-- 家庭版结果 -->
  <rect x="80" y="1190" width="840" height="120" class="result-box" rx="10"/>
  <text x="500" y="1220" class="reason-title" style="text-anchor: middle;">📊 家庭版的局限性结果</text>
  <text x="120" y="1245" class="problem-text">❌ 配置更新周期: 2-3周 (发版周期)</text>
  <text x="120" y="1265" class="problem-text">❌ 业务响应速度: 慢 (需要发版)</text>
  <text x="120" y="1285" class="problem-text">❌ 定制化能力: 0% (无法个性化)</text>

  <!-- 进院版 - 右侧：为什么不需要发版 -->
  <rect x="1050" y="160" width="900" height="1400" class="hospital-box" rx="20" filter="url(#glow)"/>
  <text x="1500" y="200" class="section-title" style="fill: #2e7d32;">🏥 医疗进院版 - 为什么不需要发版？</text>

  <!-- 优势1：服务器端动态配置 -->
  <rect x="1080" y="230" width="840" height="200" class="code-box" rx="10"/>
  <text x="1500" y="260" class="reason-title" style="text-anchor: middle;">✅ 优势1: 服务器端动态配置</text>

  <text x="1100" y="285" class="code-text">// MHospitalMode.kt - 完全动态的数据结构</text>
  <text x="1100" y="300" class="code-text">data class MHospitalMode(</text>
  <text x="1120" y="315" class="code-text">var moduleEnable: Boolean?,      // 服务器端实时控制</text>
  <text x="1120" y="330" class="code-text">var moduleKey: String?,          // 动态模块标识</text>
  <text x="1120" y="345" class="code-text">var url: String?,                // 动态URL配置</text>
  <text x="1120" y="360" class="code-text">var loginAuthToken: String?      // 动态认证信息</text>
  <text x="1100" y="375" class="code-text">)</text>

  <text x="1100" y="400" class="advantage-text">优势：所有配置来自服务器API，可实时修改</text>
  <text x="1100" y="420" class="advantage-text">结果：无需修改代码即可调整模块配置</text>

  <!-- 优势2：Activity跳转架构 -->
  <rect x="1080" y="450" width="840" height="220" class="code-box" rx="10"/>
  <text x="1500" y="480" class="reason-title" style="text-anchor: middle;">✅ 优势2: Activity跳转架构完全解耦</text>

  <text x="1100" y="505" class="code-text">// HospitalMainFragment.kt - 动态路由系统</text>
  <text x="1100" y="520" class="code-text">mModuleAdapter.onItemClick = { module -></text>
  <text x="1120" y="535" class="code-text">when(module.moduleKey){  // module来自服务器配置</text>
  <text x="1140" y="550" class="code-text">HospitalModuleKey.INSPECTION_CENTER.moduleKey -> {</text>
  <text x="1160" y="565" class="code-text">// Activity跳转，模块完全独立</text>
  <text x="1160" y="580" class="code-text">startActivity(InspectionCenterActivity.createIntent(</text>
  <text x="1180" y="595" class="code-text">mActivity, module.url))</text>
  <text x="1140" y="610" class="code-text">}</text>
  <text x="1140" y="625" class="code-text">HospitalModuleKey.TRAINING_CENTER.moduleKey -> {</text>
  <text x="1160" y="640" class="code-text">startActivity(TrainCenterActivity.createIntent(</text>
  <text x="1180" y="655" class="code-text">mActivity, module.url))</text>
  <text x="1140" y="670" class="code-text">}</text>

  <!-- 优势3：实时配置获取 -->
  <rect x="1080" y="690" width="840" height="180" class="code-box" rx="10"/>
  <text x="1500" y="720" class="reason-title" style="text-anchor: middle;">✅ 优势3: 实时配置获取机制</text>

  <text x="1100" y="745" class="code-text">// HospitalViewModel.kt - 动态配置获取</text>
  <text x="1100" y="760" class="code-text">fun getHospitalEditionProfile(){</text>
  <text x="1120" y="775" class="code-text">// 调用API实时获取最新配置</text>
  <text x="1120" y="790" class="code-text">hospitalRepository.getHospitalEditionProfile()</text>
  <text x="1100" y="805" class="code-text">}</text>

  <text x="1100" y="830" class="advantage-text">优势：启动时获取最新配置，无需硬编码</text>
  <text x="1100" y="850" class="advantage-text">结果：配置变更立即生效，无需重启应用</text>

  <!-- 进院版零发版流程 -->
  <rect x="1080" y="890" width="840" height="280" class="process-box" rx="10"/>
  <text x="1500" y="920" class="reason-title" style="text-anchor: middle;">⚡ 进院版新增模块的零发版流程</text>

  <text x="1100" y="950" class="process-text" style="font-weight: bold;">步骤1: 服务器端配置 (耗时5分钟)</text>
  <text x="1120" y="970" class="process-text">• 添加模块配置: moduleKey, moduleName, url</text>
  <text x="1120" y="985" class="process-text">• 设置启用状态: moduleEnable = true</text>
  <text x="1120" y="1000" class="process-text">• 配置封面图片: cover URL</text>
  <text x="1120" y="1015" class="process-text">• 设置认证信息: loginAuthToken (如需要)</text>

  <text x="1100" y="1040" class="process-text" style="font-weight: bold;">步骤2: 客户端自动响应 (实时生效)</text>
  <text x="1120" y="1060" class="process-text">• 下次启动时自动获取新配置 → UI自动渲染新模块</text>

  <text x="1100" y="1085" class="process-text" style="font-weight: bold;">步骤3: 用户立即可用 (无需更新)</text>
  <text x="1120" y="1105" class="process-text">• 用户重启应用即可看到新模块 → 点击即可使用</text>

  <text x="1100" y="1130" class="advantage-text" style="font-weight: bold;">总耗时: 5分钟，用户立即可用新功能</text>
  <text x="1100" y="1150" class="advantage-text">优势: 配置错误可立即回滚，支持A/B测试</text>

  <!-- 进院版结果 -->
  <rect x="1080" y="1190" width="840" height="120" class="result-box" rx="10"/>
  <text x="1500" y="1220" class="reason-title" style="text-anchor: middle;">🏆 进院版的技术优势结果</text>
  <text x="1120" y="1245" class="advantage-text">✅ 配置更新周期: 5分钟 (实时配置)</text>
  <text x="1120" y="1265" class="advantage-text">✅ 业务响应速度: 极快 (零发版)</text>
  <text x="1120" y="1285" class="advantage-text">✅ 定制化能力: 100% (医院级个性化)</text>

  <!-- 对比箭头和总结 -->
  <line x1="500" y1="1330" x2="500" y2="1380" class="need-release-arrow"/>
  <text x="500" y="1370" class="problem-text" style="text-anchor: middle;">需要发版</text>

  <line x1="1500" y1="1330" x2="1500" y2="1380" class="no-release-arrow"/>
  <text x="1500" y="1370" class="advantage-text" style="text-anchor: middle;">零发版更新</text>

  <!-- 最终对比总结 -->
  <rect x="50" y="1420" width="1900" height="160" class="result-box" rx="15" filter="url(#shadow)"/>
  <text x="1000" y="1450" class="main-title" style="font-size: 28px;">🎯 核心差异总结</text>

  <text x="100" y="1480" class="reason-title">📊 技术架构对比:</text>
  <text x="120" y="1500" class="description">• 家庭版: Fragment嵌入 + 代码固化 → 高耦合 → 必须发版</text>
  <text x="120" y="1520" class="description">• 进院版: Activity跳转 + 动态配置 → 完全解耦 → 零发版更新</text>

  <text x="1000" y="1480" class="reason-title">⏱️ 响应速度对比:</text>
  <text x="1020" y="1500" class="description">• 家庭版: 2-3周发版周期 → 业务响应慢</text>
  <text x="1020" y="1520" class="description">• 进院版: 5分钟配置生效 → 业务响应极快</text>

  <text x="100" y="1550" class="reason-title">🎛️ 配置能力对比:</text>
  <text x="120" y="1570" class="description">• 家庭版: 0%定制化，固定功能，无法个性化</text>

  <text x="1000" y="1550" class="reason-title">🏆 最终结论:</text>
  <text x="1020" y="1570" class="advantage-text">• 进院版: 100%定制化，医院级个性化，业界领先</text>

</svg>
