<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #1a237e; }
      .subtitle { font-family: Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .xml-code { font-family: 'Courier New', monospace; font-size: 9px; fill: #e74c3c; }
      .java-code { font-family: 'Courier New', monospace; font-size: 9px; fill: #27ae60; }
      .level1-box { fill: #ffebee; stroke: #f44336; stroke-width: 3; }
      .level2-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .level3-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .level4-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .level5-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .nesting-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#nestarrow); }
      .custom-arrow { stroke: #ff9800; stroke-width: 3; fill: none; marker-end: url(#customarrow); }
    </style>
    <marker id="nestarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="customarrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#ff9800" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">XML布局嵌套结构详解</text>
  <text x="700" y="45" text-anchor="middle" class="subtitle">从Activity容器到自定义View的完整层级</text>

  <!-- 层级1: Activity根布局 -->
  <rect x="50" y="70" width="1300" height="120" class="level1-box" rx="10"/>
  <text x="700" y="95" text-anchor="middle" class="subtitle">层级1: Activity根布局 - FrameLayout容器</text>
  
  <rect x="70" y="110" width="600" height="60" class="level1-box" rx="5"/>
  <text x="370" y="130" text-anchor="middle" class="subtitle">activity_saccade_ability_evaluate.xml</text>
  <text x="80" y="150" class="xml-code">&lt;FrameLayout android:id="@+id/root_saccade_ability"</text>
  <text x="90" y="165" class="xml-code">  android:layout_width="match_parent" android:layout_height="match_parent"/&gt;</text>

  <rect x="690" y="110" width="640" height="60" class="level1-box" rx="5"/>
  <text x="1010" y="130" text-anchor="middle" class="subtitle">Activity中的Fragment管理代码</text>
  <text x="700" y="150" class="java-code">supportFragmentManager.beginTransaction()</text>
  <text x="710" y="165" class="java-code">  .replace(R.id.root_saccade_ability, SaccadeAbilityExplainFragment.newInstance())</text>

  <!-- 层级2: Fragment布局 -->
  <rect x="50" y="210" width="1300" height="140" class="level2-box" rx="10"/>
  <text x="700" y="235" text-anchor="middle" class="subtitle">层级2: Fragment布局 - ConstraintLayout主容器</text>

  <!-- 说明Fragment -->
  <rect x="70" y="250" width="400" height="80" class="level2-box" rx="5"/>
  <text x="270" y="270" text-anchor="middle" class="subtitle">fragment_saccade_ability_explain.xml</text>
  <text x="80" y="290" class="xml-code">&lt;ConstraintLayout android:background="#EEF3F6"&gt;</text>
  <text x="90" y="305" class="xml-code">  &lt;TextView 说明文字/&gt;</text>
  <text x="90" y="320" class="xml-code">  &lt;ImageView 演示GIF/&gt;</text>

  <!-- 评估Fragment -->
  <rect x="490" y="250" width="400" height="80" class="level2-box" rx="5"/>
  <text x="690" y="270" text-anchor="middle" class="subtitle">fragment_saccade_ability_evaluating.xml</text>
  <text x="500" y="290" class="xml-code">&lt;ConstraintLayout tools:background="#EEF3F6"&gt;</text>
  <text x="510" y="305" class="xml-code">  &lt;TextView id="tv_time" 计时器/&gt;</text>
  <text x="510" y="320" class="xml-code">  &lt;SaccadeAbilityEvaluatingView 自定义View/&gt;</text>

  <!-- 结果Activity -->
  <rect x="910" y="250" width="420" height="80" class="level2-box" rx="5"/>
  <text x="1120" y="270" text-anchor="middle" class="subtitle">activity_saccade_ability_evaluate_result.xml</text>
  <text x="920" y="290" class="xml-code">&lt;ConstraintLayout tools:background="#EEF3F6"&gt;</text>
  <text x="930" y="305" class="xml-code">  &lt;SaccadeAbilityEvaluateResultView</text>
  <text x="940" y="320" class="xml-code">    android:layout_width="match_parent"/&gt;</text>

  <!-- 层级3: 自定义View容器 -->
  <rect x="50" y="370" width="1300" height="160" class="level3-box" rx="10"/>
  <text x="700" y="395" text-anchor="middle" class="subtitle">层级3: 自定义View - FrameLayout继承</text>

  <!-- 评估View -->
  <rect x="70" y="410" width="400" height="100" class="level3-box" rx="5"/>
  <text x="270" y="430" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluatingView.kt</text>
  <text x="80" y="450" class="java-code">class SaccadeAbilityEvaluatingView : FrameLayout {</text>
  <text x="90" y="465" class="java-code">  private lateinit var targetImageView: ImageView</text>
  <text x="90" y="480" class="java-code">  fun startEvaluating(pointF: PointF) {</text>
  <text x="100" y="495" class="java-code">    targetImageView.x = pointF.x</text>
  <text x="100" y="510" class="java-code">    targetImageView.y = pointF.y }</text>

  <!-- 结果View -->
  <rect x="490" y="410" width="400" height="100" class="level3-box" rx="5"/>
  <text x="690" y="430" text-anchor="middle" class="subtitle">SaccadeAbilityEvaluateResultView.kt</text>
  <text x="500" y="450" class="java-code">class SaccadeAbilityEvaluateResultView : FrameLayout {</text>
  <text x="510" y="465" class="java-code">  override fun onDraw(canvas: Canvas) {</text>
  <text x="520" y="480" class="java-code">    super.onDraw(canvas)</text>
  <text x="520" y="495" class="java-code">    canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="520" y="510" class="java-code">    // 绘制眼动轨迹和分析结果 }</text>

  <!-- 倒计时遮罩层 -->
  <rect x="910" y="410" width="420" height="100" class="level3-box" rx="5"/>
  <text x="1120" y="430" text-anchor="middle" class="subtitle">倒计时遮罩层 (ConstraintLayout)</text>
  <text x="920" y="450" class="xml-code">&lt;ConstraintLayout id="cl_count_down"</text>
  <text x="930" y="465" class="xml-code">  android:background="#EEF3F6"&gt;</text>
  <text x="930" y="480" class="xml-code">  &lt;TextView id="tv_count_down" 倒计时数字/&gt;</text>
  <text x="930" y="495" class="xml-code">  &lt;TextView 提示文字/&gt;</text>
  <text x="920" y="510" class="xml-code">&lt;/ConstraintLayout&gt;</text>

  <!-- 层级4: 动态组件 -->
  <rect x="50" y="550" width="1300" height="140" class="level4-box" rx="10"/>
  <text x="700" y="575" text-anchor="middle" class="subtitle">层级4: 动态添加的UI组件</text>

  <!-- ImageView目标点 -->
  <rect x="70" y="590" width="300" height="80" class="level4-box" rx="5"/>
  <text x="220" y="610" text-anchor="middle" class="subtitle">动态ImageView (目标点)</text>
  <text x="80" y="630" class="java-code">val targetImageView = ImageView(context)</text>
  <text x="80" y="645" class="java-code">targetImageView.setImageResource(R.drawable.icon)</text>
  <text x="80" y="660" class="java-code">addView(targetImageView) // 动态添加到FrameLayout</text>

  <!-- Canvas绘制层 -->
  <rect x="390" y="590" width="300" height="80" class="level4-box" rx="5"/>
  <text x="540" y="610" text-anchor="middle" class="subtitle">Canvas绘制层</text>
  <text x="400" y="630" class="java-code">override fun onDraw(canvas: Canvas) {</text>
  <text x="410" y="645" class="java-code">  canvas.drawPath(gazePath, gazePathPaint)</text>
  <text x="410" y="660" class="java-code">  canvas.drawCircle(x, y, radius, pointPaint)</text>

  <!-- Paint样式配置 -->
  <rect x="710" y="590" width="300" height="80" class="level4-box" rx="5"/>
  <text x="860" y="610" text-anchor="middle" class="subtitle">Paint样式配置</text>
  <text x="720" y="630" class="java-code">gazePathPaint.color = Color.RED</text>
  <text x="720" y="645" class="java-code">gazePathPaint.strokeWidth = 3f</text>
  <text x="720" y="660" class="java-code">gazePathPaint.style = Paint.Style.STROKE</text>

  <!-- 坐标转换 -->
  <rect x="1030" y="590" width="300" height="80" class="level4-box" rx="5"/>
  <text x="1180" y="610" text-anchor="middle" class="subtitle">坐标转换</text>
  <text x="1040" y="630" class="java-code">val screenX = gazePoint.x * screenWidth</text>
  <text x="1040" y="645" class="java-code">val screenY = gazePoint.y * screenHeight</text>
  <text x="1040" y="660" class="java-code">canvas.drawCircle(screenX, screenY, radius, paint)</text>

  <!-- 层级5: 具体绘制元素 -->
  <rect x="50" y="710" width="1300" height="120" class="level5-box" rx="10"/>
  <text x="700" y="735" text-anchor="middle" class="subtitle">层级5: 具体绘制元素详解</text>

  <!-- 轨迹路径 -->
  <rect x="70" y="750" width="240" height="60" class="level5-box" rx="5"/>
  <text x="190" y="770" text-anchor="middle" class="subtitle">轨迹路径 (Path)</text>
  <text x="80" y="790" class="java-code">gazePath.moveTo(startX, startY)</text>
  <text x="80" y="805" class="java-code">gazePath.lineTo(endX, endY)</text>

  <!-- 注视点圆圈 -->
  <rect x="330" y="750" width="240" height="60" class="level5-box" rx="5"/>
  <text x="450" y="770" text-anchor="middle" class="subtitle">注视点圆圈</text>
  <text x="340" y="790" class="java-code">canvas.drawCircle(x, y, 8f, pointPaint)</text>
  <text x="340" y="805" class="java-code">// 半径8像素的实心圆</text>

  <!-- 目标点标记 -->
  <rect x="590" y="750" width="240" height="60" class="level5-box" rx="5"/>
  <text x="710" y="770" text-anchor="middle" class="subtitle">目标点标记</text>
  <text x="600" y="790" class="java-code">canvas.drawCircle(targetX, targetY, 12f, targetPaint)</text>
  <text x="600" y="805" class="java-code">// 半径12像素的空心圆</text>

  <!-- 文字标注 -->
  <rect x="850" y="750" width="240" height="60" class="level5-box" rx="5"/>
  <text x="970" y="770" text-anchor="middle" class="subtitle">文字标注</text>
  <text x="860" y="790" class="java-code">canvas.drawText("目标点", x, y, textPaint)</text>
  <text x="860" y="805" class="java-code">// 绘制说明文字</text>

  <!-- 背景网格 -->
  <rect x="1110" y="750" width="240" height="60" class="level5-box" rx="5"/>
  <text x="1230" y="770" text-anchor="middle" class="subtitle">背景网格</text>
  <text x="1120" y="790" class="java-code">canvas.drawLine(0, y, width, y, gridPaint)</text>
  <text x="1120" y="805" class="java-code">// 绘制参考网格线</text>

  <!-- 嵌套关系箭头 -->
  <line x1="700" y1="190" x2="700" y2="210" class="nesting-arrow"/>
  <line x1="700" y1="350" x2="700" y2="370" class="nesting-arrow"/>
  <line x1="700" y1="530" x2="700" y2="550" class="nesting-arrow"/>
  <line x1="700" y1="690" x2="700" y2="710" class="nesting-arrow"/>

  <!-- 自定义View特殊标注 -->
  <line x1="270" y1="510" x2="220" y2="590" class="custom-arrow"/>
  <line x1="690" y1="510" x2="540" y2="590" class="custom-arrow"/>
  <text x="200" y="555" class="text" fill="#ff9800">动态组件</text>
  <text x="620" y="555" class="text" fill="#ff9800">Canvas绘制</text>

  <!-- 总结说明 -->
  <rect x="50" y="850" width="1300" height="120" class="level1-box" rx="10"/>
  <text x="700" y="875" text-anchor="middle" class="subtitle">XML嵌套结构特点总结</text>
  
  <text x="70" y="900" class="text">🏗️ <tspan class="subtitle">层级清晰</tspan>: Activity容器 → Fragment布局 → 自定义View → 动态组件 → 绘制元素，每层职责明确</text>
  <text x="70" y="920" class="text">🎨 <tspan class="subtitle">自定义核心</tspan>: 关键的评估和结果展示都通过自定义View实现，继承FrameLayout，重写onDraw()方法</text>
  <text x="70" y="940" class="text">📱 <tspan class="subtitle">响应式布局</tspan>: 使用ConstraintLayout确保在不同屏幕尺寸下的适配，match_parent确保全屏显示</text>
  <text x="70" y="960" class="text">🔄 <tspan class="subtitle">动态管理</tspan>: Fragment动态切换，ImageView动态定位，Canvas实时重绘，实现流畅的用户交互体验</text>

</svg>
